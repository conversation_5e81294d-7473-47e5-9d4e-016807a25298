(footprint "DIP-24_W7.62mm"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "24-lead though-hole mounted DIP package, row spacing 7.62 mm (300 mils)")
	(tags "THT DIP DIL PDIP 2.54mm 7.62mm 300mil")
	(property "Reference" "REF**"
		(at 3.81 -2.39 0)
		(layer "F.SilkS")
		(uuid "195a0bcb-083a-477e-8d7d-51685e7c262f")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "EP600"
		(at 3.81 30.33 0)
		(layer "F.Fab")
		(uuid "bafd43c1-93c3-4c03-93d3-090f7b98371b")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "b892efa2-e02e-4b1f-9a5a-9e55c94d3584")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "10f4c237-17c9-4246-9bd8-4b1a5a46d86e")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start 1.16 -1.33)
		(end 1.16 29.27)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "9692493a-d225-4437-90bd-56ca926bab9f")
	)
	(fp_line
		(start 1.16 29.27)
		(end 6.46 29.27)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "e3fcd640-44f5-4594-9c22-2cb0513f2ab8")
	)
	(fp_line
		(start 2.81 -1.33)
		(end 1.16 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "fd7c1834-1e47-43da-97fa-9ddce864934b")
	)
	(fp_line
		(start 6.46 -1.33)
		(end 4.81 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "e9f54845-b3c1-4528-a786-c13e8025958e")
	)
	(fp_line
		(start 6.46 29.27)
		(end 6.46 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f0c59500-9eeb-4e24-bc5f-183693a9c9ec")
	)
	(fp_arc
		(start 4.81 -1.33)
		(mid 3.81 -0.33)
		(end 2.81 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f16faebd-637a-477f-aae2-b18678e98117")
	)
	(fp_line
		(start -1.1 -1.55)
		(end -1.1 29.5)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "719acb03-194e-46d7-b370-c01033e22524")
	)
	(fp_line
		(start -1.1 29.5)
		(end 8.7 29.5)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "56e41f5b-d2b6-4956-9936-e403b191dee9")
	)
	(fp_line
		(start 8.7 -1.55)
		(end -1.1 -1.55)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "33ebb8d0-2dcb-4a05-98e4-e8a756efb9d9")
	)
	(fp_line
		(start 8.7 29.5)
		(end 8.7 -1.55)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "7a75bb72-5cf5-48b6-8f86-4d71c83d8fe0")
	)
	(fp_line
		(start 0.635 -0.27)
		(end 1.635 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "54008982-a650-4f4a-8fae-cca349e33004")
	)
	(fp_line
		(start 0.635 29.21)
		(end 0.635 -0.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "d8290487-c022-405b-a16b-047e6e89d8da")
	)
	(fp_line
		(start 1.635 -1.27)
		(end 6.985 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "dbccb1be-402f-4b52-ab5f-fd8747d1942d")
	)
	(fp_line
		(start 6.985 -1.27)
		(end 6.985 29.21)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "eb8a655d-de05-446d-aee8-6e0cec5243df")
	)
	(fp_line
		(start 6.985 29.21)
		(end 0.635 29.21)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "4c68a5f4-75ce-46a5-9d6c-906bcc6c464e")
	)
	(fp_text user "${REFERENCE}"
		(at 3.81 13.97 0)
		(layer "F.Fab")
		(uuid "0a30889b-a4f3-4d8e-b7eb-b0c302bd9dc8")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "240a8514-454a-478c-8691-e39a57a4d077")
	)
	(pad "2" thru_hole oval
		(at 0 2.54)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "4e2e66c6-a8c8-4e92-b5fd-a3ccb1f6b256")
	)
	(pad "3" thru_hole oval
		(at 0 5.08)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "78e86b4f-b011-4818-8623-18fa6b2ebe68")
	)
	(pad "4" thru_hole oval
		(at 0 7.62)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0616d7c7-03fd-4f6d-afbf-2cd720af8d97")
	)
	(pad "5" thru_hole oval
		(at 0 10.16)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c4b5a052-b35a-4179-9fcb-a4764ab28f12")
	)
	(pad "6" thru_hole oval
		(at 0 12.7)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "39e361fb-bdd1-4d52-bd52-cb6a60ca25c9")
	)
	(pad "7" thru_hole oval
		(at 0 15.24)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "757d3936-f4f6-4bb7-b478-a9ded1d60875")
	)
	(pad "8" thru_hole oval
		(at 0 17.78)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "dc2d22f2-835b-4a34-ac97-d0d524f3df1a")
	)
	(pad "9" thru_hole oval
		(at 0 20.32)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6c1eb80d-2845-4469-9bca-5a31be85cd5f")
	)
	(pad "10" thru_hole oval
		(at 0 22.86)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "93c8587f-069e-4ed9-aee5-ac7d7a990736")
	)
	(pad "11" thru_hole oval
		(at 0 25.4)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "19e621a7-38c5-4215-9733-d117202ee011")
	)
	(pad "12" thru_hole oval
		(at 0 27.94)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c4d45669-22a0-4a41-a047-0423c5239374")
	)
	(pad "13" thru_hole oval
		(at 7.62 27.94)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "dc09106b-9889-4c7f-82b1-6850b8646c62")
	)
	(pad "14" thru_hole oval
		(at 7.62 25.4)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "efd35020-b932-4112-9b5b-7a428b8304fd")
	)
	(pad "15" thru_hole oval
		(at 7.62 22.86)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "bc47bac1-4f6a-4242-b672-8201cf745228")
	)
	(pad "16" thru_hole oval
		(at 7.62 20.32)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "b6dff3b1-9542-4cd1-95fd-64e39521580d")
	)
	(pad "17" thru_hole oval
		(at 7.62 17.78)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "55558138-cf1b-465b-ab34-531a6b560c54")
	)
	(pad "18" thru_hole oval
		(at 7.62 15.24)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "76a5e358-39ea-41f8-a58f-51b79ae9d320")
	)
	(pad "19" thru_hole oval
		(at 7.62 12.7)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0d8c9c30-4e82-4782-adf6-eb56124d68df")
	)
	(pad "20" thru_hole oval
		(at 7.62 10.16)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "79ffbff6-418a-4e82-8224-95fd8ae81f05")
	)
	(pad "21" thru_hole oval
		(at 7.62 7.62)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "924d377e-577d-4eaf-a2de-93e42a7f4a27")
	)
	(pad "22" thru_hole oval
		(at 7.62 5.08)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6ccaeb99-82e5-47ef-9410-8db84bb9fb0e")
	)
	(pad "23" thru_hole oval
		(at 7.62 2.54)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "72824a08-fa63-4ee4-8eff-00357070ef3f")
	)
	(pad "24" thru_hole oval
		(at 7.62 0)
		(size 1.6 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "10f042c4-097e-425f-9a35-50d56c1097b1")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/Package_DIP.3dshapes/DIP-24_W7.62mm.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
