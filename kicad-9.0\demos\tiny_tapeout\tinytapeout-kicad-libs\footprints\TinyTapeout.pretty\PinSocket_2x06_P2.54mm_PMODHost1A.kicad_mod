(footprint "PinSocket_2x06_P2.54mm_PMODHost1A" (version 20221018) (generator pcbnew)
  (layer "F.Cu")
  (descr "Through hole angled socket strip, 2x06, 2.54mm pitch, 8.51mm socket length, double cols (from Kicad 4.0.7), script generated")
  (tags "PMOD host expanded GPIO hole angled socket strip THT 2x06 2.54mm double row")
  (attr through_hole)
  (fp_text reference "REF**" (at -5.65 -2.77) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 509f56ae-2b8c-47f7-90de-a99cc7625065)
  )
  (fp_text value "PinSocket_2x06_P2.54mm_PMODHost1A" (at -5.65 15.47) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 26b3ac5d-ba39-4b5e-aee7-20df12a5fac7)
  )
  (fp_text user "${REFERENCE}" (at -8.315 6.35 90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 707f2f48-7c88-4d03-9587-ac00c638256f)
  )
  (fp_text user "digilent spec pinout" (at 2.5 14 90 unlocked) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)) (justify left bottom))
    (tstamp e38ca324-7698-4273-ae47-89f576abd7ab)
  )
  (fp_line (start -12.63 -1.33) (end -12.63 14.03)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 79529e55-f88c-409d-bd77-70a16867ce69))
  (fp_line (start -12.63 -1.33) (end -4 -1.33)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e1828673-ebcd-4c16-b9cc-718c143e78e1))
  (fp_line (start -12.63 1.22762) (end -4 1.22762)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 8c4fa0f6-1c66-48eb-b3c4-0e4f3e0343f4))
  (fp_line (start -12.63 1.345715) (end -4 1.345715)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 50df869e-c1c3-4216-ae49-8c35c03f4885))
  (fp_line (start -12.63 1.46381) (end -4 1.46381)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 8251cd57-9956-4c15-bfab-8d41d396709c))
  (fp_line (start -12.63 1.581905) (end -4 1.581905)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 60333b83-bf6d-400d-8923-99c43d55ebff))
  (fp_line (start -12.63 1.7) (end -4 1.7)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 494c3cbe-d7cf-4a01-854d-a8dd4b07a62b))
  (fp_line (start -12.63 1.818095) (end -4 1.818095)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f22fd8e7-0bf6-4e42-98d0-ef2483bb3132))
  (fp_line (start -12.63 1.93619) (end -4 1.93619)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp d489f5ec-618a-4b64-b175-0c2ced332322))
  (fp_line (start -12.63 2.054285) (end -4 2.054285)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 9b5ad7d3-0106-4396-aa2f-1d16eacb82f8))
  (fp_line (start -12.63 2.17238) (end -4 2.17238)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f72adad0-2e40-4389-814e-4b685efd8bf1))
  (fp_line (start -12.63 2.290475) (end -4 2.290475)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0075ad57-0c8b-4ea1-a94c-616238e909e1))
  (fp_line (start -12.63 2.40857) (end -4 2.40857)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c8793ae3-ff4c-41e9-b8fc-9d09e62024b3))
  (fp_line (start -12.63 2.526665) (end -4 2.526665)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 73082b79-5ad8-4f90-bd68-795c1c5d770f))
  (fp_line (start -12.63 2.64476) (end -4 2.64476)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 87609691-0f5b-4f42-8350-cce7e8fcdc70))
  (fp_line (start -12.63 2.762855) (end -4 2.762855)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 9c8c55b2-b408-46ba-b4e6-a7a185d721a3))
  (fp_line (start -12.63 2.88095) (end -4 2.88095)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp aa2f5c48-9b05-40b0-b5af-3c89607173df))
  (fp_line (start -12.63 2.999045) (end -4 2.999045)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp dd5bf063-7288-427c-9344-64cbf51abd13))
  (fp_line (start -12.63 3.11714) (end -4 3.11714)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2a2bdc03-aacb-4844-868d-983719675df3))
  (fp_line (start -12.63 3.235235) (end -4 3.235235)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a72955e6-3039-4724-ae20-f98d0061d28c))
  (fp_line (start -12.63 3.35333) (end -4 3.35333)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f5b6437c-d2df-436a-9bf2-81ce83526122))
  (fp_line (start -12.63 3.471425) (end -4 3.471425)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp cd03ae82-b4a9-43d6-a757-1c264934d90d))
  (fp_line (start -12.63 3.58952) (end -4 3.58952)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f8689705-d017-4deb-8568-88249b413d28))
  (fp_line (start -12.63 3.70762) (end -4 3.70762)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp fb374f73-bf2f-4d6d-b0c5-b90a58ed1c77))
  (fp_line (start -12.63 3.81) (end -4 3.81)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 86a9e8fb-5b2e-4c64-a2f2-dc8be3172687))
  (fp_line (start -12.63 6.35) (end -4 6.35)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 15915230-bb52-4f3b-ade8-dccf852404e1))
  (fp_line (start -12.63 8.89) (end -4 8.89)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp d2502cb9-6097-415f-a942-bdd11b231cba))
  (fp_line (start -12.63 11.43) (end -4 11.43)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 065f7854-aa80-4e21-9e50-b89a10da6e35))
  (fp_line (start -12.63 14.03) (end -4 14.03)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 63bbed3b-178a-4f7a-a2b0-901b3d753db6))
  (fp_line (start -4 -1.33) (end -4 14.03)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ac57cf57-0d3f-480b-995e-061fc1c7a61f))
  (fp_line (start -4 -0.36) (end -3.59 -0.36)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a40c3692-1dbf-4452-b5a0-014589b1a52b))
  (fp_line (start -4 0.36) (end -3.59 0.36)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 180cfa7f-139b-44f3-9ce2-668663f74245))
  (fp_line (start -4 2.18) (end -3.59 2.18)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 622129e2-ce3d-4430-a734-23fdda82adaf))
  (fp_line (start -4 2.9) (end -3.59 2.9)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0979484d-a3a1-4898-ae4a-18131093585f))
  (fp_line (start -4 4.72) (end -3.59 4.72)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 99e1846f-79a9-42de-ac30-5b2359310e1b))
  (fp_line (start -4 5.44) (end -3.59 5.44)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp d3e8d370-29fb-4abc-8b0a-fd8a713f0530))
  (fp_line (start -4 7.26) (end -3.59 7.26)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 13e4dca8-925e-4f14-8588-a50245e0e78f))
  (fp_line (start -4 7.98) (end -3.59 7.98)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f0e2457a-a339-4ac5-b8b8-458c5fcd2963))
  (fp_line (start -4 9.8) (end -3.59 9.8)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e15c7242-0ef0-4426-8a83-392c271bb11e))
  (fp_line (start -4 10.52) (end -3.59 10.52)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp d9955ded-42e1-43a8-a11b-97d5413595e8))
  (fp_line (start -4 12.34) (end -3.59 12.34)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 8d173c43-6d5e-42fb-b0a5-70620afea7d2))
  (fp_line (start -4 13.06) (end -3.59 13.06)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 25c3d63c-9b2f-43af-a658-a5a2ba7a4117))
  (fp_line (start -1.49 -0.36) (end -1.11 -0.36)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 4f592e8c-f03d-4197-849a-aae44e6f1215))
  (fp_line (start -1.49 0.36) (end -1.11 0.36)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 36bd85d3-0770-4e80-846d-907517d66d9e))
  (fp_line (start -1.49 2.18) (end -1.05 2.18)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 74765d39-7bd5-4450-9e6b-af41e1c0ec46))
  (fp_line (start -1.49 2.9) (end -1.05 2.9)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e95a7eb1-4936-4c1f-acfe-82c1e78c8528))
  (fp_line (start -1.49 4.72) (end -1.05 4.72)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 53750392-6242-4a1c-ac67-deb4dab59747))
  (fp_line (start -1.49 5.44) (end -1.05 5.44)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 74940531-d227-42dd-90eb-c93bb42c5250))
  (fp_line (start -1.49 7.26) (end -1.05 7.26)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp aa1e33a8-8858-44bd-95cd-f23aa97a5c5e))
  (fp_line (start -1.49 7.98) (end -1.05 7.98)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 77980d63-eeab-462a-a61f-dbb1788cd837))
  (fp_line (start -1.49 9.8) (end -1.05 9.8)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 10605a78-871f-4c9d-bef5-adfcd9b17042))
  (fp_line (start -1.49 10.52) (end -1.05 10.52)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a70be39b-3565-46e7-a6fe-d5fba3605b88))
  (fp_line (start -1.49 12.34) (end -1.05 12.34)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ec46963b-1ea1-4385-b973-d5adaeddfea3))
  (fp_line (start -1.49 13.06) (end -1.05 13.06)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 5d61e8ac-2a16-4957-bcd8-78e50178a61d))
  (fp_line (start 0 -1.33) (end 1.11 -1.33)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 881e194f-e52e-4bde-a7a2-c1fdb4b9bb1c))
  (fp_line (start 1.11 -1.33) (end 1.11 0)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c14b3f62-bc32-41ce-a8d5-0e37223f2f03))
  (fp_line (start -13.05 -1.8) (end -13.05 14.45)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 71c23e74-d986-48b0-8d82-64a67acec539))
  (fp_line (start -13.05 14.45) (end 1.8 14.45)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 5ea0fc9a-c3c4-4cdf-8475-fd5205cbf041))
  (fp_line (start 1.8 -1.8) (end -13.05 -1.8)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 3f2e1e1c-8ff8-417d-b5e5-c84b2c5231ee))
  (fp_line (start 1.8 14.45) (end 1.8 -1.8)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 5699845d-2736-4659-8019-bf2a517faeaa))
  (fp_line (start -12.57 -1.27) (end -5.03 -1.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1a106b84-bf81-47e7-a34c-36efe13fc0fb))
  (fp_line (start -12.57 13.97) (end -12.57 -1.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 943753e0-cd14-4789-8bd8-cba8ea632af8))
  (fp_line (start -5.03 -1.27) (end -4.06 -0.3)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1c6b0377-a51c-4c95-bb89-5c6f3f3bc574))
  (fp_line (start -4.06 -0.3) (end -4.06 13.97)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6530cba0-624c-443e-b92d-1d4f70db4dbe))
  (fp_line (start -4.06 0.3) (end 0 0.3)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 37ac2b32-4643-447d-b72b-3bcdea7c766b))
  (fp_line (start -4.06 2.84) (end 0 2.84)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 31d8a0af-54f7-49b0-9f17-db093074493e))
  (fp_line (start -4.06 5.38) (end 0 5.38)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 194f04ab-b0f7-4a75-a7d1-b7e947abd737))
  (fp_line (start -4.06 7.92) (end 0 7.92)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e7788dd5-2ea4-446c-8777-221451456c71))
  (fp_line (start -4.06 10.46) (end 0 10.46)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1666a64d-28b0-43d1-ae83-97c9507a2a2b))
  (fp_line (start -4.06 13) (end 0 13)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp cabe8a95-5a02-493c-8ec1-4fbb4189cf4a))
  (fp_line (start -4.06 13.97) (end -12.57 13.97)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 817c2f2a-b981-4cf4-a8fd-c05d7a1d7c0f))
  (fp_line (start 0 -0.3) (end -4.06 -0.3)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7f6fb50b-f737-49c3-b304-6592295db6cd))
  (fp_line (start 0 0.3) (end 0 -0.3)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ce427f38-fbd4-4ee4-b6ce-72373944b91c))
  (fp_line (start 0 2.24) (end -4.06 2.24)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a9d2b233-aab1-40c2-81fa-08c3d5093906))
  (fp_line (start 0 2.84) (end 0 2.24)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e9951cb4-7ecf-4998-8c7a-05547ea898ed))
  (fp_line (start 0 4.78) (end -4.06 4.78)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp cebc097b-d9d9-47d8-b05d-7d385b3f9682))
  (fp_line (start 0 5.38) (end 0 4.78)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 93122ae8-c105-4684-8f42-c5b2dd90d41e))
  (fp_line (start 0 7.32) (end -4.06 7.32)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6a5f485c-9dcf-4876-8515-84fbfbcb05dd))
  (fp_line (start 0 7.92) (end 0 7.32)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7a1db717-f69f-4c1b-bfc2-89daa51b1268))
  (fp_line (start 0 9.86) (end -4.06 9.86)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ab7c653c-b586-44f7-8e50-b673ea6fc1e5))
  (fp_line (start 0 10.46) (end 0 9.86)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp bd3a419a-eac4-41b6-8ddc-50199351272b))
  (fp_line (start 0 12.4) (end -4.06 12.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 737c5623-3363-4caf-9418-8905f502a1dc))
  (fp_line (start 0 13) (end 0 12.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 603c8e2c-0cd4-4f81-998c-9685d33a9b7f))
  (pad "1" thru_hole rect (at 0 12.7) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 35ea4e74-da2b-4efc-accf-888c35a8003f))
  (pad "2" thru_hole oval (at 0 10.16) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 617932a0-909e-4918-983a-08178f066d53))
  (pad "3" thru_hole oval (at 0 7.62) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 91108f6d-e9da-40c7-b240-93d33493e5fd))
  (pad "4" thru_hole oval (at 0 5.08) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 9ed4306b-b247-4477-b462-0700a1fa1502))
  (pad "5" thru_hole oval (at 0 2.54) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 7a117c8d-ae96-4881-b50f-576252c5eb1f))
  (pad "6" thru_hole circle (at 0 0) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 6cb403c8-9ac3-4484-8265-04b1ba11a6d2))
  (pad "7" thru_hole oval (at -2.54 12.7) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 30681928-7183-4e4a-85c3-d39b2a1f74ee))
  (pad "8" thru_hole oval (at -2.54 10.16) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 87f8ac58-a22f-45ee-b6d5-1ac216fad964))
  (pad "9" thru_hole oval (at -2.54 7.62) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 8d35eff3-730f-4507-9591-98969c6a1768))
  (pad "10" thru_hole oval (at -2.54 5.08) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp e8505061-281e-4ab8-8dfa-8abef87c673f))
  (pad "11" thru_hole oval (at -2.54 2.54) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp bb706857-c7bd-46da-b892-dfc3ba9278f7))
  (pad "12" thru_hole oval (at -2.54 0) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 19c28c23-0e14-412d-9826-a500a46111d5))
  (model "${KICAD6_3DMODEL_DIR}/Connector_PinSocket_2.54mm.3dshapes/PinSocket_2x06_P2.54mm_Horizontal.wrl"
    (offset (xyz 0 0 0))
    (scale (xyz 1 1 1))
    (rotate (xyz 0 0 0))
  )
)
