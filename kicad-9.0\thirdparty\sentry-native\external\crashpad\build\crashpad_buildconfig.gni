# Copyright 2017 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

declare_args() {
  # Determines various flavors of build configuration, and which concrete
  # targets to use for dependencies. Valid values are "standalone", "chromium",
  # "fuchsia", "dart" or "external".
  crashpad_dependencies = "standalone"

  if (defined(is_fuchsia_tree) && is_fuchsia_tree) {
    crashpad_dependencies = "fuchsia"
  }
}

assert(
    crashpad_dependencies == "chromium" || crashpad_dependencies == "fuchsia" ||
    crashpad_dependencies == "standalone" ||
    crashpad_dependencies == "external" || crashpad_dependencies == "dart")

crashpad_is_in_chromium = crashpad_dependencies == "chromium"
crashpad_is_in_fuchsia = crashpad_dependencies == "fuchsia"
crashpad_is_in_dart = crashpad_dependencies == "dart"
crashpad_is_external = crashpad_dependencies == "external"
crashpad_is_standalone = crashpad_dependencies == "standalone"

# This is the parent directory that contains the mini_chromium source dir.
# This variable is not used when crashpad_is_in_chromium.
if (crashpad_is_in_fuchsia) {
  import("//third_party/crashpad/fuchsia_buildconfig.gni")
  mini_chromium_source_parent =
      fuchsia_crashpad_root + "/third_party/mini_chromium"
} else {
  mini_chromium_source_parent = "../third_party/mini_chromium"
}

# This is the source directory of mini_chromium (what is checked out).
_mini_chromium_source_root = "$mini_chromium_source_parent/mini_chromium"

# This references the mini_chromium location for importing GN files.
if (crashpad_is_external || crashpad_is_in_dart) {
  mini_chromium_import_root = "../../../$_mini_chromium_source_root"
} else if (crashpad_is_in_fuchsia) {
  mini_chromium_import_root = fuchsia_mini_chromium_root
} else {
  mini_chromium_import_root = _mini_chromium_source_root
}

if (crashpad_is_in_chromium) {
  crashpad_is_mac = is_mac
  crashpad_is_ios = is_ios
  crashpad_is_apple = is_apple
  crashpad_is_win = is_win
  crashpad_is_linux = is_linux || is_chromeos
  crashpad_is_android = is_android
  crashpad_is_fuchsia = is_fuchsia

  crashpad_is_posix = is_posix

  crashpad_is_clang = is_clang
} else {
  import("$mini_chromium_import_root/build/compiler.gni")
  import("$mini_chromium_import_root/build/platform.gni")

  crashpad_is_mac = mini_chromium_is_mac
  crashpad_is_ios = mini_chromium_is_ios
  crashpad_is_apple = mini_chromium_is_apple
  crashpad_is_win = mini_chromium_is_win
  crashpad_is_linux = mini_chromium_is_linux
  crashpad_is_android = mini_chromium_is_android
  crashpad_is_fuchsia = mini_chromium_is_fuchsia

  crashpad_is_posix = mini_chromium_is_posix

  crashpad_is_clang = mini_chromium_is_clang
}

crashpad_flock_always_supported = !(crashpad_is_android || crashpad_is_fuchsia)

template("crashpad_executable") {
  executable(target_name) {
    forward_variables_from(invoker,
                           "*",
                           [
                             "configs",
                             "remove_configs",
                           ])
    if (defined(invoker.remove_configs)) {
      configs -= invoker.remove_configs
    }

    if (defined(invoker.configs)) {
      configs += invoker.configs
    }

    if (crashpad_is_in_fuchsia) {
      conversion_config = [ "//build/config:Wno-conversion" ]
      if (configs + conversion_config - conversion_config == configs) {
        # TODO(https://fxbug.dev/58162): Decide if these are worth enabling.
        configs += conversion_config
      }
    }
  }
}

template("crashpad_loadable_module") {
  loadable_module(target_name) {
    forward_variables_from(invoker,
                           "*",
                           [
                             "configs",
                             "remove_configs",
                           ])
    if (defined(invoker.remove_configs)) {
      configs -= invoker.remove_configs
    }

    if (defined(invoker.configs)) {
      configs += invoker.configs
    }

    if (crashpad_is_in_fuchsia) {
      conversion_config = [ "//build/config:Wno-conversion" ]
      if (configs + conversion_config - conversion_config == configs) {
        # TODO(https://fxbug.dev/58162): Decide if these are worth enabling.
        configs += conversion_config
      }
    }
  }
}

template("crashpad_static_library") {
  static_library(target_name) {
    forward_variables_from(invoker,
                           "*",
                           [
                             "configs",
                             "remove_configs",
                           ])
    if (defined(invoker.remove_configs)) {
      configs -= invoker.remove_configs
    }

    if (defined(invoker.configs)) {
      configs += invoker.configs
    }

    if (crashpad_is_in_fuchsia) {
      conversion_config = [ "//build/config:Wno-conversion" ]
      if (configs + conversion_config - conversion_config == configs) {
        # TODO(https://fxbug.dev/58162): Decide if these are worth enabling.
        configs += conversion_config
      }
    }
  }
}
