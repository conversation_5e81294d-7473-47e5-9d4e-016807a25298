(kicad_pcb
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(title_block
		(rev "1")
		(company "Kicad Demo")
	)
	(layers
		(0 "F.Cu" signal "top_copper")
		(2 "B.Cu" signal "bottom_copper")
		(9 "F.Adhes" user "F.Adhesive")
		(11 "B.Adhes" user "B.Adhesive")
		(13 "F.Paste" user)
		(15 "B.Paste" user)
		(5 "F.SilkS" user "F.Silkscreen")
		(7 "B.SilkS" user "B.Silkscreen")
		(1 "F.Mask" user)
		(3 "B.Mask" user)
		(17 "Dwgs.User" user "User.Drawings")
		(19 "Cmts.User" user "User.Comments")
		(21 "Eco1.User" user "User.Eco1")
		(23 "Eco2.User" user "User.Eco2")
		(25 "Edge.Cuts" user)
		(27 "Margin" user)
		(31 "F.CrtYd" user "F.Courtyard")
		(29 "B.CrtYd" user "B.Courtyard")
		(35 "F.Fab" user)
		(33 "B.Fab" user)
	)
	(setup
		(stackup
			(layer "F.SilkS"
				(type "Top Silk Screen")
				(color "White")
			)
			(layer "F.Paste"
				(type "Top Solder Paste")
			)
			(layer "F.Mask"
				(type "Top Solder Mask")
				(color "Green")
				(thickness 0.01)
			)
			(layer "F.Cu"
				(type "copper")
				(thickness 0.035)
			)
			(layer "dielectric 1"
				(type "core")
				(thickness 1.51)
				(material "FR4")
				(epsilon_r 4.5)
				(loss_tangent 0.02)
			)
			(layer "B.Cu"
				(type "copper")
				(thickness 0.035)
			)
			(layer "B.Mask"
				(type "Bottom Solder Mask")
				(color "Green")
				(thickness 0.01)
			)
			(layer "B.Paste"
				(type "Bottom Solder Paste")
			)
			(layer "B.SilkS"
				(type "Bottom Silk Screen")
				(color "White")
			)
			(copper_finish "Immersion tin")
			(dielectric_constraints no)
		)
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(tenting front back)
		(pcbplotparams
			(layerselection 0x00000000_00000000_00001030_ffffffff)
			(plot_on_all_layers_selection 0x00000000_00000000_00000000_00000000)
			(disableapertmacros no)
			(usegerberextensions no)
			(usegerberattributes yes)
			(usegerberadvancedattributes yes)
			(creategerberjobfile yes)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 6)
			(plotframeref no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(pdf_metadata yes)
			(pdf_single_document no)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plot_black_and_white yes)
			(plotinvisibletext no)
			(sketchpadsonfab no)
			(plotpadnumbers no)
			(hidednponfab no)
			(sketchdnponfab yes)
			(crossoutdnponfab yes)
			(subtractmaskfromsilk no)
			(outputformat 1)
			(mirror no)
			(drillshape 0)
			(scaleselection 1)
			(outputdirectory "plots")
		)
	)
	(net 0 "")
	(net 1 "/CLK-D1")
	(net 2 "/CTRL-D3")
	(net 3 "/DONE-SELECT*")
	(net 4 "/PWR_3,3-5V")
	(net 5 "/TCK-CCLK")
	(net 6 "/TD0-DONE")
	(net 7 "/TD0-PROG-D4")
	(net 8 "/TDI-DIN")
	(net 9 "/TDI-DIN-D0")
	(net 10 "/TMS-PROG")
	(net 11 "/TMS-PROG-D2")
	(net 12 "/VCC_SENSE-ERROR*")
	(net 13 "GND")
	(net 14 "VCC")
	(net 15 "Net-(U1B-O)")
	(net 16 "Net-(U1A-O)")
	(net 17 "unconnected-(J1-Pad1)")
	(net 18 "unconnected-(J1-Pad7)")
	(net 19 "unconnected-(J1-Pad9)")
	(net 20 "Net-(U1D-O)")
	(net 21 "Net-(U1C-O)")
	(net 22 "Net-(D1-K)")
	(net 23 "unconnected-(J1-P23-Pad23)")
	(net 24 "unconnected-(J1-P18-Pad18)")
	(net 25 "unconnected-(J1-P17-Pad17)")
	(net 26 "unconnected-(J1-P16-Pad16)")
	(net 27 "unconnected-(J1-P24-Pad24)")
	(net 28 "unconnected-(J1-P19-Pad19)")
	(net 29 "Net-(J1-Pad11)")
	(net 30 "unconnected-(J1-Pad10)")
	(net 31 "unconnected-(J1-P22-Pad22)")
	(net 32 "unconnected-(J1-P21-Pad21)")
	(net 33 "unconnected-(J1-P14-Pad14)")
	(net 34 "Net-(U2A-O)")
	(net 35 "Net-(U1A-E)")
	(net 36 "Net-(U1B-D)")
	(net 37 "Net-(U1B-E)")
	(net 38 "Net-(U1D-D)")
	(net 39 "Net-(U1C-D)")
	(net 40 "unconnected-(U2B-O-Pad6)")
	(net 41 "unconnected-(U2D-O-Pad11)")
	(net 42 "unconnected-(U2C-O-Pad8)")
	(footprint "sonde-xilinx:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56ced6")
		(at 162.56 88.9 180)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=10.16mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 10.16mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R14"
			(at 5.08 -2.31 180)
			(layer "F.SilkS")
			(uuid "f961c032-2628-44fa-901a-648dc9739910")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100"
			(at 5.08 2.31 180)
			(layer "F.Fab")
			(uuid "f64ab331-ecba-4afe-8b37-ca50ac790e15")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "7cd2d6db-d91d-42f8-ac03-f17573552caf")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "b937e545-8646-40bc-9221-b1e2b2eb3cf0")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R? SM0603 SM0805 R?-* SM1206")
		(path "/00000000-0000-0000-0000-00003ebf819b")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 9.12 0)
			(end 8.35 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f576f106-238d-48c1-ba70-bcd87dce78cb")
		)
		(fp_line
			(start 8.35 1.37)
			(end 8.35 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a35d1ae8-dd7c-42d5-9de1-5cd654c8dc2f")
		)
		(fp_line
			(start 8.35 -1.37)
			(end 1.81 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "62ba076f-51eb-4cbb-8ba8-91576037c63d")
		)
		(fp_line
			(start 1.81 1.37)
			(end 8.35 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "93f747c4-8cc7-4c0d-96f2-46c492a926c7")
		)
		(fp_line
			(start 1.81 -1.37)
			(end 1.81 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9df8b84f-dda1-417e-ae1c-ee15bdbe4190")
		)
		(fp_line
			(start 1.04 0)
			(end 1.81 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1fc5a2c7-fce1-40fa-aaae-9838a387d9e8")
		)
		(fp_line
			(start 11.21 1.5)
			(end 11.21 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "ead9097a-2b23-4967-b735-6d58923fae5d")
		)
		(fp_line
			(start 11.21 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "1c47a3a2-dc9d-430d-9714-9929e5e4eb72")
		)
		(fp_line
			(start -1.05 1.5)
			(end 11.21 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "e43497c9-f358-4a7c-a5d0-fe6a92ce99d0")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "5f89ea94-fa6a-473d-b812-45e5e9c1189c")
		)
		(fp_line
			(start 10.16 0)
			(end 8.23 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "8f4c9c57-5b0e-42e4-b8c0-444dea29d526")
		)
		(fp_line
			(start 8.23 1.25)
			(end 8.23 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "cbc13a27-fd2f-4ccf-97f7-c40c98183e6b")
		)
		(fp_line
			(start 8.23 -1.25)
			(end 1.93 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "9f60f68c-4aff-4a88-b9b8-c6a5b4edbbe6")
		)
		(fp_line
			(start 1.93 1.25)
			(end 8.23 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b3437511-cdc8-4f6b-b95d-b8ba77354fdc")
		)
		(fp_line
			(start 1.93 -1.25)
			(end 1.93 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c25bae93-6ace-4f1d-b7b7-8fff076b66ca")
		)
		(fp_line
			(start 0 0)
			(end 1.93 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2c1b28a3-b7b3-4ea0-88b0-e26e7a28fd32")
		)
		(fp_text user "${REFERENCE}"
			(at 5.08 0 180)
			(layer "F.Fab")
			(uuid "a589d448-aa7c-4e18-a805-d986201b447e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 6 "/TD0-DONE")
			(pintype "passive")
			(uuid "d635f7bd-86f0-42fb-8cf2-caf6cbab272b")
		)
		(pad "2" thru_hole oval
			(at 10.16 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 16 "Net-(U1A-O)")
			(pintype "passive")
			(uuid "960b4293-6d19-40f7-996e-444a6ef82e8a")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56ceec")
		(at 121.92 80.518 180)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=10.16mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 10.16mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R9"
			(at 12.72 0.018 180)
			(layer "F.SilkS")
			(uuid "232c7e26-d999-4f51-a77f-de82d363cc90")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100"
			(at 3.393 -0.451 180)
			(layer "F.Fab")
			(uuid "54cf641d-00e0-48f0-b919-17f2a1d60c9a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "0e58e979-8c51-4bbb-be9e-e65e573a5ceb")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "98ec230f-d100-4602-9d8d-dfdada4785ba")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R? SM0603 SM0805 R?-* SM1206")
		(path "/00000000-0000-0000-0000-00003ebf7d33")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 9.12 0)
			(end 8.35 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "527e47fe-71e2-4717-abd1-7d16b8d89f02")
		)
		(fp_line
			(start 8.35 1.37)
			(end 8.35 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "701974fb-872c-4d9b-9605-079fb6ca2322")
		)
		(fp_line
			(start 8.35 -1.37)
			(end 1.81 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0b42e392-171c-4ff0-a7e7-c8669662bf34")
		)
		(fp_line
			(start 1.81 1.37)
			(end 8.35 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2c42657d-3530-49a5-8a31-7a75e225b5a9")
		)
		(fp_line
			(start 1.81 -1.37)
			(end 1.81 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5b9f60e3-a12c-4574-a24c-1b951d0f280d")
		)
		(fp_line
			(start 1.04 0)
			(end 1.81 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ddd47341-3cc8-4d6c-b1fd-5334faf95142")
		)
		(fp_line
			(start 11.21 1.5)
			(end 11.21 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "02b580eb-8ac3-438f-acde-ee010e09767d")
		)
		(fp_line
			(start 11.21 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b9cff6d7-3158-465f-a3cb-7a7cb8999753")
		)
		(fp_line
			(start -1.05 1.5)
			(end 11.21 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "43d9869e-dff4-4c67-89db-61647ed98c5c")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "99340cb0-bbab-471e-bf8e-419b1f28ea1d")
		)
		(fp_line
			(start 10.16 0)
			(end 8.23 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "860707c8-0de8-44e1-9a38-af903218aa0c")
		)
		(fp_line
			(start 8.23 1.25)
			(end 8.23 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "67f276b8-522e-43c8-b494-45da3050a9f8")
		)
		(fp_line
			(start 8.23 -1.25)
			(end 1.93 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "1dafea05-4bc7-4a52-a72f-ace5e8018326")
		)
		(fp_line
			(start 1.93 1.25)
			(end 8.23 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0c5ba9d8-359a-48bd-a34d-9533130b5921")
		)
		(fp_line
			(start 1.93 -1.25)
			(end 1.93 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "1937ee0b-976f-4c12-9400-37b257824ade")
		)
		(fp_line
			(start 0 0)
			(end 1.93 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "bc1009db-cf59-4df4-bba5-91b8d743e7af")
		)
		(fp_text user "${REFERENCE}"
			(at 7.142 0.46 180)
			(layer "F.Fab")
			(uuid "871a94b3-d034-4560-85ea-5f9c0836c164")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 38 "Net-(U1D-D)")
			(pintype "passive")
			(uuid "a67489c6-f351-4364-9869-0c5c04d81b27")
		)
		(pad "2" thru_hole oval
			(at 10.16 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 11 "/TMS-PROG-D2")
			(pintype "passive")
			(uuid "d2695ed2-e944-42d8-a12a-36c5066b6545")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56cf02")
		(at 121.92 104.14 180)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=10.16mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 10.16mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R4"
			(at 5.08 -2.31 180)
			(layer "F.SilkS")
			(uuid "3038068f-fd74-4869-9edb-e3c4f5dc392d")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "47"
			(at 5.08 2.31 180)
			(layer "F.Fab")
			(uuid "a11c9d3d-b900-4036-863e-6008cb6a9d24")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "814cdcc2-87ff-404e-9a0a-07598357b15c")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "f48e4063-eb28-420f-a0d9-b9b1f732fe9c")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R? SM0603 SM0805 R?-* SM1206")
		(path "/00000000-0000-0000-0000-00003ebf7d22")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 9.12 0)
			(end 8.35 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "91f1a67b-e09a-4044-9c31-39bb19ae2d5e")
		)
		(fp_line
			(start 8.35 1.37)
			(end 8.35 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "762c5ef0-c81d-446b-961c-c9e410d4905f")
		)
		(fp_line
			(start 8.35 -1.37)
			(end 1.81 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3de6f115-d233-4e8a-8254-fc412d3b260e")
		)
		(fp_line
			(start 1.81 1.37)
			(end 8.35 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "48de1780-d728-4746-bc60-9688b8f1427d")
		)
		(fp_line
			(start 1.81 -1.37)
			(end 1.81 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "488df9c1-f2c3-464e-beb5-015618dece25")
		)
		(fp_line
			(start 1.04 0)
			(end 1.81 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "915d6a45-6e15-4490-bee1-670af964c6ef")
		)
		(fp_line
			(start 11.21 1.5)
			(end 11.21 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "7b93bbec-1759-4e5a-aeab-7707807227e8")
		)
		(fp_line
			(start 11.21 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "32514bf0-95ab-4bac-b5f5-b8310b23ad06")
		)
		(fp_line
			(start -1.05 1.5)
			(end 11.21 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "76e044b1-9800-4ada-9842-ece599b6ecd7")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "287a5338-8212-428a-a1cc-87d6aa1e557a")
		)
		(fp_line
			(start 10.16 0)
			(end 8.23 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "77c726af-c1fa-462a-af71-574b5a854bc2")
		)
		(fp_line
			(start 8.23 1.25)
			(end 8.23 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6215cd41-1878-4cf6-bf0e-8c41c21b3927")
		)
		(fp_line
			(start 8.23 -1.25)
			(end 1.93 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ae025d12-cb87-45ad-a161-4cf017740326")
		)
		(fp_line
			(start 1.93 1.25)
			(end 8.23 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "7b57d183-6977-4430-ad92-1b57b2fc9293")
		)
		(fp_line
			(start 1.93 -1.25)
			(end 1.93 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2e0102ea-ca10-410c-a73d-3470e71d66d8")
		)
		(fp_line
			(start 0 0)
			(end 1.93 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b8378c95-8020-4c76-924d-2cac13740f04")
		)
		(fp_text user "${REFERENCE}"
			(at 5.08 0 180)
			(layer "F.Fab")
			(uuid "9272d4c7-bcdd-4851-b0a2-99ecc8b8676a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 34 "Net-(U2A-O)")
			(pintype "passive")
			(uuid "6baff7c9-6e4c-420c-8314-5e30b7268214")
		)
		(pad "2" thru_hole oval
			(at 10.16 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 3 "/DONE-SELECT*")
			(pintype "passive")
			(uuid "9b6a1ef5-51c5-4a90-959b-fc7dc608f3f1")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56cf18")
		(at 162.56 105.41 180)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=10.16mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 10.16mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R13"
			(at 5.08 -2.31 180)
			(layer "F.SilkS")
			(uuid "0bbd1e43-4a91-47f0-af60-c611ecdeb57c")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100"
			(at 5.08 2.31 180)
			(layer "F.Fab")
			(uuid "827266ec-d483-4d95-8c55-f88fd3b9cf7e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "100be963-a8bf-4eed-9c5d-b7eeb1707a56")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "02b29499-40b4-46fb-8234-6ea39c2f4841")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R? SM0603 SM0805 R?-* SM1206")
		(path "/00000000-0000-0000-0000-00004d528082")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 9.12 0)
			(end 8.35 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "50295dd9-add7-4e38-a1dd-21f20a2d3d60")
		)
		(fp_line
			(start 8.35 1.37)
			(end 8.35 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2f5b8a6a-b4ac-4a83-9824-a625eae4aa59")
		)
		(fp_line
			(start 8.35 -1.37)
			(end 1.81 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3848b348-fc43-4d5f-a841-e8ede1b06821")
		)
		(fp_line
			(start 1.81 1.37)
			(end 8.35 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "82b39403-2e84-48ee-ba35-eaee85d50bec")
		)
		(fp_line
			(start 1.81 -1.37)
			(end 1.81 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "985bdab9-0d5b-48db-a62d-de6168ac8463")
		)
		(fp_line
			(start 1.04 0)
			(end 1.81 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "426162a6-99c4-4d94-bcc4-0d7e38b49f5c")
		)
		(fp_line
			(start 11.21 1.5)
			(end 11.21 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "016a21c9-884c-4bf0-9042-86b0073bc44d")
		)
		(fp_line
			(start 11.21 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "5eeb9bd2-daa6-4108-83a2-135685b5314b")
		)
		(fp_line
			(start -1.05 1.5)
			(end 11.21 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "58358c93-6457-477a-ac56-c2455ea96e2f")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "ec666414-6394-4ee6-9c3f-a55cd18588fe")
		)
		(fp_line
			(start 10.16 0)
			(end 8.23 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "9eb82895-3aa8-4bd2-ac29-f20a648d8514")
		)
		(fp_line
			(start 8.23 1.25)
			(end 8.23 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "8b2ad77a-caf1-4876-b0c8-5e968e09fcc5")
		)
		(fp_line
			(start 8.23 -1.25)
			(end 1.93 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "eda3ecf1-f7dc-43d3-a51a-aa96664723d6")
		)
		(fp_line
			(start 1.93 1.25)
			(end 8.23 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "dd4d4c76-ec7b-4256-a8fa-c1f95b0888a5")
		)
		(fp_line
			(start 1.93 -1.25)
			(end 1.93 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ebe6ff19-4453-4999-afa4-565011df52f7")
		)
		(fp_line
			(start 0 0)
			(end 1.93 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "7589c753-9a3b-4605-80ac-cbaa692e8a0c")
		)
		(fp_text user "${REFERENCE}"
			(at 5.08 0 180)
			(layer "F.Fab")
			(uuid "1c6c8232-b9ba-4f92-957a-36a4d4933ecc")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 10 "/TMS-PROG")
			(pintype "passive")
			(uuid "4bbecfd2-3afa-4dc3-83fa-9dfc2599e52c")
		)
		(pad "2" thru_hole oval
			(at 10.16 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 20 "Net-(U1D-O)")
			(pintype "passive")
			(uuid "0475f946-d4a3-47c4-a885-ea2141c90b69")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56cf2e")
		(at 162.56 99.06 180)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=10.16mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 10.16mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R12"
			(at 5.08 -2.31 180)
			(layer "F.SilkS")
			(uuid "eabad64c-e379-46a1-83ba-c478368d3ee4")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100"
			(at 5.08 2.31 180)
			(layer "F.Fab")
			(uuid "9963b536-ee62-41bf-986c-1c3e995bad3f")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "c0ecce2a-048e-4b0f-a74e-95b5052f8935")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "ffffb29d-b7ad-4225-bc1d-5a35c8b26409")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R? SM0603 SM0805 R?-* SM1206")
		(path "/00000000-0000-0000-0000-00004d528083")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 9.12 0)
			(end 8.35 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d7135905-0b8c-4c47-a7a5-fac32569fca1")
		)
		(fp_line
			(start 8.35 1.37)
			(end 8.35 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bb37301b-3bf6-4aa7-a525-8f0776de7489")
		)
		(fp_line
			(start 8.35 -1.37)
			(end 1.81 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7b466e63-9eb7-46d0-970b-190a67cda671")
		)
		(fp_line
			(start 1.81 1.37)
			(end 8.35 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f0034ce8-019e-417b-8536-71e659408f77")
		)
		(fp_line
			(start 1.81 -1.37)
			(end 1.81 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "dd636097-234c-4d26-9223-45d4691ec8a8")
		)
		(fp_line
			(start 1.04 0)
			(end 1.81 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4428e007-332a-4b25-9415-a3c9936c1a8f")
		)
		(fp_line
			(start 11.21 1.5)
			(end 11.21 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "9a8ed122-7453-43ec-8848-d5c7a6f80adf")
		)
		(fp_line
			(start 11.21 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b4221ad1-4429-44d9-8e6b-14b0264f81c6")
		)
		(fp_line
			(start -1.05 1.5)
			(end 11.21 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "a60e6948-af0d-4576-bb2f-00466d2ab96b")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "63fe7872-ded8-4357-96a4-0ec3ff840e64")
		)
		(fp_line
			(start 10.16 0)
			(end 8.23 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c1075d7c-778a-421f-afc0-4dcb19d7da56")
		)
		(fp_line
			(start 8.23 1.25)
			(end 8.23 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "19e4b5d9-22c4-466d-aaa7-faf3bc43eb63")
		)
		(fp_line
			(start 8.23 -1.25)
			(end 1.93 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a5cd244b-e46c-44fa-a21f-f0a2e6faa8ed")
		)
		(fp_line
			(start 1.93 1.25)
			(end 8.23 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "8701e3cd-ca9b-4d85-bec8-61f070925fdf")
		)
		(fp_line
			(start 1.93 -1.25)
			(end 1.93 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "e2e6e412-d25a-43a9-ac6f-8d4921585442")
		)
		(fp_line
			(start 0 0)
			(end 1.93 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "11c9ca48-115a-45fc-ae69-f564dc42f2c5")
		)
		(fp_text user "${REFERENCE}"
			(at 5.08 0 180)
			(layer "F.Fab")
			(uuid "c7f9cb8d-4c36-417a-b5f1-62a724ae74a9")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 5 "/TCK-CCLK")
			(pintype "passive")
			(uuid "23b006a3-e67f-4521-bdc3-e424d572065e")
		)
		(pad "2" thru_hole oval
			(at 10.16 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 21 "Net-(U1C-O)")
			(pintype "passive")
			(uuid "b30ee46f-774a-4171-b36f-05bede0be75b")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56cf44")
		(at 162.56 93.98 180)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=10.16mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 10.16mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R11"
			(at 5.08 -2.31 180)
			(layer "F.SilkS")
			(uuid "d487287a-f806-4b4b-badf-f42c40b32041")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100"
			(at 5.08 2.31 180)
			(layer "F.Fab")
			(uuid "351a8a80-e0bf-4db2-b069-19274065f711")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "3ba14cec-47a7-4286-aeac-eeaabc9c1015")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "7afb3171-fa08-4793-abf4-5b6fc3498adf")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R? SM0603 SM0805 R?-* SM1206")
		(path "/00000000-0000-0000-0000-00004d527316")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 9.12 0)
			(end 8.35 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e41379cc-**************-ab5c59e9d6fe")
		)
		(fp_line
			(start 8.35 1.37)
			(end 8.35 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f14f3809-4fbb-4217-9b0d-4689261894d1")
		)
		(fp_line
			(start 8.35 -1.37)
			(end 1.81 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3ce30095-ed07-4cb8-88d8-d8b6e6c6669e")
		)
		(fp_line
			(start 1.81 1.37)
			(end 8.35 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "95d3a5da-b412-48f9-beaa-651cae355731")
		)
		(fp_line
			(start 1.81 -1.37)
			(end 1.81 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "16767bb3-07c8-4803-a72d-6a1397d7947f")
		)
		(fp_line
			(start 1.04 0)
			(end 1.81 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d0aa44f9-1433-4716-926c-fc703337d20c")
		)
		(fp_line
			(start 11.21 1.5)
			(end 11.21 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "1b3ae9ff-ab7d-4035-b683-5ef96857c691")
		)
		(fp_line
			(start 11.21 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "d39bbe6f-f099-498a-975e-0ece99660426")
		)
		(fp_line
			(start -1.05 1.5)
			(end 11.21 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "12e85fc1-6a96-4768-9119-4c79228c452a")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "f81ef3d4-2dd3-43d2-8f90-4496cbe86298")
		)
		(fp_line
			(start 10.16 0)
			(end 8.23 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "e488e5d3-a56c-42a3-b019-2a96e112a5d0")
		)
		(fp_line
			(start 8.23 1.25)
			(end 8.23 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "cd4e0f55-2388-4e42-910d-3a5112eb5c86")
		)
		(fp_line
			(start 8.23 -1.25)
			(end 1.93 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "acd5c8cd-e355-4b6e-8d90-afccef254ac7")
		)
		(fp_line
			(start 1.93 1.25)
			(end 8.23 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2cedf356-dd4b-49cf-8a3e-f69731255035")
		)
		(fp_line
			(start 1.93 -1.25)
			(end 1.93 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a5c820f7-1d61-446b-9f4c-6c5db79f8d9f")
		)
		(fp_line
			(start 0 0)
			(end 1.93 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b13aa604-d388-4c39-813a-ed92652f7bc4")
		)
		(fp_text user "${REFERENCE}"
			(at 5.08 0 180)
			(layer "F.Fab")
			(uuid "57acaf9c-ee80-450c-8c82-a7a40bedcbfc")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 8 "/TDI-DIN")
			(pintype "passive")
			(uuid "1e8c81a0-391a-40d6-89d0-282d946cb294")
		)
		(pad "2" thru_hole oval
			(at 10.16 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 15 "Net-(U1B-O)")
			(pintype "passive")
			(uuid "0c841b77-dfe7-4f38-aed3-51d0fb0003c0")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56cf5a")
		(at 121.92 87.63 180)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=10.16mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 10.16mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R8"
			(at 12.62 0.03 180)
			(layer "F.SilkS")
			(uuid "3d02568a-de6d-4c13-814d-4b718716c220")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100"
			(at 3.527 -0.194 180)
			(layer "F.Fab")
			(uuid "17179323-7d12-455b-9c88-47b89c9a2d9f")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "c9c76a95-da38-4d86-87ed-889730fe82d4")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "08abcf76-9919-4078-b8a5-535a2233ee50")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R? SM0603 SM0805 R?-* SM1206")
		(path "/00000000-0000-0000-0000-00004d528080")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 9.12 0)
			(end 8.35 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "38b456c1-03e2-43b9-a568-c4bcd8d089ac")
		)
		(fp_line
			(start 8.35 1.37)
			(end 8.35 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f11fd64b-2f8a-452e-ba1d-2538f2f696bd")
		)
		(fp_line
			(start 8.35 -1.37)
			(end 1.81 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1086fa06-6f1a-4753-967f-617561fd7ff2")
		)
		(fp_line
			(start 1.81 1.37)
			(end 8.35 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cb2f10af-e3c0-4946-b02a-472fc137e0a8")
		)
		(fp_line
			(start 1.81 -1.37)
			(end 1.81 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8b95c03e-33c4-4768-95cb-2122697e371b")
		)
		(fp_line
			(start 1.04 0)
			(end 1.81 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8dd672ee-22b9-4f65-a810-9cef74edcf02")
		)
		(fp_line
			(start 11.21 1.5)
			(end 11.21 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "2c749d5f-0fab-42ca-8f39-826a1bb33428")
		)
		(fp_line
			(start 11.21 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "ce7d29af-866a-40ab-8040-b2d6c893cd2d")
		)
		(fp_line
			(start -1.05 1.5)
			(end 11.21 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "f4f32082-9884-4bec-ae56-e265537ecff3")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "2f68e6e1-dfe1-4198-941b-94689b5b794e")
		)
		(fp_line
			(start 10.16 0)
			(end 8.23 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d6efed4f-3100-4fee-9878-7404176908cb")
		)
		(fp_line
			(start 8.23 1.25)
			(end 8.23 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "7b75ee0e-5211-42d5-8eb1-2421f8ca2743")
		)
		(fp_line
			(start 8.23 -1.25)
			(end 1.93 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5b383d40-0552-4e8e-a1b1-7d55f8af50b8")
		)
		(fp_line
			(start 1.93 1.25)
			(end 8.23 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a8445d58-5eb3-4631-a5a8-5a2d81e33f59")
		)
		(fp_line
			(start 1.93 -1.25)
			(end 1.93 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "30cea4da-2c7e-4221-b491-b45f400d07c2")
		)
		(fp_line
			(start 0 0)
			(end 1.93 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a3391689-131e-4982-840c-80a25a996f30")
		)
		(fp_text user "${REFERENCE}"
			(at 6.955 0.395 180)
			(layer "F.Fab")
			(uuid "72a4f7e3-b647-4a18-9dc1-508f95e2b20a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 37 "Net-(U1B-E)")
			(pintype "passive")
			(uuid "b9ad73cc-419f-4fe5-aa1a-9ffbe839ca5d")
		)
		(pad "2" thru_hole oval
			(at 10.16 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 2 "/CTRL-D3")
			(pintype "passive")
			(uuid "25db75d3-b9cd-47d9-b91a-bf24f0210e4f")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56cf70")
		(at 121.92 92.71 180)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=10.16mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 10.16mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R7"
			(at 5.08 -2.31 180)
			(layer "F.SilkS")
			(uuid "6af0dd12-3f9b-43b3-9fc8-abe3e6e6444a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100"
			(at 3.976 0.033 180)
			(layer "F.Fab")
			(uuid "06dc683e-4c95-4b01-9f60-0982bf47fc5a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "0863c9e4-3b78-443f-b9d3-bf789dd4cecb")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "e82dedc2-c606-4412-906c-2d1d23a61368")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R? SM0603 SM0805 R?-* SM1206")
		(path "/00000000-0000-0000-0000-00004d52807f")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 9.12 0)
			(end 8.35 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cecf59cd-0983-498f-9907-bec16b9dc8a5")
		)
		(fp_line
			(start 8.35 1.37)
			(end 8.35 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "217a1ff9-3d5a-4728-8d8b-ed0b01da7b89")
		)
		(fp_line
			(start 8.35 -1.37)
			(end 1.81 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "50ba7610-7d79-4a14-b4a9-abd2f15bc915")
		)
		(fp_line
			(start 1.81 1.37)
			(end 8.35 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "eb35e802-6dec-4c44-bbf3-6dffbed6d2d6")
		)
		(fp_line
			(start 1.81 -1.37)
			(end 1.81 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d4b36ded-e04c-4289-ba4e-d48139959274")
		)
		(fp_line
			(start 1.04 0)
			(end 1.81 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7851464d-728a-4e1d-9950-cb739924a37f")
		)
		(fp_line
			(start 11.21 1.5)
			(end 11.21 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "76625405-eaba-4c01-94cf-f9e00736a3ee")
		)
		(fp_line
			(start 11.21 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "0bbbb612-e4f4-42d9-8658-3be63a1edb7d")
		)
		(fp_line
			(start -1.05 1.5)
			(end 11.21 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "d211e97c-ceb6-4d6c-8099-14590eaac10a")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "dd92914f-0dbe-4652-82fa-a994fa5700e2")
		)
		(fp_line
			(start 10.16 0)
			(end 8.23 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b22ce13f-a4b7-48ec-b6e7-bdd94ca8c8d6")
		)
		(fp_line
			(start 8.23 1.25)
			(end 8.23 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f61ebdda-6f14-499e-b3af-f58c012eefc9")
		)
		(fp_line
			(start 8.23 -1.25)
			(end 1.93 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "96b83081-105b-41af-9e4d-48382eb6ecf5")
		)
		(fp_line
			(start 1.93 1.25)
			(end 8.23 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b8df5375-4f6e-4c30-b6bd-d56e44c8c95f")
		)
		(fp_line
			(start 1.93 -1.25)
			(end 1.93 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "9773acc0-9011-45d8-a47d-925761896a07")
		)
		(fp_line
			(start 0 0)
			(end 1.93 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "da5ea84d-c123-4101-b662-b058a95c07e4")
		)
		(fp_text user "${REFERENCE}"
			(at 7.832 0.14 180)
			(layer "F.Fab")
			(uuid "644838bc-5bc5-4490-b5e9-a1e1d4dde053")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 36 "Net-(U1B-D)")
			(pintype "passive")
			(uuid "382349e0-5f94-485c-8acb-85f5fea9fa22")
		)
		(pad "2" thru_hole oval
			(at 10.16 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 9 "/TDI-DIN-D0")
			(pintype "passive")
			(uuid "bb5bda22-3abc-49ad-89e0-9f28048757c8")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56cf86")
		(at 121.9327 83.82 180)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=10.16mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 10.16mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R6"
			(at 12.6327 0.02 180)
			(layer "F.SilkS")
			(uuid "b8fe7c24-8287-4595-ad6c-84440b2b898d")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100"
			(at 3.6467 -0.496 180)
			(layer "F.Fab")
			(uuid "1c23d1a6-0158-4a68-8bda-7cdda9ad4206")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "6074e1ac-aa34-49bb-8259-0cd0746b3529")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "86f35a10-3b53-4a76-bbcb-be266731b693")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R? SM0603 SM0805 R?-* SM1206")
		(path "/00000000-0000-0000-0000-00003ebf7d26")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 9.12 0)
			(end 8.35 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2d673bca-cb09-4527-b58c-fd0c8abb7cb2")
		)
		(fp_line
			(start 8.35 1.37)
			(end 8.35 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5211386c-31a0-4bc0-b341-8db9b72e05be")
		)
		(fp_line
			(start 8.35 -1.37)
			(end 1.81 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8efe9a19-2fa4-4b40-a62d-527077f91efb")
		)
		(fp_line
			(start 1.81 1.37)
			(end 8.35 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7d37b677-fb47-4f10-a615-72dbbebe7a18")
		)
		(fp_line
			(start 1.81 -1.37)
			(end 1.81 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c80ec601-059c-4522-8fbe-6cdfdbf3daa0")
		)
		(fp_line
			(start 1.04 0)
			(end 1.81 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a98ee0c8-0454-4899-8a5a-53c6265fde32")
		)
		(fp_line
			(start 11.21 1.5)
			(end 11.21 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "7c9f080c-79a5-41eb-a17c-fb1071b753ef")
		)
		(fp_line
			(start 11.21 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "4b4770b8-3670-4c74-8171-9175727bf03d")
		)
		(fp_line
			(start -1.05 1.5)
			(end 11.21 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "bd709008-12b5-41fd-aed3-d710dee0842f")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "604fe0d0-7b52-4f4c-929a-97f3a610503f")
		)
		(fp_line
			(start 10.16 0)
			(end 8.23 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2d2a91fb-11b9-4b2d-8335-b7151a6e2205")
		)
		(fp_line
			(start 8.23 1.25)
			(end 8.23 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "527b99a7-c029-4ab6-88dc-412cf9e959d8")
		)
		(fp_line
			(start 8.23 -1.25)
			(end 1.93 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ac785513-d68c-4a86-8d74-432c76c0475f")
		)
		(fp_line
			(start 1.93 1.25)
			(end 8.23 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "107bf8e6-96e8-4b8d-8c79-6e9c882eb71e")
		)
		(fp_line
			(start 1.93 -1.25)
			(end 1.93 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "256e9175-3e94-4299-bde6-3d8fc3237c61")
		)
		(fp_line
			(start 0 0)
			(end 1.93 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "868f648b-7536-418e-9ca5-126d5de762c2")
		)
		(fp_text user "${REFERENCE}"
			(at 7.2087 0.307 180)
			(layer "F.Fab")
			(uuid "4a6a3ffe-c936-4779-b075-18b2aaa87802")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 35 "Net-(U1A-E)")
			(pintype "passive")
			(uuid "10c23125-6d32-48e3-b72a-dd8b5864ae26")
		)
		(pad "2" thru_hole oval
			(at 10.16 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 7 "/TD0-PROG-D4")
			(pintype "passive")
			(uuid "a2943ec0-19e8-47a6-a579-0e1c46243f05")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56cf9c")
		(at 137.16 72.39)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=10.16mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 10.16mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R2"
			(at 5.08 -2.31 0)
			(layer "F.SilkS")
			(uuid "88f9bdda-b970-4906-a56e-10642010295e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "5,1K"
			(at 1.84 -2.59 0)
			(layer "F.Fab")
			(uuid "7826a96b-5143-44ba-8fa0-38ae5dc9291e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "e1ae0330-4e17-49fc-9880-047fc77533cd")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "fb22ea17-2310-4429-b181-ee7c26e1e19b")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R? SM0603 SM0805 R?-* SM1206")
		(path "/00000000-0000-0000-0000-00003ebf8187")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 1.04 0)
			(end 1.81 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "84a976c0-9268-4661-a685-7d519bb53475")
		)
		(fp_line
			(start 1.81 -1.37)
			(end 1.81 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cac2213b-ae2f-4702-8dda-9dcbc1fede0b")
		)
		(fp_line
			(start 1.81 1.37)
			(end 8.35 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4b224915-68d1-4c0b-b880-efb901b9605a")
		)
		(fp_line
			(start 8.35 -1.37)
			(end 1.81 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "26ce0bce-e519-4bce-93c0-31b17143db6a")
		)
		(fp_line
			(start 8.35 1.37)
			(end 8.35 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "406220e1-4b7f-49a7-87c9-7a0f15212606")
		)
		(fp_line
			(start 9.12 0)
			(end 8.35 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "513f7c6f-b24a-40d1-90f3-50b347821443")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "fde18105-735a-4b65-ac36-6ac7240289eb")
		)
		(fp_line
			(start -1.05 1.5)
			(end 11.21 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3eb13435-c2ba-4ad5-8575-416c97044679")
		)
		(fp_line
			(start 11.21 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "4c3af9cf-b3c2-41a6-90d0-ddca54540448")
		)
		(fp_line
			(start 11.21 1.5)
			(end 11.21 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "e607b523-b68e-4558-95d4-e2d7aede787c")
		)
		(fp_line
			(start 0 0)
			(end 1.93 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "49cc14fc-bcc3-4db1-a8d3-f632ae1f5836")
		)
		(fp_line
			(start 1.93 -1.25)
			(end 1.93 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "4bea7726-5384-4d88-b076-84a89aa2c03e")
		)
		(fp_line
			(start 1.93 1.25)
			(end 8.23 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "84b7a706-79be-4b43-83df-005096386d52")
		)
		(fp_line
			(start 8.23 -1.25)
			(end 1.93 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "be06ba8c-3b47-466a-9247-6d73768bb559")
		)
		(fp_line
			(start 8.23 1.25)
			(end 8.23 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "552c9b0a-2d1a-4243-adb8-20befa3aaa71")
		)
		(fp_line
			(start 10.16 0)
			(end 8.23 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a03cb0e3-0216-4165-91be-c84a71382c0a")
		)
		(fp_text user "${REFERENCE}"
			(at 5.08 0 0)
			(layer "F.Fab")
			(uuid "ac1ed8e4-d0b2-45b6-8f6e-550051c30afc")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 14 "VCC")
			(pintype "passive")
			(uuid "6b7bfb5b-317c-44b2-bc57-aeb520a2f5e8")
		)
		(pad "2" thru_hole oval
			(at 10.16 0)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 16 "Net-(U1A-O)")
			(pintype "passive")
			(uuid "7e170c02-a1dd-437b-a508-4a6ee05b0f3e")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56cfc8")
		(at 151.13 73.66)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=10.16mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 10.16mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R5"
			(at 13.27 -0.16 0)
			(layer "F.SilkS")
			(uuid "032992b6-3e10-4fe0-8f6b-7f16f7a1183c")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "1K"
			(at 5.08 2.31 0)
			(layer "F.Fab")
			(uuid "d7b9b03e-ac5b-4723-a8b7-d7edd4d036d2")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "17a3428c-807a-4785-bd51-ab22a4128de4")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "9cb9d2a2-136f-4135-bf0f-b60697f70d45")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R? SM0603 SM0805 R?-* SM1206")
		(path "/00000000-0000-0000-0000-00003ebf818e")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 1.04 0)
			(end 1.81 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3061c376-b1ce-4464-8f2d-bb1c6a29680a")
		)
		(fp_line
			(start 1.81 -1.37)
			(end 1.81 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e22e3569-1b49-4ec9-a157-cea74cb3d3b4")
		)
		(fp_line
			(start 1.81 1.37)
			(end 8.35 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a1b90a0d-bc19-4f34-8003-bbe42029827e")
		)
		(fp_line
			(start 8.35 -1.37)
			(end 1.81 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "be8be70c-024a-4b5c-a287-6920518dbd17")
		)
		(fp_line
			(start 8.35 1.37)
			(end 8.35 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bc9d784d-8911-48ae-b10f-be6796edd715")
		)
		(fp_line
			(start 9.12 0)
			(end 8.35 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5f987609-582a-411c-9ad4-4c2951e028c1")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "e0e32e65-aa0d-498d-bcda-b67766d379a9")
		)
		(fp_line
			(start -1.05 1.5)
			(end 11.21 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "1a8fee0b-1ef3-4591-b36a-4eea6cdcba1c")
		)
		(fp_line
			(start 11.21 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "0a4040e0-6854-4b41-b24b-69de1939aba2")
		)
		(fp_line
			(start 11.21 1.5)
			(end 11.21 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "2ad8e656-9702-446a-8102-8ba22a22b379")
		)
		(fp_line
			(start 0 0)
			(end 1.93 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "30a82c78-6dc6-4f5a-a6f9-622059992c06")
		)
		(fp_line
			(start 1.93 -1.25)
			(end 1.93 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "337a0724-7387-4085-8c98-624b0205bc8c")
		)
		(fp_line
			(start 1.93 1.25)
			(end 8.23 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2cf4df95-3994-4027-ad7d-fe36bc276b41")
		)
		(fp_line
			(start 8.23 -1.25)
			(end 1.93 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "e10102f8-b6c4-4aa3-b272-dd6b0d0a8d87")
		)
		(fp_line
			(start 8.23 1.25)
			(end 8.23 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "fb34cf4f-7b12-4322-a1a3-fbf079566d45")
		)
		(fp_line
			(start 10.16 0)
			(end 8.23 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b2612154-2cb5-4926-9526-4cab9576f47c")
		)
		(fp_text user "${REFERENCE}"
			(at 5.08 0 0)
			(layer "F.Fab")
			(uuid "0a597070-4c07-47f4-be46-73d843781cb0")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 14 "VCC")
			(pintype "passive")
			(uuid "660d8bce-d1bc-4f67-a7bc-0594a370d836")
		)
		(pad "2" thru_hole oval
			(at 10.16 0)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pintype "passive")
			(uuid "bb35d764-3e34-4805-98c0-a547e67f84fd")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56cfde")
		(at 121.92 77.216 180)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=10.16mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 10.16mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R10"
			(at -2.88 0.016 180)
			(layer "F.SilkS")
			(uuid "d294f47b-0b60-48a1-bc58-e759a4e042d1")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100"
			(at 3.34 -0.459 180)
			(layer "F.Fab")
			(uuid "09086b8a-5d1a-4416-8986-93e5f9ce8011")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "22d04ae4-966a-41a7-ae66-0cb4f7aa2c12")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "834efaf8-ca91-464a-9812-81444e117b91")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R? SM0603 SM0805 R?-* SM1206")
		(path "/00000000-0000-0000-0000-00003ebf7d31")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 9.12 0)
			(end 8.35 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8ef4de2a-80bd-46f3-b502-c10855d1734b")
		)
		(fp_line
			(start 8.35 1.37)
			(end 8.35 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fb0c08a0-ddaf-4dba-967a-4606852f0a5e")
		)
		(fp_line
			(start 8.35 -1.37)
			(end 1.81 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "12222823-02d3-48eb-a2be-53b5568cb510")
		)
		(fp_line
			(start 1.81 1.37)
			(end 8.35 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a11e2be9-2992-4517-bf04-8787aba264b3")
		)
		(fp_line
			(start 1.81 -1.37)
			(end 1.81 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7eed7018-86d2-4a80-8b04-a278bc4d681f")
		)
		(fp_line
			(start 1.04 0)
			(end 1.81 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "87535cb8-1f19-40cd-a9df-6c57e4a8b6c5")
		)
		(fp_line
			(start 11.21 1.5)
			(end 11.21 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "6e5ab2f6-364c-4905-9318-f72d0267eec0")
		)
		(fp_line
			(start 11.21 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3b80944b-b515-4b25-86e4-2dfcad6aa33b")
		)
		(fp_line
			(start -1.05 1.5)
			(end 11.21 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "d380977a-27f8-492a-8ba3-201b1da78d25")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "8b08b6ea-6ea1-4787-9f42-dd15a84283a8")
		)
		(fp_line
			(start 10.16 0)
			(end 8.23 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0a46fa33-3ad6-489b-82b7-ae3de3b84c9c")
		)
		(fp_line
			(start 8.23 1.25)
			(end 8.23 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "e76e5e6f-d760-4b24-910f-9005b8ab4ee5")
		)
		(fp_line
			(start 8.23 -1.25)
			(end 1.93 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ff793a90-7b96-4e0e-b779-0bd08144a2a5")
		)
		(fp_line
			(start 1.93 1.25)
			(end 8.23 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "759842f5-4391-4ff1-b778-ae602edcee2f")
		)
		(fp_line
			(start 1.93 -1.25)
			(end 1.93 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "aa30c6bb-0ea8-4850-9d4d-11ba8b1896e7")
		)
		(fp_line
			(start 0 0)
			(end 1.93 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "bdf30a7b-f9e1-4dab-b2da-4024dab2c6eb")
		)
		(fp_text user "${REFERENCE}"
			(at 6.674 0.264 180)
			(layer "F.Fab")
			(uuid "b61c68ed-4eb0-4e6b-98dc-e72a90fd77d3")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 39 "Net-(U1C-D)")
			(pintype "passive")
			(uuid "7bb1b896-6846-40a6-90bd-fb47ab1c4cd1")
		)
		(pad "2" thru_hole oval
			(at 10.16 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "/CLK-D1")
			(pintype "passive")
			(uuid "79c3e59c-5074-45ca-8ca6-bb089b0c1f57")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:D_A-405_P7.62mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56d036")
		(at 160.02 78.74 180)
		(descr "Diode, A-405 series, Axial, Horizontal, pin pitch=7.62mm, , length*diameter=5.2*2.7mm^2, , http://www.diodes.com/_files/packages/A-405.pdf")
		(tags "Diode A-405 series Axial Horizontal pin pitch 7.62mm  length 5.2mm diameter 2.7mm")
		(property "Reference" "D2"
			(at 3.81 -2.47 180)
			(layer "F.SilkS")
			(uuid "1f5570ca-a739-4543-943f-c0a0cb8e67b0")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "BAT46"
			(at 7.12 -2.56 180)
			(layer "F.Fab")
			(uuid "684ca0fd-0b3d-4de0-a79f-f7da97de0aed")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "9c7a89b4-65f4-4e00-9e2c-3999b3dcbf4e")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "d9953df0-1b6d-49a9-b17b-aab6baf08888")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "D? S*")
		(path "/00000000-0000-0000-0000-00003ebf8176")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 6.53 1.47)
			(end 6.53 1.14)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4dd8a510-c0c8-4c06-8efe-2bde984259e2")
		)
		(fp_line
			(start 6.53 -1.47)
			(end 6.53 -1.14)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fa718208-3755-4775-985c-dcca011bf845")
		)
		(fp_line
			(start 2.11 -1.47)
			(end 2.11 1.47)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "529f8a0f-c4fc-4e49-a318-d9bf904a8668")
		)
		(fp_line
			(start 1.99 -1.47)
			(end 1.99 1.47)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b7340071-77ca-4b12-b4cb-52c73d6a7831")
		)
		(fp_line
			(start 1.87 -1.47)
			(end 1.87 1.47)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e8cc377e-3f6d-4aa1-a3ab-ff6d848fee09")
		)
		(fp_line
			(start 1.09 1.47)
			(end 6.53 1.47)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e93270c7-ecb9-43a2-9e43-a7a6d7c9c10c")
		)
		(fp_line
			(start 1.09 1.14)
			(end 1.09 1.47)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4ffa1396-6af3-4875-988a-5ead83e44128")
		)
		(fp_line
			(start 1.09 -1.14)
			(end 1.09 -1.47)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "68cfec6a-09f9-4433-836b-6e44067ca080")
		)
		(fp_line
			(start 1.09 -1.47)
			(end 6.53 -1.47)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "852ddc78-f4d8-46b0-b335-29c7c5da4896")
		)
		(fp_line
			(start 8.77 1.6)
			(end 8.77 -1.6)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b496f820-556a-41a3-b859-b8cfc36b2d3b")
		)
		(fp_line
			(start 8.77 -1.6)
			(end -1.15 -1.6)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "48173a1a-a31c-4cb0-8adb-3a9cde25acd8")
		)
		(fp_line
			(start -1.15 1.6)
			(end 8.77 1.6)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "9b2fa4f0-feca-44e3-aa56-560b0d19f46f")
		)
		(fp_line
			(start -1.15 -1.6)
			(end -1.15 1.6)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "5a874863-f511-484f-968a-18e125d9474c")
		)
		(fp_line
			(start 7.62 0)
			(end 6.41 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c38a051f-875f-43f9-92ef-be7b8bef00b0")
		)
		(fp_line
			(start 6.41 1.35)
			(end 6.41 -1.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "1801cb50-4b61-4e85-a5be-dba48b0efcf2")
		)
		(fp_line
			(start 6.41 -1.35)
			(end 1.21 -1.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "05cced5b-8d23-4379-be40-f9f0a05b97a5")
		)
		(fp_line
			(start 2.09 -1.35)
			(end 2.09 1.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ac89df56-ca81-4cda-9b59-8ce3b6df09fc")
		)
		(fp_line
			(start 1.99 -1.35)
			(end 1.99 1.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "e3cedfce-8118-49f0-a65b-46e91f18f3e3")
		)
		(fp_line
			(start 1.89 -1.35)
			(end 1.89 1.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d6be5827-40fe-44dd-9d08-b6af06a874ec")
		)
		(fp_line
			(start 1.21 1.35)
			(end 6.41 1.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "62f0dc37-fb3e-47e7-9f51-19fba29b203c")
		)
		(fp_line
			(start 1.21 -1.35)
			(end 1.21 1.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "781471c0-3d0b-4d14-8ba3-1685af983f10")
		)
		(fp_line
			(start 0 0)
			(end 1.21 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "7e6314d9-af68-4a7f-a5f2-c99e61e85a03")
		)
		(fp_text user "K"
			(at 0 -1.9 180)
			(layer "F.SilkS")
			(uuid "324aad6e-d9ed-4c28-aadc-70c2396e6c7a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(fp_text user "K"
			(at 0 -1.9 180)
			(layer "F.Fab")
			(uuid "076fa760-5012-4924-b456-246bb642be85")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(fp_text user "${REFERENCE}"
			(at 4.2 0 180)
			(layer "F.Fab")
			(uuid "51504344-56f0-4e37-8d7e-61a481905e61")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0 180)
			(size 1.8 1.8)
			(drill 0.9)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 4 "/PWR_3,3-5V")
			(pinfunction "A")
			(pintype "passive")
			(uuid "12802cfd-c86e-4201-933e-bb8485ebb2df")
		)
		(pad "2" thru_hole oval
			(at 7.62 0 180)
			(size 1.8 1.8)
			(drill 0.9)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 14 "VCC")
			(pinfunction "K")
			(pintype "passive")
			(uuid "dc2851e0-549e-494b-8894-670e2ea76275")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Diode_THT.3dshapes/D_A-405_P7.62mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:D_A-405_P7.62mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56d054")
		(at 134.62 72.39 180)
		(descr "Diode, A-405 series, Axial, Horizontal, pin pitch=7.62mm, , length*diameter=5.2*2.7mm^2, , http://www.diodes.com/_files/packages/A-405.pdf")
		(tags "Diode A-405 series Axial Horizontal pin pitch 7.62mm  length 5.2mm diameter 2.7mm")
		(property "Reference" "D1"
			(at 3.81 -2.47 180)
			(layer "F.SilkS")
			(uuid "861e2843-ad09-4ad1-a1dc-e9183d6d99c1")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "BAT46"
			(at 3.81 2.47 180)
			(layer "F.Fab")
			(uuid "728d2750-364e-4160-83d3-12a8f758976f")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "a86a76bd-3f37-45af-a934-e4bfa610abb2")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "e0ba1fc8-**************-3307f60e4148")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "D? S*")
		(path "/00000000-0000-0000-0000-00003ebf815e")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 6.53 1.47)
			(end 6.53 1.14)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6a00fa1f-9b49-4c25-8ec4-28e13f80a87b")
		)
		(fp_line
			(start 6.53 -1.47)
			(end 6.53 -1.14)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d0257f4e-dbd0-4317-a1cc-7d2dc29e0357")
		)
		(fp_line
			(start 2.11 -1.47)
			(end 2.11 1.47)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4079b76b-9e36-4746-a2ee-c14f11107e79")
		)
		(fp_line
			(start 1.99 -1.47)
			(end 1.99 1.47)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "14ae6755-f697-41b9-a38c-e825a6144a87")
		)
		(fp_line
			(start 1.87 -1.47)
			(end 1.87 1.47)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b5a84041-5a99-4283-a052-2d8585fc9ae4")
		)
		(fp_line
			(start 1.09 1.47)
			(end 6.53 1.47)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d86ec884-cdf2-4fdf-88a0-9df749826912")
		)
		(fp_line
			(start 1.09 1.14)
			(end 1.09 1.47)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "17c5de42-4da4-4b83-92bb-5ffc242eb964")
		)
		(fp_line
			(start 1.09 -1.14)
			(end 1.09 -1.47)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "50e0e878-d047-4416-8678-a40bda3dde36")
		)
		(fp_line
			(start 1.09 -1.47)
			(end 6.53 -1.47)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "88f88238-87f1-40b7-937f-11ea5de25624")
		)
		(fp_line
			(start 8.77 1.6)
			(end 8.77 -1.6)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "bb305beb-7d28-4dff-9da5-356f310ba714")
		)
		(fp_line
			(start 8.77 -1.6)
			(end -1.15 -1.6)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "112852c1-2d62-415e-bc33-3e77647f0499")
		)
		(fp_line
			(start -1.15 1.6)
			(end 8.77 1.6)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "d1a5f6fd-3a4e-43b5-a643-9daad9aca0fe")
		)
		(fp_line
			(start -1.15 -1.6)
			(end -1.15 1.6)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "84be302a-20f8-4f97-a183-474814410b67")
		)
		(fp_line
			(start 7.62 0)
			(end 6.41 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5effd22f-89bb-4771-a09a-19425f2dbfa8")
		)
		(fp_line
			(start 6.41 1.35)
			(end 6.41 -1.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c5efb42a-9bb3-4c83-a25f-2e5b4bd4cc4a")
		)
		(fp_line
			(start 6.41 -1.35)
			(end 1.21 -1.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "27bbe3b1-fee4-448f-9c7f-07196ae5a9b0")
		)
		(fp_line
			(start 2.09 -1.35)
			(end 2.09 1.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "24c74fa0-905e-446a-a6e2-1aed0559dac8")
		)
		(fp_line
			(start 1.99 -1.35)
			(end 1.99 1.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "4a286a7d-578a-424b-9599-8b8b961396ca")
		)
		(fp_line
			(start 1.89 -1.35)
			(end 1.89 1.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ca71d0e8-5ff7-4e7b-a541-73e85f40bd41")
		)
		(fp_line
			(start 1.21 1.35)
			(end 6.41 1.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "9ca30aa4-af78-40ff-9b4e-8889b11eee6c")
		)
		(fp_line
			(start 1.21 -1.35)
			(end 1.21 1.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2dc438a6-1762-4dab-827d-6fbf109fc61b")
		)
		(fp_line
			(start 0 0)
			(end 1.21 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a96e132f-8e56-4aae-85bd-8854f869f376")
		)
		(fp_text user "K"
			(at 0 -1.9 180)
			(layer "F.SilkS")
			(uuid "3eca0318-537c-4bb1-9cd2-1bf2c053cfdc")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(fp_text user "K"
			(at 0 -1.9 180)
			(layer "F.Fab")
			(uuid "020d07b0-9fdf-42e1-a0fe-eee42c54d3ff")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(fp_text user "${REFERENCE}"
			(at 4.2 0 180)
			(layer "F.Fab")
			(uuid "25555504-11dd-45ad-bbd9-93a64895fbd8")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0 180)
			(size 1.8 1.8)
			(drill 0.9)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 14 "VCC")
			(pinfunction "A")
			(pintype "passive")
			(uuid "727555b6-6be7-4b3a-9046-d3ca876d6786")
		)
		(pad "2" thru_hole oval
			(at 7.62 0 180)
			(size 1.8 1.8)
			(drill 0.9)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 22 "Net-(D1-K)")
			(pinfunction "K")
			(pintype "passive")
			(uuid "738a02c5-1fea-425e-a405-757d413a265c")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Diode_THT.3dshapes/D_A-405_P7.62mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a56d255")
		(at 121.92 73.8632 180)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=10.16mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 10.16mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R1"
			(at -2.18 -0.2368 180)
			(layer "F.SilkS")
			(uuid "3ea00045-1294-4b48-bb93-3ff6a52356e0")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100"
			(at 3.701 -0.4378 180)
			(layer "F.Fab")
			(uuid "b6c927b8-22d8-401d-8aea-4403278b071e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "4cfc6b1f-91e6-44f8-8fc9-8dd23d92ba21")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "c369bc3c-0468-4207-9d12-2782162d1857")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R? SM0603 SM0805 R?-* SM1206")
		(path "/00000000-0000-0000-0000-00003ebf7d16")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 9.12 0)
			(end 8.35 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b1a91be9-061e-4d82-995f-8998a8337bfe")
		)
		(fp_line
			(start 8.35 1.37)
			(end 8.35 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "50098a21-c078-41cd-acc4-270598ce8188")
		)
		(fp_line
			(start 8.35 -1.37)
			(end 1.81 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ae6016b5-55c1-4ae1-adeb-362656e2c21f")
		)
		(fp_line
			(start 1.81 1.37)
			(end 8.35 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "74741ea5-07e4-4853-86d8-67112686d916")
		)
		(fp_line
			(start 1.81 -1.37)
			(end 1.81 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e3109744-2845-4314-98b8-4e6d751dee9d")
		)
		(fp_line
			(start 1.04 0)
			(end 1.81 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "10dbebcd-65c4-4925-b0a7-83c6bf1e318f")
		)
		(fp_line
			(start 11.21 1.5)
			(end 11.21 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "9f324c2b-63b1-4c98-bd87-f4321a75d2e9")
		)
		(fp_line
			(start 11.21 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "101273d8-deed-4e4c-adde-c8a5e7ac358a")
		)
		(fp_line
			(start -1.05 1.5)
			(end 11.21 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "ac584f02-c28e-4240-9fe2-b755ab3afec7")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3e73701c-6431-4e39-aa14-ae1bdad6bf26")
		)
		(fp_line
			(start 10.16 0)
			(end 8.23 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "87cd8c63-1f56-4c86-849f-e60959d4e00b")
		)
		(fp_line
			(start 8.23 1.25)
			(end 8.23 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "59d7e4c8-40c5-42c8-870f-453528f58004")
		)
		(fp_line
			(start 8.23 -1.25)
			(end 1.93 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "41476c3d-d320-4eed-887e-310f00f145b9")
		)
		(fp_line
			(start 1.93 1.25)
			(end 8.23 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6744a7a1-9439-4673-b011-c7378bfd7ed7")
		)
		(fp_line
			(start 1.93 -1.25)
			(end 1.93 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "36e2b9fb-2d6e-4808-a84d-45fb6c0a6b7d")
		)
		(fp_line
			(start 0 0)
			(end 1.93 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ba63558b-f628-45f3-82a8-9e68d09b0ade")
		)
		(fp_text user "${REFERENCE}"
			(at 7.075 0.3252 180)
			(layer "F.Fab")
			(uuid "db10cdc9-28fc-4b46-a749-756feedccdcf")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 22 "Net-(D1-K)")
			(pintype "passive")
			(uuid "8c0c8714-ad58-43f2-a75c-b6b749fe2807")
		)
		(pad "2" thru_hole oval
			(at 10.16 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 12 "/VCC_SENSE-ERROR*")
			(pintype "passive")
			(uuid "7665802f-e7f3-4377-a628-fe63417c94df")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:C_Axial_L5.1mm_D3.1mm_P12.50mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a5834a9")
		(at 134.62 76.2)
		(descr "C, Axial series, Axial, Horizontal, pin pitch=12.5mm, , length*diameter=5.1*3.1mm^2, http://www.vishay.com/docs/45231/arseries.pdf")
		(tags "C Axial series Axial Horizontal pin pitch 12.5mm  length 5.1mm diameter 3.1mm")
		(property "Reference" "C1"
			(at -2.62 -0.1 0)
			(layer "F.SilkS")
			(uuid "18f32f9a-ab01-4c6a-83b8-62169619e05f")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "1uF"
			(at 6.25 2.61 0)
			(layer "F.Fab")
			(uuid "95b1f4a1-cd60-4fb8-8dcd-bb9024f974a1")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "22314dd5-b773-44a6-8869-fb1ef2605125")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "edca671d-4d24-45e3-aef1-55eefc267495")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "CP* SM*")
		(path "/00000000-0000-0000-0000-00003ebf82c6")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 1.04 0)
			(end 3.58 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1d7adb68-be6d-4468-a69b-d384f0f8111b")
		)
		(fp_line
			(start 3.58 -1.67)
			(end 3.58 1.67)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "975bb8a9-3850-479b-9153-74529da180f2")
		)
		(fp_line
			(start 3.58 1.67)
			(end 8.92 1.67)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "975481bd-884f-4920-8759-97f79e33fc92")
		)
		(fp_line
			(start 8.92 -1.67)
			(end 3.58 -1.67)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fa2d6995-fcdd-4e71-bbb6-753343bb8ddc")
		)
		(fp_line
			(start 8.92 1.67)
			(end 8.92 -1.67)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c0e30e40-4d89-4a22-be18-1ce93392d676")
		)
		(fp_line
			(start 11.46 0)
			(end 8.92 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3b6bc460-a9aa-4782-a7d4-aa95c288938b")
		)
		(fp_line
			(start -1.05 -1.8)
			(end -1.05 1.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "16f65c78-c11a-425e-8f0e-80d33b0b9696")
		)
		(fp_line
			(start -1.05 1.8)
			(end 13.55 1.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "4095c384-e12c-4dc6-a648-30f90dcfb5b1")
		)
		(fp_line
			(start 13.55 -1.8)
			(end -1.05 -1.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "19efb91c-efb5-4d99-bbbc-51ab4e0f8e87")
		)
		(fp_line
			(start 13.55 1.8)
			(end 13.55 -1.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "0d74a688-e72f-4e26-b907-77aa27ded5f2")
		)
		(fp_line
			(start 0 0)
			(end 3.7 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "1d531fd1-912e-4785-a5a1-b3bd5dfee047")
		)
		(fp_line
			(start 3.7 -1.55)
			(end 3.7 1.55)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2108ff84-b598-4cec-b8fb-98414e132e4a")
		)
		(fp_line
			(start 3.7 1.55)
			(end 8.8 1.55)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "01225d54-3651-47af-84e6-b37d171c0333")
		)
		(fp_line
			(start 8.8 -1.55)
			(end 3.7 -1.55)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6fafc832-875d-40a2-983e-7b06b898980e")
		)
		(fp_line
			(start 8.8 1.55)
			(end 8.8 -1.55)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "eb731b0c-9a91-4dce-83be-59faa530cd13")
		)
		(fp_line
			(start 12.5 0)
			(end 8.8 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "70669370-91e0-4eb2-a07d-c4fbb85f307d")
		)
		(fp_text user "${REFERENCE}"
			(at 6.25 0 0)
			(layer "F.Fab")
			(uuid "52fcbc16-b8d9-426f-91f3-cf5a0abcc55b")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 14 "VCC")
			(pintype "passive")
			(uuid "2b3f2d2f-4413-4386-999a-9f4008c185c0")
		)
		(pad "2" thru_hole oval
			(at 12.5 0)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pintype "passive")
			(uuid "bd9dec7e-8ca1-4cee-b91f-86498464f8d5")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Capacitor_THT.3dshapes/C_Axial_L5.1mm_D3.1mm_P12.50mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:PinHeader_1x06_P2.54mm_Vertical"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a583d55")
		(at 169.672 80.137)
		(descr "Through hole straight pin header, 1x06, 2.54mm pitch, single row")
		(tags "Through hole pin header THT 1x06 2.54mm single row")
		(property "Reference" "P1"
			(at 0 -2.33 0)
			(layer "F.SilkS")
			(uuid "218d9421-c184-429a-8abb-************")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "CONN_6"
			(at 0 15.03 0)
			(layer "F.Fab")
			(uuid "6d3a7da2-5c3d-4c84-8c50-c40536ab42ec")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "86eda3af-7c33-45c0-a617-e897ea820721")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "57a81578-0927-4893-85ac-67eb0bddea10")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-00003ebf830c")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start -1.33 -1.33)
			(end 0 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e3877f12-7e12-4ae8-99b5-01df176f21ec")
		)
		(fp_line
			(start -1.33 0)
			(end -1.33 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f883b081-c88b-403b-8aca-45b5d303a25e")
		)
		(fp_line
			(start -1.33 1.27)
			(end -1.33 14.03)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7f253f03-831d-4273-8377-97d190c69297")
		)
		(fp_line
			(start -1.33 1.27)
			(end 1.33 1.27)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "43589fca-9c0a-4868-8278-07e5c5471b70")
		)
		(fp_line
			(start -1.33 14.03)
			(end 1.33 14.03)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "560d988b-e50f-4bb0-855a-a4035e4dcaf6")
		)
		(fp_line
			(start 1.33 1.27)
			(end 1.33 14.03)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d1c5a7d8-43f9-4179-9822-1fabf1f91824")
		)
		(fp_line
			(start -1.8 -1.8)
			(end -1.8 14.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "06f4f95f-f429-4904-8135-5859f719a007")
		)
		(fp_line
			(start -1.8 14.5)
			(end 1.8 14.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "c847c434-2c63-4234-a19e-5ed407813ecf")
		)
		(fp_line
			(start 1.8 -1.8)
			(end -1.8 -1.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "5281895a-0ed8-4b85-ad0a-5e7984ef5829")
		)
		(fp_line
			(start 1.8 14.5)
			(end 1.8 -1.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "fb2a8b3e-87a9-4061-b27a-e2f58378729f")
		)
		(fp_line
			(start -1.27 -0.635)
			(end -0.635 -1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a1db0d35-1fd1-4b2e-920e-a35dff23d775")
		)
		(fp_line
			(start -1.27 13.97)
			(end -1.27 -0.635)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a1da1e96-4f0c-4c84-befd-67ea23412554")
		)
		(fp_line
			(start -0.635 -1.27)
			(end 1.27 -1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0ff37db3-b9f2-47f0-9a60-4fe60ae79ac3")
		)
		(fp_line
			(start 1.27 -1.27)
			(end 1.27 13.97)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "df6c3c84-a55e-4557-97e4-f10ddad17282")
		)
		(fp_line
			(start 1.27 13.97)
			(end -1.27 13.97)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0b94a9f1-c59a-4a11-9374-b4ed4fff4d21")
		)
		(fp_text user "${REFERENCE}"
			(at 0 6.35 90)
			(layer "F.Fab")
			(uuid "cf9cf20e-20e3-44b4-b943-4a64c09842c9")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0)
			(size 1.7 1.7)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 4 "/PWR_3,3-5V")
			(pinfunction "1")
			(pintype "passive")
			(uuid "e27e19f1-9c0b-4480-b63e-ae6238e44dc3")
		)
		(pad "2" thru_hole oval
			(at 0 2.54)
			(size 1.7 1.7)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pinfunction "2")
			(pintype "passive")
			(uuid "3d6c4114-fe31-4a9c-944e-f57200513aa8")
		)
		(pad "3" thru_hole oval
			(at 0 5.08)
			(size 1.7 1.7)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 6 "/TD0-DONE")
			(pinfunction "3")
			(pintype "passive")
			(uuid "40455fc5-80ca-4ebc-b854-23a4bb003b73")
		)
		(pad "4" thru_hole oval
			(at 0 7.62)
			(size 1.7 1.7)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 8 "/TDI-DIN")
			(pinfunction "4")
			(pintype "passive")
			(uuid "2816d0ee-480e-439f-8e76-f498aa49f87b")
		)
		(pad "5" thru_hole oval
			(at 0 10.16)
			(size 1.7 1.7)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 5 "/TCK-CCLK")
			(pinfunction "5")
			(pintype "passive")
			(uuid "59bd6feb-e4ad-4da4-b965-b60d494d4161")
		)
		(pad "6" thru_hole oval
			(at 0 12.7)
			(size 1.7 1.7)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 10 "/TMS-PROG")
			(pinfunction "6")
			(pintype "passive")
			(uuid "a2220205-9f1f-4dbf-a67d-8d00cb75b066")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x06_P2.54mm_Vertical.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:DIP-14_W7.62mm_LongPads"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a58f58c")
		(at 140.97 82.55)
		(descr "14-lead though-hole mounted DIP package, row spacing 7.62 mm (300 mils), LongPads")
		(tags "THT DIP DIL PDIP 2.54mm 7.62mm 300mil LongPads")
		(property "Reference" "U2"
			(at 3.81 -2.33 0)
			(layer "F.SilkS")
			(uuid "12f165f2-0f9d-42c4-8f7e-e8450b5d3586")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "74LS125"
			(at 3.81 17.57 0)
			(layer "F.Fab")
			(uuid "070a278b-7aa0-41b7-88c0-6ac6750163c7")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "013fe076-7487-4b02-91bc-662c831a4b0f")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "b99cb509-69aa-4e53-ae94-b5d9bb74acd6")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-00003ebf7edd")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 1.56 -1.33)
			(end 1.56 16.57)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "679bbe07-bf1b-467b-8d42-28a0b1087a2e")
		)
		(fp_line
			(start 1.56 16.57)
			(end 6.06 16.57)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "038d785a-b0c4-4d3c-8542-eb63a600c54f")
		)
		(fp_line
			(start 2.81 -1.33)
			(end 1.56 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "20bc013c-72a2-47de-ab63-b0f05a06d4c6")
		)
		(fp_line
			(start 6.06 -1.33)
			(end 4.81 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3d1377f0-e207-4c1d-ad24-a4e3c6776635")
		)
		(fp_line
			(start 6.06 16.57)
			(end 6.06 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f912dc72-951b-4820-ac75-1aa304a7ae1d")
		)
		(fp_arc
			(start 4.81 -1.33)
			(mid 3.81 -0.33)
			(end 2.81 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a1d893e5-309b-4010-813c-15a3dc8c09ce")
		)
		(fp_line
			(start -1.45 -1.55)
			(end -1.45 16.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "4a3d850a-db3a-44dd-9ae1-e383096fca3f")
		)
		(fp_line
			(start -1.45 16.8)
			(end 9.1 16.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "c26fc1f4-9759-4501-b458-6084c74fbfdc")
		)
		(fp_line
			(start 9.1 -1.55)
			(end -1.45 -1.55)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "8bbe08a9-9b60-403a-a9f4-398b84617779")
		)
		(fp_line
			(start 9.1 16.8)
			(end 9.1 -1.55)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "c33c3ae5-370c-43dd-9b33-cbeda138d933")
		)
		(fp_line
			(start 0.635 -0.27)
			(end 1.635 -1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "bddcab9d-f575-4e3f-bb65-279ed5b84d30")
		)
		(fp_line
			(start 0.635 16.51)
			(end 0.635 -0.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c645c1c5-7f18-4d26-9cf6-34774a1183de")
		)
		(fp_line
			(start 1.635 -1.27)
			(end 6.985 -1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "9ac92eb8-8b15-4f40-b825-302c1ab9b93d")
		)
		(fp_line
			(start 6.985 -1.27)
			(end 6.985 16.51)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "8eb477df-58b7-4fb1-a390-71f4a6567356")
		)
		(fp_line
			(start 6.985 16.51)
			(end 0.635 16.51)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "3e3c3857-de05-4e80-aed4-810a90115381")
		)
		(fp_text user "${REFERENCE}"
			(at 3.81 7.62 0)
			(layer "F.Fab")
			(uuid "15d02851-e1b3-421f-b05f-bf2bd5c2c389")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pinfunction "E")
			(pintype "input")
			(uuid "9d75a86a-1f32-4d4b-b214-82f3ad19ba7f")
		)
		(pad "2" thru_hole oval
			(at 0 2.54)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 16 "Net-(U1A-O)")
			(pinfunction "D")
			(pintype "input")
			(uuid "e26a53b2-a211-4ec9-9a25-df843e4a9b64")
		)
		(pad "3" thru_hole oval
			(at 0 5.08)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 34 "Net-(U2A-O)")
			(pinfunction "O")
			(pintype "tri_state")
			(uuid "0356a0f5-8f42-447f-996a-4e7c6443a6a3")
		)
		(pad "4" thru_hole oval
			(at 0 7.62)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pinfunction "E")
			(pintype "input")
			(uuid "b1ed72dc-5b63-494e-bc1d-7a82178483e0")
		)
		(pad "5" thru_hole oval
			(at 0 10.16)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pinfunction "D")
			(pintype "input")
			(uuid "06dc3c3b-63f5-4c84-ae2e-9854d50e5b1b")
		)
		(pad "6" thru_hole oval
			(at 0 12.7)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 40 "unconnected-(U2B-O-Pad6)")
			(pinfunction "O")
			(pintype "tri_state+no_connect")
			(uuid "1c964460-8cf5-428b-938d-ff69d3a18074")
		)
		(pad "7" thru_hole oval
			(at 0 15.24)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pinfunction "GND")
			(pintype "power_in")
			(uuid "0f0bdd72-f350-4208-82f8-0eb0f5b20c59")
		)
		(pad "8" thru_hole oval
			(at 7.62 15.24)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 42 "unconnected-(U2C-O-Pad8)")
			(pinfunction "O")
			(pintype "tri_state+no_connect")
			(uuid "e7db05f5-f671-4dbe-a34d-c12df1c8bf14")
		)
		(pad "9" thru_hole oval
			(at 7.62 12.7)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pinfunction "D")
			(pintype "input")
			(uuid "f32c9b45-2bed-430a-a7d1-8f90106ecd52")
		)
		(pad "10" thru_hole oval
			(at 7.62 10.16)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pinfunction "E")
			(pintype "input")
			(uuid "46c2258d-e41c-4f85-88ae-4726f1b7666f")
		)
		(pad "11" thru_hole oval
			(at 7.62 7.62)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 41 "unconnected-(U2D-O-Pad11)")
			(pinfunction "O")
			(pintype "tri_state+no_connect")
			(uuid "486dacd0-87cf-44aa-b9f5-b9442f8a93db")
		)
		(pad "12" thru_hole oval
			(at 7.62 5.08)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pinfunction "D")
			(pintype "input")
			(uuid "7c8111c2-2b4a-4a81-9b63-601d28106fe2")
		)
		(pad "13" thru_hole oval
			(at 7.62 2.54)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pinfunction "E")
			(pintype "input")
			(uuid "6aab3a9a-e1c5-4e4c-bc60-c75dc27f392c")
		)
		(pad "14" thru_hole oval
			(at 7.62 0)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 14 "VCC")
			(pinfunction "VCC")
			(pintype "power_in")
			(uuid "1d3301c7-cf67-46fc-8910-4fe1ad681cef")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Package_DIP.3dshapes/DIP-14_W7.62mm.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:DIP-14_W7.62mm_LongPads"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a58f5ad")
		(at 127 82.55)
		(descr "14-lead though-hole mounted DIP package, row spacing 7.62 mm (300 mils), LongPads")
		(tags "THT DIP DIL PDIP 2.54mm 7.62mm 300mil LongPads")
		(property "Reference" "U1"
			(at 3.81 -2.33 0)
			(layer "F.SilkS")
			(uuid "08e98ad4-9110-4186-86bd-435971296ede")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "74LS125"
			(at 3.81 17.57 0)
			(layer "F.Fab")
			(uuid "dfc3d816-e793-4549-a6d7-48249773feed")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "d3e69042-ac32-489c-9f9e-f2d17720e6af")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "ac357c0f-6522-4d2c-9b26-afac093a2baa")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-00003ebf7dad")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 1.56 -1.33)
			(end 1.56 16.57)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f8c7dfc0-91ff-4a78-a390-d20754aacce6")
		)
		(fp_line
			(start 1.56 16.57)
			(end 6.06 16.57)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d9b07ea5-ab07-423b-b2b9-ad255c0bd662")
		)
		(fp_line
			(start 2.81 -1.33)
			(end 1.56 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cb5346bb-fd06-4d26-ba2c-be7315259ea3")
		)
		(fp_line
			(start 6.06 -1.33)
			(end 4.81 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "08a9ce74-4bd9-4c61-b97b-308bb4018a1c")
		)
		(fp_line
			(start 6.06 16.57)
			(end 6.06 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f544bc49-a94a-4971-aaf0-fe2968015b83")
		)
		(fp_arc
			(start 4.81 -1.33)
			(mid 3.81 -0.33)
			(end 2.81 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fad5a94c-2609-41a4-843b-11fa65277daf")
		)
		(fp_line
			(start -1.45 -1.55)
			(end -1.45 16.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "a3a3aca3-3ba5-4ba4-b5fd-30cf3db96292")
		)
		(fp_line
			(start -1.45 16.8)
			(end 9.1 16.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "e98856a3-b6aa-4e29-9113-1c31642414f3")
		)
		(fp_line
			(start 9.1 -1.55)
			(end -1.45 -1.55)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "0685438f-dc50-408f-b640-9453ed3a9fdb")
		)
		(fp_line
			(start 9.1 16.8)
			(end 9.1 -1.55)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "95bf0238-f987-4f4d-8a5f-c5632dc6d699")
		)
		(fp_line
			(start 0.635 -0.27)
			(end 1.635 -1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "cbff2b3a-0197-48f7-8225-61f9eae1ecba")
		)
		(fp_line
			(start 0.635 16.51)
			(end 0.635 -0.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0aea849b-350a-4492-abbb-f69ddf0f123c")
		)
		(fp_line
			(start 1.635 -1.27)
			(end 6.985 -1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ad1996c7-a7ef-45b1-be3a-272dfadf1547")
		)
		(fp_line
			(start 6.985 -1.27)
			(end 6.985 16.51)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2619e686-35f4-456b-a3dd-0a722d1c4c57")
		)
		(fp_line
			(start 6.985 16.51)
			(end 0.635 16.51)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "aef6ef91-8611-4883-9d6d-7cdae437aa97")
		)
		(fp_text user "${REFERENCE}"
			(at 3.81 7.62 0)
			(layer "F.Fab")
			(uuid "83f38ae8-c314-4732-bc69-6ee00bb3ff62")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 35 "Net-(U1A-E)")
			(pinfunction "E")
			(pintype "input")
			(uuid "e3c8cf45-aaed-457e-b73e-b5fc88ecbcad")
		)
		(pad "2" thru_hole oval
			(at 0 2.54)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pinfunction "D")
			(pintype "input")
			(uuid "1c81992c-be6e-40bf-8af7-6fef1a04ba6f")
		)
		(pad "3" thru_hole oval
			(at 0 5.08)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 16 "Net-(U1A-O)")
			(pinfunction "O")
			(pintype "tri_state")
			(uuid "8865c04e-1de2-484d-807f-92aa9bf849d6")
		)
		(pad "4" thru_hole oval
			(at 0 7.62)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 37 "Net-(U1B-E)")
			(pinfunction "E")
			(pintype "input")
			(uuid "33da7a8c-ee5b-490c-9582-a92d6b1a6a6e")
		)
		(pad "5" thru_hole oval
			(at 0 10.16)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 36 "Net-(U1B-D)")
			(pinfunction "D")
			(pintype "input")
			(uuid "6bdf9d31-01bd-4b6d-8a20-b78811adf6a8")
		)
		(pad "6" thru_hole oval
			(at 0 12.7)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 15 "Net-(U1B-O)")
			(pinfunction "O")
			(pintype "tri_state")
			(uuid "af21886d-ddf3-4272-b9fa-bcf7a564067e")
		)
		(pad "7" thru_hole oval
			(at 0 15.24)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pinfunction "GND")
			(pintype "power_in")
			(uuid "e6be32e7-c8b1-4b4e-9651-33f6a1ef2c91")
		)
		(pad "8" thru_hole oval
			(at 7.62 15.24)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 21 "Net-(U1C-O)")
			(pinfunction "O")
			(pintype "tri_state")
			(uuid "7016adbd-3a4a-4d00-acee-45ecd8c4a3eb")
		)
		(pad "9" thru_hole oval
			(at 7.62 12.7)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 39 "Net-(U1C-D)")
			(pinfunction "D")
			(pintype "input")
			(uuid "82733397-d2a6-4952-9bd8-79cc6eed498f")
		)
		(pad "10" thru_hole oval
			(at 7.62 10.16)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 37 "Net-(U1B-E)")
			(pinfunction "E")
			(pintype "input")
			(uuid "afd15ace-1ebd-406e-89f7-a8d99205910c")
		)
		(pad "11" thru_hole oval
			(at 7.62 7.62)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 20 "Net-(U1D-O)")
			(pinfunction "O")
			(pintype "tri_state")
			(uuid "fb829f29-3714-42a3-9de3-1a85cef818e4")
		)
		(pad "12" thru_hole oval
			(at 7.62 5.08)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 38 "Net-(U1D-D)")
			(pinfunction "D")
			(pintype "input")
			(uuid "d6c4a115-8510-4b3b-98cc-4577055df330")
		)
		(pad "13" thru_hole oval
			(at 7.62 2.54)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 37 "Net-(U1B-E)")
			(pinfunction "E")
			(pintype "input")
			(uuid "f32b3439-6dd8-4a67-a5bf-b22da2ce3075")
		)
		(pad "14" thru_hole oval
			(at 7.62 0)
			(size 2.4 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 14 "VCC")
			(pinfunction "VCC")
			(pintype "power_in")
			(uuid "1b65e52d-86ed-4b40-b7e9-dbe77b3d1a19")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Package_DIP.3dshapes/DIP-14_W7.62mm.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:C_Disc_D5.1mm_W3.2mm_P5.00mm"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a59b977")
		(at 145 102.87 -90)
		(descr "C, Disc series, Radial, pin pitch=5.00mm, , diameter*width=5.1*3.2mm^2, Capacitor, http://www.vishay.com/docs/45233/krseries.pdf")
		(tags "C Disc series Radial pin pitch 5.00mm  diameter 5.1mm width 3.2mm Capacitor")
		(property "Reference" "C5"
			(at 2.93 -3.3 270)
			(layer "F.SilkS")
			(uuid "254c9678-dbac-4428-ac24-ea8c9201fde0")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100pF"
			(at 2.5 2.91 270)
			(layer "F.Fab")
			(uuid "4162f139-eb1a-4a14-b4e2-6ea44c495b69")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "0f09aa58-3c89-447a-b1e3-0451b47fe9b9")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "73541cc9-6249-47c6-8c72-81be0cb373e9")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "SM* C? C1-1")
		(path "/00000000-0000-0000-0000-00004d528086")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start -0.17 1.721)
			(end 5.17 1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "daf87ca3-ea9c-4104-b47a-67101456828f")
		)
		(fp_line
			(start -0.17 1.055)
			(end -0.17 1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "32d58ce5-ed6d-49aa-84a7-6ab7f7c2faae")
		)
		(fp_line
			(start 5.17 1.055)
			(end 5.17 1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "761d3047-19a3-4122-aecd-55fd6bb110b3")
		)
		(fp_line
			(start -0.17 -1.721)
			(end -0.17 -1.055)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "602a2bd1-3a8a-406f-b921-9fc431e645fb")
		)
		(fp_line
			(start -0.17 -1.721)
			(end 5.17 -1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "dd3e8fd9-602b-42be-9ae6-0ded502f115e")
		)
		(fp_line
			(start 5.17 -1.721)
			(end 5.17 -1.055)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "db3f49f9-5b7a-4e97-a270-f42d0c790f09")
		)
		(fp_line
			(start -1.05 1.85)
			(end 6.05 1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "74cdee7a-d052-431a-a97b-ff3b7a5a8187")
		)
		(fp_line
			(start 6.05 1.85)
			(end 6.05 -1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "2d814810-197e-405c-8113-33113ab81067")
		)
		(fp_line
			(start -1.05 -1.85)
			(end -1.05 1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "4745a082-4749-41fe-b7f4-036ea3d9c990")
		)
		(fp_line
			(start 6.05 -1.85)
			(end -1.05 -1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "0f5e8cf7-9b15-4602-b85c-f9843bddfacf")
		)
		(fp_line
			(start -0.05 1.6)
			(end 5.05 1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "de82a543-2c73-4a38-8487-0161546831b5")
		)
		(fp_line
			(start 5.05 1.6)
			(end 5.05 -1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "8c08b0e4-d0b2-4176-b306-0ead3b1fcc8a")
		)
		(fp_line
			(start -0.05 -1.6)
			(end -0.05 1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "4f12a5ae-5a77-40f3-b694-7adcb163aa47")
		)
		(fp_line
			(start 5.05 -1.6)
			(end -0.05 -1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "9b6d0f7a-7a25-46b1-85f0-0104e8c4bcda")
		)
		(fp_text user "${REFERENCE}"
			(at 2.5 0 270)
			(layer "F.Fab")
			(uuid "ec393c42-2de1-4641-b53a-02545abc1806")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 21 "Net-(U1C-O)")
			(pintype "passive")
			(uuid "c90a2607-754a-4f50-a72c-157232733302")
		)
		(pad "2" thru_hole circle
			(at 5 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pintype "passive")
			(uuid "722c6177-a4b9-4ecc-961a-b71e10bad330")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Capacitor_THT.3dshapes/C_Disc_D5.1mm_W3.2mm_P5.00mm.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:C_Disc_D5.1mm_W3.2mm_P5.00mm"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a59b98b")
		(at 149 102.87 -90)
		(descr "C, Disc series, Radial, pin pitch=5.00mm, , diameter*width=5.1*3.2mm^2, Capacitor, http://www.vishay.com/docs/45233/krseries.pdf")
		(tags "C Disc series Radial pin pitch 5.00mm  diameter 5.1mm width 3.2mm Capacitor")
		(property "Reference" "C4"
			(at 5.43 -2.7 270)
			(layer "F.SilkS")
			(uuid "d539385a-b1cc-45b3-b4db-00d4cd6246ee")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100pF"
			(at 2.5 2.91 270)
			(layer "F.Fab")
			(uuid "c9f82409-6301-4ef7-8052-bc0de8258e1e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "ee82d73e-0978-4336-9859-387dd17c6583")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "fc340c19-56f3-458c-9c05-f691ff05ec30")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "SM* C? C1-1")
		(path "/00000000-0000-0000-0000-00003ebf81a7")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start -0.17 1.721)
			(end 5.17 1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "dd366a55-7163-45f2-95af-49f6f3763eab")
		)
		(fp_line
			(start -0.17 1.055)
			(end -0.17 1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ac37c304-ae9d-4fe3-9a2c-52efe9e5fcdf")
		)
		(fp_line
			(start 5.17 1.055)
			(end 5.17 1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "db30aea9-b479-4f92-919b-6669dd3b786a")
		)
		(fp_line
			(start -0.17 -1.721)
			(end -0.17 -1.055)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cc3f7e7c-ad22-4571-8c4f-4ddcac0c96ce")
		)
		(fp_line
			(start -0.17 -1.721)
			(end 5.17 -1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c42d3b95-3acd-4582-8ec0-aed0b64e5e0d")
		)
		(fp_line
			(start 5.17 -1.721)
			(end 5.17 -1.055)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b1dfe691-02e3-45b7-8035-9a3df36d53b9")
		)
		(fp_line
			(start -1.05 1.85)
			(end 6.05 1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b181caf7-8370-4c37-8398-65628f877c65")
		)
		(fp_line
			(start 6.05 1.85)
			(end 6.05 -1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "1cd3123c-28cf-4f35-9e10-59130f5b7680")
		)
		(fp_line
			(start -1.05 -1.85)
			(end -1.05 1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "53c9dbfb-9439-4d80-84dd-cf31bad81e81")
		)
		(fp_line
			(start 6.05 -1.85)
			(end -1.05 -1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "656fe619-00c7-4c36-acc2-0124b813b785")
		)
		(fp_line
			(start -0.05 1.6)
			(end 5.05 1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "4b7b32ee-c75d-448c-918c-d513bce26b23")
		)
		(fp_line
			(start 5.05 1.6)
			(end 5.05 -1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5f86d694-1ac5-4fdf-ac37-1da31d3f2183")
		)
		(fp_line
			(start -0.05 -1.6)
			(end -0.05 1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "e4364e78-e651-404c-af61-01a6c2286e0e")
		)
		(fp_line
			(start 5.05 -1.6)
			(end -0.05 -1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "df9b9aed-783e-4cf4-8754-ef5dc1092c3d")
		)
		(fp_text user "${REFERENCE}"
			(at 2.5 0 270)
			(layer "F.Fab")
			(uuid "17926472-c07a-4888-91e9-c99e5f0d204a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 20 "Net-(U1D-O)")
			(pintype "passive")
			(uuid "d73fd411-04f7-4175-89dd-08b8a688920a")
		)
		(pad "2" thru_hole circle
			(at 5 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pintype "passive")
			(uuid "b3d13469-9f6a-4969-ab8d-d138ba4fb539")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Capacitor_THT.3dshapes/C_Disc_D5.1mm_W3.2mm_P5.00mm.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:C_Disc_D5.1mm_W3.2mm_P5.00mm"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a59b99f")
		(at 151.13 69.85)
		(descr "C, Disc series, Radial, pin pitch=5.00mm, , diameter*width=5.1*3.2mm^2, Capacitor, http://www.vishay.com/docs/45233/krseries.pdf")
		(tags "C Disc series Radial pin pitch 5.00mm  diameter 5.1mm width 3.2mm Capacitor")
		(property "Reference" "C3"
			(at 9.17 -0.45 0)
			(layer "F.SilkS")
			(uuid "fd5c650a-adf8-4dff-858c-e3113865ef93")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100pF"
			(at 8.87 1.05 0)
			(layer "F.Fab")
			(uuid "004775a0-590e-45ca-8f54-ca09dbb97e4e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "7adda4ec-ee44-4753-952e-c7353f1c9b15")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "0159dcd8-3523-4227-aa19-dc7a88856076")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "SM* C? C1-1")
		(path "/00000000-0000-0000-0000-00004d528084")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start -0.17 -1.721)
			(end -0.17 -1.055)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a9d0c3b4-bc42-4941-97aa-01bf6d4235dd")
		)
		(fp_line
			(start -0.17 -1.721)
			(end 5.17 -1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7dc01f12-3ac2-4c99-bff7-26595599fc07")
		)
		(fp_line
			(start -0.17 1.055)
			(end -0.17 1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ea058180-f632-4524-b072-a2ad99fced99")
		)
		(fp_line
			(start -0.17 1.721)
			(end 5.17 1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "40047a88-fd07-4af4-9f90-5945bae70017")
		)
		(fp_line
			(start 5.17 -1.721)
			(end 5.17 -1.055)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "264f4e94-6224-4913-873f-d5c93769c188")
		)
		(fp_line
			(start 5.17 1.055)
			(end 5.17 1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3e8e6ceb-44c6-44a5-8403-6dc159d29b9a")
		)
		(fp_line
			(start -1.05 -1.85)
			(end -1.05 1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "31f68ea8-c30c-456f-92c8-7d5d47ba7cb0")
		)
		(fp_line
			(start -1.05 1.85)
			(end 6.05 1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "767822ea-3349-4347-a6b7-85a07c9cc2dc")
		)
		(fp_line
			(start 6.05 -1.85)
			(end -1.05 -1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "56f59dab-d13f-4738-992b-ea3974ccf8b9")
		)
		(fp_line
			(start 6.05 1.85)
			(end 6.05 -1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "6694e1e4-37d3-4e82-a117-58d38e8c14a7")
		)
		(fp_line
			(start -0.05 -1.6)
			(end -0.05 1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "22f568d1-75e4-4d72-95f9-2d4c19c8b09b")
		)
		(fp_line
			(start -0.05 1.6)
			(end 5.05 1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "83405664-08f6-4c42-8924-ef1aa98c3220")
		)
		(fp_line
			(start 5.05 -1.6)
			(end -0.05 -1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "704bc1d7-b315-46c0-89fb-5513fa3c64e7")
		)
		(fp_line
			(start 5.05 1.6)
			(end 5.05 -1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6a113203-293c-42c2-a483-279440e30868")
		)
		(fp_text user "${REFERENCE}"
			(at 2.5 0 0)
			(layer "F.Fab")
			(uuid "50bd58fc-b0d1-4000-b0b6-65331761f991")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 16 "Net-(U1A-O)")
			(pintype "passive")
			(uuid "4134ac71-4649-4315-a470-7aab44164468")
		)
		(pad "2" thru_hole circle
			(at 5 0)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pintype "passive")
			(uuid "7e5a9ee8-c95c-4dab-9d74-bdcf25215214")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Capacitor_THT.3dshapes/C_Disc_D5.1mm_W3.2mm_P5.00mm.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:C_Disc_D5.1mm_W3.2mm_P5.00mm"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a59b9b3")
		(at 140.97 102.87 -90)
		(descr "C, Disc series, Radial, pin pitch=5.00mm, , diameter*width=5.1*3.2mm^2, Capacitor, http://www.vishay.com/docs/45233/krseries.pdf")
		(tags "C Disc series Radial pin pitch 5.00mm  diameter 5.1mm width 3.2mm Capacitor")
		(property "Reference" "C2"
			(at 2.83 -3.23 270)
			(layer "F.SilkS")
			(uuid "1ef27b9f-86a6-4be9-afec-483b1d505d7e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100pF"
			(at 2.5 2.91 270)
			(layer "F.Fab")
			(uuid "ec64310d-5c2d-443c-a56a-08131828c27a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "536281fe-d31b-4e12-931c-895ee4fc21d4")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "044b11e8-8ee3-4e9d-8c15-811dea0acd06")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "SM* C? C1-1")
		(path "/00000000-0000-0000-0000-00004d528085")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr through_hole)
		(fp_line
			(start -0.17 1.721)
			(end 5.17 1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "241b658a-62de-441d-8d63-bab161116210")
		)
		(fp_line
			(start -0.17 1.055)
			(end -0.17 1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c08cd226-dafe-4b9c-bb6e-e44324cc8ad8")
		)
		(fp_line
			(start 5.17 1.055)
			(end 5.17 1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "348d006f-f340-4755-9ae0-e0a5342f6fdd")
		)
		(fp_line
			(start -0.17 -1.721)
			(end -0.17 -1.055)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ab1253c1-43fc-4424-9a0c-b02067e4da9a")
		)
		(fp_line
			(start -0.17 -1.721)
			(end 5.17 -1.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "525cd809-ceba-448c-b082-b86e6f7da5a0")
		)
		(fp_line
			(start 5.17 -1.721)
			(end 5.17 -1.055)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1693af72-0a7c-4feb-baaa-60fc145696cb")
		)
		(fp_line
			(start -1.05 1.85)
			(end 6.05 1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "69f19a06-4b46-4aea-b4ae-4544ab2e3fad")
		)
		(fp_line
			(start 6.05 1.85)
			(end 6.05 -1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "62babf75-3f6e-4b5b-8228-29728fe93912")
		)
		(fp_line
			(start -1.05 -1.85)
			(end -1.05 1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "ca902d7f-72d4-4f1f-8023-e514bcd245fd")
		)
		(fp_line
			(start 6.05 -1.85)
			(end -1.05 -1.85)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "bd5f5775-1d70-4cc2-8448-adb4d04e3b33")
		)
		(fp_line
			(start -0.05 1.6)
			(end 5.05 1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "50e69622-f3f5-4825-a6f0-d1d968e4dfe7")
		)
		(fp_line
			(start 5.05 1.6)
			(end 5.05 -1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "aa812754-a93e-4c61-99a4-654c3e21144b")
		)
		(fp_line
			(start -0.05 -1.6)
			(end -0.05 1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b8d99ca7-45e5-4f5e-a4e4-f6128720928d")
		)
		(fp_line
			(start 5.05 -1.6)
			(end -0.05 -1.6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "7a2424df-731e-427f-84ff-7c2a54c59764")
		)
		(fp_text user "${REFERENCE}"
			(at 2.5 0 270)
			(layer "F.Fab")
			(uuid "07b8b30b-94ac-47e8-acb4-da65e6a353a5")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 15 "Net-(U1B-O)")
			(pintype "passive")
			(uuid "0f98b4f6-020f-498c-a127-d97f65fa26d1")
		)
		(pad "2" thru_hole circle
			(at 5 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "GND")
			(pintype "passive")
			(uuid "d67f7df7-99b8-46cc-8faa-462b57ee0477")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Capacitor_THT.3dshapes/C_Disc_D5.1mm_W3.2mm_P5.00mm.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:DSUB-25_Male_EdgeMount_P2.77mm"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a73ac29")
		(at 105.5116 89.1032 -90)
		(descr "25-pin D-Sub connector, solder-cups edge-mounted, male, x-pin-pitch 2.77mm, distance of mounting holes 47.1mm, see https://disti-assets.s3.amazonaws.com/tonar/files/datasheets/16730.pdf")
		(tags "25-pin D-Sub connector edge mount solder cup male x-pin-pitch 2.77mm mounting holes distance 47.1mm")
		(property "Reference" "J1"
			(at -19.6032 -0.6884 90)
			(layer "F.SilkS")
			(uuid "fa805b2c-e169-408b-beff-e53c6912fc9c")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "DB25MALE"
			(at 0 16.69 90)
			(layer "F.Fab")
			(uuid "7e4f3873-601a-4547-82b1-1837f97016ae")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "59c0162a-7850-4367-bc45-b992a17f7bcb")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "2c360eeb-8969-4837-8ba2-7c75098320e8")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "DB25*")
		(path "/00000000-0000-0000-0000-00003ebf7d04")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr smd)
		(fp_line
			(start 17.803333 1.74)
			(end 17.803333 -2)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "87a6b6e4-6ae9-41c4-9fa5-44232b733550")
		)
		(fp_line
			(start -18.043333 0)
			(end -18.043333 -2.24)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d21330c3-d479-4cd8-954b-413cde8b6191")
		)
		(fp_line
			(start -17.803333 -2)
			(end -17.803333 1.74)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b62d8ce6-5490-4170-85e3-67cbdc8f4fe1")
		)
		(fp_line
			(start 17.803333 -2)
			(end -17.803333 -2)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "95f0d801-b076-4069-9e62-4f3da1d87575")
		)
		(fp_line
			(start -18.043333 -2.24)
			(end -13.85 -2.24)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5bdf9735-af86-4d23-81f9-7dbcf282ce8a")
		)
		(fp_line
			(start -26.55 1.99)
			(end 26.55 1.99)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "Dwgs.User")
			(uuid "933e467a-a61e-4517-89af-046e085dc266")
		)
		(fp_line
			(start -19.65 16.19)
			(end -19.65 10.19)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "0f39e4ff-955e-46d2-ae9e-02cf0a7b1678")
		)
		(fp_line
			(start 19.65 16.19)
			(end -19.65 16.19)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "32d1ab3f-e2e6-4154-bd06-ef57a32cf5c6")
		)
		(fp_line
			(start -27.05 10.19)
			(end -27.05 8.79)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "e89c38b8-c683-49aa-8831-2bd068245ecb")
		)
		(fp_line
			(start -19.65 10.19)
			(end -27.05 10.19)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "ce1d76d7-36f6-48ff-8c13-9aa2d46d1ece")
		)
		(fp_line
			(start 19.65 10.19)
			(end 19.65 16.19)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "9ef04425-37d6-4a41-91b9-8aca56cc984e")
		)
		(fp_line
			(start 27.05 10.19)
			(end 19.65 10.19)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "7b67622c-5cbf-4e58-8d9b-7a83d0227c42")
		)
		(fp_line
			(start -27.05 8.79)
			(end -20.05 8.79)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3ef176d5-36c0-4665-ad5f-196f17430975")
		)
		(fp_line
			(start -20.05 8.79)
			(end -20.05 4.29)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "35505d43-041a-42b4-b6fd-4af79ab6942d")
		)
		(fp_line
			(start 20.05 8.79)
			(end 27.05 8.79)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "55ea9798-85b0-4646-bdb4-43ae5ccf6b89")
		)
		(fp_line
			(start 27.05 8.79)
			(end 27.05 10.19)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "bfb9fe6a-4c0c-4cb9-bb01-4645cd0f07a4")
		)
		(fp_line
			(start -20.05 4.29)
			(end -19.05 4.29)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3d5e2828-9ec6-4c27-a90c-2ba8e5020c4f")
		)
		(fp_line
			(start -19.05 4.29)
			(end -19.05 1.49)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "0a28b4a0-6938-44a4-bc8a-83f38dbf0994")
		)
		(fp_line
			(start 19.05 4.29)
			(end 20.05 4.29)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "a9c25bc8-77e5-44f0-82f0-0ff8c094d562")
		)
		(fp_line
			(start 20.05 4.29)
			(end 20.05 8.79)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "2c487b42-485a-4a2f-9fa6-a85f0b8fe745")
		)
		(fp_line
			(start -19.05 1.49)
			(end -18.05 1.49)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "592aefaa-61e0-459a-aca3-60b3ed65937d")
		)
		(fp_line
			(start -18.05 1.49)
			(end -18.05 -2.25)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "bbd71061-b7f0-4e39-b94c-64617c7f6713")
		)
		(fp_line
			(start 18.05 1.49)
			(end 19.05 1.49)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "e938768e-463e-4b5f-b8b9-a0ead614c9da")
		)
		(fp_line
			(start 19.05 1.49)
			(end 19.05 4.29)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "ea447168-6941-436d-a414-1bee8fc125b1")
		)
		(fp_line
			(start -18.05 -2.25)
			(end 18.05 -2.25)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "221e928c-16f9-47b1-84fb-916fe3dd0220")
		)
		(fp_line
			(start 18.05 -2.25)
			(end 18.05 1.49)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "22befb9d-afcc-452e-ba59-fdebabd8322d")
		)
		(fp_line
			(start -15.835 1.99)
			(end -14.635 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "a30198ad-1f58-427e-b268-a56b3841abe7")
		)
		(fp_line
			(start -14.635 1.99)
			(end -14.635 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "50052e15-8df0-4cb1-bcfb-62d4338f96c2")
		)
		(fp_line
			(start -13.065 1.99)
			(end -11.865 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "eccb9f05-86a6-48f1-b4e9-be01eef8e560")
		)
		(fp_line
			(start -11.865 1.99)
			(end -11.865 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "d04e5ad6-6d91-4349-ae69-106e837dde38")
		)
		(fp_line
			(start -10.295 1.99)
			(end -9.095 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "13be1cd2-d8bb-47d9-a747-11a20aca08eb")
		)
		(fp_line
			(start -9.095 1.99)
			(end -9.095 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "034b2e84-ad42-4daf-9895-11b7ab079cf4")
		)
		(fp_line
			(start -7.525 1.99)
			(end -6.325 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "eac07cfc-02af-48c2-907a-649dc3058d46")
		)
		(fp_line
			(start -6.325 1.99)
			(end -6.325 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "f59c6901-b456-431a-987e-ffc17a99173e")
		)
		(fp_line
			(start -4.755 1.99)
			(end -3.555 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "8d7d0cef-03f8-4e08-9869-b7a6e0bbb654")
		)
		(fp_line
			(start -3.555 1.99)
			(end -3.555 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "5e142ddd-bb59-4fb6-b885-bbc9c64978a6")
		)
		(fp_line
			(start -1.985 1.99)
			(end -0.785 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "bb892cb0-3fd4-4468-adfa-527391ec0316")
		)
		(fp_line
			(start -0.785 1.99)
			(end -0.785 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "218dc0a6-e48d-49a3-8cfc-0b3b7431e1c2")
		)
		(fp_line
			(start 0.785 1.99)
			(end 1.985 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "4f943d76-988f-4bc3-849b-0d2f99335ac3")
		)
		(fp_line
			(start 1.985 1.99)
			(end 1.985 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "6f384421-**************-bc66c4a86c21")
		)
		(fp_line
			(start 3.555 1.99)
			(end 4.755 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "6755ce18-d164-4090-9238-b25dc1c19d2d")
		)
		(fp_line
			(start 4.755 1.99)
			(end 4.755 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "4b6da93e-d3c0-4222-9aa0-742f48bea0c4")
		)
		(fp_line
			(start 6.325 1.99)
			(end 7.525 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "0cd4ff16-c61d-47be-8aa8-c9ffc71cd3bd")
		)
		(fp_line
			(start 7.525 1.99)
			(end 7.525 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "7a6b9610-6606-42fe-9f97-cda91b6f641b")
		)
		(fp_line
			(start 9.095 1.99)
			(end 10.295 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "ea29a200-4efe-4fc6-b6de-cab90a22c2aa")
		)
		(fp_line
			(start 10.295 1.99)
			(end 10.295 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "d781fce8-6632-40b4-b866-1532b7f00f06")
		)
		(fp_line
			(start 11.865 1.99)
			(end 13.065 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "49407f8d-46b4-44f3-be72-66dcd66a2c0b")
		)
		(fp_line
			(start 13.065 1.99)
			(end 13.065 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "368b1071-e9a6-4d3a-b618-d6043667bb0b")
		)
		(fp_line
			(start 14.635 1.99)
			(end 15.835 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "81e9622d-a031-4dc0-8772-17f162385b78")
		)
		(fp_line
			(start 15.835 1.99)
			(end 15.835 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "ed384894-f3ea-4bca-9dfb-e8425bc6a7d9")
		)
		(fp_line
			(start -15.835 -0.91)
			(end -15.835 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "4863f1d4-64dd-4e23-ae5b-4e3ea5769f3b")
		)
		(fp_line
			(start -14.635 -0.91)
			(end -15.835 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "9cec3aa3-cbfd-476f-acef-25b8c3d12b22")
		)
		(fp_line
			(start -13.065 -0.91)
			(end -13.065 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "744eddc3-d2a4-485b-b012-fc0c65a113ff")
		)
		(fp_line
			(start -11.865 -0.91)
			(end -13.065 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "f61d930c-7f7b-4577-bca3-1b0b5b12b434")
		)
		(fp_line
			(start -10.295 -0.91)
			(end -10.295 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "3897648f-3d07-42f7-96f6-e7b23fe5b4f1")
		)
		(fp_line
			(start -9.095 -0.91)
			(end -10.295 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "903b3f33-fbd0-4659-9495-ea8c2fc1b1af")
		)
		(fp_line
			(start -7.525 -0.91)
			(end -7.525 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "ac448436-7acf-4b47-b9c3-e6e0fbe35b14")
		)
		(fp_line
			(start -6.325 -0.91)
			(end -7.525 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "ed5a9c1d-2670-4689-9c59-13d9d213573b")
		)
		(fp_line
			(start -4.755 -0.91)
			(end -4.755 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "8de7e1a8-cb93-4ad7-a9f3-be54b13127e2")
		)
		(fp_line
			(start -3.555 -0.91)
			(end -4.755 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "2d10bf61-7873-45ef-b7c2-5ec2638088f5")
		)
		(fp_line
			(start -1.985 -0.91)
			(end -1.985 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "2bb1e46f-c64a-452b-b5bd-bd0348968193")
		)
		(fp_line
			(start -0.785 -0.91)
			(end -1.985 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "4eb14f69-f05a-42cd-a45a-5aada0a6fc15")
		)
		(fp_line
			(start 0.785 -0.91)
			(end 0.785 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "186ea8aa-4bdd-4afb-93e9-a4e4bc6f9c65")
		)
		(fp_line
			(start 1.985 -0.91)
			(end 0.785 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "043390a7-b470-4844-8f6b-ddc9db7fefb5")
		)
		(fp_line
			(start 3.555 -0.91)
			(end 3.555 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "213c017c-**************-316d43bf043b")
		)
		(fp_line
			(start 4.755 -0.91)
			(end 3.555 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "98844a6c-3bd6-42a2-adc6-cc747c2f6706")
		)
		(fp_line
			(start 6.325 -0.91)
			(end 6.325 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "78922767-0f30-4d6b-a844-0d1dfda58317")
		)
		(fp_line
			(start 7.525 -0.91)
			(end 6.325 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "b8071aa4-501a-434e-a339-be3d0f3689ee")
		)
		(fp_line
			(start 9.095 -0.91)
			(end 9.095 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "9122da46-7892-4b07-b5b6-c0f05f1ee105")
		)
		(fp_line
			(start 10.295 -0.91)
			(end 9.095 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "def66b51-fd47-4414-acaf-f86356226a90")
		)
		(fp_line
			(start 11.865 -0.91)
			(end 11.865 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "b4d15944-9497-48dc-ba37-64d601033611")
		)
		(fp_line
			(start 13.065 -0.91)
			(end 11.865 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "12691d2a-8c54-43c5-b1f7-352ca6c6527d")
		)
		(fp_line
			(start 14.635 -0.91)
			(end 14.635 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "cedd8eae-b1dc-4443-8b5a-0901fb588242")
		)
		(fp_line
			(start 15.835 -0.91)
			(end 14.635 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "4c3dd9ee-62c9-4ce0-8040-7535ec73cde4")
		)
		(fp_line
			(start -19.15 15.69)
			(end 19.15 15.69)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5711fe39-e508-4daf-8948-33c7735d23ac")
		)
		(fp_line
			(start 19.15 15.69)
			(end 19.15 9.69)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f1bc2f92-241b-43d0-b38e-15f22be0b171")
		)
		(fp_line
			(start -26.55 9.69)
			(end 26.55 9.69)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "923fe629-79ad-4dfa-86ff-a495261913ea")
		)
		(fp_line
			(start -19.15 9.69)
			(end -19.15 15.69)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6a016669-5975-4221-b09d-79142e2eaff7")
		)
		(fp_line
			(start 19.15 9.69)
			(end -19.15 9.69)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "34f5ec25-9e58-418f-91ce-5c8ec3ae4dea")
		)
		(fp_line
			(start 26.55 9.69)
			(end 26.55 9.29)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "35c7469d-9db4-4625-9e41-600c400cca59")
		)
		(fp_line
			(start -26.55 9.29)
			(end -26.55 9.69)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a6be3645-886d-48fc-aa62-f64e82cef186")
		)
		(fp_line
			(start -19.55 9.29)
			(end 19.55 9.29)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c4e559a0-09e8-4431-bb6f-d26ecfa83703")
		)
		(fp_line
			(start 19.55 9.29)
			(end 19.55 4.79)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6bd1332a-0665-440e-9a3a-e140bc1a05dd")
		)
		(fp_line
			(start 26.55 9.29)
			(end -26.55 9.29)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "7e58a0ea-0fdf-4cdd-97d8-2b0e4d6c3f8d")
		)
		(fp_line
			(start -19.55 4.79)
			(end -19.55 9.29)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "7d17bc6c-f475-4e25-8110-056bfdfee29b")
		)
		(fp_line
			(start -18.55 4.79)
			(end 18.55 4.79)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0c6d6a59-dde4-44b8-ae14-05948e1b1ba2")
		)
		(fp_line
			(start 18.55 4.79)
			(end 18.55 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "1e012979-1189-4a97-9c5d-2b4a9d8cf350")
		)
		(fp_line
			(start 19.55 4.79)
			(end -19.55 4.79)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "7a8902a7-a876-40d1-9266-93429269dbd3")
		)
		(fp_line
			(start -18.55 1.99)
			(end -18.55 4.79)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "*************-4d1a-99f5-1edc32a3517b")
		)
		(fp_line
			(start -17.22 1.99)
			(end -16.02 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d1ec08b3-9c32-408c-8a23-fcb7efc470fc")
		)
		(fp_line
			(start -16.02 1.99)
			(end -16.02 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "939423dd-e36d-474e-b79d-da88dcff46b7")
		)
		(fp_line
			(start -14.45 1.99)
			(end -13.25 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "3b5e5286-ab3f-449d-970b-a447385e20dc")
		)
		(fp_line
			(start -13.25 1.99)
			(end -13.25 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "51633c90-3db3-485d-ba6e-6dc8f6fbcda5")
		)
		(fp_line
			(start -11.68 1.99)
			(end -10.48 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f1df01e3-ef12-40d5-a750-d2e3a563e50d")
		)
		(fp_line
			(start -10.48 1.99)
			(end -10.48 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "53c824a3-d187-4465-8693-513f1311e1b1")
		)
		(fp_line
			(start -8.91 1.99)
			(end -7.71 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "43560a85-1d75-4a7b-a973-01c402627178")
		)
		(fp_line
			(start -7.71 1.99)
			(end -7.71 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6dc47fed-0990-4667-84b9-08c554e8c77d")
		)
		(fp_line
			(start -6.14 1.99)
			(end -4.94 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "93e1be83-c211-4263-acf1-e9df583b8a06")
		)
		(fp_line
			(start -4.94 1.99)
			(end -4.94 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2eab0654-3bd0-44b8-8f68-79652924681a")
		)
		(fp_line
			(start -3.37 1.99)
			(end -2.17 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "60abe8f4-f1f0-4032-8d6d-eef7564dc0ee")
		)
		(fp_line
			(start -2.17 1.99)
			(end -2.17 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6aefdca8-2d67-4aa9-991e-4f431e9aff02")
		)
		(fp_line
			(start -0.6 1.99)
			(end 0.6 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "929cb378-ed63-4338-96e0-00000c653efd")
		)
		(fp_line
			(start 0.6 1.99)
			(end 0.6 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "cf4812dc-**************-afb752adeda4")
		)
		(fp_line
			(start 2.17 1.99)
			(end 3.37 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b9f31d90-c201-4b15-940d-f1b346566c77")
		)
		(fp_line
			(start 3.37 1.99)
			(end 3.37 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "cb259e15-4e43-47e7-8cd8-0b688d422a6b")
		)
		(fp_line
			(start 4.94 1.99)
			(end 6.14 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c206b3e8-681a-4c28-a31d-ea2baef5ed16")
		)
		(fp_line
			(start 6.14 1.99)
			(end 6.14 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "abeafd24-d45d-407b-80a2-0b118dfddcfa")
		)
		(fp_line
			(start 7.71 1.99)
			(end 8.91 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "56c395f0-be9e-4f13-a9c9-9c5fa327c2c6")
		)
		(fp_line
			(start 8.91 1.99)
			(end 8.91 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a44ba74d-9f59-449b-a2d2-38d617a890d3")
		)
		(fp_line
			(start 10.48 1.99)
			(end 11.68 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "65f83baf-c153-4c20-868a-61fd430a4957")
		)
		(fp_line
			(start 11.68 1.99)
			(end 11.68 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "42b67046-cf4a-42e8-a34b-ce8809502389")
		)
		(fp_line
			(start 13.25 1.99)
			(end 14.45 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f4cc81f4-ea2a-4308-a7f8-f8bc043dd9bd")
		)
		(fp_line
			(start 14.45 1.99)
			(end 14.45 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "674559d9-31f4-4880-b5dc-21606558ad97")
		)
		(fp_line
			(start 16.02 1.99)
			(end 17.22 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "dab4ea33-aa7e-4e71-8d2a-c35c28f6cdbe")
		)
		(fp_line
			(start 17.22 1.99)
			(end 17.22 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d7deee75-b585-4d86-8fb3-8a07b5234fa6")
		)
		(fp_line
			(start 18.55 1.99)
			(end -18.55 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "e4e457a3-f725-4326-8a84-b95dae430a14")
		)
		(fp_line
			(start -17.22 -0.91)
			(end -17.22 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "79db4c0d-9df4-4eb3-9598-bd5cc37f6340")
		)
		(fp_line
			(start -16.02 -0.91)
			(end -17.22 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "cfa8421d-efbf-406f-899b-973b694e3aba")
		)
		(fp_line
			(start -14.45 -0.91)
			(end -14.45 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5f2a6608-cbaf-4025-afef-d852a44207d2")
		)
		(fp_line
			(start -13.25 -0.91)
			(end -14.45 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "35cf873e-170c-46d9-9617-7561ad462e04")
		)
		(fp_line
			(start -11.68 -0.91)
			(end -11.68 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "db3954e2-515d-4ff8-8ce2-8c73e75d2e34")
		)
		(fp_line
			(start -10.48 -0.91)
			(end -11.68 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "630be793-66b5-4912-9d2f-48db59531c3f")
		)
		(fp_line
			(start -8.91 -0.91)
			(end -8.91 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d843a845-17fd-4a24-aec7-ec6db7740532")
		)
		(fp_line
			(start -7.71 -0.91)
			(end -8.91 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "521bcf90-1630-4fcb-ad64-abc0e84fd735")
		)
		(fp_line
			(start -6.14 -0.91)
			(end -6.14 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "512c1750-b3a8-4b43-bdd4-1b81d608add2")
		)
		(fp_line
			(start -4.94 -0.91)
			(end -6.14 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "32aded01-27a0-4b7f-ab0c-0a19a62ab9ee")
		)
		(fp_line
			(start -3.37 -0.91)
			(end -3.37 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0836a8cc-9c30-4bb4-bad6-60d02c69a208")
		)
		(fp_line
			(start -2.17 -0.91)
			(end -3.37 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c367f952-b62c-4513-b5e2-fc009dc509f9")
		)
		(fp_line
			(start -0.6 -0.91)
			(end -0.6 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "802b41fd-89d8-458e-a7e9-78d33e5ba7ff")
		)
		(fp_line
			(start 0.6 -0.91)
			(end -0.6 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "23ada3f0-a884-4768-91cf-c37f52e1ba92")
		)
		(fp_line
			(start 2.17 -0.91)
			(end 2.17 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ac9611dc-0517-4a5e-a0a9-6217a532fbf5")
		)
		(fp_line
			(start 3.37 -0.91)
			(end 2.17 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "82a4661e-77b8-49a4-bea8-43d952b4868f")
		)
		(fp_line
			(start 4.94 -0.91)
			(end 4.94 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d6fcd72d-8784-4e11-b7bd-b71f81fb125a")
		)
		(fp_line
			(start 6.14 -0.91)
			(end 4.94 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "e3a49394-89b8-410d-a339-77ccfd9b8d34")
		)
		(fp_line
			(start 7.71 -0.91)
			(end 7.71 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "09ac99fd-d134-439b-a352-065113b3542b")
		)
		(fp_line
			(start 8.91 -0.91)
			(end 7.71 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "839169e2-e3cd-4d40-aead-e3175142e0c1")
		)
		(fp_line
			(start 10.48 -0.91)
			(end 10.48 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5169acbf-a923-4db6-8f99-1ac69d851746")
		)
		(fp_line
			(start 11.68 -0.91)
			(end 10.48 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "4d9355de-2d9b-4645-8faa-bf7fa10d4406")
		)
		(fp_line
			(start 13.25 -0.91)
			(end 13.25 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "abe3e9ff-a980-4314-8c72-efafab685a1a")
		)
		(fp_line
			(start 14.45 -0.91)
			(end 13.25 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "782919de-64c0-49ca-820e-5252f55c22e0")
		)
		(fp_line
			(start 16.02 -0.91)
			(end 16.02 1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6aa53b3c-dd38-4d72-98ec-06ecc14b1f3f")
		)
		(fp_line
			(start 17.22 -0.91)
			(end 16.02 -0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d4f923e1-69e8-4132-b3ac-30debb66ba1b")
		)
		(fp_text user "PCB edge"
			(at -21.55 1.323333 90)
			(layer "Dwgs.User")
			(uuid "06dffa85-6593-4247-b760-daf00488ca02")
			(effects
				(font
					(size 0.5 0.5)
					(thickness 0.075)
				)
			)
		)
		(fp_text user "${REFERENCE}"
			(at 0 3.39 90)
			(layer "F.Fab")
			(uuid "d933f077-8504-4afa-ac39-139f20300a38")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" smd rect
			(at -16.62 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 17 "unconnected-(J1-Pad1)")
			(pinfunction "1")
			(pintype "passive+no_connect")
			(uuid "a4dd22c1-947a-40ec-bfc9-ef5ddb344848")
		)
		(pad "2" smd rect
			(at -13.85 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 9 "/TDI-DIN-D0")
			(pinfunction "2")
			(pintype "passive")
			(uuid "b29319db-1b9b-4b6a-a4a6-108e3206839b")
		)
		(pad "3" smd rect
			(at -11.08 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 1 "/CLK-D1")
			(pinfunction "3")
			(pintype "passive")
			(uuid "1f175e1c-1773-4fbe-861f-248be328a2e5")
		)
		(pad "4" smd rect
			(at -8.31 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 11 "/TMS-PROG-D2")
			(pinfunction "4")
			(pintype "passive")
			(uuid "d85bd893-df4f-4810-8c87-827b3177da9d")
		)
		(pad "5" smd rect
			(at -5.54 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 2 "/CTRL-D3")
			(pinfunction "5")
			(pintype "passive")
			(uuid "dc921a8b-7d05-48bc-b1a5-3d6e8d368fb4")
		)
		(pad "6" smd rect
			(at -2.77 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 7 "/TD0-PROG-D4")
			(pinfunction "6")
			(pintype "passive")
			(uuid "ac6477f2-ce8d-4c64-a22d-48cf72e3229e")
		)
		(pad "7" smd rect
			(at 0 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 18 "unconnected-(J1-Pad7)")
			(pinfunction "7")
			(pintype "passive+no_connect")
			(uuid "5f54c4ea-6516-4e70-b4b4-b1f3dcc6ac35")
		)
		(pad "8" smd rect
			(at 2.77 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 29 "Net-(J1-Pad11)")
			(pinfunction "8")
			(pintype "passive")
			(uuid "e1b15f58-e809-4df9-94d9-ae3fdc35bf20")
		)
		(pad "9" smd rect
			(at 5.54 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 19 "unconnected-(J1-Pad9)")
			(pinfunction "9")
			(pintype "passive+no_connect")
			(uuid "7f6d6020-06d7-444b-b6ea-1acea8bfd886")
		)
		(pad "10" smd rect
			(at 8.31 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 30 "unconnected-(J1-Pad10)")
			(pinfunction "10")
			(pintype "passive+no_connect")
			(uuid "6f6e7534-3e8d-4013-8f29-bbc62e4742c5")
		)
		(pad "11" smd rect
			(at 11.08 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 29 "Net-(J1-Pad11)")
			(pinfunction "11")
			(pintype "passive")
			(uuid "6b74bbef-21ff-43b9-ac28-7ebbfd0683ed")
		)
		(pad "12" smd rect
			(at 13.85 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 29 "Net-(J1-Pad11)")
			(pinfunction "12")
			(pintype "passive")
			(uuid "d56a4cb9-4811-4641-949c-e6081680acca")
		)
		(pad "13" smd rect
			(at 16.62 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 3 "/DONE-SELECT*")
			(pinfunction "13")
			(pintype "passive")
			(uuid "64433fa7-8320-4ab2-ad0f-3ea6b4e2c1ce")
		)
		(pad "14" smd rect
			(at -15.235 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 33 "unconnected-(J1-P14-Pad14)")
			(pinfunction "P14")
			(pintype "passive+no_connect")
			(uuid "f878ea41-07e6-4d0d-9525-6463f07d2d41")
		)
		(pad "15" smd rect
			(at -12.465 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 12 "/VCC_SENSE-ERROR*")
			(pinfunction "P15")
			(pintype "passive")
			(uuid "6f117566-67af-4b13-9c24-27ba5c49f01b")
		)
		(pad "16" smd rect
			(at -9.695 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 26 "unconnected-(J1-P16-Pad16)")
			(pinfunction "P16")
			(pintype "passive+no_connect")
			(uuid "6dad85b5-21ee-4e55-af7e-7756fca4b60f")
		)
		(pad "17" smd rect
			(at -6.925 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 25 "unconnected-(J1-P17-Pad17)")
			(pinfunction "P17")
			(pintype "passive+no_connect")
			(uuid "5dd63cee-3178-42f2-b2f8-3a70a02de179")
		)
		(pad "18" smd rect
			(at -4.155 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 24 "unconnected-(J1-P18-Pad18)")
			(pinfunction "P18")
			(pintype "passive+no_connect")
			(uuid "4b7f6d83-e519-4264-aca0-085fb4501d53")
		)
		(pad "19" smd rect
			(at -1.385 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 28 "unconnected-(J1-P19-Pad19)")
			(pinfunction "P19")
			(pintype "passive+no_connect")
			(uuid "a946095f-8b0a-422b-bd5d-bd3df8693d2c")
		)
		(pad "20" smd rect
			(at 1.385 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 13 "GND")
			(pinfunction "P20")
			(pintype "passive")
			(uuid "82fc538f-a1f5-4f4d-a9ef-cc2b97f1252a")
		)
		(pad "21" smd rect
			(at 4.155 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 32 "unconnected-(J1-P21-Pad21)")
			(pinfunction "P21")
			(pintype "passive+no_connect")
			(uuid "d2078c3e-fe54-4fc3-8496-1aff53ed27ca")
		)
		(pad "22" smd rect
			(at 6.925 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 31 "unconnected-(J1-P22-Pad22)")
			(pinfunction "P22")
			(pintype "passive+no_connect")
			(uuid "c1d5a037-b751-42ec-9cd9-3d5e8ec733af")
		)
		(pad "23" smd rect
			(at 9.695 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 23 "unconnected-(J1-P23-Pad23)")
			(pinfunction "P23")
			(pintype "passive+no_connect")
			(uuid "0a6a21d1-170c-4828-8386-5d701aebc664")
		)
		(pad "24" smd rect
			(at 12.465 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 27 "unconnected-(J1-P24-Pad24)")
			(pinfunction "P24")
			(pintype "passive+no_connect")
			(uuid "a0c34409-7d84-4558-bf37-130937d84c97")
		)
		(pad "25" smd rect
			(at 15.235 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 13 "GND")
			(pinfunction "P25")
			(pintype "passive")
			(uuid "8f9d01df-9e28-4016-b3d6-582408c63e27")
		)
		(embedded_fonts no)
		(model "${KICAD8_3DMODEL_DIR}/Connector_Dsub.3dshapes/DSUB-25_Male_EdgeMount_P2.77mm.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "sonde-xilinx:DSUB-9_Male_EdgeMount_P2.77mm"
		(layer "B.Cu")
		(uuid "00000000-0000-0000-0000-00005a73b143")
		(at 181.61 90.1192 -90)
		(descr "9-pin D-Sub connector, solder-cups edge-mounted, male, x-pin-pitch 2.77mm, distance of mounting holes 25mm, see https://disti-assets.s3.amazonaws.com/tonar/files/datasheets/16730.pdf")
		(tags "9-pin D-Sub connector edge mount solder cup male x-pin-pitch 2.77mm mounting holes distance 25mm")
		(property "Reference" "J2"
			(at -9.463333 0 90)
			(layer "B.SilkS")
			(uuid "4dbb19cc-766d-4a5a-8ce8-704d942eb1c6")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
				(justify mirror)
			)
		)
		(property "Value" "DB9MALE"
			(at 0 -16.69 90)
			(layer "B.Fab")
			(uuid "c099ebae-af7a-447f-90cb-92c00ddbc593")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
				(justify mirror)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "933132de-475e-420f-b7f9-11f8969a6ac2")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "8126fd01-eb29-46c5-82e7-fa8ed509c723")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "DB9*")
		(path "/00000000-0000-0000-0000-00003ecde5c8")
		(sheetname "Racine")
		(sheetfile "sonde xilinx.kicad_sch")
		(attr smd)
		(fp_line
			(start -6.963333 2.24)
			(end -2.77 2.24)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "B.SilkS")
			(uuid "8c8dcd0b-5fe6-472b-af39-cf7cffec0328")
		)
		(fp_line
			(start -6.723333 2)
			(end -6.723333 -1.74)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "B.SilkS")
			(uuid "92abce3f-be96-4d74-9b91-e49a45685a10")
		)
		(fp_line
			(start 6.723333 2)
			(end -6.723333 2)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "B.SilkS")
			(uuid "a345d032-e682-4354-9225-b57ebe2ed0f2")
		)
		(fp_line
			(start -6.963333 0)
			(end -6.963333 2.24)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "B.SilkS")
			(uuid "6aa76824-92fc-4b26-b686-5d16324b9758")
		)
		(fp_line
			(start 6.723333 -1.74)
			(end 6.723333 2)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "B.SilkS")
			(uuid "cc83013b-619c-4602-a2f8-0059072fa70d")
		)
		(fp_line
			(start -15.425 -1.99)
			(end 15.425 -1.99)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "Dwgs.User")
			(uuid "007631ce-b3de-4f23-8ef5-1def5e6000bf")
		)
		(fp_line
			(start -6.97 2.25)
			(end 6.97 2.25)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "4cdcc85e-360d-4f83-ab03-cc20865ad3f3")
		)
		(fp_line
			(start 6.97 2.25)
			(end 6.97 -1.49)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "2c696a56-7edf-4758-a45b-f0c6ef7ee42d")
		)
		(fp_line
			(start -8.05 -1.49)
			(end -6.97 -1.49)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "1f0431c3-7ccf-4c5d-a969-344de82cfe6e")
		)
		(fp_line
			(start -6.97 -1.49)
			(end -6.97 2.25)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "d579c556-0156-4e0d-b884-f874a981edf0")
		)
		(fp_line
			(start 6.97 -1.49)
			(end 8.05 -1.49)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "de149d9e-9e26-402b-bda6-945b08913a25")
		)
		(fp_line
			(start 8.05 -1.49)
			(end 8.05 -4.29)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "5edd1805-9baf-4340-b190-634589d47cc0")
		)
		(fp_line
			(start -9.05 -4.29)
			(end -8.05 -4.29)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "32d82f2c-c2bc-47d5-9d37-f4aaa485eb16")
		)
		(fp_line
			(start -8.05 -4.29)
			(end -8.05 -1.49)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "da573183-865d-4225-9322-92009117e06b")
		)
		(fp_line
			(start 8.05 -4.29)
			(end 9.05 -4.29)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "0e9beeb0-577e-4fc2-ae24-5912779e445f")
		)
		(fp_line
			(start 9.05 -4.29)
			(end 9.05 -8.79)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "7c91e5fb-0fdd-4e73-b791-80b35f70f8c9")
		)
		(fp_line
			(start -15.93 -8.79)
			(end -9.05 -8.79)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "f3b4e69a-504e-462e-865e-c0f6a6e22474")
		)
		(fp_line
			(start -9.05 -8.79)
			(end -9.05 -4.29)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "36896af1-d438-4d77-879d-1f101d59b968")
		)
		(fp_line
			(start 9.05 -8.79)
			(end 15.93 -8.79)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "d5d71207-bffc-4f72-a962-593d8900e3be")
		)
		(fp_line
			(start 15.93 -8.79)
			(end 15.93 -10.19)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "6978655b-5721-4f96-81ce-2774de592fd4")
		)
		(fp_line
			(start -15.93 -10.19)
			(end -15.93 -8.79)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "a8b0ac46-a1b1-4ff6-85cb-3b568a56bc74")
		)
		(fp_line
			(start -8.65 -10.19)
			(end -15.93 -10.19)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "4d3bb2b4-bc9c-4b4e-941f-8e85d966c521")
		)
		(fp_line
			(start 8.65 -10.19)
			(end 8.65 -16.19)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "42bb6ef2-77d2-4aa3-a5c4-90558d5163d2")
		)
		(fp_line
			(start 15.93 -10.19)
			(end 8.65 -10.19)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "f15f470b-6cee-4f52-9d8c-4c16f257d7b6")
		)
		(fp_line
			(start -8.65 -16.19)
			(end -8.65 -10.19)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "20ad9122-05a8-43f9-89f8-5e6809f1fb11")
		)
		(fp_line
			(start 8.65 -16.19)
			(end -8.65 -16.19)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "19cdae25-a5c3-4c8c-a2a1-b5f51399f636")
		)
		(fp_line
			(start -6.14 0.91)
			(end -6.14 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "7262be20-8483-49c9-9dee-741bba7aee6e")
		)
		(fp_line
			(start -4.94 0.91)
			(end -6.14 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "a5c3735e-773f-41e9-a7b1-d82c9c26ef77")
		)
		(fp_line
			(start -3.37 0.91)
			(end -3.37 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "8b96a688-4fb9-42e0-bd7b-1476576819c9")
		)
		(fp_line
			(start -2.17 0.91)
			(end -3.37 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "77b6fdf1-e319-4421-abdb-1cf9eb562c88")
		)
		(fp_line
			(start -0.6 0.91)
			(end -0.6 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "25e59055-ca57-453d-add3-f54151e7412d")
		)
		(fp_line
			(start 0.6 0.91)
			(end -0.6 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "4b153709-89b3-402b-ae8a-68c619ea7a32")
		)
		(fp_line
			(start 2.17 0.91)
			(end 2.17 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "0db2d22d-fa72-4fd9-ad7b-0a7a8fae59d4")
		)
		(fp_line
			(start 3.37 0.91)
			(end 2.17 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "4efda11b-0fa8-449b-96be-3a4cb632a999")
		)
		(fp_line
			(start 4.94 0.91)
			(end 4.94 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "79d0e6f7-6718-4554-a1da-46b232950463")
		)
		(fp_line
			(start 6.14 0.91)
			(end 4.94 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "613febdd-fe4f-4864-a9c2-010d92a96bc5")
		)
		(fp_line
			(start -7.55 -1.99)
			(end -7.55 -4.79)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "f6469387-44ed-4bdf-bc55-507fbac6f8d8")
		)
		(fp_line
			(start -6.14 -1.99)
			(end -4.94 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "487db4ef-fc57-4189-af0a-1dcdcfc4d442")
		)
		(fp_line
			(start -4.94 -1.99)
			(end -4.94 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "188beaa4-62a4-4673-bf65-dbf094010183")
		)
		(fp_line
			(start -3.37 -1.99)
			(end -2.17 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "d4a97d7f-4a44-42da-a35a-6b905e963f50")
		)
		(fp_line
			(start -2.17 -1.99)
			(end -2.17 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "a8cc0a77-0e2e-4007-bc0a-54ebd9571edc")
		)
		(fp_line
			(start -0.6 -1.99)
			(end 0.6 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "3ca154a1-c798-4233-b620-3f98d24b9e9b")
		)
		(fp_line
			(start 0.6 -1.99)
			(end 0.6 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "05ab603b-29de-40ba-9810-efffed43bb94")
		)
		(fp_line
			(start 2.17 -1.99)
			(end 3.37 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "1de7dc49-e1ca-41a1-976d-c35fad80104f")
		)
		(fp_line
			(start 3.37 -1.99)
			(end 3.37 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "5f753d75-aac2-41e8-98ce-e33ad79a78e4")
		)
		(fp_line
			(start 4.94 -1.99)
			(end 6.14 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "e3001374-79c3-4348-9194-4692b417dfa1")
		)
		(fp_line
			(start 6.14 -1.99)
			(end 6.14 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "5604b3b8-0c99-41c0-8b50-241df0480b8f")
		)
		(fp_line
			(start 7.55 -1.99)
			(end -7.55 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "402a4ec0-ca16-4779-9192-5e32326a8b89")
		)
		(fp_line
			(start -8.55 -4.79)
			(end -8.55 -9.29)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "f84cba9b-5141-4531-90f7-355b1faa810f")
		)
		(fp_line
			(start -7.55 -4.79)
			(end 7.55 -4.79)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "95678d0e-d952-419f-993b-af2eeba00cb4")
		)
		(fp_line
			(start 7.55 -4.79)
			(end 7.55 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "edeeea8f-4213-46cf-8e0f-5c755087375c")
		)
		(fp_line
			(start 8.55 -4.79)
			(end -8.55 -4.79)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "926c457a-dbb2-4373-9af2-f5b163508f85")
		)
		(fp_line
			(start -15.425 -9.29)
			(end -15.425 -9.69)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "fc2ae2ab-7022-4d4a-b535-f33576de049f")
		)
		(fp_line
			(start -8.55 -9.29)
			(end 8.55 -9.29)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "835e5422-8609-46da-83ba-d273162097ff")
		)
		(fp_line
			(start 8.55 -9.29)
			(end 8.55 -4.79)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "ccda08ca-4ef3-4e0a-b95d-cb8a35ff37cc")
		)
		(fp_line
			(start 15.425 -9.29)
			(end -15.425 -9.29)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "cad7c153-96b4-42b3-94ab-81e9f826847c")
		)
		(fp_line
			(start -15.425 -9.69)
			(end 15.425 -9.69)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "205fd687-edb9-461d-8042-dd839bb62a6a")
		)
		(fp_line
			(start -8.15 -9.69)
			(end -8.15 -15.69)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "ac6ab5e4-5bdb-4659-b12d-fb27cfb74b7e")
		)
		(fp_line
			(start 8.15 -9.69)
			(end -8.15 -9.69)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "2e8c44d6-b9fc-493d-8a72-2cc05e9ea984")
		)
		(fp_line
			(start 15.425 -9.69)
			(end 15.425 -9.29)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "a3676693-0071-43e4-bd50-3b415893c22a")
		)
		(fp_line
			(start -8.15 -15.69)
			(end 8.15 -15.69)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "1b892b31-8920-4fcb-82e1-26fb5dd8bd7e")
		)
		(fp_line
			(start 8.15 -15.69)
			(end 8.15 -9.69)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "a8339586-851e-4ac0-8a50-2a7f267c1a14")
		)
		(fp_line
			(start -4.755 0.91)
			(end -4.755 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "1fb685be-41c6-4de8-baca-e504b94c3175")
		)
		(fp_line
			(start -3.555 0.91)
			(end -4.755 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "4a63044c-a9aa-4360-b911-f5f776ec7f1a")
		)
		(fp_line
			(start -1.985 0.91)
			(end -1.985 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "485e51c5-ad32-4d0c-a944-2bc6f046108f")
		)
		(fp_line
			(start -0.785 0.91)
			(end -1.985 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d849f207-ad61-4df4-a149-b4c1486994dd")
		)
		(fp_line
			(start 0.785 0.91)
			(end 0.785 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "bc74d99c-36bc-47f8-b3d9-2c744d0b47a1")
		)
		(fp_line
			(start 1.985 0.91)
			(end 0.785 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "1aaf25f7-2e6f-44b6-b359-a15dfccd8ad1")
		)
		(fp_line
			(start 3.555 0.91)
			(end 3.555 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "e4b73168-e54f-4cbf-a59d-f4f31d75a9c0")
		)
		(fp_line
			(start 4.755 0.91)
			(end 3.555 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "96c47886-9ba9-437f-a289-1742c165a62b")
		)
		(fp_line
			(start -4.755 -1.99)
			(end -3.555 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d68dfb5e-9c3e-42af-b4fc-330e99770df7")
		)
		(fp_line
			(start -3.555 -1.99)
			(end -3.555 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "86d4a845-6864-42d5-a6d5-e5b9782130ca")
		)
		(fp_line
			(start -1.985 -1.99)
			(end -0.785 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "61076a1e-e9ca-44a8-9250-46c2b9e1d01d")
		)
		(fp_line
			(start -0.785 -1.99)
			(end -0.785 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "7ff0165a-0d9b-427c-9b8a-d93a01265368")
		)
		(fp_line
			(start 0.785 -1.99)
			(end 1.985 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5665c9c5-e577-48ba-8763-232cea0a66fe")
		)
		(fp_line
			(start 1.985 -1.99)
			(end 1.985 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "84207cbc-e807-465f-9a70-866d31a1dee9")
		)
		(fp_line
			(start 3.555 -1.99)
			(end 4.755 -1.99)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5a92dc1f-b846-4511-8d02-d63745fad254")
		)
		(fp_line
			(start 4.755 -1.99)
			(end 4.755 0.91)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "82e80292-5337-46b1-b1dd-c286de69a753")
		)
		(fp_text user "PCB edge"
			(at -10.425 -1.323333 90)
			(layer "Dwgs.User")
			(uuid "7e6c8c4f-9a65-4b63-951c-873b5abee73c")
			(effects
				(font
					(size 0.5 0.5)
					(thickness 0.075)
				)
				(justify mirror)
			)
		)
		(fp_text user "${REFERENCE}"
			(at 0 -3.39 90)
			(layer "B.Fab")
			(uuid "90b06ba2-e852-43e7-aa60-1be230dd82cc")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
				(justify mirror)
			)
		)
		(pad "1" smd rect
			(at -5.54 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 4 "/PWR_3,3-5V")
			(pinfunction "1")
			(pintype "passive")
			(uuid "5a71f1b5-55fa-4504-91d2-8225955318aa")
		)
		(pad "2" smd rect
			(at -2.77 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 6 "/TD0-DONE")
			(pinfunction "2")
			(pintype "passive")
			(uuid "e470a78d-3c4f-4270-983b-5e321f13b801")
		)
		(pad "3" smd rect
			(at 0 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 8 "/TDI-DIN")
			(pinfunction "3")
			(pintype "passive")
			(uuid "86e91185-7b00-4329-9a90-dd9de5d2824a")
		)
		(pad "4" smd rect
			(at 2.77 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 5 "/TCK-CCLK")
			(pinfunction "4")
			(pintype "passive")
			(uuid "2d96c25a-5fd0-43eb-8ecb-e76d7473de19")
		)
		(pad "5" smd rect
			(at 5.54 0 270)
			(size 1.846667 3.48)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 10 "/TMS-PROG")
			(pinfunction "5")
			(pintype "passive")
			(uuid "daf68687-c87d-47bb-ab09-8478764358d4")
		)
		(pad "6" smd rect
			(at -4.155 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 13 "GND")
			(pinfunction "P6")
			(pintype "passive")
			(uuid "d6700b04-2a63-47e1-b09e-feb3edf7df67")
		)
		(pad "7" smd rect
			(at -1.385 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 13 "GND")
			(pinfunction "P7")
			(pintype "passive")
			(uuid "edbbf432-5304-453b-8560-b7f1d22f025d")
		)
		(pad "8" smd rect
			(at 1.385 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 13 "GND")
			(pinfunction "P8")
			(pintype "passive")
			(uuid "874d351c-1cca-41df-862f-99aa41303e1b")
		)
		(pad "9" smd rect
			(at 4.155 0 270)
			(size 1.846667 3.48)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 13 "GND")
			(pinfunction "P9")
			(pintype "passive")
			(uuid "00203f73-f051-4825-8700-366fa481308d")
		)
		(embedded_fonts no)
		(model "${KICAD8_3DMODEL_DIR}/Connector_Dsub.3dshapes/DSUB-9_Male_EdgeMount_P2.77mm.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(gr_line
		(start 103.3 110.49)
		(end 103.3 67.31)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "36cde3e2-d7fa-4360-b96f-19b3794f18da")
	)
	(gr_line
		(start 183.7 110.49)
		(end 103.3 110.49)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "65e72fa7-afb2-47d0-baee-1dfeff930100")
	)
	(gr_line
		(start 103.3 67.31)
		(end 183.7 67.31)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "98e005b0-3099-4cb6-a606-3772c06b5e36")
	)
	(gr_line
		(start 183.7 67.31)
		(end 183.7 110.49)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "ae5ca856-80ea-4818-b46b-23af5d11e5ea")
	)
	(gr_text "TMS-PROG"
		(at 158.115 102.235 0)
		(layer "F.Cu")
		(uuid "2a51a229-75db-4354-926d-ac6179482fc4")
		(effects
			(font
				(size 1.524 1.524)
				(thickness 0.381)
			)
		)
	)
	(gr_text "TDO-DONE"
		(at 158.115 86.36 0)
		(layer "F.Cu")
		(uuid "2be32b3c-9123-4383-9cf1-02617616cbbd")
		(effects
			(font
				(size 1.524 1.524)
				(thickness 0.381)
			)
		)
	)
	(gr_text "GND"
		(at 162.56 81.28 0)
		(layer "F.Cu")
		(uuid "717b9b54-8836-45e6-8f9b-3a4b8501ac9e")
		(effects
			(font
				(size 1.524 1.524)
				(thickness 0.381)
			)
		)
	)
	(gr_text "TCK-CCL"
		(at 158.115 96.52 0)
		(layer "F.Cu")
		(uuid "72a7430a-2e55-4e1e-8d44-b846dd022cfb")
		(effects
			(font
				(size 1.524 1.524)
				(thickness 0.381)
			)
		)
	)
	(gr_text "Component Side"
		(at 118.1 68.8 0)
		(layer "F.Cu")
		(uuid "c08d1940-d1ce-4853-9225-e876141cb34c")
		(effects
			(font
				(size 1.524 1.524)
				(thickness 0.3048)
			)
		)
	)
	(gr_text "VCC"
		(at 162.56 76.2 0)
		(layer "F.Cu")
		(uuid "c7030d22-8d6e-4c85-9f73-958187afe1ad")
		(effects
			(font
				(size 1.524 1.524)
				(thickness 0.381)
			)
		)
	)
	(gr_text "TDI-TIN"
		(at 158.115 91.44 0)
		(layer "F.Cu")
		(uuid "e1e2e17c-9c97-4111-bd02-e15c2c41e832")
		(effects
			(font
				(size 1.524 1.524)
				(thickness 0.381)
			)
		)
	)
	(gr_text "Copper layer"
		(at 119.38 68.58 0)
		(layer "B.Cu")
		(uuid "2949626e-a492-4c0f-b71e-12e7e31d79ab")
		(effects
			(font
				(size 1.524 1.524)
				(thickness 0.3048)
			)
			(justify mirror)
		)
	)
	(dimension
		(type aligned)
		(layer "Dwgs.User")
		(uuid "e1c1f73c-7bfa-4ec7-804f-a4a1824b8e2d")
		(pts
			(xy 183.7 110.49) (xy 103.3 110.5)
		)
		(height -5.08)
		(format
			(prefix "")
			(suffix "")
			(units 2)
			(units_format 1)
			(precision 4)
		)
		(style
			(thickness 0.3048)
			(arrow_length 1.27)
			(text_position_mode 0)
			(arrow_direction outward)
			(extension_height 0.58642)
			(extension_offset 0)
			(keep_text_aligned yes)
		)
		(gr_text "80.4000 mm"
			(at 143.500405 113.7462 0.007126340699)
			(layer "Dwgs.User")
			(uuid "e1c1f73c-7bfa-4ec7-804f-a4a1824b8e2d")
			(effects
				(font
					(size 1.524 1.524)
					(thickness 0.3048)
				)
			)
		)
	)
	(segment
		(start 109.855 78.105)
		(end 106.045 78.105)
		(width 0.635)
		(layer "F.Cu")
		(net 1)
		(uuid "18d99a3d-a929-42de-9c4e-312d08ad54f5")
	)
	(segment
		(start 111.76 77.216)
		(end 110.744 77.216)
		(width 0.635)
		(layer "F.Cu")
		(net 1)
		(uuid "5d6f6304-458c-4958-9eab-8d6fe46d003a")
	)
	(segment
		(start 106.045 78.105)
		(end 105.537 77.978)
		(width 0.635)
		(layer "F.Cu")
		(net 1)
		(uuid "9c2fa2fe-ae0e-4439-88b7-d50c1ee2f3a6")
	)
	(segment
		(start 110.744 77.216)
		(end 109.855 78.105)
		(width 0.635)
		(layer "F.Cu")
		(net 1)
		(uuid "9ecc6f37-0c3c-498b-acbd-************")
	)
	(segment
		(start 109.22 83.82)
		(end 105.41 83.82)
		(width 0.635)
		(layer "F.Cu")
		(net 2)
		(uuid "155e11ab-2f81-48c8-9f48-79da2198ca5a")
	)
	(segment
		(start 109.855 84.455)
		(end 109.22 83.82)
		(width 0.635)
		(layer "F.Cu")
		(net 2)
		(uuid "4577a76b-ce0c-4e9f-af39-317ce2347a7a")
	)
	(segment
		(start 111.76 87.63)
		(end 109.855 85.725)
		(width 0.635)
		(layer "F.Cu")
		(net 2)
		(uuid "577fc509-4b05-4a50-aec6-4a6be197cef0")
	)
	(segment
		(start 109.855 85.725)
		(end 109.855 84.455)
		(width 0.635)
		(layer "F.Cu")
		(net 2)
		(uuid "d598266c-0363-45fa-b291-8e5b561f09aa")
	)
	(segment
		(start 105.41 83.82)
		(end 105.537 83.439)
		(width 0.635)
		(layer "F.Cu")
		(net 2)
		(uuid "f57dcef3-efd8-417a-b37e-479cfeb37aca")
	)
	(segment
		(start 111.76 104.14)
		(end 111.76 104.775)
		(width 0.635)
		(layer "F.Cu")
		(net 3)
		(uuid "234ee843-e1b6-4384-97be-a6be805edb89")
	)
	(segment
		(start 111.76 104.775)
		(end 111.125 105.41)
		(width 0.635)
		(layer "F.Cu")
		(net 3)
		(uuid "bc41d2bb-d547-4d47-898e-2f2d25507d7d")
	)
	(segment
		(start 111.125 105.41)
		(end 105.537 105.41)
		(width 0.635)
		(layer "F.Cu")
		(net 3)
		(uuid "ce345b72-2fa9-450b-a14f-cf98de33d03e")
	)
	(segment
		(start 181.61 80.01)
		(end 180.213 78.613)
		(width 1.016)
		(layer "B.Cu")
		(net 4)
		(uuid "0c63b618-21ff-4992-9978-182ccabdf343")
	)
	(segment
		(start 160.02 78.74)
		(end 168.40201 78.74)
		(width 1.016)
		(layer "B.Cu")
		(net 4)
		(uuid "230087f7-5e97-4093-879e-24a8f252c558")
	)
	(segment
		(start 180.213 78.613)
		(end 171.050935 78.613)
		(width 1.016)
		(layer "B.Cu")
		(net 4)
		(uuid "266aa691-c806-4e73-8483-d21f1d2f7e0e")
	)
	(segment
		(start 181.61 80.01)
		(end 181.61 84.582)
		(width 1.016)
		(layer "B.Cu")
		(net 4)
		(uuid "3017f693-5448-41ec-9928-5ed315378f62")
	)
	(segment
		(start 171.050935 78.613)
		(end 169.599468 80.064467)
		(width 1.016)
		(layer "B.Cu")
		(net 4)
		(uuid "5bd61b5f-7107-401c-a2b6-1d1822840dc7")
	)
	(segment
		(start 168.40201 78.74)
		(end 169.672 80.00999)
		(width 1.016)
		(layer "B.Cu")
		(net 4)
		(uuid "ed82166c-28da-4adb-a5d9-54b75a692012")
	)
	(segment
		(start 169.037 90.297)
		(end 167.005 92.329)
		(width 0.635)
		(layer "B.Cu")
		(net 5)
		(uuid "081bf510-f556-4f12-9915-fa629e69c425")
	)
	(segment
		(start 181.61 92.71)
		(end 181.61 92.964)
		(width 0.635)
		(layer "B.Cu")
		(net 5)
		(uuid "1c68234d-5205-4306-ae3a-cc57ff45560b")
	)
	(segment
		(start 169.672 90.297)
		(end 169.037 90.297)
		(width 0.635)
		(layer "B.Cu")
		(net 5)
		(uuid "2b815ab6-fc59-4b5a-9bd0-1bddf225c362")
	)
	(segment
		(start 174.625 92.71)
		(end 181.61 92.71)
		(width 0.635)
		(layer "B.Cu")
		(net 5)
		(uuid "3847b88f-9ba6-4971-ab6c-2edd79524d2a")
	)
	(segment
		(start 169.672 90.297)
		(end 172.212 90.297)
		(width 0.635)
		(layer "B.Cu")
		(net 5)
		(uuid "5a83b046-72b1-41e0-aa56-03a9849dd88e")
	)
	(segment
		(start 167.005 92.329)
		(end 167.005 94.615)
		(width 0.635)
		(layer "B.Cu")
		(net 5)
		(uuid "874b0748-2e7c-490e-a352-09bda5155403")
	)
	(segment
		(start 172.212 90.297)
		(end 174.625 92.71)
		(width 0.635)
		(layer "B.Cu")
		(net 5)
		(uuid "92c8e643-b55e-47c6-8433-0b307f60655a")
	)
	(segment
		(start 167.005 94.615)
		(end 162.56 99.06)
		(width 0.635)
		(layer "B.Cu")
		(net 5)
		(uuid "d5ecf6b5-cfe9-426e-84b5-f5a14c2c4421")
	)
	(segment
		(start 178.435 86.995)
		(end 180.975 86.995)
		(width 0.635)
		(layer "B.Cu")
		(net 6)
		(uuid "0b7f8f31-8d25-4a9b-b6e0-f374cc99bd61")
	)
	(segment
		(start 180.975 86.995)
		(end 181.61 87.376)
		(width 0.635)
		(layer "B.Cu")
		(net 6)
		(uuid "301f61f1-1046-452c-9c80-56a24b4c936d")
	)
	(segment
		(start 169.672 85.217)
		(end 176.657 85.217)
		(width 0.635)
		(layer "B.Cu")
		(net 6)
		(uuid "3586b6b6-0809-444b-87a0-f98d016ab938")
	)
	(segment
		(start 162.56 88.138)
		(end 165.481 85.217)
		(width 0.635)
		(layer "B.Cu")
		(net 6)
		(uuid "3906da2b-ec39-4b95-a9db-8ebc3b69d8e2")
	)
	(segment
		(start 165.481 85.217)
		(end 169.672 85.217)
		(width 0.635)
		(layer "B.Cu")
		(net 6)
		(uuid "7f79d9ed-94a1-42c4-89ec-7e6700cb252f")
	)
	(segment
		(start 162.56 88.9)
		(end 162.56 88.138)
		(width 0.635)
		(layer "B.Cu")
		(net 6)
		(uuid "9d7055ca-8d61-47d2-9bc9-f635a4a799b1")
	)
	(segment
		(start 176.657 85.217)
		(end 178.435 86.995)
		(width 0.635)
		(layer "B.Cu")
		(net 6)
		(uuid "c7029c8b-1424-4d0f-a544-6a448743d52c")
	)
	(segment
		(start 110.744 89.408)
		(end 113.284 89.408)
		(width 0.635)
		(layer "F.Cu")
		(net 7)
		(uuid "4b44c6c5-2be3-4121-9848-3e8350027c78")
	)
	(segment
		(start 114.3 84.455)
		(end 113.665 83.82)
		(width 0.635)
		(layer "F.Cu")
		(net 7)
		(uuid "5b54821c-c677-48ea-a4a3-57569cf032b4")
	)
	(segment
		(start 107.569 86.233)
		(end 110.744 89.408)
		(width 0.635)
		(layer "F.Cu")
		(net 7)
		(uuid "792e0fe2-9dda-46eb-bf1b-a878a4348507")
	)
	(segment
		(start 114.3 88.392)
		(end 114.3 84.455)
		(width 0.635)
		(layer "F.Cu")
		(net 7)
		(uuid "7e6cb92d-08e4-4dd0-8e06-64eac82e02a8")
	)
	(segment
		(start 105.537 86.233)
		(end 107.569 86.233)
		(width 0.635)
		(layer "F.Cu")
		(net 7)
		(uuid "8fc7e6ed-ce2a-4e3a-a5ab-d02b538b6108")
	)
	(segment
		(start 113.665 83.82)
		(end 111.76 83.82)
		(width 0.635)
		(layer "F.Cu")
		(net 7)
		(uuid "e361e1d6-870f-4d7f-a1bd-03db06a29c7c")
	)
	(segment
		(start 113.284 89.408)
		(end 114.3 88.392)
		(width 0.635)
		(layer "F.Cu")
		(net 7)
		(uuid "ea52bf08-49de-4076-b0dd-448538138a7d")
	)
	(segment
		(start 167.513 87.757)
		(end 166.243 89.027)
		(width 0.635)
		(layer "B.Cu")
		(net 8)
		(uuid "2c703bc6-e2e8-418b-9473-491875a7b0ce")
	)
	(segment
		(start 169.672 87.757)
		(end 172.847 87.757)
		(width 0.635)
		(layer "B.Cu")
		(net 8)
		(uuid "50b71b69-deae-49d4-9df6-7b669e143e3f")
	)
	(segment
		(start 175.26 90.17)
		(end 181.61 90.17)
		(width 0.635)
		(layer "B.Cu")
		(net 8)
		(uuid "5e2d5da0-35de-433d-ab1b-a9ad9d7302bd")
	)
	(segment
		(start 162.56 93.98)
		(end 163.703 93.98)
		(width 0.635)
		(layer "B.Cu")
		(net 8)
		(uuid "5f1a6262-e6fb-4d28-b029-7e63c3ab9e97")
	)
	(segment
		(start 163.703 93.98)
		(end 166.243 91.44)
		(width 0.635)
		(layer "B.Cu")
		(net 8)
		(uuid "8eac3014-9ef0-4c9f-8427-217da4b3a554")
	)
	(segment
		(start 166.243 89.027)
		(end 166.243 91.44)
		(width 0.635)
		(layer "B.Cu")
		(net 8)
		(uuid "cae1be35-a50c-4ef0-a7e3-************")
	)
	(segment
		(start 172.847 87.757)
		(end 175.26 90.17)
		(width 0.635)
		(layer "B.Cu")
		(net 8)
		(uuid "d7662f70-ede0-41c7-833c-9c4967c8f6a6")
	)
	(segment
		(start 169.672 87.757)
		(end 167.513 87.757)
		(width 0.635)
		(layer "B.Cu")
		(net 8)
		(uuid "df86c077-a814-4c50-8e2e-a194eeaf1956")
	)
	(segment
		(start 116.205 91.44)
		(end 116.205 72.771)
		(width 0.635)
		(layer "F.Cu")
		(net 9)
		(uuid "0315e6fc-6a06-40e9-982d-da7ed5947234")
	)
	(segment
		(start 111.76 92.71)
		(end 114.935 92.71)
		(width 0.635)
		(layer "F.Cu")
		(net 9)
		(uuid "0d549c14-a8d9-40f0-bd7a-1b5784bd3f48")
	)
	(segment
		(start 115.316 71.882)
		(end 110.744 71.882)
		(width 0.635)
		(layer "F.Cu")
		(net 9)
		(uuid "3b7450f5-9335-433d-b593-641334fb26b1")
	)
	(segment
		(start 114.935 92.71)
		(end 116.205 91.44)
		(width 0.635)
		(layer "F.Cu")
		(net 9)
		(uuid "7225c56e-a4a0-41ef-8f82-5cf2ebd0c159")
	)
	(segment
		(start 106.045 74.93)
		(end 106.045 75.565)
		(width 0.635)
		(layer "F.Cu")
		(net 9)
		(uuid "93fda8e1-a4e8-406d-be9d-75083fe45b4b")
	)
	(segment
		(start 107.696 74.93)
		(end 106.045 74.93)
		(width 0.635)
		(layer "F.Cu")
		(net 9)
		(uuid "adb53912-61ed-428e-a4e4-de5f97139505")
	)
	(segment
		(start 106.045 75.565)
		(end 105.537 75.184)
		(width 0.635)
		(layer "F.Cu")
		(net 9)
		(uuid "b62a59d5-f7cc-448d-bfd2-e713c9d4c86b")
	)
	(segment
		(start 116.205 72.771)
		(end 115.316 71.882)
		(width 0.635)
		(layer "F.Cu")
		(net 9)
		(uuid "bf89d27c-1476-45ba-b77f-d13c8cf04aa5")
	)
	(segment
		(start 110.744 71.882)
		(end 107.696 74.93)
		(width 0.635)
		(layer "F.Cu")
		(net 9)
		(uuid "d6e59a67-e543-45cf-86f0-fbe36bb17527")
	)
	(segment
		(start 181.61 95.885)
		(end 172.72 95.885)
		(width 0.635)
		(layer "B.Cu")
		(net 10)
		(uuid "00210e8f-7311-4730-abf3-5eef0b09b54f")
	)
	(segment
		(start 162.56 103.632)
		(end 169.672 96.52)
		(width 0.635)
		(layer "B.Cu")
		(net 10)
		(uuid "516bb148-aa90-4a2b-8773-c38c6e8c13b6")
	)
	(segment
		(start 169.672 96.52)
		(end 169.672 92.837)
		(width 0.635)
		(layer "B.Cu")
		(net 10)
		(uuid "9f048155-4918-41a0-98a8-8789c7a666e0")
	)
	(segment
		(start 181.61 95.885)
		(end 181.61 95.758)
		(width 0.635)
		(layer "B.Cu")
		(net 10)
		(uuid "b0a23dc0-0b00-4689-b4d4-0f4fc2821ae5")
	)
	(segment
		(start 172.72 95.885)
		(end 169.672 92.837)
		(width 0.635)
		(layer "B.Cu")
		(net 10)
		(uuid "d3e915eb-3463-43d6-a57b-be9b50f421b1")
	)
	(segment
		(start 162.56 105.41)
		(end 162.56 103.632)
		(width 0.635)
		(layer "B.Cu")
		(net 10)
		(uuid "dcb7a309-0ff6-4271-b896-22e13ae73e3a")
	)
	(segment
		(start 109.982 80.518)
		(end 109.855 80.645)
		(width 0.635)
		(layer "F.Cu")
		(net 11)
		(uuid "490cc7d8-8568-40c5-8694-d718ab4290f9")
	)
	(segment
		(start 109.855 80.645)
		(end 106.045 80.645)
		(width 0.635)
		(layer "F.Cu")
		(net 11)
		(uuid "4d13a9de-858a-420e-bff5-4ffe20120b10")
	)
	(segment
		(start 106.045 80.645)
		(end 105.537 80.772)
		(width 0.635)
		(layer "F.Cu")
		(net 11)
		(uuid "d4ea8a4e-2287-4883-8fd4-a0d1e2ada63f")
	)
	(segment
		(start 111.76 80.518)
		(end 109.982 80.518)
		(width 0.635)
		(layer "F.Cu")
		(net 11)
		(uuid "f48ac418-06a7-4789-9f4a-d8ec82823f95")
	)
	(segment
		(start 109.4232 76.2)
		(end 111.76 73.8632)
		(width 0.635)
		(layer "B.Cu")
		(net 12)
		(uuid "13622de0-82ff-40ba-8d5f-fdc960eb61ca")
	)
	(segment
		(start 106.045 76.2)
		(end 105.537 76.581)
		(width 0.635)
		(layer "B.Cu")
		(net 12)
		(uuid "28bc8527-108e-407e-8600-c1681fcf36e4")
	)
	(segment
		(start 109.4232 76.2)
		(end 106.045 76.2)
		(width 0.635)
		(layer "B.Cu")
		(net 12)
		(uuid "da7f8c3c-6f6c-495e-b78c-9dc406d0ab23")
	)
	(segment
		(start 180.975 81.28)
		(end 181.61 81.915)
		(width 1.016)
		(layer "F.Cu")
		(net 13)
		(uuid "14d4fe31-f396-49b7-8335-36852e0f77ef")
	)
	(segment
		(start 181.61 88.773)
		(end 181.61 85.979)
		(width 1.016)
		(layer "F.Cu")
		(net 13)
		(uuid "1a59a53b-7acb-407d-be01-fcf15f86b53c")
	)
	(segment
		(start 181.61 85.979)
		(end 181.61 81.915)
		(width 1.016)
		(layer "F.Cu")
		(net 13)
		(uuid "21427728-87ba-447a-a299-79c75fa14387")
	)
	(segment
		(start 181.61 94.361)
		(end 181.61 91.567)
		(width 1.016)
		(layer "F.Cu")
		(net 13)
		(uuid "befffe98-3f3a-4cfe-a544-600fb81aec5f")
	)
	(segment
		(start 181.61 91.567)
		(end 181.61 88.773)
		(width 1.016)
		(layer "F.Cu")
		(net 13)
		(uuid "d92decc1-ef78-4e69-bef1-77df72819e08")
	)
	(segment
		(start 177.8 81.28)
		(end 180.975 81.28)
		(width 1.016)
		(layer "F.Cu")
		(net 13)
		(uuid "f4558286-5c0f-425d-8392-be65489f2955")
	)
	(via
		(at 177.8 81.28)
		(size 1.778)
		(drill 0.635)
		(layers "F.Cu" "B.Cu")
		(net 13)
		(uuid "6a8a8f8d-03d4-4eaf-a5be-35e6932d5276")
	)
	(segment
		(start 140.97 92.71)
		(end 140.97 90.17)
		(width 1.016)
		(layer "B.Cu")
		(net 13)
		(uuid "1654ba1d-d685-46f0-ba9f-5c26020a39b7")
	)
	(segment
		(start 148.59 69.85)
		(end 149.86 71.12)
		(width 1.016)
		(layer "F.Cu")
		(net 14)
		(uuid "13b78818-0d17-43e4-982f-158d1896ee95")
	)
	(segment
		(start 142.875 72.39)
		(end 145.415 69.85)
		(width 1.016)
		(layer "F.Cu")
		(net 14)
		(uuid "1931bfff-ea27-4bdc-8e05-f6f5a9880c0c")
	)
	(segment
		(start 137.16 72.39)
		(end 142.875 72.39)
		(width 1.016)
		(layer "F.Cu")
		(net 14)
		(uuid "1aa46aa2-5f8b-4f36-9542-40200bead189")
	)
	(segment
		(start 149.86 73.025)
		(end 150.495 73.66)
		(width 1.016)
		(layer "F.Cu")
		(net 14)
		(uuid "7fe89c29-d70b-4bcd-9774-7804262e8007")
	)
	(segment
		(start 149.86 71.12)
		(end 149.86 73.025)
		(width 1.016)
		(layer "F.Cu")
		(net 14)
		(uuid "adea5e1a-279a-47b1-9b09-cbdd8f9f2f90")
	)
	(segment
		(start 150.495 73.66)
		(end 151.13 73.66)
		(width 1.016)
		(layer "F.Cu")
		(net 14)
		(uuid "b53ab9c9-e81f-49e1-a1fe-3d889dcddb22")
	)
	(segment
		(start 145.415 69.85)
		(end 148.59 69.85)
		(width 1.016)
		(layer "F.Cu")
		(net 14)
		(uuid "e268d6df-fe4e-447d-b8f3-3b793d8c6e6f")
	)
	(segment
		(start 137.16 72.39)
		(end 134.62 72.39)
		(width 1.016)
		(layer "B.Cu")
		(net 14)
		(uuid "5b9d839b-2b99-4e1f-9a9d-26ae6dcf3944")
	)
	(segment
		(start 134.62 82.55)
		(end 134.62 76.2)
		(width 1.016)
		(layer "B.Cu")
		(net 14)
		(uuid "5c91052e-6f70-417d-9f5d-754585e878df")
	)
	(segment
		(start 151.13 78.74)
		(end 152.4 78.74)
		(width 1.016)
		(layer "B.Cu")
		(net 14)
		(uuid "68ca8217-f482-4e0a-8cab-420623a4d8c3")
	)
	(segment
		(start 148.59 79.375)
		(end 149.225 78.74)
		(width 1.016)
		(layer "B.Cu")
		(net 14)
		(uuid "a0abe526-5192-4572-a79e-89c967ece491")
	)
	(segment
		(start 149.225 78.74)
		(end 151.13 78.74)
		(width 1.016)
		(layer "B.Cu")
		(net 14)
		(uuid "b4edbc8a-baa2-4b2c-8215-28250ce3040e")
	)
	(segment
		(start 148.59 82.55)
		(end 148.59 79.375)
		(width 1.016)
		(layer "B.Cu")
		(net 14)
		(uuid "bb58df36-ec91-4cbf-9834-50987f988afe")
	)
	(segment
		(start 151.13 73.66)
		(end 151.13 78.74)
		(width 1.016)
		(layer "B.Cu")
		(net 14)
		(uuid "c6a6d4ad-e4a1-4c49-8b4c-1198f088e7c6")
	)
	(segment
		(start 134.62 76.2)
		(end 134.62 72.39)
		(width 1.016)
		(layer "B.Cu")
		(net 14)
		(uuid "d43af22a-e93c-4709-94ed-0ba8f13da5a4")
	)
	(segment
		(start 137.795 97.155)
		(end 137.795 99.06)
		(width 0.635)
		(layer "B.Cu")
		(net 15)
		(uuid "4982a716-ad89-42f8-aa68-c2b93a7131e0")
	)
	(segment
		(start 127 95.25)
		(end 130.81 95.25)
		(width 0.635)
		(layer "B.Cu")
		(net 15)
		(uuid "5f636e96-8b12-4c10-bb80-78ddc609f10f")
	)
	(segment
		(start 140.97 102.87)
		(end 144.145 99.695)
		(width 0.635)
		(layer "B.Cu")
		(net 15)
		(uuid "911e9dd2-5c90-4a84-9272-cb7ae8d62f05")
	)
	(segment
		(start 132.08 96.52)
		(end 137.16 96.52)
		(width 0.4318)
		(layer "B.Cu")
		(net 15)
		(uuid "96eacb27-2e14-423f-b6eb-aed7590e3a90")
	)
	(segment
		(start 152.4 96.52)
		(end 152.4 93.98)
		(width 0.635)
		(layer "B.Cu")
		(net 15)
		(uuid "9e261aa6-81d4-491a-81eb-146497492751")
	)
	(segment
		(start 137.795 102.87)
		(end 140.97 102.87)
		(width 0.635)
		(layer "B.Cu")
		(net 15)
		(uuid "ad57f252-aab1-463b-8369-85c0f9d11c47")
	)
	(segment
		(start 137.795 99.06)
		(end 137.16 99.695)
		(width 0.635)
		(layer "B.Cu")
		(net 15)
		(uuid "b5d7d58b-6f4f-485b-bca7-a5a486604b4b")
	)
	(segment
		(start 130.81 95.25)
		(end 132.08 96.52)
		(width 0.635)
		(layer "B.Cu")
		(net 15)
		(uuid "b6daba52-6576-4b98-9d40-9c68e15d1408")
	)
	(segment
		(start 149.86 99.695)
		(end 150.495 99.06)
		(width 0.635)
		(layer "B.Cu")
		(net 15)
		(uuid "b771633a-7ad6-448f-af4b-1030c817dfbb")
	)
	(segment
		(start 150.495 99.06)
		(end 150.495 98.425)
		(width 0.635)
		(layer "B.Cu")
		(net 15)
		(uuid "ba2396e8-33e4-4597-b94e-b56223eac1ca")
	)
	(segment
		(start 137.16 102.235)
		(end 137.795 102.87)
		(width 0.635)
		(layer "B.Cu")
		(net 15)
		(uuid "ba4899fb-d2a5-4540-9651-6d1a43f284fa")
	)
	(segment
		(start 137.16 99.695)
		(end 137.16 102.235)
		(width 0.635)
		(layer "B.Cu")
		(net 15)
		(uuid "ba7a4866-dfa6-4ce4-8583-edc6630ddd8b")
	)
	(segment
		(start 144.145 99.695)
		(end 149.86 99.695)
		(width 0.635)
		(layer "B.Cu")
		(net 15)
		(uuid "c8df37dc-9b72-4bdc-9a67-ab587243fe1f")
	)
	(segment
		(start 150.495 98.425)
		(end 152.4 96.52)
		(width 0.635)
		(layer "B.Cu")
		(net 15)
		(uuid "e1c3cdf3-a41b-41a6-9871-24d2ddf44206")
	)
	(segment
		(start 137.16 96.52)
		(end 137.795 97.155)
		(width 0.635)
		(layer "B.Cu")
		(net 15)
		(uuid "e7ed3fb3-5afd-42a7-bf12-a52890782e62")
	)
	(segment
		(start 147.32 73.66)
		(end 147.32 72.39)
		(width 0.635)
		(layer "F.Cu")
		(net 16)
		(uuid "1629c71d-c143-4cca-bd80-0617b513ff2b")
	)
	(segment
		(start 144.145 74.93)
		(end 144.78 74.295)
		(width 0.635)
		(layer "F.Cu")
		(net 16)
		(uuid "200deebf-4504-4c2e-8e6c-cc4fcc254601")
	)
	(segment
		(start 126.365 78.105)
		(end 143.51 78.105)
		(width 0.635)
		(layer "F.Cu")
		(net 16)
		(uuid "211b8842-4c8f-4285-9b22-ac9ca1c3be85")
	)
	(segment
		(start 124.46 87.63)
		(end 124.46 80.01)
		(width 0.635)
		(layer "F.Cu")
		(net 16)
		(uuid "4ec869ec-c108-4bd6-895c-d59b968528c8")
	)
	(segment
		(start 124.46 80.01)
		(end 126.365 78.105)
		(width 0.635)
		(layer "F.Cu")
		(net 16)
		(uuid "ab756a67-2beb-4c5e-9f7e-e3e073bf950e")
	)
	(segment
		(start 143.51 78.105)
		(end 144.145 77.47)
		(width 0.635)
		(layer "F.Cu")
		(net 16)
		(uuid "c377a0e8-e14e-44eb-8894-bc7bbaeedfe1")
	)
	(segment
		(start 144.145 77.47)
		(end 144.145 74.93)
		(width 0.635)
		(layer "F.Cu")
		(net 16)
		(uuid "e035ee4d-c6be-45a0-abf5-d9fa07216665")
	)
	(segment
		(start 146.685 74.295)
		(end 147.32 73.66)
		(width 0.635)
		(layer "F.Cu")
		(net 16)
		(uuid "e3dd02ba-**************-ab44def75bfb")
	)
	(segment
		(start 144.78 74.295)
		(end 146.685 74.295)
		(width 0.635)
		(layer "F.Cu")
		(net 16)
		(uuid "e9b8ed84-44a2-4cd8-b6ad-097aa6785a20")
	)
	(via
		(at 124.46 87.63)
		(size 1.778)
		(drill 0.635)
		(layers "F.Cu" "B.Cu")
		(net 16)
		(uuid "1361cc2b-6cfa-4cfd-b289-dac3043507a8")
	)
	(segment
		(start 147.955 69.85)
		(end 151.13 69.85)
		(width 0.635)
		(layer "B.Cu")
		(net 16)
		(uuid "0f8dc335-1d78-4fb1-8629-a9480c60e93f")
	)
	(segment
		(start 142.24 72.39)
		(end 147.32 72.39)
		(width 0.635)
		(layer "B.Cu")
		(net 16)
		(uuid "1b924bc6-d097-40be-8e8b-da5947f67301")
	)
	(segment
		(start 143.51 85.09)
		(end 140.97 85.09)
		(width 0.635)
		(layer "B.Cu")
		(net 16)
		(uuid "41e117cf-abde-415c-8023-647b395fd45e")
	)
	(segment
		(start 138.43 76.2)
		(end 142.24 72.39)
		(width 0.635)
		(layer "B.Cu")
		(net 16)
		(uuid "57872d10-600e-45bb-84f6-5673c9d3502f")
	)
	(segment
		(start 145.415 88.9)
		(end 144.78 88.265)
		(width 0.635)
		(layer "B.Cu")
		(net 16)
		(uuid "657400f9-b380-441b-8ed6-070cae842c5d")
	)
	(segment
		(start 138.43 83.82)
		(end 138.43 76.2)
		(width 0.635)
		(layer "B.Cu")
		(net 16)
		(uuid "677d68fc-b742-415e-bc10-f62e94f53cfe")
	)
	(segment
		(start 140.97 85.09)
		(end 139.7 85.09)
		(width 0.635)
		(layer "B.Cu")
		(net 16)
		(uuid "6b8efe58-1cdd-4609-8d71-f8bd1fd2acf6")
	)
	(segment
		(start 127 87.63)
		(end 124.46 87.63)
		(width 0.635)
		(layer "B.Cu")
		(net 16)
		(uuid "7187edbb-3cd1-45cd-8bf0-704d62442156")
	)
	(segment
		(start 139.7 85.09)
		(end 138.43 83.82)
		(width 0.635)
		(layer "B.Cu")
		(net 16)
		(uuid "93f71192-3bdb-42fd-9e2f-024593947a05")
	)
	(segment
		(start 152.4 88.9)
		(end 145.415 88.9)
		(width 0.4318)
		(layer "B.Cu")
		(net 16)
		(uuid "a564e4b1-b97f-4246-8bf6-79ed1284c3cb")
	)
	(segment
		(start 144.78 86.36)
		(end 143.51 85.09)
		(width 0.635)
		(layer "B.Cu")
		(net 16)
		(uuid "a8626d46-20bc-466c-8b78-9a7c85d9a168")
	)
	(segment
		(start 147.32 70.485)
		(end 147.955 69.85)
		(width 0.635)
		(layer "B.Cu")
		(net 16)
		(uuid "c391dbf5-b27d-4c3e-9f82-78670c484401")
	)
	(segment
		(start 144.78 88.265)
		(end 144.78 86.36)
		(width 0.635)
		(layer "B.Cu")
		(net 16)
		(uuid "d8fc8aa7-7308-49dd-af06-acb1f310852c")
	)
	(segment
		(start 147.32 72.39)
		(end 147.32 70.485)
		(width 0.635)
		(layer "B.Cu")
		(net 16)
		(uuid "ec17fde2-1451-4226-b632-935b596c25be")
	)
	(segment
		(start 134.62 90.17)
		(end 135.89 90.17)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "0503e2c0-2ef2-4137-8cbc-79860c6d94eb")
	)
	(segment
		(start 136.525 93.345)
		(end 135.89 93.98)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "0740703e-7ead-4b3d-ad05-42a81605e658")
	)
	(segment
		(start 135.89 93.98)
		(end 125.73 93.98)
		(width 0.4318)
		(layer "B.Cu")
		(net 20)
		(uuid "0fa39d8c-a97c-4b0c-a9fd-b06c293689a3")
	)
	(segment
		(start 149 102.87)
		(end 151.765 102.87)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "2062c704-7cd0-4f2b-9571-ed5242759765")
	)
	(segment
		(start 152.4 103.505)
		(end 152.4 105.41)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "249ead8b-af88-48f6-8156-4e1a2822e7d9")
	)
	(segment
		(start 149 102.87)
		(end 147.32 102.87)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "3d4c167a-2ebb-43da-8f20-81c943dc41ce")
	)
	(segment
		(start 125.73 96.52)
		(end 125.095 95.885)
		(width 0.4318)
		(layer "B.Cu")
		(net 20)
		(uuid "3da92e06-e1fd-46ce-a349-fddbdeecbf63")
	)
	(segment
		(start 125.73 93.98)
		(end 125.095 94.615)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "600e1136-55a5-4dcd-8ba1-8a1d0e477004")
	)
	(segment
		(start 145.415 106.045)
		(end 146.685 104.775)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "62cf17ad-c8df-4e7a-b8bb-59cadad650c6")
	)
	(segment
		(start 130.81 97.79)
		(end 130.81 104.775)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "639d4431-23c7-4e07-9f2f-8b1ec26f23ac")
	)
	(segment
		(start 129.54 96.52)
		(end 130.81 97.79)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "6a5c6b5f-e5ff-4e04-ad08-1bc0cdf9ae64")
	)
	(segment
		(start 132.08 106.045)
		(end 145.415 106.045)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "748db04f-fc45-4ac7-a9f0-bb4f65538e33")
	)
	(segment
		(start 125.095 94.615)
		(end 125.095 95.885)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "78af75eb-2976-4ba8-a731-41ed13bd54df")
	)
	(segment
		(start 135.89 90.17)
		(end 136.525 90.805)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "8edb0142-00af-43fb-a680-bea8964c0366")
	)
	(segment
		(start 146.685 103.505)
		(end 147.32 102.87)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "95ef96c5-fc2f-45f6-9388-610cfc31f388")
	)
	(segment
		(start 146.685 104.775)
		(end 146.685 103.505)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "aeb8cc74-9fb6-4aab-b418-930a416c4c81")
	)
	(segment
		(start 151.765 102.87)
		(end 152.4 103.505)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "bac3d83f-bf15-483c-8b3b-648a37b5518f")
	)
	(segment
		(start 125.73 96.52)
		(end 128.905 96.52)
		(width 0.4318)
		(layer "B.Cu")
		(net 20)
		(uuid "ca9e8d86-1dd4-4cc5-967c-3de2f5f04f1d")
	)
	(segment
		(start 128.905 96.52)
		(end 129.54 96.52)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "cfb78591-2c86-427d-b579-1b1e7559b7a4")
	)
	(segment
		(start 130.81 104.775)
		(end 132.08 106.045)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "d392717e-fa7f-44f8-ac82-3b928eff41d8")
	)
	(segment
		(start 136.525 90.805)
		(end 136.525 93.345)
		(width 0.635)
		(layer "B.Cu")
		(net 20)
		(uuid "e91ed180-3ac4-4483-abfc-2fd186b56e31")
	)
	(segment
		(start 145 102.87)
		(end 145 101.5)
		(width 0.635)
		(layer "B.Cu")
		(net 21)
		(uuid "14e08bc4-5a83-4222-8a28-609e24a53534")
	)
	(segment
		(start 152.4 100.33)
		(end 152.4 99.06)
		(width 0.635)
		(layer "B.Cu")
		(net 21)
		(uuid "1c043c2c-9d10-4fc8-8048-34f773c2ebb2")
	)
	(segment
		(start 145 101.5)
		(end 145.535 100.965)
		(width 0.635)
		(layer "B.Cu")
		(net 21)
		(uuid "1e40bdf7-4dc2-490e-a633-763719e79c3d")
	)
	(segment
		(start 145 102.87)
		(end 145 103.92)
		(width 0.635)
		(layer "B.Cu")
		(net 21)
		(uuid "4fcd118f-3ff4-4a44-8098-d9d6bfad2b1e")
	)
	(segment
		(start 151.765 100.965)
		(end 152.4 100.33)
		(width 0.635)
		(layer "B.Cu")
		(net 21)
		(uuid "63af642e-da00-4dfb-85b3-7c1239a0f986")
	)
	(segment
		(start 134.62 104.14)
		(end 135.255 104.775)
		(width 0.635)
		(layer "B.Cu")
		(net 21)
		(uuid "8248e200-6b23-4bd3-80a1-d00c285a48c4")
	)
	(segment
		(start 145 103.92)
		(end 144.145 104.775)
		(width 0.635)
		(layer "B.Cu")
		(net 21)
		(uuid "89186cc8-02d4-4b0b-9f3b-3c0371353956")
	)
	(segment
		(start 135.255 104.775)
		(end 144.145 104.775)
		(width 0.635)
		(layer "B.Cu")
		(net 21)
		(uuid "89ab0ae8-2211-4b4b-bd7d-177449367326")
	)
	(segment
		(start 134.62 97.79)
		(end 134.62 104.14)
		(width 0.635)
		(layer "B.Cu")
		(net 21)
		(uuid "9361157b-61d1-4b07-b9c7-62e1d8aefdad")
	)
	(segment
		(start 145.535 100.965)
		(end 151.765 100.965)
		(width 0.635)
		(layer "B.Cu")
		(net 21)
		(uuid "a088dd79-d17d-4d6c-b9e7-bafd321eba17")
	)
	(segment
		(start 122.555 72.39)
		(end 127 72.39)
		(width 0.635)
		(layer "B.Cu")
		(net 22)
		(uuid "96b18c93-7524-4cb5-a51f-22577d4e391c")
	)
	(segment
		(start 121.92 73.025)
		(end 122.555 72.39)
		(width 0.635)
		(layer "B.Cu")
		(net 22)
		(uuid "af097f1b-b68e-401c-b4b1-b065cb82d4c4")
	)
	(segment
		(start 121.92 73.025)
		(end 121.92 73.8632)
		(width 0.635)
		(layer "B.Cu")
		(net 22)
		(uuid "b8b045bf-6022-4db7-8236-1f0a768a850e")
	)
	(segment
		(start 109.855 92.329)
		(end 109.855 102.235)
		(width 0.635)
		(layer "F.Cu")
		(net 29)
		(uuid "35e822d3-b6c1-48ac-947b-0c49dd99b3cf")
	)
	(segment
		(start 109.22 91.694)
		(end 109.855 92.329)
		(width 0.635)
		(layer "F.Cu")
		(net 29)
		(uuid "6bdbc486-0b80-4d0e-a7a8-a30992e01b36")
	)
	(segment
		(start 109.22 102.87)
		(end 105.41 102.87)
		(width 0.635)
		(layer "F.Cu")
		(net 29)
		(uuid "74799268-938f-4cd3-a03d-42f85109f704")
	)
	(segment
		(start 105.537 91.694)
		(end 109.22 91.694)
		(width 0.635)
		(layer "F.Cu")
		(net 29)
		(uuid "9b91a7ca-5594-421f-ba30-b5a1d187c177")
	)
	(segment
		(start 105.537 99.949)
		(end 109.855 99.949)
		(width 0.635)
		(layer "F.Cu")
		(net 29)
		(uuid "bfb774bf-2b2b-487e-bf8f-43685ed61c5b")
	)
	(segment
		(start 109.855 102.235)
		(end 109.22 102.87)
		(width 0.635)
		(layer "F.Cu")
		(net 29)
		(uuid "f6f53771-**************-4e955ebb8ce0")
	)
	(segment
		(start 135.255 104.14)
		(end 121.92 104.14)
		(width 0.635)
		(layer "F.Cu")
		(net 34)
		(uuid "26a2b1b1-8e69-4079-90fd-ea26ccc44a1b")
	)
	(segment
		(start 139.065 100.33)
		(end 135.255 104.14)
		(width 0.635)
		(layer "F.Cu")
		(net 34)
		(uuid "9fe153db-bd12-4c17-956f-3c10bd4cc957")
	)
	(via
		(at 139.065 100.33)
		(size 1.778)
		(drill 0.635)
		(layers "F.Cu" "B.Cu")
		(net 34)
		(uuid "2be77646-59a1-4342-a436-2d34a02a9df8")
	)
	(segment
		(start 140.97 87.63)
		(end 139.065 89.535)
		(width 0.635)
		(layer "B.Cu")
		(net 34)
		(uuid "2453fc21-bd16-4f19-a8e9-0a5136c9071c")
	)
	(segment
		(start 139.065 89.535)
		(end 139.065 100.33)
		(width 0.635)
		(layer "B.Cu")
		(net 34)
		(uuid "c0807313-377f-4078-9538-fda302d9e350")
	)
	(segment
		(start 125.73 82.55)
		(end 127 82.55)
		(width 0.635)
		(layer "B.Cu")
		(net 35)
		(uuid "744995b9-f662-4a00-bcdf-ad1711c0f8f4")
	)
	(segment
		(start 124.46 83.82)
		(end 125.73 82.55)
		(width 0.635)
		(layer "B.Cu")
		(net 35)
		(uuid "9b76f048-ca9f-4c00-9463-ecab2d68cd8c")
	)
	(segment
		(start 121.92 83.82)
		(end 124.46 83.82)
		(width 0.635)
		(layer "B.Cu")
		(net 35)
		(uuid "a412cf86-561f-438e-8b41-31eb9b609d7c")
	)
	(segment
		(start 121.92 92.71)
		(end 127 92.71)
		(width 0.635)
		(layer "B.Cu")
		(net 36)
		(uuid "5ab7bf0a-0ff9-49f4-b42a-869adc36c3da")
	)
	(segment
		(start 121.92 89.535)
		(end 121.92 87.63)
		(width 0.635)
		(layer "B.Cu")
		(net 37)
		(uuid "135af78c-c0fc-4f9e-91d5-c7c815d0cf01")
	)
	(segment
		(start 135.89 88.9)
		(end 136.525 88.265)
		(width 0.635)
		(layer "B.Cu")
		(net 37)
		(uuid "3385afc4-8250-4fa2-9ac9-454ed803f4f3")
	)
	(segment
		(start 121.92 89.535)
		(end 122.555 90.17)
		(width 0.635)
		(layer "B.Cu")
		(net 37)
		(uuid "34159226-0e5e-4493-9700-58921cf04323")
	)
	(segment
		(start 127 90.17)
		(end 130.175 90.17)
		(width 0.635)
		(layer "B.Cu")
		(net 37)
		(uuid "37dec852-00d0-4765-a267-8578451fbd47")
	)
	(segment
		(start 131.445 88.9)
		(end 135.89 88.9)
		(width 0.4318)
		(layer "B.Cu")
		(net 37)
		(uuid "44b5951e-facd-4b3e-a910-8c24ea7ee41d")
	)
	(segment
		(start 122.555 90.17)
		(end 127 90.17)
		(width 0.635)
		(layer "B.Cu")
		(net 37)
		(uuid "5a912538-ba98-4838-bcbb-d2dc7f80b389")
	)
	(segment
		(start 135.89 85.09)
		(end 134.62 85.09)
		(width 0.635)
		(layer "B.Cu")
		(net 37)
		(uuid "5f75231a-f9cc-4851-81e8-fda9b6221abd")
	)
	(segment
		(start 132.715 92.71)
		(end 134.62 92.71)
		(width 0.635)
		(layer "B.Cu")
		(net 37)
		(uuid "61b4b364-e696-4942-bc45-5da5686a0f2c")
	)
	(segment
		(start 130.175 90.17)
		(end 132.715 92.71)
		(width 0.635)
		(layer "B.Cu")
		(net 37)
		(uuid "66e7dcc8-590c-4da6-b6fc-a374325fd9d3")
	)
	(segment
		(start 136.525 88.265)
		(end 136.525 85.725)
		(width 0.635)
		(layer "B.Cu")
		(net 37)
		(uuid "c455cd91-8134-4bfb-8334-84264348e83e")
	)
	(segment
		(start 135.89 85.09)
		(end 136.525 85.725)
		(width 0.635)
		(layer "B.Cu")
		(net 37)
		(uuid "d88b254d-9891-4ec5-98d0-94215d6986c8")
	)
	(segment
		(start 130.175 90.17)
		(end 131.445 88.9)
		(width 0.635)
		(layer "B.Cu")
		(net 37)
		(uuid "f098214b-4246-4f87-8ad9-1483f9f48a18")
	)
	(segment
		(start 124.333 80.518)
		(end 124.46 80.645)
		(width 0.635)
		(layer "B.Cu")
		(net 38)
		(uuid "187317ec-6999-482c-b970-9eaadf34548d")
	)
	(segment
		(start 130.81 81.915)
		(end 129.54 80.645)
		(width 0.635)
		(layer "B.Cu")
		(net 38)
		(uuid "1ffbb51f-55de-4430-8a76-fc607274a79e")
	)
	(segment
		(start 130.81 86.995)
		(end 130.81 81.915)
		(width 0.635)
		(layer "B.Cu")
		(net 38)
		(uuid "836f8d0a-7dab-463d-9e00-337bcc484871")
	)
	(segment
		(start 131.445 87.63)
		(end 134.62 87.63)
		(width 0.635)
		(layer "B.Cu")
		(net 38)
		(uuid "8e5e7cd1-2af6-46a4-a727-2d0a1c690900")
	)
	(segment
		(start 129.54 80.645)
		(end 124.46 80.645)
		(width 0.635)
		(layer "B.Cu")
		(net 38)
		(uuid "c8fb5cd0-de63-40b6-9dba-ce3a4e2dc3d0")
	)
	(segment
		(start 131.445 87.63)
		(end 130.81 86.995)
		(width 0.635)
		(layer "B.Cu")
		(net 38)
		(uuid "e1db0c2d-7eb3-42cc-a222-bd48388796ae")
	)
	(segment
		(start 121.92 80.518)
		(end 124.333 80.518)
		(width 0.635)
		(layer "B.Cu")
		(net 38)
		(uuid "e8b8155a-d38e-435f-91e7-50dcbaeeb705")
	)
	(segment
		(start 121.92 77.216)
		(end 122.936 77.216)
		(width 0.635)
		(layer "B.Cu")
		(net 39)
		(uuid "013657c4-2373-4bb5-be5e-f809f5eb14e6")
	)
	(segment
		(start 137.795 94.615)
		(end 137.16 95.25)
		(width 0.635)
		(layer "B.Cu")
		(net 39)
		(uuid "07f70ed2-1f65-462e-8471-9dfc93fe6a37")
	)
	(segment
		(start 132.08 83.185)
		(end 132.715 83.82)
		(width 0.635)
		(layer "B.Cu")
		(net 39)
		(uuid "1e83b227-4b63-4325-8c0b-d43cd1c94424")
	)
	(segment
		(start 132.715 83.82)
		(end 136.525 83.82)
		(width 0.4318)
		(layer "B.Cu")
		(net 39)
		(uuid "55cd73c0-46b1-465c-b2a4-b07ac563d135")
	)
	(segment
		(start 136.525 83.82)
		(end 137.795 85.09)
		(width 0.635)
		(layer "B.Cu")
		(net 39)
		(uuid "6619e9d0-ea8e-4413-85cb-f22ba6df8b3d")
	)
	(segment
		(start 122.936 77.216)
		(end 124.46 78.74)
		(width 0.635)
		(layer "B.Cu")
		(net 39)
		(uuid "69d9ae8d-ab6c-486a-9a76-f667dd27c2ee")
	)
	(segment
		(start 137.795 94.615)
		(end 137.795 85.09)
		(width 0.635)
		(layer "B.Cu")
		(net 39)
		(uuid "74dcb082-873d-4ba4-b89e-4b45e82d609a")
	)
	(segment
		(start 132.08 79.375)
		(end 132.08 83.185)
		(width 0.635)
		(layer "B.Cu")
		(net 39)
		(uuid "7a2747e6-7a17-4c25-8227-6c4a75d79619")
	)
	(segment
		(start 137.16 95.25)
		(end 134.62 95.25)
		(width 0.635)
		(layer "B.Cu")
		(net 39)
		(uuid "e1ffae39-72fb-4c17-8f56-40dfda99feaf")
	)
	(segment
		(start 131.445 78.74)
		(end 132.08 79.375)
		(width 0.635)
		(layer "B.Cu")
		(net 39)
		(uuid "f45efea5-8f79-4c8f-ad1c-0f1ac4c6ca65")
	)
	(segment
		(start 124.46 78.74)
		(end 131.445 78.74)
		(width 0.635)
		(layer "B.Cu")
		(net 39)
		(uuid "fe01b6bb-2ed2-46d6-b388-2b124cc9529e")
	)
	(zone
		(net 13)
		(net_name "GND")
		(layer "B.Cu")
		(uuid "00000000-0000-0000-0000-00005b221ae5")
		(hatch edge 0.508)
		(connect_pads
			(clearance 0.508)
		)
		(min_thickness 0.254)
		(filled_areas_thickness no)
		(fill yes
			(thermal_gap 0.508)
			(thermal_bridge_width 0.508)
		)
		(polygon
			(pts
				(xy 182.88 110.49) (xy 182.88 67.31) (xy 105.41 67.31) (xy 105.41 110.49)
			)
		)
		(filled_polygon
			(layer "B.Cu")
			(pts
				(xy 143.183791 85.936002) (xy 143.204765 85.952905) (xy 143.917095 86.665235) (xy 143.951121 86.727547)
				(xy 143.954 86.75433) (xy 143.954 88.346359) (xy 143.976432 88.459131) (xy 143.985742 88.505935)
				(xy 144.048008 88.656258) (xy 144.138404 88.791545) (xy 144.234595 88.887736) (xy 144.257845 88.910986)
				(xy 144.257856 88.910996) (xy 144.888456 89.541596) (xy 145.023742 89.631992) (xy 145.174065 89.694258)
				(xy 145.333647 89.726001) (xy 145.333648 89.726001) (xy 145.496353 89.726001) (xy 145.496354 89.726001)
				(xy 145.655936 89.694258) (xy 145.801433 89.63399) (xy 145.849651 89.6244) (xy 146.818014 89.6244)
				(xy 146.886135 89.644402) (xy 146.932628 89.698058) (xy 146.942732 89.768332) (xy 146.937847 89.789336)
				(xy 146.913721 89.863587) (xy 146.91372 89.86359) (xy 146.91372 89.863592) (xy 146.8815 90.067019)
				(xy 146.8815 90.272981) (xy 146.91372 90.476408) (xy 146.977366 90.67229) (xy 147.070871 90.855803)
				(xy 147.191932 91.02243) (xy 147.191934 91.022432) (xy 147.191936 91.022435) (xy 147.337564 91.168063)
				(xy 147.337567 91.168065) (xy 147.33757 91.168068) (xy 147.504197 91.289129) (xy 147.580511 91.328013)
				(xy 147.632126 91.37676) (xy 147.649192 91.445675) (xy 147.626292 91.512876) (xy 147.580513 91.552545)
				(xy 147.504455 91.591299) (xy 147.337892 91.712315) (xy 147.337889 91.712317) (xy 147.192317 91.857889)
				(xy 147.192315 91.857892) (xy 147.0713 92.024454) (xy 146.977829 92.2079) (xy 146.977826 92.207906)
				(xy 146.914208 92.403704) (xy 146.905926 92.456) (xy 148.278314 92.456) (xy 148.26992 92.464394)
				(xy 148.217259 92.555606) (xy 148.19 92.657339) (xy 148.19 92.762661) (xy 148.217259 92.864394)
				(xy 148.26992 92.955606) (xy 148.278314 92.964) (xy 146.905926 92.964) (xy 146.914208 93.016295)
				(xy 146.977826 93.212093) (xy 146.977829 93.212099) (xy 147.0713 93.395545) (xy 147.192315 93.562107)
				(xy 147.192317 93.56211) (xy 147.337889 93.707682) (xy 147.337892 93.707684) (xy 147.504454 93.828699)
				(xy 147.581061 93.867733) (xy 147.632676 93.916482) (xy 147.649742 93.985397) (xy 147.626841 94.052598)
				(xy 147.581061 94.092267) (xy 147.504454 94.1313) (xy 147.337892 94.252315) (xy 147.337889 94.252317)
				(xy 147.192317 94.397889) (xy 147.192315 94.397892) (xy 147.0713 94.564454) (xy 146.977829 94.7479)
				(xy 146.977826 94.747906) (xy 146.914208 94.943704) (xy 146.905926 94.996) (xy 148.278314 94.996)
				(xy 148.26992 95.004394) (xy 148.217259 95.095606) (xy 148.19 95.197339) (xy 148.19 95.302661) (xy 148.217259 95.404394)
				(xy 148.26992 95.495606) (xy 148.278314 95.504) (xy 146.905926 95.504) (xy 146.914208 95.556295)
				(xy 146.977826 95.752093) (xy 146.977829 95.752099) (xy 147.0713 95.935545) (xy 147.192315 96.102107)
				(xy 147.192317 96.10211) (xy 147.337889 96.247682) (xy 147.337892 96.247684) (xy 147.504455 96.3687)
				(xy 147.580511 96.407453) (xy 147.632126 96.456202) (xy 147.649192 96.525117) (xy 147.626291 96.592318)
				(xy 147.580511 96.631987) (xy 147.504193 96.670872) (xy 147.337567 96.791934) (xy 147.337564 96.791936)
				(xy 147.191936 96.937564) (xy 147.191934 96.937567) (xy 147.070873 97.104193) (xy 146.977367 97.287708)
				(xy 146.977364 97.287714) (xy 146.913721 97.483587) (xy 146.91372 97.48359) (xy 146.91372 97.483592)
				(xy 146.8815 97.687019) (xy 146.8815 97.892981) (xy 146.913702 98.096295) (xy 146.913721 98.096412)
				(xy 146.949838 98.20757) (xy 146.977366 98.29229) (xy 147.070871 98.475803) (xy 147.191932 98.64243)
				(xy 147.191934 98.642432) (xy 147.191936 98.642435) (xy 147.203406 98.653905) (xy 147.237432 98.716217)
				(xy 147.232367 98.787032) (xy 147.18982 98.843868) (xy 147.1233 98.868679) (xy 147.114311 98.869)
				(xy 144.06364 98.869) (xy 143.926862 98.896207) (xy 143.904065 98.900742) (xy 143.904062 98.900742)
				(xy 143.904059 98.900744) (xy 143.753742 98.963008) (xy 143.618459 99.053401) (xy 143.618452 99.053406)
				(xy 141.147264 101.524595) (xy 141.084952 101.558621) (xy 141.058169 101.5615) (xy 140.867019 101.5615)
				(xy 140.663592 101.59372) (xy 140.66359 101.59372) (xy 140.663587 101.593721) (xy 140.467714 101.657364)
				(xy 140.467708 101.657367) (xy 140.284193 101.750873) (xy 140.117567 101.871934) (xy 140.117564 101.871936)
				(xy 139.982406 102.007095) (xy 139.920094 102.041121) (xy 139.893311 102.044) (xy 138.18933 102.044)
				(xy 138.159414 102.035216) (xy 138.128945 102.028588) (xy 138.123849 102.024773) (xy 138.121209 102.023998)
				(xy 138.100235 102.007095) (xy 138.022905 101.929765) (xy 137.988879 101.867453) (xy 137.986 101.84067)
				(xy 137.986 101.520753) (xy 138.006002 101.452632) (xy 138.059658 101.406139) (xy 138.129932 101.396035)
				(xy 138.186057 101.418815) (xy 138.332551 101.525249) (xy 138.528546 101.625114) (xy 138.737751 101.693089)
				(xy 138.955014 101.7275) (xy 138.955017 101.7275) (xy 139.174983 101.7275) (xy 139.174986 101.7275)
				(xy 139.392249 101.693089) (xy 139.601454 101.625114) (xy 139.797449 101.525249) (xy 139.97541 101.395953)
				(xy 140.130953 101.24041) (xy 140.260249 101.062449) (xy 140.360114 100.866454) (xy 140.428089 100.657249)
				(xy 140.4625 100.439986) (xy 140.4625 100.220014) (xy 140.428089 100.002751) (xy 140.360114 99.793546)
				(xy 140.260249 99.597551) (xy 140.130953 99.41959) (xy 140.13095 99.419587) (xy 140.130948 99.419584)
				(xy 139.975415 99.264051) (xy 139.975409 99.264046) (xy 139.942938 99.240454) (xy 139.939543 99.236051)
				(xy 139.934488 99.233743) (xy 139.918097 99.208239) (xy 139.899585 99.184231) (xy 139.898311 99.177452)
				(xy 139.896104 99.174017) (xy 139.891 99.138519) (xy 139.891 99.117468) (xy 139.911002 99.049347)
				(xy 139.964658 99.002854) (xy 140.034932 98.99275) (xy 140.065227 99.001063) (xy 140.067907 99.002173)
				(xy 140.263705 99.065791) (xy 140.263701 99.065791) (xy 140.467061 99.098) (xy 140.716 99.098) (xy 140.716 98.101686)
				(xy 140.724394 98.11008) (xy 140.815606 98.162741) (xy 140.917339 98.19) (xy 141.022661 98.19) (xy 141.124394 98.162741)
				(xy 141.215606 98.11008) (xy 141.224 98.101686) (xy 141.224 99.098) (xy 141.472939 99.098) (xy 141.676296 99.065791)
				(xy 141.872093 99.002173) (xy 141.872099 99.00217) (xy 142.055545 98.908699) (xy 142.222107 98.787684)
				(xy 142.22211 98.787682) (xy 142.367682 98.64211) (xy 142.367684 98.642107) (xy 142.488699 98.475545)
				(xy 142.58217 98.292099) (xy 142.582173 98.292093) (xy 142.645791 98.096295) (xy 142.654074 98.044)
				(xy 141.281686 98.044) (xy 141.29008 98.035606) (xy 141.342741 97.944394) (xy 141.37 97.842661)
				(xy 141.37 97.737339) (xy 141.342741 97.635606) (xy 141.29008 97.544394) (xy 141.281686 97.536)
				(xy 142.654074 97.536) (xy 142.645791 97.483704) (xy 142.582173 97.287906) (xy 142.58217 97.2879)
				(xy 142.488699 97.104454) (xy 142.367684 96.937892) (xy 142.367682 96.937889) (xy 142.22211 96.792317)
				(xy 142.222107 96.792315) (xy 142.055542 96.671298) (xy 141.979487 96.632545) (xy 141.927872 96.583797)
				(xy 141.910807 96.514882) (xy 141.933708 96.44768) (xy 141.979485 96.408014) (xy 142.055803 96.369129)
				(xy 142.22243 96.248068) (xy 142.368068 96.10243) (xy 142.489129 95.935803) (xy 142.582634 95.75229)
				(xy 142.64628 95.556408) (xy 142.6785 95.352981) (xy 142.6785 95.147019) (xy 142.64628 94.943592)
				(xy 142.582634 94.74771) (xy 142.489129 94.564197) (xy 142.368068 94.39757) (xy 142.368065 94.397567)
				(xy 142.368063 94.397564) (xy 142.222435 94.251936) (xy 142.222432 94.251934) (xy 142.22243 94.251932)
				(xy 142.096999 94.160802) (xy 142.055806 94.130873) (xy 142.055805 94.130872) (xy 142.055803 94.130871)
				(xy 141.979486 94.091985) (xy 141.927873 94.043239) (xy 141.910807 93.974324) (xy 141.933708 93.907122)
				(xy 141.979488 93.867453) (xy 142.055545 93.828699) (xy 142.222107 93.707684) (xy 142.22211 93.707682)
				(xy 142.367682 93.56211) (xy 142.367684 93.562107) (xy 142.488699 93.395545) (xy 142.58217 93.212099)
				(xy 142.582173 93.212093) (xy 142.645791 93.016295) (xy 142.654074 92.964) (xy 141.281686 92.964)
				(xy 141.29008 92.955606) (xy 141.342741 92.864394) (xy 141.37 92.762661) (xy 141.37 92.657339) (xy 141.342741 92.555606)
				(xy 141.29008 92.464394) (xy 141.281686 92.456) (xy 142.654074 92.456) (xy 142.645791 92.403704)
				(xy 142.582173 92.207906) (xy 142.58217 92.2079) (xy 142.488699 92.024454) (xy 142.367684 91.857892)
				(xy 142.367682 91.857889) (xy 142.22211 91.712317) (xy 142.222107 91.712315) (xy 142.055545 91.5913)
				(xy 141.978938 91.552267) (xy 141.927323 91.503519) (xy 141.910257 91.434604) (xy 141.933158 91.367402)
				(xy 141.978938 91.327733) (xy 142.055545 91.288699) (xy 142.222107 91.167684) (xy 142.22211 91.167682)
				(xy 142.367682 91.02211) (xy 142.367684 91.022107) (xy 142.488699 90.855545) (xy 142.58217 90.672099)
				(xy 142.582173 90.672093) (xy 142.645791 90.476295) (xy 142.654074 90.424) (xy 141.281686 90.424)
				(xy 141.29008 90.415606) (xy 141.342741 90.324394) (xy 141.37 90.222661) (xy 141.37 90.117339) (xy 141.342741 90.015606)
				(xy 141.29008 89.924394) (xy 141.281686 89.916) (xy 142.654074 89.916) (xy 142.645791 89.863704)
				(xy 142.582173 89.667906) (xy 142.58217 89.6679) (xy 142.488699 89.484454) (xy 142.367684 89.317892)
				(xy 142.367682 89.317889) (xy 142.22211 89.172317) (xy 142.222107 89.172315) (xy 142.055542 89.051298)
				(xy 141.979487 89.012545) (xy 141.927872 88.963797) (xy 141.910807 88.894882) (xy 141.933708 88.82768)
				(xy 141.979485 88.788014) (xy 142.055803 88.749129) (xy 142.22243 88.628068) (xy 142.368068 88.48243)
				(xy 142.489129 88.315803) (xy 142.582634 88.13229) (xy 142.64628 87.936408) (xy 142.6785 87.732981)
				(xy 142.6785 87.527019) (xy 142.64628 87.323592) (xy 142.582634 87.12771) (xy 142.489129 86.944197)
				(xy 142.368068 86.77757) (xy 142.368065 86.777567) (xy 142.368063 86.777564) (xy 142.222435 86.631936)
				(xy 142.222432 86.631934) (xy 142.22243 86.631932) (xy 142.096999 86.540802) (xy 142.055806 86.510873)
				(xy 142.055805 86.510872) (xy 142.055803 86.510871) (xy 141.980034 86.472265) (xy 141.928422 86.423519)
				(xy 141.911356 86.354604) (xy 141.934257 86.287402) (xy 141.980034 86.247735) (xy 142.055803 86.209129)
				(xy 142.22243 86.088068) (xy 142.267498 86.043) (xy 142.357594 85.952905) (xy 142.419906 85.918879)
				(xy 142.446689 85.916) (xy 143.11567 85.916)
			)
		)
		(filled_polygon
			(layer "B.Cu")
			(pts
				(xy 148.844 94.938314) (xy 148.835606 94.92992) (xy 148.744394 94.877259) (xy 148.642661 94.85)
				(xy 148.537339 94.85) (xy 148.435606 94.877259) (xy 148.344394 94.92992) (xy 148.336 94.938314)
				(xy 148.336 93.021686) (xy 148.344394 93.03008) (xy 148.435606 93.082741) (xy 148.537339 93.11)
				(xy 148.642661 93.11) (xy 148.744394 93.082741) (xy 148.835606 93.03008) (xy 148.844 93.021686)
			)
		)
		(filled_polygon
			(layer "B.Cu")
			(pts
				(xy 141.224 92.398314) (xy 141.215606 92.38992) (xy 141.124394 92.337259) (xy 141.022661 92.31)
				(xy 140.917339 92.31) (xy 140.815606 92.337259) (xy 140.724394 92.38992) (xy 140.716 92.398314)
				(xy 140.716 90.481686) (xy 140.724394 90.49008) (xy 140.815606 90.542741) (xy 140.917339 90.57)
				(xy 141.022661 90.57) (xy 141.124394 90.542741) (xy 141.215606 90.49008) (xy 141.224 90.481686)
			)
		)
		(filled_polygon
			(layer "B.Cu")
			(pts
				(xy 129.213791 81.491002) (xy 129.234765 81.507905) (xy 129.947095 82.220235) (xy 129.981121 82.282547)
				(xy 129.984 82.30933) (xy 129.984 87.076359) (xy 129.994254 87.127906) (xy 130.015742 87.235935)
				(xy 130.078008 87.386258) (xy 130.168404 87.521545) (xy 130.238359 87.5915) (xy 130.287855 87.640996)
				(xy 130.287866 87.641006) (xy 130.803401 88.156541) (xy 130.803404 88.156545) (xy 130.820614 88.173755)
				(xy 130.822767 88.175908) (xy 130.840275 88.207975) (xy 130.856789 88.238217) (xy 130.856788 88.238219)
				(xy 130.85679 88.238222) (xy 130.85457 88.269241) (xy 130.851724 88.309032) (xy 130.851722 88.309034)
				(xy 130.851722 88.309038) (xy 130.851708 88.309055) (xy 130.822763 88.354095) (xy 129.869763 89.307096)
				(xy 129.807453 89.34112) (xy 129.78067 89.344) (xy 128.476689 89.344) (xy 128.408568 89.323998)
				(xy 128.387594 89.307095) (xy 128.252435 89.171936) (xy 128.252432 89.171934) (xy 128.25243 89.171932)
				(xy 128.085803 89.050871) (xy 128.010034 89.012265) (xy 127.958422 88.963519) (xy 127.941356 88.894604)
				(xy 127.964257 88.827402) (xy 128.010034 88.787735) (xy 128.085803 88.749129) (xy 128.25243 88.628068)
				(xy 128.398068 88.48243) (xy 128.519129 88.315803) (xy 128.612634 88.13229) (xy 128.67628 87.936408)
				(xy 128.7085 87.732981) (xy 128.7085 87.527019) (xy 128.67628 87.323592) (xy 128.612634 87.12771)
				(xy 128.519129 86.944197) (xy 128.398068 86.77757) (xy 128.398065 86.777567) (xy 128.398063 86.777564)
				(xy 128.252435 86.631936) (xy 128.252432 86.631934) (xy 128.25243 86.631932) (xy 128.126999 86.540802)
				(xy 128.085806 86.510873) (xy 128.085805 86.510872) (xy 128.085803 86.510871) (xy 128.009486 86.471985)
				(xy 127.957873 86.423239) (xy 127.940807 86.354324) (xy 127.963708 86.287122) (xy 128.009488 86.247453)
				(xy 128.085545 86.208699) (xy 128.252107 86.087684) (xy 128.25211 86.087682) (xy 128.397682 85.94211)
				(xy 128.397684 85.942107) (xy 128.518699 85.775545) (xy 128.61217 85.592099) (xy 128.612173 85.592093)
				(xy 128.675791 85.396295) (xy 128.684074 85.344) (xy 127.311686 85.344) (xy 127.32008 85.335606)
				(xy 127.372741 85.244394) (xy 127.4 85.142661) (xy 127.4 85.037339) (xy 127.372741 84.935606) (xy 127.32008 84.844394)
				(xy 127.311686 84.836) (xy 128.684074 84.836) (xy 128.675791 84.783704) (xy 128.612173 84.587906)
				(xy 128.61217 84.5879) (xy 128.518699 84.404454) (xy 128.397684 84.237892) (xy 128.397682 84.237889)
				(xy 128.25211 84.092317) (xy 128.252102 84.09231) (xy 128.235934 84.080563) (xy 128.192581 84.02434)
				(xy 128.186507 83.953604) (xy 128.21964 83.890813) (xy 128.281461 83.855903) (xy 128.296523 83.853351)
				(xy 128.309201 83.851989) (xy 128.446204 83.800889) (xy 128.446799 83.800444) (xy 128.563261 83.713261)
				(xy 128.650887 83.596207) (xy 128.650887 83.596206) (xy 128.650889 83.596204) (xy 128.701989 83.459201)
				(xy 128.705487 83.426669) (xy 128.708499 83.398649) (xy 128.7085 83.398632) (xy 128.7085 81.701367)
				(xy 128.708499 81.70135) (xy 128.70199 81.640803) (xy 128.700177 81.633132) (xy 128.701488 81.632822)
				(xy 128.697012 81.570214) (xy 128.731039 81.507903) (xy 128.793352 81.473879) (xy 128.820132 81.471)
				(xy 129.14567 81.471)
			)
		)
		(filled_polygon
			(layer "B.Cu")
			(pts
				(xy 148.844 87.318314) (xy 148.835606 87.30992) (xy 148.744394 87.257259) (xy 148.642661 87.23)
				(xy 148.537339 87.23) (xy 148.435606 87.257259) (xy 148.344394 87.30992) (xy 148.336 87.318314)
				(xy 148.336 85.401686) (xy 148.344394 85.41008) (xy 148.435606 85.462741) (xy 148.537339 85.49)
				(xy 148.642661 85.49) (xy 148.744394 85.462741) (xy 148.835606 85.41008) (xy 148.844 85.401686)
			)
		)
		(filled_polygon
			(layer "B.Cu")
			(pts
				(xy 111.471649 67.340502) (xy 111.518142 67.394158) (xy 111.529528 67.4465) (xy 111.529528 70.444834)
				(xy 127.088077 70.444834) (xy 127.088077 67.4465) (xy 127.108079 67.378379) (xy 127.161735 67.331886)
				(xy 127.214077 67.3205) (xy 182.754 67.3205) (xy 182.822121 67.340502) (xy 182.868614 67.394158)
				(xy 182.88 67.4465) (xy 182.88 83.021366) (xy 182.877408 83.030191) (xy 182.878718 83.039298) (xy 182.867562 83.063723)
				(xy 182.859998 83.089487) (xy 182.853044 83.095511) (xy 182.849224 83.103878) (xy 182.826638 83.118392)
				(xy 182.806342 83.13598) (xy 182.795655 83.138304) (xy 182.789498 83.142262) (xy 182.754 83.147366)
				(xy 182.7525 83.147366) (xy 182.684379 83.127364) (xy 182.637886 83.073708) (xy 182.6265 83.021366)
				(xy 182.6265 79.909888) (xy 182.626499 79.90988) (xy 182.61984 79.876403) (xy 182.611046 79.832193)
				(xy 182.587436 79.713498) (xy 182.51081 79.528507) (xy 182.399567 79.362018) (xy 182.399566 79.362017)
				(xy 182.399563 79.362013) (xy 180.860988 77.823439) (xy 180.860982 77.823434) (xy 180.860981 77.823433)
				(xy 180.694493 77.71219) (xy 180.523915 77.641534) (xy 180.509505 77.635565) (xy 180.509502 77.635564)
				(xy 180.313119 77.5965) (xy 180.313116 77.5965) (xy 171.151051 77.5965) (xy 170.950818 77.5965)
				(xy 170.950814 77.5965) (xy 170.754432 77.635564) (xy 170.754429 77.635565) (xy 170.569443 77.712189)
				(xy 170.402952 77.823433) (xy 170.402948 77.823436) (xy 169.752067 78.474318) (xy 169.689755 78.508344)
				(xy 169.61894 78.503279) (xy 169.573877 78.474318) (xy 169.049998 77.950439) (xy 169.049992 77.950434)
				(xy 169.025563 77.934111) (xy 168.883503 77.83919) (xy 168.766849 77.79087) (xy 168.698515 77.762565)
				(xy 168.698512 77.762564) (xy 168.502129 77.7235) (xy 168.502126 77.7235) (xy 161.50675 77.7235)
				(xy 161.438629 77.703498) (xy 161.392136 77.649842) (xy 161.388695 77.641534) (xy 161.376995 77.610166)
				(xy 161.370889 77.593796) (xy 161.370888 77.593794) (xy 161.283261 77.476738) (xy 161.166207 77.389112)
				(xy 161.166202 77.38911) (xy 161.029204 77.338011) (xy 161.029196 77.338009) (xy 160.968649 77.3315)
				(xy 160.968638 77.3315) (xy 159.071362 77.3315) (xy 159.07135 77.3315) (xy 159.010803 77.338009)
				(xy 159.010795 77.338011) (xy 158.873797 77.38911) (xy 158.873792 77.389112) (xy 158.756738 77.476738)
				(xy 158.669112 77.593792) (xy 158.66911 77.593797) (xy 158.618011 77.730795) (xy 158.618009 77.730803)
				(xy 158.6115 77.79135) (xy 158.6115 79.688649) (xy 158.618009 79.749196) (xy 158.618011 79.749204)
				(xy 158.66911 79.886202) (xy 158.669112 79.886207) (xy 158.756738 80.003261) (xy 158.873792 80.090887)
				(xy 158.873794 80.090888) (xy 158.873796 80.090889) (xy 158.932875 80.112924) (xy 159.010795 80.141988)
				(xy 159.010803 80.14199) (xy 159.07135 80.148499) (xy 159.071355 80.148499) (xy 159.071362 80.1485)
				(xy 159.071368 80.1485) (xy 160.968632 80.1485) (xy 160.968638 80.1485) (xy 160.968645 80.148499)
				(xy 160.968649 80.148499) (xy 161.029196 80.14199) (xy 161.029199 80.141989) (xy 161.029201 80.141989)
				(xy 161.031242 80.141228) (xy 161.080471 80.122866) (xy 161.166204 80.090889) (xy 161.242095 80.034078)
				(xy 161.283261 80.003261) (xy 161.370889 79.886204) (xy 161.388695 79.838466) (xy 161.431242 79.781631)
				(xy 161.497762 79.756821) (xy 161.50675 79.7565) (xy 167.928772 79.7565) (xy 167.996893 79.776502)
				(xy 168.017867 79.793405) (xy 168.276595 80.052133) (xy 168.310621 80.114445) (xy 168.3135 80.141228)
				(xy 168.3135 81.035649) (xy 168.320009 81.096196) (xy 168.320011 81.096204) (xy 168.37111 81.233202)
				(xy 168.371112 81.233207) (xy 168.458738 81.350261) (xy 168.575792 81.437887) (xy 168.575796 81.437889)
				(xy 168.694224 81.482061) (xy 168.751059 81.524607) (xy 168.77587 81.591128) (xy 168.760778 81.660502)
				(xy 168.739287 81.689211) (xy 168.636176 81.792322) (xy 168.510536 81.965249) (xy 168.413493 82.155707)
				(xy 168.41349 82.155713) (xy 168.34744 82.358995) (xy 168.337302 82.423) (xy 169.241297 82.423)
				(xy 169.206075 82.484007) (xy 169.172 82.611174) (xy 169.172 82.742826) (xy 169.206075 82.869993)
				(xy 169.241297 82.931) (xy 168.337302 82.931) (xy 168.34744 82.995004) (xy 168.41349 83.198286)
				(xy 168.413493 83.198292) (xy 168.510536 83.38875) (xy 168.636177 83.561679) (xy 168.78732 83.712822)
				(xy 168.960244 83.83846) (xy 168.96156 83.839266) (xy 168.961955 83.839703) (xy 168.964258 83.841376)
				(xy 168.963906 83.841859) (xy 169.009196 83.89191) (xy 169.020809 83.96195) (xy 168.992712 84.02715)
				(xy 168.963925 84.052098) (xy 168.963996 84.052196) (xy 168.962862 84.053019) (xy 168.96158 84.054131)
				(xy 168.959998 84.0551) (xy 168.786993 84.180796) (xy 168.78699 84.180798) (xy 168.635798 84.33199)
				(xy 168.635793 84.331996) (xy 168.63066 84.339062) (xy 168.574438 84.382415) (xy 168.528725 84.391)
				(xy 165.568586 84.391) (xy 165.568566 84.390999) (xy 165.562353 84.390999) (xy 165.399647 84.390999)
				(xy 165.399644 84.390999) (xy 165.240064 84.422742) (xy 165.240061 84.422743) (xy 165.089742 84.485008)
				(xy 164.954459 84.575401) (xy 164.954452 84.575406) (xy 161.918406 87.611452) (xy 161.918401 87.611459)
				(xy 161.828007 87.746742) (xy 161.828006 87.746744) (xy 161.801498 87.810739) (xy 161.759153 87.864454)
				(xy 161.707564 87.901936) (xy 161.561936 88.047564) (xy 161.561934 88.047567) (xy 161.440873 88.214193)
				(xy 161.347367 88.397708) (xy 161.347364 88.397714) (xy 161.283721 88.593587) (xy 161.28372 88.59359)
				(xy 161.28372 88.593592) (xy 161.2515 88.797019) (xy 161.2515 89.002981) (xy 161.283376 89.204234)
				(xy 161.283721 89.206412) (xy 161.331418 89.353209) (xy 161.347366 89.40229) (xy 161.440871 89.585803)
				(xy 161.561932 89.75243) (xy 161.561934 89.752432) (xy 161.561936 89.752435) (xy 161.707564 89.898063)
				(xy 161.707567 89.898065) (xy 161.70757 89.898068) (xy 161.874197 90.019129) (xy 162.05771 90.112634)
				(xy 162.253592 90.17628) (xy 162.457019 90.2085) (xy 162.457022 90.2085) (xy 162.662978 90.2085)
				(xy 162.662981 90.2085) (xy 162.866408 90.17628) (xy 163.06229 90.112634) (xy 163.245803 90.019129)
				(xy 163.41243 89.898068) (xy 163.558068 89.75243) (xy 163.679129 89.585803) (xy 163.772634 89.40229)
				(xy 163.83628 89.206408) (xy 163.8685 89.002981) (xy 163.8685 88.797019) (xy 163.83628 88.593592)
				(xy 163.772634 88.39771) (xy 163.711511 88.277751) (xy 163.698408 88.207975) (xy 163.725108 88.14219)
				(xy 163.734675 88.131463) (xy 165.786235 86.079905) (xy 165.848547 86.045879) (xy 165.87533 86.043)
				(xy 168.528725 86.043) (xy 168.596846 86.063002) (xy 168.63066 86.094938) (xy 168.635793 86.102003)
				(xy 168.635798 86.102009) (xy 168.78699 86.253201) (xy 168.786993 86.253203) (xy 168.786996 86.253206)
				(xy 168.959991 86.378894) (xy 168.959996 86.378896) (xy 168.961088 86.379566) (xy 168.961418 86.379931)
				(xy 168.963996 86.381804) (xy 168.963602 86.382345) (xy 169.008721 86.432213) (xy 169.020328 86.502255)
				(xy 168.992226 86.567453) (xy 168.96387 86.592023) (xy 168.963996 86.592196) (xy 168.961982 86.593658)
				(xy 168.961088 86.594434) (xy 168.959987 86.595108) (xy 168.786993 86.720796) (xy 168.78699 86.720798)
				(xy 168.635798 86.87199) (xy 168.635793 86.871996) (xy 168.63066 86.879062) (xy 168.574438 86.922415)
				(xy 168.528725 86.931) (xy 167.43164 86.931) (xy 167.294862 86.958207) (xy 167.272065 86.962742)
				(xy 167.272062 86.962742) (xy 167.272059 86.962744) (xy 167.12174 87.025008) (xy 167.10185 87.038298)
				(xy 167.091835 87.044991) (xy 166.986455 87.115403) (xy 166.941601 87.160258) (xy 166.871404 87.230455)
				(xy 165.601406 88.500452) (xy 165.601401 88.500459) (xy 165.511008 88.635742) (xy 165.448744 88.786059)
				(xy 165.448742 88.786066) (xy 165.417 88.94564) (xy 165.417 91.045668) (xy 165.396998 91.113789)
				(xy 165.380095 91.134763) (xy 163.558314 92.956543) (xy 163.496002 92.990569) (xy 163.425186 92.985504)
				(xy 163.395162 92.969386) (xy 163.245803 92.860871) (xy 163.06229 92.767366) (xy 163.062287 92.767365)
				(xy 163.062285 92.767364) (xy 162.866412 92.703721) (xy 162.86641 92.70372) (xy 162.866408 92.70372)
				(xy 162.662981 92.6715) (xy 162.457019 92.6715) (xy 162.253592 92.70372) (xy 162.25359 92.70372)
				(xy 162.253587 92.703721) (xy 162.057714 92.767364) (xy 162.057708 92.767367) (xy 161.874193 92.860873)
				(xy 161.707567 92.981934) (xy 161.707564 92.981936) (xy 161.561936 93.127564) (xy 161.561934 93.127567)
				(xy 161.440873 93.294193) (xy 161.347367 93.477708) (xy 161.347364 93.477714) (xy 161.283721 93.673587)
				(xy 161.28372 93.67359) (xy 161.28372 93.673592) (xy 161.2515 93.877019) (xy 161.2515 94.082981)
				(xy 161.28372 94.286408) (xy 161.283721 94.286412) (xy 161.319838 94.39757) (xy 161.347366 94.48229)
				(xy 161.440871 94.665803) (xy 161.561932 94.83243) (xy 161.561934 94.832432) (xy 161.561936 94.832435)
				(xy 161.707564 94.978063) (xy 161.707567 94.978065) (xy 161.70757 94.978068) (xy 161.874197 95.099129)
				(xy 162.05771 95.192634) (xy 162.253592 95.25628) (xy 162.457019 95.2885) (xy 162.457022 95.2885)
				(xy 162.662978 95.2885) (xy 162.662981 95.2885) (xy 162.866408 95.25628) (xy 163.06229 95.192634)
				(xy 163.245803 95.099129) (xy 163.41243 94.978068) (xy 163.471003 94.919495) (xy 163.547594 94.842905)
				(xy 163.609906 94.808879) (xy 163.636689 94.806) (xy 163.784353 94.806) (xy 163.784354 94.806) (xy 163.943935 94.774258)
				(xy 164.094258 94.711992) (xy 164.229545 94.621596) (xy 164.344596 94.506545) (xy 164.344596 94.506543)
				(xy 164.354966 94.496174) (xy 164.354969 94.496169) (xy 165.963906 92.887232) (xy 166.026217 92.853208)
				(xy 166.097033 92.858273) (xy 166.153868 92.90082) (xy 166.178679 92.96734) (xy 166.179 92.976329)
				(xy 166.179 94.220669) (xy 166.158998 94.28879) (xy 166.142095 94.309764) (xy 162.737264 97.714595)
				(xy 162.674952 97.748621) (xy 162.648169 97.7515) (xy 162.457019 97.7515) (xy 162.253592 97.78372)
				(xy 162.25359 97.78372) (xy 162.253587 97.783721) (xy 162.057714 97.847364) (xy 162.057708 97.847367)
				(xy 161.874193 97.940873) (xy 161.707567 98.061934) (xy 161.707564 98.061936) (xy 161.561936 98.207564)
				(xy 161.561934 98.207567) (xy 161.440873 98.374193) (xy 161.347367 98.557708) (xy 161.347364 98.557714)
				(xy 161.283721 98.753587) (xy 161.28372 98.75359) (xy 161.28372 98.753592) (xy 161.2515 98.957019)
				(xy 161.2515 99.162981) (xy 161.273795 99.303742) (xy 161.283721 99.366412) (xy 161.312201 99.454065)
				(xy 161.347366 99.56229) (xy 161.440871 99.745803) (xy 161.561932 99.91243) (xy 161.561934 99.912432)
				(xy 161.561936 99.912435) (xy 161.707564 100.058063) (xy 161.707567 100.058065) (xy 161.70757 100.058068)
				(xy 161.874197 100.179129) (xy 162.05771 100.272634) (xy 162.253592 100.33628) (xy 162.457019 100.3685)
				(xy 162.457022 100.3685) (xy 162.662978 100.3685) (xy 162.662981 100.3685) (xy 162.866408 100.33628)
				(xy 163.06229 100.272634) (xy 163.245803 100.179129) (xy 163.41243 100.058068) (xy 163.558068 99.91243)
				(xy 163.679129 99.745803) (xy 163.772634 99.56229) (xy 163.83628 99.366408) (xy 163.8685 99.162981)
				(xy 163.8685 98.971829) (xy 163.888502 98.903708) (xy 163.905405 98.882734) (xy 165.705461 97.082678)
				(xy 167.524359 95.263779) (xy 167.524367 95.263773) (xy 167.531542 95.256597) (xy 167.531545 95.256596)
				(xy 167.646596 95.141545) (xy 167.703603 95.056228) (xy 167.736992 95.006259) (xy 167.798128 94.858662)
				(xy 167.799258 94.855935) (xy 167.803933 94.832435) (xy 167.830999 94.696359) (xy 167.831 94.696352)
				(xy 167.831 92.723329) (xy 167.851002 92.655208) (xy 167.867895 92.634244) (xy 168.150895 92.351244)
				(xy 168.213202 92.317223) (xy 168.284017 92.322287) (xy 168.340853 92.364834) (xy 168.365664 92.431354)
				(xy 168.359819 92.479276) (xy 168.346952 92.518877) (xy 168.346951 92.518884) (xy 168.3135 92.730084)
				(xy 168.3135 92.943916) (xy 168.342587 93.127564) (xy 168.346952 93.155121) (xy 168.41079 93.351596)
				(xy 168.413028 93.358483) (xy 168.510106 93.549009) (xy 168.510108 93.549012) (xy 168.527467 93.572905)
				(xy 168.635794 93.722004) (xy 168.635796 93.722006) (xy 168.635798 93.722009) (xy 168.786984 93.873195)
				(xy 168.786991 93.873201) (xy 168.786996 93.873206) (xy 168.794055 93.878334) (xy 168.837412 93.934553)
				(xy 168.846 93.980274) (xy 168.846 96.125669) (xy 168.825998 96.19379) (xy 168.809095 96.214764)
				(xy 161.918406 103.105452) (xy 161.918401 103.105459) (xy 161.828008 103.240742) (xy 161.765744 103.391059)
				(xy 161.765742 103.391066) (xy 161.734 103.55064) (xy 161.734 104.333311) (xy 161.713998 104.401432)
				(xy 161.697095 104.422406) (xy 161.561936 104.557564) (xy 161.561934 104.557567) (xy 161.440873 104.724193)
				(xy 161.347367 104.907708) (xy 161.347364 104.907714) (xy 161.283721 105.103587) (xy 161.28372 105.10359)
				(xy 161.28372 105.103592) (xy 161.2515 105.307019) (xy 161.2515 105.512981) (xy 161.28372 105.716408)
				(xy 161.283721 105.716412) (xy 161.34387 105.901533) (xy 161.347366 105.91229) (xy 161.440871 106.095803)
				(xy 161.561932 106.26243) (xy 161.561934 106.262432) (xy 161.561936 106.262435) (xy 161.707564 106.408063)
				(xy 161.707567 106.408065) (xy 161.70757 106.408068) (xy 161.874197 106.529129) (xy 162.05771 106.622634)
				(xy 162.253592 106.68628) (xy 162.457019 106.7185) (xy 162.457022 106.7185) (xy 162.662978 106.7185)
				(xy 162.662981 106.7185) (xy 162.866408 106.68628) (xy 163.06229 106.622634) (xy 163.245803 106.529129)
				(xy 163.41243 106.408068) (xy 163.558068 106.26243) (xy 163.679129 106.095803) (xy 163.772634 105.91229)
				(xy 163.83628 105.716408) (xy 163.8685 105.512981) (xy 163.8685 105.307019) (xy 163.83628 105.103592)
				(xy 163.772634 104.90771) (xy 163.679129 104.724197) (xy 163.558068 104.55757) (xy 163.558065 104.557567)
				(xy 163.558063 104.557564) (xy 163.422905 104.422406) (xy 163.388879 104.360094) (xy 163.386 104.333311)
				(xy 163.386 104.026329) (xy 163.406002 103.958208) (xy 163.4229 103.937239) (xy 170.191359 97.168779)
				(xy 170.191367 97.168773) (xy 170.198542 97.161597) (xy 170.198545 97.161596) (xy 170.313596 97.046545)
				(xy 170.403992 96.911258) (xy 170.466258 96.760935) (xy 170.472153 96.7313) (xy 170.498001 96.601353)
				(xy 170.498001 96.438647) (xy 170.498001 96.433455) (xy 170.498 96.433429) (xy 170.498 95.13533)
				(xy 170.518002 95.067209) (xy 170.571658 95.020716) (xy 170.641932 95.010612) (xy 170.706512 95.040106)
				(xy 170.713095 95.046235) (xy 172.075228 96.408368) (xy 172.075242 96.408383) (xy 172.078403 96.411544)
				(xy 172.078404 96.411545) (xy 172.193455 96.526596) (xy 172.328742 96.616992) (xy 172.479065 96.679258)
				(xy 172.606335 96.704573) (xy 172.63864 96.710999) (xy 172.638643 96.710999) (xy 172.638646 96.711)
				(xy 172.638647 96.711) (xy 172.801353 96.711) (xy 179.287714 96.711) (xy 179.355835 96.731002) (xy 179.402328 96.784658)
				(xy 179.405761 96.792945) (xy 179.419111 96.828738) (xy 179.419112 96.828739) (xy 179.419113 96.828742)
				(xy 179.506738 96.945795) (xy 179.623792 97.033421) (xy 179.623794 97.033422) (xy 179.623796 97.033423)
				(xy 179.658964 97.04654) (xy 179.760795 97.084522) (xy 179.760803 97.084524) (xy 179.82135 97.091033)
				(xy 179.821355 97.091033) (xy 179.821362 97.091034) (xy 179.821368 97.091034) (xy 182.754 97.091034)
				(xy 182.822121 97.111036) (xy 182.868614 97.164692) (xy 182.88 97.217034) (xy 182.88 110.3535) (xy 182.859998 110.421621)
				(xy 182.806342 110.468114) (xy 182.754 110.4795) (xy 105.536 110.4795) (xy 105.467879 110.459498)
				(xy 105.421386 110.405842) (xy 105.41 110.3535) (xy 105.41 105.901533) (xy 105.41 105.769534) (xy 105.7656 105.769534)
				(xy 107.300185 105.769534) (xy 107.300197 105.769533) (xy 107.360693 105.763028) (xy 107.497564 105.711978)
				(xy 107.497565 105.711978) (xy 107.614504 105.624438) (xy 107.702044 105.507499) (xy 107.702044 105.507498)
				(xy 107.753094 105.370627) (xy 107.759599 105.310131) (xy 107.7596 105.310119) (xy 107.7596 104.5922)
				(xy 105.7656 104.5922) (xy 105.7656 105.769534) (xy 105.41 105.769534) (xy 105.41 104.3382) (xy 105.5116 104.3382)
				(xy 105.5116 104.2102) (xy 105.531602 104.142079) (xy 105.585258 104.095586) (xy 105.6376 104.0842)
				(xy 107.7596 104.0842) (xy 107.7596 104.037019) (xy 110.4515 104.037019) (xy 110.4515 104.242981)
				(xy 110.48372 104.446408) (xy 110.483721 104.446412) (xy 110.53109 104.5922) (xy 110.547366 104.64229)
				(xy 110.640871 104.825803) (xy 110.761932 104.99243) (xy 110.761934 104.992432) (xy 110.761936 104.992435)
				(xy 110.907564 105.138063) (xy 110.907567 105.138065) (xy 110.90757 105.138068) (xy 111.074197 105.259129)
				(xy 111.25771 105.352634) (xy 111.453592 105.41628) (xy 111.657019 105.4485) (xy 111.657022 105.4485)
				(xy 111.862978 105.4485) (xy 111.862981 105.4485) (xy 112.066408 105.41628) (xy 112.26229 105.352634)
				(xy 112.445803 105.259129) (xy 112.61243 105.138068) (xy 112.758068 104.99243) (xy 112.879129 104.825803)
				(xy 112.972634 104.64229) (xy 113.03628 104.446408) (xy 113.0685 104.242981) (xy 113.0685 104.037019)
				(xy 120.6115 104.037019) (xy 120.6115 104.242981) (xy 120.64372 104.446408) (xy 120.643721 104.446412)
				(xy 120.69109 104.5922) (xy 120.707366 104.64229) (xy 120.800871 104.825803) (xy 120.921932 104.99243)
				(xy 120.921934 104.992432) (xy 120.921936 104.992435) (xy 121.067564 105.138063) (xy 121.067567 105.138065)
				(xy 121.06757 105.138068) (xy 121.234197 105.259129) (xy 121.41771 105.352634) (xy 121.613592 105.41628)
				(xy 121.817019 105.4485) (xy 121.817022 105.4485) (xy 122.022978 105.4485) (xy 122.022981 105.4485)
				(xy 122.226408 105.41628) (xy 122.42229 105.352634) (xy 122.605803 105.259129) (xy 122.77243 105.138068)
				(xy 122.918068 104.99243) (xy 123.039129 104.825803) (xy 123.132634 104.64229) (xy 123.19628 104.446408)
				(xy 123.2285 104.242981) (xy 123.2285 104.037019) (xy 123.19628 103.833592) (xy 123.132634 103.63771)
				(xy 123.039129 103.454197) (xy 122.918068 103.28757) (xy 122.918065 103.287567) (xy 122.918063 103.287564)
				(xy 122.772435 103.141936) (xy 122.772432 103.141934) (xy 122.77243 103.141932) (xy 122.605803 103.020871)
				(xy 122.42229 102.927366) (xy 122.422287 102.927365) (xy 122.422285 102.927364) (xy 122.226412 102.863721)
				(xy 122.22641 102.86372) (xy 122.226408 102.86372) (xy 122.022981 102.8315) (xy 121.817019 102.8315)
				(xy 121.613592 102.86372) (xy 121.61359 102.86372) (xy 121.613587 102.863721) (xy 121.417714 102.927364)
				(xy 121.417708 102.927367) (xy 121.234193 103.020873) (xy 121.067567 103.141934) (xy 121.067564 103.141936)
				(xy 120.921936 103.287564) (xy 120.921934 103.287567) (xy 120.800873 103.454193) (xy 120.707367 103.637708)
				(xy 120.707364 103.637714) (xy 120.643721 103.833587) (xy 120.64372 103.83359) (xy 120.64372 103.833592)
				(xy 120.6115 104.037019) (xy 113.0685 104.037019) (xy 113.03628 103.833592) (xy 112.972634 103.63771)
				(xy 112.879129 103.454197) (xy 112.758068 103.28757) (xy 112.758065 103.287567) (xy 112.758063 103.287564)
				(xy 112.612435 103.141936) (xy 112.612432 103.141934) (xy 112.61243 103.141932) (xy 112.445803 103.020871)
				(xy 112.26229 102.927366) (xy 112.262287 102.927365) (xy 112.262285 102.927364) (xy 112.066412 102.863721)
				(xy 112.06641 102.86372) (xy 112.066408 102.86372) (xy 111.862981 102.8315) (xy 111.657019 102.8315)
				(xy 111.453592 102.86372) (xy 111.45359 102.86372) (xy 111.453587 102.863721) (xy 111.257714 102.927364)
				(xy 111.257708 102.927367) (xy 111.074193 103.020873) (xy 110.907567 103.141934) (xy 110.907564 103.141936)
				(xy 110.761936 103.287564) (xy 110.761934 103.287567) (xy 110.640873 103.454193) (xy 110.547367 103.637708)
				(xy 110.547364 103.637714) (xy 110.483721 103.833587) (xy 110.48372 103.83359) (xy 110.48372 103.833592)
				(xy 110.4515 104.037019) (xy 107.7596 104.037019) (xy 107.7596 103.36628) (xy 107.759599 103.366268)
				(xy 107.753094 103.305772) (xy 107.702044 103.168901) (xy 107.702044 103.1689) (xy 107.614502 103.051959)
				(xy 107.608132 103.045589) (xy 107.60986 103.04386) (xy 107.57519 102.997552) (xy 107.570121 102.926736)
				(xy 107.604142 102.864422) (xy 107.612328 102.857328) (xy 107.614859 102.854797) (xy 107.614859 102.854795)
				(xy 107.614861 102.854795) (xy 107.680567 102.767022) (xy 107.702487 102.737741) (xy 107.702487 102.73774)
				(xy 107.702489 102.737738) (xy 107.753589 102.600735) (xy 107.755333 102.584519) (xy 107.760099 102.540183)
				(xy 107.7601 102.540166) (xy 107.7601 100.596233) (xy 107.760099 100.596216) (xy 107.75359 100.535669)
				(xy 107.753588 100.535661) (xy 107.707223 100.411355) (xy 107.702489 100.398662) (xy 107.702488 100.39866)
				(xy 107.702487 100.398658) (xy 107.614859 100.281602) (xy 107.608489 100.275232) (xy 107.610233 100.273487)
				(xy 107.575608 100.227242) (xy 107.570537 100.156426) (xy 107.604558 100.094111) (xy 107.611957 100.087699)
				(xy 107.614859 100.084797) (xy 107.614859 100.084795) (xy 107.614861 100.084795) (xy 107.702489 99.967738)
				(xy 107.753589 99.830735) (xy 107.7601 99.770172) (xy 107.7601 97.826228) (xy 107.760099 97.826216)
				(xy 107.75359 97.765669) (xy 107.753588 97.765661) (xy 107.720429 97.67676) (xy 107.702489 97.628662)
				(xy 107.702488 97.62866) (xy 107.702487 97.628658) (xy 107.614859 97.511602) (xy 107.608489 97.505232)
				(xy 107.610233 97.503487) (xy 107.575608 97.457242) (xy 107.570537 97.386426) (xy 107.604558 97.324111)
				(xy 107.611957 97.317699) (xy 107.614859 97.314797) (xy 107.614859 97.314795) (xy 107.614861 97.314795)
				(xy 107.688044 97.217034) (xy 107.702487 97.197741) (xy 107.702487 97.19774) (xy 107.702489 97.197738)
				(xy 107.753589 97.060735) (xy 107.756526 97.033423) (xy 107.760099 97.000183) (xy 107.7601 97.000166)
				(xy 107.7601 95.056228) (xy 107.760099 95.056216) (xy 107.75359 94.995669) (xy 107.753588 94.995661)
				(xy 107.702489 94.858662) (xy 107.702487 94.858658) (xy 107.614859 94.741602) (xy 107.608489 94.735232)
				(xy 107.610233 94.733487) (xy 107.575608 94.687242) (xy 107.570537 94.616426) (xy 107.604558 94.554111)
				(xy 107.611957 94.547699) (xy 107.614859 94.544797) (xy 107.614859 94.544795) (xy 107.614861 94.544795)
				(xy 107.702489 94.427738) (xy 107.753589 94.290735) (xy 107.753799 94.28879) (xy 107.7601 94.230173)
				(xy 107.7601 92.607019) (xy 110.4515 92.607019) (xy 110.4515 92.812981) (xy 110.483702 93.016295)
				(xy 110.483721 93.016412) (xy 110.534805 93.173633) (xy 110.547366 93.21229) (xy 110.640871 93.395803)
				(xy 110.761932 93.56243) (xy 110.761934 93.562432) (xy 110.761936 93.562435) (xy 110.907564 93.708063)
				(xy 110.907567 93.708065) (xy 110.90757 93.708068) (xy 111.074197 93.829129) (xy 111.25771 93.922634)
				(xy 111.453592 93.98628) (xy 111.657019 94.0185) (xy 111.657022 94.0185) (xy 111.862978 94.0185)
				(xy 111.862981 94.0185) (xy 112.066408 93.98628) (xy 112.26229 93.922634) (xy 112.445803 93.829129)
				(xy 112.61243 93.708068) (xy 112.758068 93.56243) (xy 112.879129 93.395803) (xy 112.972634 93.21229)
				(xy 113.03628 93.016408) (xy 113.0685 92.812981) (xy 113.0685 92.607019) (xy 113.03628 92.403592)
				(xy 112.972634 92.20771) (xy 112.879129 92.024197) (xy 112.758068 91.85757) (xy 112.758065 91.857567)
				(xy 112.758063 91.857564) (xy 112.612435 91.711936) (xy 112.612432 91.711934) (xy 112.61243 91.711932)
				(xy 112.461954 91.602605) (xy 112.445806 91.590873) (xy 112.445805 91.590872) (xy 112.445803 91.590871)
				(xy 112.26229 91.497366) (xy 112.262287 91.497365) (xy 112.262285 91.497364) (xy 112.066412 91.433721)
				(xy 112.06641 91.43372) (xy 112.066408 91.43372) (xy 111.862981 91.4015) (xy 111.657019 91.4015)
				(xy 111.453592 91.43372) (xy 111.45359 91.43372) (xy 111.453587 91.433721) (xy 111.257714 91.497364)
				(xy 111.257708 91.497367) (xy 111.074193 91.590873) (xy 110.907567 91.711934) (xy 110.907564 91.711936)
				(xy 110.761936 91.857564) (xy 110.761934 91.857567) (xy 110.640873 92.024193) (xy 110.547367 92.207708)
				(xy 110.547364 92.207714) (xy 110.483721 92.403587) (xy 110.48372 92.40359) (xy 110.48372 92.403592)
				(xy 110.4515 92.607019) (xy 107.7601 92.607019) (xy 107.7601 92.286233) (xy 107.760099 92.286216)
				(xy 107.75359 92.225669) (xy 107.753588 92.225661) (xy 107.702489 92.088663) (xy 107.702487 92.088658)
				(xy 107.614859 91.971602) (xy 107.608489 91.965232) (xy 107.610126 91.963594) (xy 107.575185 91.916913)
				(xy 107.570124 91.846097) (xy 107.604152 91.783787) (xy 107.61166 91.777282) (xy 107.614502 91.77444)
				(xy 107.702044 91.657499) (xy 107.702044 91.657498) (xy 107.753094 91.520627) (xy 107.759599 91.460131)
				(xy 107.7596 91.460119) (xy 107.7596 90.7422) (xy 105.6376 90.7422) (xy 105.569479 90.722198) (xy 105.522986 90.668542)
				(xy 105.5116 90.6162) (xy 105.5116 90.3602) (xy 105.531602 90.292079) (xy 105.585258 90.245586)
				(xy 105.6376 90.2342) (xy 107.7596 90.2342) (xy 107.7596 89.51628) (xy 107.759599 89.516268) (xy 107.753094 89.455772)
				(xy 107.702044 89.318901) (xy 107.702044 89.3189) (xy 107.614502 89.201959) (xy 107.608132 89.195589)
				(xy 107.60986 89.19386) (xy 107.57519 89.147552) (xy 107.570121 89.076736) (xy 107.604142 89.014422)
				(xy 107.612328 89.007328) (xy 107.614859 89.004797) (xy 107.614859 89.004795) (xy 107.614861 89.004795)
				(xy 107.702489 88.887738) (xy 107.753589 88.750735) (xy 107.756526 88.723423) (xy 107.7601 88.690173)
				(xy 107.7601 87.527019) (xy 110.4515 87.527019) (xy 110.4515 87.732981) (xy 110.483702 87.936295)
				(xy 110.483721 87.936412) (xy 110.53239 88.086201) (xy 110.547366 88.13229) (xy 110.640871 88.315803)
				(xy 110.761932 88.48243) (xy 110.761934 88.482432) (xy 110.761936 88.482435) (xy 110.907564 88.628063)
				(xy 110.907567 88.628065) (xy 110.90757 88.628068) (xy 111.074197 88.749129) (xy 111.25771 88.842634)
				(xy 111.453592 88.90628) (xy 111.657019 88.9385) (xy 111.657022 88.9385) (xy 111.862978 88.9385)
				(xy 111.862981 88.9385) (xy 112.066408 88.90628) (xy 112.26229 88.842634) (xy 112.445803 88.749129)
				(xy 112.61243 88.628068) (xy 112.758068 88.48243) (xy 112.879129 88.315803) (xy 112.972634 88.13229)
				(xy 113.03628 87.936408) (xy 113.0685 87.732981) (xy 113.0685 87.527019) (xy 113.03628 87.323592)
				(xy 112.972634 87.12771) (xy 112.879129 86.944197) (xy 112.758068 86.77757) (xy 112.758065 86.777567)
				(xy 112.758063 86.777564) (xy 112.612435 86.631936) (xy 112.612432 86.631934) (xy 112.61243 86.631932)
				(xy 112.486999 86.540802) (xy 112.445806 86.510873) (xy 112.445805 86.510872) (xy 112.445803 86.510871)
				(xy 112.26229 86.417366) (xy 112.262287 86.417365) (xy 112.262285 86.417364) (xy 112.066412 86.353721)
				(xy 112.06641 86.35372) (xy 112.066408 86.35372) (xy 111.862981 86.3215) (xy 111.657019 86.3215)
				(xy 111.453592 86.35372) (xy 111.45359 86.35372) (xy 111.453587 86.353721) (xy 111.257714 86.417364)
				(xy 111.257708 86.417367) (xy 111.074193 86.510873) (xy 110.907567 86.631934) (xy 110.907564 86.631936)
				(xy 110.761936 86.777564) (xy 110.761934 86.777567) (xy 110.640873 86.944193) (xy 110.547367 87.127708)
				(xy 110.547364 87.127714) (xy 110.483721 87.323587) (xy 110.48372 87.32359) (xy 110.48372 87.323592)
				(xy 110.4515 87.527019) (xy 107.7601 87.527019) (xy 107.7601 86.746233) (xy 107.760099 86.746216)
				(xy 107.75359 86.685669) (xy 107.753588 86.685661) (xy 107.719272 86.593658) (xy 107.702489 86.548662)
				(xy 107.702488 86.54866) (xy 107.702487 86.548658) (xy 107.614859 86.431602) (xy 107.608489 86.425232)
				(xy 107.610233 86.423487) (xy 107.575608 86.377242) (xy 107.570537 86.306426) (xy 107.604558 86.244111)
				(xy 107.611957 86.237699) (xy 107.614859 86.234797) (xy 107.614859 86.234795) (xy 107.614861 86.234795)
				(xy 107.684933 86.14119) (xy 107.702487 86.117741) (xy 107.702487 86.11774) (xy 107.702489 86.117738)
				(xy 107.753589 85.980735) (xy 107.754879 85.968741) (xy 107.7601 85.920173) (xy 107.7601 83.976233)
				(xy 107.760099 83.976216) (xy 107.75359 83.915669) (xy 107.753588 83.915661) (xy 107.710779 83.800889)
				(xy 107.702489 83.778662) (xy 107.656344 83.717019) (xy 110.4642 83.717019) (xy 110.4642 83.922981)
				(xy 110.492034 84.098716) (xy 110.496421 84.126412) (xy 110.55144 84.295744) (xy 110.560066 84.32229)
				(xy 110.653571 84.505803) (xy 110.774632 84.67243) (xy 110.774634 84.672432) (xy 110.774636 84.672435)
				(xy 110.920264 84.818063) (xy 110.920267 84.818065) (xy 110.92027 84.818068) (xy 111.086897 84.939129)
				(xy 111.27041 85.032634) (xy 111.466292 85.09628) (xy 111.669719 85.1285) (xy 111.669722 85.1285)
				(xy 111.875678 85.1285) (xy 111.875681 85.1285) (xy 112.079108 85.09628) (xy 112.27499 85.032634)
				(xy 112.458503 84.939129) (xy 112.62513 84.818068) (xy 112.770768 84.67243) (xy 112.891829 84.505803)
				(xy 112.985334 84.32229) (xy 113.04898 84.126408) (xy 113.0812 83.922981) (xy 113.0812 83.717019)
				(xy 113.04898 83.513592) (xy 112.985334 83.31771) (xy 112.891829 83.134197) (xy 112.770768 82.96757)
				(xy 112.770765 82.967567) (xy 112.770763 82.967564) (xy 112.625135 82.821936) (xy 112.625132 82.821934)
				(xy 112.62513 82.821932) (xy 112.458503 82.700871) (xy 112.27499 82.607366) (xy 112.274987 82.607365)
				(xy 112.274985 82.607364) (xy 112.079112 82.543721) (xy 112.07911 82.54372) (xy 112.079108 82.54372)
				(xy 111.875681 82.5115) (xy 111.669719 82.5115) (xy 111.466292 82.54372) (xy 111.46629 82.54372)
				(xy 111.466287 82.543721) (xy 111.270414 82.607364) (xy 111.270408 82.607367) (xy 111.086893 82.700873)
				(xy 110.920267 82.821934) (xy 110.920264 82.821936) (xy 110.774636 82.967564) (xy 110.774634 82.967567)
				(xy 110.653573 83.134193) (xy 110.560067 83.317708) (xy 110.560064 83.317714) (xy 110.496421 83.513587)
				(xy 110.49642 83.51359) (xy 110.49642 83.513592) (xy 110.4642 83.717019) (xy 107.656344 83.717019)
				(xy 107.614861 83.661605) (xy 107.604647 83.653959) (xy 107.59428 83.640657) (xy 107.587181 83.622699)
				(xy 107.575608 83.607242) (xy 107.574399 83.590365) (xy 107.56818 83.574632) (xy 107.571916 83.555688)
				(xy 107.570537 83.536426) (xy 107.578645 83.521574) (xy 107.581919 83.504977) (xy 107.595304 83.49106)
				(xy 107.604558 83.474111) (xy 107.611957 83.467699) (xy 107.614859 83.464797) (xy 107.614859 83.464795)
				(xy 107.614861 83.464795) (xy 107.702489 83.347738) (xy 107.753589 83.210735) (xy 107.754209 83.204976)
				(xy 107.760099 83.150183) (xy 107.7601 83.150166) (xy 107.7601 81.206233) (xy 107.760099 81.206216)
				(xy 107.75359 81.145669) (xy 107.753588 81.145661) (xy 107.712555 81.035649) (xy 107.702489 81.008662)
				(xy 107.702488 81.00866) (xy 107.702487 81.008658) (xy 107.614859 80.891602) (xy 107.608489 80.885232)
				(xy 107.610233 80.883487) (xy 107.575608 80.837242) (xy 107.570537 80.766426) (xy 107.604558 80.704111)
				(xy 107.611957 80.697699) (xy 107.614859 80.694797) (xy 107.614859 80.694795) (xy 107.614861 80.694795)
				(xy 107.702489 80.577738) (xy 107.753589 80.440735) (xy 107.754631 80.431048) (xy 107.756354 80.415019)
				(xy 110.4515 80.415019) (xy 110.4515 80.620981) (xy 110.478777 80.7932) (xy 110.483721 80.824412)
				(xy 110.544473 81.011388) (xy 110.547366 81.02029) (xy 110.640871 81.203803) (xy 110.640873 81.203806)
				(xy 110.662232 81.233204) (xy 110.761932 81.37043) (xy 110.761934 81.370432) (xy 110.761936 81.370435)
				(xy 110.907564 81.516063) (xy 110.907567 81.516065) (xy 110.90757 81.516068) (xy 111.074197 81.637129)
				(xy 111.25771 81.730634) (xy 111.453592 81.79428) (xy 111.657019 81.8265) (xy 111.657022 81.8265)
				(xy 111.862978 81.8265) (xy 111.862981 81.8265) (xy 112.066408 81.79428) (xy 112.26229 81.730634)
				(xy 112.445803 81.637129) (xy 112.61243 81.516068) (xy 112.758068 81.37043) (xy 112.879129 81.203803)
				(xy 112.972634 81.02029) (xy 113.03628 80.824408) (xy 113.0685 80.620981) (xy 113.0685 80.415019)
				(xy 113.03628 80.211592) (xy 112.972634 80.01571) (xy 112.879129 79.832197) (xy 112.758068 79.66557)
				(xy 112.758065 79.665567) (xy 112.758063 79.665564) (xy 112.612435 79.519936) (xy 112.612432 79.519934)
				(xy 112.61243 79.519932) (xy 112.445803 79.398871) (xy 112.26229 79.305366) (xy 112.262287 79.305365)
				(xy 112.262285 79.305364) (xy 112.066412 79.241721) (xy 112.06641 79.24172) (xy 112.066408 79.24172)
				(xy 111.862981 79.2095) (xy 111.657019 79.2095) (xy 111.453592 79.24172) (xy 111.45359 79.24172)
				(xy 111.453587 79.241721) (xy 111.257714 79.305364) (xy 111.257708 79.305367) (xy 111.074193 79.398873)
				(xy 110.907567 79.519934) (xy 110.907564 79.519936) (xy 110.761936 79.665564) (xy 110.761934 79.665567)
				(xy 110.640873 79.832193) (xy 110.547367 80.015708) (xy 110.547364 80.015714) (xy 110.483721 80.211587)
				(xy 110.48372 80.21159) (xy 110.48372 80.211592) (xy 110.4515 80.415019) (xy 107.756354 80.415019)
				(xy 107.758334 80.396601) (xy 107.758334 80.396599) (xy 107.760099 80.380181) (xy 107.7601 80.380166)
				(xy 107.7601 78.436233) (xy 107.760099 78.436216) (xy 107.75359 78.375669) (xy 107.753588 78.375661)
				(xy 107.702489 78.238663) (xy 107.702487 78.238658) (xy 107.614859 78.121602) (xy 107.608489 78.115232)
				(xy 107.610233 78.113487) (xy 107.575608 78.067242) (xy 107.570537 77.996426) (xy 107.604558 77.934111)
				(xy 107.611957 77.927699) (xy 107.614859 77.924797) (xy 107.614859 77.924795) (xy 107.614861 77.924795)
				(xy 107.691495 77.822424) (xy 107.702487 77.807741) (xy 107.702487 77.80774) (xy 107.702489 77.807738)
				(xy 107.753589 77.670735) (xy 107.755836 77.649842) (xy 107.760099 77.610183) (xy 107.7601 77.610166)
				(xy 107.7601 77.152) (xy 107.780102 77.083879) (xy 107.833758 77.037386) (xy 107.8861 77.026) (xy 109.504553 77.026)
				(xy 109.504554 77.026) (xy 109.664135 76.994258) (xy 109.814458 76.931992) (xy 109.949745 76.841596)
				(xy 110.064796 76.726545) (xy 110.064797 76.726542) (xy 110.071973 76.719367) (xy 110.071979 76.719359)
				(xy 110.517702 76.273636) (xy 110.580012 76.239612) (xy 110.650827 76.244677) (xy 110.707663 76.287224)
				(xy 110.732474 76.353744) (xy 110.717383 76.423118) (xy 110.708732 76.436793) (xy 110.640872 76.530194)
				(xy 110.547367 76.713708) (xy 110.547364 76.713714) (xy 110.483721 76.909587) (xy 110.48372 76.90959)
				(xy 110.48372 76.909592) (xy 110.4515 77.113019) (xy 110.4515 77.318981) (xy 110.48372 77.522408)
				(xy 110.483721 77.522412) (xy 110.545383 77.712189) (xy 110.547366 77.71829) (xy 110.640871 77.901803)
				(xy 110.640873 77.901806) (xy 110.657576 77.924795) (xy 110.761932 78.06843) (xy 110.761934 78.068432)
				(xy 110.761936 78.068435) (xy 110.907564 78.214063) (xy 110.907567 78.214065) (xy 110.90757 78.214068)
				(xy 111.074197 78.335129) (xy 111.25771 78.428634) (xy 111.453592 78.49228) (xy 111.657019 78.5245)
				(xy 111.657022 78.5245) (xy 111.862978 78.5245) (xy 111.862981 78.5245) (xy 112.066408 78.49228)
				(xy 112.26229 78.428634) (xy 112.445803 78.335129) (xy 112.61243 78.214068) (xy 112.758068 78.06843)
				(xy 112.879129 77.901803) (xy 112.972634 77.71829) (xy 113.03628 77.522408) (xy 113.0685 77.318981)
				(xy 113.0685 77.113019) (xy 120.6115 77.113019) (xy 120.6115 77.318981) (xy 120.64372 77.522408)
				(xy 120.643721 77.522412) (xy 120.705383 77.712189) (xy 120.707366 77.71829) (xy 120.800871 77.901803)
				(xy 120.800873 77.901806) (xy 120.817576 77.924795) (xy 120.921932 78.06843) (xy 120.921934 78.068432)
				(xy 120.921936 78.068435) (xy 121.067564 78.214063) (xy 121.067567 78.214065) (xy 121.06757 78.214068)
				(xy 121.234197 78.335129) (xy 121.41771 78.428634) (xy 121.613592 78.49228) (xy 121.817019 78.5245)
				(xy 121.817022 78.5245) (xy 122.022978 78.5245) (xy 122.022981 78.5245) (xy 122.226408 78.49228)
				(xy 122.42229 78.428634) (xy 122.605803 78.335129) (xy 122.681603 78.280056) (xy 122.748467 78.256199)
				(xy 122.817619 78.272278) (xy 122.844757 78.292898) (xy 123.933449 79.381591) (xy 123.933455 79.381596)
				(xy 124.052642 79.461235) (xy 124.09817 79.515712) (xy 124.107017 79.586155) (xy 124.076376 79.650199)
				(xy 124.015974 79.687511) (xy 123.98264 79.692) (xy 122.996689 79.692) (xy 122.928568 79.671998)
				(xy 122.907594 79.655095) (xy 122.772435 79.519936) (xy 122.772432 79.519934) (xy 122.77243 79.519932)
				(xy 122.605803 79.398871) (xy 122.42229 79.305366) (xy 122.422287 79.305365) (xy 122.422285 79.305364)
				(xy 122.226412 79.241721) (xy 122.22641 79.24172) (xy 122.226408 79.24172) (xy 122.022981 79.2095)
				(xy 121.817019 79.2095) (xy 121.613592 79.24172) (xy 121.61359 79.24172) (xy 121.613587 79.241721)
				(xy 121.417714 79.305364) (xy 121.417708 79.305367) (xy 121.234193 79.398873) (xy 121.067567 79.519934)
				(xy 121.067564 79.519936) (xy 120.921936 79.665564) (xy 120.921934 79.665567) (xy 120.800873 79.832193)
				(xy 120.707367 80.015708) (xy 120.707364 80.015714) (xy 120.643721 80.211587) (xy 120.64372 80.21159)
				(xy 120.64372 80.211592) (xy 120.6115 80.415019) (xy 120.6115 80.620981) (xy 120.638777 80.7932)
				(xy 120.643721 80.824412) (xy 120.704473 81.011388) (xy 120.707366 81.02029) (xy 120.800871 81.203803)
				(xy 120.800873 81.203806) (xy 120.822232 81.233204) (xy 120.921932 81.37043) (xy 120.921934 81.370432)
				(xy 120.921936 81.370435) (xy 121.067564 81.516063) (xy 121.067567 81.516065) (xy 121.06757 81.516068)
				(xy 121.234197 81.637129) (xy 121.41771 81.730634) (xy 121.613592 81.79428) (xy 121.817019 81.8265)
				(xy 121.817022 81.8265) (xy 122.022978 81.8265) (xy 122.022981 81.8265) (xy 122.226408 81.79428)
				(xy 122.42229 81.730634) (xy 122.605803 81.637129) (xy 122.77243 81.516068) (xy 122.817499 81.470999)
				(xy 122.907594 81.380905) (xy 122.969906 81.346879) (xy 122.996689 81.344) (xy 123.981143 81.344)
				(xy 124.033425 81.355359) (xy 124.042686 81.359582) (xy 124.068741 81.376992) (xy 124.198817 81.430871)
				(xy 124.219065 81.439258) (xy 124.356469 81.466588) (xy 124.37864 81.470999) (xy 124.378643 81.470999)
				(xy 124.378646 81.471) (xy 124.378647 81.471) (xy 124.541353 81.471) (xy 125.179868 81.471) (xy 125.247989 81.491002)
				(xy 125.294482 81.544658) (xy 125.305491 81.606745) (xy 125.304119 81.62442) (xy 125.298011 81.640799)
				(xy 125.2915 81.701362) (xy 125.2915 81.787109) (xy 125.291123 81.79197) (xy 125.280179 81.820778)
				(xy 125.271498 81.850346) (xy 125.26734 81.854578) (xy 125.265912 81.858339) (xy 125.257637 81.864455)
				(xy 125.235503 81.886989) (xy 125.203458 81.908401) (xy 125.203455 81.908403) (xy 125.14661 81.965249)
				(xy 125.088404 82.023455) (xy 124.154764 82.957095) (xy 124.092452 82.991121) (xy 124.065669 82.994)
				(xy 123.009389 82.994) (xy 122.941268 82.973998) (xy 122.920294 82.957095) (xy 122.785135 82.821936)
				(xy 122.785132 82.821934) (xy 122.78513 82.821932) (xy 122.618503 82.700871) (xy 122.43499 82.607366)
				(xy 122.434987 82.607365) (xy 122.434985 82.607364) (xy 122.239112 82.543721) (xy 122.23911 82.54372)
				(xy 122.239108 82.54372) (xy 122.035681 82.5115) (xy 121.829719 82.5115) (xy 121.626292 82.54372)
				(xy 121.62629 82.54372) (xy 121.626287 82.543721) (xy 121.430414 82.607364) (xy 121.430408 82.607367)
				(xy 121.246893 82.700873) (xy 121.080267 82.821934) (xy 121.080264 82.821936) (xy 120.934636 82.967564)
				(xy 120.934634 82.967567) (xy 120.813573 83.134193) (xy 120.720067 83.317708) (xy 120.720064 83.317714)
				(xy 120.656421 83.513587) (xy 120.65642 83.51359) (xy 120.65642 83.513592) (xy 120.6242 83.717019)
				(xy 120.6242 83.922981) (xy 120.652034 84.098716) (xy 120.656421 84.126412) (xy 120.71144 84.295744)
				(xy 120.720066 84.32229) (xy 120.813571 84.505803) (xy 120.934632 84.67243) (xy 120.934634 84.672432)
				(xy 120.934636 84.672435) (xy 121.080264 84.818063) (xy 121.080267 84.818065) (xy 121.08027 84.818068)
				(xy 121.246897 84.939129) (xy 121.43041 85.032634) (xy 121.626292 85.09628) (xy 121.829719 85.1285)
				(xy 121.829722 85.1285) (xy 122.035678 85.1285) (xy 122.035681 85.1285) (xy 122.239108 85.09628)
				(xy 122.43499 85.032634) (xy 122.618503 84.939129) (xy 122.78513 84.818068) (xy 122.839853 84.763345)
				(xy 122.920294 84.682905) (xy 122.982606 84.648879) (xy 123.009389 84.646) (xy 124.541353 84.646)
				(xy 124.541354 84.646) (xy 124.700935 84.614258) (xy 124.851258 84.551992) (xy 124.986545 84.461596)
				(xy 125.101596 84.346545) (xy 125.101597 84.346542) (xy 125.108773 84.339367) (xy 125.108779 84.339359)
				(xy 125.564605 83.883533) (xy 125.626915 83.849509) (xy 125.67424 83.848316) (xy 125.68629 83.850307)
				(xy 125.690799 83.851989) (xy 125.707023 83.853733) (xy 125.710544 83.854315) (xy 125.739427 83.868244)
				(xy 125.769059 83.880517) (xy 125.771165 83.88355) (xy 125.774492 83.885155) (xy 125.791261 83.912491)
				(xy 125.809552 83.938833) (xy 125.809684 83.942523) (xy 125.811616 83.945673) (xy 125.810943 83.977732)
				(xy 125.81209 84.009784) (xy 125.810204 84.012962) (xy 125.810127 84.016654) (xy 125.792232 84.043254)
				(xy 125.775865 84.070844) (xy 125.77095 84.074892) (xy 125.7705 84.075562) (xy 125.769738 84.07589)
				(xy 125.764068 84.080562) (xy 125.747889 84.092317) (xy 125.602317 84.237889) (xy 125.602315 84.237892)
				(xy 125.4813 84.404454) (xy 125.387829 84.5879) (xy 125.387826 84.587906) (xy 125.324208 84.783704)
				(xy 125.315926 84.836) (xy 126.688314 84.836) (xy 126.67992 84.844394) (xy 126.627259 84.935606)
				(xy 126.6 85.037339) (xy 126.6 85.142661) (xy 126.627259 85.244394) (xy 126.67992 85.335606) (xy 126.688314 85.344)
				(xy 125.315926 85.344) (xy 125.324208 85.396295) (xy 125.387826 85.592093) (xy 125.387829 85.592099)
				(xy 125.4813 85.775545) (xy 125.602315 85.942107) (xy 125.602317 85.94211) (xy 125.747889 86.087682)
				(xy 125.747892 86.087684) (xy 125.914455 86.2087) (xy 125.990511 86.247453) (xy 126.042126 86.296202)
				(xy 126.059192 86.365117) (xy 126.036291 86.432318) (xy 125.990511 86.471987) (xy 125.914193 86.510872)
				(xy 125.747569 86.631932) (xy 125.682027 86.697474) (xy 125.619714 86.731499) (xy 125.548899 86.726433)
				(xy 125.503837 86.697473) (xy 125.370415 86.564051) (xy 125.370412 86.564049) (xy 125.37041 86.564047)
				(xy 125.192449 86.434751) (xy 124.996454 86.334886) (xy 124.996453 86.334885) (xy 124.99645 86.334884)
				(xy 124.787254 86.266912) (xy 124.78725 86.266911) (xy 124.787249 86.266911) (xy 124.569986 86.2325)
				(xy 124.350014 86.2325) (xy 124.132751 86.266911) (xy 124.132745 86.266912) (xy 123.923549 86.334884)
				(xy 123.727547 86.434753) (xy 123.549587 86.564049) (xy 123.549584 86.564051) (xy 123.394051 86.719584)
				(xy 123.394049 86.719587) (xy 123.26475 86.89755) (xy 123.25112 86.924301) (xy 123.20237 86.975915)
				(xy 123.133455 86.992979) (xy 123.066254 86.970076) (xy 123.036921 86.941158) (xy 122.918068 86.77757)
				(xy 122.918065 86.777567) (xy 122.918063 86.777564) (xy 122.772435 86.631936) (xy 122.772432 86.631934)
				(xy 122.77243 86.631932) (xy 122.646999 86.540802) (xy 122.605806 86.510873) (xy 122.605805 86.510872)
				(xy 122.605803 86.510871) (xy 122.42229 86.417366) (xy 122.422287 86.417365) (xy 122.422285 86.417364)
				(xy 122.226412 86.353721) (xy 122.22641 86.35372) (xy 122.226408 86.35372) (xy 122.022981 86.3215)
				(xy 121.817019 86.3215) (xy 121.613592 86.35372) (xy 121.61359 86.35372) (xy 121.613587 86.353721)
				(xy 121.417714 86.417364) (xy 121.417708 86.417367) (xy 121.234193 86.510873) (xy 121.067567 86.631934)
				(xy 121.067564 86.631936) (xy 120.921936 86.777564) (xy 120.921934 86.777567) (xy 120.800873 86.944193)
				(xy 120.707367 87.127708) (xy 120.707364 87.127714) (xy 120.643721 87.323587) (xy 120.64372 87.32359)
				(xy 120.64372 87.323592) (xy 120.6115 87.527019) (xy 120.6115 87.732981) (xy 120.643702 87.936295)
				(xy 120.643721 87.936412) (xy 120.69239 88.086201) (xy 120.707366 88.13229) (xy 120.800871 88.315803)
				(xy 120.921932 88.48243) (xy 120.921934 88.482432) (xy 120.921936 88.482435) (xy 121.057095 88.617594)
				(xy 121.091121 88.679906) (xy 121.094 88.706689) (xy 121.094 89.616359) (xy 121.11581 89.726001)
				(xy 121.125742 89.775935) (xy 121.188008 89.926258) (xy 121.278404 90.061545) (xy 121.342826 90.125967)
				(xy 121.397855 90.180996) (xy 121.397866 90.181006) (xy 121.910228 90.693368) (xy 121.910242 90.693383)
				(xy 121.913403 90.696544) (xy 121.913404 90.696545) (xy 122.028455 90.811596) (xy 122.140338 90.886354)
				(xy 122.140337 90.886354) (xy 122.163737 90.901989) (xy 122.163739 90.90199) (xy 122.163742 90.901992)
				(xy 122.314065 90.964258) (xy 122.473647 90.996001) (xy 122.473648 90.996001) (xy 122.642567 90.996001)
				(xy 122.642587 90.996) (xy 125.523311 90.996) (xy 125.591432 91.016002) (xy 125.612406 91.032905)
				(xy 125.747564 91.168063) (xy 125.747567 91.168065) (xy 125.74757 91.168068) (xy 125.914197 91.289129)
				(xy 125.989962 91.327733) (xy 126.041577 91.376482) (xy 126.058643 91.445397) (xy 126.035742 91.512598)
				(xy 125.989963 91.552266) (xy 125.939403 91.578028) (xy 125.914193 91.590873) (xy 125.747567 91.711934)
				(xy 125.747564 91.711936) (xy 125.612406 91.847095) (xy 125.550094 91.881121) (xy 125.523311 91.884)
				(xy 122.996689 91.884) (xy 122.928568 91.863998) (xy 122.907594 91.847095) (xy 122.772435 91.711936)
				(xy 122.772432 91.711934) (xy 122.77243 91.711932) (xy 122.621954 91.602605) (xy 122.605806 91.590873)
				(xy 122.605805 91.590872) (xy 122.605803 91.590871) (xy 122.42229 91.497366) (xy 122.422287 91.497365)
				(xy 122.422285 91.497364) (xy 122.226412 91.433721) (xy 122.22641 91.43372) (xy 122.226408 91.43372)
				(xy 122.022981 91.4015) (xy 121.817019 91.4015) (xy 121.613592 91.43372) (xy 121.61359 91.43372)
				(xy 121.613587 91.433721) (xy 121.417714 91.497364) (xy 121.417708 91.497367) (xy 121.234193 91.590873)
				(xy 121.067567 91.711934) (xy 121.067564 91.711936) (xy 120.921936 91.857564) (xy 120.921934 91.857567)
				(xy 120.800873 92.024193) (xy 120.707367 92.207708) (xy 120.707364 92.207714) (xy 120.643721 92.403587)
				(xy 120.64372 92.40359) (xy 120.64372 92.403592) (xy 120.6115 92.607019) (xy 120.6115 92.812981)
				(xy 120.643702 93.016295) (xy 120.643721 93.016412) (xy 120.694805 93.173633) (xy 120.707366 93.21229)
				(xy 120.800871 93.395803) (xy 120.921932 93.56243) (xy 120.921934 93.562432) (xy 120.921936 93.562435)
				(xy 121.067564 93.708063) (xy 121.067567 93.708065) (xy 121.06757 93.708068) (xy 121.234197 93.829129)
				(xy 121.41771 93.922634) (xy 121.613592 93.98628) (xy 121.817019 94.0185) (xy 121.817022 94.0185)
				(xy 122.022978 94.0185) (xy 122.022981 94.0185) (xy 122.226408 93.98628) (xy 122.42229 93.922634)
				(xy 122.605803 93.829129) (xy 122.77243 93.708068) (xy 122.818498 93.662) (xy 122.907594 93.572905)
				(xy 122.969906 93.538879) (xy 122.996689 93.536) (xy 124.701669 93.536) (xy 124.76979 93.556002)
				(xy 124.816283 93.609658) (xy 124.826387 93.679932) (xy 124.796893 93.744512) (xy 124.79077 93.751088)
				(xy 124.674406 93.867453) (xy 124.568458 93.973401) (xy 124.568455 93.973404) (xy 124.453406 94.088452)
				(xy 124.453401 94.088459) (xy 124.363008 94.223742) (xy 124.300744 94.374059) (xy 124.300742 94.374066)
				(xy 124.269 94.53364) (xy 124.269 95.966359) (xy 124.291672 96.08034) (xy 124.300742 96.125935)
				(xy 124.363008 96.276258) (xy 124.453404 96.411545) (xy 124.568455 96.526596) (xy 124.703742 96.616992)
				(xy 124.849241 96.67726) (xy 124.890114 96.70457) (xy 125.268222 97.082678) (xy 125.342501 97.13231)
				(xy 125.388029 97.186787) (xy 125.396877 97.25723) (xy 125.388915 97.285275) (xy 125.387831 97.287891)
				(xy 125.324208 97.483704) (xy 125.315926 97.536) (xy 126.688314 97.536) (xy 126.67992 97.544394)
				(xy 126.627259 97.635606) (xy 126.6 97.737339) (xy 126.6 97.842661) (xy 126.627259 97.944394) (xy 126.67992 98.035606)
				(xy 126.688314 98.044) (xy 125.315926 98.044) (xy 125.324208 98.096295) (xy 125.387826 98.292093)
				(xy 125.387829 98.292099) (xy 125.4813 98.475545) (xy 125.602315 98.642107) (xy 125.602317 98.64211)
				(xy 125.747889 98.787682) (xy 125.747892 98.787684) (xy 125.914454 98.908699) (xy 126.0979 99.00217)
				(xy 126.097906 99.002173) (xy 126.293705 99.065791) (xy 126.293701 99.065791) (xy 126.497061 99.098)
				(xy 126.746 99.098) (xy 126.746 98.101686) (xy 126.754394 98.11008) (xy 126.845606 98.162741) (xy 126.947339 98.19)
				(xy 127.052661 98.19) (xy 127.154394 98.162741) (xy 127.245606 98.11008) (xy 127.254 98.101686)
				(xy 127.254 99.098) (xy 127.502939 99.098) (xy 127.706296 99.065791) (xy 127.902093 99.002173) (xy 127.902099 99.00217)
				(xy 128.085545 98.908699) (xy 128.252107 98.787684) (xy 128.25211 98.787682) (xy 128.397682 98.64211)
				(xy 128.397684 98.642107) (xy 128.518699 98.475545) (xy 128.61217 98.292099) (xy 128.612173 98.292093)
				(xy 128.675791 98.096295) (xy 128.684074 98.044) (xy 127.311686 98.044) (xy 127.32008 98.035606)
				(xy 127.372741 97.944394) (xy 127.4 97.842661) (xy 127.4 97.737339) (xy 127.372741 97.635606) (xy 127.32008 97.544394)
				(xy 127.311686 97.536) (xy 128.684074 97.536) (xy 128.676743 97.489713) (xy 128.685843 97.419302)
				(xy 128.731565 97.364988) (xy 128.799393 97.344015) (xy 128.817455 97.345664) (xy 128.817482 97.345393)
				(xy 128.823645 97.345999) (xy 128.823646 97.346) (xy 129.14567 97.346) (xy 129.213791 97.366002)
				(xy 129.234765 97.382905) (xy 129.947095 98.095235) (xy 129.981121 98.157547) (xy 129.984 98.18433)
				(xy 129.984 104.856359) (xy 130.006672 104.97034) (xy 130.015742 105.015935) (xy 130.078008 105.166258)
				(xy 130.168404 105.301545) (xy 130.237486 105.370627) (xy 130.287855 105.420996) (xy 130.287866 105.421006)
				(xy 131.438401 106.571541) (xy 131.438404 106.571545) (xy 131.553455 106.686596) (xy 131.601203 106.7185)
				(xy 131.688741 106.776992) (xy 131.839065 106.839258) (xy 131.976469 106.866588) (xy 131.99864 106.870999)
				(xy 131.998643 106.870999) (xy 131.998646 106.871) (xy 131.998647 106.871) (xy 139.83175 106.871)
				(xy 139.899871 106.891002) (xy 139.946364 106.944658) (xy 139.956468 107.014932) (xy 139.933686 107.071061)
				(xy 139.851299 107.184454) (xy 139.757829 107.3679) (xy 139.757826 107.367906) (xy 139.694208 107.563703)
				(xy 139.662 107.767061) (xy 139.662 107.972938) (xy 139.694208 108.176296) (xy 139.757826 108.372093)
				(xy 139.757829 108.372099) (xy 139.851297 108.55554) (xy 139.851298 108.555541) (xy 139.882416 108.598372)
				(xy 139.882417 108.598372) (xy 140.57 107.910789) (xy 140.57 107.922661) (xy 140.597259 108.024394)
				(xy 140.64992 108.115606) (xy 140.724394 108.19008) (xy 140.815606 108.242741) (xy 140.917339 108.27)
				(xy 140.92921 108.27) (xy 140.241626 108.957582) (xy 140.241626 108.957583) (xy 140.284454 108.988699)
				(xy 140.4679 109.08217) (xy 140.467906 109.082173) (xy 140.663705 109.145791) (xy 140.663701 109.145791)
				(xy 140.867061 109.178) (xy 141.072939 109.178) (xy 141.276296 109.145791) (xy 141.472093 109.082173)
				(xy 141.472099 109.08217) (xy 141.655541 108.988701) (xy 141.698372 108.957582) (xy 141.698372 108.957581)
				(xy 141.010791 108.27) (xy 141.022661 108.27) (xy 141.124394 108.242741) (xy 141.215606 108.19008)
				(xy 141.29008 108.115606) (xy 141.342741 108.024394) (xy 141.37 107.922661) (xy 141.37 107.910789)
				(xy 142.057582 108.598372) (xy 142.088701 108.555541) (xy 142.18217 108.372099) (xy 142.182173 108.372093)
				(xy 142.245791 108.176296) (xy 142.278 107.972938) (xy 142.278 107.767061) (xy 142.245791 107.563703)
				(xy 142.182173 107.367906) (xy 142.18217 107.3679) (xy 142.0887 107.184454) (xy 142.006314 107.071061)
				(xy 141.982456 107.004194) (xy 141.998536 106.935042) (xy 142.04945 106.885562) (xy 142.10825 106.871)
				(xy 143.86175 106.871) (xy 143.929871 106.891002) (xy 143.976364 106.944658) (xy 143.986468 107.014932)
				(xy 143.963686 107.071061) (xy 143.881299 107.184454) (xy 143.787829 107.3679) (xy 143.787826 107.367906)
				(xy 143.724208 107.563703) (xy 143.692 107.767061) (xy 143.692 107.972938) (xy 143.724208 108.176296)
				(xy 143.787826 108.372093) (xy 143.787829 108.372099) (xy 143.881297 108.55554) (xy 143.881298 108.555541)
				(xy 143.912416 108.598372) (xy 143.912417 108.598372) (xy 144.6 107.910789) (xy 144.6 107.922661)
				(xy 144.627259 108.024394) (xy 144.67992 108.115606) (xy 144.754394 108.19008) (xy 144.845606 108.242741)
				(xy 144.947339 108.27) (xy 144.95921 108.27) (xy 144.271626 108.957582) (xy 144.271626 108.957583)
				(xy 144.314454 108.988699) (xy 144.4979 109.08217) (xy 144.497906 109.082173) (xy 144.693705 109.145791)
				(xy 144.693701 109.145791) (xy 144.897061 109.178) (xy 145.102939 109.178) (xy 145.306296 109.145791)
				(xy 145.502093 109.082173) (xy 145.502099 109.08217) (xy 145.685541 108.988701) (xy 145.728372 108.957582)
				(xy 145.728372 108.957581) (xy 145.040791 108.27) (xy 145.052661 108.27) (xy 145.154394 108.242741)
				(xy 145.245606 108.19008) (xy 145.32008 108.115606) (xy 145.372741 108.024394) (xy 145.4 107.922661)
				(xy 145.4 107.910791) (xy 146.087581 108.598372) (xy 146.087582 108.598372) (xy 146.118701 108.555541)
				(xy 146.21217 108.372099) (xy 146.212173 108.372093) (xy 146.275791 108.176296) (xy 146.308 107.972938)
				(xy 146.308 107.767061) (xy 147.692 107.767061) (xy 147.692 107.972938) (xy 147.724208 108.176296)
				(xy 147.787826 108.372093) (xy 147.787829 108.372099) (xy 147.881297 108.55554) (xy 147.881298 108.555541)
				(xy 147.912416 108.598372) (xy 148.6 107.910788) (xy 148.6 107.922661) (xy 148.627259 108.024394)
				(xy 148.67992 108.115606) (xy 148.754394 108.19008) (xy 148.845606 108.242741) (xy 148.947339 108.27)
				(xy 148.95921 108.27) (xy 148.271626 108.957582) (xy 148.271626 108.957583) (xy 148.314454 108.988699)
				(xy 148.4979 109.08217) (xy 148.497906 109.082173) (xy 148.693705 109.145791) (xy 148.693701 109.145791)
				(xy 148.897061 109.178) (xy 149.102939 109.178) (xy 149.306296 109.145791) (xy 149.502093 109.082173)
				(xy 149.502099 109.08217) (xy 149.685541 108.988701) (xy 149.728372 108.957582) (xy 149.728372 108.957581)
				(xy 149.040791 108.27) (xy 149.052661 108.27) (xy 149.154394 108.242741) (xy 149.245606 108.19008)
				(xy 149.32008 108.115606) (xy 149.372741 108.024394) (xy 149.4 107.922661) (xy 149.4 107.910791)
				(xy 150.087581 108.598372) (xy 150.087582 108.598372) (xy 150.118701 108.555541) (xy 150.21217 108.372099)
				(xy 150.212173 108.372093) (xy 150.275791 108.176296) (xy 150.308 107.972938) (xy 150.308 107.767061)
				(xy 150.275791 107.563703) (xy 150.212173 107.367906) (xy 150.21217 107.3679) (xy 150.118699 107.184454)
				(xy 150.087583 107.141626) (xy 150.087582 107.141626) (xy 149.4 107.829208) (xy 149.4 107.817339)
				(xy 149.372741 107.715606) (xy 149.32008 107.624394) (xy 149.245606 107.54992) (xy 149.154394 107.497259)
				(xy 149.052661 107.47) (xy 149.04079 107.47) (xy 149.728372 106.782416) (xy 149.685541 106.751298)
				(xy 149.68554 106.751297) (xy 149.502099 106.657829) (xy 149.502093 106.657826) (xy 149.306294 106.594208)
				(xy 149.306298 106.594208) (xy 149.102939 106.562) (xy 148.897061 106.562) (xy 148.693703 106.594208)
				(xy 148.497906 106.657826) (xy 148.4979 106.657829) (xy 148.314452 106.751301) (xy 148.271625 106.782416)
				(xy 148.959209 107.47) (xy 148.947339 107.47) (xy 148.845606 107.497259) (xy 148.754394 107.54992)
				(xy 148.67992 107.624394) (xy 148.627259 107.715606) (xy 148.6 107.817339) (xy 148.6 107.829209)
				(xy 147.912416 107.141625) (xy 147.881301 107.184452) (xy 147.787829 107.3679) (xy 147.787826 107.367906)
				(xy 147.724208 107.563703) (xy 147.692 107.767061) (xy 146.308 107.767061) (xy 146.275791 107.563703)
				(xy 146.212173 107.367906) (xy 146.21217 107.3679) (xy 146.118699 107.184454) (xy 146.087583 107.141626)
				(xy 146.087582 107.141626) (xy 145.4 107.829208) (xy 145.4 107.817339) (xy 145.372741 107.715606)
				(xy 145.32008 107.624394) (xy 145.245606 107.54992) (xy 145.154394 107.497259) (xy 145.052661 107.47)
				(xy 145.040789 107.47) (xy 145.664837 106.845951) (xy 145.665933 106.845143) (xy 145.666283 106.844528)
				(xy 145.670793 106.841566) (xy 145.695336 106.823501) (xy 145.700407 106.820836) (xy 145.806258 106.776992)
				(xy 145.941545 106.686596) (xy 146.056596 106.571545) (xy 146.056597 106.571542) (xy 146.063773 106.564367)
				(xy 146.063779 106.564359) (xy 147.204358 105.42378) (xy 147.204372 105.423769) (xy 147.326591 105.30155)
				(xy 147.326596 105.301545) (xy 147.381751 105.219) (xy 147.416992 105.166259) (xy 147.479258 105.015935)
				(xy 147.511 104.856354) (xy 147.511 104.693647) (xy 147.511 104.693646) (xy 147.511 103.89933) (xy 147.519783 103.869414)
				(xy 147.526412 103.838945) (xy 147.530226 103.833849) (xy 147.531002 103.831209) (xy 147.547905 103.810235)
				(xy 147.625235 103.732905) (xy 147.687547 103.698879) (xy 147.71433 103.696) (xy 147.923311 103.696)
				(xy 147.991432 103.716002) (xy 148.012406 103.732905) (xy 148.147564 103.868063) (xy 148.147567 103.868065)
				(xy 148.14757 103.868068) (xy 148.314197 103.989129) (xy 148.49771 104.082634) (xy 148.693592 104.14628)
				(xy 148.897019 104.1785) (xy 148.897022 104.1785) (xy 149.102978 104.1785) (xy 149.102981 104.1785)
				(xy 149.306408 104.14628) (xy 149.50229 104.082634) (xy 149.685803 103.989129) (xy 149.85243 103.868068)
				(xy 149.91543 103.805068) (xy 149.987594 103.732905) (xy 150.049906 103.698879) (xy 150.076689 103.696)
				(xy 151.37067 103.696) (xy 151.400585 103.704783) (xy 151.431055 103.711412) (xy 151.43615 103.715226)
				(xy 151.438791 103.716002) (xy 151.459765 103.732905) (xy 151.537095 103.810235) (xy 151.571121 103.872547)
				(xy 151.574 103.89933) (xy 151.574 104.333311) (xy 151.553998 104.401432) (xy 151.537095 104.422406)
				(xy 151.401936 104.557564) (xy 151.401934 104.557567) (xy 151.280873 104.724193) (xy 151.187367 104.907708)
				(xy 151.187364 104.907714) (xy 151.123721 105.103587) (xy 151.12372 105.10359) (xy 151.12372 105.103592)
				(xy 151.0915 105.307019) (xy 151.0915 105.512981) (xy 151.12372 105.716408) (xy 151.123721 105.716412)
				(xy 151.18387 105.901533) (xy 151.187366 105.91229) (xy 151.280871 106.095803) (xy 151.401932 106.26243)
				(xy 151.401934 106.262432) (xy 151.401936 106.262435) (xy 151.547564 106.408063) (xy 151.547567 106.408065)
				(xy 151.54757 106.408068) (xy 151.714197 106.529129) (xy 151.89771 106.622634) (xy 152.093592 106.68628)
				(xy 152.297019 106.7185) (xy 152.297022 106.7185) (xy 152.502978 106.7185) (xy 152.502981 106.7185)
				(xy 152.706408 106.68628) (xy 152.90229 106.622634) (xy 153.085803 106.529129) (xy 153.25243 106.408068)
				(xy 153.398068 106.26243) (xy 153.519129 106.095803) (xy 153.612634 105.91229) (xy 153.67628 105.716408)
				(xy 153.7085 105.512981) (xy 153.7085 105.307019) (xy 153.67628 105.103592) (xy 153.612634 104.90771)
				(xy 153.519129 104.724197) (xy 153.398068 104.55757) (xy 153.398065 104.557567) (xy 153.398063 104.557564)
				(xy 153.262905 104.422406) (xy 153.228879 104.360094) (xy 153.226 104.333311) (xy 153.226 103.592587)
				(xy 153.226001 103.592566) (xy 153.226001 103.423648) (xy 153.226 103.423644) (xy 153.21952 103.391065)
				(xy 153.194258 103.264065) (xy 153.131992 103.113742) (xy 153.096751 103.061) (xy 153.041596 102.978455)
				(xy 153.041591 102.978449) (xy 152.923399 102.860257) (xy 152.923368 102.860228) (xy 152.411006 102.347866)
				(xy 152.410997 102.347856) (xy 152.379495 102.316354) (xy 152.291545 102.228404) (xy 152.156258 102.138008)
				(xy 152.005935 102.075742) (xy 151.96034 102.066672) (xy 151.846359 102.044) (xy 151.846354 102.044)
				(xy 150.076689 102.044) (xy 150.046773 102.035216) (xy 150.016304 102.028588) (xy 150.011208 102.024773)
				(xy 150.008568 102.023998) (xy 149.987594 102.007095) (xy 149.986594 102.006095) (xy 149.952568 101.943783)
				(xy 149.957633 101.872968) (xy 150.00018 101.816132) (xy 150.0667 101.791321) (xy 150.075689 101.791)
				(xy 151.846353 101.791) (xy 151.846354 101.791) (xy 152.005935 101.759258) (xy 152.156258 101.696992)
				(xy 152.291545 101.606596) (xy 152.406596 101.491545) (xy 152.406597 101.491542) (xy 152.413773 101.484367)
				(xy 152.413779 101.484359) (xy 152.919359 100.978779) (xy 152.919367 100.978773) (xy 152.926542 100.971597)
				(xy 152.926545 100.971596) (xy 153.041596 100.856545) (xy 153.131992 100.721258) (xy 153.194258 100.570935)
				(xy 153.226001 100.411353) (xy 153.226001 100.248646) (xy 153.226001 100.243454) (xy 153.226 100.243428)
				(xy 153.226 100.136689) (xy 153.246002 100.068568) (xy 153.262905 100.047594) (xy 153.398063 99.912435)
				(xy 153.398068 99.91243) (xy 153.519129 99.745803) (xy 153.612634 99.56229) (xy 153.67628 99.366408)
				(xy 153.7085 99.162981) (xy 153.7085 98.957019) (xy 153.67628 98.753592) (xy 153.612634 98.55771)
				(xy 153.519129 98.374197) (xy 153.398068 98.20757) (xy 153.398065 98.207567) (xy 153.398063 98.207564)
				(xy 153.252435 98.061936) (xy 153.252432 98.061934) (xy 153.25243 98.061932) (xy 153.085803 97.940871)
				(xy 152.90229 97.847366) (xy 152.902287 97.847365) (xy 152.902285 97.847364) (xy 152.706412 97.783721)
				(xy 152.70641 97.78372) (xy 152.706408 97.78372) (xy 152.603626 97.76744) (xy 152.539474 97.737028)
				(xy 152.501947 97.67676) (xy 152.502961 97.60577) (xy 152.53424 97.553898) (xy 152.919361 97.168777)
				(xy 152.919368 97.168772) (xy 152.926542 97.161597) (xy 152.926545 97.161596) (xy 153.041596 97.046545)
				(xy 153.131992 96.911258) (xy 153.194258 96.760935) (xy 153.200153 96.7313) (xy 153.226001 96.601353)
				(xy 153.226001 96.438647) (xy 153.226001 96.433455) (xy 153.226 96.433429) (xy 153.226 95.056689)
				(xy 153.246002 94.988568) (xy 153.262905 94.967594) (xy 153.398063 94.832435) (xy 153.398068 94.83243)
				(xy 153.519129 94.665803) (xy 153.612634 94.48229) (xy 153.67628 94.286408) (xy 153.7085 94.082981)
				(xy 153.7085 93.877019) (xy 153.67628 93.673592) (xy 153.612634 93.47771) (xy 153.519129 93.294197)
				(xy 153.398068 93.12757) (xy 153.398065 93.127567) (xy 153.398063 93.127564) (xy 153.252435 92.981936)
				(xy 153.252432 92.981934) (xy 153.25243 92.981932) (xy 153.085803 92.860871) (xy 152.90229 92.767366)
				(xy 152.902287 92.767365) (xy 152.902285 92.767364) (xy 152.706412 92.703721) (xy 152.70641 92.70372)
				(xy 152.706408 92.70372) (xy 152.502981 92.6715) (xy 152.297019 92.6715) (xy 152.093592 92.70372)
				(xy 152.09359 92.70372) (xy 152.093587 92.703721) (xy 151.897714 92.767364) (xy 151.897708 92.767367)
				(xy 151.714193 92.860873) (xy 151.547567 92.981934) (xy 151.547564 92.981936) (xy 151.401936 93.127564)
				(xy 151.401934 93.127567) (xy 151.280873 93.294193) (xy 151.187367 93.477708) (xy 151.187364 93.477714)
				(xy 151.123721 93.673587) (xy 151.12372 93.67359) (xy 151.12372 93.673592) (xy 151.0915 93.877019)
				(xy 151.0915 94.082981) (xy 151.12372 94.286408) (xy 151.123721 94.286412) (xy 151.159838 94.39757)
				(xy 151.187366 94.48229) (xy 151.280871 94.665803) (xy 151.401932 94.83243) (xy 151.401934 94.832432)
				(xy 151.401936 94.832435) (xy 151.537095 94.967594) (xy 151.571121 95.029906) (xy 151.574 95.056689)
				(xy 151.574 96.125669) (xy 151.553998 96.19379) (xy 151.537095 96.214764) (xy 150.412157 97.339701)
				(xy 150.349845 97.373727) (xy 150.279029 97.368662) (xy 150.222194 97.326115) (xy 150.204627 97.29224)
				(xy 150.204527 97.292282) (xy 150.204041 97.291109) (xy 150.20323 97.289545) (xy 150.202634 97.28771)
				(xy 150.109129 97.104197) (xy 149.988068 96.93757) (xy 149.988065 96.937567) (xy 149.988063 96.937564)
				(xy 149.842435 96.791936) (xy 149.842432 96.791934) (xy 149.84243 96.791932) (xy 149.687347 96.679258)
				(xy 149.675806 96.670873) (xy 149.675805 96.670872) (xy 149.675803 96.670871) (xy 149.599486 96.631985)
				(xy 149.547873 96.583239) (xy 149.530807 96.514324) (xy 149.553708 96.447122) (xy 149.599488 96.407453)
				(xy 149.675545 96.368699) (xy 149.842107 96.247684) (xy 149.84211 96.247682) (xy 149.987682 96.10211)
				(xy 149.987684 96.102107) (xy 150.108699 95.935545) (xy 150.20217 95.752099) (xy 150.202173 95.752093)
				(xy 150.265791 95.556295) (xy 150.274074 95.504) (xy 148.901686 95.504) (xy 148.91008 95.495606)
				(xy 148.962741 95.404394) (xy 148.99 95.302661) (xy 148.99 95.197339) (xy 148.962741 95.095606)
				(xy 148.91008 95.004394) (xy 148.901686 94.996) (xy 150.274074 94.996) (xy 150.265791 94.943704)
				(xy 150.202173 94.747906) (xy 150.20217 94.7479) (xy 150.108699 94.564454) (xy 149.987684 94.397892)
				(xy 149.987682 94.397889) (xy 149.84211 94.252317) (xy 149.842107 94.252315) (xy 149.675545 94.1313)
				(xy 149.598938 94.092267) (xy 149.547323 94.043519) (xy 149.530257 93.974604) (xy 149.553158 93.907402)
				(xy 149.598938 93.867733) (xy 149.675545 93.828699) (xy 149.842107 93.707684) (xy 149.84211 93.707682)
				(xy 149.987682 93.56211) (xy 149.987684 93.562107) (xy 150.108699 93.395545) (xy 150.20217 93.212099)
				(xy 150.202173 93.212093) (xy 150.265791 93.016295) (xy 150.274074 92.964) (xy 148.901686 92.964)
				(xy 148.91008 92.955606) (xy 148.962741 92.864394) (xy 148.99 92.762661) (xy 148.99 92.657339) (xy 148.962741 92.555606)
				(xy 148.91008 92.464394) (xy 148.901686 92.456) (xy 150.274074 92.456) (xy 150.265791 92.403704)
				(xy 150.202173 92.207906) (xy 150.20217 92.2079) (xy 150.108699 92.024454) (xy 149.987684 91.857892)
				(xy 149.987682 91.857889) (xy 149.84211 91.712317) (xy 149.842107 91.712315) (xy 149.675542 91.591298)
				(xy 149.599487 91.552545) (xy 149.547872 91.503797) (xy 149.530807 91.434882) (xy 149.553708 91.36768)
				(xy 149.599485 91.328014) (xy 149.675803 91.289129) (xy 149.84243 91.168068) (xy 149.988068 91.02243)
				(xy 150.109129 90.855803) (xy 150.202634 90.67229) (xy 150.26628 90.476408) (xy 150.2985 90.272981)
				(xy 150.2985 90.067019) (xy 150.26628 89.863592) (xy 150.242153 89.789336) (xy 150.240125 89.718369)
				(xy 150.276788 89.657571) (xy 150.3405 89.626245) (xy 150.361986 89.6244) (xy 151.244713 89.6244)
				(xy 151.312834 89.644402) (xy 151.346648 89.676338) (xy 151.401932 89.75243) (xy 151.401934 89.752432)
				(xy 151.401936 89.752435) (xy 151.547564 89.898063) (xy 151.547567 89.898065) (xy 151.54757 89.898068)
				(xy 151.714197 90.019129) (xy 151.89771 90.112634) (xy 152.093592 90.17628) (xy 152.297019 90.2085)
				(xy 152.297022 90.2085) (xy 152.502978 90.2085) (xy 152.502981 90.2085) (xy 152.706408 90.17628)
				(xy 152.90229 90.112634) (xy 153.085803 90.019129) (xy 153.25243 89.898068) (xy 153.398068 89.75243)
				(xy 153.519129 89.585803) (xy 153.612634 89.40229) (xy 153.67628 89.206408) (xy 153.7085 89.002981)
				(xy 153.7085 88.797019) (xy 153.67628 88.593592) (xy 153.612634 88.39771) (xy 153.519129 88.214197)
				(xy 153.398068 88.04757) (xy 153.398065 88.047567) (xy 153.398063 88.047564) (xy 153.252435 87.901936)
				(xy 153.252432 87.901934) (xy 153.25243 87.901932) (xy 153.097347 87.789258) (xy 153.085806 87.780873)
				(xy 153.085805 87.780872) (xy 153.085803 87.780871) (xy 152.90229 87.687366) (xy 152.902287 87.687365)
				(xy 152.902285 87.687364) (xy 152.706412 87.623721) (xy 152.70641 87.62372) (xy 152.706408 87.62372)
				(xy 152.502981 87.5915) (xy 152.297019 87.5915) (xy 152.093592 87.62372) (xy 152.09359 87.62372)
				(xy 152.093587 87.623721) (xy 151.897714 87.687364) (xy 151.897708 87.687367) (xy 151.714193 87.780873)
				(xy 151.547567 87.901934) (xy 151.547564 87.901936) (xy 151.401936 88.047564) (xy 151.401934 88.047567)
				(xy 151.346649 88.123661) (xy 151.290427 88.167015) (xy 151.244713 88.1756) (xy 150.361461 88.1756)
				(xy 150.29334 88.155598) (xy 150.246847 88.101942) (xy 150.236743 88.031668) (xy 150.241628 88.010664)
				(xy 150.265791 87.936295) (xy 150.274074 87.884) (xy 148.901686 87.884) (xy 148.91008 87.875606)
				(xy 148.962741 87.784394) (xy 148.99 87.682661) (xy 148.99 87.577339) (xy 148.962741 87.475606)
				(xy 148.91008 87.384394) (xy 148.901686 87.376) (xy 150.274074 87.376) (xy 150.265791 87.323704)
				(xy 150.202173 87.127906) (xy 150.20217 87.1279) (xy 150.108699 86.944454) (xy 149.987684 86.777892)
				(xy 149.987682 86.777889) (xy 149.84211 86.632317) (xy 149.842107 86.632315) (xy 149.675545 86.5113)
				(xy 149.598938 86.472267) (xy 149.547323 86.423519) (xy 149.530257 86.354604) (xy 149.553158 86.287402)
				(xy 149.598938 86.247733) (xy 149.675545 86.208699) (xy 149.842107 86.087684) (xy 149.84211 86.087682)
				(xy 149.987682 85.94211) (xy 149.987684 85.942107) (xy 150.108699 85.775545) (xy 150.20217 85.592099)
				(xy 150.202173 85.592093) (xy 150.265791 85.396295) (xy 150.274074 85.344) (xy 148.901686 85.344)
				(xy 148.91008 85.335606) (xy 148.962741 85.244394) (xy 148.99 85.142661) (xy 148.99 85.037339) (xy 148.962741 84.935606)
				(xy 148.91008 84.844394) (xy 148.901686 84.836) (xy 150.274074 84.836) (xy 150.265791 84.783704)
				(xy 150.202173 84.587906) (xy 150.20217 84.5879) (xy 150.108699 84.404454) (xy 149.987684 84.237892)
				(xy 149.987682 84.237889) (xy 149.84211 84.092317) (xy 149.842107 84.092315) (xy 149.675542 83.971298)
				(xy 149.599487 83.932545) (xy 149.547872 83.883797) (xy 149.530807 83.814882) (xy 149.553708 83.74768)
				(xy 149.599485 83.708014) (xy 149.675803 83.669129) (xy 149.84243 83.548068) (xy 149.988068 83.40243)
				(xy 150.109129 83.235803) (xy 150.202634 83.05229) (xy 150.26628 82.856408) (xy 150.2985 82.652981)
				(xy 150.2985 82.447019) (xy 150.26628 82.243592) (xy 150.202634 82.04771) (xy 150.109129 81.864197)
				(xy 149.988068 81.69757) (xy 149.988065 81.697567) (xy 149.988063 81.697564) (xy 149.842435 81.551936)
				(xy 149.842432 81.551934) (xy 149.84243 81.551932) (xy 149.675803 81.430871) (xy 149.675285 81.430607)
				(xy 149.675123 81.430454) (xy 149.671585 81.428286) (xy 149.67204 81.427542) (xy 149.623675 81.381853)
				(xy 149.6065 81.318346) (xy 149.6065 79.8825) (xy 149.626502 79.814379) (xy 149.680158 79.767886)
				(xy 149.7325 79.7565) (xy 151.029884 79.7565) (xy 151.230116 79.7565) (xy 151.37239 79.7565) (xy 151.440511 79.776502)
				(xy 151.461485 79.793405) (xy 151.482421 79.814341) (xy 151.488834 79.819) (xy 151.661785 79.944657)
				(xy 151.859324 80.045308) (xy 152.070176 80.113818) (xy 152.289149 80.1485) (xy 152.289152 80.1485)
				(xy 152.510848 80.1485) (xy 152.510851 80.1485) (xy 152.729824 80.113818) (xy 152.940676 80.045308)
				(xy 153.138215 79.944657) (xy 153.317576 79.814343) (xy 153.474343 79.657576) (xy 153.604657 79.478215)
				(xy 153.705308 79.280676) (xy 153.773818 79.069824) (xy 153.8085 78.850851) (xy 153.8085 78.629149)
				(xy 153.773818 78.410176) (xy 153.705308 78.199324) (xy 153.604657 78.001785) (xy 153.532018 77.901806)
				(xy 153.474341 77.822421) (xy 153.317578 77.665658) (xy 153.138218 77.535345) (xy 153.138217 77.535344)
				(xy 153.138215 77.535343) (xy 152.940676 77.434692) (xy 152.940673 77.434691) (xy 152.940671 77.43469)
				(xy 152.729829 77.366183) (xy 152.729825 77.366182) (xy 152.729824 77.366182) (xy 152.510851 77.3315)
				(xy 152.289149 77.3315) (xy 152.289145 77.3315) (xy 152.284216 77.331888) (xy 152.284064 77.329969)
				(xy 152.221767 77.321901) (xy 152.167466 77.276164) (xy 152.146512 77.20833) (xy 152.1465 77.206566)
				(xy 152.1465 74.527999) (xy 152.166502 74.459878) (xy 152.170564 74.453938) (xy 152.249129 74.345803)
				(xy 152.342634 74.16229) (xy 152.40628 73.966408) (xy 152.4385 73.762981) (xy 152.4385 73.557019)
				(xy 152.414581 73.406) (xy 160.005926 73.406) (xy 160.978314 73.406) (xy 160.96992 73.414394) (xy 160.917259 73.505606)
				(xy 160.89 73.607339) (xy 160.89 73.712661) (xy 160.917259 73.814394) (xy 160.96992 73.905606) (xy 160.978314 73.914)
				(xy 160.005926 73.914) (xy 160.014208 73.966295) (xy 160.077826 74.162093) (xy 160.077829 74.162099)
				(xy 160.1713 74.345545) (xy 160.292315 74.512107) (xy 160.292317 74.51211) (xy 160.437889 74.657682)
				(xy 160.437892 74.657684) (xy 160.604454 74.778699) (xy 160.7879 74.87217) (xy 160.787906 74.872173)
				(xy 160.983705 74.935791) (xy 160.983717 74.935794) (xy 161.035999 74.944074) (xy 161.036 74.944074)
				(xy 161.036 73.971686) (xy 161.044394 73.98008) (xy 161.135606 74.032741) (xy 161.237339 74.06)
				(xy 161.342661 74.06) (xy 161.444394 74.032741) (xy 161.535606 73.98008) (xy 161.544 73.971686)
				(xy 161.544 74.944074) (xy 161.596282 74.935794) (xy 161.596294 74.935791) (xy 161.792093 74.872173)
				(xy 161.792099 74.87217) (xy 161.975545 74.778699) (xy 162.142107 74.657684) (xy 162.14211 74.657682)
				(xy 162.287682 74.51211) (xy 162.287684 74.512107) (xy 162.408699 74.345545) (xy 162.50217 74.162099)
				(xy 162.502173 74.162093) (xy 162.565791 73.966295) (xy 162.574074 73.914) (xy 161.601686 73.914)
				(xy 161.61008 73.905606) (xy 161.662741 73.814394) (xy 161.69 73.712661) (xy 161.69 73.607339) (xy 161.662741 73.505606)
				(xy 161.61008 73.414394) (xy 161.601686 73.406) (xy 162.574074 73.406) (xy 162.565791 73.353704)
				(xy 162.502173 73.157906) (xy 162.50217 73.1579) (xy 162.408699 72.974454) (xy 162.287684 72.807892)
				(xy 162.287682 72.807889) (xy 162.14211 72.662317) (xy 162.142107 72.662315) (xy 161.975545 72.5413)
				(xy 161.792099 72.447829) (xy 161.792093 72.447826) (xy 161.596294 72.384208) (xy 161.596296 72.384208)
				(xy 161.544 72.375925) (xy 161.544 73.348314) (xy 161.535606 73.33992) (xy 161.444394 73.287259)
				(xy 161.342661 73.26) (xy 161.237339 73.26) (xy 161.135606 73.287259) (xy 161.044394 73.33992) (xy 161.036 73.348314)
				(xy 161.036 72.375925) (xy 160.983704 72.384208) (xy 160.787906 72.447826) (xy 160.7879 72.447829)
				(xy 160.604454 72.5413) (xy 160.437892 72.662315) (xy 160.437889 72.662317) (xy 160.292317 72.807889)
				(xy 160.292315 72.807892) (xy 160.1713 72.974454) (xy 160.077829 73.1579) (xy 160.077826 73.157906)
				(xy 160.014208 73.353704) (xy 160.005926 73.406) (xy 152.414581 73.406) (xy 152.40628 73.353592)
				(xy 152.342634 73.15771) (xy 152.249129 72.974197) (xy 152.128068 72.80757) (xy 152.128065 72.807567)
				(xy 152.128063 72.807564) (xy 151.982435 72.661936) (xy 151.982432 72.661934) (xy 151.98243 72.661932)
				(xy 151.834837 72.5547) (xy 151.815806 72.540873) (xy 151.815805 72.540872) (xy 151.815803 72.540871)
				(xy 151.63229 72.447366) (xy 151.632287 72.447365) (xy 151.632285 72.447364) (xy 151.436412 72.383721)
				(xy 151.43641 72.38372) (xy 151.436408 72.38372) (xy 151.232981 72.3515) (xy 151.027019 72.3515)
				(xy 150.823592 72.38372) (xy 150.82359 72.38372) (xy 150.823587 72.383721) (xy 150.627714 72.447364)
				(xy 150.627708 72.447367) (xy 150.444193 72.540873) (xy 150.277567 72.661934) (xy 150.277564 72.661936)
				(xy 150.131936 72.807564) (xy 150.131934 72.807567) (xy 150.010873 72.974193) (xy 149.917367 73.157708)
				(xy 149.917364 73.157714) (xy 149.853721 73.353587) (xy 149.85372 73.35359) (xy 149.85372 73.353592)
				(xy 149.8215 73.557019) (xy 149.8215 73.762981) (xy 149.853684 73.966181) (xy 149.853721 73.966412)
				(xy 149.86413 73.998449) (xy 149.917366 74.16229) (xy 149.917367 74.162291) (xy 150.010873 74.345806)
				(xy 150.089436 74.453938) (xy 150.113294 74.520805) (xy 150.1135 74.527999) (xy 150.1135 77.5975)
				(xy 150.093498 77.665621) (xy 150.039842 77.712114) (xy 149.9875 77.7235) (xy 149.12488 77.7235)
				(xy 148.928497 77.762564) (xy 148.928494 77.762565) (xy 148.743507 77.83919) (xy 148.577017 77.950434)
				(xy 148.577016 77.950435) (xy 148.413965 78.113487) (xy 147.942019 78.585433) (xy 147.898303 78.629149)
				(xy 147.800435 78.727016) (xy 147.800434 78.727017) (xy 147.68919 78.893507) (xy 147.612565 79.078494)
				(xy 147.612564 79.078497) (xy 147.5735 79.27488) (xy 147.5735 81.318346) (xy 147.553498 81.386467)
				(xy 147.50801 81.427625) (xy 147.508415 81.428286) (xy 147.504899 81.43044) (xy 147.504715 81.430607)
				(xy 147.504196 81.430871) (xy 147.337567 81.551934) (xy 147.337564 81.551936) (xy 147.191936 81.697564)
				(xy 147.191934 81.697567) (xy 147.070873 81.864193) (xy 146.977367 82.047708) (xy 146.977364 82.047714)
				(xy 146.913721 82.243587) (xy 146.91372 82.24359) (xy 146.91372 82.243592) (xy 146.8815 82.447019)
				(xy 146.8815 82.652981) (xy 146.912734 82.850182) (xy 146.913721 82.856412) (xy 146.975396 83.046229)
				(xy 146.977366 83.05229) (xy 147.070871 83.235803) (xy 147.191932 83.40243) (xy 147.191934 83.402432)
				(xy 147.191936 83.402435) (xy 147.337564 83.548063) (xy 147.337567 83.548065) (xy 147.33757 83.548068)
				(xy 147.504197 83.669129) (xy 147.580511 83.708013) (xy 147.632126 83.75676) (xy 147.649192 83.825675)
				(xy 147.626292 83.892876) (xy 147.580513 83.932545) (xy 147.504455 83.971299) (xy 147.337892 84.092315)
				(xy 147.337889 84.092317) (xy 147.192317 84.237889) (xy 147.192315 84.237892) (xy 147.0713 84.404454)
				(xy 146.977829 84.5879) (xy 146.977826 84.587906) (xy 146.914208 84.783704) (xy 146.905926 84.836)
				(xy 148.278314 84.836) (xy 148.26992 84.844394) (xy 148.217259 84.935606) (xy 148.19 85.037339)
				(xy 148.19 85.142661) (xy 148.217259 85.244394) (xy 148.26992 85.335606) (xy 148.278314 85.344)
				(xy 146.905926 85.344) (xy 146.914208 85.396295) (xy 146.977826 85.592093) (xy 146.977829 85.592099)
				(xy 147.0713 85.775545) (xy 147.192315 85.942107) (xy 147.192317 85.94211) (xy 147.337889 86.087682)
				(xy 147.337892 86.087684) (xy 147.504454 86.208699) (xy 147.581061 86.247733) (xy 147.632676 86.296482)
				(xy 147.649742 86.365397) (xy 147.626841 86.432598) (xy 147.581061 86.472267) (xy 147.504454 86.5113)
				(xy 147.337892 86.632315) (xy 147.337889 86.632317) (xy 147.192317 86.777889) (xy 147.192315 86.777892)
				(xy 147.0713 86.944454) (xy 146.977829 87.1279) (xy 146.977826 87.127906) (xy 146.914208 87.323704)
				(xy 146.905926 87.376) (xy 148.278314 87.376) (xy 148.26992 87.384394) (xy 148.217259 87.475606)
				(xy 148.19 87.577339) (xy 148.19 87.682661) (xy 148.217259 87.784394) (xy 148.26992 87.875606) (xy 148.278314 87.884)
				(xy 146.905926 87.884) (xy 146.914208 87.936295) (xy 146.938372 88.010664) (xy 146.9404 88.081631)
				(xy 146.903737 88.142429) (xy 146.840025 88.173755) (xy 146.818539 88.1756) (xy 145.91093 88.1756)
				(xy 145.881014 88.166816) (xy 145.850545 88.160188) (xy 145.845449 88.156373) (xy 145.842809 88.155598)
				(xy 145.821835 88.138695) (xy 145.642905 87.959765) (xy 145.608879 87.897453) (xy 145.606 87.87067)
				(xy 145.606 86.278647) (xy 145.605999 86.27864) (xy 145.597278 86.234797) (xy 145.574258 86.119065)
				(xy 145.561419 86.088068) (xy 145.511992 85.968741) (xy 145.462787 85.895102) (xy 145.462787 85.895101)
				(xy 145.462786 85.895101) (xy 145.421596 85.833455) (xy 145.306545 85.718404) (xy 145.306544 85.718403)
				(xy 145.303383 85.715242) (xy 145.303368 85.715228) (xy 144.156006 84.567866) (xy 144.155997 84.567856)
				(xy 144.140132 84.551991) (xy 144.036545 84.448404) (xy 143.901258 84.358008) (xy 143.750935 84.295742)
				(xy 143.70534 84.286672) (xy 143.591359 84.264) (xy 143.591354 84.264) (xy 142.446689 84.264) (xy 142.417958 84.255564)
				(xy 142.388575 84.249798) (xy 142.382058 84.245023) (xy 142.378568 84.243998) (xy 142.359432 84.228896)
				(xy 142.358482 84.227984) (xy 142.22243 84.091932) (xy 142.199096 84.074979) (xy 142.192873 84.069005)
				(xy 142.179341 84.045384) (xy 142.162715 84.023822) (xy 142.161961 84.015045) (xy 142.157582 84.007401)
				(xy 142.158969 83.980209) (xy 142.15664 83.953086) (xy 142.160751 83.945294) (xy 142.1612 83.936497)
				(xy 142.177065 83.914375) (xy 142.189772 83.890294) (xy 142.197443 83.885961) (xy 142.202577 83.878804)
				(xy 142.227882 83.868772) (xy 142.251592 83.855383) (xy 142.266663 83.852831) (xy 142.279093 83.851494)
				(xy 142.415964 83.800444) (xy 142.415965 83.800444) (xy 142.532904 83.712904) (xy 142.620444 83.595965)
				(xy 142.620444 83.595964) (xy 142.671494 83.459093) (xy 142.677999 83.398597) (xy 142.678 83.398585)
				(xy 142.678 82.804) (xy 141.281686 82.804) (xy 141.29008 82.795606) (xy 141.342741 82.704394) (xy 141.37 82.602661)
				(xy 141.37 82.497339) (xy 141.342741 82.395606) (xy 141.29008 82.304394) (xy 141.281686 82.296)
				(xy 142.678 82.296) (xy 142.678 81.701414) (xy 142.677999 81.701402) (xy 142.671494 81.640906) (xy 142.620444 81.504035)
				(xy 142.620444 81.504034) (xy 142.532904 81.387095) (xy 142.415965 81.299555) (xy 142.279093 81.248505)
				(xy 142.218597 81.242) (xy 141.224 81.242) (xy 141.224 82.238314) (xy 141.215606 82.22992) (xy 141.124394 82.177259)
				(xy 141.022661 82.15) (xy 140.917339 82.15) (xy 140.815606 82.177259) (xy 140.724394 82.22992) (xy 140.716 82.238314)
				(xy 140.716 81.242) (xy 139.721402 81.242) (xy 139.660906 81.248505) (xy 139.524035 81.299555) (xy 139.524034 81.299556)
				(xy 139.457509 81.349356) (xy 139.390988 81.374167) (xy 139.321614 81.359075) (xy 139.271412 81.308873)
				(xy 139.256 81.248488) (xy 139.256 76.59433) (xy 139.264783 76.564414) (xy 139.271412 76.533945)
				(xy 139.275226 76.528849) (xy 139.276002 76.526209) (xy 139.292905 76.505235) (xy 139.85214 75.946)
				(xy 145.835926 75.946) (xy 146.808314 75.946) (xy 146.79992 75.954394) (xy 146.747259 76.045606)
				(xy 146.72 76.147339) (xy 146.72 76.252661) (xy 146.747259 76.354394) (xy 146.79992 76.445606) (xy 146.808314 76.454)
				(xy 145.835926 76.454) (xy 145.844208 76.506295) (xy 145.907826 76.702093) (xy 145.907829 76.702099)
				(xy 146.0013 76.885545) (xy 146.122315 77.052107) (xy 146.122317 77.05211) (xy 146.267889 77.197682)
				(xy 146.267892 77.197684) (xy 146.434454 77.318699) (xy 146.6179 77.41217) (xy 146.617906 77.412173)
				(xy 146.813705 77.475791) (xy 146.813717 77.475794) (xy 146.865999 77.484074) (xy 146.866 77.484074)
				(xy 146.866 76.511686) (xy 146.874394 76.52008) (xy 146.965606 76.572741) (xy 147.067339 76.6) (xy 147.172661 76.6)
				(xy 147.274394 76.572741) (xy 147.365606 76.52008) (xy 147.374 76.511686) (xy 147.374 77.484074)
				(xy 147.426282 77.475794) (xy 147.426294 77.475791) (xy 147.622093 77.412173) (xy 147.622099 77.41217)
				(xy 147.805545 77.318699) (xy 147.972107 77.197684) (xy 147.97211 77.197682) (xy 148.117682 77.05211)
				(xy 148.117684 77.052107) (xy 148.238699 76.885545) (xy 148.33217 76.702099) (xy 148.332173 76.702093)
				(xy 148.395791 76.506295) (xy 148.404074 76.454) (xy 147.431686 76.454) (xy 147.44008 76.445606)
				(xy 147.492741 76.354394) (xy 147.52 76.252661) (xy 147.52 76.147339) (xy 147.492741 76.045606)
				(xy 147.44008 75.954394) (xy 147.431686 75.946) (xy 148.404074 75.946) (xy 148.395791 75.893704)
				(xy 148.332173 75.697906) (xy 148.33217 75.6979) (xy 148.238699 75.514454) (xy 148.117684 75.347892)
				(xy 148.117682 75.347889) (xy 147.97211 75.202317) (xy 147.972107 75.202315) (xy 147.805545 75.0813)
				(xy 147.622099 74.987829) (xy 147.622093 74.987826) (xy 147.426294 74.924208) (xy 147.426296 74.924208)
				(xy 147.374 74.915925) (xy 147.374 75.888314) (xy 147.365606 75.87992) (xy 147.274394 75.827259)
				(xy 147.172661 75.8) (xy 147.067339 75.8) (xy 146.965606 75.827259) (xy 146.874394 75.87992) (xy 146.866 75.888314)
				(xy 146.866 74.915925) (xy 146.813704 74.924208) (xy 146.617906 74.987826) (xy 146.6179 74.987829)
				(xy 146.434454 75.0813) (xy 146.267892 75.202315) (xy 146.267889 75.202317) (xy 146.122317 75.347889)
				(xy 146.122315 75.347892) (xy 146.0013 75.514454) (xy 145.907829 75.6979) (xy 145.907826 75.697906)
				(xy 145.844208 75.893704) (xy 145.835926 75.946) (xy 139.85214 75.946) (xy 142.545235 73.252905)
				(xy 142.607547 73.218879) (xy 142.63433 73.216) (xy 146.243311 73.216) (xy 146.311432 73.236002)
				(xy 146.332406 73.252905) (xy 146.467564 73.388063) (xy 146.467567 73.388065) (xy 146.46757 73.388068)
				(xy 146.634197 73.509129) (xy 146.81771 73.602634) (xy 147.013592 73.66628) (xy 147.217019 73.6985)
				(xy 147.217022 73.6985) (xy 147.422978 73.6985) (xy 147.422981 73.6985) (xy 147.626408 73.66628)
				(xy 147.82229 73.602634) (xy 148.005803 73.509129) (xy 148.17243 73.388068) (xy 148.318068 73.24243)
				(xy 148.439129 73.075803) (xy 148.532634 72.89229) (xy 148.59628 72.696408) (xy 148.6285 72.492981)
				(xy 148.6285 72.287019) (xy 148.59628 72.083592) (xy 148.532634 71.88771) (xy 148.439129 71.704197)
				(xy 148.318068 71.53757) (xy 148.318065 71.537567) (xy 148.318063 71.537564) (xy 148.182905 71.402406)
				(xy 148.148879 71.340094) (xy 148.146 71.313311) (xy 148.146 70.87933) (xy 148.154783 70.849414)
				(xy 148.161412 70.818945) (xy 148.165226 70.813849) (xy 148.166002 70.811209) (xy 148.182905 70.790235)
				(xy 148.260235 70.712905) (xy 148.322547 70.678879) (xy 148.34933 70.676) (xy 150.053311 70.676)
				(xy 150.121432 70.696002) (xy 150.142406 70.712905) (xy 150.277564 70.848063) (xy 150.277567 70.848065)
				(xy 150.27757 70.848068) (xy 150.444197 70.969129) (xy 150.62771 71.062634) (xy 150.823592 71.12628)
				(xy 151.027019 71.1585) (xy 151.027022 71.1585) (xy 151.232978 71.1585) (xy 151.232981 71.1585)
				(xy 151.436408 71.12628) (xy 151.63229 71.062634) (xy 151.815803 70.969129) (xy 151.98243 70.848068)
				(xy 152.003358 70.82714) (xy 152.030033 70.800466) (xy 152.128063 70.702435) (xy 152.128068 70.70243)
				(xy 152.249129 70.535803) (xy 152.342634 70.35229) (xy 152.40628 70.156408) (xy 152.4385 69.952981)
				(xy 152.4385 69.747061) (xy 154.822 69.747061) (xy 154.822 69.952938) (xy 154.854208 70.156296)
				(xy 154.917826 70.352093) (xy 154.917829 70.352099) (xy 155.011297 70.53554) (xy 155.011298 70.535541)
				(xy 155.042416 70.578372) (xy 155.73 69.890788) (xy 155.73 69.902661) (xy 155.757259 70.004394)
				(xy 155.80992 70.095606) (xy 155.884394 70.17008) (xy 155.975606 70.222741) (xy 156.077339 70.25)
				(xy 156.08921 70.25) (xy 155.401626 70.937582) (xy 155.401626 70.937583) (xy 155.444454 70.968699)
				(xy 155.6279 71.06217) (xy 155.627906 71.062173) (xy 155.823705 71.125791) (xy 155.823701 71.125791)
				(xy 156.027061 71.158) (xy 156.232939 71.158) (xy 156.436296 71.125791) (xy 156.632093 71.062173)
				(xy 156.632099 71.06217) (xy 156.815541 70.968701) (xy 156.858372 70.937582) (xy 156.858372 70.937581)
				(xy 156.170791 70.25) (xy 156.182661 70.25) (xy 156.284394 70.222741) (xy 156.375606 70.17008) (xy 156.45008 70.095606)
				(xy 156.502741 70.004394) (xy 156.53 69.902661) (xy 156.53 69.890791) (xy 157.217581 70.578372)
				(xy 157.217582 70.578372) (xy 157.248701 70.535541) (xy 157.34217 70.352099) (xy 157.342173 70.352093)
				(xy 157.405791 70.156296) (xy 157.438 69.952938) (xy 157.438 69.747061) (xy 157.405791 69.543703)
				(xy 157.342173 69.347906) (xy 157.34217 69.3479) (xy 157.248699 69.164454) (xy 157.217583 69.121626)
				(xy 157.217582 69.121626) (xy 156.53 69.809208) (xy 156.53 69.797339) (xy 156.502741 69.695606)
				(xy 156.45008 69.604394) (xy 156.375606 69.52992) (xy 156.284394 69.477259) (xy 156.182661 69.45)
				(xy 156.17079 69.45) (xy 156.858372 68.762416) (xy 156.815541 68.731298) (xy 156.81554 68.731297)
				(xy 156.632099 68.637829) (xy 156.632093 68.637826) (xy 156.436294 68.574208) (xy 156.436298 68.574208)
				(xy 156.232939 68.542) (xy 156.027061 68.542) (xy 155.823703 68.574208) (xy 155.627906 68.637826)
				(xy 155.6279 68.637829) (xy 155.444452 68.731301) (xy 155.401625 68.762416) (xy 156.089209 69.45)
				(xy 156.077339 69.45) (xy 155.975606 69.477259) (xy 155.884394 69.52992) (xy 155.80992 69.604394)
				(xy 155.757259 69.695606) (xy 155.73 69.797339) (xy 155.73 69.809209) (xy 155.042416 69.121625)
				(xy 155.011301 69.164452) (xy 154.917829 69.3479) (xy 154.917826 69.347906) (xy 154.854208 69.543703)
				(xy 154.822 69.747061) (xy 152.4385 69.747061) (xy 152.4385 69.747019) (xy 152.40628 69.543592)
				(xy 152.342634 69.34771) (xy 152.249129 69.164197) (xy 152.128068 68.99757) (xy 152.128065 68.997567)
				(xy 152.128063 68.997564) (xy 151.982435 68.851936) (xy 151.982432 68.851934) (xy 151.98243 68.851932)
				(xy 151.816391 68.731298) (xy 151.815806 68.730873) (xy 151.815805 68.730872) (xy 151.815803 68.730871)
				(xy 151.63229 68.637366) (xy 151.632287 68.637365) (xy 151.632285 68.637364) (xy 151.436412 68.573721)
				(xy 151.43641 68.57372) (xy 151.436408 68.57372) (xy 151.232981 68.5415) (xy 151.027019 68.5415)
				(xy 150.823592 68.57372) (xy 150.82359 68.57372) (xy 150.823587 68.573721) (xy 150.627714 68.637364)
				(xy 150.627708 68.637367) (xy 150.444193 68.730873) (xy 150.277567 68.851934) (xy 150.277564 68.851936)
				(xy 150.142406 68.987095) (xy 150.080094 69.021121) (xy 150.053311 69.024) (xy 148.042587 69.024)
				(xy 148.042567 69.023999) (xy 148.036354 69.023999) (xy 147.873647 69.023999) (xy 147.873644 69.023999)
				(xy 147.714064 69.055742) (xy 147.714061 69.055743) (xy 147.563742 69.118008) (xy 147.428459 69.208401)
				(xy 147.428452 69.208406) (xy 146.678406 69.958452) (xy 146.678401 69.958459) (xy 146.588008 70.093742)
				(xy 146.534575 70.222741) (xy 146.525742 70.244065) (xy 146.521207 70.266862) (xy 146.494 70.40364)
				(xy 146.494 71.313311) (xy 146.485216 71.343226) (xy 146.478588 71.373696) (xy 146.474773 71.378791)
				(xy 146.473998 71.381432) (xy 146.457095 71.402406) (xy 146.332406 71.527095) (xy 146.270094 71.561121)
				(xy 146.243311 71.564) (xy 142.327587 71.564) (xy 142.327567 71.563999) (xy 142.321354 71.563999)
				(xy 142.158647 71.563999) (xy 142.158644 71.563999) (xy 141.999064 71.595742) (xy 141.999061 71.595743)
				(xy 141.848742 71.658008) (xy 141.713459 71.748401) (xy 141.713452 71.748406) (xy 137.788406 75.673452)
				(xy 137.788401 75.673459) (xy 137.698008 75.808742) (xy 137.635744 75.959059) (xy 137.635742 75.959066)
				(xy 137.604 76.11864) (xy 137.604 83.426669) (xy 137.583998 83.49479) (xy 137.530342 83.541283)
				(xy 137.460068 83.551387) (xy 137.395488 83.521893) (xy 137.388905 83.515764) (xy 137.051549 83.178408)
				(xy 137.051543 83.178403) (xy 136.997454 83.142262) (xy 136.916258 83.088008) (xy 136.765935 83.025742)
				(xy 136.72034 83.016672) (xy 136.606358 82.994) (xy 136.606353 82.994) (xy 136.443647 82.994) (xy 136.443646 82.994)
				(xy 136.437483 82.994607) (xy 136.437292 82.992672) (xy 136.375552 82.987133) (xy 136.319496 82.943564)
				(xy 136.295893 82.876606) (xy 136.297264 82.850192) (xy 136.3285 82.652981) (xy 136.3285 82.447019)
				(xy 136.29628 82.243592) (xy 136.232634 82.04771) (xy 136.139129 81.864197) (xy 136.018068 81.69757)
				(xy 136.018065 81.697567) (xy 136.018063 81.697564) (xy 135.872435 81.551936) (xy 135.872432 81.551934)
				(xy 135.87243 81.551932) (xy 135.705803 81.430871) (xy 135.705285 81.430607) (xy 135.705123 81.430454)
				(xy 135.701585 81.428286) (xy 135.70204 81.427542) (xy 135.653675 81.381853) (xy 135.6365 81.318346)
				(xy 135.6365 77.067999) (xy 135.656502 76.999878) (xy 135.660564 76.993938) (xy 135.739129 76.885803)
				(xy 135.832634 76.70229) (xy 135.89628 76.506408) (xy 135.9285 76.302981) (xy 135.9285 76.097019)
				(xy 135.89628 75.893592) (xy 135.832634 75.69771) (xy 135.739129 75.514197) (xy 135.660563 75.40606)
				(xy 135.636705 75.339193) (xy 135.6365 75.332) (xy 135.6365 73.876749) (xy 135.656502 73.808628)
				(xy 135.710158 73.762135) (xy 135.718454 73.758698) (xy 135.766204 73.740889) (xy 135.883261 73.653261)
				(xy 135.970889 73.536204) (xy 135.988695 73.488466) (xy 136.002179 73.470453) (xy 136.011526 73.449988)
				(xy 136.023038 73.442589) (xy 136.031242 73.431631) (xy 136.052325 73.423767) (xy 136.071252 73.411604)
				(xy 136.093523 73.408401) (xy 136.097762 73.406821) (xy 136.10675 73.4065) (xy 136.292 73.4065)
				(xy 136.360121 73.426502) (xy 136.366061 73.430564) (xy 136.474197 73.509129) (xy 136.65771 73.602634)
				(xy 136.853592 73.66628) (xy 137.057019 73.6985) (xy 137.057022 73.6985) (xy 137.262978 73.6985)
				(xy 137.262981 73.6985) (xy 137.466408 73.66628) (xy 137.66229 73.602634) (xy 137.845803 73.509129)
				(xy 138.01243 73.388068) (xy 138.158068 73.24243) (xy 138.279129 73.075803) (xy 138.372634 72.89229)
				(xy 138.43628 72.696408) (xy 138.4685 72.492981) (xy 138.4685 72.287019) (xy 138.43628 72.083592)
				(xy 138.372634 71.88771) (xy 138.279129 71.704197) (xy 138.158068 71.53757) (xy 138.158065 71.537567)
				(xy 138.158063 71.537564) (xy 138.012435 71.391936) (xy 138.012432 71.391934) (xy 138.01243 71.391932)
				(xy 137.845803 71.270871) (xy 137.66229 71.177366) (xy 137.662287 71.177365) (xy 137.662285 71.177364)
				(xy 137.466412 71.113721) (xy 137.46641 71.11372) (xy 137.466408 71.11372) (xy 137.262981 71.0815)
				(xy 137.057019 71.0815) (xy 136.853592 71.11372) (xy 136.85359 71.11372) (xy 136.853587 71.113721)
				(xy 136.657714 71.177364) (xy 136.657708 71.177367) (xy 136.474193 71.270873) (xy 136.366061 71.349436)
				(xy 136.34569 71.356704) (xy 136.327498 71.368396) (xy 136.303085 71.371906) (xy 136.299193 71.373295)
				(xy 136.292 71.3735) (xy 136.10675 71.3735) (xy 136.038629 71.353498) (xy 135.992136 71.299842)
				(xy 135.988695 71.291534) (xy 135.970888 71.243794) (xy 135.883261 71.126738) (xy 135.766207 71.039112)
				(xy 135.766202 71.03911) (xy 135.629204 70.988011) (xy 135.629196 70.988009) (xy 135.568649 70.9815)
				(xy 135.568638 70.9815) (xy 133.671362 70.9815) (xy 133.67135 70.9815) (xy 133.610803 70.988009)
				(xy 133.610795 70.988011) (xy 133.473797 71.03911) (xy 133.473792 71.039112) (xy 133.356738 71.126738)
				(xy 133.269112 71.243792) (xy 133.26911 71.243797) (xy 133.218011 71.380795) (xy 133.218009 71.380803)
				(xy 133.2115 71.44135) (xy 133.2115 73.338632) (xy 133.211501 73.338647) (xy 133.218009 73.399196)
				(xy 133.218011 73.399204) (xy 133.26911 73.536202) (xy 133.269112 73.536207) (xy 133.356738 73.653261)
				(xy 133.473792 73.740887) (xy 133.473794 73.740887) (xy 133.473796 73.740889) (xy 133.521533 73.758694)
				(xy 133.578368 73.80124) (xy 133.603179 73.86776) (xy 133.6035 73.876749) (xy 133.6035 75.332) (xy 133.583498 75.400121)
				(xy 133.579436 75.406061) (xy 133.500873 75.514193) (xy 133.407367 75.697708) (xy 133.407364 75.697714)
				(xy 133.343721 75.893587) (xy 133.34372 75.89359) (xy 133.34372 75.893592) (xy 133.3115 76.097019)
				(xy 133.3115 76.302981) (xy 133.343702 76.506295) (xy 133.343721 76.506412) (xy 133.405531 76.696644)
				(xy 133.407366 76.70229) (xy 133.500871 76.885803) (xy 133.534428 76.931991) (xy 133.579436 76.993938)
				(xy 133.603294 77.060805) (xy 133.6035 77.067999) (xy 133.6035 81.318346) (xy 133.583498 81.386467)
				(xy 133.53801 81.427625) (xy 133.538415 81.428286) (xy 133.534899 81.43044) (xy 133.534715 81.430607)
				(xy 133.534196 81.430871) (xy 133.367567 81.551934) (xy 133.367564 81.551936) (xy 133.221936 81.697564)
				(xy 133.221934 81.697567) (xy 133.133936 81.818686) (xy 133.077714 81.86204) (xy 133.006977 81.868115)
				(xy 132.944186 81.834983) (xy 132.909274 81.773163) (xy 132.906 81.744625) (xy 132.906 79.462587)
				(xy 132.906001 79.462566) (xy 132.906001 79.293648) (xy 132.906 79.293644) (xy 132.903421 79.280677)
				(xy 132.874258 79.134065) (xy 132.811992 78.983742) (xy 132.762386 78.909502) (xy 132.721596 78.848455)
				(xy 132.721592 78.848451) (xy 132.721589 78.848447) (xy 132.603399 78.730257) (xy 132.603368 78.730228)
				(xy 132.091006 78.217866) (xy 132.090997 78.217856) (xy 132.072463 78.199322) (xy 131.971545 78.098404)
				(xy 131.836258 78.008008) (xy 131.697265 77.950435) (xy 131.68594 77.945744) (xy 131.685939 77.945743)
				(xy 131.685935 77.945742) (xy 131.627461 77.934111) (xy 131.526359 77.914) (xy 131.526354 77.914)
				(xy 124.854331 77.914) (xy 124.78621 77.893998) (xy 124.765236 77.877095) (xy 123.584784 76.696644)
				(xy 123.584779 76.696638) (xy 123.577596 76.689455) (xy 123.462545 76.574404) (xy 123.327258 76.484008)
				(xy 123.176935 76.421742) (xy 123.13134 76.412672) (xy 123.017359 76.39) (xy 123.017354 76.39) (xy 122.996689 76.39)
				(xy 122.928568 76.369998) (xy 122.907594 76.353095) (xy 122.772435 76.217936) (xy 122.772432 76.217934)
				(xy 122.77243 76.217932) (xy 122.646999 76.126802) (xy 122.605806 76.096873) (xy 122.605805 76.096872)
				(xy 122.605803 76.096871) (xy 122.42229 76.003366) (xy 122.422287 76.003365) (xy 122.422285 76.003364)
				(xy 122.226412 75.939721) (xy 122.22641 75.93972) (xy 122.226408 75.93972) (xy 122.022981 75.9075)
				(xy 121.817019 75.9075) (xy 121.613592 75.93972) (xy 121.61359 75.93972) (xy 121.613587 75.939721)
				(xy 121.417714 76.003364) (xy 121.417708 76.003367) (xy 121.234193 76.096873) (xy 121.067567 76.217934)
				(xy 121.067564 76.217936) (xy 120.921936 76.363564) (xy 120.921934 76.363567) (xy 120.800873 76.530193)
				(xy 120.707367 76.713708) (xy 120.707364 76.713714) (xy 120.643721 76.909587) (xy 120.64372 76.90959)
				(xy 120.64372 76.909592) (xy 120.6115 77.113019) (xy 113.0685 77.113019) (xy 113.03628 76.909592)
				(xy 112.972634 76.71371) (xy 112.879129 76.530197) (xy 112.758068 76.36357) (xy 112.758065 76.363567)
				(xy 112.758063 76.363564) (xy 112.612435 76.217936) (xy 112.612432 76.217934) (xy 112.61243 76.217932)
				(xy 112.486999 76.126802) (xy 112.445806 76.096873) (xy 112.445805 76.096872) (xy 112.445803 76.096871)
				(xy 112.26229 76.003366) (xy 112.262287 76.003365) (xy 112.262285 76.003364) (xy 112.066412 75.939721)
				(xy 112.06641 75.93972) (xy 112.066408 75.93972) (xy 111.862981 75.9075) (xy 111.657019 75.9075)
				(xy 111.453592 75.93972) (xy 111.45359 75.93972) (xy 111.453587 75.939721) (xy 111.257714 76.003364)
				(xy 111.257708 76.003367) (xy 111.074194 76.096872) (xy 110.980793 76.164732) (xy 110.913925 76.18859)
				(xy 110.844774 76.172509) (xy 110.795294 76.121594) (xy 110.781195 76.052012) (xy 110.806954 75.985853)
				(xy 110.817623 75.973715) (xy 111.582736 75.208602) (xy 111.645046 75.174579) (xy 111.671829 75.1717)
				(xy 111.862978 75.1717) (xy 111.862981 75.1717) (xy 112.066408 75.13948) (xy 112.26229 75.075834)
				(xy 112.445803 74.982329) (xy 112.61243 74.861268) (xy 112.758068 74.71563) (xy 112.879129 74.549003)
				(xy 112.972634 74.36549) (xy 113.03628 74.169608) (xy 113.0685 73.966181) (xy 113.0685 73.760219)
				(xy 120.6115 73.760219) (xy 120.6115 73.966181) (xy 120.64372 74.169608) (xy 120.707366 74.36549)
				(xy 120.800871 74.549003) (xy 120.921932 74.71563) (xy 120.921934 74.715632) (xy 120.921936 74.715635)
				(xy 121.067564 74.861263) (xy 121.067567 74.861265) (xy 121.06757 74.861268) (xy 121.234197 74.982329)
				(xy 121.41771 75.075834) (xy 121.613592 75.13948) (xy 121.817019 75.1717) (xy 121.817022 75.1717)
				(xy 122.022978 75.1717) (xy 122.022981 75.1717) (xy 122.226408 75.13948) (xy 122.42229 75.075834)
				(xy 122.605803 74.982329) (xy 122.77243 74.861268) (xy 122.918068 74.71563) (xy 123.039129 74.549003)
				(xy 123.132634 74.36549) (xy 123.19628 74.169608) (xy 123.2285 73.966181) (xy 123.2285 73.760219)
				(xy 123.19628 73.556792) (xy 123.150012 73.414394) (xy 123.139141 73.380936) (xy 123.137113 73.309969)
				(xy 123.173776 73.249171) (xy 123.237488 73.217845) (xy 123.258974 73.216) (xy 125.794923 73.216)
				(xy 125.863044 73.236002) (xy 125.896859 73.267939) (xy 125.925658 73.307578) (xy 126.082421 73.464341)
				(xy 126.115626 73.488466) (xy 126.261785 73.594657) (xy 126.459324 73.695308) (xy 126.670176 73.763818)
				(xy 126.889149 73.7985) (xy 126.889152 73.7985) (xy 127.110848 73.7985) (xy 127.110851 73.7985)
				(xy 127.329824 73.763818) (xy 127.540676 73.695308) (xy 127.738215 73.594657) (xy 127.917576 73.464343)
				(xy 128.074343 73.307576) (xy 128.204657 73.128215) (xy 128.305308 72.930676) (xy 128.373818 72.719824)
				(xy 128.4085 72.500851) (xy 128.4085 72.279149) (xy 128.373818 72.060176) (xy 128.305308 71.849324)
				(xy 128.204657 71.651785) (xy 128.074343 71.472424) (xy 128.074341 71.472421) (xy 127.917578 71.315658)
				(xy 127.738218 71.185345) (xy 127.738217 71.185344) (xy 127.738215 71.185343) (xy 127.540676 71.084692)
				(xy 127.540673 71.084691) (xy 127.540671 71.08469) (xy 127.329829 71.016183) (xy 127.329825 71.016182)
				(xy 127.329824 71.016182) (xy 127.110851 70.9815) (xy 126.889149 70.9815) (xy 126.670176 71.016182)
				(xy 126.67017 71.016183) (xy 126.459328 71.08469) (xy 126.459322 71.084693) (xy 126.261781 71.185345)
				(xy 126.082421 71.315658) (xy 125.925658 71.472421) (xy 125.896859 71.512061) (xy 125.840636 71.555415)
				(xy 125.794923 71.564) (xy 122.642587 71.564) (xy 122.642567 71.563999) (xy 122.636354 71.563999)
				(xy 122.473647 71.563999) (xy 122.473644 71.563999) (xy 122.314064 71.595742) (xy 122.314061 71.595743)
				(xy 122.163742 71.658008) (xy 122.028459 71.748401) (xy 122.028452 71.748406) (xy 121.278406 72.498452)
				(xy 121.278401 72.498459) (xy 121.188008 72.633741) (xy 121.125741 72.784067) (xy 121.123944 72.789992)
				(xy 121.121984 72.789397) (xy 121.093407 72.84399) (xy 121.076829 72.858404) (xy 121.067569 72.865132)
				(xy 121.067562 72.865138) (xy 120.921936 73.010764) (xy 120.921934 73.010767) (xy 120.800873 73.177393)
				(xy 120.707367 73.360908) (xy 120.707364 73.360914) (xy 120.643721 73.556787) (xy 120.64372 73.55679)
				(xy 120.64372 73.556792) (xy 120.6115 73.760219) (xy 113.0685 73.760219) (xy 113.03628 73.556792)
				(xy 112.972634 73.36091) (xy 112.879129 73.177397) (xy 112.758068 73.01077) (xy 112.758065 73.010767)
				(xy 112.758063 73.010764) (xy 112.612435 72.865136) (xy 112.612432 72.865134) (xy 112.61243 72.865132)
				(xy 112.445803 72.744071) (xy 112.26229 72.650566) (xy 112.262287 72.650565) (xy 112.262285 72.650564)
				(xy 112.066412 72.586921) (xy 112.06641 72.58692) (xy 112.066408 72.58692) (xy 111.862981 72.5547)
				(xy 111.657019 72.5547) (xy 111.453592 72.58692) (xy 111.45359 72.58692) (xy 111.453587 72.586921)
				(xy 111.257714 72.650564) (xy 111.257708 72.650567) (xy 111.074193 72.744073) (xy 110.907567 72.865134)
				(xy 110.907564 72.865136) (xy 110.761936 73.010764) (xy 110.761934 73.010767) (xy 110.640873 73.177393)
				(xy 110.547367 73.360908) (xy 110.547364 73.360914) (xy 110.483721 73.556787) (xy 110.48372 73.55679)
				(xy 110.48372 73.556792) (xy 110.461276 73.6985) (xy 110.4515 73.760222) (xy 110.4515 73.951369)
				(xy 110.431498 74.01949) (xy 110.414595 74.040464) (xy 109.117965 75.337095) (xy 109.055653 75.37112)
				(xy 109.02887 75.374) (xy 107.699847 75.374) (xy 107.631726 75.353998) (xy 107.585233 75.300342)
				(xy 107.575129 75.230068) (xy 107.604623 75.165488) (xy 107.610752 75.158905) (xy 107.614859 75.154797)
				(xy 107.614859 75.154795) (xy 107.614861 75.154795) (xy 107.702489 75.037738) (xy 107.753589 74.900735)
				(xy 107.757833 74.861265) (xy 107.760099 74.840183) (xy 107.7601 74.840166) (xy 107.7601 72.896233)
				(xy 107.760099 72.896216) (xy 107.75359 72.835669) (xy 107.753588 72.835661) (xy 107.702489 72.698663)
				(xy 107.702487 72.698658) (xy 107.614861 72.581604) (xy 107.497807 72.493978) (xy 107.497802 72.493976)
				(xy 107.360804 72.442877) (xy 107.360796 72.442875) (xy 107.300249 72.436366) (xy 107.300238 72.436366)
				(xy 105.536 72.436366) (xy 105.467879 72.416364) (xy 105.421386 72.362708) (xy 105.41 72.310366)
				(xy 105.41 67.4465) (xy 105.430002 67.378379) (xy 105.483658 67.331886) (xy 105.536 67.3205) (xy 111.403528 67.3205)
			)
		)
		(filled_polygon
			(layer "B.Cu")
			(pts
				(xy 179.807883 79.649502) (xy 179.828857 79.666405) (xy 180.556595 80.394143) (xy 180.590621 80.456455)
				(xy 180.5935 80.483238) (xy 180.5935 83.021366) (xy 180.573498 83.089487) (xy 180.519842 83.13598)
				(xy 180.4675 83.147366) (xy 179.82135 83.147366) (xy 179.760803 83.153875) (xy 179.760795 83.153877)
				(xy 179.623797 83.204976) (xy 179.623792 83.204978) (xy 179.506738 83.292604) (xy 179.419112 83.409658)
				(xy 179.41911 83.409663) (xy 179.368011 83.546661) (xy 179.368009 83.546669) (xy 179.3615 83.607216)
				(xy 179.3615 85.551183) (xy 179.368009 85.61173) (xy 179.368011 85.611738) (xy 179.41911 85.748736)
				(xy 179.419112 85.748741) (xy 179.50674 85.865797) (xy 179.513111 85.872168) (xy 179.511368 85.87391)
				(xy 179.545997 85.920173) (xy 179.551058 85.990989) (xy 179.525795 86.043574) (xy 179.515953 86.055707)
				(xy 179.506739 86.062605) (xy 179.463409 86.120485) (xy 179.461878 86.122374) (xy 179.43459 86.14119)
				(xy 179.408054 86.161056) (xy 179.404978 86.16161) (xy 179.403431 86.162678) (xy 179.398103 86.162851)
				(xy 179.364022 86.169) (xy 178.82933 86.169) (xy 178.761209 86.148998) (xy 178.740235 86.132095)
				(xy 177.303006 84.694866) (xy 177.302997 84.694856) (xy 177.253535 84.645394) (xy 177.183545 84.575404)
				(xy 177.048258 84.485008) (xy 176.897935 84.422742) (xy 176.85234 84.413672) (xy 176.738359 84.391)
				(xy 176.738354 84.391) (xy 170.815275 84.391) (xy 170.747154 84.370998) (xy 170.71334 84.339062)
				(xy 170.708206 84.331996) (xy 170.708201 84.33199) (xy 170.557009 84.180798) (xy 170.557006 84.180796)
				(xy 170.557004 84.180794) (xy 170.487806 84.130518) (xy 170.384006 84.055103) (xy 170.382427 84.054136)
				(xy 170.381952 84.053611) (xy 170.380004 84.052196) (xy 170.380301 84.051786) (xy 170.334797 84.001488)
				(xy 170.323191 83.931446) (xy 170.351296 83.866249) (xy 170.379844 83.841516) (xy 170.379742 83.841376)
				(xy 170.381382 83.840184) (xy 170.382448 83.839261) (xy 170.383754 83.83846) (xy 170.556679 83.712822)
				(xy 170.707822 83.561679) (xy 170.833463 83.38875) (xy 170.930506 83.198292) (xy 170.930509 83.198286)
				(xy 170.996559 82.995004) (xy 171.006698 82.931) (xy 170.102703 82.931) (xy 170.137925 82.869993)
				(xy 170.172 82.742826) (xy 170.172 82.611174) (xy 170.137925 82.484007) (xy 170.102703 82.423) (xy 171.006697 82.423)
				(xy 170.996559 82.358995) (xy 170.930509 82.155713) (xy 170.930506 82.155707) (xy 170.833463 81.965249)
				(xy 170.707822 81.79232) (xy 170.604713 81.689211) (xy 170.570687 81.626899) (xy 170.575752 81.556084)
				(xy 170.618299 81.499248) (xy 170.649768 81.482063) (xy 170.768204 81.437889) (xy 170.885261 81.350261)
				(xy 170.961448 81.248488) (xy 170.972887 81.233207) (xy 170.972887 81.233206) (xy 170.972889 81.233204)
				(xy 171.023989 81.096201) (xy 171.0305 81.035638) (xy 171.0305 80.123174) (xy 171.050502 80.055053)
				(xy 171.067405 80.034078) (xy 171.43508 79.666404) (xy 171.497392 79.632379) (xy 171.524175 79.6295)
				(xy 179.739762 79.6295)
			)
		)
	)
	(embedded_fonts no)
)
