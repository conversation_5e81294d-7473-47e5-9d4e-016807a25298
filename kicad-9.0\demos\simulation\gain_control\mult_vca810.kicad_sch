(kicad_sch
	(version 20231120)
	(generator "eeschema")
	(generator_version "7.99")
	(uuid "62c66358-895a-4557-9af0-9d199226cdcd")
	(paper "A4")
	(lib_symbols
		(symbol "Simulation_SPICE:VDC"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "1"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Voltage source, DC"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Pins" "1=+ 2=-"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Type" "DC"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Device" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "ki_keywords" "simulation"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VDC_0_0"
				(polyline
					(pts
						(xy -1.27 0.254) (xy 1.27 0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.762 -0.254) (xy -1.27 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.254 -0.254) (xy -0.254 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -0.254) (xy 0.762 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VDC_0_1"
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "VDC_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "Simulation_SPICE:VPWL"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "VPWL"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Voltage source, piece-wise linear"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Pins" "1=+ 2=-"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Device" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Sim.Params" "pwl=\"0 -1 50n -1 51n 0 97n 1 171n -1 200n -1\""
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Sim.Type" "PWL"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "simulation"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VPWL_0_0"
				(polyline
					(pts
						(xy -1.778 -1.016) (xy -0.762 1.016) (xy -0.254 0) (xy 0.762 0) (xy 1.27 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VPWL_0_1"
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "VPWL_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "Simulation_SPICE:VSIN"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "VSIN"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Voltage source, sinusoidal"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Pins" "1=+ 2=-"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Params" "dc=0 ampl=1 f=1k ac=1"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Sim.Type" "SIN"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Device" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "ki_keywords" "simulation ac vac"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VSIN_0_0"
				(arc
					(start 0 0)
					(mid -0.635 0.6323)
					(end -1.27 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 0 0)
					(mid 0.635 -0.6323)
					(end 1.27 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VSIN_0_1"
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "VSIN_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "VCA810:VCA810"
			(pin_names
				(offset 0.127)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at 1.27 6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "VCA810"
				(at 1.27 3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 1.27 1.27 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 1.27 3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "High Gain Adjust Range, Wideband and Variable Gain Amplifier"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "single opamp"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "DIP*W7.62mm* SOIC*3.9x4.9mm*P1.27mm* TO*99*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VCA810_0_1"
				(polyline
					(pts
						(xy -5.08 5.08) (xy 5.08 0) (xy -5.08 -5.08) (xy -5.08 5.08)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "VCA810_1_1"
				(pin input line
					(at -7.62 2.54 0)
					(length 2.54)
					(name "+"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at 2.54 -7.62 90)
					(length 6.35)
					(name "GND"
						(effects
							(font
								(size 0.508 0.508)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at 0 -7.62 90)
					(length 5.08)
					(name "Gain"
						(effects
							(font
								(size 0.508 0.508)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin no_connect line
					(at 0 2.54 270)
					(length 2.54) hide
					(name "NC"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin output line
					(at 7.62 0 180)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at -2.54 7.62 270)
					(length 3.81)
					(name "V+"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at -2.54 -7.62 90)
					(length 3.81)
					(name "V-"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at -7.62 -2.54 0)
					(length 2.54)
					(name "-"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "power:+5V"
			(power)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "+5V"
				(at 0 3.556 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"+5V\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "+5V_0_1"
				(polyline
					(pts
						(xy -0.762 1.27) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 2.54) (xy 0.762 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "+5V_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0) hide
					(name "+5V"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "power:-5V"
			(power)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "-5V"
				(at 0 3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"-5V\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "-5V_0_0"
				(pin power_in line
					(at 0 0 90)
					(length 0) hide
					(name "-5V"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "-5V_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 1.27) (xy 0.762 1.27) (xy 0 2.54) (xy -0.762 1.27) (xy 0 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
			)
		)
		(symbol "power:GND"
			(power)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "GND"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "GND_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "GND_1_1"
				(pin power_in line
					(at 0 0 270)
					(length 0) hide
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
	)
	(junction
		(at 185.42 92.71)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "f6787f81-9a1c-4e03-9cb1-7db36a5121a2")
	)
	(wire
		(pts
			(xy 185.42 78.74) (xy 185.42 81.28)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1949f361-8209-4174-9a95-368b9073d8eb")
	)
	(wire
		(pts
			(xy 214.63 128.27) (xy 214.63 119.38)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4c699e62-460b-4458-abf8-ba8673d78aea")
	)
	(wire
		(pts
			(xy 217.17 100.33) (xy 217.17 102.87)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6aaebd3a-e3bc-4ed8-99ba-7f5754648b44")
	)
	(wire
		(pts
			(xy 185.42 104.14) (xy 185.42 106.68)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6e618d5b-9ada-44e4-b1d3-6e8c9b8ae8f1")
	)
	(wire
		(pts
			(xy 204.47 97.79) (xy 204.47 95.25)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "74fc70c5-71c9-476e-ad85-12c571cf3260")
	)
	(wire
		(pts
			(xy 198.12 116.84) (xy 198.12 90.17)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "8d594704-5a22-4a76-a7d7-78b3f85c88b6")
	)
	(wire
		(pts
			(xy 179.07 92.71) (xy 185.42 92.71)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b01c9ffa-e4be-45cb-bfad-2b19811beabf")
	)
	(wire
		(pts
			(xy 198.12 129.54) (xy 198.12 127)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b0928a90-1a5b-424a-b11e-6ba6f26afa4b")
	)
	(wire
		(pts
			(xy 179.07 95.25) (xy 179.07 92.71)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cd463fa4-9b43-4d3f-afdf-8bec421b9899")
	)
	(wire
		(pts
			(xy 185.42 91.44) (xy 185.42 92.71)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cda6a931-bdd1-41b0-bb76-2abd90948226")
	)
	(wire
		(pts
			(xy 231.14 92.71) (xy 222.25 92.71)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cf8053ea-8d53-47f0-80fc-06904a7d68b6")
	)
	(wire
		(pts
			(xy 212.09 82.55) (xy 212.09 85.09)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e4a1f067-884b-4ef2-860d-f3babe00b165")
	)
	(wire
		(pts
			(xy 198.12 90.17) (xy 207.01 90.17)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e7a515cb-152f-42b7-8d2d-3371c9776e07")
	)
	(wire
		(pts
			(xy 212.09 100.33) (xy 212.09 102.87)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "eb10ed6c-0390-4551-8832-4b7277b27815")
	)
	(wire
		(pts
			(xy 214.63 100.33) (xy 214.63 109.22)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ebb6997a-4082-4c34-affe-118f4033dbda")
	)
	(wire
		(pts
			(xy 185.42 92.71) (xy 185.42 93.98)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "eebd4926-38af-4968-9b31-04b3302e2730")
	)
	(wire
		(pts
			(xy 204.47 95.25) (xy 207.01 95.25)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "fdd0b61f-200b-463c-a3cf-bad9abc4a425")
	)
	(label "out"
		(at 231.14 92.71 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "212ac7eb-ddc7-4170-8ee7-e1121d756cf0")
	)
	(label "in"
		(at 201.93 90.17 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "85db221d-717f-4a7f-9e02-47cfd5b9e9fc")
	)
	(label "gain"
		(at 214.63 100.33 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "c83cef09-7fbd-42a8-90a9-a6313f20c4c9")
	)
	(symbol
		(lib_id "power:+5V")
		(at 185.42 78.74 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "17263c2c-c948-464d-af3c-665025ec4e5e")
		(property "Reference" "#PWR07"
			(at 185.42 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+5V"
			(at 185.42 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 185.42 78.74 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 185.42 78.74 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"+5V\""
			(at 185.42 78.74 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "ef763eb1-67b1-4493-9193-d058ac6d61ff")
		)
		(instances
			(project "mult_vca810"
				(path "/62c66358-895a-4557-9af0-9d199226cdcd"
					(reference "#PWR07")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Simulation_SPICE:VDC")
		(at 185.42 86.36 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "18563f8e-3373-4077-ad58-6f22cd9e9634")
		(property "Reference" "V1"
			(at 189.23 84.9601 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "5"
			(at 189.23 87.5001 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 185.42 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 185.42 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Voltage source, DC"
			(at 185.42 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 185.42 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Type" "DC"
			(at 185.42 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "V"
			(at 185.42 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "51c1f5c6-25f2-4011-8452-e88faf1fb8b5")
		)
		(pin "1"
			(uuid "e4c47c55-a400-4a28-948b-28156f5929d7")
		)
		(instances
			(project "mult_vca810"
				(path "/62c66358-895a-4557-9af0-9d199226cdcd"
					(reference "V1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:-5V")
		(at 212.09 102.87 0)
		(mirror x)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "2898e2f0-d74f-45ac-b2d8-a3a84f0a0f82")
		(property "Reference" "#PWR02"
			(at 212.09 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "-5V"
			(at 212.09 107.95 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 212.09 102.87 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 212.09 102.87 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"-5V\""
			(at 212.09 102.87 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "6075b485-256f-41f0-83d6-d15fbc7f6a3d")
		)
		(instances
			(project "mult_vca810"
				(path "/62c66358-895a-4557-9af0-9d199226cdcd"
					(reference "#PWR02")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 214.63 128.27 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "31af25e6-3096-40ed-a1c6-75aee6f726c2")
		(property "Reference" "#PWR09"
			(at 214.63 134.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 214.63 133.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 214.63 128.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 214.63 128.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 214.63 128.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "e5b9cdf6-2d6c-4a5e-b90e-04052bc75cf5")
		)
		(instances
			(project "mult_vca810"
				(path "/62c66358-895a-4557-9af0-9d199226cdcd"
					(reference "#PWR09")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:-5V")
		(at 185.42 106.68 0)
		(mirror x)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "47a08449-753e-490a-81d7-3c25a84d2c97")
		(property "Reference" "#PWR06"
			(at 185.42 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "-5V"
			(at 185.42 111.76 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 185.42 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 185.42 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"-5V\""
			(at 185.42 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "592c1edc-0608-43a4-9ce0-2bac0475a594")
		)
		(instances
			(project "mult_vca810"
				(path "/62c66358-895a-4557-9af0-9d199226cdcd"
					(reference "#PWR06")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 217.17 102.87 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "49cb7dba-af3d-40af-a8c8-0e58440e8dd6")
		(property "Reference" "#PWR03"
			(at 217.17 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 217.17 107.95 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 217.17 102.87 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 217.17 102.87 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 217.17 102.87 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "32d49076-21e7-49f9-854c-2d00b4d76bfe")
		)
		(instances
			(project "mult_vca810"
				(path "/62c66358-895a-4557-9af0-9d199226cdcd"
					(reference "#PWR03")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Simulation_SPICE:VDC")
		(at 185.42 99.06 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "5f713048-0343-481b-94bf-3036c689bfa6")
		(property "Reference" "V2"
			(at 189.23 97.6601 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "5"
			(at 189.23 100.2001 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 185.42 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 185.42 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Voltage source, DC"
			(at 185.42 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 185.42 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Type" "DC"
			(at 185.42 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "V"
			(at 185.42 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "d3cfc6c6-a07f-448a-852d-8d9d3b412ea2")
		)
		(pin "1"
			(uuid "d8fee442-903f-42ba-a0d8-d7d76e83a073")
		)
		(instances
			(project "mult_vca810"
				(path "/62c66358-895a-4557-9af0-9d199226cdcd"
					(reference "V2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Simulation_SPICE:VSIN")
		(at 198.12 121.92 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "5fb4cdf5-e2c1-459f-b8cd-b8b178233b81")
		(property "Reference" "V3"
			(at 201.93 119.2501 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "VSIN"
			(at 201.93 121.7901 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 198.12 121.92 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 198.12 121.92 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Voltage source, sinusoidal"
			(at 198.12 121.92 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 198.12 121.92 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Params" "dc=0 ampl=10m f=1k ac=1"
			(at 201.93 124.3301 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Sim.Type" "SIN"
			(at 198.12 121.92 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "V"
			(at 198.12 121.92 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "82e38626-e2ce-40c1-9974-47cee3c0b7c2")
		)
		(pin "1"
			(uuid "4d2f5369-adac-4f05-a0a0-a3c2c6ff95b1")
		)
		(instances
			(project "mult_vca810"
				(path "/62c66358-895a-4557-9af0-9d199226cdcd"
					(reference "V3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 179.07 95.25 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a4f9e736-0769-4b60-bd2a-dc61bd54e801")
		(property "Reference" "#PWR05"
			(at 179.07 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 179.07 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 179.07 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 179.07 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 179.07 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "a80cea03-83df-4af2-a14a-033f43000cde")
		)
		(instances
			(project "mult_vca810"
				(path "/62c66358-895a-4557-9af0-9d199226cdcd"
					(reference "#PWR05")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 198.12 129.54 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a753c260-47fe-432b-aa0c-35860ab07c55")
		(property "Reference" "#PWR08"
			(at 198.12 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 198.12 134.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 198.12 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 198.12 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 198.12 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "29a902eb-abea-47e2-99e9-55690bbfcba1")
		)
		(instances
			(project "mult_vca810"
				(path "/62c66358-895a-4557-9af0-9d199226cdcd"
					(reference "#PWR08")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Simulation_SPICE:VPWL")
		(at 214.63 114.3 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "ac18fee6-6204-41d5-aa67-7d54efb51e6c")
		(property "Reference" "V4"
			(at 218.44 111.6301 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "VPWL"
			(at 218.44 114.1701 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 214.63 114.3 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 214.63 114.3 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Voltage source, piece-wise linear"
			(at 214.63 114.3 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 214.63 114.3 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "V"
			(at 214.63 114.3 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Params" "pwl=\"0 -1.2 10m -1.2 10.01m -1.6 20m -1.6 20.1m -2 100m -2\""
			(at 218.44 116.7101 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Sim.Type" "PWL"
			(at 214.63 114.3 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "dedb0601-f231-434d-89a0-d491d4125c81")
		)
		(pin "2"
			(uuid "ff48a882-ff9b-4b0e-86c8-91de0bf643d6")
		)
		(instances
			(project "mult_vca810"
				(path "/62c66358-895a-4557-9af0-9d199226cdcd"
					(reference "V4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 204.47 97.79 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "bc98b1f4-9d4d-4a79-bd6e-88fac618489b")
		(property "Reference" "#PWR04"
			(at 204.47 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 204.47 102.87 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 204.47 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 204.47 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 204.47 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b56447cd-fc46-42a9-957c-475110183bec")
		)
		(instances
			(project "mult_vca810"
				(path "/62c66358-895a-4557-9af0-9d199226cdcd"
					(reference "#PWR04")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "VCA810:VCA810")
		(at 214.63 92.71 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "e7e26bcc-e46f-4931-9acb-9817395f46aa")
		(property "Reference" "U1"
			(at 224.79 88.519 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "VCA810"
			(at 224.79 91.059 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 215.9 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 215.9 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "High Gain Adjust Range, Wideband and Variable Gain Amplifier"
			(at 214.63 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Library" "vca810.lib"
			(at 214.63 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Name" "VCA810"
			(at 214.63 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "SUBCKT"
			(at 214.63 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=1 2=2 3=3 5=5 6=6 7=7 8=8"
			(at 214.63 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "4"
			(uuid "a4f85ecc-6bc3-4820-bdce-24432edb0151")
		)
		(pin "3"
			(uuid "326dea3a-ee15-444a-b717-6c9bfe891a66")
		)
		(pin "6"
			(uuid "39077a79-ffc5-4a78-8313-70edcdd1d0d0")
		)
		(pin "8"
			(uuid "f527be61-505f-48c4-9e6a-8c328200b2ff")
		)
		(pin "2"
			(uuid "80924363-dfd8-4174-b032-b99cd1e17a9b")
		)
		(pin "7"
			(uuid "960c81d2-93cc-400d-8240-431e59b25d62")
		)
		(pin "1"
			(uuid "1bff1d0a-074a-4cb3-ae21-08ebda9c2cc7")
		)
		(pin "5"
			(uuid "ca07eb4b-10e4-482f-967f-076238116546")
		)
		(instances
			(project "mult_vca810"
				(path "/62c66358-895a-4557-9af0-9d199226cdcd"
					(reference "U1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:+5V")
		(at 212.09 82.55 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "fe95d10e-b6bf-4e7f-b65f-bbdf9f000036")
		(property "Reference" "#PWR01"
			(at 212.09 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+5V"
			(at 212.09 77.47 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 212.09 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 212.09 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"+5V\""
			(at 212.09 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "69756e8d-3874-4ea8-923c-4bfd8d5223b1")
		)
		(instances
			(project "mult_vca810"
				(path "/62c66358-895a-4557-9af0-9d199226cdcd"
					(reference "#PWR01")
					(unit 1)
				)
			)
		)
	)
	(sheet_instances
		(path "/"
			(page "1")
		)
	)
)