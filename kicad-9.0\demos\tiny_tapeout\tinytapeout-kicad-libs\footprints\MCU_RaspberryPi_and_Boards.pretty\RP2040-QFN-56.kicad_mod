(module RP2040-QFN-56 (layer F.Cu) (tedit 5EF32B43)
  (descr "QFN, 56 Pin (http://www.cypress.com/file/416486/download#page=40), generated with kicad-footprint-generator ipc_dfn_qfn_generator.py")
  (tags "QFN DFN_QFN")
  (attr smd)
  (fp_text reference REF** (at 0 -4.82) (layer F.SilkS)
    (effects (font (size 1 1) (thickness 0.15)))
  )
  (fp_text value Pico2040-QFN-56 (at 0 4.82) (layer F.Fab)
    (effects (font (size 1 1) (thickness 0.15)))
  )
  (fp_text user %R (at 0 0) (layer F.Fab)
    (effects (font (size 1 1) (thickness 0.15)))
  )
  (fp_line (start 4.12 -4.12) (end -4.12 -4.12) (layer F.CrtYd) (width 0.05))
  (fp_line (start 4.12 4.12) (end 4.12 -4.12) (layer F.CrtYd) (width 0.05))
  (fp_line (start -4.12 4.12) (end 4.12 4.12) (layer F.CrtYd) (width 0.05))
  (fp_line (start -4.12 -4.12) (end -4.12 4.12) (layer F.CrtYd) (width 0.05))
  (fp_line (start -3.5 -2.5) (end -2.5 -3.5) (layer F.Fab) (width 0.1))
  (fp_line (start -3.5 3.5) (end -3.5 -2.5) (layer F.Fab) (width 0.1))
  (fp_line (start 3.5 3.5) (end -3.5 3.5) (layer F.Fab) (width 0.1))
  (fp_line (start 3.5 -3.5) (end 3.5 3.5) (layer F.Fab) (width 0.1))
  (fp_line (start -2.5 -3.5) (end 3.5 -3.5) (layer F.Fab) (width 0.1))
  (fp_line (start -2.96 -3.61) (end -3.61 -3.61) (layer F.SilkS) (width 0.12))
  (fp_line (start 3.61 3.61) (end 3.61 2.96) (layer F.SilkS) (width 0.12))
  (fp_line (start 2.96 3.61) (end 3.61 3.61) (layer F.SilkS) (width 0.12))
  (fp_line (start -3.61 3.61) (end -3.61 2.96) (layer F.SilkS) (width 0.12))
  (fp_line (start -2.96 3.61) (end -3.61 3.61) (layer F.SilkS) (width 0.12))
  (fp_line (start 3.61 -3.61) (end 3.61 -2.96) (layer F.SilkS) (width 0.12))
  (fp_line (start 2.96 -3.61) (end 3.61 -3.61) (layer F.SilkS) (width 0.12))
  (pad 56 smd roundrect (at -2.6 -3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 55 smd roundrect (at -2.2 -3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 54 smd roundrect (at -1.8 -3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 53 smd roundrect (at -1.4 -3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 52 smd roundrect (at -1 -3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 51 smd roundrect (at -0.6 -3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 50 smd roundrect (at -0.2 -3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 49 smd roundrect (at 0.2 -3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 48 smd roundrect (at 0.6 -3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 47 smd roundrect (at 1 -3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 46 smd roundrect (at 1.4 -3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 45 smd roundrect (at 1.8 -3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 44 smd roundrect (at 2.2 -3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 43 smd roundrect (at 2.6 -3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 42 smd roundrect (at 3.4375 -2.6) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 41 smd roundrect (at 3.4375 -2.2) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 40 smd roundrect (at 3.4375 -1.8) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 39 smd roundrect (at 3.4375 -1.4) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 38 smd roundrect (at 3.4375 -1) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 37 smd roundrect (at 3.4375 -0.6) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 36 smd roundrect (at 3.4375 -0.2) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 35 smd roundrect (at 3.4375 0.2) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 34 smd roundrect (at 3.4375 0.6) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 33 smd roundrect (at 3.4375 1) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 32 smd roundrect (at 3.4375 1.4) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 31 smd roundrect (at 3.4375 1.8) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 30 smd roundrect (at 3.4375 2.2) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 29 smd roundrect (at 3.4375 2.6) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 28 smd roundrect (at 2.6 3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 27 smd roundrect (at 2.2 3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 26 smd roundrect (at 1.8 3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 25 smd roundrect (at 1.4 3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 24 smd roundrect (at 1 3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 23 smd roundrect (at 0.6 3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 22 smd roundrect (at 0.2 3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 21 smd roundrect (at -0.2 3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 20 smd roundrect (at -0.6 3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 19 smd roundrect (at -1 3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 18 smd roundrect (at -1.4 3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 17 smd roundrect (at -1.8 3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 16 smd roundrect (at -2.2 3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 15 smd roundrect (at -2.6 3.4375) (size 0.2 0.875) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 14 smd roundrect (at -3.4375 2.6) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 13 smd roundrect (at -3.4375 2.2) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 12 smd roundrect (at -3.4375 1.8) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 11 smd roundrect (at -3.4375 1.4) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 10 smd roundrect (at -3.4375 1) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 9 smd roundrect (at -3.4375 0.6) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 8 smd roundrect (at -3.4375 0.2) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 7 smd roundrect (at -3.4375 -0.2) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 6 smd roundrect (at -3.4375 -0.6) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 5 smd roundrect (at -3.4375 -1) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 4 smd roundrect (at -3.4375 -1.4) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 3 smd roundrect (at -3.4375 -1.8) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 2 smd roundrect (at -3.4375 -2.2) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad 1 smd roundrect (at -3.4375 -2.6) (size 0.875 0.2) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25))
  (pad "" smd roundrect (at 0.6375 0.6375) (size 1.084435 1.084435) (layers F.Paste) (roundrect_rratio 0.230535))
  (pad "" smd roundrect (at 0.6375 -0.6375) (size 1.084435 1.084435) (layers F.Paste) (roundrect_rratio 0.230535))
  (pad "" smd roundrect (at -0.6375 0.6375) (size 1.084435 1.084435) (layers F.Paste) (roundrect_rratio 0.230535))
  (pad "" smd roundrect (at -0.6375 -0.6375) (size 1.084435 1.084435) (layers F.Paste) (roundrect_rratio 0.230535))
  (pad 57 thru_hole circle (at 1.275 1.275) (size 0.6 0.6) (drill 0.35) (layers *.Cu))
  (pad 57 thru_hole circle (at 0 1.275) (size 0.6 0.6) (drill 0.35) (layers *.Cu))
  (pad 57 thru_hole circle (at -1.275 1.275) (size 0.6 0.6) (drill 0.35) (layers *.Cu))
  (pad 57 thru_hole circle (at 1.275 0) (size 0.6 0.6) (drill 0.35) (layers *.Cu))
  (pad 57 thru_hole circle (at 0 0) (size 0.6 0.6) (drill 0.35) (layers *.Cu))
  (pad 57 thru_hole circle (at -1.275 0) (size 0.6 0.6) (drill 0.35) (layers *.Cu))
  (pad 57 thru_hole circle (at 1.275 -1.275) (size 0.6 0.6) (drill 0.35) (layers *.Cu))
  (pad 57 thru_hole circle (at 0 -1.275) (size 0.6 0.6) (drill 0.35) (layers *.Cu))
  (pad 57 thru_hole circle (at -1.275 -1.275) (size 0.6 0.6) (drill 0.35) (layers *.Cu))
  (pad 57 smd roundrect (at 0 0) (size 3.2 3.2) (layers F.Cu F.Mask) (roundrect_rratio 0.045))
  (model ${KISYS3DMOD}/Package_DFN_QFN.3dshapes/QFN-56-1EP_7x7mm_P0.4mm_EP5.6x5.6mm.wrl
    (at (xyz 0 0 0))
    (scale (xyz 1 1 1))
    (rotate (xyz 0 0 0))
  )
)
