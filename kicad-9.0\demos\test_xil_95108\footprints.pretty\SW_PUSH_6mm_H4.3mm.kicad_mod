(footprint "SW_PUSH_6mm_H4.3mm"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "tactile push button, 6x6mm e.g. PHAP33xx series, height=4.3mm")
	(tags "tact sw push 6mm")
	(property "Reference" "REF**"
		(at 3.25 -2 0)
		(layer "F.SilkS")
		(uuid "d127df8a-ad36-4152-a42f-f2bfaa967345")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "SW_PUSH"
		(at 3.75 6.7 0)
		(layer "F.Fab")
		(uuid "7d26d011-4a10-4225-a9d0-50e1bd3a389b")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "a0353df9-7297-475b-8158-df73218d176b")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "c3c503ed-42c3-4617-a4bd-b4ad2dc5eae2")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -0.25 1.5)
		(end -0.25 3)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "960d7fdc-0d56-43ae-ab39-7a20afc3c210")
	)
	(fp_line
		(start 1 5.5)
		(end 5.5 5.5)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f827671d-718a-44b9-adc7-2304cdb2af57")
	)
	(fp_line
		(start 5.5 -1)
		(end 1 -1)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "dc589996-b549-463b-9290-b5d27cbb74b0")
	)
	(fp_line
		(start 6.75 3)
		(end 6.75 1.5)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "1bae5de5-57eb-42ae-9b93-0c867ded98eb")
	)
	(fp_line
		(start -1.5 -1.5)
		(end -1.25 -1.5)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "51c4cca0-e4bf-484a-b0c1-8921a12f9524")
	)
	(fp_line
		(start -1.5 -1.25)
		(end -1.5 -1.5)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "52b1f9f9-15ba-432a-a1a5-05bda64411c2")
	)
	(fp_line
		(start -1.5 5.75)
		(end -1.5 -1.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "0bedd072-3ec2-4e48-a98a-6e9210262e60")
	)
	(fp_line
		(start -1.5 5.75)
		(end -1.5 6)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "d2cf1018-2edb-40fc-a41f-91d77af5bdff")
	)
	(fp_line
		(start -1.5 6)
		(end -1.25 6)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "654f081c-572a-4ee9-bea5-9074151dacab")
	)
	(fp_line
		(start -1.25 -1.5)
		(end 7.75 -1.5)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "239b727d-3567-41be-b692-282d6755279a")
	)
	(fp_line
		(start 7.75 -1.5)
		(end 8 -1.5)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "acee80e6-0fae-4a69-8166-17b6a169eb2f")
	)
	(fp_line
		(start 7.75 6)
		(end -1.25 6)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "5a055c61-59d4-4967-b72f-76439bcc8685")
	)
	(fp_line
		(start 7.75 6)
		(end 8 6)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "5063a41d-315e-408b-aded-e74bbb5d49a7")
	)
	(fp_line
		(start 8 -1.5)
		(end 8 -1.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "fb5800fc-8bd8-4093-94f5-5005756ff42c")
	)
	(fp_line
		(start 8 -1.25)
		(end 8 5.75)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "897fc602-fa91-4085-b738-0dc7a2120676")
	)
	(fp_line
		(start 8 6)
		(end 8 5.75)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "42f837f5-4142-4009-8a72-d49263240bdc")
	)
	(fp_line
		(start 0.25 -0.75)
		(end 3.25 -0.75)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "699c8eeb-1d5f-4d51-b96a-40420af7e94e")
	)
	(fp_line
		(start 0.25 5.25)
		(end 0.25 -0.75)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "ce2c3dbe-d414-4f4a-85ce-a7e3c06299c6")
	)
	(fp_line
		(start 3.25 -0.75)
		(end 6.25 -0.75)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "e5ac6d14-5abd-4939-a7c2-0127b1835954")
	)
	(fp_line
		(start 6.25 -0.75)
		(end 6.25 5.25)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "46667fe3-930b-4e06-90b7-334b8b40e223")
	)
	(fp_line
		(start 6.25 5.25)
		(end 0.25 5.25)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "d95c9251-4d96-4de1-81e7-9a1499118f39")
	)
	(fp_circle
		(center 3.25 2.25)
		(end 1.25 2.5)
		(stroke
			(width 0.1)
			(type solid)
		)
		(fill no)
		(layer "F.Fab")
		(uuid "c10de194-0fc9-4d5c-a23d-200e1a123b68")
	)
	(fp_text user "${REFERENCE}"
		(at 3.25 2.25 0)
		(layer "F.Fab")
		(uuid "7ef28cf5-5048-45ad-b578-69aa1b75189e")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole circle
		(at 0 0 90)
		(size 2 2)
		(drill 1.1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "928cd867-dd20-408e-96be-69c40773015e")
	)
	(pad "1" thru_hole circle
		(at 6.5 0 90)
		(size 2 2)
		(drill 1.1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "4b0197b6-a858-45ca-b916-a32927f35ab2")
	)
	(pad "2" thru_hole circle
		(at 0 4.5 90)
		(size 2 2)
		(drill 1.1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7ec60864-4c24-4ea7-af1a-5fe5f2263012")
	)
	(pad "2" thru_hole circle
		(at 6.5 4.5 90)
		(size 2 2)
		(drill 1.1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "3901991d-4183-4767-ac2e-799ff5710c91")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/Button_Switch_THT.3dshapes/SW_PUSH_6mm_H4.3mm.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
