(footprint "JST_SH_SM04B-SRSS-TB_1x04-1MP_P1.00mm_Horizontal"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "JST SH series connector, SM04B-SRSS-TB (http://www.jst-mfg.com/product/pdf/eng/eSH.pdf), generated with kicad-footprint-generator")
	(tags "connector JST SH top entry")
	(property "Reference" "REF**"
		(at 0 -3.98 0)
		(layer "F.SilkS")
		(hide yes)
		(uuid "37ac9d59-5cb9-45ab-9e7a-5eb167c32c28")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "USB2"
		(at 0 3.5 0)
		(layer "F.SilkS")
		(uuid "fdf5e4bf-2a52-4ae7-bda8-d51eb17c01ae")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "b4fd70c5-e3e1-4db4-9a53-e453492fba22")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" "JST SH 4-Pin horizontal"
		(at 153.85 97.75 270)
		(layer "F.Fab")
		(hide yes)
		(uuid "6947d5fe-fe3f-4790-b510-626d27ea50c2")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "MPN" "SM04B-SRSS-TB(LF)(SN) "
		(at 0 0 270)
		(layer "F.Fab")
		(hide yes)
		(uuid "7512dea2-d572-435a-8fd1-1a5a60c3b5c7")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(attr smd)
	(fp_line
		(start -3.11 -1.785)
		(end -2.06 -1.785)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f89deafd-659f-43af-9231-66f5d89708cf")
	)
	(fp_line
		(start -3.11 0.715)
		(end -3.11 -1.785)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "98dd2754-e8d9-49d5-9db1-42bc42949f22")
	)
	(fp_line
		(start -2.06 -1.785)
		(end -2.06 -2.775)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f419315d-7f9a-4a3d-8073-b41eb64dea94")
	)
	(fp_line
		(start -1.94 2.685)
		(end 1.94 2.685)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "4cd1ab08-4267-4e34-82c0-e7cf317df554")
	)
	(fp_line
		(start 3.11 -1.785)
		(end 2.06 -1.785)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "353d9592-950e-4f35-860f-10596943f448")
	)
	(fp_line
		(start 3.11 0.715)
		(end 3.11 -1.785)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "b72fc5ce-4ab4-43ad-8f90-a1a8b6d1d404")
	)
	(fp_line
		(start -3.9 -3.28)
		(end -3.9 3.28)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "46fdb01e-6fcf-42d6-865e-0cf2f2794354")
	)
	(fp_line
		(start -3.9 3.28)
		(end 3.9 3.28)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "e0e6b8a7-353f-45bc-9592-9880a5c557bd")
	)
	(fp_line
		(start 3.9 -3.28)
		(end -3.9 -3.28)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "d41d1fa1-8f49-4650-8e49-6e8a98626a66")
	)
	(fp_line
		(start 3.9 3.28)
		(end 3.9 -3.28)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "abc8fc39-6b1f-4a6d-8844-61ce5cae6af4")
	)
	(fp_line
		(start -3 -1.675)
		(end -3 2.575)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "221f2ad9-7a59-4066-93a4-9a943ed5fcb6")
	)
	(fp_line
		(start -3 -1.675)
		(end 3 -1.675)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "a97d9f93-3d0a-4a1e-990c-0e5cc8a701e5")
	)
	(fp_line
		(start -3 2.575)
		(end -3 4.575)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "aa1d3ab1-8311-4b0e-9eea-45a6e5d398b5")
	)
	(fp_line
		(start -3 2.575)
		(end 3 2.575)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "5b760e33-f332-49ad-bff4-207edbe61d4f")
	)
	(fp_line
		(start -3 4.575)
		(end 3 4.575)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "b1ddb2bc-b798-4353-9847-b47ef01ed602")
	)
	(fp_line
		(start -2 -1.675)
		(end -1.5 -0.967893)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "77ac9039-2358-4e85-927c-aed303c0680b")
	)
	(fp_line
		(start -1.5 -0.967893)
		(end -1 -1.675)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "0edfb89c-09a1-4463-ad28-398c347f2cad")
	)
	(fp_line
		(start 3 -1.675)
		(end 3 2.575)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "9dbcdc1c-77c4-42c9-bc6b-974ec312e0d5")
	)
	(fp_line
		(start 3 4.575)
		(end 3 2.575)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "d65c79d9-a461-43d9-b693-817f74722c31")
	)
	(fp_text user "${REFERENCE}"
		(at 0 0 0)
		(layer "F.Fab")
		(uuid "93b178e0-b667-40d5-b790-591f5ad2d619")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "1" smd roundrect
		(at -1.5 -2)
		(size 0.6 1.55)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "c99dd6aa-32d1-42b1-a40b-23f54f0fac71")
	)
	(pad "2" smd roundrect
		(at -0.5 -2)
		(size 0.6 1.55)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "fe37d6f4-0ae0-4b97-ba11-ba394cbbd41f")
	)
	(pad "3" smd roundrect
		(at 0.5 -2)
		(size 0.6 1.55)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "6b0452f1-ff85-4a54-ac78-d8e777d5a7a1")
	)
	(pad "4" smd roundrect
		(at 1.5 -2)
		(size 0.6 1.55)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "cced14b5-bf6d-4ad2-bb21-d0491777c640")
	)
	(pad "MP" smd roundrect
		(at -2.8 1.875)
		(size 1.2 1.8)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.2083333333)
		(uuid "023f087e-e748-44a6-a09b-027ccc488a06")
	)
	(pad "MP" smd roundrect
		(at 2.8 1.875)
		(size 1.2 1.8)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.2083333333)
		(uuid "4805fcc6-1802-44a7-9ac5-9bc2fe151e3e")
	)
	(embedded_fonts no)
	(model "${KISYS3DMOD}/Connector_JST.3dshapes/JST_SH_SM04B-SRSS-TB_1x04-1MP_P1.00mm_Horizontal.step"
		(hide yes)
		(offset
			(xyz 0 1.325 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz -90 0 0)
		)
	)
	(model "${KIPRJMOD}/3dmodels/JST_SH_SM04B-SRSS-TB.STEP"
		(offset
			(xyz 0 -0.45 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz -90 0 0)
		)
	)
)
