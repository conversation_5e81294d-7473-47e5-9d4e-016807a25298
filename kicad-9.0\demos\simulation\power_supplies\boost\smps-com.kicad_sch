(kicad_sch
	(version 20231120)
	(generator "eeschema")
	(generator_version "8.0")
	(uuid "566bf9ad-05d9-4e09-9cf5-c84b6117df9c")
	(paper "A4")
	(lib_symbols
		(symbol "D_1"
			(pin_names
				(offset 1.016) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "D"
				(at 0 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "D"
				(at 0 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Diode for simulation or PCB"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Device" "D"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Sim.Pins" "1=K 2=A"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "ki_keywords" "simulation"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "TO-???* *_Diode_* *SingleDiode* D_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "D_1_0_1"
				(polyline
					(pts
						(xy -1.27 1.27) (xy -1.27 -1.27)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 0) (xy -1.27 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 1.27) (xy 1.27 -1.27) (xy -1.27 0) (xy 1.27 1.27)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "D_1_1_1"
				(pin passive line
					(at -3.81 0 0)
					(length 2.54)
					(name "K"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 3.81 0 180)
					(length 2.54)
					(name "A"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "Device:C"
			(pin_numbers hide)
			(pin_names
				(offset 0.254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "C"
				(at 0.635 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "C"
				(at 0.635 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0.9652 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Unpolarized capacitor"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "cap capacitor"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "C_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "C_0_1"
				(polyline
					(pts
						(xy -2.032 -0.762) (xy 2.032 -0.762)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -2.032 0.762) (xy 2.032 0.762)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "C_1_1"
				(pin passive line
					(at 0 3.81 270)
					(length 2.794)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -3.81 90)
					(length 2.794)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "Device:C_Small"
			(pin_numbers hide)
			(pin_names
				(offset 0.254) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "C"
				(at 0.254 1.778 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "C_Small"
				(at 0.254 -2.032 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Unpolarized capacitor, small symbol"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "capacitor cap"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "C_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "C_Small_0_1"
				(polyline
					(pts
						(xy -1.524 -0.508) (xy 1.524 -0.508)
					)
					(stroke
						(width 0.3302)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.524 0.508) (xy 1.524 0.508)
					)
					(stroke
						(width 0.3048)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "C_Small_1_1"
				(pin passive line
					(at 0 2.54 270)
					(length 2.032)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -2.54 90)
					(length 2.032)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "Device:L"
			(pin_numbers hide)
			(pin_names
				(offset 1.016) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "L"
				(at -1.27 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "L"
				(at 1.905 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Inductor"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "inductor choke coil reactor magnetic"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Choke_* *Coil* Inductor_* L_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "L_0_1"
				(arc
					(start 0 -2.54)
					(mid 0.6323 -1.905)
					(end 0 -1.27)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 0 -1.27)
					(mid 0.6323 -0.635)
					(end 0 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 0 0)
					(mid 0.6323 0.635)
					(end 0 1.27)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 0 1.27)
					(mid 0.6323 1.905)
					(end 0 2.54)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "L_1_1"
				(pin passive line
					(at 0 3.81 270)
					(length 1.27)
					(name "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -3.81 90)
					(length 1.27)
					(name "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "Device:R"
			(pin_numbers hide)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "R"
				(at 2.032 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "R"
				(at 0 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at -1.778 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Resistor"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "R res resistor"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "R_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "R_0_1"
				(rectangle
					(start -1.016 -2.54)
					(end 1.016 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "R_1_1"
				(pin passive line
					(at 0 3.81 270)
					(length 1.27)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -3.81 90)
					(length 1.27)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "PWM:PWM"
			(pin_names
				(offset 1.016) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "A1"
				(at -3.81 1.7146 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify right)
				)
			)
			(property "Value" "PWM"
				(at -3.81 -0.8254 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify right)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "https://ngspice.sourceforge.io/docs.html"
				(at 0 16.51 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Voltage-dependent Voltage source symbol for simulation only"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Library" "pwm2_model.lib"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Name" "pwm"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Device" "SUBCKT"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Pins" "1=out+ 2=out- 3=in+ 4=in-"
				(at -27.94 -6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Params" "freq=100k vlo=0.7 vhi=3.5"
				(at -16.51 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "ki_keywords" "simulation"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "PWM_0_1"
				(polyline
					(pts
						(xy 0 -1.27) (xy 0 -2.286)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.254 -1.778) (xy 0 -1.27) (xy -0.254 -1.778)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "PWM_1_1"
				(polyline
					(pts
						(xy 0.254 3.81) (xy 0.762 3.81)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.508 4.064) (xy 0.508 3.556)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.905 -3.175) (xy 3.175 -1.905)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 5.334 3.81) (xy 5.842 3.81)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 5.588 4.064) (xy 5.588 3.556)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 6.985 1.905) (xy 8.255 3.175)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 6.985 3.175) (xy 8.255 3.175) (xy 8.255 1.905)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 5.08 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(text "V"
					(at 5.08 -0.254 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(pin input line
					(at 0 5.08 270)
					(length 2.54)
					(name "N+"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at 0 -5.08 90)
					(length 2.54)
					(name "N-"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 5.08 270)
					(length 2.54)
					(name "C+"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -5.08 90)
					(length 2.54)
					(name "C-"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "Simulation_SPICE:NMOS"
			(pin_numbers hide)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "Q"
				(at 5.08 1.27 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "NMOS"
				(at 5.08 -1.27 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 5.08 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "https://ngspice.sourceforge.io/docs/ngspice-manual.pdf"
				(at 0 -12.7 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "N-MOSFET transistor, drain/source/gate"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Device" "NMOS"
				(at 0 -17.145 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Type" "VDMOS"
				(at 0 -19.05 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Pins" "1=D 2=G 3=S"
				(at 0 -15.24 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "transistor NMOS N-MOS N-MOSFET simulation"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "NMOS_0_1"
				(polyline
					(pts
						(xy 0.254 0) (xy -2.54 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.254 1.905) (xy 0.254 -1.905)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.762 -1.27) (xy 0.762 -2.286)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.762 0.508) (xy 0.762 -0.508)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.762 2.286) (xy 0.762 1.27)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 2.54 2.54) (xy 2.54 1.778)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 2.54 -2.54) (xy 2.54 0) (xy 0.762 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.762 -1.778) (xy 3.302 -1.778) (xy 3.302 1.778) (xy 0.762 1.778)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.016 0) (xy 2.032 0.381) (xy 2.032 -0.381) (xy 1.016 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.794 0.508) (xy 2.921 0.381) (xy 3.683 0.381) (xy 3.81 0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 3.302 0.381) (xy 2.921 -0.254) (xy 3.683 -0.254) (xy 3.302 0.381)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 1.651 0)
					(radius 2.794)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 2.54 -1.778)
					(radius 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(circle
					(center 2.54 1.778)
					(radius 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
			)
			(symbol "NMOS_1_1"
				(pin passive line
					(at 2.54 5.08 270)
					(length 2.54)
					(name "D"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at -5.08 0 0)
					(length 2.54)
					(name "G"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 -5.08 90)
					(length 2.54)
					(name "S"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "Simulation_SPICE:OPAMP"
			(pin_names
				(offset 0.254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at 3.81 3.175 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "${SIM.PARAMS}"
				(at 3.81 -3.175 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Operational amplifier, single, node sequence=1:+ 2:- 3:OUT 4:V+ 5:V-"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Pins" "1=1 2=2 3=3 4=4 5=5"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Device" "SPICE"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Sim.Params" "type=\"X\" model=\"OPAMP\" lib=\"\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "simulation"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "OPAMP_0_1"
				(polyline
					(pts
						(xy 5.08 0) (xy -5.08 5.08) (xy -5.08 -5.08) (xy 5.08 0)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "OPAMP_1_1"
				(pin input line
					(at -7.62 2.54 0)
					(length 2.54)
					(name "+"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at -7.62 -2.54 0)
					(length 2.54)
					(name "-"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin output line
					(at 7.62 0 180)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at -2.54 7.62 270)
					(length 3.81)
					(name "V+"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at -2.54 -7.62 90)
					(length 3.81)
					(name "V-"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "Simulation_SPICE:VDC"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "1"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Voltage source, DC"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Pins" "1=+ 2=-"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Type" "DC"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Device" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "ki_keywords" "simulation"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VDC_0_0"
				(polyline
					(pts
						(xy -1.27 0.254) (xy 1.27 0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.762 -0.254) (xy -1.27 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.254 -0.254) (xy -0.254 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -0.254) (xy 0.762 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VDC_0_1"
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "VDC_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "Simulation_SPICE:VPULSE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "VPULSE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Voltage source, pulse"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Pins" "1=+ 2=-"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Type" "PULSE"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Device" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Sim.Params" "y1=0 y2=1 td=2n tr=2n tf=2n tw=50n per=100n"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "ki_keywords" "simulation"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VPULSE_0_0"
				(polyline
					(pts
						(xy -2.032 -0.762) (xy -1.397 -0.762) (xy -1.143 0.762) (xy -0.127 0.762) (xy 0.127 -0.762) (xy 1.143 -0.762)
						(xy 1.397 0.762) (xy 2.032 0.762)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VPULSE_0_1"
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "VPULSE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "power:GND"
			(power)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "GND"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "GND_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "GND_1_1"
				(pin power_in line
					(at 0 0 270)
					(length 0) hide
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "power:PWR_FLAG"
			(power)
			(pin_numbers hide)
			(pin_names
				(offset 0) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#FLG"
				(at 0 1.905 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "PWR_FLAG"
				(at 0 3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Special symbol for telling ERC where power comes from"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "flag power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "PWR_FLAG_0_0"
				(pin power_out line
					(at 0 0 90)
					(length 0)
					(name "pwr"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "PWR_FLAG_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 1.27) (xy -1.016 1.905) (xy 0 2.54) (xy 1.016 1.905) (xy 0 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
		)
	)
	(junction
		(at 190.5 72.39)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "12ae68ec-65ac-45f6-84a6-f3871d7516bb")
	)
	(junction
		(at 200.66 99.06)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "130d5034-b1dd-4ffb-9beb-bfbcabab9f6d")
	)
	(junction
		(at 175.26 27.94)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "1b7b6db8-6f12-40f6-a8e9-98b7cb8696ad")
	)
	(junction
		(at 118.11 57.15)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "2d3f63fa-f055-41dd-ac45-1a4e0d0102b0")
	)
	(junction
		(at 139.7 95.25)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "3102847b-fdef-4419-a9d3-d1de406e1efc")
	)
	(junction
		(at 154.94 27.94)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "4434d396-bec9-4374-a5bf-69df44a9a4c1")
	)
	(junction
		(at 196.85 57.15)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "455ab2c9-773c-458b-8b55-ecb029dc84d2")
	)
	(junction
		(at 171.45 80.01)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "51517134-a110-4519-90f2-4bffd96a79f5")
	)
	(junction
		(at 113.03 57.15)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "68a6e438-e323-4de7-a175-abcb964e334a")
	)
	(junction
		(at 118.11 27.94)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "7a1c0c28-c256-4c98-9fb7-a736a085f37a")
	)
	(junction
		(at 171.45 69.85)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "7cab3669-ec70-426d-b958-27f2f405457f")
	)
	(junction
		(at 152.4 69.85)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "7e33891f-7391-492d-b065-349dd0ff2915")
	)
	(junction
		(at 190.5 80.01)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "a4842c4b-b9a5-45f6-b449-9b8130587d10")
	)
	(junction
		(at 196.85 27.94)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "a64f02f1-a8d3-4593-83f2-c4e485df117f")
	)
	(junction
		(at 175.26 57.15)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "a6d3ef36-0b33-4802-8393-7357ba3d1fea")
	)
	(junction
		(at 154.94 57.15)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "d235d83e-d6ee-4302-89a6-edc6c73412d1")
	)
	(junction
		(at 129.54 57.15)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "e4deb26e-96f7-4285-bc66-c92b85960c76")
	)
	(junction
		(at 152.4 57.15)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "ea13f65f-31aa-4ce2-b7e7-5cdb0e30138f")
	)
	(junction
		(at 196.85 44.45)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "f95a5006-74af-4c40-9c60-1df6dc079ba5")
	)
	(wire
		(pts
			(xy 144.78 81.28) (xy 144.78 83.82)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "01639ef3-413d-4535-a2b7-56c47319398a")
	)
	(wire
		(pts
			(xy 196.85 27.94) (xy 196.85 35.56)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0d12eeee-01fa-496b-bd45-36d887c427a0")
	)
	(wire
		(pts
			(xy 129.54 95.25) (xy 129.54 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "167c5d85-f364-4f6e-bc17-b93b1d232ef4")
	)
	(wire
		(pts
			(xy 175.26 27.94) (xy 196.85 27.94)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1c811bb9-7285-482f-8465-df85a9e46cae")
	)
	(wire
		(pts
			(xy 139.7 95.25) (xy 129.54 95.25)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1ceb4dea-df12-497c-9153-9cd7a633520a")
	)
	(wire
		(pts
			(xy 129.54 57.15) (xy 152.4 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1d4661c8-a249-4a3c-a8b6-3778512072ce")
	)
	(wire
		(pts
			(xy 118.11 57.15) (xy 129.54 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1ed63768-28a2-4f7a-ae5a-ee9cd36c8150")
	)
	(wire
		(pts
			(xy 191.77 99.06) (xy 191.77 100.33)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1fb8598f-9800-49c1-94c4-39240c17e5a2")
	)
	(wire
		(pts
			(xy 113.03 27.94) (xy 118.11 27.94)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "202d0b1f-a60b-4df3-9902-176d4392c865")
	)
	(wire
		(pts
			(xy 152.4 57.15) (xy 152.4 59.69)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "34ff03a3-b2b9-4e40-8e43-de293f1b5f6e")
	)
	(wire
		(pts
			(xy 175.26 52.07) (xy 175.26 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "37a08755-2836-49bd-a008-4d7653600d05")
	)
	(wire
		(pts
			(xy 152.4 67.31) (xy 152.4 69.85)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "3b14e448-511f-4ee5-b85e-0b7a6161ebe2")
	)
	(wire
		(pts
			(xy 175.26 27.94) (xy 175.26 44.45)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "3e23083e-3952-4fff-b38f-df1ff20c015b")
	)
	(wire
		(pts
			(xy 113.03 27.94) (xy 113.03 34.29)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "3e67ddbc-86bc-4302-9c92-2f07fd7d61ef")
	)
	(wire
		(pts
			(xy 189.23 67.31) (xy 212.09 67.31)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "498444e4-1b83-4d81-b606-59d63f0ec1ff")
	)
	(wire
		(pts
			(xy 152.4 57.15) (xy 154.94 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "53b6d000-3560-4d4a-b9ef-5f52338c8558")
	)
	(wire
		(pts
			(xy 144.78 69.85) (xy 152.4 69.85)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "58d888a8-189a-468f-a2a1-dda2d036b0ac")
	)
	(wire
		(pts
			(xy 200.66 99.06) (xy 200.66 100.33)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "605f6531-918c-4afe-bc51-4eeaa2c7b84f")
	)
	(wire
		(pts
			(xy 177.8 83.82) (xy 171.45 83.82)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "60df9a96-9325-4919-a8e2-b7e14dd3e78e")
	)
	(wire
		(pts
			(xy 144.78 69.85) (xy 144.78 71.12)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "69170d08-c173-475f-bdb7-4d7bb5df3558")
	)
	(wire
		(pts
			(xy 152.4 69.85) (xy 156.21 69.85)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "69a6c302-9749-41db-8538-309ce7a09d0c")
	)
	(wire
		(pts
			(xy 113.03 57.15) (xy 113.03 60.96)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6c346694-c966-45bf-9e47-7c5328d478bd")
	)
	(wire
		(pts
			(xy 171.45 80.01) (xy 171.45 69.85)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "70fd30ad-091c-48cd-ad74-b683fc7f44c2")
	)
	(wire
		(pts
			(xy 200.66 85.09) (xy 200.66 87.63)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "77e4ce55-1929-4d45-89cf-f8e55529aeba")
	)
	(wire
		(pts
			(xy 207.01 72.39) (xy 201.93 72.39)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "785b7180-2ff2-4886-bf38-be664a10fa0a")
	)
	(wire
		(pts
			(xy 139.7 95.25) (xy 144.78 95.25)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "7f4aadaa-1fab-47e4-bf3d-4256a9eb4c60")
	)
	(wire
		(pts
			(xy 196.85 44.45) (xy 196.85 46.99)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "80335163-c401-4b43-8c75-d689619d631b")
	)
	(wire
		(pts
			(xy 212.09 27.94) (xy 212.09 44.45)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "83674044-cc8d-4cd1-a634-fc99f7587ace")
	)
	(wire
		(pts
			(xy 196.85 57.15) (xy 212.09 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "87d24144-9253-4195-ad65-d328948ebce5")
	)
	(wire
		(pts
			(xy 139.7 93.98) (xy 139.7 95.25)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "8ebef15f-2132-41fb-bc3a-7514dc9a1ed2")
	)
	(wire
		(pts
			(xy 207.01 44.45) (xy 207.01 72.39)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9313e136-bf89-49e2-ab24-33d44713cba6")
	)
	(wire
		(pts
			(xy 200.66 99.06) (xy 191.77 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "94284d5a-49d4-4836-a5a2-842b242fea4a")
	)
	(wire
		(pts
			(xy 196.85 54.61) (xy 196.85 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "94e1e8a9-d5b2-4d77-95d9-d956639e77e8")
	)
	(wire
		(pts
			(xy 139.7 67.31) (xy 139.7 83.82)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9540d3b1-89b4-49bf-8980-0efc2c255ab8")
	)
	(wire
		(pts
			(xy 154.94 27.94) (xy 154.94 43.18)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "99f6d92d-f7b6-4ffa-9947-e03e60911d28")
	)
	(wire
		(pts
			(xy 196.85 44.45) (xy 207.01 44.45)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9fa9c852-ae41-42bc-8077-64c0236d30a0")
	)
	(wire
		(pts
			(xy 185.42 83.82) (xy 190.5 83.82)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a367ae32-8bf9-452c-a4a0-25a343839b18")
	)
	(wire
		(pts
			(xy 189.23 72.39) (xy 190.5 72.39)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a4c946c6-b8f8-4d9e-bfb8-a4fe135b19b7")
	)
	(wire
		(pts
			(xy 118.11 27.94) (xy 125.73 27.94)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a59d7f43-0925-4e97-b642-629fdc0240a8")
	)
	(wire
		(pts
			(xy 171.45 69.85) (xy 173.99 69.85)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a6bc02dc-8ab9-459d-b2ea-48456bb4c356")
	)
	(wire
		(pts
			(xy 184.15 60.96) (xy 184.15 62.23)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "aee3fe5c-d507-40d4-ab07-9388d7def631")
	)
	(wire
		(pts
			(xy 184.15 77.47) (xy 184.15 78.74)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "aff0b1cc-e5eb-467d-be2f-b7a08d0dd919")
	)
	(wire
		(pts
			(xy 154.94 27.94) (xy 162.56 27.94)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b2bf900b-412c-4f4a-828c-c321fa98b898")
	)
	(wire
		(pts
			(xy 163.83 69.85) (xy 171.45 69.85)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b5893fbd-f130-4359-889a-05cbf9ccf3d4")
	)
	(wire
		(pts
			(xy 196.85 43.18) (xy 196.85 44.45)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b9a38a6b-9b07-40fd-aa68-c36caaa4ae61")
	)
	(wire
		(pts
			(xy 154.94 53.34) (xy 154.94 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "bc0ba47e-2cba-406c-9d9f-84572a5da2b4")
	)
	(wire
		(pts
			(xy 190.5 80.01) (xy 190.5 72.39)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "bc4d0ac3-1fa4-487e-b3a3-4c2dbcef6abb")
	)
	(wire
		(pts
			(xy 139.7 48.26) (xy 147.32 48.26)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "bf9f9a72-078b-4d38-b9f8-cfc60d961b80")
	)
	(wire
		(pts
			(xy 200.66 110.49) (xy 200.66 113.03)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "c2d4de30-190a-47a7-afb8-c646228bab31")
	)
	(wire
		(pts
			(xy 143.51 27.94) (xy 154.94 27.94)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "c398df48-872d-44eb-8f3e-79db6f32a2e8")
	)
	(wire
		(pts
			(xy 144.78 93.98) (xy 144.78 95.25)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ca9f5739-2f05-4fbc-8153-00ea5efc82fb")
	)
	(wire
		(pts
			(xy 171.45 80.01) (xy 175.26 80.01)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cec7392c-aeb5-46ff-99d6-f749ac78fb13")
	)
	(wire
		(pts
			(xy 212.09 77.47) (xy 212.09 81.28)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cf848356-1fb9-4251-ae9f-1f8279dcb7bf")
	)
	(wire
		(pts
			(xy 196.85 27.94) (xy 212.09 27.94)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d203c0c6-c45e-41c5-8390-96537379a204")
	)
	(wire
		(pts
			(xy 154.94 57.15) (xy 175.26 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e0b3c81c-d9a7-4385-8fbb-75385ec2f11a")
	)
	(wire
		(pts
			(xy 212.09 57.15) (xy 212.09 52.07)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e1ae99c8-e9a6-4f3d-bce9-e58b4fdb5ddf")
	)
	(wire
		(pts
			(xy 133.35 27.94) (xy 135.89 27.94)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e26879e4-5e00-43eb-9a43-07f7154f9b00")
	)
	(wire
		(pts
			(xy 180.34 80.01) (xy 190.5 80.01)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e80dadea-d9ab-4316-8153-bf41ee0e90ea")
	)
	(wire
		(pts
			(xy 113.03 57.15) (xy 113.03 44.45)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ead70821-4c6b-48e1-870e-4b651f7fadd3")
	)
	(wire
		(pts
			(xy 171.45 83.82) (xy 171.45 80.01)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "eb26091d-2873-4ff9-8671-3223f3360d9b")
	)
	(wire
		(pts
			(xy 139.7 48.26) (xy 139.7 59.69)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f4a0223d-b7a8-4b12-b543-44f96ef2d613")
	)
	(wire
		(pts
			(xy 170.18 27.94) (xy 175.26 27.94)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f64e38dd-7805-4931-ae09-a843c724a177")
	)
	(wire
		(pts
			(xy 200.66 97.79) (xy 200.66 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f7b652ad-9f13-43d1-ab48-5f0dee096be9")
	)
	(wire
		(pts
			(xy 175.26 57.15) (xy 196.85 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f7f19d7e-f5e7-41a7-8ecc-aa1c369e08a8")
	)
	(wire
		(pts
			(xy 194.31 72.39) (xy 190.5 72.39)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "fcc9ef50-c7cb-4a94-bcbd-de1ae1258ef5")
	)
	(wire
		(pts
			(xy 190.5 83.82) (xy 190.5 80.01)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "fd6be46e-4ea8-429c-b250-21b44ee95e39")
	)
	(wire
		(pts
			(xy 113.03 57.15) (xy 118.11 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "fe364cfb-9696-4c2f-be9a-8bce5dbe762e")
	)
	(text ".options chgtol=1e-10 abstol=10u method=gear\n.control\nset controlswait\nrusage time\nversion -s\n.endc\n"
		(exclude_from_sim no)
		(at 120.015 105.41 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "31d4ed55-c67d-4e6e-b829-65130360a5f4")
	)
	(label "in"
		(at 113.03 33.02 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "1b8d9aba-9870-40e7-9df0-5674e3b6856e")
	)
	(label "out"
		(at 207.01 27.94 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "28354f56-4ab2-4e11-a395-bc1b85d80cb3")
	)
	(label "vdiv"
		(at 207.01 62.23 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "372d77bf-afbf-45df-85c9-4a4a14254740")
	)
	(label "vcc"
		(at 184.15 60.96 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "3b537da3-77d5-47b0-b578-5c3f3f9b46bc")
	)
	(label "gg"
		(at 140.97 48.26 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "8b118ed8-7470-428b-a9f7-581d28b3db0d")
	)
	(label "sout"
		(at 154.94 35.56 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "8b83786f-1893-4941-b993-1a57cada4824")
	)
	(label "vcc"
		(at 200.66 85.09 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "8e489cbb-b4be-49db-ab7e-c4531614c0bb")
	)
	(label "vee"
		(at 200.66 113.03 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "8f8e9e75-d0c9-4d5d-b0dd-47dec94f58e2")
	)
	(label "pw"
		(at 139.7 73.66 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "95cf3e7c-1e4c-4c23-aff0-583d3e05f78d")
	)
	(label "vee"
		(at 184.15 78.74 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "a66e7050-9f31-4f4e-82f2-6c2b479d75a3")
	)
	(label "vcont"
		(at 144.78 82.55 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "f0aa7629-076a-42b0-b81d-77a85c9c71fb")
	)
	(symbol
		(lib_id "power:GND")
		(at 113.03 60.96 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "04c86d6b-266b-43e8-a85e-30429a7ff1d0")
		(property "Reference" "#PWR01"
			(at 113.03 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 113.03 66.04 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 113.03 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 113.03 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 113.03 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "9fd7163a-943d-4d89-aa3f-8968ddabce68")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "#PWR01")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "#PWR01")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:L")
		(at 139.7 27.94 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "250e7139-aa1d-4ff0-8df8-e897488152a8")
		(property "Reference" "L1"
			(at 139.7 22.86 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "10u"
			(at 139.7 25.4 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 139.7 27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 139.7 27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Inductor"
			(at 139.7 27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "20d4debc-94ba-41c0-8964-7d0e622035e2")
		)
		(pin "2"
			(uuid "009efdbd-ed9e-4690-943a-ffa45ba2a404")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "L1")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "L1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R")
		(at 198.12 72.39 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "25463d3b-4954-46e7-b110-2e492a916f14")
		(property "Reference" "R2"
			(at 201.93 74.93 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "10k"
			(at 201.93 77.47 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 198.12 70.612 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 198.12 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor"
			(at 198.12 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "2ba71ec7-7689-417e-a46e-dacdd29e95a8")
		)
		(pin "2"
			(uuid "77e3f398-5182-45e9-9b76-394cb202fa71")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "R2")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "R6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 191.77 100.33 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "3527e000-0d9c-436e-8170-a1eddc34ded8")
		(property "Reference" "#PWR01"
			(at 191.77 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 191.77 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 191.77 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 191.77 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 191.77 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "09bda2e1-a474-497d-ae18-cde486a6f464")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "#PWR01")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "#PWR03")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Simulation_SPICE:OPAMP")
		(at 181.61 69.85 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "39841637-94fb-4e25-99f0-500afe93d3a9")
		(property "Reference" "U1"
			(at 175.26 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Generic OpAmp"
			(at 175.26 66.04 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 181.61 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 181.61 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Operational amplifier, single, node sequence=1:+ 2:- 3:OUT 4:V+ 5:V-"
			(at 181.61 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=in+ 2=in- 3=out 4=vcc 5=vee"
			(at 181.61 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "SUBCKT"
			(at 181.61 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Library" "GenOpAmp.lib"
			(at 181.61 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Name" "genopa1"
			(at 181.61 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "5"
			(uuid "0645d9be-eafd-43a1-b6eb-9b6d83dc4e76")
		)
		(pin "2"
			(uuid "af65650d-6ee7-42d3-91ed-605bdeb34354")
		)
		(pin "4"
			(uuid "aff80828-3134-48b8-b2cc-0b52cc5abcc4")
		)
		(pin "3"
			(uuid "22568542-30d6-40e0-8e69-bc5e9137d796")
		)
		(pin "1"
			(uuid "1bf6361a-01be-43a7-8ddf-f3c7d9857c0b")
		)
		(instances
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "U1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Simulation_SPICE:VDC")
		(at 200.66 105.41 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "477f8e04-f62e-48a4-a813-a1aa66c941b7")
		(property "Reference" "V5"
			(at 204.47 104.0101 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10"
			(at 204.47 106.5501 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 200.66 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 200.66 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Voltage source, DC"
			(at 200.66 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 200.66 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Type" "DC"
			(at 200.66 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "V"
			(at 200.66 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "834dfcb3-13d8-4590-b99b-5a04cc3caf50")
		)
		(pin "2"
			(uuid "9697475a-9fee-45ea-9065-0d3d304da9a5")
		)
		(instances
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "V5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Simulation_SPICE:VPULSE")
		(at 113.03 39.37 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "4d94497b-eb17-4a0e-b679-633727a5d19f")
		(property "Reference" "V3"
			(at 116.84 36.7001 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "VPULSE"
			(at 116.84 39.2401 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 113.03 39.37 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 113.03 39.37 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Voltage source, pulse"
			(at 113.03 39.37 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 113.03 39.37 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Type" "PULSE"
			(at 113.03 39.37 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "V"
			(at 113.03 39.37 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Params" "y1=0 y2=2.5 td=0 tr=5m tf=5m tw=1 per=2"
			(at 116.84 41.7801 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(pin "1"
			(uuid "da9574da-3303-4351-acab-d4098e2e8071")
		)
		(pin "2"
			(uuid "ade2a0a4-af15-47a3-92ff-a3d48f5a8c3e")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "V3")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "V1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 212.09 81.28 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "583d4d4a-a818-405e-b507-6ce699130c3a")
		(property "Reference" "#PWR01"
			(at 212.09 87.63 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 212.09 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 212.09 81.28 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 212.09 81.28 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 212.09 81.28 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "37d6d181-70f1-4f57-b6a2-44c89d3980b7")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "#PWR01")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "#PWR04")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Simulation_SPICE:VDC")
		(at 212.09 72.39 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "6473ecb1-40e0-41f7-9146-69c8b95042c0")
		(property "Reference" "V6"
			(at 215.9 70.9901 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "0.5"
			(at 215.9 73.5301 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 212.09 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 212.09 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Voltage source, DC"
			(at 212.09 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 212.09 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Type" "DC"
			(at 212.09 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "V"
			(at 212.09 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "e7dc11b1-e737-4a9b-9d35-b5dff38e288f")
		)
		(pin "2"
			(uuid "262bcb5d-da25-4613-8662-eafe41783380")
		)
		(instances
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "V6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R")
		(at 196.85 39.37 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "6c0eb6fa-4924-432d-a6df-c3ab08c0c453")
		(property "Reference" "R2"
			(at 199.39 38.0999 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "9.1k"
			(at 199.39 40.6399 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 195.072 39.37 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 196.85 39.37 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor"
			(at 196.85 39.37 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "388b3803-22d9-4df0-aa20-35bfca80a67e")
		)
		(pin "2"
			(uuid "7331998d-9e8d-4904-bd60-c50475d0294f")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "R2")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "R5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:PWR_FLAG")
		(at 118.11 57.15 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "774fbfea-f797-4d07-a312-352d3ef476c6")
		(property "Reference" "#FLG02"
			(at 118.11 55.245 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "PWR_FLAG"
			(at 118.11 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 118.11 57.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 118.11 57.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Special symbol for telling ERC where power comes from"
			(at 118.11 57.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "37f863a9-c64b-4f44-9281-38f24abaaef2")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "#FLG02")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "#FLG02")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Simulation_SPICE:VDC")
		(at 200.66 92.71 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "77f696d1-c509-43e3-907d-06de8d24ad97")
		(property "Reference" "V4"
			(at 204.47 91.3101 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10"
			(at 204.47 93.8501 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 200.66 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 200.66 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Voltage source, DC"
			(at 200.66 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 200.66 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Type" "DC"
			(at 200.66 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "V"
			(at 200.66 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "84a6ddd9-b4b0-44fa-8fda-b2bb7a0aff8b")
		)
		(pin "1"
			(uuid "867f29eb-0d09-4cfa-ab3e-957066592e15")
		)
		(instances
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "V4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 177.8 80.01 270)
		(mirror x)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "80e9e17e-a3bf-433f-b710-cec1243a31d1")
		(property "Reference" "C2"
			(at 177.7936 73.66 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "10n"
			(at 177.7936 76.2 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 177.8 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 177.8 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 177.8 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "73fac37a-e053-4406-bc2e-cf62e687888a")
		)
		(pin "2"
			(uuid "84cd28af-e2d3-4297-99a7-e08f28af22b8")
		)
		(instances
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "C2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C")
		(at 175.26 48.26 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "85a6f916-1457-4c3d-8701-0bf3cfcc87fd")
		(property "Reference" "C2"
			(at 179.07 46.9899 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "4.7u"
			(at 179.07 49.5299 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 176.2252 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 175.26 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor"
			(at 175.26 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "58bbbe44-896c-4c7f-bb15-5deafa3f2824")
		)
		(pin "2"
			(uuid "91b460f0-cb74-4071-bf8c-38b682ea85eb")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "C2")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "C1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "PWM:PWM")
		(at 139.7 88.9 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a0876daf-4804-42a4-b442-6c607746a09f")
		(property "Reference" "A1"
			(at 148.59 85.9154 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "PWM"
			(at 148.59 88.4554 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 139.7 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://ngspice.sourceforge.io/docs.html"
			(at 139.7 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Voltage-dependent Voltage source symbol for simulation only"
			(at 139.7 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Library" "pwm2_model.lib"
			(at 139.7 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Name" "pwm"
			(at 139.7 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "SUBCKT"
			(at 139.7 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=out+ 2=out- 3=in+ 4=in-"
			(at 111.76 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Params" "freq=490k vlo=0 vhi=10"
			(at 148.59 90.9954 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(pin "2"
			(uuid "8de1e97b-b022-482f-a05d-0088f6a8b8a6")
		)
		(pin "4"
			(uuid "292d43bf-9ef7-49ee-99be-599c25bef44c")
		)
		(pin "3"
			(uuid "2a2730c7-3ed3-4e49-a933-e37ae4a8b187")
		)
		(pin "1"
			(uuid "c22406d9-be7e-427f-b558-ce60d105f9b7")
		)
		(instances
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "A1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Simulation_SPICE:VDC")
		(at 144.78 76.2 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a8b86083-f6db-4a16-a598-7b699a6f3cdb")
		(property "Reference" "V3"
			(at 148.59 74.8001 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "-0.5"
			(at 148.59 77.3401 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 144.78 76.2 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 144.78 76.2 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Voltage source, DC"
			(at 144.78 76.2 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 144.78 76.2 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Type" "DC"
			(at 144.78 76.2 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "V"
			(at 144.78 76.2 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "9025f378-5aa8-498d-96e2-a4f828c25bed")
		)
		(pin "2"
			(uuid "071aa098-6d0f-4572-9eb3-7f32f7bf29cb")
		)
		(instances
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "V3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:PWR_FLAG")
		(at 118.11 27.94 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "baa951e9-39da-48fc-8f15-78b22d9b0100")
		(property "Reference" "#FLG01"
			(at 118.11 26.035 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "PWR_FLAG"
			(at 118.11 22.86 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 118.11 27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 118.11 27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Special symbol for telling ERC where power comes from"
			(at 118.11 27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "85fd9886-6776-4eb1-bb7e-3df87e7cdee2")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "#FLG01")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "#FLG01")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R")
		(at 181.61 83.82 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "bc86b202-1119-4c7e-8e3f-5dcd0c9b6ca5")
		(property "Reference" "R2"
			(at 186.69 82.55 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "10Meg"
			(at 186.69 85.09 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 181.61 82.042 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 181.61 83.82 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor"
			(at 181.61 83.82 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "c0980aea-e44b-41b9-a4c0-81828ddfd902")
		)
		(pin "2"
			(uuid "f9f39205-6e08-4da3-a11b-23bfe300f4f3")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "R2")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "R9")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R")
		(at 212.09 48.26 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "d064b09b-8991-4083-b35f-373ffc7fc887")
		(property "Reference" "R2"
			(at 214.63 46.9899 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "R='time >13m ? 20 : 60'"
			(at 214.63 49.5299 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 210.312 48.26 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 212.09 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor"
			(at 212.09 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b4cd77ea-56b0-438b-9ea5-927b27a517b2")
		)
		(pin "2"
			(uuid "41d8ab89-30c2-4ce8-8060-836b306c07e6")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "R2")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "R2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R")
		(at 152.4 63.5 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "d62808df-9b98-407a-9294-2ebe8ed36950")
		(property "Reference" "R2"
			(at 154.94 62.2299 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "1k"
			(at 154.94 64.7699 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" ""
			(at 154.178 63.5 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 152.4 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor"
			(at 152.4 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "bc303435-f905-49f0-b237-6eb61d68a761")
		)
		(pin "2"
			(uuid "1cb194bf-35f3-4408-8c7a-f56e2860dc57")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "R2")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "R8")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R")
		(at 196.85 50.8 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "da1fe7de-c7b5-4eec-903c-cf13718cf602")
		(property "Reference" "R2"
			(at 199.39 49.5299 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "1k"
			(at 199.39 52.0699 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 195.072 50.8 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 196.85 50.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor"
			(at 196.85 50.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "f7176dc6-613a-41e3-9c5d-ad89fe5cd0a6")
		)
		(pin "2"
			(uuid "1f4f7cf4-5ad2-4ebb-a1c4-ac76be0d67ca")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "R2")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "R4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_name "D_1")
		(lib_id "Simulation_SPICE:D")
		(at 166.37 27.94 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "dc258399-5bb0-46cf-9524-24812e9b7f97")
		(property "Reference" "D1"
			(at 166.37 16.51 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "D"
			(at 166.37 19.05 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 166.37 27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 166.37 27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Diode for simulation or PCB"
			(at 166.37 27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "D"
			(at 166.37 21.59 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Pins" "1=K 2=A"
			(at 166.37 24.13 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "795e11be-348e-4544-909b-1fc65bd08601")
		)
		(pin "2"
			(uuid "d966c507-f4d0-4bb0-964e-de9d865fd784")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "D1")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "D1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R")
		(at 160.02 69.85 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "e1f3b66c-3e38-4e17-a109-e422ab576333")
		(property "Reference" "R2"
			(at 160.02 63.5 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "20k"
			(at 160.02 66.04 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 160.02 68.072 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 160.02 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor"
			(at 160.02 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "38d7bcf4-e405-4d11-a0e3-2f220443b228")
		)
		(pin "2"
			(uuid "6abec415-d36e-442f-bb86-cab4327cf02b")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "R2")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "R7")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R")
		(at 139.7 63.5 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "e2d8f8da-c180-45e1-a73f-23af334d70d3")
		(property "Reference" "R2"
			(at 134.62 62.23 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "1"
			(at 134.62 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" ""
			(at 141.478 63.5 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 139.7 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor"
			(at 139.7 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "97c7c87d-d14a-49cf-a237-f2982a6211c3")
		)
		(pin "2"
			(uuid "960cca09-4cbd-445d-bc69-5c0474912f54")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "R2")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "R3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R")
		(at 129.54 27.94 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "ecc67a76-f6a7-41c5-a8c0-4eda9974f00e")
		(property "Reference" "R1"
			(at 129.54 22.86 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "2.2m"
			(at 129.54 25.4 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 129.54 29.718 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 129.54 27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor"
			(at 129.54 27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "f5a29544-a541-4867-afb0-a05640ffe1dd")
		)
		(pin "2"
			(uuid "dbd1d07b-08fe-48f1-b659-56913d163260")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "R1")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "R1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Simulation_SPICE:NMOS")
		(at 152.4 48.26 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "ff166950-3da1-4866-a975-7cac5b5f63a0")
		(property "Reference" "Q1"
			(at 158.75 46.9899 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "NMOS"
			(at 158.75 49.5299 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 157.48 45.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://ngspice.sourceforge.io/docs/ngspice-manual.pdf"
			(at 152.4 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "N-MOSFET transistor, drain/source/gate"
			(at 152.4 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "NMOS"
			(at 152.4 65.405 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Type" "VDMOS"
			(at 152.4 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=D 2=G 3=S"
			(at 152.4 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "443eb38a-f0f3-4045-af97-ee72d2e81e35")
		)
		(pin "2"
			(uuid "f8b0282a-687a-4840-b237-92513d52a04b")
		)
		(pin "3"
			(uuid "9d5540c8-90a1-4472-ac02-63d596559ace")
		)
		(instances
			(project "Boost2"
				(path "/2d8d9b43-db7e-4385-89ac-686735207a11"
					(reference "Q1")
					(unit 1)
				)
			)
			(project "smps-com"
				(path "/566bf9ad-05d9-4e09-9cf5-c84b6117df9c"
					(reference "Q1")
					(unit 1)
				)
			)
		)
	)
	(sheet_instances
		(path "/"
			(page "1")
		)
	)
)