(footprint "PinHeader_2x03_P2.54mm_Vertical"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "Through hole straight pin header, 2x03, 2.54mm pitch, double rows")
	(tags "Through hole pin header THT 2x03 2.54mm double row")
	(property "Reference" "REF**"
		(at 1.27 -2.33 0)
		(layer "F.SilkS")
		(uuid "8721d775-e198-41aa-9933-0810fee60c37")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "JUMPER_TRIPLE"
		(at 1.27 7.41 0)
		(layer "F.Fab")
		(uuid "c027e051-d7ea-4ab0-ad04-d151c025341e")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "1391dfe6-12bd-4db1-bc89-9833b582233a")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "d1d8b860-c1eb-4395-b61d-43c57984817d")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -1.33 -1.33)
		(end 0 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "1a4e5857-6e51-455e-b7c7-b53f59908d9e")
	)
	(fp_line
		(start -1.33 0)
		(end -1.33 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "4eba4dd1-4713-4aca-b6e2-7aa04761039b")
	)
	(fp_line
		(start -1.33 1.27)
		(end -1.33 6.41)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "079ced54-0637-4b01-abfb-fe013b0e7aef")
	)
	(fp_line
		(start -1.33 1.27)
		(end 1.27 1.27)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "b0fd3677-e1e9-4291-abea-4673aa82b265")
	)
	(fp_line
		(start -1.33 6.41)
		(end 3.87 6.41)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "c42c5efb-aa44-4e9a-9a39-deaa5ff82ea0")
	)
	(fp_line
		(start 1.27 -1.33)
		(end 3.87 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "a98926af-2278-49be-8265-7035a1342ae4")
	)
	(fp_line
		(start 1.27 1.27)
		(end 1.27 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "5b6648d5-752d-4a92-b362-dfcdbacf5933")
	)
	(fp_line
		(start 3.87 -1.33)
		(end 3.87 6.41)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "8cf417de-509f-41a1-8b83-7c6fc5648fb3")
	)
	(fp_line
		(start -1.8 -1.8)
		(end -1.8 6.85)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "fa48b872-4883-4ead-ac7d-04cd827b6fc1")
	)
	(fp_line
		(start -1.8 6.85)
		(end 4.35 6.85)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "b8a92d5c-28d6-4b01-b0fb-a7177f8e95a5")
	)
	(fp_line
		(start 4.35 -1.8)
		(end -1.8 -1.8)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "6a430a92-dd60-4b4d-bcaf-2ec5ca970374")
	)
	(fp_line
		(start 4.35 6.85)
		(end 4.35 -1.8)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "104493f0-f488-4baa-a4d8-7aba7f794411")
	)
	(fp_line
		(start -1.27 0)
		(end 0 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "5eea1a7a-a689-4db2-b83f-0cb49e0953ec")
	)
	(fp_line
		(start -1.27 6.35)
		(end -1.27 0)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "c61ba68a-5d4b-46bb-9aa9-77f0ec82acf9")
	)
	(fp_line
		(start 0 -1.27)
		(end 3.81 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "89dab040-3d72-47c2-b23b-c94a0ca1873f")
	)
	(fp_line
		(start 3.81 -1.27)
		(end 3.81 6.35)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "a790ea9e-1e7e-4022-ae50-5f3cd9f5386f")
	)
	(fp_line
		(start 3.81 6.35)
		(end -1.27 6.35)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "42aba6d3-3e94-4294-bb32-aa38d8155f03")
	)
	(fp_text user "${REFERENCE}"
		(at 1.27 2.54 90)
		(layer "F.Fab")
		(uuid "11352bd2-1759-44b7-8321-467011c15cd4")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "721bcac9-53c8-4c45-b9ff-b3183b571fcb")
	)
	(pad "2" thru_hole oval
		(at 2.54 0)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f49042e5-9172-40d5-9cd7-bb78ec8f2467")
	)
	(pad "3" thru_hole oval
		(at 0 2.54)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "d4a852ab-a0cc-4ecb-8926-d72ae21cecad")
	)
	(pad "4" thru_hole oval
		(at 2.54 2.54)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f6face81-efb7-4df1-8331-8942d9f33510")
	)
	(pad "5" thru_hole oval
		(at 0 5.08)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "e768e888-837d-43d3-bdfd-dc8ca20d92ec")
	)
	(pad "6" thru_hole oval
		(at 2.54 5.08)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "45ae9dc8-522c-4946-aed0-c5e296b669df")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_2x03_P2.54mm_Vertical.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
