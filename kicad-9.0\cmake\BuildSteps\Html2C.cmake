
# CMake script file to process a text file by wrapping every line in double quotes.
# Input file must not abuse quotes, staying with single quotes is probably best.

set( lines "" )
file( STRINGS ${inputFile} lines )


#Remark: strings can contain semicolon. a semicolon is a separation in cmake.
#so, to avoid stripping semicolon in variables we have to quote them

file( WRITE ${outputFile} "// Do not edit this file, it is autogenerated by CMake from an HTML file\n" )

foreach( line ${lines} )
    STRING(REGEX REPLACE "\"" "\\\\\"" linem "${line}" )
    file( APPEND ${outputFile} "\"" "${linem}" "\\n\"\n" )
endforeach( line "${lines}" )
