name: Build

on:
  push:
    branches:
      - getsentry
  pull_request:

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        platform: [ubuntu-latest, windows-latest, macos-latest]
    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v2
        with:
          submodules: "recursive"

      - name: Installing Linux Dependencies
        if: ${{ runner.os == 'Linux' }}
        run: |
          sudo apt update
          sudo apt install zlib1g-dev libcurl4-openssl-dev libssl-dev libunwind-dev pkg-config

      - name: Build crashpad
        run: |
          cmake -B cmake-build -D CRASHPAD_BUILD_TOOLS=On
          cmake --build cmake-build --parallel

      - name: Build crashpad with client-side stack traces
        run: |
          cmake -B cmake-build-stacks -D CRASHPAD_ENABLE_STACKTRACE=ON
          cmake --build cmake-build-stacks --parallel

      - name: Build crashpad Windows ARM64
        if: ${{ runner.os == 'Windows' }}
        run: |
          cmake -B cmake-build-arm64 -DCMAKE_TOOLCHAIN_FILE="cmake/toolchains/win_arm64.cmake" -DCRASHPAD_BUILD_TOOLS=On
          cmake --build cmake-build-arm64 --config RelWithDebInfo -- /p:Platform=ARM64

      - name: Build crashpad with client-side stack traces Windows ARM64
        if: ${{ runner.os == 'Windows' }}
        run: |
          cmake -B cmake-build-stacks-arm64 -DCMAKE_TOOLCHAIN_FILE="cmake/toolchains/win_arm64.cmake" -DCRASHPAD_ENABLE_STACKTRACE=ON
          cmake --build cmake-build-stacks-arm64 --config RelWithDebInfo -- /p:Platform=ARM64

  build-ios:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v2
        with:
          submodules: "recursive"
      - run: |
          cmake -B crashpad-xcode -GXcode -DCMAKE_SYSTEM_NAME=iOS
          xcodebuild build -project crashpad-xcode/crashpad.xcodeproj
