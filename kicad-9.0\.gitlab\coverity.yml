# This script is responsible for configuring the coverity file from the cache
# (e.g. extracting it or updating it if needed)
.coverity_cache_prep: &coverity_cache_prep |
  echo "Downloading MD5 hash of current Coverity Scan version to compare against cache"
  curl --output cov-analysis-linux64.md5 https://scan.coverity.com/download/linux64 \
       --form project=$COVERITY_SCAN_PROJECT_NAME \
       --form token=$COVERITY_SCAN_TOKEN \
       --form md5=1
  echo " *cov-analysis-linux64.tgz" >> cov-analysis-linux64.md5
  (md5sum --ignore-missing -c cov-analysis-linux64.md5) || (
    echo "Downloading new Coverity Scan version"
    curl --output cov-analysis-linux64.tgz https://scan.coverity.com/download/linux64 \
         --form project=$COVERITY_SCAN_PROJECT_NAME \
         --form token=$COVERITY_SCAN_TOKEN
  )
  echo "Extracting Coverity Scan"
  mkdir coverity/
  tar xzf cov-analysis-linux64.tgz --strip-components=1 -C coverity
  test -d coverity/bin

# This script is responsible for tar'ing and submitting the results of the build.
# These results will be saved for 1 day if the build fails.
.coverity_submit: &coverity_submit |
  echo "Creating tar file of scan results"
  export COV_INT_FILENAME=cov-int-$(date +"%Y%m%d%H%M").tar.gz
  tar cfz $COV_INT_FILENAME cov-int
  echo "Submitting scan results"
  s5cmd/s5cmd cp $COV_INT_FILENAME s3://$KICAD_CI_R2_BUCKET/coverity/
  export FILE_URL="$KICAD_CI_R2_PUBLIC_BASE/coverity/$COV_INT_FILENAME"
  export KICAD_VERSION=$(sed 's/[()]//g' kicad_build_version.txt)
  curl https://scan.coverity.com/builds?project=$COVERITY_SCAN_PROJECT_NAME \
       --form token=$COVERITY_SCAN_TOKEN \
       --form email=$GITLAB_USER_EMAIL \
       --form url="$FILE_URL" \
       --form version="$KICAD_VERSION" \
       --form description="$KICAD_VERSION / $CI_COMMIT_TITLE" 2>&1 \
        | tee curl-response.txt
  grep -q 'Build successfully submitted' curl-response.txt


Coverity:
  tags:
    - coverity
  stage: build
  image: registry.gitlab.com/kicad/kicad-ci/source_containers/master/fedora:40
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule" && $SCHEDULED_JOB_NAME == "coverity"
  cache:
    key: coverity
    paths:
      - cov-analysis-linux64.tgz
  before_script:
    - export COVERITY_SCAN_PROJECT_NAME="kicad"
    - test "$(git rev-parse --is-shallow-repository)" = "false" || (git fetch --unshallow)
    - git fetch origin
    - curl -LO https://github.com/peak/s5cmd/releases/download/v2.0.0/s5cmd_2.0.0_Linux-64bit.tar.gz
    - mkdir s5cmd
    - tar -xvf ./s5cmd_2.0.0_Linux-64bit.tar.gz -C s5cmd
    - export AWS_ACCESS_KEY_ID=$KICAD_CI_R2_KEY_ID
    - export AWS_SECRET_ACCESS_KEY=$KICAD_CI_R2_ACCESS_KEY
    - export S3_ENDPOINT_URL=$KICAD_CI_R2_ENDPOINT
  script:
    - *coverity_cache_prep
    - cmake
      -DCMAKE_BUILD_TYPE=RelWithDebInfo
      -DKICAD_STDLIB_LIGHT_DEBUG=ON
      -DKICAD_SCRIPTING_WXPYTHON=ON
      -DKICAD_USE_CMAKE_FINDPROTOBUF=ON
    - coverity/bin/cov-build --dir cov-int make -j10
    - *coverity_submit
  artifacts:
    expire_in: 1 year
    expose_as: 'Coverity log'
    name: "coverity_log.txt"
    paths:
      - cov-int/build-log.txt
