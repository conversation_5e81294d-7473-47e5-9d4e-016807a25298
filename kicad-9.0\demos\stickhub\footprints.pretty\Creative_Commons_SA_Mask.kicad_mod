(footprint "Creative_Commons_SA_Mask"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(property "Reference" "REF**"
		(at 0 -1.55 0)
		(unlocked yes)
		(layer "F.SilkS")
		(hide yes)
		(uuid "cc28a436-5b39-4f19-92ba-81c7d3d6f044")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "Logo"
		(at 0 2 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "9c11b373-**************-394dedc8431a")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "B.Fab")
		(hide yes)
		(uuid "cc12cbfe-4a7e-4544-bd85-ed51580c5f5c")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
			(justify mirror)
		)
	)
	(property "Description" "Not Populated"
		(at 150.9 -97.1 0)
		(layer "B.Fab")
		(hide yes)
		(uuid "fe33e75d-3b95-4a61-8c54-16f7f03a4d8d")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
			(justify mirror)
		)
	)
	(attr exclude_from_pos_files)
	(fp_line
		(start 0.35 -0.15)
		(end 0.35 0.15)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.Mask")
		(uuid "4dffde25-cb02-4921-90b8-a71e2cce6f40")
	)
	(fp_arc
		(start 0.35 0.15)
		(mid 0 0.5)
		(end -0.35 0.15)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.Mask")
		(uuid "b878ab62-8126-489f-be7c-b4a839a50c57")
	)
	(fp_arc
		(start -0.35 -0.15)
		(mid 0 -0.5)
		(end 0.35 -0.15)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.Mask")
		(uuid "a1ae76f1-cc5f-482b-aaf8-e767b0c6918d")
	)
	(fp_circle
		(center 0 0)
		(end 0.75 0)
		(stroke
			(width 0.15)
			(type solid)
		)
		(fill no)
		(layer "F.Mask")
		(uuid "32b5e2bc-47e1-44cc-b6ce-5b1d353c7f6c")
	)
	(fp_poly
		(pts
			(xy -0.35 0.025) (xy -0.5 -0.2) (xy -0.2 -0.2)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(fill yes)
		(layer "F.Mask")
		(uuid "60e9123c-04f1-4d8b-a56a-840111fee39a")
	)
	(fp_poly
		(pts
			(xy -0.275 0.15) (xy -0.425 0.15) (xy -0.425 0.075) (xy -0.275 0.075)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(fill yes)
		(layer "F.Mask")
		(uuid "ba9ecb7e-4a72-4eb4-baa1-149bdb819bae")
	)
	(fp_text user "${REFERENCE}"
		(at 0 2.5 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "5accae93-c512-4072-9065-190da2748458")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(embedded_fonts no)
)
