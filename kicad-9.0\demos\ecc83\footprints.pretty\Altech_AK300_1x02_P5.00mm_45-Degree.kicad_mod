(footprint "Altech_AK300_1x02_P5.00mm_45-Degree"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "Altech AK300 serie terminal block (Script generated with StandardBox.py) (http://www.altechcorp.com/PDFS/PCBMETRC.PDF)")
	(tags "Altech AK300 serie connector")
	(property "Reference" "REF**"
		(at 0 -7.2 0)
		(layer "F.SilkS")
		(uuid "f668ae95-72d5-478e-8e29-6976957e88d1")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "IN"
		(at 2.5 7.5 0)
		(layer "F.Fab")
		(uuid "f052de58-b666-4520-8cfc-5d5e44742081")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "bb7b92d7-d6f3-45f7-8634-1b1008d317d5")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "1295af40-e5df-49d7-881e-c304392033aa")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -3 -6.5)
		(end 0 -6.5)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "0aec1412-9508-4f77-9feb-a600563c07f3")
	)
	(fp_line
		(start -3 -3.5)
		(end -3 -6.5)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "03c457bc-45e5-4f65-81d5-9154cf0a99f9")
	)
	(fp_line
		(start -2.62 -6.12)
		(end -2.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "b225ac49-35d1-44f4-900e-9b6a43b52503")
	)
	(fp_line
		(start -2.62 -6.12)
		(end 7.62 -6.12)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "93f93325-b1aa-49dd-b19d-a8af38cf25ee")
	)
	(fp_line
		(start -2.62 6.62)
		(end 7.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "c641413a-9cd3-4717-a60a-1498e6518ea0")
	)
	(fp_line
		(start 7.62 -6.12)
		(end 7.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "eea5a3c7-482a-44ce-961b-6bf34d161444")
	)
	(fp_line
		(start -2.75 -6.25)
		(end -2.75 6.75)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "c9712f4a-93fd-46f6-8296-2aed13555658")
	)
	(fp_line
		(start -2.75 -6.25)
		(end 7.75 -6.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "5ae61a3a-1542-4af8-aeda-6e9315bbd6b5")
	)
	(fp_line
		(start -2.75 6.75)
		(end 7.75 6.75)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "d112cef4-83db-4929-bdcc-9419899839d6")
	)
	(fp_line
		(start 7.75 -6.25)
		(end 7.75 6.75)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "1505bafd-5ac8-47b7-af2b-08ffb381b8d2")
	)
	(fp_line
		(start -2.5 -5.5)
		(end -2 -6)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "951bd5e3-fddb-46eb-85bc-74d4feb78848")
	)
	(fp_line
		(start -2.5 6.5)
		(end -2.5 -5.5)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "259547a5-49a4-4334-aaef-6c3bb6991ebf")
	)
	(fp_line
		(start -2 -6)
		(end 7.5 -6)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "26c44e55-b13b-46ce-872a-4c77376d244d")
	)
	(fp_line
		(start 7.5 -6)
		(end 7.5 6.5)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "dcef28de-896e-43cd-a4ab-e10421d032c8")
	)
	(fp_line
		(start 7.5 6.5)
		(end -2.5 6.5)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "be86e1c3-dc44-44a6-9c29-52746085cf4b")
	)
	(fp_text user "${REFERENCE}"
		(at 2.7 2.75 0)
		(layer "F.Fab")
		(uuid "b6685002-56ab-465c-beb2-0ff5507a1e4f")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 3 3)
		(drill 1.5)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "52cc78fc-0cff-45da-acd5-da98cbfbee77")
	)
	(pad "2" thru_hole circle
		(at 5 0)
		(size 3 3)
		(drill 1.5)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c279e659-509e-4229-ae40-413ac3663930")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/TerminalBlock_Altech.3dshapes/Altech_AK300_1x02_P5.00mm_45-Degree.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
