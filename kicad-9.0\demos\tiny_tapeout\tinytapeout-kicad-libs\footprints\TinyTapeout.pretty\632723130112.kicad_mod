(footprint "************"
	(version 20240108)
	(generator "pcbnew")
	(generator_version "8.0")
	(layer "F.Cu")
	(descr "<b>WR-COM</b><br> USB 3.1 Type C Receptacle Horizontal Mid-Mount THR / SMT 1.6 mm")
	(property "Reference" "REF**"
		(at -2.1 -5.8 0)
		(layer "F.SilkS")
		(uuid "1b78bfbd-a3c0-4938-84f6-595b5cd3cc24")
		(effects
			(font
				(size 0.64 0.64)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "************"
		(at 0 1 0)
		(layer "F.Fab")
		(uuid "ce2d2696-fd67-42b5-84e9-346b5a2f9246")
		(effects
			(font
				(size 0.64 0.64)
				(thickness 0.15)
			)
		)
	)
	(property "Footprint" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "41dd90c8-0ef2-4709-8467-eeb6a214524b")
		(effects
			(font
				(size 1.27 1.27)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "cd75ebeb-5b5c-4395-a0c9-e6c0725fa3ba")
		(effects
			(font
				(size 1.27 1.27)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "c382a87a-81ad-400b-b2d9-7ed4887b5a45")
		(effects
			(font
				(size 1.27 1.27)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -5.6 2.485)
		(end 5.6 2.485)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "763b58a4-ab0d-4716-903a-3adc457f4d04")
	)
	(fp_poly
		(pts
			(xy 3.575 -3.19) (xy 3.531 -3.188) (xy 3.487 -3.181) (xy 3.444 -3.169) (xy 3.402 -3.153) (xy 3.363 -3.133)
			(xy 3.325 -3.109) (xy 3.291 -3.081) (xy 3.259 -3.049) (xy 3.231 -3.015) (xy 3.207 -2.977) (xy 3.187 -2.938)
			(xy 3.171 -2.896) (xy 3.159 -2.853) (xy 3.152 -2.809) (xy 3.15 -2.765) (xy 3.152 -2.721) (xy 3.159 -2.677)
			(xy 3.171 -2.634) (xy 3.187 -2.592) (xy 3.207 -2.552) (xy 3.231 -2.515) (xy 3.259 -2.481) (xy 3.291 -2.449)
			(xy 3.325 -2.421) (xy 3.362 -2.397) (xy 3.402 -2.377) (xy 3.444 -2.361) (xy 3.487 -2.349) (xy 3.531 -2.342)
			(xy 3.575 -2.34) (xy 3.875 -2.34) (xy 3.919 -2.342) (xy 3.963 -2.349) (xy 4.006 -2.361) (xy 4.048 -2.377)
			(xy 4.088 -2.397) (xy 4.125 -2.421) (xy 4.159 -2.449) (xy 4.191 -2.481) (xy 4.219 -2.515) (xy 4.243 -2.552)
			(xy 4.263 -2.592) (xy 4.279 -2.634) (xy 4.291 -2.677) (xy 4.298 -2.721) (xy 4.3 -2.765) (xy 4.298 -2.809)
			(xy 4.291 -2.853) (xy 4.279 -2.896) (xy 4.263 -2.938) (xy 4.243 -2.977) (xy 4.219 -3.015) (xy 4.191 -3.049)
			(xy 4.159 -3.081) (xy 4.125 -3.109) (xy 4.088 -3.133) (xy 4.048 -3.153) (xy 4.006 -3.169) (xy 3.963 -3.181)
			(xy 3.919 -3.188) (xy 3.875 -3.19) (xy 3.575 -3.19)
		)
		(stroke
			(width 0.1)
			(type solid)
		)
		(fill solid)
		(layer "F.Mask")
		(uuid "4508f115-5df1-4972-9c37-ba2b039452cc")
	)
	(fp_line
		(start 6.4 1.2)
		(end 7.1 1.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Dwgs.User")
		(uuid "5cbfb5d7-e38e-4ef8-b6d0-409fb68c4558")
	)
	(fp_line
		(start 7.1 1.2)
		(end 7.5 1.6)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Dwgs.User")
		(uuid "eec7762d-294e-41ac-ad83-6f6ffbb1920e")
	)
	(fp_line
		(start 7.5 1.6)
		(end 7.3 1.6)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Dwgs.User")
		(uuid "f500ae4b-7dc5-4dc0-9e40-000279fb67ac")
	)
	(fp_line
		(start 7.5 1.6)
		(end 7.5 1.4)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Dwgs.User")
		(uuid "3a97457d-ba75-431c-9073-d9c07cc9a5ad")
	)
	(fp_poly
		(pts
			(xy -4 -3.975) (xy -4.002 -3.921) (xy -4.009 -3.87) (xy -4.022 -3.819) (xy -4.04 -3.77) (xy -4.063 -3.723)
			(xy -4.09 -3.678) (xy -4.123 -3.637) (xy -4.159 -3.599) (xy -4.199 -3.566) (xy -4.242 -3.536) (xy -4.289 -3.512)
			(xy -4.337 -3.492) (xy -4.388 -3.478) (xy -4.439 -3.469) (xy -4.491 -3.465) (xy -4.544 -3.467) (xy -4.595 -3.474)
			(xy -4.646 -3.487) (xy -4.695 -3.505) (xy -4.742 -3.528) (xy -4.787 -3.555) (xy -4.828 -3.588) (xy -4.866 -3.624)
			(xy -4.899 -3.664) (xy -4.929 -3.707) (xy -4.953 -3.754) (xy -4.973 -3.802) (xy -4.987 -3.853) (xy -4.996 -3.904)
			(xy -5 -3.955) (xy -5 -4.555) (xy -4.998 -4.609) (xy -4.991 -4.66) (xy -4.978 -4.711) (xy -4.96 -4.76)
			(xy -4.937 -4.807) (xy -4.91 -4.852) (xy -4.877 -4.893) (xy -4.841 -4.931) (xy -4.801 -4.964) (xy -4.758 -4.994)
			(xy -4.711 -5.018) (xy -4.663 -5.038) (xy -4.612 -5.052) (xy -4.561 -5.061) (xy -4.509 -5.065) (xy -4.456 -5.063)
			(xy -4.405 -5.056) (xy -4.354 -5.043) (xy -4.305 -5.025) (xy -4.258 -5.002) (xy -4.213 -4.975) (xy -4.172 -4.942)
			(xy -4.134 -4.906) (xy -4.101 -4.866) (xy -4.071 -4.823) (xy -4.047 -4.776) (xy -4.027 -4.728) (xy -4.013 -4.677)
			(xy -4.004 -4.626) (xy -4 -4.575) (xy -4 -3.975)
		)
		(stroke
			(width 0.01)
			(type solid)
		)
		(fill solid)
		(layer "Dwgs.User")
		(uuid "850b0f0b-97c6-4b51-b3a2-c01813f3136b")
	)
	(fp_poly
		(pts
			(xy -3.29 -3.14) (xy -3.323 -3.141) (xy -3.356 -3.145) (xy -3.39 -3.152) (xy -3.422 -3.164) (xy -3.453 -3.178)
			(xy -3.482 -3.195) (xy -3.509 -3.216) (xy -3.534 -3.239) (xy -3.556 -3.265) (xy -3.576 -3.293) (xy -3.592 -3.322)
			(xy -3.606 -3.354) (xy -3.615 -3.386) (xy -3.622 -3.42) (xy -3.625 -3.454) (xy -3.624 -3.488) (xy -3.62 -3.521)
			(xy -3.613 -3.555) (xy -3.601 -3.587) (xy -3.587 -3.618) (xy -3.57 -3.647) (xy -3.549 -3.674) (xy -3.526 -3.699)
			(xy -3.5 -3.721) (xy -3.472 -3.741) (xy -3.443 -3.757) (xy -3.411 -3.771) (xy -3.379 -3.78) (xy -3.345 -3.787)
			(xy -3.31 -3.79) (xy -2.81 -3.79) (xy -2.777 -3.789) (xy -2.744 -3.785) (xy -2.71 -3.778) (xy -2.678 -3.766)
			(xy -2.647 -3.752) (xy -2.618 -3.735) (xy -2.591 -3.714) (xy -2.566 -3.691) (xy -2.544 -3.665) (xy -2.524 -3.637)
			(xy -2.508 -3.608) (xy -2.494 -3.576) (xy -2.485 -3.544) (xy -2.478 -3.51) (xy -2.475 -3.476) (xy -2.476 -3.442)
			(xy -2.48 -3.409) (xy -2.487 -3.375) (xy -2.499 -3.343) (xy -2.513 -3.312) (xy -2.53 -3.283) (xy -2.551 -3.256)
			(xy -2.574 -3.231) (xy -2.6 -3.209) (xy -2.628 -3.189) (xy -2.657 -3.173) (xy -2.689 -3.159) (xy -2.721 -3.15)
			(xy -2.755 -3.143) (xy -2.79 -3.14) (xy -3.29 -3.14)
		)
		(stroke
			(width 0.01)
			(type solid)
		)
		(fill solid)
		(layer "Dwgs.User")
		(uuid "288ff06d-8b48-4fbb-947e-4c7aae11674b")
	)
	(fp_poly
		(pts
			(xy 2.81 -3.14) (xy 2.777 -3.141) (xy 2.744 -3.145) (xy 2.71 -3.152) (xy 2.678 -3.164) (xy 2.647 -3.178)
			(xy 2.618 -3.195) (xy 2.591 -3.216) (xy 2.566 -3.239) (xy 2.544 -3.265) (xy 2.524 -3.293) (xy 2.508 -3.322)
			(xy 2.494 -3.354) (xy 2.485 -3.386) (xy 2.478 -3.42) (xy 2.475 -3.454) (xy 2.476 -3.488) (xy 2.48 -3.521)
			(xy 2.487 -3.555) (xy 2.499 -3.587) (xy 2.513 -3.618) (xy 2.53 -3.647) (xy 2.551 -3.674) (xy 2.574 -3.699)
			(xy 2.6 -3.721) (xy 2.628 -3.741) (xy 2.657 -3.757) (xy 2.689 -3.771) (xy 2.721 -3.78) (xy 2.755 -3.787)
			(xy 2.79 -3.79) (xy 3.29 -3.79) (xy 3.323 -3.789) (xy 3.356 -3.785) (xy 3.39 -3.778) (xy 3.422 -3.766)
			(xy 3.453 -3.752) (xy 3.482 -3.735) (xy 3.509 -3.714) (xy 3.534 -3.691) (xy 3.556 -3.665) (xy 3.576 -3.637)
			(xy 3.592 -3.608) (xy 3.606 -3.576) (xy 3.615 -3.544) (xy 3.622 -3.51) (xy 3.625 -3.476) (xy 3.624 -3.442)
			(xy 3.62 -3.409) (xy 3.613 -3.375) (xy 3.601 -3.343) (xy 3.587 -3.312) (xy 3.57 -3.283) (xy 3.549 -3.256)
			(xy 3.526 -3.231) (xy 3.5 -3.209) (xy 3.472 -3.189) (xy 3.443 -3.173) (xy 3.411 -3.159) (xy 3.379 -3.15)
			(xy 3.345 -3.143) (xy 3.31 -3.14) (xy 2.81 -3.14)
		)
		(stroke
			(width 0.01)
			(type solid)
		)
		(fill solid)
		(layer "Dwgs.User")
		(uuid "cf41beff-8adb-462c-8b43-d3ac8d85367c")
	)
	(fp_poly
		(pts
			(xy 5 -3.975) (xy 4.998 -3.921) (xy 4.991 -3.87) (xy 4.978 -3.819) (xy 4.96 -3.77) (xy 4.937 -3.723)
			(xy 4.91 -3.678) (xy 4.877 -3.637) (xy 4.841 -3.599) (xy 4.801 -3.566) (xy 4.758 -3.536) (xy 4.711 -3.512)
			(xy 4.663 -3.492) (xy 4.612 -3.478) (xy 4.561 -3.469) (xy 4.509 -3.465) (xy 4.456 -3.467) (xy 4.405 -3.474)
			(xy 4.354 -3.487) (xy 4.305 -3.505) (xy 4.258 -3.528) (xy 4.213 -3.555) (xy 4.172 -3.588) (xy 4.134 -3.624)
			(xy 4.101 -3.664) (xy 4.071 -3.707) (xy 4.047 -3.754) (xy 4.027 -3.802) (xy 4.013 -3.853) (xy 4.004 -3.904)
			(xy 4 -3.955) (xy 4 -4.555) (xy 4.002 -4.609) (xy 4.009 -4.66) (xy 4.022 -4.711) (xy 4.04 -4.76)
			(xy 4.063 -4.807) (xy 4.09 -4.852) (xy 4.123 -4.893) (xy 4.159 -4.931) (xy 4.199 -4.964) (xy 4.242 -4.994)
			(xy 4.289 -5.018) (xy 4.337 -5.038) (xy 4.388 -5.052) (xy 4.439 -5.061) (xy 4.491 -5.065) (xy 4.544 -5.063)
			(xy 4.595 -5.056) (xy 4.646 -5.043) (xy 4.695 -5.025) (xy 4.742 -5.002) (xy 4.787 -4.975) (xy 4.828 -4.942)
			(xy 4.866 -4.906) (xy 4.899 -4.866) (xy 4.929 -4.823) (xy 4.953 -4.776) (xy 4.973 -4.728) (xy 4.987 -4.677)
			(xy 4.996 -4.626) (xy 5 -4.575) (xy 5 -3.975)
		)
		(stroke
			(width 0.01)
			(type solid)
		)
		(fill solid)
		(layer "Dwgs.User")
		(uuid "cd2f56ea-168a-4739-815f-e9b8c197fae1")
	)
	(fp_line
		(start -4.95 -2.215)
		(end 4.85 -2.215)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "83d22355-0705-4bf2-89a9-1e90ce3b3a22")
	)
	(fp_line
		(start -4.75 2.485)
		(end -4.75 -1.715)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "36b8b5cf-d93a-4019-a9fd-55e23d1928a6")
	)
	(fp_line
		(start 4.75 -1.715)
		(end 4.75 2.485)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "94052361-dcf5-4d4a-84f6-fd6a3b4e4261")
	)
	(fp_arc
		(start -4.75 -1.715)
		(mid -5.1 -1.865)
		(end -4.95 -2.215)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "d01efe5f-4891-4f1d-b4b6-d0bf25c465c6")
	)
	(fp_arc
		(start 4.85 -2.215)
		(mid 5.049999 -1.915001)
		(end 4.75 -1.715)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "8dc08a76-03cb-4d7b-9545-7f88f98b070e")
	)
	(fp_poly
		(pts
			(xy -5.25 -5.465) (xy -3.9 -5.465) (xy -3.9 -5.165) (xy 3.9 -5.165) (xy 3.9 -5.465) (xy 5.25 -5.465)
			(xy 5.25 0.035) (xy 6.8 0.035) (xy 6.8 2.435) (xy 4.9 2.435) (xy 4.9 5.295) (xy -4.9 5.295) (xy -4.9 2.535)
			(xy -6.8 2.535) (xy -6.8 0.035) (xy -5.25 0.035)
		)
		(stroke
			(width 0.1)
			(type solid)
		)
		(fill none)
		(layer "F.CrtYd")
		(uuid "c4649f87-2e27-49c8-b590-524535c937d3")
	)
	(fp_line
		(start -4.825 -4.995)
		(end -4.825 -2.005)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "f88a7de5-46c4-4c69-82f2-fa56d3811678")
	)
	(fp_line
		(start -4.825 -4.995)
		(end 4.825 -4.995)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "0a5613fc-68b8-4ccb-a8ad-23ba178d7613")
	)
	(fp_line
		(start -4.6 -2.015)
		(end -4.825 -2.005)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "5e3fea3a-4046-4758-812a-b17dd58bde6f")
	)
	(fp_line
		(start -4.6 2.485)
		(end -4.6 -2.005)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "a31edbae-9bae-4a89-936d-eb3932ab72b2")
	)
	(fp_line
		(start -4.6 2.485)
		(end -4.6 4.995)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "4af9a985-8f7b-48b7-b8bb-dfcc1d00742f")
	)
	(fp_line
		(start -4.6 4.995)
		(end 4.6 4.995)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "b9d6b830-1274-4553-8565-e3bdbed7e43a")
	)
	(fp_line
		(start -2.6 2.6)
		(end -2.6 2.8)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "34b2485a-8e2a-4f41-a612-f6decdfd6c45")
	)
	(fp_line
		(start -2.6 2.6)
		(end -2.4 2.6)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "75632018-63de-4507-bb78-ab47b540d579")
	)
	(fp_line
		(start -2.3 2.9)
		(end -2.6 2.6)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "7b95da5f-e09e-4d10-a10a-df462cc6b694")
	)
	(fp_line
		(start -1.8 2.9)
		(end -2.3 2.9)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "64a1e4e4-3289-41a5-911f-2948d322f4cd")
	)
	(fp_line
		(start 4.6 -2.005)
		(end 4.6 2.485)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "3ac2c5e8-47f2-4f1c-ad7c-d679f8a89ae3")
	)
	(fp_line
		(start 4.6 4.995)
		(end 4.6 2.485)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "044e8b33-6f0a-4447-a762-c355bb07b4e8")
	)
	(fp_line
		(start 4.825 -4.995)
		(end 4.825 -2.005)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "669e6773-2f05-4537-b9cb-ec0a1571c945")
	)
	(fp_line
		(start 4.825 -2.005)
		(end 4.61 -2.005)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "a7006f58-23f3-4f7d-ad17-beabd19300b6")
	)
	(fp_line
		(start 4.75 2.485)
		(end -4.75 2.485)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.1")
		(uuid "6d0312bb-07dc-4b93-93f2-d53d3d2785ff")
	)
	(fp_line
		(start -6.325 1.735)
		(end -6.325 0.735)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "5b5f4436-fc03-496e-9757-561ea4353a2c")
	)
	(fp_line
		(start -5.825 0.735)
		(end -5.825 1.735)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "4b4b9305-739f-4986-8935-eebda4e59bae")
	)
	(fp_line
		(start -4.75 -3.955)
		(end -4.75 -4.555)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "948e89eb-4889-4d19-955f-754959b2081b")
	)
	(fp_line
		(start -4.25 -4.575)
		(end -4.25 -3.975)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "e4220313-914c-4b36-929c-7a2d8ffd026b")
	)
	(fp_line
		(start -3.31 -3.665)
		(end -2.81 -3.665)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "72fc7584-279d-4f89-a26e-d596aff0bee5")
	)
	(fp_line
		(start -2.79 -3.265)
		(end -3.29 -3.265)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "15714ca9-1c07-449b-afc1-9cc828648cac")
	)
	(fp_line
		(start 4.25 -3.955)
		(end 4.25 -4.555)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "8fff8d07-ac3d-45f4-921d-efe856ccc925")
	)
	(fp_line
		(start 4.75 -4.575)
		(end 4.75 -3.975)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "6f770eb6-2a18-4e46-b005-e381ee7b605a")
	)
	(fp_line
		(start 5.825 1.735)
		(end 5.825 0.735)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "14de0b39-cc8c-4388-a68d-e017c85bd019")
	)
	(fp_line
		(start 6.325 0.735)
		(end 6.325 1.735)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "f4004eb8-d6b8-4c84-90d4-78d7ffdab401")
	)
	(fp_arc
		(start -6.325 0.735)
		(mid -6.075 0.485)
		(end -5.825 0.735)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "3abd01cf-6f20-4fb0-bb49-1fb4eb706d91")
	)
	(fp_arc
		(start -5.825 1.735)
		(mid -6.075 1.985)
		(end -6.325 1.735)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "c05e5187-777a-44da-a738-fce93ee13327")
	)
	(fp_arc
		(start -4.75 -4.555)
		(mid -4.51 -4.815)
		(end -4.25 -4.575)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "a06a8e78-6aaa-463b-8aba-f02ae9419daa")
	)
	(fp_arc
		(start -4.25 -3.975)
		(mid -4.49 -3.715)
		(end -4.75 -3.955)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "8047ac33-895b-443e-a78f-8c35945fc064")
	)
	(fp_arc
		(start -3.29 -3.265)
		(mid -3.5 -3.455)
		(end -3.31 -3.665)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "e5a6159a-f03e-4b6a-ad10-5ce9d2654a3d")
	)
	(fp_arc
		(start -2.81 -3.665)
		(mid -2.6 -3.475)
		(end -2.79 -3.265)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "971bc2f0-b362-4e56-8658-3cd1350e6c30")
	)
	(fp_arc
		(start 4.25 -4.555)
		(mid 4.49 -4.815)
		(end 4.75 -4.575)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "f5991dab-1afa-4c0e-8aa6-9222d425f698")
	)
	(fp_arc
		(start 4.75 -3.975)
		(mid 4.51 -3.715)
		(end 4.25 -3.955)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "e64ccf94-58db-4ee1-bf26-46a0b73f8af5")
	)
	(fp_arc
		(start 5.825 0.735)
		(mid 6.075 0.485)
		(end 6.325 0.735)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "36b8b24d-621a-4692-be62-e89f0d1f14d8")
	)
	(fp_arc
		(start 6.325 1.735)
		(mid 6.075 1.985)
		(end 5.825 1.735)
		(stroke
			(width 0.01)
			(type solid)
		)
		(layer "User.3")
		(uuid "9e13a1e8-06cb-4f76-8261-3e9e94eb6b33")
	)
	(fp_text user "A1"
		(at -3.3 -4.5 90)
		(layer "F.SilkS")
		(uuid "4ad9ba01-df57-4ad9-8653-0d919ce5f42d")
		(effects
			(font
				(size 0.32 0.32)
				(thickness 0.08)
			)
		)
	)
	(fp_text user "B1"
		(at 3.5 -4.1 0)
		(layer "F.SilkS")
		(uuid "ae50fa35-a678-4e82-b6fa-09af552f92bf")
		(effects
			(font
				(size 0.32 0.32)
				(thickness 0.08)
			)
		)
	)
	(fp_text user "PCB Cutout Area"
		(at 0.1 -1.5 0)
		(layer "Dwgs.User")
		(uuid "27e7c7bd-b5fd-4f58-8d31-91d494f761cc")
		(effects
			(font
				(size 0.48 0.48)
				(thickness 0.12)
			)
		)
	)
	(fp_text user "Milling Layer the hole size should  follow the Milling Layer"
		(at 7.5 1.5 90)
		(layer "Dwgs.User")
		(uuid "2954ffe5-69ac-4665-8eb9-26a29d0adad1")
		(effects
			(font
				(size 0.2 0.2)
				(thickness 0.05)
			)
		)
	)
	(fp_text user "PCB END"
		(at -1.76 3.075 0)
		(layer "Dwgs.User")
		(uuid "42cd50a1-72f8-400f-9003-9933ef66e885")
		(effects
			(font
				(size 0.32 0.32)
				(thickness 0.08)
			)
		)
	)
	(fp_text user "PCB cut  out Area"
		(at 3.44 -2.625 0)
		(layer "Dwgs.User")
		(uuid "e09bd36c-1921-4915-b864-2e6f1215233f")
		(effects
			(font
				(size 0.1 0.1)
				(thickness 0.025)
			)
		)
	)
	(fp_text user "PCB Cutout Area"
		(at 0.1 -1.5 0)
		(layer "Edge.Cuts")
		(uuid "4b5340ee-1613-41f7-90c8-8d3c9a2a5fba")
		(effects
			(font
				(size 0.48 0.48)
				(thickness 0.12)
			)
		)
	)
	(pad "" np_thru_hole circle
		(at -3.725 -2.765)
		(size 0.65 0.65)
		(drill 0.65)
		(layers "*.Cu" "*.Mask")
		(uuid "414359c7-d0e8-4040-878d-830b9c55f65f")
	)
	(pad "" np_thru_hole oval
		(at 3.725 -2.765)
		(size 1 0.7)
		(drill oval 0.95 0.65)
		(layers "F&B.Cu" "*.Mask")
		(uuid "cc1dc92d-36cc-47e9-9025-5e673e617776")
	)
	(pad "A1" smd rect
		(at -2.75 -4.625)
		(size 0.35 0.8)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(solder_mask_margin 0.102)
		(uuid "0dead2d8-1c1b-498e-bfd8-a6b34a261847")
	)
	(pad "A2" smd rect
		(at -2.25 -4.625)
		(size 0.35 0.8)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(solder_mask_margin 0.102)
		(uuid "e6b9296f-e5e0-42c4-b472-c1670ffcc583")
	)
	(pad "A3" smd rect
		(at -1.75 -4.625)
		(size 0.35 0.8)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(solder_mask_margin 0.102)
		(uuid "4807a98d-9148-4e2e-9718-a8229a71843c")
	)
	(pad "A4" smd rect
		(at -1.25 -4.625)
		(size 0.35 0.8)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(solder_mask_margin 0.102)
		(uuid "71b5070d-11d0-493e-8556-fc05edb9aa74")
	)
	(pad "A5" smd rect
		(at -0.75 -4.625)
		(size 0.35 0.8)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(solder_mask_margin 0.102)
		(uuid "995b87d7-38d7-4d8e-a80a-31830a3109d4")
	)
	(pad "A6" smd rect
		(at -0.25 -4.625)
		(size 0.35 0.8)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(solder_mask_margin 0.102)
		(uuid "cff0f0a6-156b-44fb-b0a2-9e98c59e4f69")
	)
	(pad "A7" smd rect
		(at 0.25 -4.625)
		(size 0.35 0.8)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(solder_mask_margin 0.102)
		(uuid "43455869-4d1e-4d33-8bb3-ad996b5b6033")
	)
	(pad "A8" smd rect
		(at 0.75 -4.625)
		(size 0.35 0.8)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(solder_mask_margin 0.102)
		(uuid "366b377f-291b-40eb-9d40-e2c065b869e7")
	)
	(pad "A9" smd rect
		(at 1.25 -4.625)
		(size 0.35 0.8)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(solder_mask_margin 0.102)
		(uuid "5329576b-0ea6-4c73-9ddf-ce7de0bfa657")
	)
	(pad "A10" smd rect
		(at 1.75 -4.625)
		(size 0.35 0.8)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(solder_mask_margin 0.102)
		(uuid "2723a875-bd88-46ac-8ec1-a013f0a7ae57")
	)
	(pad "A11" smd rect
		(at 2.25 -4.625)
		(size 0.35 0.8)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(solder_mask_margin 0.102)
		(uuid "89b4cccf-6014-40cc-a29a-5909ed79a0c6")
	)
	(pad "A12" smd rect
		(at 2.75 -4.625)
		(size 0.35 0.8)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(solder_mask_margin 0.102)
		(uuid "422984fd-1c31-408d-9448-aca6a3b61157")
	)
	(pad "B1" thru_hole oval
		(at 3.05 -3.465)
		(size 1.1 0.6)
		(drill oval 0.9 0.4)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.15)
		(uuid "26469737-52d6-43fc-b06a-9c87a00ba8c7")
	)
	(pad "B2" thru_hole circle
		(at 2.4 -2.765)
		(size 0.65 0.65)
		(drill 0.4)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.102)
		(uuid "c2830fb3-ac2b-4dc9-8e3e-a7361b76c2d7")
	)
	(pad "B3" thru_hole circle
		(at 1.6 -2.765)
		(size 0.65 0.65)
		(drill 0.4)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.102)
		(uuid "7b3ae8a3-25ad-4cf5-9a98-358e9a175890")
	)
	(pad "B4" thru_hole circle
		(at 1.2 -3.465)
		(size 0.65 0.65)
		(drill 0.4)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.102)
		(uuid "1e911049-a019-4427-b47b-4b90f9c72eaa")
	)
	(pad "B5" thru_hole circle
		(at 0.8 -2.765)
		(size 0.65 0.65)
		(drill 0.4)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.102)
		(uuid "b9e3934c-1a03-4bd5-9000-0bd9f2b7781b")
	)
	(pad "B6" thru_hole circle
		(at 0.4 -3.465)
		(size 0.65 0.65)
		(drill 0.4)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.102)
		(uuid "8300c39d-c386-4b85-8252-323ef47fc38e")
	)
	(pad "B7" thru_hole circle
		(at -0.4 -3.465)
		(size 0.65 0.65)
		(drill 0.4)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.102)
		(uuid "7879f6ce-24d8-43ff-a604-fb11c46a62b9")
	)
	(pad "B8" thru_hole circle
		(at -0.8 -2.765)
		(size 0.65 0.65)
		(drill 0.4)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.102)
		(uuid "ef30d539-253f-4b28-93f5-636a9c257c08")
	)
	(pad "B9" thru_hole circle
		(at -1.2 -3.465)
		(size 0.65 0.65)
		(drill 0.4)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.102)
		(uuid "37cf3de0-9060-4804-894b-900580870e1f")
	)
	(pad "B10" thru_hole circle
		(at -1.6 -2.765)
		(size 0.65 0.65)
		(drill 0.4)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.102)
		(uuid "95decc9e-96b9-4520-aca0-1645e62c2f64")
	)
	(pad "B11" thru_hole circle
		(at -2.4 -2.765)
		(size 0.65 0.65)
		(drill 0.4)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.102)
		(uuid "148af169-cf72-4b23-8042-57a36046b6a3")
	)
	(pad "B12" thru_hole oval
		(at -3.05 -3.465)
		(size 1.1 0.6)
		(drill oval 0.9 0.4)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.15)
		(uuid "3ec39637-4f52-4b89-ba5f-955d55131b16")
	)
	(pad "S1" thru_hole oval
		(at -6.075 1.235)
		(size 1 2)
		(drill oval 0.5 1.5)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.1)
		(uuid "96c5c8ee-a966-4d73-869f-2a74b907d65c")
	)
	(pad "S1" thru_hole oval
		(at -4.5 -4.265)
		(size 1 2)
		(drill oval 0.5 1.5)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.1)
		(uuid "7579bda1-bc0e-4830-996e-a810e8823197")
	)
	(pad "S2" thru_hole oval
		(at 4.5 -4.265)
		(size 1 2)
		(drill oval 0.5 1.5)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.1)
		(uuid "628a18f5-256e-4d70-9358-11b58fa4a085")
	)
	(pad "S4" thru_hole oval
		(at 6.075 1.235)
		(size 1 2)
		(drill oval 0.5 1.5)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(solder_mask_margin 0.1)
		(uuid "c8c3008d-ef8b-48b3-ac1d-9527a11d8152")
	)
	(zone
		(net 0)
		(net_name "")
		(layers "*.Cu" "*.Paste" "*.SilkS" "*.Mask" "Edge.Cuts")
		(uuid "80b65e20-1c57-4435-a831-ebcabf153fd1")
		(name "USBCUTOUT")
		(hatch edge 0.5)
		(connect_pads
			(clearance 0)
		)
		(min_thickness 0.25)
		(filled_areas_thickness no)
		(keepout
			(tracks not_allowed)
			(vias not_allowed)
			(pads not_allowed)
			(copperpour not_allowed)
			(footprints allowed)
		)
		(fill
			(thermal_gap 0.5)
			(thermal_bridge_width 0.5)
		)
		(polygon
			(pts
				(xy -4.6 -2.1) (xy 4.4 -2.1) (xy 4.4 2.8) (xy -4.6 2.8)
			)
		)
	)
	(model "${KIPRJMOD}/tinytapeout-kicad-libs/3dmodels/************.step"
		(offset
			(xyz 0 -0.5 0.3)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 90 0 180)
		)
	)
)