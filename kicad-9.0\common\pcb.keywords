#
# This program source code file is part of KiCad, a free EDA CAD application.
#
# Copyright (C) 2012 CERN.
# Copyright (C) 2019-2023 KiCad Developers, see AUTHORS.txt for contributors.
#
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License
# as published by the Free Software Foundation; either version 2
# of the License, or (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, you may find one here:
# http://www.gnu.org/licenses/old-licenses/gpl-2.0.html
# or you may search the http://www.gnu.org website for the version 2 license,
# or you may write to the Free Software Foundation, Inc.,
# 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA
#

# These are the keywords for the Pcbnew s-expression file format.

add_net
addsublayer
aligned
allowed
allow_missing_courtyard
allow_soldermask_bridges
allow_soldermask_bridges_in_footprints
allow_two_segments
anchor
angle
arc
arc_segments
area
arrow1a
arrow1b
arrow2a
arrow2b
arrow_direction
arrow_length
at
attr
autoplace_cost90
autoplace_cost180
aux_axis_origin
back
best_length_ratio
best_width_ratio
bevelled
blind
blind_buried_vias_allowed
board_only
bold
border
bottom
bottom_left
bottom_right
castellated_pads
cells
center
chamfer
chamfer_ratio
circle
class
clearance
clearance_min
color
cols
column_count
column_widths
comment
company
component_class
component_classes
connect
connect_pads
copperpour
copper_finish
crossbar
curve_points
curved_edges
custom
outline
convexhull
copper_line_width
copper_text_dims
courtyard_line_width
creepage
data
date
defaults
descr
die_length
dielectric_constraints
dimension
diff_pair_width
diff_pair_gap
dimension_precision
dimension_units
dnp
drawings
drill
edge
edge_clearance
edge_cuts_line_width
edge_connector
edge_plating
edge_width
effects
embedded_fonts
embedded_files
enabled
end
epsilon_r
exclude_from_pos_files
exclude_from_bom
extension_height
extension_offset
external
fab_layers_line_width
fab_layers_text_dims
face
false
feature1
feature2
fill
fill_segments
filled_polygon
filled_areas_thickness
fillet
filter_ratio
font
format
footprint
footprints
fp_arc
fp_circle
fp_curve
fp_line
fp_poly
fp_rect
fp_text
fp_text_box
free
front
front_inner_back
full
general
generator
generator_version
generated
grid_origin
group
gr_arc
gr_bbox
gr_circle
gr_curve
gr_line
gr_poly
gr_rect
gr_text
gr_text_box
gr_vector
hatch
hatch_thickness
hatch_gap
hatch_orientation
hatch_smoothing_level
hatch_smoothing_value
hatch_border_algorithm
hatch_min_hole_area
header
height
hide
hole_to_hole_min
host
href
id
image
inward
island
island_removal_mode
island_area_min
italic
justify
keepout
keep_end_layers
keep_text_aligned
keep_upright
kicad_pcb
knockout
last_trace_width
layer
layers
leader
leader_length
left
legacy_teardrops
linear
line_spacing
links
locked
loss_tangent
margins
max_error
max_length
max_width
material
members
micro
mid
min_thickness
mirror
mod_edge_width
mod_text_size
mod_text_width
mode
model
module
name
net
net_class
net_name
net_tie_pad_groups
nets
no
no_connects
none
not_allowed
np_thru_hole
offset
opacity
options
orientation
orthogonal
other_layers_line_width
other_layers_text_dims
outward
oval
override_value
pad
pads
pad_drill
pad_size
pad_to_mask_clearance
pad_to_paste_clearance
pad_to_paste_clearance_ratio
pad_prop_bga
pad_prop_fiducial_loc
pad_prop_fiducial_glob
pad_prop_castellated
pad_prop_testpoint
pad_prop_heatsink
pad_prop_mechanical
padstack
padvia
prefer_zone_connections
private_layers
property
page
paper
path
pcb_text_size
pcb_text_width
pcbplotparams
pinfunction
pintype
placed
placement
plus
polygon
portrait
precision
prefix
primitives
priority
pts
radial
radius
rev
rect
rect_delta
reference
remove_unused_layers
render_cache
right
rotate
roundrect
roundrect_rratio
rows
row_heights
scale
segment
segment_width
separators
setup
shape
sheetfile
sheetname
silk_line_width
silk_text_dims
size
smd
smoothing
solder_mask_margin
solder_mask_min_width
solder_paste_margin
solder_paste_margin_ratio
solder_paste_ratio
solid
span
stackup
start
status
stroke
style
suffix
suppress_zeroes
table
table_cell
tags
target
title
title_block
teardrop
teardrops
tedit
tenting
text_frame
text_position_mode
thermal_width
thermal_gap
thermal_bridge_angle
thermal_bridge_width
thickness
through_hole
through_hole_min
top
top_left
top_right
trace_width
tracks
track_end
trace_min
trace_clearance
trapezoid
thru
thru_hole
thru_hole_only
true
tstamp
type
units
units_format
unlocked
user
user_diff_pair
user_trace_width
user_via
uuid
uvia_dia
uvia_drill
uvia_min_drill
uvia_min_size
uvia_size
uvias_allowed
value
version
via
vias
via_dia
via_drill
via_min_annulus
via_min_drill
via_min_size
via_size
virtual
visible_elements
width
x
xy
xyz
yes
zone
zone_45_only
zone_clearance
zone_connect
zone_layer_connections
zone_type
zones
