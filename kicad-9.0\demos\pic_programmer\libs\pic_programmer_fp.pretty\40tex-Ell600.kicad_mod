(footprint "40tex-Ell600"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "Support TEXTOOL Dil 40 pins, pads elliptiques, e=600 mils")
	(tags "DEV")
	(property "Reference" "REF**"
		(at -6.66 -3.6 180)
		(layer "F.SilkS")
		(uuid "118c6395-eff8-4e75-9bf5-c332daeae77a")
		(effects
			(font
				(size 2.032 1.27)
				(thickness 0.3048)
			)
		)
	)
	(property "Value" "SUPP40"
		(at -9.16 2.5 180)
		(layer "F.SilkS")
		(uuid "3116afc7-4f22-4acd-84ea-a3fd61e8a534")
		(effects
			(font
				(size 2.032 1.27)
				(thickness 0.3048)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "7415c545-5c49-4b02-bf9f-138450e554ae")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "551bbd49-d84f-4049-a10c-7949810f3ffb")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -7.62 43.18)
		(end -3.81 43.18)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "8abfc9d4-2f7c-42d3-af9d-5733d902a9b5")
	)
	(fp_line
		(start -5.08 -22.86)
		(end -5.08 -16.51)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "10d63f33-8eea-4113-8d27-f2f4bacabab3")
	)
	(fp_line
		(start -3.835 -8.065)
		(end -2.565 -9.335)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "3a3f4cfe-7626-4a71-bac3-8494c49cddf0")
	)
	(fp_line
		(start -3.81 -14.605)
		(end -3.835 -8.065)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "60958efc-c589-4d37-9b60-d8bcc45f3d22")
	)
	(fp_line
		(start -3.81 48.26)
		(end -7.62 48.26)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "dc965dca-40cf-48db-9822-3c92913a6bce")
	)
	(fp_line
		(start -3.81 54.61)
		(end -3.835 -8.065)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "fd16a938-907b-4dc1-a7ad-199e6e737202")
	)
	(fp_line
		(start -2.56 -9.3)
		(end 17.76 -9.3)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "8505911c-**************-67fa75c6937b")
	)
	(fp_line
		(start -2.54 -14.605)
		(end -2.54 -5.08)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "7b86273c-9ecf-4462-8473-d284ac63b0a7")
	)
	(fp_line
		(start -2.54 -5.08)
		(end -3.81 -5.08)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "1825e6dc-d3ef-4985-8532-663364e58888")
	)
	(fp_line
		(start -2.54 55.88)
		(end -3.81 54.61)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "83563ccb-8cf8-436b-96b6-d3ac12c84bc0")
	)
	(fp_line
		(start -1.27 -16.51)
		(end -1.27 -22.86)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "03a24f3e-0a41-49f4-aa6e-e46a685b165a")
	)
	(fp_line
		(start 2.54 -5.08)
		(end 12.7 -5.08)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "b783f623-5885-4e26-8880-b8eb64cadab8")
	)
	(fp_line
		(start 2.54 50.8)
		(end 2.54 -5.08)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "a4349706-4c5e-40ae-a3fd-0806568418a2")
	)
	(fp_line
		(start 12.7 -5.08)
		(end 12.7 50.8)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "94f1b7a1-66dc-4daf-8093-dfb6ff4b349b")
	)
	(fp_line
		(start 12.7 50.8)
		(end 2.54 50.8)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "02209d91-cb36-45c3-ab6e-5c1d3f7f63b3")
	)
	(fp_line
		(start 17.765 -9.335)
		(end 19.035 -8.065)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "baa686c7-1633-4964-834a-f1ef9a198b7d")
	)
	(fp_line
		(start 17.78 55.88)
		(end -2.54 55.88)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "3beb6548-12dd-4131-bd0f-0a040ee3307d")
	)
	(fp_line
		(start 19.035 -8.065)
		(end 19.05 54.61)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "3421091d-201e-4858-b414-b86968f8316e")
	)
	(fp_line
		(start 19.05 0)
		(end 22.86 0)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "348bbad4-5dff-4550-9d50-8c2b97e371df")
	)
	(fp_line
		(start 19.05 54.61)
		(end 17.78 55.88)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "cff4b903-a109-4bf3-b2bb-2b6f6ac45d6f")
	)
	(fp_line
		(start 22.86 -5.08)
		(end 19.05 -5.08)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "09115d8d-17bb-4120-8657-9c6c848fa222")
	)
	(fp_arc
		(start -7.618665 48.260665)
		(mid -9.189804 45.720746)
		(end -7.619999 43.180001)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f50d196a-08ae-4f9b-bbad-5369c28e5c3b")
	)
	(fp_arc
		(start -5.080051 -22.86)
		(mid -3.168 -24.765038)
		(end -1.27 -22.846)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "d18152fd-5241-426b-981e-5077faa21115")
	)
	(fp_arc
		(start -1.27 -16.51)
		(mid -3.175 -14.605)
		(end -5.08 -16.51)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "48465406-e146-4018-b376-46c5aba10882")
	)
	(fp_arc
		(start 22.858665 -5.080665)
		(mid 24.429804 -2.540746)
		(end 22.859999 -0.000001)
		(stroke
			(width 0.381)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "3415b44f-1298-431b-a851-e6bd3c1cf1e6")
	)
	(fp_line
		(start -9.38 42.63)
		(end -4.38 42.63)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "d102e751-5969-4187-ab63-6ef9fb9fa64f")
	)
	(fp_line
		(start -9.38 48.63)
		(end -9.38 42.63)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "60b6fd79-dff6-4b4f-b522-f798f67c159e")
	)
	(fp_line
		(start -5.88 -25.87)
		(end -0.38 -25.87)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "21e7afeb-c193-484d-a45d-32774d133d46")
	)
	(fp_line
		(start -5.88 -10.87)
		(end -5.88 -25.87)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "4a20b41b-6ee2-42d8-9f8d-7ee885bd6893")
	)
	(fp_line
		(start -4.38 -10.87)
		(end -5.88 -10.87)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "9ad2a9a1-1575-4624-9bfa-6f75c06c54a2")
	)
	(fp_line
		(start -4.38 42.63)
		(end -4.38 -10.87)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "da1b87a4-66de-46e9-83cc-75220c384a5d")
	)
	(fp_line
		(start -4.38 48.63)
		(end -9.38 48.63)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "ea2c8f13-b39c-4104-aa90-0200d5e58699")
	)
	(fp_line
		(start -4.38 56.63)
		(end -4.38 48.63)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "a9a0b11a-d7cd-47c6-929e-e03ec556f89e")
	)
	(fp_line
		(start -0.38 -25.87)
		(end -0.38 -10.87)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "70aefd4f-e5cd-4e1b-9650-6a483f4adb3d")
	)
	(fp_line
		(start -0.38 -10.87)
		(end 19.62 -10.87)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "3c7c247e-cd64-4cf8-a790-c2583ae62d0c")
	)
	(fp_line
		(start 19.62 -10.87)
		(end 19.62 -5.87)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "00b2498b-881b-450e-9795-430ee0384b5b")
	)
	(fp_line
		(start 19.62 -5.87)
		(end 24.62 -5.87)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "03001d26-5b23-445c-a670-ce20b1598ea4")
	)
	(fp_line
		(start 19.62 0.63)
		(end 19.62 56.63)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "44ade40e-**************-d4d9c071e6cf")
	)
	(fp_line
		(start 19.62 56.63)
		(end -4.38 56.63)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "ddac9f69-3f65-405b-a3d0-32d0ee05736f")
	)
	(fp_line
		(start 24.62 -5.87)
		(end 24.62 0.63)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "21338557-b20f-45c9-983a-6e27328a39a1")
	)
	(fp_line
		(start 24.62 0.63)
		(end 19.62 0.63)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "0f92b2a0-ae23-4183-8b9d-6af47dc96a70")
	)
	(pad "" thru_hole circle
		(at -6.35 45.72 270)
		(size 2.8 2.8)
		(drill 2)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "695e6ce8-5629-4446-ab39-35b05b32dbd2")
	)
	(pad "" thru_hole circle
		(at 21.59 -2.54 270)
		(size 2.8 2.8)
		(drill 2)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "4e83dc2d-6a0e-4c53-8773-bbfe4b8f2751")
	)
	(pad "1" thru_hole oval
		(at 0 0 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "dd7e817a-a6f5-48a2-9f72-584d62c15403")
	)
	(pad "2" thru_hole oval
		(at 0 2.54 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6575f5c1-7b80-4b24-9224-4ff5593f27dc")
	)
	(pad "3" thru_hole oval
		(at 0 5.08 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "25a13d9b-f3b3-404d-97b7-ed0fc7958f80")
	)
	(pad "4" thru_hole oval
		(at 0 7.62 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "2e88f92e-1448-4d45-bb63-22769dedc3c0")
	)
	(pad "5" thru_hole oval
		(at 0 10.16 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c93e0685-6641-4ecb-8922-27fbf2064781")
	)
	(pad "6" thru_hole oval
		(at 0 12.7 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "23857ffe-f20a-49b6-92a6-ef6a9b570429")
	)
	(pad "7" thru_hole oval
		(at 0 15.24 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "4e16ea51-ded9-4e02-818e-e539f2c3311c")
	)
	(pad "8" thru_hole oval
		(at 0 17.78 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "923d5833-8a99-4d4b-a28d-43d3b2e819d2")
	)
	(pad "9" thru_hole oval
		(at 0 20.32 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "8eb7a2b2-286d-4188-b30b-e9901a17bda7")
	)
	(pad "10" thru_hole oval
		(at 0 22.86 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "fc27c209-b2cf-4584-ac1c-07fed142d059")
	)
	(pad "11" thru_hole oval
		(at 0 25.4 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "3661b879-011f-484c-aef8-a2c34483da76")
	)
	(pad "12" thru_hole oval
		(at 0 27.94 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "b1c65c4e-1804-473a-a2ce-5506bd1e9738")
	)
	(pad "13" thru_hole oval
		(at 0 30.48 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "ab8d0ff4-3b58-4125-8d12-7bb9ec653777")
	)
	(pad "14" thru_hole oval
		(at 0 33.02 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "ea40f82a-5663-4607-afcb-b5b6dbe5edf9")
	)
	(pad "15" thru_hole oval
		(at 0 35.56 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c9e13a7f-6772-4ddc-8415-13af36b3027f")
	)
	(pad "16" thru_hole oval
		(at 0 38.1 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "52519590-b804-435b-906c-c7fcb309c1d3")
	)
	(pad "17" thru_hole oval
		(at 0 40.64 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "b08db792-1f01-4999-8d35-636e220c7cc7")
	)
	(pad "18" thru_hole oval
		(at 0 43.18 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "1e3c7ef0-7b9f-4569-9954-047aa71638ab")
	)
	(pad "19" thru_hole oval
		(at 0 45.72 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "68ecfa80-285a-4f17-9000-99535ad32b40")
	)
	(pad "20" thru_hole oval
		(at 0 48.26 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "d35e61f7-d1ee-43a5-a567-8c8ac93d195f")
	)
	(pad "21" thru_hole oval
		(at 15.24 48.26 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "9bb51201-8d6d-4f85-9b85-3e396b263be5")
	)
	(pad "22" thru_hole oval
		(at 15.24 45.72 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "d2d300c2-a91a-4cbc-b9ca-7f5fbca85add")
	)
	(pad "23" thru_hole oval
		(at 15.24 43.18 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "d01dfead-79d3-4939-ac4e-5173bacc1eec")
	)
	(pad "24" thru_hole oval
		(at 15.24 40.64 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "54d7559f-80dd-4e84-aa62-8d6acc0d2faa")
	)
	(pad "25" thru_hole oval
		(at 15.24 38.1 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7b8d78c9-346e-490c-ae20-7ab1e0ee4b6e")
	)
	(pad "26" thru_hole oval
		(at 15.24 35.56 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "d7c00000-4503-44cd-8e92-bc1de09e897c")
	)
	(pad "27" thru_hole oval
		(at 15.24 33.02 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "367d0427-28f3-4b66-9f07-574cd048c497")
	)
	(pad "28" thru_hole oval
		(at 15.24 30.48 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "954528d1-71ec-4b38-be88-86280b065904")
	)
	(pad "29" thru_hole oval
		(at 15.24 27.94 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "d474c7f1-d613-4640-bc32-b4dab317d009")
	)
	(pad "30" thru_hole oval
		(at 15.24 25.4 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "61e69516-b9ac-4b04-9810-15f098157acf")
	)
	(pad "31" thru_hole oval
		(at 15.24 22.86 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "8af30392-e77c-46e7-b472-4d70c2dd9ef8")
	)
	(pad "32" thru_hole oval
		(at 15.24 20.32 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "ac0ace97-61e9-4c2c-bd34-01065eaf6872")
	)
	(pad "33" thru_hole oval
		(at 15.24 17.78 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "8c3484ad-757a-45f9-a507-5838c32619f6")
	)
	(pad "34" thru_hole oval
		(at 15.24 15.24 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "2bf2cc29-46aa-4d92-9205-2118bcde12e9")
	)
	(pad "35" thru_hole oval
		(at 15.24 12.7 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "a7e4b289-afc9-496c-86e1-19cef790495a")
	)
	(pad "36" thru_hole oval
		(at 15.24 10.16 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0b709237-cc56-4695-8f93-6af37f713426")
	)
	(pad "37" thru_hole oval
		(at 15.24 7.62 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "efc59b9f-14bd-46ca-8bdb-12f46b0c24e8")
	)
	(pad "38" thru_hole oval
		(at 15.24 5.08 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "4e6cdea0-ce20-4bf1-9749-4c64e8f1e299")
	)
	(pad "39" thru_hole oval
		(at 15.24 2.54 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "9196f10f-d331-4803-8d80-7c07d7e68736")
	)
	(pad "40" thru_hole rect
		(at 15.24 0 270)
		(size 1.6 2.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "65738889-e7c3-40db-915f-f0b50c7e1f1b")
	)
	(embedded_fonts no)
	(model "${KIPRJMOD}/libs/3d_shapes/textool_40.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 90)
		)
	)
)
