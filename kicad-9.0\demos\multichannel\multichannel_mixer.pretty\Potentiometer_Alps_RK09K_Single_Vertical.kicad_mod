(footprint "Potentiometer_Alps_RK09K_Single_Vertical"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "113004U 1130A6S 11300DR 1130A8G 1130081 1130A5R 1130AP5 1130AST  D1130C3W D1130C1B D1130C3C D1130C2P Potentiometer, vertical, Alps RK09K Single, https://tech.alpsalpine.com/prod/e/pdf/potentiometer/rotarypotentiometers/rk09k/rk09k.pdf")
	(tags "Potentiometer vertical Alps RK09K RK09D Single Snapin")
	(property "Reference" "REF**"
		(at 11.85 -5.3 270)
		(layer "F.SilkS")
		(uuid "b2d35f08-d533-491b-96c6-08276a14e4fe")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "Volume"
		(at 10.35 -5.5 270)
		(layer "F.SilkS")
		(uuid "22b500e7-c72e-40c9-a056-6927f1063e58")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "b4013823-4142-4935-87db-c4d123af4950")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" "Potentiometer"
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "26353319-4d0b-4c3c-a404-dca2ccf1fe51")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start 0.88 -2.521)
		(end 0.88 -1.2)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "9985f8a3-073f-4e62-a4b3-3d3b8dfeb963")
	)
	(fp_line
		(start 0.88 -2.521)
		(end 4.817 -2.521)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "884b37bc-cb04-496e-b503-a96645f375d4")
	)
	(fp_line
		(start 0.88 1.25)
		(end 0.88 1.63)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "10b0e179-4e35-4cd0-b3f1-c3617589e622")
	)
	(fp_line
		(start 0.88 3.371)
		(end 0.88 4.13)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "8e1274d9-8fa9-499c-a0a5-a9a7590c9681")
	)
	(fp_line
		(start 0.88 5.87)
		(end 0.88 7.52)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "1f08719a-d479-41b5-b41f-9038d139e9ef")
	)
	(fp_line
		(start 0.88 7.52)
		(end 4.817 7.52)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "b724a134-2ee2-4492-9b26-8c25536b2375")
	)
	(fp_line
		(start 9.184 -2.521)
		(end 13.12 -2.521)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "608e7052-ba06-41d2-84e4-b23c6ff4a538")
	)
	(fp_line
		(start 9.184 7.52)
		(end 13.12 7.52)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f6e08e22-c4bc-4a53-b7b7-8cea44b2c569")
	)
	(fp_line
		(start 13.12 -2.521)
		(end 13.12 7.52)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "2590c7b5-0f93-4d97-9ad5-4c90f18a2554")
	)
	(fp_line
		(start -1.15 -4.15)
		(end -1.15 9.15)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "7f9364bf-cb80-4b94-aba5-bb1018b85d44")
	)
	(fp_line
		(start -1.15 9.15)
		(end 13.25 9.15)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "4d9fe13c-**************-3e39a53957a6")
	)
	(fp_line
		(start 13.25 -4.15)
		(end -1.15 -4.15)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "c6a78825-b807-4927-91db-6ec3dcbba386")
	)
	(fp_line
		(start 13.25 9.15)
		(end 13.25 -4.15)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "63e520fb-e43f-4ef6-b9e5-62759798ebc0")
	)
	(fp_line
		(start 1 -2.4)
		(end 1 7.4)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "bd4d7e8c-0701-4701-939a-497a157e8cfc")
	)
	(fp_line
		(start 1 7.4)
		(end 13 7.4)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "0233855f-f1dd-43ef-8962-8a3ddf947bf0")
	)
	(fp_line
		(start 13 -2.4)
		(end 1 -2.4)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "b1b3556d-c95b-4758-812b-6badfc7d86ea")
	)
	(fp_line
		(start 13 7.4)
		(end 13 -2.4)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "638eaac7-613a-4b5c-be9f-9e8546d73a15")
	)
	(fp_circle
		(center 7.5 2.5)
		(end 10.5 2.5)
		(stroke
			(width 0.1)
			(type solid)
		)
		(fill no)
		(layer "F.Fab")
		(uuid "0c60a448-1ae5-4522-8c99-297ff5259175")
	)
	(fp_text user "${REFERENCE}"
		(at 2 2.5 90)
		(layer "F.Fab")
		(uuid "61490fda-96a8-4f4b-8a19-a9557d7bd234")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 1.8 1.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "e890ef5c-f9e2-46d0-ab58-************")
	)
	(pad "2" thru_hole circle
		(at 0 2.5)
		(size 1.8 1.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "278d55b7-34b2-4366-8108-ebd7f02f4819")
	)
	(pad "3" thru_hole circle
		(at 0 5)
		(size 1.8 1.8)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6a63a13b-590e-43a5-8879-69f9d1fcce0b")
	)
	(pad "MP" thru_hole roundrect
		(at 7 -1.9)
		(size 4 3)
		(drill oval 2.1 1.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(roundrect_rratio 0.25)
		(uuid "37920a51-b8c3-4de1-9149-318945f256b2")
	)
	(pad "MP" thru_hole roundrect
		(at 7 6.9)
		(size 4 3)
		(drill oval 2.1 1.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(roundrect_rratio 0.25)
		(uuid "96e4a08a-c3db-4b00-b28a-10c499386857")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/Potentiometer_THT.3dshapes/Potentiometer_Alps_RK09K_Single_Vertical.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
