(footprint "TT04_BREAKOUT_SMB" (version 20221018) (generator pcbnew)
  (layer "F.Cu")
  (descr "Ailser PCB carrier, connectors on bottom, mounted as stack (all facing same direction)")
  (attr smd)
  (fp_text reference "REF**" (at -18 -26.46) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
    (tstamp f57724fd-fe6b-43f9-9a0b-910bcf777836)
  )
  (fp_text value "TT04_BREAKOUT_SMB" (at -18 26.46) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 56ee6dd9-6735-4cc3-93e9-71b03caabcc8)
  )
  (fp_text user "${REFERENCE}" (at -18 0 90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 53a21bb8-9cfa-4c22-b9f0-f31b80991f00)
  )
  (fp_text user "${REFERENCE}" (at 18.2 0 90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 8e7e1524-c73e-4c45-b0ad-b0ad71b5b7d3)
  )
  (fp_line (start -22.04 -24.89) (end -20.6 -24.89)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 9dd88b5f-972a-42c9-8dcb-049de6daaf09))
  (fp_line (start -20.6 -25.46) (end -20.6 -24.89)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0cc960de-17d1-42af-a2f7-bd2188a9db5b))
  (fp_line (start -20.6 -25.46) (end -15.4 -25.46)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c22ead89-ce70-4a2f-9de5-f3d77abe708d))
  (fp_line (start -20.6 -23.37) (end -20.6 -22.35)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 9d42d33f-2251-407a-acef-574b46b2cb57))
  (fp_line (start -20.6 -20.83) (end -20.6 -19.81)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 3a09add9-d46f-4a75-bbd1-96b1c4018018))
  (fp_line (start -20.6 -18.29) (end -20.6 -17.27)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f3c8cf08-9789-45c8-bf72-ad93a06b83de))
  (fp_line (start -20.6 -15.75) (end -20.6 -14.73)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2d4d8df3-3dfa-4e9a-a140-09dc23a3e890))
  (fp_line (start -20.6 -13.21) (end -20.6 -12.19)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 82be782f-8a3a-4f9d-bc37-1a1ed0a4c1bf))
  (fp_line (start -20.6 -10.67) (end -20.6 -9.65)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ae385ff5-5e67-431d-9251-dd798c667ad1))
  (fp_line (start -20.6 -8.13) (end -20.6 -7.11)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp fae0478b-e31a-4529-b31d-448f72528d1f))
  (fp_line (start -20.6 -5.59) (end -20.6 -4.57)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e893178c-47b7-4c22-ae3a-1668aaaddd99))
  (fp_line (start -20.6 -3.05) (end -20.6 -2.03)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 45e2f8e6-2465-4efd-8821-8c3d26c256a0))
  (fp_line (start -20.6 -0.51) (end -20.6 0.51)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c325e92b-b926-4b20-80b7-99f1a5d88326))
  (fp_line (start -20.6 2.03) (end -20.6 3.05)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 68f2eaee-3432-448f-96ea-4d8aecd275b8))
  (fp_line (start -20.6 4.57) (end -20.6 5.59)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 058e0828-1b1f-4062-be26-a33209b3e58c))
  (fp_line (start -20.6 7.11) (end -20.6 8.13)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 620ac338-0b34-4344-9716-0a3c2d5e742c))
  (fp_line (start -20.6 9.65) (end -20.6 10.67)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp cd6fba46-c2e6-4b0d-a1ba-ddab1cf65f99))
  (fp_line (start -20.6 12.19) (end -20.6 13.21)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 8aa404b2-a548-4043-9d83-6aa059e899bf))
  (fp_line (start -20.6 14.73) (end -20.6 15.75)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp acb31fbd-9cac-4ace-9e2c-db89fe170593))
  (fp_line (start -20.6 17.27) (end -20.6 18.29)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 567f7227-f542-45ca-a46c-cc0dc4bbce13))
  (fp_line (start -20.6 19.81) (end -20.6 20.83)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 209a61bf-20f3-4c52-96d7-12956ffaeb35))
  (fp_line (start -20.6 22.35) (end -20.6 23.37)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e9cafafc-3420-4577-9fe5-ac403d29fd3a))
  (fp_line (start -20.6 24.89) (end -20.6 25.46)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f175bc3a-292e-4ccf-9065-efc7d84ecd3e))
  (fp_line (start -20.6 25.46) (end -15.4 25.46)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp d4453462-f83c-410f-a636-8241814980fe))
  (fp_line (start -15.4 -25.46) (end -15.4 -24.89)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp fe90ea31-86fa-45f7-91cf-90337abcad19))
  (fp_line (start -15.4 -23.37) (end -15.4 -22.35)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b3bd1ae8-d6db-4da3-a43c-5b40b36e7f9a))
  (fp_line (start -15.4 -20.83) (end -15.4 -19.81)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 98be24d7-f2ec-493d-8e6f-9a35d7910066))
  (fp_line (start -15.4 -18.29) (end -15.4 -17.27)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b2900622-ebf5-4cc1-a75b-79e9c793f11d))
  (fp_line (start -15.4 -15.75) (end -15.4 -14.73)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 050c3426-c0cc-4290-8c8b-47f761a3d60f))
  (fp_line (start -15.4 -13.21) (end -15.4 -12.19)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 687dcdec-7ff0-4779-b1cb-9cfbc2b0d057))
  (fp_line (start -15.4 -10.67) (end -15.4 -9.65)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 29a63686-762d-4007-b8d8-0a69aae870a1))
  (fp_line (start -15.4 -8.13) (end -15.4 -7.11)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 1a5f9d26-e7ae-4a20-9500-7c5e686f6947))
  (fp_line (start -15.4 -5.59) (end -15.4 -4.57)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ef70385e-1dd4-4b0d-b0bc-0e6a49144c26))
  (fp_line (start -15.4 -3.05) (end -15.4 -2.03)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 9d571947-bf60-49f4-9f14-6187c1d00bc3))
  (fp_line (start -15.4 -0.51) (end -15.4 0.51)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b9db831a-08e2-4053-9501-7fa2e468b86f))
  (fp_line (start -15.4 2.03) (end -15.4 3.05)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 97212210-57a0-48fd-9088-c31984b7a4b6))
  (fp_line (start -15.4 4.57) (end -15.4 5.59)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 4d210cba-5e75-42d3-ad97-4b551e3d4058))
  (fp_line (start -15.4 7.11) (end -15.4 8.13)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 6dfddeac-ce8d-4717-8fa5-d1bfc76d872a))
  (fp_line (start -15.4 9.65) (end -15.4 10.67)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 6b1ea62a-ecae-4c23-be98-1ee64011ce52))
  (fp_line (start -15.4 12.19) (end -15.4 13.21)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e88f9126-78ea-4f2f-9566-1fc2a5545507))
  (fp_line (start -15.4 14.73) (end -15.4 15.75)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 1d337551-d7fd-4e1d-9684-0280e3316f9a))
  (fp_line (start -15.4 17.27) (end -15.4 18.29)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 13910183-7ddb-4fa3-ab30-b05f00a10369))
  (fp_line (start -15.4 19.81) (end -15.4 20.83)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0e0c71b1-a6fb-493e-9c1f-9a41a379a54b))
  (fp_line (start -15.4 22.35) (end -15.4 23.37)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 11e8b46f-2a0b-41d1-80ea-8116580c860c))
  (fp_line (start -15.4 24.89) (end -15.4 25.46)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 41477842-bcc6-43ff-b154-cc9359e78fb6))
  (fp_line (start 15.4 -25.46) (end 15.4 -24.89)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 85e0d1be-df9a-4230-a637-e46b44fc6d06))
  (fp_line (start 15.4 -25.46) (end 20.6 -25.46)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ae053a85-0624-4386-99cb-8627417431e3))
  (fp_line (start 15.4 -23.37) (end 15.4 -22.35)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 644f1403-7424-475e-b079-ef00f002201d))
  (fp_line (start 15.4 -20.83) (end 15.4 -19.81)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp aa011771-c096-4014-b881-9b5a22cb99b1))
  (fp_line (start 15.4 -18.29) (end 15.4 -17.27)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 3136039b-d072-4711-bb70-682991d057e5))
  (fp_line (start 15.4 -15.75) (end 15.4 -14.73)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 3e9e6ea6-c376-40aa-ba96-5c0932f6ab48))
  (fp_line (start 15.4 -13.21) (end 15.4 -12.19)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a1d88784-985d-4d39-b059-cf22202b19ec))
  (fp_line (start 15.4 -10.67) (end 15.4 -9.65)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 43f2cabb-30ad-4799-802e-2372f8702822))
  (fp_line (start 15.4 -8.13) (end 15.4 -7.11)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e2df45e1-8021-4aef-92f4-ac49c8f0af7d))
  (fp_line (start 15.4 -5.59) (end 15.4 -4.57)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 1645d675-c467-45f3-b92d-c8784076fdac))
  (fp_line (start 15.4 -3.05) (end 15.4 -2.03)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 857becc5-8f3b-4d15-87d9-bfe88a6b2dca))
  (fp_line (start 15.4 -0.51) (end 15.4 0.51)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e961f515-6b6b-4345-870a-adfc2245f0ce))
  (fp_line (start 15.4 2.03) (end 15.4 3.05)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2a02e1ad-1a05-463d-b93f-72bbdf960756))
  (fp_line (start 15.4 4.57) (end 15.4 5.59)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 06ecb778-a89a-481c-9b1e-2add9ae73dc3))
  (fp_line (start 15.4 7.11) (end 15.4 8.13)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2e34802b-546f-4bdf-8abe-a853b18ef085))
  (fp_line (start 15.4 9.65) (end 15.4 10.67)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2bb4b52c-e0c0-4dea-9a56-341655752453))
  (fp_line (start 15.4 12.19) (end 15.4 13.21)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f6070a16-5d71-4885-9cc2-b5d8faa0bc66))
  (fp_line (start 15.4 14.73) (end 15.4 15.75)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 1f1bbce3-f5e0-4591-8689-8614c02368ab))
  (fp_line (start 15.4 17.27) (end 15.4 18.29)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp d508e779-665f-4d6f-a143-5f625ba235a5))
  (fp_line (start 15.4 19.81) (end 15.4 20.83)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 82d56b04-9ac7-4bc6-ae42-83270f74189b))
  (fp_line (start 15.4 22.35) (end 15.4 23.37)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 53320dce-b6e9-4c65-92d8-bc41989f6973))
  (fp_line (start 15.4 24.89) (end 15.4 25.46)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f7407836-14fa-4326-a7e8-ed19e266feac))
  (fp_line (start 15.4 25.46) (end 20.6 25.46)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 7583479f-b88f-4b00-9952-238dbef22083))
  (fp_line (start 20.6 -25.46) (end 20.6 -24.89)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 98bd50d1-c10b-4122-aa28-515638f105f0))
  (fp_line (start 20.6 -23.37) (end 20.6 -22.35)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c38d7114-8fd8-48a4-bf77-668e3b942a24))
  (fp_line (start 20.6 -20.83) (end 20.6 -19.81)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp cb2dddb4-c35a-4171-ba7f-d57ce0975d13))
  (fp_line (start 20.6 -18.29) (end 20.6 -17.27)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 4244b0d2-07ea-47fe-bd59-07abeb078c93))
  (fp_line (start 20.6 -15.75) (end 20.6 -14.73)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 3492c83d-6d17-4f1c-8d33-8ea9156cba07))
  (fp_line (start 20.6 -13.21) (end 20.6 -12.19)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 3488aaba-ec85-4b20-8cd9-ae65e40c043d))
  (fp_line (start 20.6 -10.67) (end 20.6 -9.65)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 63114dc8-ca7d-46bf-babd-9fdb7e8f5331))
  (fp_line (start 20.6 -8.13) (end 20.6 -7.11)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 76a159db-4738-4b4f-9b90-34388a658613))
  (fp_line (start 20.6 -5.59) (end 20.6 -4.57)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 41c7d220-3d8c-4a5d-8263-a98900ebf208))
  (fp_line (start 20.6 -3.05) (end 20.6 -2.03)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp bcb1ef58-b899-405c-88c1-00ba0e3033f3))
  (fp_line (start 20.6 -0.51) (end 20.6 0.51)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 9f5eb466-b715-42df-8501-f329a04ba46b))
  (fp_line (start 20.6 2.03) (end 20.6 3.05)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2c7280f5-b090-4b91-9a79-6359bc51fb6b))
  (fp_line (start 20.6 4.57) (end 20.6 5.59)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 72bf8d2f-234c-4e68-82fe-3cd0d82167ab))
  (fp_line (start 20.6 7.11) (end 20.6 8.13)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp befa75d2-4dbf-4d28-a5ca-9d5d55d34529))
  (fp_line (start 20.6 9.65) (end 20.6 10.67)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 12ef7b7a-4b2f-428d-a96f-1538c16b3b69))
  (fp_line (start 20.6 12.19) (end 20.6 13.21)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp dbfdea03-a5e7-49e3-b67f-5d205fba7e24))
  (fp_line (start 20.6 14.73) (end 20.6 15.75)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 77bd15bf-d481-45af-94f0-da68a437b587))
  (fp_line (start 20.6 17.27) (end 20.6 18.29)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b635ab5e-7b9f-4064-8771-8ae5864444b9))
  (fp_line (start 20.6 19.81) (end 20.6 20.83)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0ae5b87b-6b35-45df-bca7-92f23e8e0ab2))
  (fp_line (start 20.6 22.35) (end 20.6 23.37)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0a28b33a-bb4e-439f-afbe-22f55b9add63))
  (fp_line (start 20.6 24.89) (end 20.6 25.46)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b32cc368-f896-45dc-b766-5c3c536c33cb))
  (fp_circle (center -18 0) (end -17.9 0)
    (stroke (width 0.12) (type default)) (fill none) (layer "F.SilkS") (tstamp 6caa38d9-6ae6-4084-913f-dcd696343fa1))
  (fp_circle (center 18 0) (end 18.1 0)
    (stroke (width 0.12) (type default)) (fill none) (layer "F.SilkS") (tstamp 0d7fea11-2034-4237-9028-bfcaed7c9734))
  (fp_line (start -23.9 -25.9) (end -23.9 25.9)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 30b1ea26-56f8-41e7-a5a6-feeca9dd73d0))
  (fp_line (start -23.9 25.9) (end -12.1 25.9)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 81a0b9ad-b493-4c59-af28-29b9cffd7373))
  (fp_line (start -12.1 -25.9) (end -23.9 -25.9)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 1aea0f3b-2327-4f95-8f11-ecbf7ad7ea66))
  (fp_line (start -12.1 25.9) (end -12.1 -25.9)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 8f254be2-b6be-4650-97dc-dc96576c0990))
  (fp_line (start 12.1 -25.9) (end 12.1 25.9)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp e4b52ba7-b21c-4b36-b76a-9bcd104b091e))
  (fp_line (start 12.1 25.9) (end 23.9 25.9)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp f8a5d5ae-a07e-48bf-8657-7a56f6959f5f))
  (fp_line (start 23.9 -25.9) (end 12.1 -25.9)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp c1bc1b48-0e79-4904-b13e-3fdf65a147e9))
  (fp_line (start 23.9 25.9) (end 23.9 -25.9)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 00cad8f1-f17d-4739-89c7-8282a784f467))
  (fp_line (start -21.6 -24.45) (end -21.6 -23.81)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c19ebe9b-7024-499a-9936-b53497e924a5))
  (fp_line (start -21.6 -23.81) (end -20.54 -23.81)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ce898fbe-cb56-4f86-8788-0582184b7bff))
  (fp_line (start -21.6 -21.91) (end -21.6 -21.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9f5125d2-07af-4e43-ad98-1344d8f15a02))
  (fp_line (start -21.6 -21.27) (end -20.54 -21.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8010b6ee-490a-4aee-811d-657168a791be))
  (fp_line (start -21.6 -19.37) (end -21.6 -18.73)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 039b14f4-4d59-4978-8416-cfbc1e2676fd))
  (fp_line (start -21.6 -18.73) (end -20.54 -18.73)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4b4cc23c-6a6d-41d9-ad34-8d2a230b0ec2))
  (fp_line (start -21.6 -16.83) (end -21.6 -16.19)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f3bd38a0-2148-4a97-8de0-24fdebfbe4fe))
  (fp_line (start -21.6 -16.19) (end -20.54 -16.19)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f59fa6cc-242f-409a-b353-4fe29f13d2c1))
  (fp_line (start -21.6 -14.29) (end -21.6 -13.65)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4b6b9c08-7340-4c9b-bc48-51d49ab8757a))
  (fp_line (start -21.6 -13.65) (end -20.54 -13.65)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0b54c6d8-1e9a-4f83-af61-a5d83e6d5a89))
  (fp_line (start -21.6 -11.75) (end -21.6 -11.11)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp bb55a712-d579-48ca-a364-8eb02ade59bd))
  (fp_line (start -21.6 -11.11) (end -20.54 -11.11)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1fd5a0ac-27a6-4824-8818-d1ea4b7b78e7))
  (fp_line (start -21.6 -9.21) (end -21.6 -8.57)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b0485f8e-dfc2-4ab0-9012-0e058e15bcf4))
  (fp_line (start -21.6 -8.57) (end -20.54 -8.57)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d8b433ef-b503-43ed-873f-f47a832a3557))
  (fp_line (start -21.6 -6.67) (end -21.6 -6.03)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 078fcd4a-0a0f-4528-b3d0-cba1394d849f))
  (fp_line (start -21.6 -6.03) (end -20.54 -6.03)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2cd570f2-99cf-4a26-a230-33a1a8fc2cbb))
  (fp_line (start -21.6 -4.13) (end -21.6 -3.49)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp dfbd5184-b7b3-453d-8f94-06b0c3dd1cdc))
  (fp_line (start -21.6 -3.49) (end -20.54 -3.49)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 55e93b14-d81a-409e-a377-ef8fdd6742e6))
  (fp_line (start -21.6 -1.59) (end -21.6 -0.95)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 003af911-f675-4864-9052-d607139403c0))
  (fp_line (start -21.6 -0.95) (end -20.54 -0.95)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 763b2e20-c9a5-46ee-8452-296adbf017cf))
  (fp_line (start -21.6 0.95) (end -21.6 1.59)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6132cb41-01c2-46bf-ad5e-df0e08b33d13))
  (fp_line (start -21.6 1.59) (end -20.54 1.59)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3308cd31-0bec-47cf-90d2-2dd02d36584f))
  (fp_line (start -21.6 3.49) (end -21.6 4.13)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c38f34e4-2d1f-4d89-9585-4ef4874084eb))
  (fp_line (start -21.6 4.13) (end -20.54 4.13)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 340407f8-36ae-4b74-89e3-df9953134dea))
  (fp_line (start -21.6 6.03) (end -21.6 6.67)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp da3de32b-9e12-40bb-9a7c-9db8f4c8b9fa))
  (fp_line (start -21.6 6.67) (end -20.54 6.67)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f13386c6-d36c-406a-aa58-508864d91984))
  (fp_line (start -21.6 8.57) (end -21.6 9.21)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 041935e9-a9b5-4aa5-9080-fd7f59d17b26))
  (fp_line (start -21.6 9.21) (end -20.54 9.21)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 286dadde-490b-4d3a-9e46-6d270971fae4))
  (fp_line (start -21.6 11.11) (end -21.6 11.75)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6aa3bd27-a0cb-4273-9493-cb07206eb929))
  (fp_line (start -21.6 11.75) (end -20.54 11.75)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3eb617eb-af83-490a-9d5c-5fe2e73b0e46))
  (fp_line (start -21.6 13.65) (end -21.6 14.29)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8c8eba21-cc94-44f5-a039-1ba6da9096a4))
  (fp_line (start -21.6 14.29) (end -20.54 14.29)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3780114d-426f-48a0-ab26-d8b576f30614))
  (fp_line (start -21.6 16.19) (end -21.6 16.83)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0f03d15f-3879-40fa-bf02-1a0f5e3aefd3))
  (fp_line (start -21.6 16.83) (end -20.54 16.83)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 71661d82-7f2b-481f-b336-14b49ec3006c))
  (fp_line (start -21.6 18.73) (end -21.6 19.37)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 06679f69-e7b3-411d-8db2-3f8122f8f147))
  (fp_line (start -21.6 19.37) (end -20.54 19.37)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ed40dbeb-7be6-4824-9359-9ca4552cbff4))
  (fp_line (start -21.6 21.27) (end -21.6 21.91)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp db7c2fba-6779-4847-a870-db293f48357b))
  (fp_line (start -21.6 21.91) (end -20.54 21.91)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp eea9730b-57fb-487b-b8d0-6d7860a235c3))
  (fp_line (start -21.6 23.81) (end -21.6 24.45)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 39783368-dae1-4d21-9ed5-3faae8e6bb62))
  (fp_line (start -21.6 24.45) (end -20.54 24.45)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c73c368f-14f1-4c83-b077-ff846cb7c824))
  (fp_line (start -20.54 -24.45) (end -21.6 -24.45)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 12a63650-5746-4c8c-8ef0-e473f54ce9cb))
  (fp_line (start -20.54 -24.45) (end -19.59 -25.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 370b59cb-72e9-4e35-9668-e0fe0b17cb44))
  (fp_line (start -20.54 -21.91) (end -21.6 -21.91)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d28cb690-fb37-432f-8705-138ca774d55e))
  (fp_line (start -20.54 -19.37) (end -21.6 -19.37)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 213d0521-d289-439c-80db-ec3ee43f3023))
  (fp_line (start -20.54 -16.83) (end -21.6 -16.83)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 632faf79-9136-47fc-b158-d3c65bf338d8))
  (fp_line (start -20.54 -14.29) (end -21.6 -14.29)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b386301e-941a-476a-ba60-b6f8935060e1))
  (fp_line (start -20.54 -11.75) (end -21.6 -11.75)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 934e2c9a-9cd8-4ceb-ba27-9543f41919b0))
  (fp_line (start -20.54 -9.21) (end -21.6 -9.21)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 55d28b47-5241-41b3-a164-34550bb70e9e))
  (fp_line (start -20.54 -6.67) (end -21.6 -6.67)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c1371e7c-5f02-4adf-813c-ff94b95d840e))
  (fp_line (start -20.54 -4.13) (end -21.6 -4.13)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4c13712e-3455-4a68-b63c-bcc42e36ce77))
  (fp_line (start -20.54 -1.59) (end -21.6 -1.59)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f9548860-edf8-4f91-b261-d35f97fc5b04))
  (fp_line (start -20.54 0.95) (end -21.6 0.95)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c6f83766-dde3-4b4c-83dd-6791d9ae46dc))
  (fp_line (start -20.54 3.49) (end -21.6 3.49)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3124c735-dc74-4ae4-9ae3-a1753589353f))
  (fp_line (start -20.54 6.03) (end -21.6 6.03)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a6712963-6ca8-4619-9566-b94c933fa8eb))
  (fp_line (start -20.54 8.57) (end -21.6 8.57)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a96826fe-015d-49e6-a328-fd25b868a9e2))
  (fp_line (start -20.54 11.11) (end -21.6 11.11)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 56918b52-c635-4ef8-b7f1-e61a821e90d1))
  (fp_line (start -20.54 13.65) (end -21.6 13.65)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 65ce6b08-23d5-4be7-b05d-d5c8dec6bd30))
  (fp_line (start -20.54 16.19) (end -21.6 16.19)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 674c1d24-c926-4b86-8d96-508e0de664c3))
  (fp_line (start -20.54 18.73) (end -21.6 18.73)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e485483d-4ce6-409b-8f99-27c8c8fb90b3))
  (fp_line (start -20.54 21.27) (end -21.6 21.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 16872d80-2f66-40e8-b156-88a0e1dca5c6))
  (fp_line (start -20.54 23.81) (end -21.6 23.81)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9fbcfc1e-c949-497c-94b5-4c40b04e2d66))
  (fp_line (start -20.54 25.4) (end -20.54 -24.45)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b30ab1be-c1d5-429d-b944-2a74dcb864d9))
  (fp_line (start -19.59 -25.4) (end -15.46 -25.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp be786a61-71ed-43f4-a68d-2841913edc49))
  (fp_line (start -15.46 -25.4) (end -15.46 25.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp bd7e916a-b7ec-4f1f-8a84-d6e003dd8f00))
  (fp_line (start -15.46 -24.45) (end -14.4 -24.45)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b79fe234-7c74-43e8-a1b0-971299a1f027))
  (fp_line (start -15.46 -21.91) (end -14.4 -21.91)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 543d7734-10e7-4d1f-b142-2b9094a9c46e))
  (fp_line (start -15.46 -19.37) (end -14.4 -19.37)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e3fe19c6-504d-4f19-98da-acfde0f7ffce))
  (fp_line (start -15.46 -16.83) (end -14.4 -16.83)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d87905f0-b845-4e46-ae2d-6f4b51413e12))
  (fp_line (start -15.46 -14.29) (end -14.4 -14.29)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 42177052-f50c-4fef-a350-3943c2e3b75e))
  (fp_line (start -15.46 -11.75) (end -14.4 -11.75)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d43cd111-9190-4a8c-84f2-d0ec0449a8b8))
  (fp_line (start -15.46 -9.21) (end -14.4 -9.21)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6f116646-f16a-4359-9b03-caba4898c7ad))
  (fp_line (start -15.46 -6.67) (end -14.4 -6.67)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5fc843b0-2187-41f9-8d62-e05a094750f2))
  (fp_line (start -15.46 -4.13) (end -14.4 -4.13)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 160c1dfe-67c9-4c24-98d4-c944a2b7d28e))
  (fp_line (start -15.46 -1.59) (end -14.4 -1.59)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c9c0c343-a340-4813-b744-c418eee4dbae))
  (fp_line (start -15.46 0.95) (end -14.4 0.95)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c02c90ad-53bc-4f3c-bf29-cd61dd67041f))
  (fp_line (start -15.46 3.49) (end -14.4 3.49)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 495892da-62f3-41e1-a43e-992dec49a84b))
  (fp_line (start -15.46 6.03) (end -14.4 6.03)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 790e8f6e-59aa-43fb-882f-db2d18c3379d))
  (fp_line (start -15.46 8.57) (end -14.4 8.57)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 19fcf3d7-dfa5-43d0-8125-8fd8f34bf524))
  (fp_line (start -15.46 11.11) (end -14.4 11.11)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp dc90041b-d8be-43af-a851-9c3bc504c7d7))
  (fp_line (start -15.46 13.65) (end -14.4 13.65)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c4beefdb-5ea4-4550-adfb-f3a6aee66333))
  (fp_line (start -15.46 16.19) (end -14.4 16.19)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4177e065-2b17-41be-a876-056453283fd5))
  (fp_line (start -15.46 18.73) (end -14.4 18.73)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 594ee896-0491-4e2e-bd7c-902c34313b7b))
  (fp_line (start -15.46 21.27) (end -14.4 21.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 85938365-4417-4061-ba67-dd8e3f4d7c83))
  (fp_line (start -15.46 23.81) (end -14.4 23.81)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9435d0e9-e01f-46ee-b7d3-10023b2e3716))
  (fp_line (start -15.46 25.4) (end -20.54 25.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d60ec6cd-df7c-4625-90e1-c2fd45dbb4d8))
  (fp_line (start -14.4 -24.45) (end -14.4 -23.81)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2ba85674-d60c-4397-ab68-ec0ce0524b5d))
  (fp_line (start -14.4 -23.81) (end -15.46 -23.81)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8221986e-c6bc-42a2-bb1a-dcf562fc80c4))
  (fp_line (start -14.4 -21.91) (end -14.4 -21.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5c075946-efea-46f6-bd58-f031d5d4ea9b))
  (fp_line (start -14.4 -21.27) (end -15.46 -21.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d859273e-ef58-4b44-96f6-1e5a10972e24))
  (fp_line (start -14.4 -19.37) (end -14.4 -18.73)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ee26b418-6c92-4635-9d03-c65703740b53))
  (fp_line (start -14.4 -18.73) (end -15.46 -18.73)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 41227785-1706-4eb9-b6ea-71c953adeaa8))
  (fp_line (start -14.4 -16.83) (end -14.4 -16.19)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 49ccaa05-164f-42d3-8146-0f389138994c))
  (fp_line (start -14.4 -16.19) (end -15.46 -16.19)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b1cee888-8e7f-4394-aadf-3287877459fd))
  (fp_line (start -14.4 -14.29) (end -14.4 -13.65)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 764e1c8d-cb25-40a6-b485-4881d5f5fd0c))
  (fp_line (start -14.4 -13.65) (end -15.46 -13.65)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9f29bc75-5d51-4473-b380-0919a4d50602))
  (fp_line (start -14.4 -11.75) (end -14.4 -11.11)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1f0d0458-d36e-4126-872a-9b0865334058))
  (fp_line (start -14.4 -11.11) (end -15.46 -11.11)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 712bd0a8-2546-4afe-9818-1ea24d11c887))
  (fp_line (start -14.4 -9.21) (end -14.4 -8.57)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4bdab574-c897-49de-87cb-ee2e5b28bf0c))
  (fp_line (start -14.4 -8.57) (end -15.46 -8.57)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c1a561f0-97f2-44eb-91b4-a3129cf6e310))
  (fp_line (start -14.4 -6.67) (end -14.4 -6.03)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ce868244-111b-4d66-8208-a03e5705e0c6))
  (fp_line (start -14.4 -6.03) (end -15.46 -6.03)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp da4b189d-a239-4ab4-ba76-41b8f0f30782))
  (fp_line (start -14.4 -4.13) (end -14.4 -3.49)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6d91186f-0cfc-4426-839c-fea50ef3bed3))
  (fp_line (start -14.4 -3.49) (end -15.46 -3.49)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 27b6386e-520f-457a-9000-0d4936cf8cb5))
  (fp_line (start -14.4 -1.59) (end -14.4 -0.95)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1bf8ee37-a5a7-4608-9057-a0a353cbc733))
  (fp_line (start -14.4 -0.95) (end -15.46 -0.95)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 119bfc24-b964-4a27-a421-2dbdde7aedc0))
  (fp_line (start -14.4 0.95) (end -14.4 1.59)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0c11e15e-729a-4985-a815-48eebd6f3de6))
  (fp_line (start -14.4 1.59) (end -15.46 1.59)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 19d80fd8-89b6-41d6-9497-1548549678d3))
  (fp_line (start -14.4 3.49) (end -14.4 4.13)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d4db3736-ddaa-4889-9790-9cb8b3b1f5e5))
  (fp_line (start -14.4 4.13) (end -15.46 4.13)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3d76b2cf-4805-4f3d-94ac-dfa1148f39c4))
  (fp_line (start -14.4 6.03) (end -14.4 6.67)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6af62d2b-550e-47a9-860e-9afbc81012d3))
  (fp_line (start -14.4 6.67) (end -15.46 6.67)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 64d62233-70f9-4c73-a305-155bbdb3a7bc))
  (fp_line (start -14.4 8.57) (end -14.4 9.21)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp baea0457-135c-4204-a2b7-546b9cfdf845))
  (fp_line (start -14.4 9.21) (end -15.46 9.21)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5b3887c9-f48b-4b77-be0a-f6d0183a8abc))
  (fp_line (start -14.4 11.11) (end -14.4 11.75)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp cca106f2-e5d4-4322-bebc-4f1f640a7b9c))
  (fp_line (start -14.4 11.75) (end -15.46 11.75)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp af009c80-d0fb-4408-a2f6-cfcc217377b7))
  (fp_line (start -14.4 13.65) (end -14.4 14.29)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8d594096-375a-4010-8f81-479955f5f55d))
  (fp_line (start -14.4 14.29) (end -15.46 14.29)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 584e6848-bf20-4e9e-99bd-10097568d361))
  (fp_line (start -14.4 16.19) (end -14.4 16.83)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 51dafe3f-a7d8-4093-9f75-4f6a68a59baf))
  (fp_line (start -14.4 16.83) (end -15.46 16.83)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9b116c76-b116-410c-801f-5f40600456af))
  (fp_line (start -14.4 18.73) (end -14.4 19.37)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 39bbe29d-4ef1-4ea9-ae73-595d610eef1e))
  (fp_line (start -14.4 19.37) (end -15.46 19.37)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp db733da2-cfa1-4968-87f9-078bf20fd255))
  (fp_line (start -14.4 21.27) (end -14.4 21.91)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 23888151-ff2d-475a-8df0-9059201075f3))
  (fp_line (start -14.4 21.91) (end -15.46 21.91)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp bcfa1de2-b417-421f-90cc-602a54c270d5))
  (fp_line (start -14.4 23.81) (end -14.4 24.45)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8a1baad7-f807-4dc7-b778-4b27d28aa515))
  (fp_line (start -14.4 24.45) (end -15.46 24.45)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 34d9c271-0ea1-4052-8ec9-a4d91ed76d79))
  (fp_line (start 14.4 -24.45) (end 14.4 -23.81)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8bf930d2-30ce-470f-9fc8-df3b22a292f5))
  (fp_line (start 14.4 -23.81) (end 15.46 -23.81)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f330f5dc-8f92-4ed1-80ae-b32cea934d53))
  (fp_line (start 14.4 -21.91) (end 14.4 -21.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp fd8badbf-b5d6-41e5-93ca-edebdb467d84))
  (fp_line (start 14.4 -21.27) (end 15.46 -21.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c86aa066-797f-44f3-89fa-cf850c98d41a))
  (fp_line (start 14.4 -19.37) (end 14.4 -18.73)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5f26b00b-4ac5-4629-b7f1-6c72f6bf221c))
  (fp_line (start 14.4 -18.73) (end 15.46 -18.73)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 42c3d6b5-0b0c-4076-b47c-99f08808e45d))
  (fp_line (start 14.4 -16.83) (end 14.4 -16.19)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 49d5979f-02ec-4753-824c-4c6ca47ffdc6))
  (fp_line (start 14.4 -16.19) (end 15.46 -16.19)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 76e26c9d-a9d5-4ea0-94b9-d786ded5b3ce))
  (fp_line (start 14.4 -14.29) (end 14.4 -13.65)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp bcab82aa-4662-44b7-abbe-5fd4bdc94a5a))
  (fp_line (start 14.4 -13.65) (end 15.46 -13.65)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8818c18a-4e44-4679-96af-01b91a9b572e))
  (fp_line (start 14.4 -11.75) (end 14.4 -11.11)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4cd5093b-e7e9-4b2f-9a6c-98a26b6f9b5b))
  (fp_line (start 14.4 -11.11) (end 15.46 -11.11)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0f33b2d2-9092-459a-9cd3-107dc6c1ef84))
  (fp_line (start 14.4 -9.21) (end 14.4 -8.57)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7ade1279-89be-46c9-8f1e-2b7f59df367a))
  (fp_line (start 14.4 -8.57) (end 15.46 -8.57)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 28ec6a18-1a46-40cf-b78d-52aea34d8083))
  (fp_line (start 14.4 -6.67) (end 14.4 -6.03)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ea2af740-8278-4075-864b-c89b48d4eeb2))
  (fp_line (start 14.4 -6.03) (end 15.46 -6.03)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a47a242a-6074-41b9-bdbf-cd48a7fbe50c))
  (fp_line (start 14.4 -4.13) (end 14.4 -3.49)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4ca070b1-69ee-4a0b-8bd5-0976a63f4596))
  (fp_line (start 14.4 -3.49) (end 15.46 -3.49)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 83e6f284-5c57-4c0b-b31d-d811ad7d3725))
  (fp_line (start 14.4 -1.59) (end 14.4 -0.95)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e772ca0f-7d01-42d4-8e2e-e74a8ea50bbe))
  (fp_line (start 14.4 -0.95) (end 15.46 -0.95)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6def11e1-d604-47f5-a16f-f62326993be0))
  (fp_line (start 14.4 0.95) (end 14.4 1.59)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8d39f579-9e6b-4a44-a2cb-3efc9abf46e6))
  (fp_line (start 14.4 1.59) (end 15.46 1.59)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b3feaa5a-8cc5-451b-8fe1-1e48ab8e8930))
  (fp_line (start 14.4 3.49) (end 14.4 4.13)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e4e32292-e5a2-4fa8-97e2-15b5b3c40226))
  (fp_line (start 14.4 4.13) (end 15.46 4.13)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp aafdfde7-6214-4fba-a6a9-b59146f333f5))
  (fp_line (start 14.4 6.03) (end 14.4 6.67)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp db040ae5-56ec-463b-9d7c-3319b85576c5))
  (fp_line (start 14.4 6.67) (end 15.46 6.67)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 68f524a5-cd67-41e7-9891-b6c1f3d31eb1))
  (fp_line (start 14.4 8.57) (end 14.4 9.21)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 88749a11-6907-48e2-908a-d3fd6ce17c2f))
  (fp_line (start 14.4 9.21) (end 15.46 9.21)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7363e15f-793d-4127-a8e6-9ced17c02b70))
  (fp_line (start 14.4 11.11) (end 14.4 11.75)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 90b80e83-1297-4f91-b886-a5f309d0273e))
  (fp_line (start 14.4 11.75) (end 15.46 11.75)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 946277b9-ad84-43d8-a38a-a0dc1bef441a))
  (fp_line (start 14.4 13.65) (end 14.4 14.29)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 02e3c7e3-8874-46b2-90aa-8d65d4d5f5d7))
  (fp_line (start 14.4 14.29) (end 15.46 14.29)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 972a8f14-a13f-4b2f-8a7b-f113ea78b4c7))
  (fp_line (start 14.4 16.19) (end 14.4 16.83)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e2975717-62cc-4a84-8631-1f679925eaca))
  (fp_line (start 14.4 16.83) (end 15.46 16.83)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 56efd6b0-03f6-4b15-81c2-fef85cceb133))
  (fp_line (start 14.4 18.73) (end 14.4 19.37)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 27225eba-95af-4dd9-be92-2233221819d0))
  (fp_line (start 14.4 19.37) (end 15.46 19.37)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0d820c9d-435f-4441-9ad0-5c1e6fbc3708))
  (fp_line (start 14.4 21.27) (end 14.4 21.91)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 77504658-0eae-4594-98e9-8688e6be2506))
  (fp_line (start 14.4 21.91) (end 15.46 21.91)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp cf722505-9b32-491d-89ea-b8f30551141a))
  (fp_line (start 14.4 23.81) (end 14.4 24.45)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7d42715f-ed80-4279-86ae-9c9f7ae50aec))
  (fp_line (start 14.4 24.45) (end 15.46 24.45)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp faf1d67f-6929-49c1-b78a-aace1d827ed5))
  (fp_line (start 15.46 -24.45) (end 14.4 -24.45)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9be354b7-964f-41fc-988b-34c2cd316d39))
  (fp_line (start 15.46 -24.45) (end 16.41 -25.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 190aecf1-4aab-4741-9f60-82b228ea2b02))
  (fp_line (start 15.46 -21.91) (end 14.4 -21.91)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp dc76f8c3-4bfa-4f8e-9643-12101945f889))
  (fp_line (start 15.46 -19.37) (end 14.4 -19.37)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp bc8ca52a-a4da-4dea-af3f-2389bfb039d1))
  (fp_line (start 15.46 -16.83) (end 14.4 -16.83)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp badbb9e1-b956-4f4c-a39b-eea47ca746b3))
  (fp_line (start 15.46 -14.29) (end 14.4 -14.29)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a7999d73-b1e9-4647-a455-d6bb10c31be4))
  (fp_line (start 15.46 -11.75) (end 14.4 -11.75)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e069fe44-b90f-4308-a467-20ea28bddfd4))
  (fp_line (start 15.46 -9.21) (end 14.4 -9.21)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5f656b89-f0b0-410d-b685-3f8021e32158))
  (fp_line (start 15.46 -6.67) (end 14.4 -6.67)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 287c36d3-8ec4-4768-9c34-ae1432b1fee7))
  (fp_line (start 15.46 -4.13) (end 14.4 -4.13)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 350b9265-3a9b-4b70-b7a9-5f5a12d54fa5))
  (fp_line (start 15.46 -1.59) (end 14.4 -1.59)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 93c04344-971a-4826-ab11-7a6c36457ea2))
  (fp_line (start 15.46 0.95) (end 14.4 0.95)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f8183cbd-1894-4c75-9239-8931bd8333a1))
  (fp_line (start 15.46 3.49) (end 14.4 3.49)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp dba166e6-518c-4cdd-b7ab-311fc6db2862))
  (fp_line (start 15.46 6.03) (end 14.4 6.03)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 44b00b05-4176-477f-8ad0-c6cb3fee1915))
  (fp_line (start 15.46 8.57) (end 14.4 8.57)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7536bddc-3e69-4efb-b613-721cef915f9e))
  (fp_line (start 15.46 11.11) (end 14.4 11.11)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 288814e4-3956-43dc-b4d7-70d15816921f))
  (fp_line (start 15.46 13.65) (end 14.4 13.65)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp dc65f12c-1ca4-4145-9d4b-408fed7003b1))
  (fp_line (start 15.46 16.19) (end 14.4 16.19)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 41f89d87-767a-41b6-80a1-600686220f96))
  (fp_line (start 15.46 18.73) (end 14.4 18.73)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a8061f43-cf7d-4b2a-971a-2bf2a4da3558))
  (fp_line (start 15.46 21.27) (end 14.4 21.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 825d2779-b32e-4d67-94df-b346151d351e))
  (fp_line (start 15.46 23.81) (end 14.4 23.81)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 465e9420-eb00-40bb-98c9-3b6a37199864))
  (fp_line (start 15.46 25.4) (end 15.46 -24.45)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp aab3956d-05cb-4790-bffd-6afe52f5dcfc))
  (fp_line (start 16.41 -25.4) (end 20.54 -25.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp edf5a5eb-94f4-42f8-ab2d-88a3dc599579))
  (fp_line (start 20.54 -25.4) (end 20.54 25.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 694a7571-6fb8-4db7-b5b7-ef0cb3d109fd))
  (fp_line (start 20.54 -24.45) (end 21.6 -24.45)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2890b089-549c-4549-b3ff-119423958a2f))
  (fp_line (start 20.54 -21.91) (end 21.6 -21.91)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ff4f3b7a-d611-4723-8088-3e74d78362cd))
  (fp_line (start 20.54 -19.37) (end 21.6 -19.37)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 32a71929-d3b7-4e93-a4b9-0dc9887ceed4))
  (fp_line (start 20.54 -16.83) (end 21.6 -16.83)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7f525394-95e0-4e20-beb9-77d1183945aa))
  (fp_line (start 20.54 -14.29) (end 21.6 -14.29)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6a053ae1-edd4-4e66-b41a-06d0611a36d1))
  (fp_line (start 20.54 -11.75) (end 21.6 -11.75)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a8de561c-a3f0-4fe1-855b-45afc231db9b))
  (fp_line (start 20.54 -9.21) (end 21.6 -9.21)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ffd85ccb-d80f-4eb7-aa5e-9a156ec974c4))
  (fp_line (start 20.54 -6.67) (end 21.6 -6.67)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp db0b867c-4dec-4009-91a1-ce2d5c9d7bfd))
  (fp_line (start 20.54 -4.13) (end 21.6 -4.13)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 952483e3-938f-4e8b-8f2c-f0c2290c233b))
  (fp_line (start 20.54 -1.59) (end 21.6 -1.59)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 932f9181-2184-469b-8faf-a7d52bef2cd5))
  (fp_line (start 20.54 0.95) (end 21.6 0.95)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp cf9fc3e6-9d60-462e-b85e-dc3343d84592))
  (fp_line (start 20.54 3.49) (end 21.6 3.49)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 047b36e7-686b-48dc-982b-875603c7a1e3))
  (fp_line (start 20.54 6.03) (end 21.6 6.03)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c9cf0bb8-3079-4b5d-aa08-38a79f0e8d83))
  (fp_line (start 20.54 8.57) (end 21.6 8.57)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1b2909d7-1062-4a62-b58e-2a0dc9d8ad67))
  (fp_line (start 20.54 11.11) (end 21.6 11.11)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6795bac8-2f78-4c5a-8309-377800b811e4))
  (fp_line (start 20.54 13.65) (end 21.6 13.65)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ce936e82-6e58-4ec3-9f75-2bcb7a80f2fb))
  (fp_line (start 20.54 16.19) (end 21.6 16.19)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3b70d8e6-6f20-4ce6-abcb-acf4e170b348))
  (fp_line (start 20.54 18.73) (end 21.6 18.73)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 19e9d305-2d1f-4fd1-8fd1-f056bee6a179))
  (fp_line (start 20.54 21.27) (end 21.6 21.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b1477438-4e05-460c-ae2e-c399d301b226))
  (fp_line (start 20.54 23.81) (end 21.6 23.81)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c5135b7b-49f2-4138-9bce-1f24b1207890))
  (fp_line (start 20.54 25.4) (end 15.46 25.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 63ba3452-fea2-4e8e-a1b3-88812217d906))
  (fp_line (start 21.6 -24.45) (end 21.6 -23.81)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9bc9bb35-a33d-48dc-a8be-aaff4787efaa))
  (fp_line (start 21.6 -23.81) (end 20.54 -23.81)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 18b2c908-8059-458f-902d-648304a3779a))
  (fp_line (start 21.6 -21.91) (end 21.6 -21.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0341ce0d-4341-4a59-907e-0462a79beb24))
  (fp_line (start 21.6 -21.27) (end 20.54 -21.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5f69a556-6a29-4544-ab78-9fe665223e1f))
  (fp_line (start 21.6 -19.37) (end 21.6 -18.73)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2dd68be6-4130-45ce-9021-2784a7d149cc))
  (fp_line (start 21.6 -18.73) (end 20.54 -18.73)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 45206612-b181-405e-8055-6e2e08e4b2c0))
  (fp_line (start 21.6 -16.83) (end 21.6 -16.19)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8477c2cb-3a6c-46c0-a87f-681719d5667c))
  (fp_line (start 21.6 -16.19) (end 20.54 -16.19)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 48e2431a-c6b3-4c4f-9b59-f8aa63253600))
  (fp_line (start 21.6 -14.29) (end 21.6 -13.65)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7c85e6b1-e8c6-485e-a550-25d6907a90a7))
  (fp_line (start 21.6 -13.65) (end 20.54 -13.65)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 16698c6c-96d1-4859-8965-6bc622c769dc))
  (fp_line (start 21.6 -11.75) (end 21.6 -11.11)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f414e4e2-2ad9-40a1-8e96-53c1d72d450e))
  (fp_line (start 21.6 -11.11) (end 20.54 -11.11)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 04e34276-0648-4bec-965d-7a8c3af7f939))
  (fp_line (start 21.6 -9.21) (end 21.6 -8.57)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5544c931-3242-44d7-9900-75dfe59f9e09))
  (fp_line (start 21.6 -8.57) (end 20.54 -8.57)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 78f5bd36-dfef-4694-b127-5701402ecc0b))
  (fp_line (start 21.6 -6.67) (end 21.6 -6.03)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a4cc1ec7-8e98-4c1f-997a-dcd4392ee573))
  (fp_line (start 21.6 -6.03) (end 20.54 -6.03)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7ff47005-5e8f-4324-a8e8-a1d63a8ffa87))
  (fp_line (start 21.6 -4.13) (end 21.6 -3.49)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b395bf16-08dc-45b5-9611-2c77aca7ba93))
  (fp_line (start 21.6 -3.49) (end 20.54 -3.49)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp dd698c15-1194-41f5-87ea-1e9f694cce72))
  (fp_line (start 21.6 -1.59) (end 21.6 -0.95)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0f7d0f2c-0ba6-4d18-98da-874528dc0804))
  (fp_line (start 21.6 -0.95) (end 20.54 -0.95)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 646af4ea-b8a1-48d8-a1f1-d53822eba75b))
  (fp_line (start 21.6 0.95) (end 21.6 1.59)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1c4774ac-ffd8-411c-8687-ff57ca45b558))
  (fp_line (start 21.6 1.59) (end 20.54 1.59)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp dc256536-2e81-42be-b538-c4c9a2b21540))
  (fp_line (start 21.6 3.49) (end 21.6 4.13)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 07a0397a-d2f4-4908-9e46-a80f674323b0))
  (fp_line (start 21.6 4.13) (end 20.54 4.13)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1cdd28f8-87ba-4d62-8f72-51537b6de5cc))
  (fp_line (start 21.6 6.03) (end 21.6 6.67)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp cef652b6-6cb2-4fd3-94e4-d25189a7ec42))
  (fp_line (start 21.6 6.67) (end 20.54 6.67)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ff9bd9ec-1da3-4a89-8882-98ce67516ce7))
  (fp_line (start 21.6 8.57) (end 21.6 9.21)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 98108d55-5f5a-44d0-997f-fa588880c3f1))
  (fp_line (start 21.6 9.21) (end 20.54 9.21)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 29c4f65a-d69d-49cc-8b1c-ff86b605e588))
  (fp_line (start 21.6 11.11) (end 21.6 11.75)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8b89c16f-61cb-4b27-bc40-2c0340cb0cd4))
  (fp_line (start 21.6 11.75) (end 20.54 11.75)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp eef68108-43ce-4b6c-8356-348da8239c2e))
  (fp_line (start 21.6 13.65) (end 21.6 14.29)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b0ce26f0-8c6e-4188-a960-32c9010198d2))
  (fp_line (start 21.6 14.29) (end 20.54 14.29)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 86f734e1-4b8a-445e-8607-87b5178dd159))
  (fp_line (start 21.6 16.19) (end 21.6 16.83)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 813a8526-2047-46c4-a72c-500b7f5ce2b3))
  (fp_line (start 21.6 16.83) (end 20.54 16.83)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ab3de5ce-0646-4a7e-a506-9a9e12a30d7c))
  (fp_line (start 21.6 18.73) (end 21.6 19.37)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9aaf7ebb-1ece-4679-b35e-f96d360bdf21))
  (fp_line (start 21.6 19.37) (end 20.54 19.37)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0b41fbb2-58e9-4a5f-ae91-5cbb97fda530))
  (fp_line (start 21.6 21.27) (end 21.6 21.91)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d36e5789-70fb-4ac4-980d-72c8b1cf015e))
  (fp_line (start 21.6 21.91) (end 20.54 21.91)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8040f5f8-2b42-43bb-a0a4-6c876b5b8fab))
  (fp_line (start 21.6 23.81) (end 21.6 24.45)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c4300d5a-9bfb-47e6-9052-b41052a0e603))
  (fp_line (start 21.6 24.45) (end 20.54 24.45)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 714c0d4b-e33d-4c63-af18-9a37636a2c01))
  (pad "A1" smd rect (at -20.525 -24.13) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 0ad7e7cc-54f3-4fc5-b5a6-838d2a9ab015))
  (pad "A2" smd rect (at -15.475 -24.13) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 790ea7b9-da9c-4204-aba0-cd8ae404db84))
  (pad "A3" smd rect (at -20.525 -21.59) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 3d544b6b-4f61-413f-b982-b709d47f0175))
  (pad "A4" smd rect (at -15.475 -21.59) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 2f2b3ff0-7eff-45c9-855d-8e0da81f9745))
  (pad "A5" smd rect (at -20.525 -19.05) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 7fde110e-5747-4242-a996-0d4d07c88f7d))
  (pad "A6" smd rect (at -15.475 -19.05) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp a83f8089-a3cf-4d72-b3d6-99fc7207b3fa))
  (pad "A7" smd rect (at -20.525 -16.51) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp b8a93d93-5360-427f-941a-3cdb29870e84))
  (pad "A8" smd rect (at -15.475 -16.51) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 52d30b2c-7123-4984-8755-fb00e3ec9b5b))
  (pad "A9" smd rect (at -20.525 -13.97) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 627a640a-23ea-4474-851a-151648460355))
  (pad "A10" smd rect (at -15.475 -13.97) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp a59d05ea-7e97-4797-bc4c-3cdd9399a581))
  (pad "A11" smd rect (at -20.525 -11.43) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 2a847144-a88d-4e9f-aa2b-3d0a2605920e))
  (pad "A12" smd rect (at -15.475 -11.43) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 969ab265-6e18-4663-9f85-117bf33a207a))
  (pad "A13" smd rect (at -20.525 -8.89) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 5a3609c8-691a-42fd-ab76-e6f9af9a9424))
  (pad "A14" smd rect (at -15.475 -8.89) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 84ea59c9-9189-44dc-a33b-f216bd333540))
  (pad "A15" smd rect (at -20.525 -6.35) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp dac58bcb-ace6-41bf-aec0-aa7367275bc3))
  (pad "A16" smd rect (at -15.475 -6.35) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 9349ebe7-e2cc-426b-bc54-f5c3dd5b67bd))
  (pad "A17" smd rect (at -20.525 -3.81) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 3186e1ff-3cc7-4bd2-a79b-4214daf73ab7))
  (pad "A18" smd rect (at -15.475 -3.81) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp edf97f6f-7fe6-4ec2-8d36-670ab04ca3af))
  (pad "A19" smd rect (at -20.525 -1.27) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 099323a7-1783-4841-a1e0-06636599f805))
  (pad "A20" smd rect (at -15.475 -1.27) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 654ac159-340c-4f0d-a4ad-9d037adf4399))
  (pad "A21" smd rect (at -20.525 1.27) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 50efccfb-6a76-4ffe-93f1-fc7c9ddd57c4))
  (pad "A22" smd rect (at -15.475 1.27) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp c625d6e8-e400-49fb-94cb-dd8cfb4745ce))
  (pad "A23" smd rect (at -20.525 3.81) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp af95d011-1b6a-42d2-b04f-c8bfdd3368aa))
  (pad "A24" smd rect (at -15.475 3.81) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 172e03c7-0abf-4e6e-9d21-c15e2c70b6fd))
  (pad "A25" smd rect (at -20.525 6.35) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 368c1034-2066-48ec-a35f-33cfc554fe1b))
  (pad "A26" smd rect (at -15.475 6.35) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 433c4304-6e1b-4be0-b370-3ce763b0f028))
  (pad "A27" smd rect (at -20.525 8.89) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp e28fd375-ddbb-4d9c-a366-274d835b6c8e))
  (pad "A28" smd rect (at -15.475 8.89) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 5dd0a005-b373-4e42-9068-ca81bbd3ab76))
  (pad "A29" smd rect (at -20.525 11.43) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp b0fca29b-fec1-4108-84aa-911336bde3a0))
  (pad "A30" smd rect (at -15.475 11.43) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 6e0e27dd-fff6-4e1f-9d68-16758718d625))
  (pad "A31" smd rect (at -20.525 13.97) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 0d23046c-afae-4645-9c37-a8ae7c2159dc))
  (pad "A32" smd rect (at -15.475 13.97) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 7ff010c2-0748-499b-8649-379c0ab3d9da))
  (pad "A33" smd rect (at -20.525 16.51) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 0c1909c0-7838-4428-8ab8-1c82bccaee0a))
  (pad "A34" smd rect (at -15.475 16.51) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp c249967f-33e1-41c5-ba09-053a5dc82818))
  (pad "A35" smd rect (at -20.525 19.05) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp f4f2c88d-dea2-45be-9908-88ef6d967bca))
  (pad "A36" smd rect (at -15.475 19.05) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 584823da-6abb-4d05-92a5-356628cd48d8))
  (pad "A37" smd rect (at -20.525 21.59) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp f69a6fd9-43e6-4316-a7c8-a15a564bf336))
  (pad "A38" smd rect (at -15.475 21.59) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp f57fe3df-4653-4709-ab49-d04bcfcc267a))
  (pad "A39" smd rect (at -20.525 24.13) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp ccd526f0-512a-4fca-9a8d-5bf1ea949221))
  (pad "A40" smd rect (at -15.475 24.13) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 4b09e17b-31c0-4f6c-8e44-3ba8d00b5f59))
  (pad "B1" smd rect (at 15.475 -24.13) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 574dcfba-2751-4617-9792-b8add3530805))
  (pad "B2" smd rect (at 20.525 -24.13) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp aae8f3b5-b8a0-4d32-810c-b3ba84b1830a))
  (pad "B3" smd rect (at 15.475 -21.59) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 06e76537-109b-4a24-98df-75218f275292))
  (pad "B4" smd rect (at 20.525 -21.59) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 7c7598a6-fa49-4a4d-b8fe-3702fee8f458))
  (pad "B5" smd rect (at 15.475 -19.05) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 92e90532-ea47-442d-80d8-ba3db97435b6))
  (pad "B6" smd rect (at 20.525 -19.05) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 0ae4a2fa-86b5-4f94-b85e-ad2666a91664))
  (pad "B7" smd rect (at 15.475 -16.51) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 54497c40-5610-4f0b-8920-afca882c7037))
  (pad "B8" smd rect (at 20.525 -16.51) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp b15db817-e934-4ea3-a8d0-1a833a2f00d0))
  (pad "B9" smd rect (at 15.475 -13.97) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 6f88ee01-4d78-4dd0-b729-3975d9b253dc))
  (pad "B10" smd rect (at 20.525 -13.97) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 3fb8037f-0a54-49ee-b83c-440cc0f27160))
  (pad "B11" smd rect (at 15.475 -11.43) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 256cab1b-822c-4b89-8788-a257667b2d72))
  (pad "B12" smd rect (at 20.525 -11.43) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp c22707e7-a1ff-4598-aeff-688dbf03537e))
  (pad "B13" smd rect (at 15.475 -8.89) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp c4adc0d1-5198-43d1-b3fe-750eb704f34a))
  (pad "B14" smd rect (at 20.525 -8.89) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 46cfcd04-ec3d-40ce-9fe8-3e1e7577ae72))
  (pad "B15" smd rect (at 15.475 -6.35) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp be100f7c-5b7b-482f-8656-3ce8265ba79f))
  (pad "B16" smd rect (at 20.525 -6.35) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 31c9511f-26b7-42a5-9b3e-b82dc9ed3807))
  (pad "B17" smd rect (at 15.475 -3.81) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 4f2690b8-7bb1-48a2-a48f-65e7562f61b2))
  (pad "B18" smd rect (at 20.525 -3.81) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 99a9c542-2eba-4029-9949-12465b4ab84b))
  (pad "B19" smd rect (at 15.475 -1.27) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp a6b5f3af-3193-4377-a6dc-f47961668821))
  (pad "B20" smd rect (at 20.525 -1.27) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp f5e0aa8f-54fe-4ea6-9945-e09cbd0a3b80))
  (pad "B21" smd rect (at 15.475 1.27) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 51da0edb-953e-46e0-b8a6-c652a9b2c59e))
  (pad "B22" smd rect (at 20.525 1.27) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp f541c5d3-b4ce-4fef-8af5-f8cf634eba0d))
  (pad "B23" smd rect (at 15.475 3.81) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 051d99b9-dc22-4c22-9305-58439c1007f2))
  (pad "B24" smd rect (at 20.525 3.81) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 334f599c-a6c0-4ad5-bbfc-a8c85c10c2fb))
  (pad "B25" smd rect (at 15.475 6.35) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 797a1698-19c0-4bad-a621-078547dd8118))
  (pad "B26" smd rect (at 20.525 6.35) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 7d856e8a-f9f3-451c-84be-53457572e016))
  (pad "B27" smd rect (at 15.475 8.89) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 92f2539f-46bf-49c6-ae57-b135b2843cf8))
  (pad "B28" smd rect (at 20.525 8.89) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 2df2b410-fca2-4e51-86c2-322798a7cf85))
  (pad "B29" smd rect (at 15.475 11.43) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp b05d338a-fc4c-4106-b653-4aa2899bc3a5))
  (pad "B30" smd rect (at 20.525 11.43) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 805643a8-a8d9-4070-a621-71960a8a4ba5))
  (pad "B31" smd rect (at 15.475 13.97) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp f8fc11d7-a710-4803-93b8-fcee4e582cce))
  (pad "B32" smd rect (at 20.525 13.97) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 625bac67-12f3-451c-878d-52f8c04225ab))
  (pad "B33" smd rect (at 15.475 16.51) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp bb0ad28b-2b75-49ac-8d9d-0305c1fdb5a4))
  (pad "B34" smd rect (at 20.525 16.51) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 55879180-cd9b-4470-90b3-209d2e3a2dd7))
  (pad "B35" smd rect (at 15.475 19.05) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp e1ebc8b2-aebb-4eac-b5c8-6eb7518efdc2))
  (pad "B36" smd rect (at 20.525 19.05) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 0a6f0872-1f69-475c-96e0-e72efab688b4))
  (pad "B37" smd rect (at 15.475 21.59) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 6b6839f7-ccf1-4018-a31d-8eb4f5ca7a80))
  (pad "B38" smd rect (at 20.525 21.59) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 2498d1f9-c9b8-4c79-8c0f-359b2db09d30))
  (pad "B39" smd rect (at 15.475 24.13) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 95c0ec06-36a1-4353-84a8-cd7d624e2f7f))
  (pad "B40" smd rect (at 20.525 24.13) (size 3.15 1) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 2e5850b5-eeb6-43f5-a20d-b70dc6f4b32c))
  (model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_2x20_P2.54mm_Vertical_SMD.wrl"
    (offset (xyz -14.9 0 0))
    (scale (xyz 1 1 1))
    (rotate (xyz 0 0 0))
  )
  (model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_2x20_P2.54mm_Vertical_SMD.wrl"
    (offset (xyz 14.9 0 0))
    (scale (xyz 1 1 1))
    (rotate (xyz 0 0 0))
  )
)
