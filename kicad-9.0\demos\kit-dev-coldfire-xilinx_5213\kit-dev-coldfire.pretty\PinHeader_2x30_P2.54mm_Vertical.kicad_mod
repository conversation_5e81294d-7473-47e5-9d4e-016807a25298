(footprint "PinHeader_2x30_P2.54mm_Vertical"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "Through hole straight pin header, 2x30, 2.54mm pitch, double rows")
	(tags "Through hole pin header THT 2x30 2.54mm double row")
	(property "Reference" "REF**"
		(at 1.27 -2.33 0)
		(layer "F.SilkS")
		(uuid "c5a2a02a-8b6b-4250-a8f5-df4d7a404a52")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "CONN_30X2"
		(at 1.27 75.99 0)
		(layer "F.Fab")
		(uuid "96bec288-9d92-4cfa-8787-29324bc5eb5f")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "27167d43-ac96-47d9-836f-4f32d7d4e56a")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "a9712b30-f3d6-4a8b-906d-572384b69f88")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -1.33 -1.33)
		(end 0 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "9fcd2cbd-faa8-4d4f-a5b6-687ade911aca")
	)
	(fp_line
		(start -1.33 0)
		(end -1.33 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "66494999-184c-4756-a14f-016e9a1c1ff9")
	)
	(fp_line
		(start -1.33 1.27)
		(end -1.33 74.99)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "5ec91592-23be-4d46-a3c6-22554adb2c8e")
	)
	(fp_line
		(start -1.33 1.27)
		(end 1.27 1.27)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "45642990-8b29-43ce-8cd8-ede525ff9651")
	)
	(fp_line
		(start -1.33 74.99)
		(end 3.87 74.99)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "8a662122-7d3b-4bdf-b0d6-32c26efb66d2")
	)
	(fp_line
		(start 1.27 -1.33)
		(end 3.87 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f9da4186-0bc3-4119-9d7b-bb4e768e6fec")
	)
	(fp_line
		(start 1.27 1.27)
		(end 1.27 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "606fb65e-3607-468f-91d9-0f1a7ad10d0a")
	)
	(fp_line
		(start 3.87 -1.33)
		(end 3.87 74.99)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "51225db2-1650-4bac-b516-fbc07340b83d")
	)
	(fp_line
		(start -1.8 -1.8)
		(end -1.8 75.45)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "366cb920-87d8-47e8-8fbd-067bc63f6fcb")
	)
	(fp_line
		(start -1.8 75.45)
		(end 4.35 75.45)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "7796c0d1-dbc1-41f8-862f-b9210a27a458")
	)
	(fp_line
		(start 4.35 -1.8)
		(end -1.8 -1.8)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "fc24ecdd-2cca-4026-8a50-a3b06adeca71")
	)
	(fp_line
		(start 4.35 75.45)
		(end 4.35 -1.8)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "600465ee-7efa-467a-b5b0-60ddd9f37b16")
	)
	(fp_line
		(start -1.27 0)
		(end 0 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "7a4aa973-39b2-4893-9714-a2b749de7c7b")
	)
	(fp_line
		(start -1.27 74.93)
		(end -1.27 0)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "31c06704-08d9-4b64-9efb-f0c4078c34b9")
	)
	(fp_line
		(start 0 -1.27)
		(end 3.81 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "bca344e5-4f3d-432c-a589-7d8cca0e8e3c")
	)
	(fp_line
		(start 3.81 -1.27)
		(end 3.81 74.93)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "f8ea34c2-3051-4a03-83ef-f5ee60df4ef6")
	)
	(fp_line
		(start 3.81 74.93)
		(end -1.27 74.93)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "f41a7f03-5e20-4119-8442-aba752885efc")
	)
	(fp_text user "${REFERENCE}"
		(at 1.27 36.83 90)
		(layer "F.Fab")
		(uuid "63859240-9a95-4792-8c48-054d8e7bfa29")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "e328e23e-ca09-4476-a4ad-a7849260f7b3")
	)
	(pad "2" thru_hole oval
		(at 2.54 0)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f264ded6-845e-4f29-ae29-39061f6097f3")
	)
	(pad "3" thru_hole oval
		(at 0 2.54)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "4baa5a83-b816-4622-9622-c9de951c7e79")
	)
	(pad "4" thru_hole oval
		(at 2.54 2.54)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "88392f6d-0be1-4b24-bbc8-b924ef49131f")
	)
	(pad "5" thru_hole oval
		(at 0 5.08)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "16508fa7-14e9-4540-b9ae-8196871f9644")
	)
	(pad "6" thru_hole oval
		(at 2.54 5.08)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "97d4b702-f0c1-4345-974c-003a7beb3193")
	)
	(pad "7" thru_hole oval
		(at 0 7.62)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "9e39b871-0378-4107-8b90-acac9cb234bd")
	)
	(pad "8" thru_hole oval
		(at 2.54 7.62)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7acb2ce2-4296-4674-be14-8ded9b0588f3")
	)
	(pad "9" thru_hole oval
		(at 0 10.16)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "d2b663d5-e22c-4138-ad47-31f98871325f")
	)
	(pad "10" thru_hole oval
		(at 2.54 10.16)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "daa4b56c-9a05-42eb-8556-0ffb0d821ca9")
	)
	(pad "11" thru_hole oval
		(at 0 12.7)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "8fbce5a0-f0dc-47a6-a180-fb10af801ee0")
	)
	(pad "12" thru_hole oval
		(at 2.54 12.7)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "2c5252ab-1ac5-4418-84e8-9a6489fed43d")
	)
	(pad "13" thru_hole oval
		(at 0 15.24)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "8f0128c2-c186-45b1-a767-c64c4d984c2e")
	)
	(pad "14" thru_hole oval
		(at 2.54 15.24)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "cff44461-ce1f-4656-8e4c-42f8ce1217d2")
	)
	(pad "15" thru_hole oval
		(at 0 17.78)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "3880a7f6-ecb4-433c-98dd-e258654ec1e5")
	)
	(pad "16" thru_hole oval
		(at 2.54 17.78)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6a3dd787-f675-439b-8f46-e6d745ac6331")
	)
	(pad "17" thru_hole oval
		(at 0 20.32)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "4f1d35cd-723b-4a2b-ae69-8f17c8a81cfa")
	)
	(pad "18" thru_hole oval
		(at 2.54 20.32)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "ff75dd2f-f104-4451-97bd-3f584803f875")
	)
	(pad "19" thru_hole oval
		(at 0 22.86)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "5927643f-eb8d-4968-8e37-bf2cba0965b8")
	)
	(pad "20" thru_hole oval
		(at 2.54 22.86)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0e63c703-aef5-403f-82f0-b4ea3d34bbd5")
	)
	(pad "21" thru_hole oval
		(at 0 25.4)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "22f14287-b4f7-48d9-aef4-6571e65edcbc")
	)
	(pad "22" thru_hole oval
		(at 2.54 25.4)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "13dea88d-8e93-4c13-abb6-ee8357ddac33")
	)
	(pad "23" thru_hole oval
		(at 0 27.94)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "3f2f73e1-9a6d-4a7e-bb7b-b066e364e68e")
	)
	(pad "24" thru_hole oval
		(at 2.54 27.94)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "d76a5f0a-0269-497e-b686-9a3786092aed")
	)
	(pad "25" thru_hole oval
		(at 0 30.48)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "bbb3e960-5aa7-4d27-9f64-b305be0e074f")
	)
	(pad "26" thru_hole oval
		(at 2.54 30.48)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "5ac0c85e-c04a-47d8-b684-46280b8039ec")
	)
	(pad "27" thru_hole oval
		(at 0 33.02)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c4984c46-a886-4cb9-9160-1391d0eb6632")
	)
	(pad "28" thru_hole oval
		(at 2.54 33.02)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "a1b01e5c-**************-2338f62f2d46")
	)
	(pad "29" thru_hole oval
		(at 0 35.56)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "10dd5f2f-c0c0-46dd-8da9-3ac38cb410ae")
	)
	(pad "30" thru_hole oval
		(at 2.54 35.56)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7b6e5d05-d87f-4250-a8e9-fcb70fb9000c")
	)
	(pad "31" thru_hole oval
		(at 0 38.1)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "35f7e0c0-3b83-4fe8-a820-ecb1309a6f31")
	)
	(pad "32" thru_hole oval
		(at 2.54 38.1)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "afc27f36-2259-4a4d-ab61-1c1c620a8ff3")
	)
	(pad "33" thru_hole oval
		(at 0 40.64)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "ff757d54-c1fd-49e4-b730-819985d584f9")
	)
	(pad "34" thru_hole oval
		(at 2.54 40.64)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "5eff0a1d-8061-468d-9056-fb81ba986153")
	)
	(pad "35" thru_hole oval
		(at 0 43.18)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "bdcff64e-0d0a-49c6-84f9-53be26537cb2")
	)
	(pad "36" thru_hole oval
		(at 2.54 43.18)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f2dabff2-ecad-498d-af56-fa01cdf94f9d")
	)
	(pad "37" thru_hole oval
		(at 0 45.72)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "44f0f8f0-093d-455c-8c9a-6b9968b04d25")
	)
	(pad "38" thru_hole oval
		(at 2.54 45.72)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "81b26c3b-af8a-4c7b-a0d9-1ba58e6d0adc")
	)
	(pad "39" thru_hole oval
		(at 0 48.26)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7fa3283e-7b49-45f4-ac5e-f2c89f990483")
	)
	(pad "40" thru_hole oval
		(at 2.54 48.26)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "2bbf2f0e-4fda-4073-aa34-bc28a34b075e")
	)
	(pad "41" thru_hole oval
		(at 0 50.8)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "106d318c-89a0-4ab3-9d05-08027db88e9d")
	)
	(pad "42" thru_hole oval
		(at 2.54 50.8)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "9da9a9fe-4742-4285-b446-4d63a5a60d53")
	)
	(pad "43" thru_hole oval
		(at 0 53.34)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "18f74220-edd8-469a-9650-45197c127427")
	)
	(pad "44" thru_hole oval
		(at 2.54 53.34)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "bc90e565-762d-4786-b989-16a9447c2fd2")
	)
	(pad "45" thru_hole oval
		(at 0 55.88)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c205aa31-3a5a-44b2-8f83-9db3348043db")
	)
	(pad "46" thru_hole oval
		(at 2.54 55.88)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "9617c75d-**************-7c387eca848d")
	)
	(pad "47" thru_hole oval
		(at 0 58.42)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "d6eef51a-9826-4a9b-a52d-2c051e625d03")
	)
	(pad "48" thru_hole oval
		(at 2.54 58.42)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7c25ba30-2e24-48e9-886d-4d2d17a55673")
	)
	(pad "49" thru_hole oval
		(at 0 60.96)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "12d59482-3639-47b2-86db-6818975a2ddf")
	)
	(pad "50" thru_hole oval
		(at 2.54 60.96)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "71b65c05-f19d-4e41-aff7-11e54e7440b3")
	)
	(pad "51" thru_hole oval
		(at 0 63.5)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "041ee6fe-700c-4c4e-bb03-77a17b5c13ae")
	)
	(pad "52" thru_hole oval
		(at 2.54 63.5)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "26cf4c1b-732e-4280-afb9-4dd928de0f81")
	)
	(pad "53" thru_hole oval
		(at 0 66.04)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7820ce95-1c04-4a77-9d6b-81027fb4904b")
	)
	(pad "54" thru_hole oval
		(at 2.54 66.04)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "100e1d4f-fe75-48d8-afea-518668b09821")
	)
	(pad "55" thru_hole oval
		(at 0 68.58)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "622487b6-da05-4aa9-bc58-abbc80fce7cc")
	)
	(pad "56" thru_hole oval
		(at 2.54 68.58)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f0a01942-17b7-4733-867f-70a421869f87")
	)
	(pad "57" thru_hole oval
		(at 0 71.12)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "5979cf09-1417-4528-a322-dfd2ff9c4606")
	)
	(pad "58" thru_hole oval
		(at 2.54 71.12)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "63efeac4-e1ad-49fc-9e24-93597380dad7")
	)
	(pad "59" thru_hole oval
		(at 0 73.66)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "38e77542-1229-4c1b-8292-c15c2264dff7")
	)
	(pad "60" thru_hole oval
		(at 2.54 73.66)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "1b6d68d6-f21e-471d-bd96-704e14991c0d")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_2x30_P2.54mm_Vertical.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
