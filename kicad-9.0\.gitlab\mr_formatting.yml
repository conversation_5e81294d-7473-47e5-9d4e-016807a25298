##########################################################################
# Test the formatting in a merge request using clang-format
##########################################################################
# The variable CI_COMMIT_BEFORE_SHA is not available in normal merge requests
# so we must build the commit hash ourselves, see:
# https://gitlab.com/gitlab-org/gitlab/-/issues/12850
test_formatting:
  stage: test
  needs: []
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  allow_failure: true
  before_script:
    # We must manually add the KiCad remote to ensure it is named sensibly
    - git remote add product https://gitlab.com/kicad/code/kicad.git ||
      git remote set-url product https://gitlab.com/kicad/code/kicad.git
    - git remote add source ${CI_MERGE_REQUEST_SOURCE_PROJECT_URL}.git ||
      git remote set-url source ${CI_MERGE_REQUEST_SOURCE_PROJECT_URL}.git
    - git fetch -n product
    - git fetch -n source
    # Get the SHAs of the commits
    - "TARGET_HEAD_SHA=$(git rev-parse product/${CI_MERGE_REQUEST_TARGET_BRANCH_NAME})"
    - "SOURCE_HEAD_SHA=$(git rev-parse source/${CI_MERGE_REQUEST_SOURCE_BRANCH_NAME})"
    - "MERGE_BASE_SHA=$(git merge-base ${TARGET_HEAD_SHA} ${SOURCE_HEAD_SHA})"
  script:
    - echo "Testing formatting from commit ${MERGE_BASE_SHA}"
    - ./tools/check_coding.sh --diff --ci --commit ${MERGE_BASE_SHA}
