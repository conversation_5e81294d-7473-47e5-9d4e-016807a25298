<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<wxFormBuilder_Project>
  <FileVersion major="1" minor="18"/>
  <object class="Project" expanded="true">
    <property name="code_generation">C++</property>
    <property name="cpp_class_decoration"></property>
    <property name="cpp_disconnect_events">1</property>
    <property name="cpp_event_generation">connect</property>
    <property name="cpp_help_provider">none</property>
    <property name="cpp_namespace"></property>
    <property name="cpp_precompiled_header"></property>
    <property name="cpp_use_array_enum">0</property>
    <property name="cpp_use_enum">0</property>
    <property name="embedded_files_path">res</property>
    <property name="encoding">UTF-8</property>
    <property name="file">bitmap2cmp_panel_base</property>
    <property name="first_id">1000</property>
    <property name="internationalize">1</property>
    <property name="lua_skip_events">1</property>
    <property name="lua_ui_table">UI</property>
    <property name="name">bitmap2cmp_panel</property>
    <property name="path">.</property>
    <property name="php_disconnect_events">0</property>
    <property name="php_disconnect_mode">source_name</property>
    <property name="php_skip_events">1</property>
    <property name="python_disconnect_events">0</property>
    <property name="python_disconnect_mode">source_name</property>
    <property name="python_image_path_wrapper_function_name"></property>
    <property name="python_indent_with_spaces"></property>
    <property name="python_skip_events">1</property>
    <property name="relative_path">1</property>
    <property name="use_microsoft_bom">0</property>
    <property name="use_native_eol">0</property>
    <object class="Panel" expanded="true">
      <property name="aui_managed">0</property>
      <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
      <property name="bg"></property>
      <property name="context_help"></property>
      <property name="context_menu">1</property>
      <property name="drag_accept_files">0</property>
      <property name="enabled">1</property>
      <property name="event_handler">impl_virtual</property>
      <property name="fg"></property>
      <property name="font"></property>
      <property name="hidden">0</property>
      <property name="id">wxID_ANY</property>
      <property name="maximum_size"></property>
      <property name="minimum_size"></property>
      <property name="name">BITMAP2CMP_PANEL_BASE</property>
      <property name="pos"></property>
      <property name="size">-1,-1</property>
      <property name="subclass">; ; forward_declare</property>
      <property name="tooltip"></property>
      <property name="two_step_creation">0</property>
      <property name="window_extra_style"></property>
      <property name="window_name"></property>
      <property name="window_style">wxTAB_TRAVERSAL</property>
      <object class="wxBoxSizer" expanded="true">
        <property name="minimum_size"></property>
        <property name="name">bMainSizer</property>
        <property name="orient">wxHORIZONTAL</property>
        <property name="permission">none</property>
        <object class="sizeritem" expanded="true">
          <property name="border">10</property>
          <property name="flag">wxEXPAND|wxBOTTOM|wxLEFT</property>
          <property name="proportion">1</property>
          <object class="wxNotebook" expanded="true">
            <property name="BottomDockable">1</property>
            <property name="LeftDockable">1</property>
            <property name="RightDockable">1</property>
            <property name="TopDockable">1</property>
            <property name="aui_layer">0</property>
            <property name="aui_name"></property>
            <property name="aui_position">0</property>
            <property name="aui_row">0</property>
            <property name="best_size"></property>
            <property name="bg"></property>
            <property name="bitmapsize"></property>
            <property name="caption"></property>
            <property name="caption_visible">1</property>
            <property name="center_pane">0</property>
            <property name="close_button">1</property>
            <property name="context_help"></property>
            <property name="context_menu">1</property>
            <property name="default_pane">0</property>
            <property name="dock">Dock</property>
            <property name="dock_fixed">0</property>
            <property name="docking">Left</property>
            <property name="drag_accept_files">0</property>
            <property name="enabled">1</property>
            <property name="fg"></property>
            <property name="floatable">1</property>
            <property name="font"></property>
            <property name="gripper">0</property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="max_size"></property>
            <property name="maximize_button">0</property>
            <property name="maximum_size"></property>
            <property name="min_size"></property>
            <property name="minimize_button">0</property>
            <property name="minimum_size">500,-1</property>
            <property name="moveable">1</property>
            <property name="name">m_Notebook</property>
            <property name="pane_border">1</property>
            <property name="pane_position"></property>
            <property name="pane_size"></property>
            <property name="permission">protected</property>
            <property name="pin_button">1</property>
            <property name="pos"></property>
            <property name="resize">Resizable</property>
            <property name="show">1</property>
            <property name="size"></property>
            <property name="style"></property>
            <property name="subclass"></property>
            <property name="toolbar_pane">0</property>
            <property name="tooltip"></property>
            <property name="window_extra_style"></property>
            <property name="window_name"></property>
            <property name="window_style"></property>
            <object class="notebookpage" expanded="true">
              <property name="bitmap"></property>
              <property name="label">Original Picture</property>
              <property name="select">0</property>
              <object class="wxScrolledWindow" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size">-1,-1</property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size">-1,-1</property>
                <property name="moveable">1</property>
                <property name="name">m_InitialPicturePanel</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="scroll_rate_x">5</property>
                <property name="scroll_rate_y">5</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="subclass"></property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style">wxHSCROLL|wxVSCROLL</property>
                <event name="OnPaint">OnPaintInit</event>
              </object>
            </object>
            <object class="notebookpage" expanded="true">
              <property name="bitmap"></property>
              <property name="label">Greyscale Picture</property>
              <property name="select">0</property>
              <object class="wxScrolledWindow" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size">-1,-1</property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size">-1,-1</property>
                <property name="moveable">1</property>
                <property name="name">m_GreyscalePicturePanel</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="scroll_rate_x">5</property>
                <property name="scroll_rate_y">5</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="subclass"></property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style">wxHSCROLL|wxVSCROLL</property>
                <event name="OnPaint">OnPaintGreyscale</event>
              </object>
            </object>
            <object class="notebookpage" expanded="true">
              <property name="bitmap"></property>
              <property name="label">Black &amp;&amp; White Picture</property>
              <property name="select">1</property>
              <object class="wxScrolledWindow" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_BNPicturePanel</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="scroll_rate_x">5</property>
                <property name="scroll_rate_y">5</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="subclass"></property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style">wxHSCROLL|wxVSCROLL</property>
                <event name="OnPaint">OnPaintBW</event>
              </object>
            </object>
          </object>
        </object>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxEXPAND|wxALL</property>
          <property name="proportion">0</property>
          <object class="wxBoxSizer" expanded="true">
            <property name="minimum_size"></property>
            <property name="name">brightSizer</property>
            <property name="orient">wxVERTICAL</property>
            <property name="permission">none</property>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxALL</property>
              <property name="proportion">0</property>
              <object class="wxStaticBoxSizer" expanded="false">
                <property name="id">wxID_ANY</property>
                <property name="label">Image Information</property>
                <property name="minimum_size"></property>
                <property name="name">sbSizerInfo</property>
                <property name="orient">wxVERTICAL</property>
                <property name="parent">1</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="false">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND</property>
                  <property name="proportion">0</property>
                  <object class="wxFlexGridSizer" expanded="false">
                    <property name="cols">4</property>
                    <property name="flexible_direction">wxBOTH</property>
                    <property name="growablecols">1,2</property>
                    <property name="growablerows"></property>
                    <property name="hgap">0</property>
                    <property name="minimum_size"></property>
                    <property name="name">fgSizerInfo</property>
                    <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                    <property name="permission">none</property>
                    <property name="rows">0</property>
                    <property name="vgap">0</property>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALL|wxALIGN_CENTER_VERTICAL</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Image size:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticTextISize</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxALL</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">0000</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_SizeXValue</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxTOP|wxBOTTOM|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">0000</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_SizeYValue</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxTOP|wxBOTTOM|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">pixels</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_SizePixUnits</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxBOTTOM|wxRIGHT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Image PPI:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticTextDPI</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM|wxRIGHT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">0000</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_InputXValueDPI</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">0000</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_InputYValueDPI</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">PPI</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_DPIUnit</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxBOTTOM|wxLEFT|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">BPP:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticTextBPP</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM|wxRIGHT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">0000</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_BPPValue</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">bits</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_BPPunits</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag"></property>
                      <property name="proportion">0</property>
                      <object class="spacer" expanded="false">
                        <property name="height">0</property>
                        <property name="permission">protected</property>
                        <property name="width">0</property>
                      </object>
                    </object>
                  </object>
                </object>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxALL</property>
              <property name="proportion">0</property>
              <object class="wxButton" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Load Source Image</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_buttonLoad</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass"></property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">OnLoadFile</event>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">1</property>
              <object class="spacer" expanded="true">
                <property name="height">0</property>
                <property name="permission">protected</property>
                <property name="width">0</property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxALL|wxEXPAND</property>
              <property name="proportion">0</property>
              <object class="wxStaticBoxSizer" expanded="true">
                <property name="id">wxID_ANY</property>
                <property name="label">Output Size</property>
                <property name="minimum_size"></property>
                <property name="name">sbSizerImgPrms</property>
                <property name="orient">wxVERTICAL</property>
                <property name="parent">1</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND|wxBOTTOM</property>
                  <property name="proportion">0</property>
                  <object class="wxBoxSizer" expanded="true">
                    <property name="minimum_size"></property>
                    <property name="name">bSizerRes</property>
                    <property name="orient">wxHORIZONTAL</property>
                    <property name="permission">none</property>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Size:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_sizeLabel</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">private</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxTextCtrl" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="maxlength">0</property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size">60,-1</property>
                        <property name="moveable">1</property>
                        <property name="name">m_UnitSizeX</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NUMERIC</property>
                        <property name="validator_type">wxTextValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value">300</property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnText">OnSizeChangeX</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxTextCtrl" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="maxlength">0</property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size">60,-1</property>
                        <property name="moveable">1</property>
                        <property name="name">m_UnitSizeY</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NUMERIC</property>
                        <property name="validator_type">wxTextValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value">300</property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnText">OnSizeChangeY</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxChoice" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="choices"></property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size">80,-1</property>
                        <property name="moveable">1</property>
                        <property name="name">m_PixelUnit</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="selection">0</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; Not forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnChoice">OnSizeUnitChange</event>
                      </object>
                    </object>
                  </object>
                </object>
                <object class="sizeritem" expanded="false">
                  <property name="border">5</property>
                  <property name="flag">wxALL</property>
                  <property name="proportion">0</property>
                  <object class="wxCheckBox" expanded="false">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="checked">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="label">Lock height / width ratio</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_aspectRatioCheckbox</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass">; ; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="validator_data_type"></property>
                    <property name="validator_style">wxFILTER_NONE</property>
                    <property name="validator_type">wxDefaultValidator</property>
                    <property name="validator_variable"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <event name="OnCheckBox">ToggleAspectRatioLock</event>
                  </object>
                </object>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxALL|wxEXPAND</property>
              <property name="proportion">0</property>
              <object class="wxStaticBoxSizer" expanded="false">
                <property name="id">wxID_ANY</property>
                <property name="label">Options</property>
                <property name="minimum_size"></property>
                <property name="name">sbSizer2</property>
                <property name="orient">wxVERTICAL</property>
                <property name="parent">1</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="false">
                  <property name="border">5</property>
                  <property name="flag">wxRIGHT|wxLEFT</property>
                  <property name="proportion">0</property>
                  <object class="wxStaticText" expanded="false">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="label">Black / white threshold:</property>
                    <property name="markup">0</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_ThresholdText</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass"></property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <property name="wrap">-1</property>
                  </object>
                </object>
                <object class="sizeritem" expanded="false">
                  <property name="border">5</property>
                  <property name="flag">wxBOTTOM|wxEXPAND|wxLEFT|wxRIGHT</property>
                  <property name="proportion">0</property>
                  <object class="wxSlider" expanded="false">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="maxValue">100</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="minValue">0</property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_sliderThreshold</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style">wxSL_HORIZONTAL|wxSL_LABELS</property>
                    <property name="subclass"></property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip">Adjust the level to convert the greyscale picture to a black and white picture.</property>
                    <property name="validator_data_type"></property>
                    <property name="validator_style">wxFILTER_NONE</property>
                    <property name="validator_type">wxDefaultValidator</property>
                    <property name="validator_variable"></property>
                    <property name="value">50</property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <event name="OnCommandScrollChanged">OnThresholdChange</event>
                    <event name="OnCommandScrollThumbTrack">OnThresholdChange</event>
                  </object>
                </object>
                <object class="sizeritem" expanded="false">
                  <property name="border">5</property>
                  <property name="flag">wxBOTTOM|wxRIGHT|wxLEFT</property>
                  <property name="proportion">0</property>
                  <object class="wxCheckBox" expanded="false">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="checked">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="label">Negative</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_checkNegative</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass">; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="validator_data_type"></property>
                    <property name="validator_style">wxFILTER_NONE</property>
                    <property name="validator_type">wxDefaultValidator</property>
                    <property name="validator_variable"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <event name="OnCheckBox">OnNegativeClicked</event>
                  </object>
                </object>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxALL</property>
              <property name="proportion">1</property>
              <object class="wxStaticBoxSizer" expanded="true">
                <property name="id">wxID_ANY</property>
                <property name="label">Output Format</property>
                <property name="minimum_size"></property>
                <property name="name">sbOutputFormat</property>
                <property name="orient">wxVERTICAL</property>
                <property name="parent">1</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND|wxRIGHT|wxLEFT</property>
                  <property name="proportion">1</property>
                  <object class="wxFlexGridSizer" expanded="true">
                    <property name="cols">1</property>
                    <property name="flexible_direction">wxBOTH</property>
                    <property name="growablecols"></property>
                    <property name="growablerows"></property>
                    <property name="hgap">0</property>
                    <property name="minimum_size"></property>
                    <property name="name">fgSizer2</property>
                    <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                    <property name="permission">none</property>
                    <property name="rows">5</property>
                    <property name="vgap">2</property>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxBOTTOM|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxRadioButton" expanded="true">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Symbol (.kicad_sym file)</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_rbSymbol</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value">0</property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnRadioButton">OnFormatChange</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxTOP|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxRadioButton" expanded="true">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Footprint (.kicad_mod file)</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_rbFootprint</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value">0</property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnRadioButton">OnFormatChange</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxEXPAND</property>
                      <property name="proportion">1</property>
                      <object class="wxBoxSizer" expanded="true">
                        <property name="minimum_size"></property>
                        <property name="name">bSizer4</property>
                        <property name="orient">wxHORIZONTAL</property>
                        <property name="permission">none</property>
                        <object class="sizeritem" expanded="true">
                          <property name="border">28</property>
                          <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticText" expanded="true">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer">0</property>
                            <property name="aui_name"></property>
                            <property name="aui_position">0</property>
                            <property name="aui_row">0</property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Layer:</property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_layerLabel</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="true">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT|wxLEFT</property>
                          <property name="proportion">1</property>
                          <object class="wxChoice" expanded="true">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer">0</property>
                            <property name="aui_name"></property>
                            <property name="aui_position">0</property>
                            <property name="aui_row">0</property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="choices">&quot;F.Cu&quot; &quot;F.Silkscreen&quot; &quot;F.Mask&quot; &quot;User.Drawings&quot; &quot;User.Comments&quot; &quot;User.Eco1&quot; &quot;User.Eco2&quot; &quot;F.Fab&quot;</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_layerCtrl</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="selection">0</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                          </object>
                        </object>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxTOP|wxBOTTOM|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxRadioButton" expanded="true">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Postscript (.ps file)</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_rbPostscript</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value">0</property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnRadioButton">OnFormatChange</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxTOP|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxRadioButton" expanded="true">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Drawing Sheet (.kicad_wks file)</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_rbWorksheet</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value">0</property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnRadioButton">OnFormatChange</event>
                      </object>
                    </object>
                  </object>
                </object>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxALL</property>
              <property name="proportion">0</property>
              <object class="wxButton" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Export to File...</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_buttonExportFile</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass"></property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">OnExportToFile</event>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxBOTTOM|wxRIGHT|wxLEFT</property>
              <property name="proportion">0</property>
              <object class="wxButton" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Export to Clipboard</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_buttonExportClipboard</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass"></property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">OnExportToClipboard</event>
              </object>
            </object>
          </object>
        </object>
      </object>
    </object>
  </object>
</wxFormBuilder_Project>
