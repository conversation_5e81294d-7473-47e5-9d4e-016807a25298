# Copyright 2018 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("crashpad_buildconfig.gni")
import("test.gni")
if (crashpad_is_in_chromium) {
  import("//testing/libfuzzer/fuzzer_test.gni")
}

template("crashpad_fuzzer_test") {
  if (crashpad_is_standalone && crashpad_use_libfuzzer) {
    test(target_name) {
      forward_variables_from(invoker,
                             [
                               "cflags",
                               "cflags_cc",
                               "check_includes",
                               "defines",
                               "include_dirs",
                               "sources",
                             ])
      configs += [ "..:crashpad_config" ]
      if (defined(invoker.deps)) {
        deps = invoker.deps
      }
      deps += [ "../third_party/libfuzzer" ]

      if (!defined(invoker.cflags)) {
        cflags = []
      }
      cflags += [ "-fsanitize=fuzzer" ]
    }
    if (defined(invoker.seed_corpus)) {
      not_needed(invoker, [ "seed_corpus" ])
    }
  } else if (crashpad_is_in_chromium && use_fuzzing_engine) {
    # Append "crashpad_" to the beginning of the fuzzer's name to make it easier
    # in Chromium to recognize where fuzzer came from.
    forward_variables_from(invoker, "*")
    fuzzer_test("crashpad_" + target_name) {
    }
  } else {
    not_needed(invoker, "*")
    group(target_name) {
    }
  }
}
