(footprint "PinHeader_2x20_P2.54mm_Vertical"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "Through hole straight pin header, 2x20, 2.54mm pitch, double rows")
	(tags "Through hole pin header THT 2x20 2.54mm double row")
	(property "Reference" "REF**"
		(at 1.27 -2.33 0)
		(layer "F.SilkS")
		(uuid "853eafd5-0895-4820-bde9-76e6af42a9cf")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "CONN_20X2"
		(at 1.27 50.59 0)
		(layer "F.Fab")
		(uuid "21e7bd2a-f0ed-44e9-bd40-1ef046f703d7")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "d28a746e-1a14-4c41-88bf-7264356a92ff")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "574893db-c501-47bc-bcaf-1157b501862c")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -1.33 -1.33)
		(end 0 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "abd07564-4fcd-4dc4-b79d-ca5adf74dacd")
	)
	(fp_line
		(start -1.33 0)
		(end -1.33 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "b2c16551-4c43-4cae-8910-a0dd03db664c")
	)
	(fp_line
		(start -1.33 1.27)
		(end -1.33 49.59)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "207ba74e-7efd-4f94-b3d8-62ff106aff74")
	)
	(fp_line
		(start -1.33 1.27)
		(end 1.27 1.27)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "9f66580a-5d81-4355-bb45-a42fc14c03db")
	)
	(fp_line
		(start -1.33 49.59)
		(end 3.87 49.59)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f30099b4-5b69-495b-aee0-aced5a4d806f")
	)
	(fp_line
		(start 1.27 -1.33)
		(end 3.87 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "1bcc6446-f9e9-44d5-a4db-23c5f6b4b99f")
	)
	(fp_line
		(start 1.27 1.27)
		(end 1.27 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "d20cde1b-10d1-47e9-a1f4-73e9a8c85676")
	)
	(fp_line
		(start 3.87 -1.33)
		(end 3.87 49.59)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "29424452-bb03-46ea-98e4-8d93cfa794f1")
	)
	(fp_line
		(start -1.8 -1.8)
		(end -1.8 50.05)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "e2badcc6-9fdf-4811-bae7-ce0959010037")
	)
	(fp_line
		(start -1.8 50.05)
		(end 4.35 50.05)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "00a5fb1d-3f4b-42ae-ac92-291fc2d934e8")
	)
	(fp_line
		(start 4.35 -1.8)
		(end -1.8 -1.8)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "b5bba4af-304b-40cd-be03-1d823aace0fb")
	)
	(fp_line
		(start 4.35 50.05)
		(end 4.35 -1.8)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "af7c0674-743a-4bb8-8e47-a94ccbca2e6a")
	)
	(fp_line
		(start -1.27 0)
		(end 0 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "ac1eee9d-32a6-432a-9688-a64e0c0d3919")
	)
	(fp_line
		(start -1.27 49.53)
		(end -1.27 0)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "5661d5b2-8531-464a-a412-f26e9cf984b7")
	)
	(fp_line
		(start 0 -1.27)
		(end 3.81 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "ca4bce69-ca10-4e02-a5e4-44f12d70df26")
	)
	(fp_line
		(start 3.81 -1.27)
		(end 3.81 49.53)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "345cfc33-79e0-45c2-abc6-fd8c831ba1a0")
	)
	(fp_line
		(start 3.81 49.53)
		(end -1.27 49.53)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "cf3654bb-be9c-418c-ab15-87b05e813134")
	)
	(fp_text user "${REFERENCE}"
		(at 1.27 24.13 90)
		(layer "F.Fab")
		(uuid "eeb11980-1a93-4756-8e06-0a460b7240d6")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "959e4222-edd6-4ab3-8146-166dfe774f0c")
	)
	(pad "2" thru_hole oval
		(at 2.54 0)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "be98a1af-2a5e-4c9c-9ab1-bffaf822b2a2")
	)
	(pad "3" thru_hole oval
		(at 0 2.54)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c75f198e-f0f2-4b43-a7b1-4adb8954eced")
	)
	(pad "4" thru_hole oval
		(at 2.54 2.54)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0935ae66-3da0-49c7-82b3-b47ea9a15684")
	)
	(pad "5" thru_hole oval
		(at 0 5.08)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "fb41bbd3-8c8d-4f07-8d06-3ec2db0f18ef")
	)
	(pad "6" thru_hole oval
		(at 2.54 5.08)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f1d4c4bb-01af-423b-823d-44a307894372")
	)
	(pad "7" thru_hole oval
		(at 0 7.62)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "3263333f-175a-4791-9f12-ba9d7482c3f6")
	)
	(pad "8" thru_hole oval
		(at 2.54 7.62)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f08830ab-f2f8-4885-8d78-de221b805573")
	)
	(pad "9" thru_hole oval
		(at 0 10.16)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0366ea4a-8120-4556-8e41-42bd800f44c8")
	)
	(pad "10" thru_hole oval
		(at 2.54 10.16)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "67f11382-8367-4861-a488-5c39fb5bf65d")
	)
	(pad "11" thru_hole oval
		(at 0 12.7)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "54ca4f26-9544-4e86-af3d-d93bb4198557")
	)
	(pad "12" thru_hole oval
		(at 2.54 12.7)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6064fd8e-bdf1-4c35-8b49-7d076c119af1")
	)
	(pad "13" thru_hole oval
		(at 0 15.24)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "d50cc1b6-332f-42d8-aae5-4bffee775198")
	)
	(pad "14" thru_hole oval
		(at 2.54 15.24)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "458a76b5-d386-479d-b113-6f5274fa89b4")
	)
	(pad "15" thru_hole oval
		(at 0 17.78)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7159b871-782a-4ff8-9c45-051a1e6a92b5")
	)
	(pad "16" thru_hole oval
		(at 2.54 17.78)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c4ed5d8a-d2e6-4363-bef6-821062fab8da")
	)
	(pad "17" thru_hole oval
		(at 0 20.32)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "263e605a-d199-4373-850a-fecf07e386a5")
	)
	(pad "18" thru_hole oval
		(at 2.54 20.32)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f8ba9b5f-2048-4f4b-9f40-bef0c23da3a0")
	)
	(pad "19" thru_hole oval
		(at 0 22.86)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "42d574b8-09d2-48fa-8aa7-b1b7d83f4404")
	)
	(pad "20" thru_hole oval
		(at 2.54 22.86)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "a744f6a3-d1ea-4645-a65c-d017b8486d66")
	)
	(pad "21" thru_hole oval
		(at 0 25.4)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "19988161-58da-4c20-9f78-67a8d599d3df")
	)
	(pad "22" thru_hole oval
		(at 2.54 25.4)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "845d8a1a-60e0-41d5-a7d9-5272393e8ef2")
	)
	(pad "23" thru_hole oval
		(at 0 27.94)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "027b47bd-0785-44a4-97ca-60a5ab2e770e")
	)
	(pad "24" thru_hole oval
		(at 2.54 27.94)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "de4f320d-8a84-46a0-b2f9-3e35c4cdfe45")
	)
	(pad "25" thru_hole oval
		(at 0 30.48)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "ae0f7aa1-ffe2-40a6-ba36-bb20080decfd")
	)
	(pad "26" thru_hole oval
		(at 2.54 30.48)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7307e3c2-9400-4dbc-94bc-edebaf6be455")
	)
	(pad "27" thru_hole oval
		(at 0 33.02)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "69afc123-485a-4897-b0ca-852b890ab058")
	)
	(pad "28" thru_hole oval
		(at 2.54 33.02)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "d8947df4-0143-4408-810b-6bd0f81b509a")
	)
	(pad "29" thru_hole oval
		(at 0 35.56)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "2602bcae-194c-4251-a28b-c3287f44987e")
	)
	(pad "30" thru_hole oval
		(at 2.54 35.56)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "9b698b08-8cc8-4f4d-b6f6-734668d18661")
	)
	(pad "31" thru_hole oval
		(at 0 38.1)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f6564bdc-9d01-4c60-aab3-8442ef649d6a")
	)
	(pad "32" thru_hole oval
		(at 2.54 38.1)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "131cf3fb-1fd8-449f-bec6-356ac30c2948")
	)
	(pad "33" thru_hole oval
		(at 0 40.64)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "678f5e1c-43e1-4675-ba1d-98d99354f7c1")
	)
	(pad "34" thru_hole oval
		(at 2.54 40.64)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "9d04031e-a08d-40ec-b3ef-12e68c576e38")
	)
	(pad "35" thru_hole oval
		(at 0 43.18)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "3afed5a8-1527-41ba-843a-1425a6d3dfbb")
	)
	(pad "36" thru_hole oval
		(at 2.54 43.18)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "550e9a61-97cd-4328-a063-5f79247262f2")
	)
	(pad "37" thru_hole oval
		(at 0 45.72)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "273bbf3f-0c28-4662-b7cb-a185a961d9a1")
	)
	(pad "38" thru_hole oval
		(at 2.54 45.72)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "5dbac7d6-9c69-4c30-a8e5-f11bfcb928db")
	)
	(pad "39" thru_hole oval
		(at 0 48.26)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "3a45df6a-eea6-4918-a4cf-2c180dcad916")
	)
	(pad "40" thru_hole oval
		(at 2.54 48.26)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "103953db-3ea2-481c-a881-576efef5bfe2")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_2x20_P2.54mm_Vertical.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
