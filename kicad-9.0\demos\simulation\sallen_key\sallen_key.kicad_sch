(kicad_sch (version 20230121) (generator eeschema)

  (uuid 403a5eac-c035-4b08-8ba1-e036b0ed18fb)

  (paper "A4")

  (lib_symbols
    (symbol "C_1" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "C" (at 0.635 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "C_1" (at 0.635 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0.9652 -3.81 0)
        (effects (font (size 0.762 0.762)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "ki_fp_filters" "C? C_????_* C_???? SMD*_c Capacitor*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "C_1_0_1"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy 2.032 -0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.032 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
      )
      (symbol "C_1_1_1"
        (pin passive line (at 0 3.81 270) (length 2.794)
          (name "1" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
        (pin passive line (at 0 -3.81 90) (length 2.794)
          (name "2" (effects (font (size 0.508 0.508))))
          (number "2" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "R_1" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "R" (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R_1" (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at -1.778 0 90)
        (effects (font (size 0.762 0.762)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 0.762 0.762)))
      )
      (property "ki_fp_filters" "R_* Resistor_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_1_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "1" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "2" (effects (font (size 0.508 0.508))))
          (number "2" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "power:PWR_FLAG" (power) (pin_numbers hide) (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "#FLG" (at 0 1.905 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "PWR_FLAG" (at 0 3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Special symbol for telling ERC where power comes from" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "PWR_FLAG_0_0"
        (pin power_out line (at 0 0 90) (length 0)
          (name "pwr" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
      (symbol "PWR_FLAG_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 1.27)
            (xy -1.016 1.905)
            (xy 0 2.54)
            (xy 1.016 1.905)
            (xy 0 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
    )
    (symbol "sallen_key_schlib:C" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "C" (at 0.635 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "C" (at 0.635 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0.9652 -3.81 0)
        (effects (font (size 0.762 0.762)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "ki_fp_filters" "C? C_????_* C_???? SMD*_c Capacitor*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "C_0_1"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy 2.032 -0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.032 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
      )
      (symbol "C_1_1"
        (pin passive line (at 0 3.81 270) (length 2.794)
          (name "1" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
        (pin passive line (at 0 -3.81 90) (length 2.794)
          (name "2" (effects (font (size 0.508 0.508))))
          (number "2" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "sallen_key_schlib:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "sallen_key_schlib:Generic_Opamp" (in_bom yes) (on_board yes)
      (property "Reference" "U" (at 0 6.35 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "Generic_Opamp" (at 0 3.81 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at -2.54 -2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (symbol "Generic_Opamp_0_1"
        (polyline
          (pts
            (xy -5.08 5.08)
            (xy 5.08 0)
            (xy -5.08 -5.08)
            (xy -5.08 5.08)
          )
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "Generic_Opamp_1_1"
        (pin input line (at -7.62 2.54 0) (length 2.54)
          (name "+" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -7.62 -2.54 0) (length 2.54)
          (name "-" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at -2.54 7.62 270) (length 3.81)
          (name "V+" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at -2.54 -7.62 90) (length 3.81)
          (name "V-" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin output line (at 7.62 0 180) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "sallen_key_schlib:R" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "R" (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at -1.778 0 90)
        (effects (font (size 0.762 0.762)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 0.762 0.762)))
      )
      (property "ki_fp_filters" "R_* Resistor_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "1" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "2" (effects (font (size 0.508 0.508))))
          (number "2" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "sallen_key_schlib:VDD" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "VDD" (at 0 3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (symbol "VDD_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 1.905) (radius 0.635)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "VDD_1_1"
        (pin power_in line (at 0 0 90) (length 0) hide
          (name "VDD" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "sallen_key_schlib:VSOURCE" (pin_names (offset 1.016)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (at 5.08 5.08 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "VSOURCE" (at 6.35 2.54 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Fieldname" "Value" (at 0 0 0)
        (effects (font (size 1.524 1.524)) hide)
      )
      (property "Spice_Primitive" "V" (at 0 0 0)
        (effects (font (size 1.524 1.524)) hide)
      )
      (property "Spice_Node_Sequence" "1 2" (at -7.62 5.08 0)
        (effects (font (size 1.524 1.524)) hide)
      )
      (symbol "VSOURCE_0_1"
        (polyline
          (pts
            (xy 0 -1.905)
            (xy 0 1.905)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 1.905)
            (xy -0.635 0.635)
            (xy 0.635 0.635)
            (xy 0 1.905)
          )
          (stroke (width 0) (type default))
          (fill (type outline))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "VSOURCE_1_1"
        (pin input line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "sallen_key_schlib:VSS" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "VSS" (at 0 3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (symbol "VSS_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 1.905) (radius 0.635)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "VSS_1_1"
        (pin power_in line (at 0 0 90) (length 0) hide
          (name "VSS" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 238.76 53.34) (diameter 1.016) (color 0 0 0 0)
    (uuid 02da825e-d7f6-49c7-8c03-12654ce766c9)
  )
  (junction (at 245.11 40.64) (diameter 1.016) (color 0 0 0 0)
    (uuid 0f0b2605-3d33-4744-b0b4-3b5ecf3d92b2)
  )
  (junction (at 245.11 64.77) (diameter 1.016) (color 0 0 0 0)
    (uuid 2b9d0e01-0976-459c-981e-afa20845f847)
  )
  (junction (at 168.91 109.22) (diameter 1.016) (color 0 0 0 0)
    (uuid 4773872d-f7b2-47d0-87af-5b6d0531d9ea)
  )
  (junction (at 212.09 111.76) (diameter 1.016) (color 0 0 0 0)
    (uuid 4cd048d1-0789-4dd1-bb8e-34e7741086e5)
  )
  (junction (at 245.11 53.34) (diameter 1.016) (color 0 0 0 0)
    (uuid 604e0fed-41f1-4c47-b586-ee91372323a8)
  )
  (junction (at 187.96 125.73) (diameter 1.016) (color 0 0 0 0)
    (uuid 9e18109d-441e-4778-a097-f1676b17921a)
  )
  (junction (at 186.69 109.22) (diameter 1.016) (color 0 0 0 0)
    (uuid dd495cd1-7e58-43ed-aac3-8099ec2dc842)
  )

  (wire (pts (xy 212.09 125.73) (xy 187.96 125.73))
    (stroke (width 0) (type solid))
    (uuid 04a0fa97-724b-4250-b3c9-50868d81ca29)
  )
  (wire (pts (xy 168.91 109.22) (xy 172.72 109.22))
    (stroke (width 0) (type solid))
    (uuid 0a3a4288-cc59-4f63-8919-1ed2d0495376)
  )
  (wire (pts (xy 187.96 114.3) (xy 191.77 114.3))
    (stroke (width 0) (type solid))
    (uuid 18af8a04-7474-4306-bc63-fb241a675c11)
  )
  (wire (pts (xy 238.76 53.34) (xy 245.11 53.34))
    (stroke (width 0) (type solid))
    (uuid 20cdbfb1-cf98-4869-ae0a-35934c6fe1c1)
  )
  (wire (pts (xy 237.49 64.77) (xy 237.49 66.04))
    (stroke (width 0) (type solid))
    (uuid 29ec3d0b-f6aa-4938-895a-e7a4f9c879e8)
  )
  (wire (pts (xy 186.69 109.22) (xy 191.77 109.22))
    (stroke (width 0) (type solid))
    (uuid 2c3d03d4-6df6-4aea-9439-547003fbd912)
  )
  (wire (pts (xy 187.96 125.73) (xy 181.61 125.73))
    (stroke (width 0) (type solid))
    (uuid 36dc6c4c-5af7-4616-ba76-d4c9f815abbd)
  )
  (wire (pts (xy 180.34 109.22) (xy 186.69 109.22))
    (stroke (width 0) (type solid))
    (uuid 38332f6a-ef3c-4b8f-a7a0-c5d0ca83aacf)
  )
  (wire (pts (xy 152.4 124.46) (xy 152.4 127))
    (stroke (width 0) (type solid))
    (uuid 3b4eed65-c7c5-4efe-babc-151ad52f99ad)
  )
  (wire (pts (xy 245.11 64.77) (xy 237.49 64.77))
    (stroke (width 0) (type solid))
    (uuid 467689ee-94c7-439f-9284-4154ec32566c)
  )
  (wire (pts (xy 168.91 125.73) (xy 168.91 109.22))
    (stroke (width 0) (type solid))
    (uuid 55ebc0b1-c787-407c-a267-6e11e8f64dac)
  )
  (wire (pts (xy 212.09 125.73) (xy 212.09 111.76))
    (stroke (width 0) (type solid))
    (uuid 5799e104-a3aa-48fe-a8aa-4ded2dcc8dbe)
  )
  (wire (pts (xy 187.96 125.73) (xy 187.96 114.3))
    (stroke (width 0) (type solid))
    (uuid 619abdde-c8ec-4cd3-b2ac-96f4c0be3b95)
  )
  (wire (pts (xy 212.09 111.76) (xy 226.06 111.76))
    (stroke (width 0) (type solid))
    (uuid 640ba09e-d127-41b3-9851-0b378f034976)
  )
  (wire (pts (xy 173.99 125.73) (xy 168.91 125.73))
    (stroke (width 0) (type solid))
    (uuid 644909cf-ebce-45bb-9cf8-d7ff7b16ce44)
  )
  (wire (pts (xy 166.37 109.22) (xy 168.91 109.22))
    (stroke (width 0) (type solid))
    (uuid 65aa925b-cc2b-4ed1-938b-53ab398eaeac)
  )
  (wire (pts (xy 245.11 52.07) (xy 245.11 53.34))
    (stroke (width 0) (type solid))
    (uuid 689f4ba3-6ca8-461e-86fc-2a1db5fafcec)
  )
  (wire (pts (xy 238.76 53.34) (xy 238.76 54.61))
    (stroke (width 0) (type solid))
    (uuid 843a6d4c-45b3-4f69-a0a1-f472412e2c21)
  )
  (wire (pts (xy 238.76 40.64) (xy 245.11 40.64))
    (stroke (width 0) (type solid))
    (uuid 93d26412-87cc-4799-a843-8829f8fcbe4f)
  )
  (wire (pts (xy 186.69 105.41) (xy 186.69 109.22))
    (stroke (width 0) (type solid))
    (uuid 99cc3a79-95ad-48e7-907c-7be9ee6eeee0)
  )
  (wire (pts (xy 245.11 53.34) (xy 245.11 54.61))
    (stroke (width 0) (type solid))
    (uuid 9c22a812-9067-4604-be3f-547a0a2217c9)
  )
  (wire (pts (xy 232.41 53.34) (xy 232.41 54.61))
    (stroke (width 0) (type solid))
    (uuid a426ff34-4317-4d53-8ebb-77d42a3aef18)
  )
  (wire (pts (xy 245.11 66.04) (xy 245.11 64.77))
    (stroke (width 0) (type solid))
    (uuid c854f96f-1c70-4368-a74d-7eed960694e1)
  )
  (wire (pts (xy 232.41 53.34) (xy 238.76 53.34))
    (stroke (width 0) (type solid))
    (uuid ccb9d828-8fe0-4c3e-8489-2598990b2481)
  )
  (wire (pts (xy 245.11 40.64) (xy 245.11 41.91))
    (stroke (width 0) (type solid))
    (uuid cd086666-3eba-4dec-b71d-7f0791de7a0a)
  )
  (wire (pts (xy 186.69 96.52) (xy 186.69 97.79))
    (stroke (width 0) (type solid))
    (uuid dad68928-14fc-4cec-851a-d33b8a09ed10)
  )
  (wire (pts (xy 207.01 111.76) (xy 212.09 111.76))
    (stroke (width 0) (type solid))
    (uuid dbc07984-8dcb-427d-9b78-57aeeb8bf649)
  )
  (wire (pts (xy 158.75 109.22) (xy 152.4 109.22))
    (stroke (width 0) (type solid))
    (uuid e2298bba-e155-47c7-bbf1-ddeed005fc64)
  )
  (wire (pts (xy 152.4 109.22) (xy 152.4 114.3))
    (stroke (width 0) (type solid))
    (uuid fc2d08ca-d4e2-4706-9885-a0781d86f92f)
  )

  (text_box ".control\nversion\n.endc"
    (at 109.22 130.81 0) (size 14.986 8.7796)
    (stroke (width 0) (type default))
    (fill (type none))
    (effects (font (size 1.524 1.524)) (justify left top))
    (uuid d15f446c-5062-4dbf-aa54-9559d3f29629)
  )

  (text ".ac dec 10 1 1Meg\n" (at 109.22 127 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
    (uuid d38c3d1e-9916-43e1-b08b-2be978d6d7e1)
  )

  (label "lowpass" (at 217.17 111.76 0) (fields_autoplaced)
    (effects (font (size 1.524 1.524)) (justify left bottom))
    (uuid bd619414-e4f2-475c-8922-cde135b28a78)
  )

  (symbol (lib_id "sallen_key_schlib:VSOURCE") (at 152.4 119.38 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-000057336052)
    (property "Reference" "V1" (at 155.6512 118.2116 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "${SIM.PARAMS}" (at 155.6512 120.523 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 152.4 119.38 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 152.4 119.38 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (at 152.4 119.38 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Device" "V" (at 152.4 119.38 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Params" "ac=1" (at 0 0 0)
      (effects (font (size 0 0)) hide)
    )
    (property "Sim.Pins" "1=+ 2=-" (at 144.78 114.3 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Type" "DC" (at 152.4 119.38 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 6b115aad-7e6a-4d3d-9f04-32436028f2e2))
    (pin "2" (uuid 676ffa01-b5ac-46ff-aa06-7a996aec4756))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "V1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "sallen_key_schlib:Generic_Opamp") (at 199.39 111.76 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005788ff9f)
    (property "Reference" "U1" (at 201.93 107.95 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "AD8051" (at 200.66 115.57 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 196.85 114.3 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 199.39 111.76 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Sim.Library" "ad8051.lib" (at 199.39 111.76 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Name" "AD8051" (at 199.39 111.76 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Pins" "1=1 2=2 3=99 4=50 5=45" (at 0 0 0)
      (effects (font (size 0 0)) hide)
    )
    (pin "1" (uuid 5ce021d9-0c83-45c0-a205-62b618da33c2))
    (pin "2" (uuid 5083cecd-6789-4a7e-8828-39dc0e1fe03b))
    (pin "3" (uuid 8101937b-8472-4cd6-8f15-36904ee73daa))
    (pin "4" (uuid bacade97-4581-4b25-9c4a-22c8c16bcb13))
    (pin "5" (uuid 9279f3fb-ce5d-490a-8f5b-684612565b68))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "U1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "sallen_key_schlib:VSOURCE") (at 245.11 46.99 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 00000000-0000-0000-0000-0000578900ba)
    (property "Reference" "V2" (at 248.285 46.3955 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "10" (at 248.285 48.1577 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 245.11 46.99 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 245.11 46.99 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Sim.Device" "V" (at 245.11 46.99 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Type" "DC" (at 0 0 0)
      (effects (font (size 0 0)) hide)
    )
    (property "Sim.Pins" "1=+ 2=-" (at 237.49 41.91 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid 63d57c3f-1061-4b05-b866-55bec0c26e5b))
    (pin "2" (uuid 5c0c2285-7e50-4c77-a6f7-d3a084f8ed0d))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "V2") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "sallen_key_schlib:VSOURCE") (at 245.11 59.69 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 00000000-0000-0000-0000-000057890232)
    (property "Reference" "V3" (at 248.285 59.0955 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "10" (at 248.285 60.8577 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 245.11 59.69 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 245.11 59.69 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Sim.Device" "V" (at 245.11 59.69 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Type" "DC" (at 0 0 0)
      (effects (font (size 0 0)) hide)
    )
    (property "Sim.Pins" "1=+ 2=-" (at 237.49 54.61 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid 3a312d53-d59f-447a-afa9-d697fafd0175))
    (pin "2" (uuid 8f8436df-0b89-4660-a3c0-3ba3117ad384))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "V3") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "sallen_key_schlib:GND") (at 238.76 54.61 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000578902d2)
    (property "Reference" "#PWR05" (at 238.76 60.96 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 238.887 59.0042 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 238.76 54.61 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 238.76 54.61 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid e291403e-12ec-4780-94f1-476cef62b8fd))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "#PWR05") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "sallen_key_schlib:VDD") (at 245.11 40.64 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000578903c0)
    (property "Reference" "#PWR06" (at 245.11 44.45 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "VDD" (at 245.5418 36.2458 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 245.11 40.64 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 245.11 40.64 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 08fb5b5f-9d7e-42cd-85b7-2369422cf2e4))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "#PWR06") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "sallen_key_schlib:VSS") (at 245.11 66.04 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000578903e2)
    (property "Reference" "#PWR07" (at 245.11 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "VSS" (at 244.6528 70.4342 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 245.11 66.04 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 245.11 66.04 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 954b9000-f9d7-465e-8075-b60c8cee0229))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "#PWR07") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "sallen_key_schlib:VDD") (at 196.85 104.14 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-000057890425)
    (property "Reference" "#PWR03" (at 196.85 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "VDD" (at 197.2818 99.7458 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 196.85 104.14 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 196.85 104.14 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid d071a357-1d06-4ef1-a4cb-70a18c24ba96))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "#PWR03") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "sallen_key_schlib:VSS") (at 196.85 119.38 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-000057890453)
    (property "Reference" "#PWR04" (at 196.85 115.57 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "VSS" (at 196.3928 123.7742 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 196.85 119.38 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 196.85 119.38 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 6ff1c0ce-774e-419b-b6ea-34916926fb24))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "#PWR04") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "sallen_key_schlib:R") (at 176.53 109.22 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 00000000-0000-0000-0000-000057890691)
    (property "Reference" "R2" (at 176.53 105.3719 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "1k" (at 176.53 107.1341 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 176.53 107.442 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 176.53 109.22 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (at 176.53 109.22 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Pins" "1=1 2=2" (at 176.53 109.22 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid fcb5a355-f45c-497e-b6a3-3e5a13cc7722))
    (pin "2" (uuid 4422bd9e-5e5f-4bc2-bf15-76469b94f69c))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "R2") (unit 1)
        )
      )
    )
  )

  (symbol (lib_name "R_1") (lib_id "sallen_key_schlib:R") (at 162.56 109.22 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 00000000-0000-0000-0000-0000578906ff)
    (property "Reference" "R1" (at 162.56 105.3719 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "1k" (at 162.56 107.1341 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 162.56 107.442 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 162.56 109.22 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (at 162.56 109.22 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Pins" "1=1 2=2" (at 162.56 109.22 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid 58c9f791-9dd7-4632-b8eb-c2cf84d59bb7))
    (pin "2" (uuid f9915401-49fb-4d8b-a88a-406c8445878e))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "R1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_name "C_1") (lib_id "sallen_key_schlib:C") (at 177.8 125.73 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 00000000-0000-0000-0000-00005789077d)
    (property "Reference" "C1" (at 177.8 120.7389 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "100n" (at 177.8 122.5011 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 173.99 126.6952 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 177.8 125.73 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid d06ec118-9aea-40e0-bc66-0c264d3d39d8))
    (pin "2" (uuid cab9a507-b497-44ad-a2d5-72bd7ea0128f))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "C1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "sallen_key_schlib:C") (at 186.69 101.6 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005789085b)
    (property "Reference" "C2" (at 183.769 102.7684 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100n" (at 183.769 100.457 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 185.7248 97.79 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 186.69 101.6 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (at 186.69 101.6 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Pins" "1=1 2=2" (at 186.69 101.6 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid f26dfb31-49c6-4c2c-94ed-a4dda9281e00))
    (pin "2" (uuid c612e07d-6937-41c6-b3be-261e6cbb96da))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "C2") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "sallen_key_schlib:GND") (at 186.69 96.52 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-000057890b95)
    (property "Reference" "#PWR02" (at 186.69 90.17 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 186.563 92.1258 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 186.69 96.52 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 186.69 96.52 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 3a5206ce-0eb3-487f-b69e-eca956e6bcc8))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "#PWR02") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "sallen_key_schlib:GND") (at 152.4 127 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005a0b57f1)
    (property "Reference" "#PWR0101" (at 152.4 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 152.527 131.3942 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 152.4 127 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 152.4 127 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid b676fc81-c1ef-4fa1-a0c3-6854254163ad))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "#PWR0101") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:PWR_FLAG") (at 237.49 66.04 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid c10484fa-5553-45ee-ac36-942fdc26ec8e)
    (property "Reference" "#FLG0101" (at 237.49 67.945 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "PWR_FLAG" (at 237.49 70.3644 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 237.49 66.04 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 237.49 66.04 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c12835cf-8fd3-413e-ba06-7c963e127850))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "#FLG0101") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:PWR_FLAG") (at 238.76 40.64 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid d2ccd91b-84a5-4a27-b12e-cea8603afb89)
    (property "Reference" "#FLG0103" (at 238.76 38.735 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "PWR_FLAG" (at 238.76 36.3156 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 238.76 40.64 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 238.76 40.64 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 2e534ba7-e485-440d-ab50-1013be43f65a))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "#FLG0103") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:PWR_FLAG") (at 232.41 54.61 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid e06aaf18-42f6-424d-8f09-e7146efa2360)
    (property "Reference" "#FLG0102" (at 232.41 56.515 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "PWR_FLAG" (at 232.41 58.9344 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 232.41 54.61 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 232.41 54.61 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid e1826a3e-938e-458c-9c54-55323c05818a))
    (instances
      (project "sallen_key"
        (path "/403a5eac-c035-4b08-8ba1-e036b0ed18fb"
          (reference "#FLG0102") (unit 1)
        )
      )
    )
  )

  (sheet_instances
    (path "/" (page "1"))
  )
)
