(footprint "LQFP-100_14x14mm_P0.5mm"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "LQFP, 100 Pin (https://www.nxp.com/docs/en/package-information/SOT407-1.pdf), generated with kicad-footprint-generator ipc_gullwing_generator.py")
	(tags "LQFP QFP")
	(property "Reference" "REF**"
		(at 0 -9.65 0)
		(layer "F.SilkS")
		(uuid "3ad8465d-7657-4532-884f-5e181ea0a5f7")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "MCF5213-LQFP100"
		(at 0 9.65 0)
		(layer "F.Fab")
		(uuid "86d4fbc1-c71b-41cb-a2b7-da7883ff5026")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "2bab1278-32e5-4948-ad5f-1a03d3127f77")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "2d7f1ee5-bac1-46ec-81f2-d534b06b6cc4")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr smd)
	(fp_line
		(start -7.11 -7.11)
		(end -7.11 -6.41)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "42a0445b-9701-4346-90e9-2c311d46d637")
	)
	(fp_line
		(start -7.11 7.11)
		(end -7.11 6.41)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "21b3e668-900c-4bf6-aa4c-d4fc4ff13e47")
	)
	(fp_line
		(start -6.41 -7.11)
		(end -7.11 -7.11)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "bc0b3eea-3dbe-415f-92ee-34a7546c895d")
	)
	(fp_line
		(start -6.41 7.11)
		(end -7.11 7.11)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "16bafaca-862c-4714-af64-bdc54fee38b8")
	)
	(fp_line
		(start 6.41 -7.11)
		(end 7.11 -7.11)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "2c039619-f85c-48d4-b1ff-75915c881e9c")
	)
	(fp_line
		(start 6.41 7.11)
		(end 7.11 7.11)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "86424d52-f334-4e80-9179-90f3a4f48e94")
	)
	(fp_line
		(start 7.11 -7.11)
		(end 7.11 -6.41)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "4a4c04b1-b784-4251-8faa-5412c5d2af9a")
	)
	(fp_line
		(start 7.11 7.11)
		(end 7.11 6.41)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "e097baf1-c526-4438-894f-c68a7872e1fd")
	)
	(fp_poly
		(pts
			(xy -7.7375 -6.41) (xy -8.0775 -6.88) (xy -7.3975 -6.88) (xy -7.7375 -6.41)
		)
		(stroke
			(width 0.12)
			(type solid)
		)
		(fill yes)
		(layer "F.SilkS")
		(uuid "76eab8a9-718b-40f4-b144-adc04bfe56ba")
	)
	(fp_line
		(start -8.72 -6.4)
		(end -8.72 0)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "0bad68e9-ecb8-47c2-9e17-37525c8ab1ed")
	)
	(fp_line
		(start -8.72 6.4)
		(end -8.72 0)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "b458baed-41c9-4fb5-a471-85a4eb6279dd")
	)
	(fp_line
		(start -7.25 -7.25)
		(end -7.25 -6.4)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "c8297679-322c-4ff0-861f-81730d4e33ae")
	)
	(fp_line
		(start -7.25 -6.4)
		(end -8.72 -6.4)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "7324f9c8-16af-4ae7-a1f1-1f19733140a4")
	)
	(fp_line
		(start -7.25 6.4)
		(end -8.72 6.4)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "04b0e8ea-06a0-44b3-8843-79ca91177bf9")
	)
	(fp_line
		(start -7.25 7.25)
		(end -7.25 6.4)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "5d18adc5-42a9-41ca-9e2b-caa0ad3b5fc7")
	)
	(fp_line
		(start -6.4 -8.72)
		(end -6.4 -7.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "1ee165f9-1465-41b4-aeeb-233f485925f3")
	)
	(fp_line
		(start -6.4 -7.25)
		(end -7.25 -7.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "fe7f9062-d7a5-4134-8419-cabfdcf54c84")
	)
	(fp_line
		(start -6.4 7.25)
		(end -7.25 7.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "fb9796a6-c648-4b53-a582-10b30b4af6bd")
	)
	(fp_line
		(start -6.4 8.72)
		(end -6.4 7.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "71064201-b675-4d96-8310-0a9412892bf2")
	)
	(fp_line
		(start 0 -8.72)
		(end -6.4 -8.72)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "5c453f70-9f4f-44e6-b640-191254317665")
	)
	(fp_line
		(start 0 -8.72)
		(end 6.4 -8.72)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "d1331b05-5f4b-4c86-acef-f862f15ce43c")
	)
	(fp_line
		(start 0 8.72)
		(end -6.4 8.72)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "fda8b5ea-d45f-4500-bcde-e1225d3b9f97")
	)
	(fp_line
		(start 0 8.72)
		(end 6.4 8.72)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "c8a38ccf-c33a-4de1-bbf5-d1fff5c72ec9")
	)
	(fp_line
		(start 6.4 -8.72)
		(end 6.4 -7.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "291599dc-ac45-479a-8fb0-c782dd97e1c8")
	)
	(fp_line
		(start 6.4 -7.25)
		(end 7.25 -7.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "9a51e977-47c4-406f-a84f-c215e2bcbafd")
	)
	(fp_line
		(start 6.4 7.25)
		(end 7.25 7.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "f962ca92-2cff-4b17-af7c-be5012c905d5")
	)
	(fp_line
		(start 6.4 8.72)
		(end 6.4 7.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "7912e666-5fe1-40d3-9972-0b9b29ce2f79")
	)
	(fp_line
		(start 7.25 -7.25)
		(end 7.25 -6.4)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "d6ce4868-7310-44a6-b51b-3e9d815fc589")
	)
	(fp_line
		(start 7.25 -6.4)
		(end 8.72 -6.4)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "c7f496f0-772e-4009-874e-4d533de67c3d")
	)
	(fp_line
		(start 7.25 6.4)
		(end 8.72 6.4)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "7ad3f946-8f0c-46ae-b8d5-37a66ee20bf5")
	)
	(fp_line
		(start 7.25 7.25)
		(end 7.25 6.4)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "6a2dc9a5-de78-45f8-849e-32d2b413eb9b")
	)
	(fp_line
		(start 8.72 -6.4)
		(end 8.72 0)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "f3f1ebc5-c75b-456b-afd5-d5dd529164e9")
	)
	(fp_line
		(start 8.72 6.4)
		(end 8.72 0)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "8e19ef65-c9b4-44af-bc65-64985353888f")
	)
	(fp_line
		(start -7 -6)
		(end -6 -7)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "88d60a80-c612-4a8e-9085-75056512baa6")
	)
	(fp_line
		(start -7 7)
		(end -7 -6)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "7f01306c-258e-4ce3-8eff-be57bd4643b5")
	)
	(fp_line
		(start -6 -7)
		(end 7 -7)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "6aed0600-760e-4eca-8103-b91dbbf2ec92")
	)
	(fp_line
		(start 7 -7)
		(end 7 7)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "b892db04-4063-4447-bef4-3990a43dc7da")
	)
	(fp_line
		(start 7 7)
		(end -7 7)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "6350e5ab-4eac-4cf5-a21d-fdedfc355f8d")
	)
	(fp_text user "${REFERENCE}"
		(at 0 0 0)
		(layer "F.Fab")
		(uuid "b05a9955-d98a-43ac-93bc-dfcc026c62c2")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(pad "1" smd roundrect
		(at -7.675 -6)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "43c22ae4-c12e-40b8-bba6-7791cefcf778")
	)
	(pad "2" smd roundrect
		(at -7.675 -5.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "57ce5863-ebcf-4258-a39e-86b76ff6291f")
	)
	(pad "3" smd roundrect
		(at -7.675 -5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "5b8fdd6b-25cd-40c9-96ed-bacdd4760703")
	)
	(pad "4" smd roundrect
		(at -7.675 -4.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "e0b15d70-2d20-4ee4-b3e3-0fc9c0b142c9")
	)
	(pad "5" smd roundrect
		(at -7.675 -4)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "ba30dc2c-a73c-4530-8693-5a63c446346c")
	)
	(pad "6" smd roundrect
		(at -7.675 -3.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "ab8e8858-f5dd-4fb7-9f4e-a754f69ed1f5")
	)
	(pad "7" smd roundrect
		(at -7.675 -3)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "1ee2a37e-adcf-48b6-b932-3ba71ede2a9e")
	)
	(pad "8" smd roundrect
		(at -7.675 -2.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "15186198-e91c-4888-a2cf-66309f8d0200")
	)
	(pad "9" smd roundrect
		(at -7.675 -2)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "6841a822-db80-4667-81f8-ed9823d24f34")
	)
	(pad "10" smd roundrect
		(at -7.675 -1.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "e0144780-a953-4949-a8d7-33c5228c9e01")
	)
	(pad "11" smd roundrect
		(at -7.675 -1)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "d75b2f1e-c8e2-4cd9-98b8-adb5976f5a0f")
	)
	(pad "12" smd roundrect
		(at -7.675 -0.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "802df260-c489-4e40-82bf-1a633a92388e")
	)
	(pad "13" smd roundrect
		(at -7.675 0)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "f1f3007b-b097-4faa-b5af-3843e3d6c038")
	)
	(pad "14" smd roundrect
		(at -7.675 0.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "55134d39-f118-488e-b37c-6cb480a91608")
	)
	(pad "15" smd roundrect
		(at -7.675 1)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "a05f4ada-3f3f-4582-b0e7-4c632a9893a8")
	)
	(pad "16" smd roundrect
		(at -7.675 1.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "c09014e4-cc70-417f-b121-f0aed1e983a8")
	)
	(pad "17" smd roundrect
		(at -7.675 2)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "9f54170e-ee2b-4527-8c6e-d1da83b117c1")
	)
	(pad "18" smd roundrect
		(at -7.675 2.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "8a348c70-7fa2-47db-9dfb-108dac654b49")
	)
	(pad "19" smd roundrect
		(at -7.675 3)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "6210c167-7079-49c2-b9f5-7eeea9496c8e")
	)
	(pad "20" smd roundrect
		(at -7.675 3.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "1ccffedc-5f8d-414e-8473-0bccac3744e6")
	)
	(pad "21" smd roundrect
		(at -7.675 4)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "4898e100-83eb-4de6-a499-5215fffee5c6")
	)
	(pad "22" smd roundrect
		(at -7.675 4.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "fe2200ad-88ad-4936-b964-e25bc1af1780")
	)
	(pad "23" smd roundrect
		(at -7.675 5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "12539195-5104-46a6-a20a-ab79c81575c2")
	)
	(pad "24" smd roundrect
		(at -7.675 5.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "1655a836-583d-4c51-bbc6-0d67f51fbb4b")
	)
	(pad "25" smd roundrect
		(at -7.675 6)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "d9df4215-9ffb-471e-bf08-ea74fd8cf259")
	)
	(pad "26" smd roundrect
		(at -6 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "9e8ed0b7-bf92-4d5f-a7d7-165b39b6a40d")
	)
	(pad "27" smd roundrect
		(at -5.5 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "2ed32a9a-3900-48d9-a939-8c80db26a7da")
	)
	(pad "28" smd roundrect
		(at -5 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "294fa6dd-fd81-4ef5-a218-6b4abc0d9e38")
	)
	(pad "29" smd roundrect
		(at -4.5 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "7dd5234d-82fc-462a-8c53-949e7ca777b9")
	)
	(pad "30" smd roundrect
		(at -4 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "1e885430-3522-4e9d-a432-ddc0d4bcee06")
	)
	(pad "31" smd roundrect
		(at -3.5 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "b44caac1-26c4-4d72-b5e8-0066466aeb2d")
	)
	(pad "32" smd roundrect
		(at -3 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "d264e8b9-4b68-497a-a55f-9d5c3c387a63")
	)
	(pad "33" smd roundrect
		(at -2.5 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "fa3fbaf4-3ce8-43e8-ab9f-947af4a0a92b")
	)
	(pad "34" smd roundrect
		(at -2 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "cf0c74bc-0983-454c-99d4-e4731874b8b6")
	)
	(pad "35" smd roundrect
		(at -1.5 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "77572631-f02b-4c5f-b97d-ff7ccedbac12")
	)
	(pad "36" smd roundrect
		(at -1 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "2a851f86-5672-49d8-84b6-2c9c63c89c4f")
	)
	(pad "37" smd roundrect
		(at -0.5 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "8654bf2a-4b6d-4060-8bc3-3401b7df0e86")
	)
	(pad "38" smd roundrect
		(at 0 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "e7647dbc-a325-4d7e-8c9d-d867f1ced002")
	)
	(pad "39" smd roundrect
		(at 0.5 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "2f41dc2b-6542-430a-b2f5-a9f39e3e379e")
	)
	(pad "40" smd roundrect
		(at 1 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "c09d8930-062a-47fc-91de-b76cf6b38e92")
	)
	(pad "41" smd roundrect
		(at 1.5 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "1c739f9e-b3a6-4240-9c19-f45fabfcaf04")
	)
	(pad "42" smd roundrect
		(at 2 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "e256beb2-d280-400d-b4a8-d70cd499881f")
	)
	(pad "43" smd roundrect
		(at 2.5 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "8fdf2e15-906e-47ae-9788-c2903c40d723")
	)
	(pad "44" smd roundrect
		(at 3 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "46c24950-afdd-493b-98c4-3250d689ff83")
	)
	(pad "45" smd roundrect
		(at 3.5 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "de45edf2-8b85-4af3-b501-fdf86a7e03d9")
	)
	(pad "46" smd roundrect
		(at 4 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "335a1b85-51e4-439a-9668-1ba37f4282c7")
	)
	(pad "47" smd roundrect
		(at 4.5 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "92f7ccb9-fbfa-49f2-8b18-a2760d525a68")
	)
	(pad "48" smd roundrect
		(at 5 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "6af091af-b665-457b-bfc3-f6cdbcf8cfe4")
	)
	(pad "49" smd roundrect
		(at 5.5 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "c087766e-145c-4c98-a13e-3d4475122ffd")
	)
	(pad "50" smd roundrect
		(at 6 7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "9d6d615d-e4ee-4d15-9a0f-05067f6a5884")
	)
	(pad "51" smd roundrect
		(at 7.675 6)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "45059dc6-65e0-4a52-b0de-e1415ded72ef")
	)
	(pad "52" smd roundrect
		(at 7.675 5.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "21ef8d72-76ee-4fcb-960d-4777a6ccc35a")
	)
	(pad "53" smd roundrect
		(at 7.675 5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "d15ad337-50ae-40f2-a7dd-0313cb6ff289")
	)
	(pad "54" smd roundrect
		(at 7.675 4.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "d461db29-9f41-46d8-a87d-93123ff7e17e")
	)
	(pad "55" smd roundrect
		(at 7.675 4)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "538752bb-c61a-45f5-979c-5170e2635af4")
	)
	(pad "56" smd roundrect
		(at 7.675 3.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "bf8489fe-00be-44af-a842-24a64b7e576f")
	)
	(pad "57" smd roundrect
		(at 7.675 3)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "bd730e0b-22a2-4e7b-b385-42a090abcf7b")
	)
	(pad "58" smd roundrect
		(at 7.675 2.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "814c0626-9308-4fcb-a50d-3fc6a0f583f0")
	)
	(pad "59" smd roundrect
		(at 7.675 2)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "62b6dc1b-7b4d-4758-983a-8503c6891a58")
	)
	(pad "60" smd roundrect
		(at 7.675 1.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "d4e90468-09f1-4ee8-b3a1-3099330d1ea2")
	)
	(pad "61" smd roundrect
		(at 7.675 1)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "e6a1707e-4f6e-46a3-9014-f7ba66a9bb06")
	)
	(pad "62" smd roundrect
		(at 7.675 0.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "e2974dc6-297a-42d4-83f2-2079757ad387")
	)
	(pad "63" smd roundrect
		(at 7.675 0)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "c4e5b8db-842c-4d0f-a099-0ade555109c5")
	)
	(pad "64" smd roundrect
		(at 7.675 -0.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "7f9df482-c629-4eb1-ba11-eb875866a373")
	)
	(pad "65" smd roundrect
		(at 7.675 -1)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "0b30e621-8422-4bfe-ab2d-f278d85c74c9")
	)
	(pad "66" smd roundrect
		(at 7.675 -1.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "3ff2d150-fde3-4f22-b42b-1732cd014409")
	)
	(pad "67" smd roundrect
		(at 7.675 -2)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "64fd6f9d-9dfb-4e53-8445-2e128e20c6c0")
	)
	(pad "68" smd roundrect
		(at 7.675 -2.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "c8b1b36f-fb13-48d7-919c-b9026eaedac0")
	)
	(pad "69" smd roundrect
		(at 7.675 -3)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "9736d20c-6846-4efc-a936-02555d2ffb5b")
	)
	(pad "70" smd roundrect
		(at 7.675 -3.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "5d065864-17f1-452d-ae1d-f15a6ba6e561")
	)
	(pad "71" smd roundrect
		(at 7.675 -4)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "f350a07f-cece-4edd-8ade-3c6076b9d6f2")
	)
	(pad "72" smd roundrect
		(at 7.675 -4.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "6c53f45e-ef78-424b-ba4a-375c39d6817c")
	)
	(pad "73" smd roundrect
		(at 7.675 -5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "d56181a9-f01d-4a28-8e78-e7418dcd4f3f")
	)
	(pad "74" smd roundrect
		(at 7.675 -5.5)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "31cf8a9e-c311-4acb-8860-bce08946940e")
	)
	(pad "75" smd roundrect
		(at 7.675 -6)
		(size 1.6 0.3)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "b61140d0-22f9-44a4-889c-940d49ac07f6")
	)
	(pad "76" smd roundrect
		(at 6 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "fb2ba217-b8f2-4841-96b4-16597de78c4c")
	)
	(pad "77" smd roundrect
		(at 5.5 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "a913381f-8844-4b0a-a164-fb3f5b9f3430")
	)
	(pad "78" smd roundrect
		(at 5 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "69e63d0d-6abf-438b-8c11-33889be5c30b")
	)
	(pad "79" smd roundrect
		(at 4.5 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "58b9cee9-11cf-461d-9db8-0d9fda9da042")
	)
	(pad "80" smd roundrect
		(at 4 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "545987bd-adcc-4d00-98a8-7723f52c70d9")
	)
	(pad "81" smd roundrect
		(at 3.5 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "8fb9e560-358f-4c73-abe0-6baa036d88ca")
	)
	(pad "82" smd roundrect
		(at 3 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "feb8d079-1a33-4115-8285-59c8bd5f0b1f")
	)
	(pad "83" smd roundrect
		(at 2.5 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "31314e6b-3924-4f44-adb4-a26300accb29")
	)
	(pad "84" smd roundrect
		(at 2 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "bc8d98bc-a6f6-4830-940f-7e42ec5331e5")
	)
	(pad "85" smd roundrect
		(at 1.5 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "0b13942f-e974-4b90-936d-29cb0523811c")
	)
	(pad "86" smd roundrect
		(at 1 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "53984115-88f3-4a17-a449-64050ea6531c")
	)
	(pad "87" smd roundrect
		(at 0.5 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "8802df1c-9cdb-49e0-adc5-321c41fe060e")
	)
	(pad "88" smd roundrect
		(at 0 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "db0d1b6e-a91a-44c2-82ef-2d713665dcb9")
	)
	(pad "89" smd roundrect
		(at -0.5 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "19d8d435-bb55-4103-b53c-67b5c7642e92")
	)
	(pad "90" smd roundrect
		(at -1 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "95a7f955-aff8-4620-adb9-e61436fe27da")
	)
	(pad "91" smd roundrect
		(at -1.5 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "ad9c16e1-d66f-4a47-aa3d-4ef884d6092c")
	)
	(pad "92" smd roundrect
		(at -2 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "77131253-01ea-405b-97bf-9eb8e4280036")
	)
	(pad "93" smd roundrect
		(at -2.5 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "d7204b2d-0e2c-4dc3-8b59-35741782edfd")
	)
	(pad "94" smd roundrect
		(at -3 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "95024829-6d35-4f61-9495-09326cab743f")
	)
	(pad "95" smd roundrect
		(at -3.5 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "d072f0e3-ce12-44cb-a9a6-146cc6238ede")
	)
	(pad "96" smd roundrect
		(at -4 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "59be0242-bea2-409e-a4cd-fda538a36a07")
	)
	(pad "97" smd roundrect
		(at -4.5 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "2fd52f5c-1f0e-445c-874c-e7b69e359027")
	)
	(pad "98" smd roundrect
		(at -5 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "bce30f11-a2ab-4858-abc6-54b5b1b4d3da")
	)
	(pad "99" smd roundrect
		(at -5.5 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "749e7ba4-0e9f-48a5-b824-2b9c8552c4f1")
	)
	(pad "100" smd roundrect
		(at -6 -7.675)
		(size 0.3 1.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "97ab3c56-b17b-43a5-8c2c-193332af7e7b")
	)
	(embedded_fonts no)
	(model "${KICAD8_3DMODEL_DIR}/Package_QFP.3dshapes/LQFP-100_14x14mm_P0.5mm.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
