<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<wxFormBuilder_Project>
    <FileVersion major="1" minor="16" />
    <object class="Project" expanded="1">
        <property name="class_decoration"></property>
        <property name="code_generation">C++</property>
        <property name="disconnect_events">1</property>
        <property name="disconnect_mode">source_name</property>
        <property name="disconnect_php_events">0</property>
        <property name="disconnect_python_events">0</property>
        <property name="embedded_files_path">res</property>
        <property name="encoding">UTF-8</property>
        <property name="event_generation">connect</property>
        <property name="file">dialog_display_html_text_base</property>
        <property name="first_id">1000</property>
        <property name="help_provider">none</property>
        <property name="image_path_wrapper_function_name"></property>
        <property name="indent_with_spaces"></property>
        <property name="internationalize">1</property>
        <property name="name">dialog_display_html_text_base</property>
        <property name="namespace"></property>
        <property name="path">.</property>
        <property name="precompiled_header"></property>
        <property name="relative_path">1</property>
        <property name="skip_lua_events">1</property>
        <property name="skip_php_events">1</property>
        <property name="skip_python_events">1</property>
        <property name="ui_table">UI</property>
        <property name="use_array_enum">0</property>
        <property name="use_enum">0</property>
        <property name="use_microsoft_bom">0</property>
        <object class="Dialog" expanded="1">
            <property name="aui_managed">0</property>
            <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
            <property name="bg"></property>
            <property name="center"></property>
            <property name="context_help"></property>
            <property name="context_menu">1</property>
            <property name="enabled">1</property>
            <property name="event_handler">impl_virtual</property>
            <property name="extra_style"></property>
            <property name="fg"></property>
            <property name="font"></property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="maximum_size"></property>
            <property name="minimum_size">500,300</property>
            <property name="name">DIALOG_DISPLAY_HTML_TEXT_BASE</property>
            <property name="pos"></property>
            <property name="size">-1,-1</property>
            <property name="style">wxDEFAULT_DIALOG_STYLE|wxRESIZE_BORDER</property>
            <property name="subclass">DIALOG_SHIM; dialog_shim.h</property>
            <property name="title"></property>
            <property name="tooltip"></property>
            <property name="two_step_creation">0</property>
            <property name="window_extra_style"></property>
            <property name="window_name"></property>
            <property name="window_style"></property>
            <object class="wxBoxSizer" expanded="1">
                <property name="minimum_size">540,240</property>
                <property name="name">bMainSizer</property>
                <property name="orient">wxVERTICAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="0">
                    <property name="border">10</property>
                    <property name="flag">wxALL|wxEXPAND</property>
                    <property name="proportion">1</property>
                    <object class="wxHtmlWindow" expanded="0">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size">-1,-1</property>
                        <property name="moveable">1</property>
                        <property name="name">m_htmlWindow</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style">wxHW_SCROLLBAR_AUTO</property>
                        <property name="subclass">HTML_WINDOW; widgets/html_window.h; </property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnHtmlLinkClicked">OnHTMLLinkClicked</event>
                    </object>
                </object>
                <object class="sizeritem" expanded="1">
                    <property name="border">5</property>
                    <property name="flag">wxEXPAND|wxBOTTOM|wxRIGHT</property>
                    <property name="proportion">0</property>
                    <object class="wxStdDialogButtonSizer" expanded="1">
                        <property name="Apply">0</property>
                        <property name="Cancel">0</property>
                        <property name="ContextHelp">0</property>
                        <property name="Help">0</property>
                        <property name="No">0</property>
                        <property name="OK">1</property>
                        <property name="Save">0</property>
                        <property name="Yes">0</property>
                        <property name="minimum_size"></property>
                        <property name="name">m_sdbSizer1</property>
                        <property name="permission">protected</property>
                    </object>
                </object>
            </object>
        </object>
    </object>
</wxFormBuilder_Project>
