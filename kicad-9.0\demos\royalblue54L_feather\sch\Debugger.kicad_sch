(kicad_sch
	(version 20250114)
	(generator "eeschema")
	(generator_version "9.0")
	(uuid "6127e733-3ec0-48e6-87c3-67e6a4ebd9df")
	(paper "A4")
	(title_block
		(title "RoyalBlue54L")
		(date "2025-01-25")
		(rev "0.0.1")
		(company "Lord's Boards")
	)
	(lib_symbols
		(symbol "Connector:Conn_ARM_SWD_TagConnect_TC2030-NL"
			(exclude_from_sim no)
			(in_bom no)
			(on_board yes)
			(property "Reference" "J"
				(at 2.54 11.43 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "Conn_ARM_SWD_TagConnect_TC2030-NL"
				(at 2.54 8.89 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" "Connector:Tag-Connect_TC2030-IDC-NL_2x03_P1.27mm_Vertical"
				(at 0 -17.78 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "https://www.tag-connect.com/wp-content/uploads/bsk-pdf-manager/TC2030-CTX_1.pdf"
				(at 0 -15.24 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Tag-Connect ARM Cortex SWD JTAG connector, 6 pin, no legs"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "Cortex Debug Connector ARM SWD JTAG"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "*TC2030*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Conn_ARM_SWD_TagConnect_TC2030-NL_0_0"
				(pin power_in line
					(at -2.54 10.16 270)
					(length 2.54)
					(name "VCC"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at -2.54 -10.16 90)
					(length 2.54)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin open_collector line
					(at 7.62 5.08 180)
					(length 2.54)
					(name "~{RESET}"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin output line
					(at 7.62 0 180)
					(length 2.54)
					(name "SWCLK"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(alternate "TCK" output line)
				)
				(pin bidirectional line
					(at 7.62 -2.54 180)
					(length 2.54)
					(name "SWDIO"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(alternate "TMS" bidirectional line)
				)
				(pin input line
					(at 7.62 -5.08 180)
					(length 2.54)
					(name "SWO"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(alternate "TDO" input line)
				)
			)
			(symbol "Conn_ARM_SWD_TagConnect_TC2030-NL_0_1"
				(rectangle
					(start -5.08 7.62)
					(end 5.08 -7.62)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Connector:TestPoint_Small"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.762)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "TP"
				(at 0 3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "TestPoint_Small"
				(at 0 2.032 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 5.08 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 5.08 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "test point"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "test point tp"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Pin* Test*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "TestPoint_Small_0_1"
				(circle
					(center 0 0)
					(radius 0.508)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "TestPoint_Small_1_1"
				(pin passive line
					(at 0 0 90)
					(length 0)
					(name "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Connector:USB_C_Receptacle_USB2.0_16P"
			(pin_names
				(offset 1.016)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "J"
				(at 0 22.225 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "USB_C_Receptacle_USB2.0_16P"
				(at 0 19.685 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 3.81 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "https://www.usb.org/sites/default/files/documents/usb_type-c.zip"
				(at 3.81 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "USB 2.0-only 16P Type-C Receptacle connector"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "usb universal serial bus type-C USB2.0"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "USB*C*Receptacle*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "USB_C_Receptacle_USB2.0_16P_0_0"
				(rectangle
					(start -0.254 -17.78)
					(end 0.254 -16.764)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 10.16 15.494)
					(end 9.144 14.986)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 10.16 10.414)
					(end 9.144 9.906)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 10.16 7.874)
					(end 9.144 7.366)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 10.16 2.794)
					(end 9.144 2.286)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 10.16 0.254)
					(end 9.144 -0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 10.16 -2.286)
					(end 9.144 -2.794)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 10.16 -4.826)
					(end 9.144 -5.334)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 10.16 -12.446)
					(end 9.144 -12.954)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 10.16 -14.986)
					(end 9.144 -15.494)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "USB_C_Receptacle_USB2.0_16P_0_1"
				(rectangle
					(start -10.16 17.78)
					(end 10.16 -17.78)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(polyline
					(pts
						(xy -8.89 -3.81) (xy -8.89 3.81)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -7.62 -3.81)
					(end -6.35 3.81)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(arc
					(start -7.62 3.81)
					(mid -6.985 4.4423)
					(end -6.35 3.81)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -7.62 3.81)
					(mid -6.985 4.4423)
					(end -6.35 3.81)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(arc
					(start -8.89 3.81)
					(mid -6.985 5.7067)
					(end -5.08 3.81)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -5.08 -3.81)
					(mid -6.985 -5.7067)
					(end -8.89 -3.81)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -6.35 -3.81)
					(mid -6.985 -4.4423)
					(end -7.62 -3.81)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -6.35 -3.81)
					(mid -6.985 -4.4423)
					(end -7.62 -3.81)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -5.08 3.81) (xy -5.08 -3.81)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center -2.54 1.143)
					(radius 0.635)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -1.27 4.318) (xy 0 6.858) (xy 1.27 4.318) (xy -1.27 4.318)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0 -2.032) (xy 2.54 0.508) (xy 2.54 1.778)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 -3.302) (xy -2.54 -0.762) (xy -2.54 0.508)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 -5.842) (xy 0 4.318)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 -5.842)
					(radius 1.27)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 1.905 1.778)
					(end 3.175 3.048)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type outline)
					)
				)
			)
			(symbol "USB_C_Receptacle_USB2.0_16P_1_1"
				(pin passive line
					(at -7.62 -22.86 90)
					(length 5.08)
					(name "SHIELD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "S1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -22.86 90)
					(length 5.08)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "A1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -22.86 90)
					(length 5.08)
					(hide yes)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "A12"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -22.86 90)
					(length 5.08)
					(hide yes)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -22.86 90)
					(length 5.08)
					(hide yes)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B12"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 15.24 15.24 180)
					(length 5.08)
					(name "VBUS"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "A4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 15.24 15.24 180)
					(length 5.08)
					(hide yes)
					(name "VBUS"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "A9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 15.24 15.24 180)
					(length 5.08)
					(hide yes)
					(name "VBUS"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 15.24 15.24 180)
					(length 5.08)
					(hide yes)
					(name "VBUS"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 15.24 10.16 180)
					(length 5.08)
					(name "CC1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "A5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 15.24 7.62 180)
					(length 5.08)
					(name "CC2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 15.24 2.54 180)
					(length 5.08)
					(name "D-"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "A7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 15.24 0 180)
					(length 5.08)
					(name "D-"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 15.24 -2.54 180)
					(length 5.08)
					(name "D+"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "A6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 15.24 -5.08 180)
					(length 5.08)
					(name "D+"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 15.24 -12.7 180)
					(length 5.08)
					(name "SBU1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "A8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 15.24 -15.24 180)
					(length 5.08)
					(name "SBU2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Device:C_Small"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.254)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "C"
				(at 0.254 1.778 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "C_Small"
				(at 0.254 -2.032 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Unpolarized capacitor, small symbol"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "capacitor cap"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "C_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "C_Small_0_1"
				(polyline
					(pts
						(xy -1.524 0.508) (xy 1.524 0.508)
					)
					(stroke
						(width 0.3048)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.524 -0.508) (xy 1.524 -0.508)
					)
					(stroke
						(width 0.3302)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "C_Small_1_1"
				(pin passive line
					(at 0 2.54 270)
					(length 2.032)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -2.54 90)
					(length 2.032)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Device:R_Small_US"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.254)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "R"
				(at 0.762 0.508 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "R_Small_US"
				(at 0.762 -1.016 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Resistor, small US symbol"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "r resistor"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "R_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "R_Small_US_1_1"
				(polyline
					(pts
						(xy 0 1.524) (xy 1.016 1.143) (xy 0 0.762) (xy -1.016 0.381) (xy 0 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 1.016 -0.381) (xy 0 -0.762) (xy -1.016 -1.143) (xy 0 -1.524)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(pin passive line
					(at 0 2.54 270)
					(length 1.016)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -2.54 90)
					(length 1.016)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Graphic:Logo_Open_Hardware_Small"
			(exclude_from_sim no)
			(in_bom no)
			(on_board no)
			(property "Reference" "#SYM"
				(at 0 6.985 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "Logo_Open_Hardware_Small"
				(at 0 -5.715 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Open Hardware logo, small"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Enable" "0"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "Logo"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Logo_Open_Hardware_Small_0_1"
				(polyline
					(pts
						(xy 3.3528 -4.3434) (xy 3.302 -4.318) (xy 3.175 -4.2418) (xy 2.9972 -4.1148) (xy 2.7686 -3.9624)
						(xy 2.54 -3.81) (xy 2.3622 -3.7084) (xy 2.2352 -3.6068) (xy 2.1844 -3.5814) (xy 2.159 -3.6068)
						(xy 2.0574 -3.6576) (xy 1.905 -3.7338) (xy 1.8034 -3.7846) (xy 1.6764 -3.8354) (xy 1.6002 -3.8354)
						(xy 1.6002 -3.8354) (xy 1.5494 -3.7338) (xy 1.4732 -3.5306) (xy 1.3462 -3.302) (xy 1.2446 -3.0226)
						(xy 1.1176 -2.7178) (xy 0.9652 -2.413) (xy 0.8636 -2.1082) (xy 0.7366 -1.8288) (xy 0.6604 -1.6256)
						(xy 0.6096 -1.4732) (xy 0.5842 -1.397) (xy 0.5842 -1.397) (xy 0.6604 -1.3208) (xy 0.7874 -1.2446)
						(xy 1.0414 -1.016) (xy 1.2954 -0.6858) (xy 1.4478 -0.3302) (xy 1.524 0.0762) (xy 1.4732 0.4572)
						(xy 1.3208 0.8128) (xy 1.0668 1.143) (xy 0.762 1.3716) (xy 0.4064 1.524) (xy 0 1.5748) (xy -0.381 1.5494)
						(xy -0.7366 1.397) (xy -1.0668 1.143) (xy -1.2192 0.9906) (xy -1.397 0.6604) (xy -1.524 0.3048)
						(xy -1.524 0.2286) (xy -1.4986 -0.1778) (xy -1.397 -0.5334) (xy -1.1938 -0.8636) (xy -0.9144 -1.143)
						(xy -0.8636 -1.1684) (xy -0.7366 -1.27) (xy -0.635 -1.3462) (xy -0.5842 -1.397) (xy -1.0668 -2.5908)
						(xy -1.143 -2.794) (xy -1.2954 -3.1242) (xy -1.397 -3.4036) (xy -1.4986 -3.6322) (xy -1.5748 -3.7846)
						(xy -1.6002 -3.8354) (xy -1.6002 -3.8354) (xy -1.651 -3.8354) (xy -1.7272 -3.81) (xy -1.905 -3.7338)
						(xy -2.0066 -3.683) (xy -2.1336 -3.6068) (xy -2.2098 -3.5814) (xy -2.2606 -3.6068) (xy -2.3622 -3.683)
						(xy -2.54 -3.81) (xy -2.7686 -3.9624) (xy -2.9718 -4.0894) (xy -3.1496 -4.2164) (xy -3.302 -4.318)
						(xy -3.3528 -4.3434) (xy -3.3782 -4.3434) (xy -3.429 -4.318) (xy -3.5306 -4.2164) (xy -3.7084 -4.064)
						(xy -3.937 -3.8354) (xy -3.9624 -3.81) (xy -4.1656 -3.6068) (xy -4.318 -3.4544) (xy -4.4196 -3.3274)
						(xy -4.445 -3.2766) (xy -4.445 -3.2766) (xy -4.4196 -3.2258) (xy -4.318 -3.0734) (xy -4.2164 -2.8956)
						(xy -4.064 -2.667) (xy -3.6576 -2.0828) (xy -3.8862 -1.5494) (xy -3.937 -1.3716) (xy -4.0386 -1.1684)
						(xy -4.0894 -1.0414) (xy -4.1148 -0.9652) (xy -4.191 -0.9398) (xy -4.318 -0.9144) (xy -4.5466 -0.8636)
						(xy -4.8006 -0.8128) (xy -5.0546 -0.7874) (xy -5.2578 -0.7366) (xy -5.4356 -0.7112) (xy -5.5118 -0.6858)
						(xy -5.5118 -0.6858) (xy -5.5372 -0.635) (xy -5.5372 -0.5588) (xy -5.5372 -0.4318) (xy -5.5626 -0.2286)
						(xy -5.5626 0.0762) (xy -5.5626 0.127) (xy -5.5372 0.4064) (xy -5.5372 0.635) (xy -5.5372 0.762)
						(xy -5.5372 0.8382) (xy -5.5372 0.8382) (xy -5.461 0.8382) (xy -5.3086 0.889) (xy -5.08 0.9144)
						(xy -4.826 0.9652) (xy -4.8006 0.9906) (xy -4.5466 1.0414) (xy -4.318 1.0668) (xy -4.1656 1.1176)
						(xy -4.0894 1.143) (xy -4.0894 1.143) (xy -4.0386 1.2446) (xy -3.9624 1.4224) (xy -3.8608 1.6256)
						(xy -3.7846 1.8288) (xy -3.7084 2.0066) (xy -3.6576 2.159) (xy -3.6322 2.2098) (xy -3.6322 2.2098)
						(xy -3.683 2.286) (xy -3.7592 2.413) (xy -3.8862 2.5908) (xy -4.064 2.8194) (xy -4.064 2.8448)
						(xy -4.2164 3.0734) (xy -4.3434 3.2512) (xy -4.4196 3.3782) (xy -4.445 3.4544) (xy -4.445 3.4544)
						(xy -4.3942 3.5052) (xy -4.2926 3.6322) (xy -4.1148 3.81) (xy -3.937 4.0132) (xy -3.8608 4.064)
						(xy -3.6576 4.2926) (xy -3.5052 4.4196) (xy -3.4036 4.4958) (xy -3.3528 4.5212) (xy -3.3528 4.5212)
						(xy -3.302 4.4704) (xy -3.1496 4.3688) (xy -2.9718 4.2418) (xy -2.7432 4.0894) (xy -2.7178 4.0894)
						(xy -2.4892 3.937) (xy -2.3114 3.81) (xy -2.1844 3.7084) (xy -2.1336 3.683) (xy -2.1082 3.683)
						(xy -2.032 3.7084) (xy -1.8542 3.7592) (xy -1.6764 3.8354) (xy -1.4732 3.937) (xy -1.27 4.0132)
						(xy -1.143 4.064) (xy -1.0668 4.1148) (xy -1.0668 4.1148) (xy -1.0414 4.191) (xy -1.016 4.3434)
						(xy -0.9652 4.572) (xy -0.9144 4.8514) (xy -0.889 4.9022) (xy -0.8382 5.1562) (xy -0.8128 5.3848)
						(xy -0.7874 5.5372) (xy -0.762 5.588) (xy -0.7112 5.6134) (xy -0.5842 5.6134) (xy -0.4064 5.6134)
						(xy -0.1524 5.6134) (xy 0.0762 5.6134) (xy 0.3302 5.6134) (xy 0.5334 5.6134) (xy 0.6858 5.588)
						(xy 0.7366 5.588) (xy 0.7366 5.588) (xy 0.762 5.5118) (xy 0.8128 5.334) (xy 0.8382 5.1054) (xy 0.9144 4.826)
						(xy 0.9144 4.7752) (xy 0.9652 4.5212) (xy 1.016 4.2926) (xy 1.0414 4.1402) (xy 1.0668 4.0894)
						(xy 1.0668 4.0894) (xy 1.1938 4.0386) (xy 1.3716 3.9624) (xy 1.5748 3.8608) (xy 2.0828 3.6576)
						(xy 2.7178 4.0894) (xy 2.7686 4.1402) (xy 2.9972 4.2926) (xy 3.175 4.4196) (xy 3.302 4.4958) (xy 3.3782 4.5212)
						(xy 3.3782 4.5212) (xy 3.429 4.4704) (xy 3.556 4.3434) (xy 3.7338 4.191) (xy 3.9116 3.9878) (xy 4.064 3.8354)
						(xy 4.2418 3.6576) (xy 4.3434 3.556) (xy 4.4196 3.4798) (xy 4.4196 3.429) (xy 4.4196 3.4036) (xy 4.3942 3.3274)
						(xy 4.2926 3.2004) (xy 4.1656 2.9972) (xy 4.0132 2.794) (xy 3.8862 2.5908) (xy 3.7592 2.3876)
						(xy 3.6576 2.2352) (xy 3.6322 2.159) (xy 3.6322 2.1336) (xy 3.683 2.0066) (xy 3.7592 1.8288) (xy 3.8608 1.6002)
						(xy 4.064 1.1176) (xy 4.3942 1.0414) (xy 4.5974 1.016) (xy 4.8768 0.9652) (xy 5.1308 0.9144) (xy 5.5372 0.8382)
						(xy 5.5626 -0.6604) (xy 5.4864 -0.6858) (xy 5.4356 -0.6858) (xy 5.2832 -0.7366) (xy 5.0546 -0.762)
						(xy 4.8006 -0.8128) (xy 4.5974 -0.8636) (xy 4.3688 -0.9144) (xy 4.2164 -0.9398) (xy 4.1402 -0.9398)
						(xy 4.1148 -0.9652) (xy 4.064 -1.0668) (xy 3.9878 -1.2446) (xy 3.9116 -1.4478) (xy 3.81 -1.651)
						(xy 3.7338 -1.8542) (xy 3.683 -2.0066) (xy 3.6576 -2.0828) (xy 3.683 -2.1336) (xy 3.7846 -2.2606)
						(xy 3.8862 -2.4638) (xy 4.0386 -2.667) (xy 4.191 -2.8956) (xy 4.318 -3.0734) (xy 4.3942 -3.2004)
						(xy 4.445 -3.2766) (xy 4.4196 -3.3274) (xy 4.3434 -3.429) (xy 4.1656 -3.5814) (xy 3.937 -3.8354)
						(xy 3.8862 -3.8608) (xy 3.683 -4.064) (xy 3.5306 -4.2164) (xy 3.4036 -4.318) (xy 3.3528 -4.3434)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Logic_LevelTranslator:TXB0106RGY"
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at -6.35 13.97 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "TXB0106RGY"
				(at 3.81 13.97 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" "Package_DFN_QFN:Texas_RGY_R-PVQFN-N16_EP2.05x2.55mm"
				(at 0 -19.05 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "http://www.ti.com/lit/ds/symlink/txb0106.pdf"
				(at 0 -5.08 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "6-Bit Bidirectional Voltage-Level Translator, Auto Direction Sensing and ±15-kV ESD Protection, 1.2 - 3.6V APort, 1.65 - 5.5V BPort, VQFN-16"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "bidirectional voltage level translator"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "*RGY*PVQFN*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "TXB0106RGY_0_1"
				(rectangle
					(start -7.62 12.7)
					(end 7.62 -12.7)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(polyline
					(pts
						(xy -2.54 0) (xy -2.54 1.016) (xy -0.762 1.016)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.762 -1.016) (xy -2.54 -1.016) (xy -2.54 0) (xy -4.572 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.762 -1.016) (xy 1.016 0) (xy 1.016 -2.032) (xy -0.762 -1.016)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.016 1.016) (xy -0.762 0) (xy -0.762 2.032) (xy 1.016 1.016)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.016 1.016) (xy 2.794 1.016) (xy 2.794 0) (xy 4.064 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 2.794 0) (xy 2.794 -1.016) (xy 1.016 -1.016)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "TXB0106RGY_1_1"
				(pin input line
					(at -10.16 7.62 0)
					(length 2.54)
					(name "OE"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -10.16 5.08 0)
					(length 2.54)
					(name "A1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -10.16 2.54 0)
					(length 2.54)
					(name "A2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -10.16 0 0)
					(length 2.54)
					(name "A3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -10.16 -2.54 0)
					(length 2.54)
					(name "A4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -10.16 -5.08 0)
					(length 2.54)
					(name "A5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -10.16 -7.62 0)
					(length 2.54)
					(name "A6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at -2.54 15.24 270)
					(length 2.54)
					(name "V_{CCA}"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -15.24 90)
					(length 2.54)
					(hide yes)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "17"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 0 -15.24 90)
					(length 2.54)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 2.54 15.24 270)
					(length 2.54)
					(name "V_{CCB}"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "15"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 10.16 5.08 180)
					(length 2.54)
					(name "B1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "16"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 10.16 2.54 180)
					(length 2.54)
					(name "B2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "14"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 10.16 0 180)
					(length 2.54)
					(name "B3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "13"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 10.16 -2.54 180)
					(length 2.54)
					(name "B4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "12"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 10.16 -5.08 180)
					(length 2.54)
					(name "B5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "11"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 10.16 -7.62 180)
					(length 2.54)
					(name "B6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "10"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "LordsBoards-Graphic:LordsBoardsLogo"
			(exclude_from_sim no)
			(in_bom no)
			(on_board no)
			(property "Reference" "#SYM"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "LordsBoardsLogo_1_0"
				(polyline
					(pts
						(xy -5.5268 4.7992) (xy -5.473 4.7941) (xy -5.4197 4.7851) (xy -5.3671 4.7724) (xy -5.3154 4.7559)
						(xy -5.2647 4.7357) (xy -5.2152 4.7117) (xy -5.1671 4.6841) (xy -5.1206 4.6528) (xy -5.0759 4.6179)
						(xy -2.4774 2.4374) (xy -2.4704 2.4318) (xy -2.4632 2.4265) (xy -2.4558 2.4216) (xy -2.4483 2.4171)
						(xy -2.4406 2.413) (xy -2.4328 2.4092) (xy -2.4249 2.4058) (xy -2.4169 2.4028) (xy -2.4089 2.4001)
						(xy -2.4007 2.3978) (xy -2.3924 2.3959) (xy -2.3841 2.3943) (xy -2.3758 2.3931) (xy -2.3674 2.3923)
						(xy -2.359 2.3918) (xy -2.3506 2.3918) (xy -2.3422 2.392) (xy -2.3338 2.3927) (xy -2.3255 2.3937)
						(xy -2.3172 2.3951) (xy -2.3089 2.3968) (xy -2.3007 2.3989) (xy -2.2926 2.4014) (xy -2.2846 2.4042)
						(xy -2.2766 2.4074) (xy -2.2688 2.411) (xy -2.2611 2.4149) (xy -2.2536 2.4192) (xy -2.2462 2.4239)
						(xy -2.239 2.4289) (xy -2.2319 2.4343) (xy -2.225 2.4401) (xy -0.5157 3.9374) (xy -0.488 3.9606)
						(xy -0.4595 3.9822) (xy -0.4303 4.0024) (xy -0.4003 4.021) (xy -0.3697 4.0382) (xy -0.3385 4.0539)
						(xy -0.3068 4.0681) (xy -0.2745 4.0808) (xy -0.2418 4.092) (xy -0.2088 4.1018) (xy -0.1754 4.11)
						(xy -0.1418 4.1168) (xy -0.1079 4.122) (xy -0.0739 4.1258) (xy -0.0397 4.1281) (xy -0.0055 4.1288)
						(xy 0.0287 4.1281) (xy 0.0628 4.1259) (xy 0.0969 4.1223) (xy 0.1307 4.1171) (xy 0.1644 4.1104)
						(xy 0.1978 4.1023) (xy 0.2309 4.0926) (xy 0.2636 4.0815) (xy 0.2958 4.0688) (xy 0.3276 4.0547)
						(xy 0.3589 4.0391) (xy 0.3895 4.022) (xy 0.4195 4.0034) (xy 0.4488 3.9833) (xy 0.4774 3.9617)
						(xy 0.5051 3.9387) (xy 2.2245 2.4392) (xy 2.2314 2.4335) (xy 2.2385 2.4281) (xy 2.2457 2.4231)
						(xy 2.2531 2.4184) (xy 2.2607 2.4141) (xy 2.2684 2.4102) (xy 2.2762 2.4066) (xy 2.2841 2.4034)
						(xy 2.2921 2.4006) (xy 2.3003 2.3982) (xy 2.3085 2.3961) (xy 2.3167 2.3943) (xy 2.325 2.393) (xy 2.3334 2.392)
						(xy 2.3417 2.3914) (xy 2.3501 2.3911) (xy 2.3585 2.3912) (xy 2.3669 2.3917) (xy 2.3753 2.3925)
						(xy 2.3836 2.3937) (xy 2.3919 2.3953) (xy 2.4001 2.3972) (xy 2.4082 2.3995) (xy 2.4163 2.4022)
						(xy 2.4243 2.4052) (xy 2.4322 2.4087) (xy 2.4399 2.4124) (xy 2.4475 2.4166) (xy 2.455 2.4211)
						(xy 2.4624 2.4259) (xy 2.4695 2.4312) (xy 2.4765 2.4368) (xy 5.0759 4.6179) (xy 5.1206 4.6528)
						(xy 5.1671 4.6841) (xy 5.2152 4.7117) (xy 5.2647 4.7356) (xy 5.3154 4.7558) (xy 5.3671 4.7723)
						(xy 5.4197 4.7851) (xy 5.473 4.794) (xy 5.5268 4.7992) (xy 5.5809 4.8005) (xy 5.6351 4.798) (xy 5.6893 4.7916)
						(xy 5.7433 4.7813) (xy 5.7969 4.7671) (xy 5.8499 4.7489) (xy 5.9021 4.7268) (xy 5.9527 4.701)
						(xy 6.0007 4.6721) (xy 6.046 4.6402) (xy 6.0886 4.6054) (xy 6.1284 4.568) (xy 6.1652 4.5281) (xy 6.199 4.4858)
						(xy 6.2296 4.4413) (xy 6.257 4.3947) (xy 6.2811 4.3462) (xy 6.3017 4.296) (xy 6.3189 4.2441) (xy 6.3323 4.1908)
						(xy 6.3421 4.1362) (xy 6.348 4.0805) (xy 6.35 4.0238) (xy 6.35 -4.0327) (xy 6.349 -4.0725) (xy 6.346 -4.1118)
						(xy 6.341 -4.1506) (xy 6.3342 -4.1888) (xy 6.3255 -4.2263) (xy 6.3151 -4.2631) (xy 6.3029 -4.2991)
						(xy 6.289 -4.3343) (xy 6.2734 -4.3686) (xy 6.2563 -4.4021) (xy 6.2376 -4.4345) (xy 6.2174 -4.466)
						(xy 6.1957 -4.4964) (xy 6.1727 -4.5257) (xy 6.1483 -4.5539) (xy 6.1226 -4.5808) (xy 6.0956 -4.6065)
						(xy 6.0675 -4.6309) (xy 6.0382 -4.654) (xy 6.0077 -4.6756) (xy 5.9763 -4.6958) (xy 5.9438 -4.7145)
						(xy 5.9104 -4.7316) (xy 5.876 -4.7472) (xy 5.8408 -4.7611) (xy 5.8048 -4.7733) (xy 5.768 -4.7838)
						(xy 5.7305 -4.7924) (xy 5.6924 -4.7993) (xy 5.6536 -4.8042) (xy 5.6143 -4.8072) (xy 5.5744 -4.8082)
						(xy -5.5744 -4.8082) (xy -5.6143 -4.8072) (xy -5.6536 -4.8042) (xy -5.6924 -4.7993) (xy -5.7306 -4.7924)
						(xy -5.7681 -4.7838) (xy -5.8048 -4.7733) (xy -5.8409 -4.7611) (xy -5.8761 -4.7472) (xy -5.9104 -4.7316)
						(xy -5.9438 -4.7145) (xy -5.9763 -4.6958) (xy -6.0078 -4.6756) (xy -6.0382 -4.654) (xy -6.0675 -4.6309)
						(xy -6.0957 -4.6065) (xy -6.1226 -4.5808) (xy -6.1483 -4.5539) (xy -6.1727 -4.5257) (xy -6.1957 -4.4964)
						(xy -6.2174 -4.466) (xy -6.2376 -4.4345) (xy -6.2563 -4.4021) (xy -6.2734 -4.3686) (xy -6.289 -4.3343)
						(xy -6.3029 -4.2991) (xy -6.3151 -4.2631) (xy -6.3255 -4.2263) (xy -6.3342 -4.1888) (xy -6.341 -4.1506)
						(xy -6.346 -4.1118) (xy -6.349 -4.0725) (xy -6.35 -4.0327) (xy -6.35 1.5556) (xy -5.7683 1.5556)
						(xy -5.7683 -4.0327) (xy -5.7681 -4.0426) (xy -5.7673 -4.0525) (xy -5.7661 -4.0622) (xy -5.7644 -4.0717)
						(xy -5.7622 -4.0811) (xy -5.7596 -4.0903) (xy -5.7566 -4.0993) (xy -5.7531 -4.1081) (xy -5.7492 -4.1167)
						(xy -5.7449 -4.1251) (xy -5.7403 -4.1332) (xy -5.7352 -4.1411) (xy -5.7298 -4.1487) (xy -5.7241 -4.156)
						(xy -5.718 -4.163) (xy -5.7115 -4.1698) (xy -5.7048 -4.1762) (xy -5.6978 -4.1823) (xy -5.6905 -4.188)
						(xy -5.6829 -4.1934) (xy -5.675 -4.1985) (xy -5.6669 -4.2032) (xy -5.6585 -4.2074) (xy -5.6499 -4.2113)
						(xy -5.6411 -4.2148) (xy -5.6321 -4.2178) (xy -5.6229 -4.2204) (xy -5.6135 -4.2226) (xy -5.604 -4.2243)
						(xy -5.5943 -4.2255) (xy -5.5844 -4.2263) (xy -5.5744 -4.2265) (xy 5.5744 -4.2265) (xy 5.5844 -4.2263)
						(xy 5.5942 -4.2255) (xy 5.6039 -4.2243) (xy 5.6135 -4.2226) (xy 5.6229 -4.2204) (xy 5.6321 -4.2178)
						(xy 5.6411 -4.2148) (xy 5.6499 -4.2113) (xy 5.6585 -4.2074) (xy 5.6668 -4.2032) (xy 5.6749 -4.1985)
						(xy 5.6828 -4.1934) (xy 5.6904 -4.188) (xy 5.6977 -4.1823) (xy 5.7048 -4.1762) (xy 5.7115 -4.1698)
						(xy 5.7179 -4.163) (xy 5.724 -4.156) (xy 5.7298 -4.1487) (xy 5.7352 -4.1411) (xy 5.7402 -4.1332)
						(xy 5.7449 -4.1251) (xy 5.7492 -4.1167) (xy 5.7531 -4.1081) (xy 5.7565 -4.0993) (xy 5.7596 -4.0903)
						(xy 5.7622 -4.0811) (xy 5.7644 -4.0717) (xy 5.7661 -4.0622) (xy 5.7673 -4.0525) (xy 5.7681 -4.0426)
						(xy 5.7683 -4.0327) (xy 5.7683 1.5556) (xy 5.768 1.5681) (xy 5.7671 1.5804) (xy 5.7655 1.5925)
						(xy 5.7634 1.6045) (xy 5.7607 1.6162) (xy 5.7574 1.6277) (xy 5.7536 1.6389) (xy 5.7493 1.6499)
						(xy 5.7444 1.6607) (xy 5.7391 1.6711) (xy 5.7332 1.6813) (xy 5.7269 1.6911) (xy 5.7201 1.7006)
						(xy 5.713 1.7098) (xy 5.7053 1.7186) (xy 5.6973 1.727) (xy 5.6889 1.735) (xy 5.6801 1.7426) (xy 5.6709 1.7498)
						(xy 5.6614 1.7566) (xy 5.6516 1.7629) (xy 5.6414 1.7687) (xy 5.631 1.7741) (xy 5.6203 1.7789)
						(xy 5.6093 1.7833) (xy 5.598 1.7871) (xy 5.5865 1.7904) (xy 5.5748 1.7931) (xy 5.5628 1.7952)
						(xy 5.5507 1.7968) (xy 5.5384 1.7977) (xy 5.526 1.798) (xy -5.526 1.798) (xy -5.5384 1.7977) (xy -5.5507 1.7968)
						(xy -5.5629 1.7952) (xy -5.5748 1.7931) (xy -5.5865 1.7904) (xy -5.598 1.7871) (xy -5.6093 1.7833)
						(xy -5.6203 1.7789) (xy -5.631 1.7741) (xy -5.6415 1.7687) (xy -5.6516 1.7629) (xy -5.6615 1.7566)
						(xy -5.671 1.7498) (xy -5.6801 1.7426) (xy -5.6889 1.735) (xy -5.6973 1.727) (xy -5.7054 1.7186)
						(xy -5.713 1.7098) (xy -5.7202 1.7006) (xy -5.7269 1.6911) (xy -5.7332 1.6813) (xy -5.7391 1.6711)
						(xy -5.7444 1.6607) (xy -5.7493 1.6499) (xy -5.7536 1.6389) (xy -5.7574 1.6277) (xy -5.7607 1.6162)
						(xy -5.7634 1.6045) (xy -5.7655 1.5925) (xy -5.7671 1.5804) (xy -5.768 1.5681) (xy -5.7683 1.5556)
						(xy -6.35 1.5556) (xy -6.35 4.0238) (xy -6.3495 4.0523) (xy -6.348 4.0805) (xy -6.3455 4.1085)
						(xy -6.3421 4.1362) (xy -6.3377 4.1637) (xy -6.3323 4.1908) (xy -6.3261 4.2176) (xy -6.3189 4.2441)
						(xy -6.3108 4.2702) (xy -6.3018 4.296) (xy -6.2919 4.3213) (xy -6.2811 4.3462) (xy -6.2695 4.3707)
						(xy -6.2571 4.3947) (xy -6.2438 4.4182) (xy -6.2297 4.4413) (xy -6.2148 4.4638) (xy -6.199 4.4858)
						(xy -6.1825 4.5072) (xy -6.1653 4.5281) (xy -6.1472 4.5484) (xy -6.1285 4.568) (xy -6.1089 4.587)
						(xy -6.0887 4.6054) (xy -6.0677 4.6231) (xy -6.0461 4.6402) (xy -6.0238 4.6565) (xy -6.0007 4.6721)
						(xy -5.9771 4.6869) (xy -5.9527 4.701) (xy -5.9278 4.7143) (xy -5.9022 4.7268) (xy -5.85 4.7489)
						(xy -5.797 4.7671) (xy -5.7434 4.7813) (xy -5.6894 4.7916) (xy -5.6352 4.798) (xy -5.5809 4.8006)
						(xy -5.5268 4.7992)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -4.5733 1.0153) (xy -4.5659 1.0147) (xy -4.5586 1.0138) (xy -4.5515 1.0125) (xy -4.5444 1.0109)
						(xy -4.5375 1.0089) (xy -4.5308 1.0067) (xy -4.5242 1.0041) (xy -4.5177 1.0011) (xy -4.5115 0.9979)
						(xy -4.5054 0.9944) (xy -4.4995 0.9906) (xy -4.4938 0.9866) (xy -4.4883 0.9823) (xy -4.483 0.9777)
						(xy -4.4779 0.9729) (xy -4.4731 0.9678) (xy -4.4686 0.9626) (xy -4.4642 0.9571) (xy -4.4602 0.9514)
						(xy -4.4564 0.9455) (xy -4.4529 0.9394) (xy -4.4497 0.9331) (xy -4.4468 0.9267) (xy -4.4442 0.9201)
						(xy -4.4419 0.9133) (xy -4.4399 0.9064) (xy -4.4383 0.8994) (xy -4.437 0.8922) (xy -4.4361 0.8849)
						(xy -4.4355 0.8776) (xy -4.4353 0.8701) (xy -4.4353 0.3369) (xy -4.4355 0.3294) (xy -4.4361 0.322)
						(xy -4.437 0.3147) (xy -4.4383 0.3076) (xy -4.4399 0.3005) (xy -4.4419 0.2936) (xy -4.4442 0.2869)
						(xy -4.4468 0.2803) (xy -4.4497 0.2738) (xy -4.4529 0.2676) (xy -4.4564 0.2615) (xy -4.4602 0.2556)
						(xy -4.4642 0.2499) (xy -4.4686 0.2444) (xy -4.4731 0.2391) (xy -4.4779 0.2341) (xy -4.483 0.2292)
						(xy -4.4883 0.2247) (xy -4.4938 0.2203) (xy -4.4995 0.2163) (xy -4.5054 0.2125) (xy -4.5115 0.209)
						(xy -4.5177 0.2058) (xy -4.5242 0.2029) (xy -4.5308 0.2003) (xy -4.5375 0.198) (xy -4.5444 0.196)
						(xy -4.5515 0.1944) (xy -4.5586 0.1931) (xy -4.5659 0.1922) (xy -4.5733 0.1916) (xy -4.5808 0.1914)
						(xy -5.114 0.1914) (xy -5.1214 0.1916) (xy -5.1288 0.1922) (xy -5.1361 0.1931) (xy -5.1433 0.1944)
						(xy -5.1503 0.196) (xy -5.1572 0.198) (xy -5.1639 0.2003) (xy -5.1705 0.2029) (xy -5.177 0.2058)
						(xy -5.1833 0.209) (xy -5.1893 0.2125) (xy -5.1952 0.2163) (xy -5.2009 0.2203) (xy -5.2064 0.2247)
						(xy -5.2117 0.2292) (xy -5.2168 0.2341) (xy -5.2216 0.2391) (xy -5.2262 0.2444) (xy -5.2305 0.2499)
						(xy -5.2345 0.2556) (xy -5.2383 0.2615) (xy -5.2418 0.2676) (xy -5.245 0.2738) (xy -5.2479 0.2803)
						(xy -5.2506 0.2869) (xy -5.2528 0.2936) (xy -5.2548 0.3005) (xy -5.2564 0.3076) (xy -5.2577 0.3147)
						(xy -5.2586 0.322) (xy -5.2592 0.3294) (xy -5.2594 0.3369) (xy -5.2594 0.8701) (xy -5.2592 0.8776)
						(xy -5.2586 0.8849) (xy -5.2577 0.8922) (xy -5.2564 0.8994) (xy -5.2548 0.9064) (xy -5.2528 0.9133)
						(xy -5.2506 0.9201) (xy -5.2479 0.9267) (xy -5.245 0.9331) (xy -5.2418 0.9394) (xy -5.2383 0.9455)
						(xy -5.2345 0.9514) (xy -5.2305 0.9571) (xy -5.2262 0.9626) (xy -5.2216 0.9678) (xy -5.2168 0.9729)
						(xy -5.2117 0.9777) (xy -5.2064 0.9823) (xy -5.2009 0.9866) (xy -5.1952 0.9906) (xy -5.1893 0.9944)
						(xy -5.1833 0.9979) (xy -5.177 1.0011) (xy -5.1705 1.0041) (xy -5.1639 1.0067) (xy -5.1572 1.0089)
						(xy -5.1503 1.0109) (xy -5.1433 1.0125) (xy -5.1361 1.0138) (xy -5.1288 1.0147) (xy -5.1214 1.0153)
						(xy -5.114 1.0155) (xy -4.5808 1.0155) (xy -4.5733 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -4.5733 -0.1965) (xy -4.5659 -0.1971) (xy -4.5586 -0.198) (xy -4.5515 -0.1993) (xy -4.5444 -0.2009)
						(xy -4.5375 -0.2029) (xy -4.5308 -0.2052) (xy -4.5242 -0.2078) (xy -4.5177 -0.2107) (xy -4.5115 -0.2139)
						(xy -4.5054 -0.2174) (xy -4.4995 -0.2212) (xy -4.4938 -0.2253) (xy -4.4883 -0.2296) (xy -4.483 -0.2341)
						(xy -4.4779 -0.239) (xy -4.4731 -0.244) (xy -4.4686 -0.2493) (xy -4.4642 -0.2548) (xy -4.4602 -0.2605)
						(xy -4.4564 -0.2664) (xy -4.4529 -0.2725) (xy -4.4497 -0.2787) (xy -4.4468 -0.2852) (xy -4.4442 -0.2918)
						(xy -4.4419 -0.2985) (xy -4.4399 -0.3054) (xy -4.4383 -0.3125) (xy -4.437 -0.3196) (xy -4.4361 -0.3269)
						(xy -4.4355 -0.3343) (xy -4.4353 -0.3418) (xy -4.4353 -0.875) (xy -4.4355 -0.8825) (xy -4.4361 -0.8898)
						(xy -4.437 -0.8971) (xy -4.4383 -0.9043) (xy -4.4399 -0.9113) (xy -4.4419 -0.9182) (xy -4.4442 -0.925)
						(xy -4.4468 -0.9316) (xy -4.4497 -0.938) (xy -4.4529 -0.9443) (xy -4.4564 -0.9504) (xy -4.4602 -0.9563)
						(xy -4.4642 -0.962) (xy -4.4686 -0.9675) (xy -4.4731 -0.9727) (xy -4.4779 -0.9778) (xy -4.483 -0.9826)
						(xy -4.4883 -0.9872) (xy -4.4938 -0.9915) (xy -4.4995 -0.9956) (xy -4.5054 -0.9993) (xy -4.5115 -1.0028)
						(xy -4.5177 -1.006) (xy -4.5242 -1.009) (xy -4.5308 -1.0116) (xy -4.5375 -1.0139) (xy -4.5444 -1.0158)
						(xy -4.5515 -1.0174) (xy -4.5586 -1.0187) (xy -4.5659 -1.0196) (xy -4.5733 -1.0202) (xy -4.5808 -1.0204)
						(xy -5.114 -1.0204) (xy -5.1214 -1.0202) (xy -5.1288 -1.0196) (xy -5.1361 -1.0187) (xy -5.1433 -1.0174)
						(xy -5.1503 -1.0158) (xy -5.1572 -1.0139) (xy -5.1639 -1.0116) (xy -5.1705 -1.009) (xy -5.177 -1.006)
						(xy -5.1833 -1.0028) (xy -5.1893 -0.9993) (xy -5.1952 -0.9956) (xy -5.2009 -0.9915) (xy -5.2064 -0.9872)
						(xy -5.2117 -0.9826) (xy -5.2168 -0.9778) (xy -5.2216 -0.9727) (xy -5.2262 -0.9675) (xy -5.2305 -0.962)
						(xy -5.2345 -0.9563) (xy -5.2383 -0.9504) (xy -5.2418 -0.9443) (xy -5.245 -0.938) (xy -5.2479 -0.9316)
						(xy -5.2506 -0.925) (xy -5.2528 -0.9182) (xy -5.2548 -0.9113) (xy -5.2564 -0.9043) (xy -5.2577 -0.8971)
						(xy -5.2586 -0.8898) (xy -5.2592 -0.8825) (xy -5.2594 -0.875) (xy -5.2594 -0.3418) (xy -5.2592 -0.3343)
						(xy -5.2586 -0.3269) (xy -5.2577 -0.3196) (xy -5.2564 -0.3125) (xy -5.2548 -0.3054) (xy -5.2528 -0.2985)
						(xy -5.2506 -0.2918) (xy -5.2479 -0.2852) (xy -5.245 -0.2787) (xy -5.2418 -0.2725) (xy -5.2383 -0.2664)
						(xy -5.2345 -0.2605) (xy -5.2305 -0.2548) (xy -5.2262 -0.2493) (xy -5.2216 -0.244) (xy -5.2168 -0.239)
						(xy -5.2117 -0.2341) (xy -5.2064 -0.2296) (xy -5.2009 -0.2253) (xy -5.1952 -0.2212) (xy -5.1893 -0.2174)
						(xy -5.1833 -0.2139) (xy -5.177 -0.2107) (xy -5.1705 -0.2078) (xy -5.1639 -0.2052) (xy -5.1572 -0.2029)
						(xy -5.1503 -0.2009) (xy -5.1433 -0.1993) (xy -5.1361 -0.198) (xy -5.1288 -0.1971) (xy -5.1214 -0.1965)
						(xy -5.114 -0.1964) (xy -4.5808 -0.1964) (xy -4.5733 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -4.5733 -2.6029) (xy -4.5659 -2.6035) (xy -4.5586 -2.6044) (xy -4.5515 -2.6057) (xy -4.5444 -2.6073)
						(xy -4.5375 -2.6093) (xy -4.5308 -2.6115) (xy -4.5242 -2.6141) (xy -4.5177 -2.6171) (xy -4.5115 -2.6203)
						(xy -4.5054 -2.6238) (xy -4.4995 -2.6276) (xy -4.4938 -2.6316) (xy -4.4883 -2.6359) (xy -4.483 -2.6405)
						(xy -4.4779 -2.6453) (xy -4.4731 -2.6504) (xy -4.4686 -2.6556) (xy -4.4642 -2.6611) (xy -4.4602 -2.6668)
						(xy -4.4564 -2.6727) (xy -4.4529 -2.6788) (xy -4.4497 -2.6851) (xy -4.4468 -2.6915) (xy -4.4442 -2.6981)
						(xy -4.4419 -2.7049) (xy -4.4399 -2.7118) (xy -4.4383 -2.7188) (xy -4.437 -2.726) (xy -4.4361 -2.7333)
						(xy -4.4355 -2.7406) (xy -4.4353 -2.7481) (xy -4.4353 -3.2813) (xy -4.4355 -3.2888) (xy -4.4361 -3.2962)
						(xy -4.437 -3.3035) (xy -4.4383 -3.3106) (xy -4.4399 -3.3177) (xy -4.4419 -3.3246) (xy -4.4442 -3.3313)
						(xy -4.4468 -3.3379) (xy -4.4497 -3.3444) (xy -4.4529 -3.3506) (xy -4.4564 -3.3567) (xy -4.4602 -3.3626)
						(xy -4.4642 -3.3683) (xy -4.4686 -3.3738) (xy -4.4731 -3.3791) (xy -4.4779 -3.3841) (xy -4.483 -3.389)
						(xy -4.4883 -3.3935) (xy -4.4938 -3.3979) (xy -4.4995 -3.4019) (xy -4.5054 -3.4057) (xy -4.5115 -3.4092)
						(xy -4.5177 -3.4124) (xy -4.5242 -3.4153) (xy -4.5308 -3.4179) (xy -4.5375 -3.4202) (xy -4.5444 -3.4222)
						(xy -4.5515 -3.4238) (xy -4.5586 -3.4251) (xy -4.5659 -3.426) (xy -4.5733 -3.4266) (xy -4.5808 -3.4268)
						(xy -5.114 -3.4268) (xy -5.1214 -3.4266) (xy -5.1288 -3.426) (xy -5.1361 -3.4251) (xy -5.1433 -3.4238)
						(xy -5.1503 -3.4222) (xy -5.1572 -3.4202) (xy -5.1639 -3.4179) (xy -5.1705 -3.4153) (xy -5.177 -3.4124)
						(xy -5.1833 -3.4092) (xy -5.1893 -3.4057) (xy -5.1952 -3.4019) (xy -5.2009 -3.3979) (xy -5.2064 -3.3935)
						(xy -5.2117 -3.389) (xy -5.2168 -3.3841) (xy -5.2216 -3.3791) (xy -5.2262 -3.3738) (xy -5.2305 -3.3683)
						(xy -5.2345 -3.3626) (xy -5.2383 -3.3567) (xy -5.2418 -3.3506) (xy -5.245 -3.3444) (xy -5.2479 -3.3379)
						(xy -5.2506 -3.3313) (xy -5.2528 -3.3246) (xy -5.2548 -3.3177) (xy -5.2564 -3.3106) (xy -5.2577 -3.3035)
						(xy -5.2586 -3.2962) (xy -5.2592 -3.2888) (xy -5.2594 -3.2813) (xy -5.2594 -2.7481) (xy -5.2592 -2.7406)
						(xy -5.2586 -2.7333) (xy -5.2577 -2.726) (xy -5.2564 -2.7188) (xy -5.2548 -2.7118) (xy -5.2528 -2.7049)
						(xy -5.2506 -2.6981) (xy -5.2479 -2.6915) (xy -5.245 -2.6851) (xy -5.2418 -2.6788) (xy -5.2383 -2.6727)
						(xy -5.2345 -2.6668) (xy -5.2305 -2.6611) (xy -5.2262 -2.6556) (xy -5.2216 -2.6504) (xy -5.2168 -2.6453)
						(xy -5.2117 -2.6405) (xy -5.2064 -2.6359) (xy -5.2009 -2.6316) (xy -5.1952 -2.6276) (xy -5.1893 -2.6238)
						(xy -5.1833 -2.6203) (xy -5.177 -2.6171) (xy -5.1705 -2.6141) (xy -5.1639 -2.6115) (xy -5.1572 -2.6093)
						(xy -5.1503 -2.6073) (xy -5.1433 -2.6057) (xy -5.1361 -2.6044) (xy -5.1288 -2.6035) (xy -5.1214 -2.6029)
						(xy -5.114 -2.6027) (xy -4.5808 -2.6027) (xy -4.5733 -2.6029)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -4.5733 -2.6202) (xy -4.5659 -2.6208) (xy -4.5586 -2.6217) (xy -4.5515 -2.623) (xy -4.5444 -2.6246)
						(xy -4.5375 -2.6266) (xy -4.5308 -2.6288) (xy -4.5242 -2.6314) (xy -4.5177 -2.6344) (xy -4.5115 -2.6376)
						(xy -4.5054 -2.6411) (xy -4.4995 -2.6449) (xy -4.4938 -2.6489) (xy -4.4883 -2.6532) (xy -4.483 -2.6578)
						(xy -4.4779 -2.6626) (xy -4.4731 -2.6677) (xy -4.4686 -2.673) (xy -4.4642 -2.6784) (xy -4.4602 -2.6841)
						(xy -4.4564 -2.69) (xy -4.4529 -2.6961) (xy -4.4497 -2.7024) (xy -4.4468 -2.7088) (xy -4.4442 -2.7155)
						(xy -4.4419 -2.7222) (xy -4.4399 -2.7291) (xy -4.4383 -2.7361) (xy -4.437 -2.7433) (xy -4.4361 -2.7506)
						(xy -4.4355 -2.758) (xy -4.4353 -2.7654) (xy -4.4353 -3.2986) (xy -4.4355 -3.3061) (xy -4.4361 -3.3135)
						(xy -4.437 -3.3208) (xy -4.4383 -3.3279) (xy -4.4399 -3.335) (xy -4.4419 -3.3419) (xy -4.4442 -3.3486)
						(xy -4.4468 -3.3552) (xy -4.4497 -3.3617) (xy -4.4529 -3.3679) (xy -4.4564 -3.374) (xy -4.4602 -3.3799)
						(xy -4.4642 -3.3856) (xy -4.4686 -3.3911) (xy -4.4731 -3.3964) (xy -4.4779 -3.4015) (xy -4.483 -3.4063)
						(xy -4.4883 -3.4108) (xy -4.4938 -3.4152) (xy -4.4995 -3.4192) (xy -4.5054 -3.423) (xy -4.5115 -3.4265)
						(xy -4.5177 -3.4297) (xy -4.5242 -3.4326) (xy -4.5308 -3.4352) (xy -4.5375 -3.4375) (xy -4.5444 -3.4395)
						(xy -4.5515 -3.4411) (xy -4.5586 -3.4424) (xy -4.5659 -3.4433) (xy -4.5733 -3.4439) (xy -4.5808 -3.4441)
						(xy -5.114 -3.4441) (xy -5.1214 -3.4439) (xy -5.1288 -3.4433) (xy -5.1361 -3.4424) (xy -5.1433 -3.4411)
						(xy -5.1503 -3.4395) (xy -5.1572 -3.4375) (xy -5.1639 -3.4352) (xy -5.1705 -3.4326) (xy -5.177 -3.4297)
						(xy -5.1833 -3.4265) (xy -5.1893 -3.423) (xy -5.1952 -3.4192) (xy -5.2009 -3.4152) (xy -5.2064 -3.4108)
						(xy -5.2117 -3.4063) (xy -5.2168 -3.4015) (xy -5.2216 -3.3964) (xy -5.2262 -3.3911) (xy -5.2305 -3.3856)
						(xy -5.2345 -3.3799) (xy -5.2383 -3.374) (xy -5.2418 -3.3679) (xy -5.245 -3.3617) (xy -5.2479 -3.3552)
						(xy -5.2506 -3.3486) (xy -5.2528 -3.3419) (xy -5.2548 -3.335) (xy -5.2564 -3.3279) (xy -5.2577 -3.3208)
						(xy -5.2586 -3.3135) (xy -5.2592 -3.3061) (xy -5.2594 -3.2986) (xy -5.2594 -2.7654) (xy -5.2592 -2.758)
						(xy -5.2586 -2.7506) (xy -5.2577 -2.7433) (xy -5.2564 -2.7361) (xy -5.2548 -2.7291) (xy -5.2528 -2.7222)
						(xy -5.2506 -2.7155) (xy -5.2479 -2.7088) (xy -5.245 -2.7024) (xy -5.2418 -2.6961) (xy -5.2383 -2.69)
						(xy -5.2345 -2.6841) (xy -5.2305 -2.6784) (xy -5.2262 -2.673) (xy -5.2216 -2.6677) (xy -5.2168 -2.6626)
						(xy -5.2117 -2.6578) (xy -5.2064 -2.6532) (xy -5.2009 -2.6489) (xy -5.1952 -2.6449) (xy -5.1893 -2.6411)
						(xy -5.1833 -2.6376) (xy -5.177 -2.6344) (xy -5.1705 -2.6314) (xy -5.1639 -2.6288) (xy -5.1572 -2.6266)
						(xy -5.1503 -2.6246) (xy -5.1433 -2.623) (xy -5.1361 -2.6217) (xy -5.1288 -2.6208) (xy -5.1214 -2.6202)
						(xy -5.114 -2.62) (xy -4.5808 -2.62) (xy -4.5733 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3668 -1.4084) (xy -3.3594 -1.4089) (xy -3.3521 -1.4099) (xy -3.345 -1.4111) (xy -3.3379 -1.4128)
						(xy -3.331 -1.4147) (xy -3.3243 -1.417) (xy -3.3177 -1.4196) (xy -3.3112 -1.4225) (xy -3.305 -1.4257)
						(xy -3.2989 -1.4292) (xy -3.293 -1.433) (xy -3.2873 -1.4371) (xy -3.2818 -1.4414) (xy -3.2765 -1.446)
						(xy -3.2714 -1.4508) (xy -3.2666 -1.4558) (xy -3.2621 -1.4611) (xy -3.2577 -1.4666) (xy -3.2537 -1.4723)
						(xy -3.2499 -1.4782) (xy -3.2464 -1.4843) (xy -3.2432 -1.4906) (xy -3.2403 -1.497) (xy -3.2377 -1.5036)
						(xy -3.2354 -1.5104) (xy -3.2334 -1.5173) (xy -3.2318 -1.5243) (xy -3.2305 -1.5315) (xy -3.2296 -1.5387)
						(xy -3.229 -1.5461) (xy -3.2288 -1.5536) (xy -3.2288 -2.0868) (xy -3.229 -2.0943) (xy -3.2296 -2.1017)
						(xy -3.2305 -2.1089) (xy -3.2318 -2.1161) (xy -3.2334 -2.1231) (xy -3.2354 -2.13) (xy -3.2377 -2.1368)
						(xy -3.2403 -2.1434) (xy -3.2432 -2.1498) (xy -3.2464 -2.1561) (xy -3.2499 -2.1622) (xy -3.2537 -2.1681)
						(xy -3.2577 -2.1738) (xy -3.2621 -2.1793) (xy -3.2666 -2.1846) (xy -3.2714 -2.1896) (xy -3.2765 -2.1944)
						(xy -3.2818 -2.199) (xy -3.2873 -2.2033) (xy -3.293 -2.2074) (xy -3.2989 -2.2112) (xy -3.305 -2.2147)
						(xy -3.3112 -2.2179) (xy -3.3177 -2.2208) (xy -3.3243 -2.2234) (xy -3.331 -2.2257) (xy -3.3379 -2.2276)
						(xy -3.345 -2.2293) (xy -3.3521 -2.2306) (xy -3.3594 -2.2315) (xy -3.3668 -2.232) (xy -3.3743 -2.2322)
						(xy -3.4549 -2.2322) (xy -3.9075 -2.2322) (xy -4.5861 -2.2322) (xy -5.0226 -2.2322) (xy -5.1193 -2.2322)
						(xy -5.1268 -2.232) (xy -5.1342 -2.2315) (xy -5.1414 -2.2306) (xy -5.1486 -2.2293) (xy -5.1556 -2.2276)
						(xy -5.1625 -2.2257) (xy -5.1693 -2.2234) (xy -5.1759 -2.2208) (xy -5.1823 -2.2179) (xy -5.1886 -2.2147)
						(xy -5.1947 -2.2112) (xy -5.2006 -2.2074) (xy -5.2063 -2.2033) (xy -5.2118 -2.199) (xy -5.2171 -2.1944)
						(xy -5.2221 -2.1896) (xy -5.2269 -2.1846) (xy -5.2315 -2.1793) (xy -5.2358 -2.1738) (xy -5.2399 -2.1681)
						(xy -5.2437 -2.1622) (xy -5.2472 -2.1561) (xy -5.2504 -2.1498) (xy -5.2533 -2.1434) (xy -5.2559 -2.1368)
						(xy -5.2582 -2.13) (xy -5.2601 -2.1231) (xy -5.2618 -2.1161) (xy -5.263 -2.1089) (xy -5.264 -2.1017)
						(xy -5.2645 -2.0943) (xy -5.2647 -2.0868) (xy -5.2647 -1.5536) (xy -5.2645 -1.5461) (xy -5.264 -1.5387)
						(xy -5.263 -1.5315) (xy -5.2618 -1.5243) (xy -5.2601 -1.5173) (xy -5.2582 -1.5104) (xy -5.2559 -1.5036)
						(xy -5.2533 -1.497) (xy -5.2504 -1.4906) (xy -5.2472 -1.4843) (xy -5.2437 -1.4782) (xy -5.2399 -1.4723)
						(xy -5.2358 -1.4666) (xy -5.2315 -1.4611) (xy -5.2269 -1.4558) (xy -5.2221 -1.4508) (xy -5.2171 -1.446)
						(xy -5.2118 -1.4414) (xy -5.2063 -1.4371) (xy -5.2006 -1.433) (xy -5.1947 -1.4292) (xy -5.1886 -1.4257)
						(xy -5.1823 -1.4225) (xy -5.1759 -1.4196) (xy -5.1693 -1.417) (xy -5.1625 -1.4147) (xy -5.1556 -1.4128)
						(xy -5.1486 -1.4111) (xy -5.1414 -1.4099) (xy -5.1342 -1.4089) (xy -5.1268 -1.4084) (xy -5.1193 -1.4082)
						(xy -5.0226 -1.4082) (xy -4.5861 -1.4082) (xy -3.9075 -1.4082) (xy -3.4549 -1.4082) (xy -3.3743 -1.4082)
						(xy -3.3668 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3561 1.0153) (xy -3.3487 1.0147) (xy -3.3415 1.0138) (xy -3.3343 1.0125) (xy -3.3273 1.0109)
						(xy -3.3204 1.0089) (xy -3.3136 1.0067) (xy -3.307 1.0041) (xy -3.3006 1.0011) (xy -3.2943 0.9979)
						(xy -3.2882 0.9944) (xy -3.2823 0.9906) (xy -3.2766 0.9866) (xy -3.2711 0.9823) (xy -3.2658 0.9777)
						(xy -3.2608 0.9729) (xy -3.256 0.9678) (xy -3.2514 0.9626) (xy -3.2471 0.9571) (xy -3.243 0.9514)
						(xy -3.2392 0.9455) (xy -3.2357 0.9394) (xy -3.2325 0.9331) (xy -3.2296 0.9267) (xy -3.227 0.9201)
						(xy -3.2247 0.9133) (xy -3.2228 0.9064) (xy -3.2211 0.8994) (xy -3.2199 0.8922) (xy -3.2189 0.8849)
						(xy -3.2184 0.8776) (xy -3.2182 0.8701) (xy -3.2182 0.3369) (xy -3.2184 0.3294) (xy -3.2189 0.322)
						(xy -3.2199 0.3147) (xy -3.2211 0.3076) (xy -3.2228 0.3005) (xy -3.2247 0.2936) (xy -3.227 0.2869)
						(xy -3.2296 0.2803) (xy -3.2325 0.2738) (xy -3.2357 0.2676) (xy -3.2392 0.2615) (xy -3.243 0.2556)
						(xy -3.2471 0.2499) (xy -3.2514 0.2444) (xy -3.256 0.2391) (xy -3.2608 0.2341) (xy -3.2658 0.2292)
						(xy -3.2711 0.2247) (xy -3.2766 0.2203) (xy -3.2823 0.2163) (xy -3.2882 0.2125) (xy -3.2943 0.209)
						(xy -3.3006 0.2058) (xy -3.307 0.2029) (xy -3.3136 0.2003) (xy -3.3204 0.198) (xy -3.3273 0.196)
						(xy -3.3343 0.1944) (xy -3.3415 0.1931) (xy -3.3487 0.1922) (xy -3.3561 0.1916) (xy -3.3636 0.1914)
						(xy -3.8968 0.1914) (xy -3.9043 0.1916) (xy -3.9117 0.1922) (xy -3.9189 0.1931) (xy -3.9261 0.1944)
						(xy -3.9331 0.196) (xy -3.94 0.198) (xy -3.9468 0.2003) (xy -3.9534 0.2029) (xy -3.9598 0.2058)
						(xy -3.9661 0.209) (xy -3.9722 0.2125) (xy -3.9781 0.2163) (xy -3.9838 0.2203) (xy -3.9893 0.2247)
						(xy -3.9946 0.2292) (xy -3.9996 0.2341) (xy -4.0044 0.2391) (xy -4.009 0.2444) (xy -4.0133 0.2499)
						(xy -4.0174 0.2556) (xy -4.0212 0.2615) (xy -4.0247 0.2676) (xy -4.0279 0.2738) (xy -4.0308 0.2803)
						(xy -4.0334 0.2869) (xy -4.0357 0.2936) (xy -4.0376 0.3005) (xy -4.0393 0.3076) (xy -4.0405 0.3147)
						(xy -4.0415 0.322) (xy -4.042 0.3294) (xy -4.0422 0.3369) (xy -4.0422 0.8701) (xy -4.042 0.8776)
						(xy -4.0415 0.8849) (xy -4.0405 0.8922) (xy -4.0393 0.8994) (xy -4.0376 0.9064) (xy -4.0357 0.9133)
						(xy -4.0334 0.9201) (xy -4.0308 0.9267) (xy -4.0279 0.9331) (xy -4.0247 0.9394) (xy -4.0212 0.9455)
						(xy -4.0174 0.9514) (xy -4.0133 0.9571) (xy -4.009 0.9626) (xy -4.0044 0.9678) (xy -3.9996 0.9729)
						(xy -3.9946 0.9777) (xy -3.9893 0.9823) (xy -3.9838 0.9866) (xy -3.9781 0.9906) (xy -3.9722 0.9944)
						(xy -3.9661 0.9979) (xy -3.9598 1.0011) (xy -3.9534 1.0041) (xy -3.9468 1.0067) (xy -3.94 1.0089)
						(xy -3.9331 1.0109) (xy -3.9261 1.0125) (xy -3.9189 1.0138) (xy -3.9117 1.0147) (xy -3.9043 1.0153)
						(xy -3.8968 1.0155) (xy -3.3636 1.0155) (xy -3.3561 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3561 -0.1965) (xy -3.3487 -0.1971) (xy -3.3415 -0.198) (xy -3.3343 -0.1993) (xy -3.3273 -0.2009)
						(xy -3.3204 -0.2029) (xy -3.3136 -0.2052) (xy -3.307 -0.2078) (xy -3.3006 -0.2107) (xy -3.2943 -0.2139)
						(xy -3.2882 -0.2174) (xy -3.2823 -0.2212) (xy -3.2766 -0.2253) (xy -3.2711 -0.2296) (xy -3.2658 -0.2341)
						(xy -3.2608 -0.239) (xy -3.256 -0.244) (xy -3.2514 -0.2493) (xy -3.2471 -0.2548) (xy -3.243 -0.2605)
						(xy -3.2392 -0.2664) (xy -3.2357 -0.2725) (xy -3.2325 -0.2787) (xy -3.2296 -0.2852) (xy -3.227 -0.2918)
						(xy -3.2247 -0.2985) (xy -3.2228 -0.3054) (xy -3.2211 -0.3125) (xy -3.2199 -0.3196) (xy -3.2189 -0.3269)
						(xy -3.2184 -0.3343) (xy -3.2182 -0.3418) (xy -3.2182 -0.875) (xy -3.2184 -0.8825) (xy -3.2189 -0.8898)
						(xy -3.2199 -0.8971) (xy -3.2211 -0.9043) (xy -3.2228 -0.9113) (xy -3.2247 -0.9182) (xy -3.227 -0.925)
						(xy -3.2296 -0.9316) (xy -3.2325 -0.938) (xy -3.2357 -0.9443) (xy -3.2392 -0.9504) (xy -3.243 -0.9563)
						(xy -3.2471 -0.962) (xy -3.2514 -0.9675) (xy -3.256 -0.9727) (xy -3.2608 -0.9778) (xy -3.2658 -0.9826)
						(xy -3.2711 -0.9872) (xy -3.2766 -0.9915) (xy -3.2823 -0.9956) (xy -3.2882 -0.9993) (xy -3.2943 -1.0028)
						(xy -3.3006 -1.006) (xy -3.307 -1.009) (xy -3.3136 -1.0116) (xy -3.3204 -1.0139) (xy -3.3273 -1.0158)
						(xy -3.3343 -1.0174) (xy -3.3415 -1.0187) (xy -3.3487 -1.0196) (xy -3.3561 -1.0202) (xy -3.3636 -1.0204)
						(xy -3.8968 -1.0204) (xy -3.9043 -1.0202) (xy -3.9117 -1.0196) (xy -3.9189 -1.0187) (xy -3.9261 -1.0174)
						(xy -3.9331 -1.0158) (xy -3.94 -1.0139) (xy -3.9468 -1.0116) (xy -3.9534 -1.009) (xy -3.9598 -1.006)
						(xy -3.9661 -1.0028) (xy -3.9722 -0.9993) (xy -3.9781 -0.9956) (xy -3.9838 -0.9915) (xy -3.9893 -0.9872)
						(xy -3.9946 -0.9826) (xy -3.9996 -0.9778) (xy -4.0044 -0.9727) (xy -4.009 -0.9675) (xy -4.0133 -0.962)
						(xy -4.0174 -0.9563) (xy -4.0212 -0.9504) (xy -4.0247 -0.9443) (xy -4.0279 -0.938) (xy -4.0308 -0.9316)
						(xy -4.0334 -0.925) (xy -4.0357 -0.9182) (xy -4.0376 -0.9113) (xy -4.0393 -0.9043) (xy -4.0405 -0.8971)
						(xy -4.0415 -0.8898) (xy -4.042 -0.8825) (xy -4.0422 -0.875) (xy -4.0422 -0.3418) (xy -4.042 -0.3343)
						(xy -4.0415 -0.3269) (xy -4.0405 -0.3196) (xy -4.0393 -0.3125) (xy -4.0376 -0.3054) (xy -4.0357 -0.2985)
						(xy -4.0334 -0.2918) (xy -4.0308 -0.2852) (xy -4.0279 -0.2787) (xy -4.0247 -0.2725) (xy -4.0212 -0.2664)
						(xy -4.0174 -0.2605) (xy -4.0133 -0.2548) (xy -4.009 -0.2493) (xy -4.0044 -0.244) (xy -3.9996 -0.239)
						(xy -3.9946 -0.2341) (xy -3.9893 -0.2296) (xy -3.9838 -0.2253) (xy -3.9781 -0.2212) (xy -3.9722 -0.2174)
						(xy -3.9661 -0.2139) (xy -3.9598 -0.2107) (xy -3.9534 -0.2078) (xy -3.9468 -0.2052) (xy -3.94 -0.2029)
						(xy -3.9331 -0.2009) (xy -3.9261 -0.1993) (xy -3.9189 -0.198) (xy -3.9117 -0.1971) (xy -3.9043 -0.1965)
						(xy -3.8968 -0.1964) (xy -3.3636 -0.1964) (xy -3.3561 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3561 -2.6029) (xy -3.3487 -2.6035) (xy -3.3415 -2.6044) (xy -3.3343 -2.6057) (xy -3.3273 -2.6073)
						(xy -3.3204 -2.6093) (xy -3.3136 -2.6115) (xy -3.307 -2.6141) (xy -3.3006 -2.6171) (xy -3.2943 -2.6203)
						(xy -3.2882 -2.6238) (xy -3.2823 -2.6276) (xy -3.2766 -2.6316) (xy -3.2711 -2.6359) (xy -3.2658 -2.6405)
						(xy -3.2608 -2.6453) (xy -3.256 -2.6504) (xy -3.2514 -2.6556) (xy -3.2471 -2.6611) (xy -3.243 -2.6668)
						(xy -3.2392 -2.6727) (xy -3.2357 -2.6788) (xy -3.2325 -2.6851) (xy -3.2296 -2.6915) (xy -3.227 -2.6981)
						(xy -3.2247 -2.7049) (xy -3.2228 -2.7118) (xy -3.2211 -2.7188) (xy -3.2199 -2.726) (xy -3.2189 -2.7333)
						(xy -3.2184 -2.7406) (xy -3.2182 -2.7481) (xy -3.2182 -3.2813) (xy -3.2184 -3.2888) (xy -3.2189 -3.2962)
						(xy -3.2199 -3.3035) (xy -3.2211 -3.3106) (xy -3.2228 -3.3177) (xy -3.2247 -3.3246) (xy -3.227 -3.3313)
						(xy -3.2296 -3.3379) (xy -3.2325 -3.3444) (xy -3.2357 -3.3506) (xy -3.2392 -3.3567) (xy -3.243 -3.3626)
						(xy -3.2471 -3.3683) (xy -3.2514 -3.3738) (xy -3.256 -3.3791) (xy -3.2608 -3.3841) (xy -3.2658 -3.389)
						(xy -3.2711 -3.3935) (xy -3.2766 -3.3979) (xy -3.2823 -3.4019) (xy -3.2882 -3.4057) (xy -3.2943 -3.4092)
						(xy -3.3006 -3.4124) (xy -3.307 -3.4153) (xy -3.3136 -3.4179) (xy -3.3204 -3.4202) (xy -3.3273 -3.4222)
						(xy -3.3343 -3.4238) (xy -3.3415 -3.4251) (xy -3.3487 -3.426) (xy -3.3561 -3.4266) (xy -3.3636 -3.4268)
						(xy -3.8968 -3.4268) (xy -3.9043 -3.4266) (xy -3.9117 -3.426) (xy -3.9189 -3.4251) (xy -3.9261 -3.4238)
						(xy -3.9331 -3.4222) (xy -3.94 -3.4202) (xy -3.9468 -3.4179) (xy -3.9534 -3.4153) (xy -3.9598 -3.4124)
						(xy -3.9661 -3.4092) (xy -3.9722 -3.4057) (xy -3.9781 -3.4019) (xy -3.9838 -3.3979) (xy -3.9893 -3.3935)
						(xy -3.9946 -3.389) (xy -3.9996 -3.3841) (xy -4.0044 -3.3791) (xy -4.009 -3.3738) (xy -4.0133 -3.3683)
						(xy -4.0174 -3.3626) (xy -4.0212 -3.3567) (xy -4.0247 -3.3506) (xy -4.0279 -3.3444) (xy -4.0308 -3.3379)
						(xy -4.0334 -3.3313) (xy -4.0357 -3.3246) (xy -4.0376 -3.3177) (xy -4.0393 -3.3106) (xy -4.0405 -3.3035)
						(xy -4.0415 -3.2962) (xy -4.042 -3.2888) (xy -4.0422 -3.2813) (xy -4.0422 -2.7481) (xy -4.042 -2.7406)
						(xy -4.0415 -2.7333) (xy -4.0405 -2.726) (xy -4.0393 -2.7188) (xy -4.0376 -2.7118) (xy -4.0357 -2.7049)
						(xy -4.0334 -2.6981) (xy -4.0308 -2.6915) (xy -4.0279 -2.6851) (xy -4.0247 -2.6788) (xy -4.0212 -2.6727)
						(xy -4.0174 -2.6668) (xy -4.0133 -2.6611) (xy -4.009 -2.6556) (xy -4.0044 -2.6504) (xy -3.9996 -2.6453)
						(xy -3.9946 -2.6405) (xy -3.9893 -2.6359) (xy -3.9838 -2.6316) (xy -3.9781 -2.6276) (xy -3.9722 -2.6238)
						(xy -3.9661 -2.6203) (xy -3.9598 -2.6171) (xy -3.9534 -2.6141) (xy -3.9468 -2.6115) (xy -3.94 -2.6093)
						(xy -3.9331 -2.6073) (xy -3.9261 -2.6057) (xy -3.9189 -2.6044) (xy -3.9117 -2.6035) (xy -3.9043 -2.6029)
						(xy -3.8968 -2.6027) (xy -3.3636 -2.6027) (xy -3.3561 -2.6029)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3561 -2.6202) (xy -3.3487 -2.6208) (xy -3.3415 -2.6217) (xy -3.3343 -2.623) (xy -3.3273 -2.6246)
						(xy -3.3204 -2.6266) (xy -3.3136 -2.6288) (xy -3.307 -2.6314) (xy -3.3006 -2.6344) (xy -3.2943 -2.6376)
						(xy -3.2882 -2.6411) (xy -3.2823 -2.6449) (xy -3.2766 -2.6489) (xy -3.2711 -2.6532) (xy -3.2658 -2.6578)
						(xy -3.2608 -2.6626) (xy -3.256 -2.6677) (xy -3.2514 -2.673) (xy -3.2471 -2.6784) (xy -3.243 -2.6841)
						(xy -3.2392 -2.69) (xy -3.2357 -2.6961) (xy -3.2325 -2.7024) (xy -3.2296 -2.7088) (xy -3.227 -2.7155)
						(xy -3.2247 -2.7222) (xy -3.2228 -2.7291) (xy -3.2211 -2.7361) (xy -3.2199 -2.7433) (xy -3.2189 -2.7506)
						(xy -3.2184 -2.758) (xy -3.2182 -2.7654) (xy -3.2182 -3.2986) (xy -3.2184 -3.3061) (xy -3.2189 -3.3135)
						(xy -3.2199 -3.3208) (xy -3.2211 -3.3279) (xy -3.2228 -3.335) (xy -3.2247 -3.3419) (xy -3.227 -3.3486)
						(xy -3.2296 -3.3552) (xy -3.2325 -3.3617) (xy -3.2357 -3.3679) (xy -3.2392 -3.374) (xy -3.243 -3.3799)
						(xy -3.2471 -3.3856) (xy -3.2514 -3.3911) (xy -3.256 -3.3964) (xy -3.2608 -3.4015) (xy -3.2658 -3.4063)
						(xy -3.2711 -3.4108) (xy -3.2766 -3.4152) (xy -3.2823 -3.4192) (xy -3.2882 -3.423) (xy -3.2943 -3.4265)
						(xy -3.3006 -3.4297) (xy -3.307 -3.4326) (xy -3.3136 -3.4352) (xy -3.3204 -3.4375) (xy -3.3273 -3.4395)
						(xy -3.3343 -3.4411) (xy -3.3415 -3.4424) (xy -3.3487 -3.4433) (xy -3.3561 -3.4439) (xy -3.3636 -3.4441)
						(xy -3.8968 -3.4441) (xy -3.9043 -3.4439) (xy -3.9117 -3.4433) (xy -3.9189 -3.4424) (xy -3.9261 -3.4411)
						(xy -3.9331 -3.4395) (xy -3.94 -3.4375) (xy -3.9468 -3.4352) (xy -3.9534 -3.4326) (xy -3.9598 -3.4297)
						(xy -3.9661 -3.4265) (xy -3.9722 -3.423) (xy -3.9781 -3.4192) (xy -3.9838 -3.4152) (xy -3.9893 -3.4108)
						(xy -3.9946 -3.4063) (xy -3.9996 -3.4015) (xy -4.0044 -3.3964) (xy -4.009 -3.3911) (xy -4.0133 -3.3856)
						(xy -4.0174 -3.3799) (xy -4.0212 -3.374) (xy -4.0247 -3.3679) (xy -4.0279 -3.3617) (xy -4.0308 -3.3552)
						(xy -4.0334 -3.3486) (xy -4.0357 -3.3419) (xy -4.0376 -3.335) (xy -4.0393 -3.3279) (xy -4.0405 -3.3208)
						(xy -4.0415 -3.3135) (xy -4.042 -3.3061) (xy -4.0422 -3.2986) (xy -4.0422 -2.7654) (xy -4.042 -2.758)
						(xy -4.0415 -2.7506) (xy -4.0405 -2.7433) (xy -4.0393 -2.7361) (xy -4.0376 -2.7291) (xy -4.0357 -2.7222)
						(xy -4.0334 -2.7155) (xy -4.0308 -2.7088) (xy -4.0279 -2.7024) (xy -4.0247 -2.6961) (xy -4.0212 -2.69)
						(xy -4.0174 -2.6841) (xy -4.0133 -2.6784) (xy -4.009 -2.673) (xy -4.0044 -2.6677) (xy -3.9996 -2.6626)
						(xy -3.9946 -2.6578) (xy -3.9893 -2.6532) (xy -3.9838 -2.6489) (xy -3.9781 -2.6449) (xy -3.9722 -2.6411)
						(xy -3.9661 -2.6376) (xy -3.9598 -2.6344) (xy -3.9534 -2.6314) (xy -3.9468 -2.6288) (xy -3.94 -2.6266)
						(xy -3.9331 -2.6246) (xy -3.9261 -2.623) (xy -3.9189 -2.6217) (xy -3.9117 -2.6208) (xy -3.9043 -2.6202)
						(xy -3.8968 -2.62) (xy -3.3636 -2.62) (xy -3.3561 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -2.1496 1.0153) (xy -2.1422 1.0147) (xy -2.135 1.0138) (xy -2.1278 1.0125) (xy -2.1208 1.0109)
						(xy -2.1139 1.0089) (xy -2.1071 1.0067) (xy -2.1005 1.0041) (xy -2.0941 1.0011) (xy -2.0878 0.9979)
						(xy -2.0817 0.9944) (xy -2.0758 0.9906) (xy -2.0701 0.9866) (xy -2.0646 0.9823) (xy -2.0593 0.9777)
						(xy -2.0543 0.9729) (xy -2.0495 0.9678) (xy -2.0449 0.9626) (xy -2.0406 0.9571) (xy -2.0365 0.9514)
						(xy -2.0327 0.9455) (xy -2.0292 0.9394) (xy -2.026 0.9331) (xy -2.0231 0.9267) (xy -2.0205 0.9201)
						(xy -2.0182 0.9133) (xy -2.0163 0.9064) (xy -2.0146 0.8994) (xy -2.0133 0.8922) (xy -2.0124 0.8849)
						(xy -2.0119 0.8776) (xy -2.0117 0.8701) (xy -2.0117 0.3369) (xy -2.0119 0.3294) (xy -2.0124 0.322)
						(xy -2.0133 0.3147) (xy -2.0146 0.3076) (xy -2.0163 0.3005) (xy -2.0182 0.2936) (xy -2.0205 0.2869)
						(xy -2.0231 0.2803) (xy -2.026 0.2738) (xy -2.0292 0.2676) (xy -2.0327 0.2615) (xy -2.0365 0.2556)
						(xy -2.0406 0.2499) (xy -2.0449 0.2444) (xy -2.0495 0.2391) (xy -2.0543 0.2341) (xy -2.0593 0.2292)
						(xy -2.0646 0.2247) (xy -2.0701 0.2203) (xy -2.0758 0.2163) (xy -2.0817 0.2125) (xy -2.0878 0.209)
						(xy -2.0941 0.2058) (xy -2.1005 0.2029) (xy -2.1071 0.2003) (xy -2.1139 0.198) (xy -2.1208 0.196)
						(xy -2.1278 0.1944) (xy -2.135 0.1931) (xy -2.1422 0.1922) (xy -2.1496 0.1916) (xy -2.1571 0.1914)
						(xy -2.6903 0.1914) (xy -2.6978 0.1916) (xy -2.7052 0.1922) (xy -2.7124 0.1931) (xy -2.7196 0.1944)
						(xy -2.7266 0.196) (xy -2.7335 0.198) (xy -2.7403 0.2003) (xy -2.7469 0.2029) (xy -2.7533 0.2058)
						(xy -2.7596 0.209) (xy -2.7657 0.2125) (xy -2.7716 0.2163) (xy -2.7773 0.2203) (xy -2.7828 0.2247)
						(xy -2.7881 0.2292) (xy -2.7931 0.2341) (xy -2.7979 0.2391) (xy -2.8025 0.2444) (xy -2.8068 0.2499)
						(xy -2.8109 0.2556) (xy -2.8147 0.2615) (xy -2.8182 0.2676) (xy -2.8214 0.2738) (xy -2.8243 0.2803)
						(xy -2.8269 0.2869) (xy -2.8292 0.2936) (xy -2.8311 0.3005) (xy -2.8328 0.3076) (xy -2.834 0.3147)
						(xy -2.835 0.322) (xy -2.8355 0.3294) (xy -2.8357 0.3369) (xy -2.8357 0.8701) (xy -2.8355 0.8776)
						(xy -2.835 0.8849) (xy -2.834 0.8922) (xy -2.8328 0.8994) (xy -2.8311 0.9064) (xy -2.8292 0.9133)
						(xy -2.8269 0.9201) (xy -2.8243 0.9267) (xy -2.8214 0.9331) (xy -2.8182 0.9394) (xy -2.8147 0.9455)
						(xy -2.8109 0.9514) (xy -2.8068 0.9571) (xy -2.8025 0.9626) (xy -2.7979 0.9678) (xy -2.7931 0.9729)
						(xy -2.7881 0.9777) (xy -2.7828 0.9823) (xy -2.7773 0.9866) (xy -2.7716 0.9906) (xy -2.7657 0.9944)
						(xy -2.7596 0.9979) (xy -2.7533 1.0011) (xy -2.7469 1.0041) (xy -2.7403 1.0067) (xy -2.7335 1.0089)
						(xy -2.7266 1.0109) (xy -2.7196 1.0125) (xy -2.7124 1.0138) (xy -2.7052 1.0147) (xy -2.6978 1.0153)
						(xy -2.6903 1.0155) (xy -2.1571 1.0155) (xy -2.1496 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -2.1496 -0.1965) (xy -2.1422 -0.1971) (xy -2.135 -0.198) (xy -2.1278 -0.1993) (xy -2.1208 -0.2009)
						(xy -2.1139 -0.2029) (xy -2.1071 -0.2052) (xy -2.1005 -0.2078) (xy -2.0941 -0.2107) (xy -2.0878 -0.2139)
						(xy -2.0817 -0.2174) (xy -2.0758 -0.2212) (xy -2.0701 -0.2253) (xy -2.0646 -0.2296) (xy -2.0593 -0.2341)
						(xy -2.0543 -0.239) (xy -2.0495 -0.244) (xy -2.0449 -0.2493) (xy -2.0406 -0.2548) (xy -2.0365 -0.2605)
						(xy -2.0327 -0.2664) (xy -2.0292 -0.2725) (xy -2.026 -0.2787) (xy -2.0231 -0.2852) (xy -2.0205 -0.2918)
						(xy -2.0182 -0.2985) (xy -2.0163 -0.3054) (xy -2.0146 -0.3125) (xy -2.0133 -0.3196) (xy -2.0124 -0.3269)
						(xy -2.0119 -0.3343) (xy -2.0117 -0.3418) (xy -2.0117 -0.875) (xy -2.0119 -0.8825) (xy -2.0124 -0.8898)
						(xy -2.0133 -0.8971) (xy -2.0146 -0.9043) (xy -2.0163 -0.9113) (xy -2.0182 -0.9182) (xy -2.0205 -0.925)
						(xy -2.0231 -0.9316) (xy -2.026 -0.938) (xy -2.0292 -0.9443) (xy -2.0327 -0.9504) (xy -2.0365 -0.9563)
						(xy -2.0406 -0.962) (xy -2.0449 -0.9675) (xy -2.0495 -0.9727) (xy -2.0543 -0.9778) (xy -2.0593 -0.9826)
						(xy -2.0646 -0.9872) (xy -2.0701 -0.9915) (xy -2.0758 -0.9956) (xy -2.0817 -0.9993) (xy -2.0878 -1.0028)
						(xy -2.0941 -1.006) (xy -2.1005 -1.009) (xy -2.1071 -1.0116) (xy -2.1139 -1.0139) (xy -2.1208 -1.0158)
						(xy -2.1278 -1.0174) (xy -2.135 -1.0187) (xy -2.1422 -1.0196) (xy -2.1496 -1.0202) (xy -2.1571 -1.0204)
						(xy -2.6903 -1.0204) (xy -2.6978 -1.0202) (xy -2.7052 -1.0196) (xy -2.7124 -1.0187) (xy -2.7196 -1.0174)
						(xy -2.7266 -1.0158) (xy -2.7335 -1.0139) (xy -2.7403 -1.0116) (xy -2.7469 -1.009) (xy -2.7533 -1.006)
						(xy -2.7596 -1.0028) (xy -2.7657 -0.9993) (xy -2.7716 -0.9956) (xy -2.7773 -0.9915) (xy -2.7828 -0.9872)
						(xy -2.7881 -0.9826) (xy -2.7931 -0.9778) (xy -2.7979 -0.9727) (xy -2.8025 -0.9675) (xy -2.8068 -0.962)
						(xy -2.8109 -0.9563) (xy -2.8147 -0.9504) (xy -2.8182 -0.9443) (xy -2.8214 -0.938) (xy -2.8243 -0.9316)
						(xy -2.8269 -0.925) (xy -2.8292 -0.9182) (xy -2.8311 -0.9113) (xy -2.8328 -0.9043) (xy -2.834 -0.8971)
						(xy -2.835 -0.8898) (xy -2.8355 -0.8825) (xy -2.8357 -0.875) (xy -2.8357 -0.3418) (xy -2.8355 -0.3343)
						(xy -2.835 -0.3269) (xy -2.834 -0.3196) (xy -2.8328 -0.3125) (xy -2.8311 -0.3054) (xy -2.8292 -0.2985)
						(xy -2.8269 -0.2918) (xy -2.8243 -0.2852) (xy -2.8214 -0.2787) (xy -2.8182 -0.2725) (xy -2.8147 -0.2664)
						(xy -2.8109 -0.2605) (xy -2.8068 -0.2548) (xy -2.8025 -0.2493) (xy -2.7979 -0.244) (xy -2.7931 -0.239)
						(xy -2.7881 -0.2341) (xy -2.7828 -0.2296) (xy -2.7773 -0.2253) (xy -2.7716 -0.2212) (xy -2.7657 -0.2174)
						(xy -2.7596 -0.2139) (xy -2.7533 -0.2107) (xy -2.7469 -0.2078) (xy -2.7403 -0.2052) (xy -2.7335 -0.2029)
						(xy -2.7266 -0.2009) (xy -2.7196 -0.1993) (xy -2.7124 -0.198) (xy -2.7052 -0.1971) (xy -2.6978 -0.1965)
						(xy -2.6903 -0.1964) (xy -2.1571 -0.1964) (xy -2.1496 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -2.1496 -1.4084) (xy -2.1422 -1.4089) (xy -2.135 -1.4099) (xy -2.1278 -1.4111) (xy -2.1208 -1.4128)
						(xy -2.1139 -1.4147) (xy -2.1071 -1.417) (xy -2.1005 -1.4196) (xy -2.0941 -1.4225) (xy -2.0878 -1.4257)
						(xy -2.0817 -1.4292) (xy -2.0758 -1.433) (xy -2.0701 -1.4371) (xy -2.0646 -1.4414) (xy -2.0593 -1.446)
						(xy -2.0543 -1.4508) (xy -2.0495 -1.4558) (xy -2.0449 -1.4611) (xy -2.0406 -1.4666) (xy -2.0365 -1.4723)
						(xy -2.0327 -1.4782) (xy -2.0292 -1.4843) (xy -2.026 -1.4906) (xy -2.0231 -1.497) (xy -2.0205 -1.5036)
						(xy -2.0182 -1.5104) (xy -2.0163 -1.5173) (xy -2.0146 -1.5243) (xy -2.0133 -1.5315) (xy -2.0124 -1.5387)
						(xy -2.0119 -1.5461) (xy -2.0117 -1.5536) (xy -2.0117 -2.0868) (xy -2.0119 -2.0943) (xy -2.0124 -2.1017)
						(xy -2.0133 -2.1089) (xy -2.0146 -2.1161) (xy -2.0163 -2.1231) (xy -2.0182 -2.13) (xy -2.0205 -2.1368)
						(xy -2.0231 -2.1434) (xy -2.026 -2.1498) (xy -2.0292 -2.1561) (xy -2.0327 -2.1622) (xy -2.0365 -2.1681)
						(xy -2.0406 -2.1738) (xy -2.0449 -2.1793) (xy -2.0495 -2.1846) (xy -2.0543 -2.1896) (xy -2.0593 -2.1944)
						(xy -2.0646 -2.199) (xy -2.0701 -2.2033) (xy -2.0758 -2.2074) (xy -2.0817 -2.2112) (xy -2.0878 -2.2147)
						(xy -2.0941 -2.2179) (xy -2.1005 -2.2208) (xy -2.1071 -2.2234) (xy -2.1139 -2.2257) (xy -2.1208 -2.2276)
						(xy -2.1278 -2.2293) (xy -2.135 -2.2306) (xy -2.1422 -2.2315) (xy -2.1496 -2.232) (xy -2.1571 -2.2322)
						(xy -2.6903 -2.2322) (xy -2.6978 -2.232) (xy -2.7052 -2.2315) (xy -2.7124 -2.2306) (xy -2.7196 -2.2293)
						(xy -2.7266 -2.2276) (xy -2.7335 -2.2257) (xy -2.7403 -2.2234) (xy -2.7469 -2.2208) (xy -2.7533 -2.2179)
						(xy -2.7596 -2.2147) (xy -2.7657 -2.2112) (xy -2.7716 -2.2074) (xy -2.7773 -2.2033) (xy -2.7828 -2.199)
						(xy -2.7881 -2.1944) (xy -2.7931 -2.1896) (xy -2.7979 -2.1846) (xy -2.8025 -2.1793) (xy -2.8068 -2.1738)
						(xy -2.8109 -2.1681) (xy -2.8147 -2.1622) (xy -2.8182 -2.1561) (xy -2.8214 -2.1498) (xy -2.8243 -2.1434)
						(xy -2.8269 -2.1368) (xy -2.8292 -2.13) (xy -2.8311 -2.1231) (xy -2.8328 -2.1161) (xy -2.834 -2.1089)
						(xy -2.835 -2.1017) (xy -2.8355 -2.0943) (xy -2.8357 -2.0868) (xy -2.8357 -1.5536) (xy -2.8355 -1.5461)
						(xy -2.835 -1.5387) (xy -2.834 -1.5315) (xy -2.8328 -1.5243) (xy -2.8311 -1.5173) (xy -2.8292 -1.5104)
						(xy -2.8269 -1.5036) (xy -2.8243 -1.497) (xy -2.8214 -1.4906) (xy -2.8182 -1.4843) (xy -2.8147 -1.4782)
						(xy -2.8109 -1.4723) (xy -2.8068 -1.4666) (xy -2.8025 -1.4611) (xy -2.7979 -1.4558) (xy -2.7931 -1.4508)
						(xy -2.7881 -1.446) (xy -2.7828 -1.4414) (xy -2.7773 -1.4371) (xy -2.7716 -1.433) (xy -2.7657 -1.4292)
						(xy -2.7596 -1.4257) (xy -2.7533 -1.4225) (xy -2.7469 -1.4196) (xy -2.7403 -1.417) (xy -2.7335 -1.4147)
						(xy -2.7266 -1.4128) (xy -2.7196 -1.4111) (xy -2.7124 -1.4099) (xy -2.7052 -1.4089) (xy -2.6978 -1.4084)
						(xy -2.6903 -1.4082) (xy -2.1571 -1.4082) (xy -2.1496 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -0.9378 1.0153) (xy -0.9304 1.0147) (xy -0.9231 1.0138) (xy -0.916 1.0125) (xy -0.9089 1.0109)
						(xy -0.902 1.0089) (xy -0.8953 1.0067) (xy -0.8887 1.0041) (xy -0.8822 1.0011) (xy -0.876 0.9979)
						(xy -0.8699 0.9944) (xy -0.864 0.9906) (xy -0.8583 0.9866) (xy -0.8528 0.9823) (xy -0.8475 0.9777)
						(xy -0.8424 0.9729) (xy -0.8376 0.9678) (xy -0.8331 0.9626) (xy -0.8287 0.9571) (xy -0.8247 0.9514)
						(xy -0.8209 0.9455) (xy -0.8174 0.9394) (xy -0.8142 0.9331) (xy -0.8113 0.9267) (xy -0.8087 0.9201)
						(xy -0.8064 0.9133) (xy -0.8044 0.9064) (xy -0.8028 0.8994) (xy -0.8015 0.8922) (xy -0.8006 0.8849)
						(xy -0.8 0.8776) (xy -0.7998 0.8701) (xy -0.7998 0.3369) (xy -0.8 0.3294) (xy -0.8006 0.322) (xy -0.8015 0.3147)
						(xy -0.8028 0.3076) (xy -0.8044 0.3005) (xy -0.8064 0.2936) (xy -0.8087 0.2869) (xy -0.8113 0.2803)
						(xy -0.8142 0.2738) (xy -0.8174 0.2676) (xy -0.8209 0.2615) (xy -0.8247 0.2556) (xy -0.8287 0.2499)
						(xy -0.8331 0.2444) (xy -0.8376 0.2391) (xy -0.8424 0.2341) (xy -0.8475 0.2292) (xy -0.8528 0.2247)
						(xy -0.8583 0.2203) (xy -0.864 0.2163) (xy -0.8699 0.2125) (xy -0.876 0.209) (xy -0.8822 0.2058)
						(xy -0.8887 0.2029) (xy -0.8953 0.2003) (xy -0.902 0.198) (xy -0.9089 0.196) (xy -0.916 0.1944)
						(xy -0.9231 0.1931) (xy -0.9304 0.1922) (xy -0.9378 0.1916) (xy -0.9453 0.1914) (xy -1.4785 0.1914)
						(xy -1.4859 0.1916) (xy -1.4933 0.1922) (xy -1.5006 0.1931) (xy -1.5078 0.1944) (xy -1.5148 0.196)
						(xy -1.5217 0.198) (xy -1.5284 0.2003) (xy -1.5351 0.2029) (xy -1.5415 0.2058) (xy -1.5478 0.209)
						(xy -1.5539 0.2125) (xy -1.5598 0.2163) (xy -1.5655 0.2203) (xy -1.5709 0.2247) (xy -1.5762 0.2292)
						(xy -1.5813 0.2341) (xy -1.5861 0.2391) (xy -1.5907 0.2444) (xy -1.595 0.2499) (xy -1.599 0.2556)
						(xy -1.6028 0.2615) (xy -1.6063 0.2676) (xy -1.6095 0.2738) (xy -1.6125 0.2803) (xy -1.6151 0.2869)
						(xy -1.6173 0.2936) (xy -1.6193 0.3005) (xy -1.6209 0.3076) (xy -1.6222 0.3147) (xy -1.6231 0.322)
						(xy -1.6237 0.3294) (xy -1.6239 0.3369) (xy -1.6239 0.8701) (xy -1.6237 0.8776) (xy -1.6231 0.8849)
						(xy -1.6222 0.8922) (xy -1.6209 0.8994) (xy -1.6193 0.9064) (xy -1.6173 0.9133) (xy -1.6151 0.9201)
						(xy -1.6125 0.9267) (xy -1.6095 0.9331) (xy -1.6063 0.9394) (xy -1.6028 0.9455) (xy -1.599 0.9514)
						(xy -1.595 0.9571) (xy -1.5907 0.9626) (xy -1.5861 0.9678) (xy -1.5813 0.9729) (xy -1.5762 0.9777)
						(xy -1.5709 0.9823) (xy -1.5655 0.9866) (xy -1.5598 0.9906) (xy -1.5539 0.9944) (xy -1.5478 0.9979)
						(xy -1.5415 1.0011) (xy -1.5351 1.0041) (xy -1.5284 1.0067) (xy -1.5217 1.0089) (xy -1.5148 1.0109)
						(xy -1.5078 1.0125) (xy -1.5006 1.0138) (xy -1.4933 1.0147) (xy -1.4859 1.0153) (xy -1.4785 1.0155)
						(xy -0.9453 1.0155) (xy -0.9378 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -0.9378 -0.1965) (xy -0.9304 -0.1971) (xy -0.9231 -0.198) (xy -0.916 -0.1993) (xy -0.9089 -0.2009)
						(xy -0.902 -0.2029) (xy -0.8953 -0.2052) (xy -0.8887 -0.2078) (xy -0.8822 -0.2107) (xy -0.876 -0.2139)
						(xy -0.8699 -0.2174) (xy -0.864 -0.2212) (xy -0.8583 -0.2253) (xy -0.8528 -0.2296) (xy -0.8475 -0.2341)
						(xy -0.8424 -0.239) (xy -0.8376 -0.244) (xy -0.8331 -0.2493) (xy -0.8287 -0.2548) (xy -0.8247 -0.2605)
						(xy -0.8209 -0.2664) (xy -0.8174 -0.2725) (xy -0.8142 -0.2787) (xy -0.8113 -0.2852) (xy -0.8087 -0.2918)
						(xy -0.8064 -0.2985) (xy -0.8044 -0.3054) (xy -0.8028 -0.3125) (xy -0.8015 -0.3196) (xy -0.8006 -0.3269)
						(xy -0.8 -0.3343) (xy -0.7998 -0.3418) (xy -0.7998 -0.875) (xy -0.8 -0.8825) (xy -0.8006 -0.8898)
						(xy -0.8015 -0.8971) (xy -0.8028 -0.9043) (xy -0.8044 -0.9113) (xy -0.8064 -0.9182) (xy -0.8087 -0.925)
						(xy -0.8113 -0.9316) (xy -0.8142 -0.938) (xy -0.8174 -0.9443) (xy -0.8209 -0.9504) (xy -0.8247 -0.9563)
						(xy -0.8287 -0.962) (xy -0.8331 -0.9675) (xy -0.8376 -0.9727) (xy -0.8424 -0.9778) (xy -0.8475 -0.9826)
						(xy -0.8528 -0.9872) (xy -0.8583 -0.9915) (xy -0.864 -0.9956) (xy -0.8699 -0.9993) (xy -0.876 -1.0028)
						(xy -0.8822 -1.006) (xy -0.8887 -1.009) (xy -0.8953 -1.0116) (xy -0.902 -1.0139) (xy -0.9089 -1.0158)
						(xy -0.916 -1.0174) (xy -0.9231 -1.0187) (xy -0.9304 -1.0196) (xy -0.9378 -1.0202) (xy -0.9453 -1.0204)
						(xy -1.4785 -1.0204) (xy -1.4859 -1.0202) (xy -1.4933 -1.0196) (xy -1.5006 -1.0187) (xy -1.5078 -1.0174)
						(xy -1.5148 -1.0158) (xy -1.5217 -1.0139) (xy -1.5284 -1.0116) (xy -1.5351 -1.009) (xy -1.5415 -1.006)
						(xy -1.5478 -1.0028) (xy -1.5539 -0.9993) (xy -1.5598 -0.9956) (xy -1.5655 -0.9915) (xy -1.5709 -0.9872)
						(xy -1.5762 -0.9826) (xy -1.5813 -0.9778) (xy -1.5861 -0.9727) (xy -1.5907 -0.9675) (xy -1.595 -0.962)
						(xy -1.599 -0.9563) (xy -1.6028 -0.9504) (xy -1.6063 -0.9443) (xy -1.6095 -0.938) (xy -1.6125 -0.9316)
						(xy -1.6151 -0.925) (xy -1.6173 -0.9182) (xy -1.6193 -0.9113) (xy -1.6209 -0.9043) (xy -1.6222 -0.8971)
						(xy -1.6231 -0.8898) (xy -1.6237 -0.8825) (xy -1.6239 -0.875) (xy -1.6239 -0.3418) (xy -1.6237 -0.3343)
						(xy -1.6231 -0.3269) (xy -1.6222 -0.3196) (xy -1.6209 -0.3125) (xy -1.6193 -0.3054) (xy -1.6173 -0.2985)
						(xy -1.6151 -0.2918) (xy -1.6125 -0.2852) (xy -1.6095 -0.2787) (xy -1.6063 -0.2725) (xy -1.6028 -0.2664)
						(xy -1.599 -0.2605) (xy -1.595 -0.2548) (xy -1.5907 -0.2493) (xy -1.5861 -0.244) (xy -1.5813 -0.239)
						(xy -1.5762 -0.2341) (xy -1.5709 -0.2296) (xy -1.5655 -0.2253) (xy -1.5598 -0.2212) (xy -1.5539 -0.2174)
						(xy -1.5478 -0.2139) (xy -1.5415 -0.2107) (xy -1.5351 -0.2078) (xy -1.5284 -0.2052) (xy -1.5217 -0.2029)
						(xy -1.5148 -0.2009) (xy -1.5078 -0.1993) (xy -1.5006 -0.198) (xy -1.4933 -0.1971) (xy -1.4859 -0.1965)
						(xy -1.4785 -0.1964) (xy -0.9453 -0.1964) (xy -0.9378 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -0.9378 -1.4084) (xy -0.9304 -1.4089) (xy -0.9231 -1.4099) (xy -0.916 -1.4111) (xy -0.9089 -1.4128)
						(xy -0.902 -1.4147) (xy -0.8953 -1.417) (xy -0.8887 -1.4196) (xy -0.8822 -1.4225) (xy -0.876 -1.4257)
						(xy -0.8699 -1.4292) (xy -0.864 -1.433) (xy -0.8583 -1.4371) (xy -0.8528 -1.4414) (xy -0.8475 -1.446)
						(xy -0.8424 -1.4508) (xy -0.8376 -1.4558) (xy -0.8331 -1.4611) (xy -0.8287 -1.4666) (xy -0.8247 -1.4723)
						(xy -0.8209 -1.4782) (xy -0.8174 -1.4843) (xy -0.8142 -1.4906) (xy -0.8113 -1.497) (xy -0.8087 -1.5036)
						(xy -0.8064 -1.5104) (xy -0.8044 -1.5173) (xy -0.8028 -1.5243) (xy -0.8015 -1.5315) (xy -0.8006 -1.5387)
						(xy -0.8 -1.5461) (xy -0.7998 -1.5536) (xy -0.7998 -2.0868) (xy -0.8 -2.0943) (xy -0.8006 -2.1017)
						(xy -0.8015 -2.1089) (xy -0.8028 -2.1161) (xy -0.8044 -2.1231) (xy -0.8064 -2.13) (xy -0.8087 -2.1368)
						(xy -0.8113 -2.1434) (xy -0.8142 -2.1498) (xy -0.8174 -2.1561) (xy -0.8209 -2.1622) (xy -0.8247 -2.1681)
						(xy -0.8287 -2.1738) (xy -0.8331 -2.1793) (xy -0.8376 -2.1846) (xy -0.8424 -2.1896) (xy -0.8475 -2.1944)
						(xy -0.8528 -2.199) (xy -0.8583 -2.2033) (xy -0.864 -2.2074) (xy -0.8699 -2.2112) (xy -0.876 -2.2147)
						(xy -0.8822 -2.2179) (xy -0.8887 -2.2208) (xy -0.8953 -2.2234) (xy -0.902 -2.2257) (xy -0.9089 -2.2276)
						(xy -0.916 -2.2293) (xy -0.9231 -2.2306) (xy -0.9304 -2.2315) (xy -0.9378 -2.232) (xy -0.9453 -2.2322)
						(xy -1.4785 -2.2322) (xy -1.4859 -2.232) (xy -1.4933 -2.2315) (xy -1.5006 -2.2306) (xy -1.5078 -2.2293)
						(xy -1.5148 -2.2276) (xy -1.5217 -2.2257) (xy -1.5284 -2.2234) (xy -1.5351 -2.2208) (xy -1.5415 -2.2179)
						(xy -1.5478 -2.2147) (xy -1.5539 -2.2112) (xy -1.5598 -2.2074) (xy -1.5655 -2.2033) (xy -1.5709 -2.199)
						(xy -1.5762 -2.1944) (xy -1.5813 -2.1896) (xy -1.5861 -2.1846) (xy -1.5907 -2.1793) (xy -1.595 -2.1738)
						(xy -1.599 -2.1681) (xy -1.6028 -2.1622) (xy -1.6063 -2.1561) (xy -1.6095 -2.1498) (xy -1.6125 -2.1434)
						(xy -1.6151 -2.1368) (xy -1.6173 -2.13) (xy -1.6193 -2.1231) (xy -1.6209 -2.1161) (xy -1.6222 -2.1089)
						(xy -1.6231 -2.1017) (xy -1.6237 -2.0943) (xy -1.6239 -2.0868) (xy -1.6239 -1.5536) (xy -1.6237 -1.5461)
						(xy -1.6231 -1.5387) (xy -1.6222 -1.5315) (xy -1.6209 -1.5243) (xy -1.6193 -1.5173) (xy -1.6173 -1.5104)
						(xy -1.6151 -1.5036) (xy -1.6125 -1.497) (xy -1.6095 -1.4906) (xy -1.6063 -1.4843) (xy -1.6028 -1.4782)
						(xy -1.599 -1.4723) (xy -1.595 -1.4666) (xy -1.5907 -1.4611) (xy -1.5861 -1.4558) (xy -1.5813 -1.4508)
						(xy -1.5762 -1.446) (xy -1.5709 -1.4414) (xy -1.5655 -1.4371) (xy -1.5598 -1.433) (xy -1.5539 -1.4292)
						(xy -1.5478 -1.4257) (xy -1.5415 -1.4225) (xy -1.5351 -1.4196) (xy -1.5284 -1.417) (xy -1.5217 -1.4147)
						(xy -1.5148 -1.4128) (xy -1.5078 -1.4111) (xy -1.5006 -1.4099) (xy -1.4933 -1.4089) (xy -1.4859 -1.4084)
						(xy -1.4785 -1.4082) (xy -0.9453 -1.4082) (xy -0.9378 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0.2687 1.0153) (xy 0.2761 1.0147) (xy 0.2834 1.0138) (xy 0.2905 1.0125) (xy 0.2976 1.0109)
						(xy 0.3045 1.0089) (xy 0.3112 1.0067) (xy 0.3178 1.0041) (xy 0.3243 1.0011) (xy 0.3305 0.9979)
						(xy 0.3366 0.9944) (xy 0.3425 0.9906) (xy 0.3482 0.9866) (xy 0.3537 0.9823) (xy 0.359 0.9777)
						(xy 0.3641 0.9729) (xy 0.3689 0.9678) (xy 0.3734 0.9626) (xy 0.3778 0.9571) (xy 0.3818 0.9514)
						(xy 0.3856 0.9455) (xy 0.3891 0.9394) (xy 0.3923 0.9331) (xy 0.3952 0.9267) (xy 0.3978 0.9201)
						(xy 0.4001 0.9133) (xy 0.4021 0.9064) (xy 0.4037 0.8994) (xy 0.405 0.8922) (xy 0.4059 0.8849)
						(xy 0.4065 0.8776) (xy 0.4067 0.8701) (xy 0.4067 0.3369) (xy 0.4065 0.3294) (xy 0.4059 0.322)
						(xy 0.405 0.3147) (xy 0.4037 0.3076) (xy 0.4021 0.3005) (xy 0.4001 0.2936) (xy 0.3978 0.2869)
						(xy 0.3952 0.2803) (xy 0.3923 0.2738) (xy 0.3891 0.2676) (xy 0.3856 0.2615) (xy 0.3818 0.2556)
						(xy 0.3778 0.2499) (xy 0.3734 0.2444) (xy 0.3689 0.2391) (xy 0.3641 0.2341) (xy 0.359 0.2292)
						(xy 0.3537 0.2247) (xy 0.3482 0.2203) (xy 0.3425 0.2163) (xy 0.3366 0.2125) (xy 0.3305 0.209)
						(xy 0.3243 0.2058) (xy 0.3178 0.2029) (xy 0.3112 0.2003) (xy 0.3045 0.198) (xy 0.2976 0.196) (xy 0.2905 0.1944)
						(xy 0.2834 0.1931) (xy 0.2761 0.1922) (xy 0.2687 0.1916) (xy 0.2612 0.1914) (xy -0.272 0.1914)
						(xy -0.2794 0.1916) (xy -0.2868 0.1922) (xy -0.2941 0.1931) (xy -0.3013 0.1944) (xy -0.3083 0.196)
						(xy -0.3152 0.198) (xy -0.3219 0.2003) (xy -0.3286 0.2029) (xy -0.335 0.2058) (xy -0.3413 0.209)
						(xy -0.3474 0.2125) (xy -0.3533 0.2163) (xy -0.359 0.2203) (xy -0.3644 0.2247) (xy -0.3697 0.2292)
						(xy -0.3748 0.2341) (xy -0.3796 0.2391) (xy -0.3842 0.2444) (xy -0.3885 0.2499) (xy -0.3925 0.2556)
						(xy -0.3963 0.2615) (xy -0.3998 0.2676) (xy -0.403 0.2738) (xy -0.406 0.2803) (xy -0.4086 0.2869)
						(xy -0.4108 0.2936) (xy -0.4128 0.3005) (xy -0.4144 0.3076) (xy -0.4157 0.3147) (xy -0.4166 0.322)
						(xy -0.4172 0.3294) (xy -0.4174 0.3369) (xy -0.4174 0.8701) (xy -0.4172 0.8776) (xy -0.4166 0.8849)
						(xy -0.4157 0.8922) (xy -0.4144 0.8994) (xy -0.4128 0.9064) (xy -0.4108 0.9133) (xy -0.4086 0.9201)
						(xy -0.406 0.9267) (xy -0.403 0.9331) (xy -0.3998 0.9394) (xy -0.3963 0.9455) (xy -0.3925 0.9514)
						(xy -0.3885 0.9571) (xy -0.3842 0.9626) (xy -0.3796 0.9678) (xy -0.3748 0.9729) (xy -0.3697 0.9777)
						(xy -0.3644 0.9823) (xy -0.359 0.9866) (xy -0.3533 0.9906) (xy -0.3474 0.9944) (xy -0.3413 0.9979)
						(xy -0.335 1.0011) (xy -0.3286 1.0041) (xy -0.3219 1.0067) (xy -0.3152 1.0089) (xy -0.3083 1.0109)
						(xy -0.3013 1.0125) (xy -0.2941 1.0138) (xy -0.2868 1.0147) (xy -0.2794 1.0153) (xy -0.272 1.0155)
						(xy 0.2612 1.0155) (xy 0.2687 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0.2687 -0.1965) (xy 0.2761 -0.1971) (xy 0.2834 -0.198) (xy 0.2905 -0.1993) (xy 0.2976 -0.2009)
						(xy 0.3045 -0.2029) (xy 0.3112 -0.2052) (xy 0.3178 -0.2078) (xy 0.3243 -0.2107) (xy 0.3305 -0.2139)
						(xy 0.3366 -0.2174) (xy 0.3425 -0.2212) (xy 0.3482 -0.2253) (xy 0.3537 -0.2296) (xy 0.359 -0.2341)
						(xy 0.3641 -0.239) (xy 0.3689 -0.244) (xy 0.3734 -0.2493) (xy 0.3778 -0.2548) (xy 0.3818 -0.2605)
						(xy 0.3856 -0.2664) (xy 0.3891 -0.2725) (xy 0.3923 -0.2787) (xy 0.3952 -0.2852) (xy 0.3978 -0.2918)
						(xy 0.4001 -0.2985) (xy 0.4021 -0.3054) (xy 0.4037 -0.3125) (xy 0.405 -0.3196) (xy 0.4059 -0.3269)
						(xy 0.4065 -0.3343) (xy 0.4067 -0.3418) (xy 0.4067 -0.875) (xy 0.4065 -0.8825) (xy 0.4059 -0.8898)
						(xy 0.405 -0.8971) (xy 0.4037 -0.9043) (xy 0.4021 -0.9113) (xy 0.4001 -0.9182) (xy 0.3978 -0.925)
						(xy 0.3952 -0.9316) (xy 0.3923 -0.938) (xy 0.3891 -0.9443) (xy 0.3856 -0.9504) (xy 0.3818 -0.9563)
						(xy 0.3778 -0.962) (xy 0.3734 -0.9675) (xy 0.3689 -0.9727) (xy 0.3641 -0.9778) (xy 0.359 -0.9826)
						(xy 0.3537 -0.9872) (xy 0.3482 -0.9915) (xy 0.3425 -0.9956) (xy 0.3366 -0.9993) (xy 0.3305 -1.0028)
						(xy 0.3243 -1.006) (xy 0.3178 -1.009) (xy 0.3112 -1.0116) (xy 0.3045 -1.0139) (xy 0.2976 -1.0158)
						(xy 0.2905 -1.0174) (xy 0.2834 -1.0187) (xy 0.2761 -1.0196) (xy 0.2687 -1.0202) (xy 0.2612 -1.0204)
						(xy -0.272 -1.0204) (xy -0.2794 -1.0202) (xy -0.2868 -1.0196) (xy -0.2941 -1.0187) (xy -0.3013 -1.0174)
						(xy -0.3083 -1.0158) (xy -0.3152 -1.0139) (xy -0.3219 -1.0116) (xy -0.3286 -1.009) (xy -0.335 -1.006)
						(xy -0.3413 -1.0028) (xy -0.3474 -0.9993) (xy -0.3533 -0.9956) (xy -0.359 -0.9915) (xy -0.3644 -0.9872)
						(xy -0.3697 -0.9826) (xy -0.3748 -0.9778) (xy -0.3796 -0.9727) (xy -0.3842 -0.9675) (xy -0.3885 -0.962)
						(xy -0.3925 -0.9563) (xy -0.3963 -0.9504) (xy -0.3998 -0.9443) (xy -0.403 -0.938) (xy -0.406 -0.9316)
						(xy -0.4086 -0.925) (xy -0.4108 -0.9182) (xy -0.4128 -0.9113) (xy -0.4144 -0.9043) (xy -0.4157 -0.8971)
						(xy -0.4166 -0.8898) (xy -0.4172 -0.8825) (xy -0.4174 -0.875) (xy -0.4174 -0.3418) (xy -0.4172 -0.3343)
						(xy -0.4166 -0.3269) (xy -0.4157 -0.3196) (xy -0.4144 -0.3125) (xy -0.4128 -0.3054) (xy -0.4108 -0.2985)
						(xy -0.4086 -0.2918) (xy -0.406 -0.2852) (xy -0.403 -0.2787) (xy -0.3998 -0.2725) (xy -0.3963 -0.2664)
						(xy -0.3925 -0.2605) (xy -0.3885 -0.2548) (xy -0.3842 -0.2493) (xy -0.3796 -0.244) (xy -0.3748 -0.239)
						(xy -0.3697 -0.2341) (xy -0.3644 -0.2296) (xy -0.359 -0.2253) (xy -0.3533 -0.2212) (xy -0.3474 -0.2174)
						(xy -0.3413 -0.2139) (xy -0.335 -0.2107) (xy -0.3286 -0.2078) (xy -0.3219 -0.2052) (xy -0.3152 -0.2029)
						(xy -0.3083 -0.2009) (xy -0.3013 -0.1993) (xy -0.2941 -0.198) (xy -0.2868 -0.1971) (xy -0.2794 -0.1965)
						(xy -0.272 -0.1964) (xy 0.2612 -0.1964) (xy 0.2687 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0.2687 -1.4084) (xy 0.2761 -1.4089) (xy 0.2834 -1.4099) (xy 0.2905 -1.4111) (xy 0.2976 -1.4128)
						(xy 0.3045 -1.4147) (xy 0.3112 -1.417) (xy 0.3178 -1.4196) (xy 0.3243 -1.4225) (xy 0.3305 -1.4257)
						(xy 0.3366 -1.4292) (xy 0.3425 -1.433) (xy 0.3482 -1.4371) (xy 0.3537 -1.4414) (xy 0.359 -1.446)
						(xy 0.3641 -1.4508) (xy 0.3689 -1.4558) (xy 0.3734 -1.4611) (xy 0.3778 -1.4666) (xy 0.3818 -1.4723)
						(xy 0.3856 -1.4782) (xy 0.3891 -1.4843) (xy 0.3923 -1.4906) (xy 0.3952 -1.497) (xy 0.3978 -1.5036)
						(xy 0.4001 -1.5104) (xy 0.4021 -1.5173) (xy 0.4037 -1.5243) (xy 0.405 -1.5315) (xy 0.4059 -1.5387)
						(xy 0.4065 -1.5461) (xy 0.4067 -1.5536) (xy 0.4067 -2.0868) (xy 0.4065 -2.0943) (xy 0.4059 -2.1017)
						(xy 0.405 -2.1089) (xy 0.4037 -2.1161) (xy 0.4021 -2.1231) (xy 0.4001 -2.13) (xy 0.3978 -2.1368)
						(xy 0.3952 -2.1434) (xy 0.3923 -2.1498) (xy 0.3891 -2.1561) (xy 0.3856 -2.1622) (xy 0.3818 -2.1681)
						(xy 0.3778 -2.1738) (xy 0.3734 -2.1793) (xy 0.3689 -2.1846) (xy 0.3641 -2.1896) (xy 0.359 -2.1944)
						(xy 0.3537 -2.199) (xy 0.3482 -2.2033) (xy 0.3425 -2.2074) (xy 0.3366 -2.2112) (xy 0.3305 -2.2147)
						(xy 0.3243 -2.2179) (xy 0.3178 -2.2208) (xy 0.3112 -2.2234) (xy 0.3045 -2.2257) (xy 0.2976 -2.2276)
						(xy 0.2905 -2.2293) (xy 0.2834 -2.2306) (xy 0.2761 -2.2315) (xy 0.2687 -2.232) (xy 0.2612 -2.2322)
						(xy -0.272 -2.2322) (xy -0.2794 -2.232) (xy -0.2868 -2.2315) (xy -0.2941 -2.2306) (xy -0.3013 -2.2293)
						(xy -0.3083 -2.2276) (xy -0.3152 -2.2257) (xy -0.3219 -2.2234) (xy -0.3286 -2.2208) (xy -0.335 -2.2179)
						(xy -0.3413 -2.2147) (xy -0.3474 -2.2112) (xy -0.3533 -2.2074) (xy -0.359 -2.2033) (xy -0.3644 -2.199)
						(xy -0.3697 -2.1944) (xy -0.3748 -2.1896) (xy -0.3796 -2.1846) (xy -0.3842 -2.1793) (xy -0.3885 -2.1738)
						(xy -0.3925 -2.1681) (xy -0.3963 -2.1622) (xy -0.3998 -2.1561) (xy -0.403 -2.1498) (xy -0.406 -2.1434)
						(xy -0.4086 -2.1368) (xy -0.4108 -2.13) (xy -0.4128 -2.1231) (xy -0.4144 -2.1161) (xy -0.4157 -2.1089)
						(xy -0.4166 -2.1017) (xy -0.4172 -2.0943) (xy -0.4174 -2.0868) (xy -0.4174 -1.5536) (xy -0.4172 -1.5461)
						(xy -0.4166 -1.5387) (xy -0.4157 -1.5315) (xy -0.4144 -1.5243) (xy -0.4128 -1.5173) (xy -0.4108 -1.5104)
						(xy -0.4086 -1.5036) (xy -0.406 -1.497) (xy -0.403 -1.4906) (xy -0.3998 -1.4843) (xy -0.3963 -1.4782)
						(xy -0.3925 -1.4723) (xy -0.3885 -1.4666) (xy -0.3842 -1.4611) (xy -0.3796 -1.4558) (xy -0.3748 -1.4508)
						(xy -0.3697 -1.446) (xy -0.3644 -1.4414) (xy -0.359 -1.4371) (xy -0.3533 -1.433) (xy -0.3474 -1.4292)
						(xy -0.3413 -1.4257) (xy -0.335 -1.4225) (xy -0.3286 -1.4196) (xy -0.3219 -1.417) (xy -0.3152 -1.4147)
						(xy -0.3083 -1.4128) (xy -0.3013 -1.4111) (xy -0.2941 -1.4099) (xy -0.2868 -1.4089) (xy -0.2794 -1.4084)
						(xy -0.272 -1.4082) (xy 0.2612 -1.4082) (xy 0.2687 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 1.4859 1.0153) (xy 1.4933 1.0147) (xy 1.5005 1.0138) (xy 1.5077 1.0125) (xy 1.5147 1.0109)
						(xy 1.5216 1.0089) (xy 1.5284 1.0067) (xy 1.535 1.0041) (xy 1.5414 1.0011) (xy 1.5477 0.9979)
						(xy 1.5538 0.9944) (xy 1.5597 0.9906) (xy 1.5654 0.9866) (xy 1.5709 0.9823) (xy 1.5762 0.9777)
						(xy 1.5812 0.9729) (xy 1.586 0.9678) (xy 1.5906 0.9626) (xy 1.5949 0.9571) (xy 1.599 0.9514) (xy 1.6028 0.9455)
						(xy 1.6063 0.9394) (xy 1.6095 0.9331) (xy 1.6124 0.9267) (xy 1.615 0.9201) (xy 1.6173 0.9133)
						(xy 1.6192 0.9064) (xy 1.6209 0.8994) (xy 1.6221 0.8922) (xy 1.6231 0.8849) (xy 1.6236 0.8776)
						(xy 1.6238 0.8701) (xy 1.6238 0.3369) (xy 1.6236 0.3294) (xy 1.6231 0.322) (xy 1.6221 0.3147)
						(xy 1.6209 0.3076) (xy 1.6192 0.3005) (xy 1.6173 0.2936) (xy 1.615 0.2869) (xy 1.6124 0.2803)
						(xy 1.6095 0.2738) (xy 1.6063 0.2676) (xy 1.6028 0.2615) (xy 1.599 0.2556) (xy 1.5949 0.2499)
						(xy 1.5906 0.2444) (xy 1.586 0.2391) (xy 1.5812 0.2341) (xy 1.5762 0.2292) (xy 1.5709 0.2247)
						(xy 1.5654 0.2203) (xy 1.5597 0.2163) (xy 1.5538 0.2125) (xy 1.5477 0.209) (xy 1.5414 0.2058)
						(xy 1.535 0.2029) (xy 1.5284 0.2003) (xy 1.5216 0.198) (xy 1.5147 0.196) (xy 1.5077 0.1944) (xy 1.5005 0.1931)
						(xy 1.4933 0.1922) (xy 1.4859 0.1916) (xy 1.4784 0.1914) (xy 0.9452 0.1914) (xy 0.9377 0.1916)
						(xy 0.9303 0.1922) (xy 0.9231 0.1931) (xy 0.9159 0.1944) (xy 0.9089 0.196) (xy 0.902 0.198) (xy 0.8952 0.2003)
						(xy 0.8886 0.2029) (xy 0.8822 0.2058) (xy 0.8759 0.209) (xy 0.8698 0.2125) (xy 0.8639 0.2163)
						(xy 0.8582 0.2203) (xy 0.8527 0.2247) (xy 0.8474 0.2292) (xy 0.8424 0.2341) (xy 0.8376 0.2391)
						(xy 0.833 0.2444) (xy 0.8287 0.2499) (xy 0.8246 0.2556) (xy 0.8208 0.2615) (xy 0.8173 0.2676)
						(xy 0.8141 0.2738) (xy 0.8112 0.2803) (xy 0.8086 0.2869) (xy 0.8063 0.2936) (xy 0.8044 0.3005)
						(xy 0.8027 0.3076) (xy 0.8015 0.3147) (xy 0.8005 0.322) (xy 0.8 0.3294) (xy 0.7998 0.3369) (xy 0.7998 0.8701)
						(xy 0.8 0.8776) (xy 0.8005 0.8849) (xy 0.8015 0.8922) (xy 0.8027 0.8994) (xy 0.8044 0.9064) (xy 0.8063 0.9133)
						(xy 0.8086 0.9201) (xy 0.8112 0.9267) (xy 0.8141 0.9331) (xy 0.8173 0.9394) (xy 0.8208 0.9455)
						(xy 0.8246 0.9514) (xy 0.8287 0.9571) (xy 0.833 0.9626) (xy 0.8376 0.9678) (xy 0.8424 0.9729)
						(xy 0.8474 0.9777) (xy 0.8527 0.9823) (xy 0.8582 0.9866) (xy 0.8639 0.9906) (xy 0.8698 0.9944)
						(xy 0.8759 0.9979) (xy 0.8822 1.0011) (xy 0.8886 1.0041) (xy 0.8952 1.0067) (xy 0.902 1.0089)
						(xy 0.9089 1.0109) (xy 0.9159 1.0125) (xy 0.9231 1.0138) (xy 0.9303 1.0147) (xy 0.9377 1.0153)
						(xy 0.9452 1.0155) (xy 1.4784 1.0155) (xy 1.4859 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 1.4859 -0.1965) (xy 1.4933 -0.1971) (xy 1.5005 -0.198) (xy 1.5077 -0.1993) (xy 1.5147 -0.2009)
						(xy 1.5216 -0.2029) (xy 1.5284 -0.2052) (xy 1.535 -0.2078) (xy 1.5414 -0.2107) (xy 1.5477 -0.2139)
						(xy 1.5538 -0.2174) (xy 1.5597 -0.2212) (xy 1.5654 -0.2253) (xy 1.5709 -0.2296) (xy 1.5762 -0.2341)
						(xy 1.5812 -0.239) (xy 1.586 -0.244) (xy 1.5906 -0.2493) (xy 1.5949 -0.2548) (xy 1.599 -0.2605)
						(xy 1.6028 -0.2664) (xy 1.6063 -0.2725) (xy 1.6095 -0.2787) (xy 1.6124 -0.2852) (xy 1.615 -0.2918)
						(xy 1.6173 -0.2985) (xy 1.6192 -0.3054) (xy 1.6209 -0.3125) (xy 1.6221 -0.3196) (xy 1.6231 -0.3269)
						(xy 1.6236 -0.3343) (xy 1.6238 -0.3418) (xy 1.6238 -0.875) (xy 1.6236 -0.8825) (xy 1.6231 -0.8898)
						(xy 1.6221 -0.8971) (xy 1.6209 -0.9043) (xy 1.6192 -0.9113) (xy 1.6173 -0.9182) (xy 1.615 -0.925)
						(xy 1.6124 -0.9316) (xy 1.6095 -0.938) (xy 1.6063 -0.9443) (xy 1.6028 -0.9504) (xy 1.599 -0.9563)
						(xy 1.5949 -0.962) (xy 1.5906 -0.9675) (xy 1.586 -0.9727) (xy 1.5812 -0.9778) (xy 1.5762 -0.9826)
						(xy 1.5709 -0.9872) (xy 1.5654 -0.9915) (xy 1.5597 -0.9956) (xy 1.5538 -0.9993) (xy 1.5477 -1.0028)
						(xy 1.5414 -1.006) (xy 1.535 -1.009) (xy 1.5284 -1.0116) (xy 1.5216 -1.0139) (xy 1.5147 -1.0158)
						(xy 1.5077 -1.0174) (xy 1.5005 -1.0187) (xy 1.4933 -1.0196) (xy 1.4859 -1.0202) (xy 1.4784 -1.0204)
						(xy 0.9452 -1.0204) (xy 0.9377 -1.0202) (xy 0.9303 -1.0196) (xy 0.9231 -1.0187) (xy 0.9159 -1.0174)
						(xy 0.9089 -1.0158) (xy 0.902 -1.0139) (xy 0.8952 -1.0116) (xy 0.8886 -1.009) (xy 0.8822 -1.006)
						(xy 0.8759 -1.0028) (xy 0.8698 -0.9993) (xy 0.8639 -0.9956) (xy 0.8582 -0.9915) (xy 0.8527 -0.9872)
						(xy 0.8474 -0.9826) (xy 0.8424 -0.9778) (xy 0.8376 -0.9727) (xy 0.833 -0.9675) (xy 0.8287 -0.962)
						(xy 0.8246 -0.9563) (xy 0.8208 -0.9504) (xy 0.8173 -0.9443) (xy 0.8141 -0.938) (xy 0.8112 -0.9316)
						(xy 0.8086 -0.925) (xy 0.8063 -0.9182) (xy 0.8044 -0.9113) (xy 0.8027 -0.9043) (xy 0.8015 -0.8971)
						(xy 0.8005 -0.8898) (xy 0.8 -0.8825) (xy 0.7998 -0.875) (xy 0.7998 -0.3418) (xy 0.8 -0.3343) (xy 0.8005 -0.3269)
						(xy 0.8015 -0.3196) (xy 0.8027 -0.3125) (xy 0.8044 -0.3054) (xy 0.8063 -0.2985) (xy 0.8086 -0.2918)
						(xy 0.8112 -0.2852) (xy 0.8141 -0.2787) (xy 0.8173 -0.2725) (xy 0.8208 -0.2664) (xy 0.8246 -0.2605)
						(xy 0.8287 -0.2548) (xy 0.833 -0.2493) (xy 0.8376 -0.244) (xy 0.8424 -0.239) (xy 0.8474 -0.2341)
						(xy 0.8527 -0.2296) (xy 0.8582 -0.2253) (xy 0.8639 -0.2212) (xy 0.8698 -0.2174) (xy 0.8759 -0.2139)
						(xy 0.8822 -0.2107) (xy 0.8886 -0.2078) (xy 0.8952 -0.2052) (xy 0.902 -0.2029) (xy 0.9089 -0.2009)
						(xy 0.9159 -0.1993) (xy 0.9231 -0.198) (xy 0.9303 -0.1971) (xy 0.9377 -0.1965) (xy 0.9452 -0.1964)
						(xy 1.4784 -0.1964) (xy 1.4859 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 1.4859 -1.4084) (xy 1.4933 -1.4089) (xy 1.5005 -1.4099) (xy 1.5077 -1.4111) (xy 1.5147 -1.4128)
						(xy 1.5216 -1.4147) (xy 1.5284 -1.417) (xy 1.535 -1.4196) (xy 1.5414 -1.4225) (xy 1.5477 -1.4257)
						(xy 1.5538 -1.4292) (xy 1.5597 -1.433) (xy 1.5654 -1.4371) (xy 1.5709 -1.4414) (xy 1.5762 -1.446)
						(xy 1.5812 -1.4508) (xy 1.586 -1.4558) (xy 1.5906 -1.4611) (xy 1.5949 -1.4666) (xy 1.599 -1.4723)
						(xy 1.6028 -1.4782) (xy 1.6063 -1.4843) (xy 1.6095 -1.4906) (xy 1.6124 -1.497) (xy 1.615 -1.5036)
						(xy 1.6173 -1.5104) (xy 1.6192 -1.5173) (xy 1.6209 -1.5243) (xy 1.6221 -1.5315) (xy 1.6231 -1.5387)
						(xy 1.6236 -1.5461) (xy 1.6238 -1.5536) (xy 1.6238 -2.0868) (xy 1.6236 -2.0943) (xy 1.6231 -2.1017)
						(xy 1.6221 -2.1089) (xy 1.6209 -2.1161) (xy 1.6192 -2.1231) (xy 1.6173 -2.13) (xy 1.615 -2.1368)
						(xy 1.6124 -2.1434) (xy 1.6095 -2.1498) (xy 1.6063 -2.1561) (xy 1.6028 -2.1622) (xy 1.599 -2.1681)
						(xy 1.5949 -2.1738) (xy 1.5906 -2.1793) (xy 1.586 -2.1846) (xy 1.5812 -2.1896) (xy 1.5762 -2.1944)
						(xy 1.5709 -2.199) (xy 1.5654 -2.2033) (xy 1.5597 -2.2074) (xy 1.5538 -2.2112) (xy 1.5477 -2.2147)
						(xy 1.5414 -2.2179) (xy 1.535 -2.2208) (xy 1.5284 -2.2234) (xy 1.5216 -2.2257) (xy 1.5147 -2.2276)
						(xy 1.5077 -2.2293) (xy 1.5005 -2.2306) (xy 1.4933 -2.2315) (xy 1.4859 -2.232) (xy 1.4784 -2.2322)
						(xy 0.9452 -2.2322) (xy 0.9377 -2.232) (xy 0.9303 -2.2315) (xy 0.9231 -2.2306) (xy 0.9159 -2.2293)
						(xy 0.9089 -2.2276) (xy 0.902 -2.2257) (xy 0.8952 -2.2234) (xy 0.8886 -2.2208) (xy 0.8822 -2.2179)
						(xy 0.8759 -2.2147) (xy 0.8698 -2.2112) (xy 0.8639 -2.2074) (xy 0.8582 -2.2033) (xy 0.8527 -2.199)
						(xy 0.8474 -2.1944) (xy 0.8424 -2.1896) (xy 0.8376 -2.1846) (xy 0.833 -2.1793) (xy 0.8287 -2.1738)
						(xy 0.8246 -2.1681) (xy 0.8208 -2.1622) (xy 0.8173 -2.1561) (xy 0.8141 -2.1498) (xy 0.8112 -2.1434)
						(xy 0.8086 -2.1368) (xy 0.8063 -2.13) (xy 0.8044 -2.1231) (xy 0.8027 -2.1161) (xy 0.8015 -2.1089)
						(xy 0.8005 -2.1017) (xy 0.8 -2.0943) (xy 0.7998 -2.0868) (xy 0.7998 -1.5536) (xy 0.8 -1.5461)
						(xy 0.8005 -1.5387) (xy 0.8015 -1.5315) (xy 0.8027 -1.5243) (xy 0.8044 -1.5173) (xy 0.8063 -1.5104)
						(xy 0.8086 -1.5036) (xy 0.8112 -1.497) (xy 0.8141 -1.4906) (xy 0.8173 -1.4843) (xy 0.8208 -1.4782)
						(xy 0.8246 -1.4723) (xy 0.8287 -1.4666) (xy 0.833 -1.4611) (xy 0.8376 -1.4558) (xy 0.8424 -1.4508)
						(xy 0.8474 -1.446) (xy 0.8527 -1.4414) (xy 0.8582 -1.4371) (xy 0.8639 -1.433) (xy 0.8698 -1.4292)
						(xy 0.8759 -1.4257) (xy 0.8822 -1.4225) (xy 0.8886 -1.4196) (xy 0.8952 -1.417) (xy 0.902 -1.4147)
						(xy 0.9089 -1.4128) (xy 0.9159 -1.4111) (xy 0.9231 -1.4099) (xy 0.9303 -1.4089) (xy 0.9377 -1.4084)
						(xy 0.9452 -1.4082) (xy 1.4784 -1.4082) (xy 1.4859 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6549 -2.6202) (xy 2.6643 -2.6208) (xy 2.6735 -2.6217) (xy 2.6826 -2.623) (xy 2.6916 -2.6246)
						(xy 2.7004 -2.6266) (xy 2.7089 -2.6288) (xy 2.7174 -2.6314) (xy 2.7255 -2.6344) (xy 2.7335 -2.6376)
						(xy 2.7413 -2.6411) (xy 2.7488 -2.6449) (xy 2.756 -2.6489) (xy 2.763 -2.6532) (xy 2.7697 -2.6578)
						(xy 2.7761 -2.6626) (xy 2.7823 -2.6677) (xy 2.7881 -2.673) (xy 2.7936 -2.6784) (xy 2.7987 -2.6841)
						(xy 2.8035 -2.69) (xy 2.808 -2.6961) (xy 2.8121 -2.7024) (xy 2.8158 -2.7088) (xy 2.8191 -2.7155)
						(xy 2.822 -2.7222) (xy 2.8245 -2.7291) (xy 2.8266 -2.7361) (xy 2.8282 -2.7433) (xy 2.8294 -2.7506)
						(xy 2.8301 -2.758) (xy 2.8303 -2.7654) (xy 2.8303 -3.2986) (xy 2.8301 -3.3061) (xy 2.8294 -3.3135)
						(xy 2.8282 -3.3208) (xy 2.8266 -3.3279) (xy 2.8245 -3.335) (xy 2.822 -3.3419) (xy 2.8191 -3.3486)
						(xy 2.8158 -3.3552) (xy 2.8121 -3.3617) (xy 2.808 -3.3679) (xy 2.8035 -3.374) (xy 2.7987 -3.3799)
						(xy 2.7936 -3.3856) (xy 2.7881 -3.3911) (xy 2.7823 -3.3964) (xy 2.7761 -3.4015) (xy 2.763 -3.4108)
						(xy 2.7488 -3.4192) (xy 2.7335 -3.4265) (xy 2.7174 -3.4326) (xy 2.7004 -3.4375) (xy 2.6826 -3.4411)
						(xy 2.6643 -3.4433) (xy 2.6549 -3.4439) (xy 2.6453 -3.4441) (xy 2.299 -3.4441) (xy 1.9671 -3.4441)
						(xy 1.1106 -3.4441) (xy 0.4323 -3.4441) (xy -0.4378 -3.4441) (xy -1.116 -3.4441) (xy -1.9725 -3.4441)
						(xy -2.3044 -3.4441) (xy -2.6508 -3.4441) (xy -2.6603 -3.4439) (xy -2.6697 -3.4433) (xy -2.6789 -3.4424)
						(xy -2.688 -3.4411) (xy -2.697 -3.4395) (xy -2.7058 -3.4375) (xy -2.7144 -3.4352) (xy -2.7228 -3.4326)
						(xy -2.731 -3.4297) (xy -2.7389 -3.4265) (xy -2.7467 -3.423) (xy -2.7542 -3.4192) (xy -2.7614 -3.4152)
						(xy -2.7684 -3.4108) (xy -2.7751 -3.4063) (xy -2.7816 -3.4015) (xy -2.7877 -3.3964) (xy -2.7935 -3.3911)
						(xy -2.799 -3.3856) (xy -2.8041 -3.3799) (xy -2.809 -3.374) (xy -2.8134 -3.3679) (xy -2.8175 -3.3617)
						(xy -2.8212 -3.3552) (xy -2.8245 -3.3486) (xy -2.8274 -3.3419) (xy -2.8299 -3.335) (xy -2.832 -3.3279)
						(xy -2.8336 -3.3208) (xy -2.8348 -3.3135) (xy -2.8355 -3.3061) (xy -2.8357 -3.2986) (xy -2.8357 -2.7654)
						(xy -2.8355 -2.758) (xy -2.8348 -2.7506) (xy -2.8336 -2.7433) (xy -2.832 -2.7361) (xy -2.8299 -2.7291)
						(xy -2.8274 -2.7222) (xy -2.8245 -2.7155) (xy -2.8212 -2.7088) (xy -2.8175 -2.7024) (xy -2.8134 -2.6961)
						(xy -2.809 -2.69) (xy -2.8041 -2.6841) (xy -2.799 -2.6784) (xy -2.7935 -2.673) (xy -2.7877 -2.6677)
						(xy -2.7816 -2.6626) (xy -2.7684 -2.6532) (xy -2.7542 -2.6449) (xy -2.7389 -2.6376) (xy -2.7228 -2.6314)
						(xy -2.7058 -2.6266) (xy -2.688 -2.623) (xy -2.6697 -2.6208) (xy -2.6603 -2.6202) (xy -2.6508 -2.62)
						(xy -2.3044 -2.62) (xy -1.9725 -2.62) (xy -1.116 -2.62) (xy -0.4378 -2.62) (xy 0.4323 -2.62) (xy 1.1106 -2.62)
						(xy 1.9671 -2.62) (xy 2.299 -2.62) (xy 2.6453 -2.62) (xy 2.6549 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6924 1.0326) (xy 2.6998 1.032) (xy 2.707 1.0311) (xy 2.7142 1.0298) (xy 2.7212 1.0282) (xy 2.7281 1.0262)
						(xy 2.7349 1.024) (xy 2.7415 1.0214) (xy 2.7479 1.0184) (xy 2.7542 1.0152) (xy 2.7603 1.0117)
						(xy 2.7662 1.0079) (xy 2.7719 1.0039) (xy 2.7774 0.9996) (xy 2.7827 0.995) (xy 2.7877 0.9902)
						(xy 2.7925 0.9851) (xy 2.7971 0.9798) (xy 2.8014 0.9744) (xy 2.8055 0.9687) (xy 2.8093 0.9628)
						(xy 2.8128 0.9567) (xy 2.816 0.9504) (xy 2.8189 0.944) (xy 2.8215 0.9373) (xy 2.8238 0.9306) (xy 2.8257 0.9237)
						(xy 2.8274 0.9167) (xy 2.8286 0.9095) (xy 2.8296 0.9022) (xy 2.8301 0.8948) (xy 2.8303 0.8874)
						(xy 2.8303 0.3542) (xy 2.8301 0.3467) (xy 2.8296 0.3393) (xy 2.8286 0.332) (xy 2.8274 0.3249)
						(xy 2.8257 0.3178) (xy 2.8238 0.3109) (xy 2.8215 0.3042) (xy 2.8189 0.2976) (xy 2.816 0.2911)
						(xy 2.8128 0.2849) (xy 2.8093 0.2788) (xy 2.8055 0.2729) (xy 2.8014 0.2672) (xy 2.7971 0.2617)
						(xy 2.7925 0.2564) (xy 2.7877 0.2513) (xy 2.7827 0.2465) (xy 2.7774 0.242) (xy 2.7719 0.2376)
						(xy 2.7662 0.2336) (xy 2.7603 0.2298) (xy 2.7542 0.2263) (xy 2.7479 0.2231) (xy 2.7415 0.2202)
						(xy 2.7349 0.2176) (xy 2.7281 0.2153) (xy 2.7212 0.2133) (xy 2.7142 0.2117) (xy 2.707 0.2104)
						(xy 2.6998 0.2095) (xy 2.6924 0.2089) (xy 2.6849 0.2087) (xy 2.1517 0.2087) (xy 2.1442 0.2089)
						(xy 2.1368 0.2095) (xy 2.1296 0.2104) (xy 2.1224 0.2117) (xy 2.1154 0.2133) (xy 2.1085 0.2153)
						(xy 2.1017 0.2176) (xy 2.0951 0.2202) (xy 2.0887 0.2231) (xy 2.0824 0.2263) (xy 2.0763 0.2298)
						(xy 2.0704 0.2336) (xy 2.0647 0.2376) (xy 2.0592 0.242) (xy 2.0539 0.2465) (xy 2.0489 0.2513)
						(xy 2.0441 0.2564) (xy 2.0395 0.2617) (xy 2.0352 0.2672) (xy 2.0311 0.2729) (xy 2.0273 0.2788)
						(xy 2.0238 0.2849) (xy 2.0206 0.2911) (xy 2.0177 0.2976) (xy 2.0151 0.3042) (xy 2.0128 0.3109)
						(xy 2.0109 0.3178) (xy 2.0092 0.3249) (xy 2.008 0.332) (xy 2.007 0.3393) (xy 2.0065 0.3467) (xy 2.0063 0.3542)
						(xy 2.0063 0.8874) (xy 2.0065 0.8948) (xy 2.007 0.9022) (xy 2.008 0.9095) (xy 2.0092 0.9167) (xy 2.0109 0.9237)
						(xy 2.0128 0.9306) (xy 2.0151 0.9373) (xy 2.0177 0.944) (xy 2.0206 0.9504) (xy 2.0238 0.9567)
						(xy 2.0273 0.9628) (xy 2.0311 0.9687) (xy 2.0352 0.9744) (xy 2.0395 0.9798) (xy 2.0441 0.9851)
						(xy 2.0489 0.9902) (xy 2.0539 0.995) (xy 2.0592 0.9996) (xy 2.0647 1.0039) (xy 2.0704 1.0079)
						(xy 2.0763 1.0117) (xy 2.0824 1.0152) (xy 2.0887 1.0184) (xy 2.0951 1.0214) (xy 2.1017 1.024)
						(xy 2.1085 1.0262) (xy 2.1154 1.0282) (xy 2.1224 1.0298) (xy 2.1296 1.0311) (xy 2.1368 1.032)
						(xy 2.1442 1.0326) (xy 2.1517 1.0328) (xy 2.6849 1.0328) (xy 2.6924 1.0326)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6924 1.0153) (xy 2.6998 1.0147) (xy 2.707 1.0138) (xy 2.7142 1.0125) (xy 2.7212 1.0109)
						(xy 2.7281 1.0089) (xy 2.7349 1.0067) (xy 2.7415 1.0041) (xy 2.7479 1.0011) (xy 2.7542 0.9979)
						(xy 2.7603 0.9944) (xy 2.7662 0.9906) (xy 2.7719 0.9866) (xy 2.7774 0.9823) (xy 2.7827 0.9777)
						(xy 2.7877 0.9729) (xy 2.7925 0.9678) (xy 2.7971 0.9626) (xy 2.8014 0.9571) (xy 2.8055 0.9514)
						(xy 2.8093 0.9455) (xy 2.8128 0.9394) (xy 2.816 0.9331) (xy 2.8189 0.9267) (xy 2.8215 0.9201)
						(xy 2.8238 0.9133) (xy 2.8257 0.9064) (xy 2.8274 0.8994) (xy 2.8286 0.8922) (xy 2.8296 0.8849)
						(xy 2.8301 0.8776) (xy 2.8303 0.8701) (xy 2.8303 0.3369) (xy 2.8301 0.3294) (xy 2.8296 0.322)
						(xy 2.8286 0.3147) (xy 2.8274 0.3076) (xy 2.8257 0.3005) (xy 2.8238 0.2936) (xy 2.8215 0.2869)
						(xy 2.8189 0.2803) (xy 2.816 0.2738) (xy 2.8128 0.2676) (xy 2.8093 0.2615) (xy 2.8055 0.2556)
						(xy 2.8014 0.2499) (xy 2.7971 0.2444) (xy 2.7925 0.2391) (xy 2.7877 0.2341) (xy 2.7827 0.2292)
						(xy 2.7774 0.2247) (xy 2.7719 0.2203) (xy 2.7662 0.2163) (xy 2.7603 0.2125) (xy 2.7542 0.209)
						(xy 2.7479 0.2058) (xy 2.7415 0.2029) (xy 2.7349 0.2003) (xy 2.7281 0.198) (xy 2.7212 0.196) (xy 2.7142 0.1944)
						(xy 2.707 0.1931) (xy 2.6998 0.1922) (xy 2.6924 0.1916) (xy 2.6849 0.1914) (xy 2.1517 0.1914)
						(xy 2.1442 0.1916) (xy 2.1368 0.1922) (xy 2.1296 0.1931) (xy 2.1224 0.1944) (xy 2.1154 0.196)
						(xy 2.1085 0.198) (xy 2.1017 0.2003) (xy 2.0951 0.2029) (xy 2.0887 0.2058) (xy 2.0824 0.209) (xy 2.0763 0.2125)
						(xy 2.0704 0.2163) (xy 2.0647 0.2203) (xy 2.0592 0.2247) (xy 2.0539 0.2292) (xy 2.0489 0.2341)
						(xy 2.0441 0.2391) (xy 2.0395 0.2444) (xy 2.0352 0.2499) (xy 2.0311 0.2556) (xy 2.0273 0.2615)
						(xy 2.0238 0.2676) (xy 2.0206 0.2738) (xy 2.0177 0.2803) (xy 2.0151 0.2869) (xy 2.0128 0.2936)
						(xy 2.0109 0.3005) (xy 2.0092 0.3076) (xy 2.008 0.3147) (xy 2.007 0.322) (xy 2.0065 0.3294) (xy 2.0063 0.3369)
						(xy 2.0063 0.8701) (xy 2.0065 0.8776) (xy 2.007 0.8849) (xy 2.008 0.8922) (xy 2.0092 0.8994) (xy 2.0109 0.9064)
						(xy 2.0128 0.9133) (xy 2.0151 0.9201) (xy 2.0177 0.9267) (xy 2.0206 0.9331) (xy 2.0238 0.9394)
						(xy 2.0273 0.9455) (xy 2.0311 0.9514) (xy 2.0352 0.9571) (xy 2.0395 0.9626) (xy 2.0441 0.9678)
						(xy 2.0489 0.9729) (xy 2.0539 0.9777) (xy 2.0592 0.9823) (xy 2.0647 0.9866) (xy 2.0704 0.9906)
						(xy 2.0763 0.9944) (xy 2.0824 0.9979) (xy 2.0887 1.0011) (xy 2.0951 1.0041) (xy 2.1017 1.0067)
						(xy 2.1085 1.0089) (xy 2.1154 1.0109) (xy 2.1224 1.0125) (xy 2.1296 1.0138) (xy 2.1368 1.0147)
						(xy 2.1442 1.0153) (xy 2.1517 1.0155) (xy 2.6849 1.0155) (xy 2.6924 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6924 -0.1965) (xy 2.6998 -0.1971) (xy 2.707 -0.198) (xy 2.7142 -0.1993) (xy 2.7212 -0.2009)
						(xy 2.7281 -0.2029) (xy 2.7349 -0.2052) (xy 2.7415 -0.2078) (xy 2.7479 -0.2107) (xy 2.7542 -0.2139)
						(xy 2.7603 -0.2174) (xy 2.7662 -0.2212) (xy 2.7719 -0.2253) (xy 2.7774 -0.2296) (xy 2.7827 -0.2341)
						(xy 2.7877 -0.239) (xy 2.7925 -0.244) (xy 2.7971 -0.2493) (xy 2.8014 -0.2548) (xy 2.8055 -0.2605)
						(xy 2.8093 -0.2664) (xy 2.8128 -0.2725) (xy 2.816 -0.2787) (xy 2.8189 -0.2852) (xy 2.8215 -0.2918)
						(xy 2.8238 -0.2985) (xy 2.8257 -0.3054) (xy 2.8274 -0.3125) (xy 2.8286 -0.3196) (xy 2.8296 -0.3269)
						(xy 2.8301 -0.3343) (xy 2.8303 -0.3418) (xy 2.8303 -0.875) (xy 2.8301 -0.8825) (xy 2.8296 -0.8898)
						(xy 2.8286 -0.8971) (xy 2.8274 -0.9043) (xy 2.8257 -0.9113) (xy 2.8238 -0.9182) (xy 2.8215 -0.925)
						(xy 2.8189 -0.9316) (xy 2.816 -0.938) (xy 2.8128 -0.9443) (xy 2.8093 -0.9504) (xy 2.8055 -0.9563)
						(xy 2.8014 -0.962) (xy 2.7971 -0.9675) (xy 2.7925 -0.9727) (xy 2.7877 -0.9778) (xy 2.7827 -0.9826)
						(xy 2.7774 -0.9872) (xy 2.7719 -0.9915) (xy 2.7662 -0.9956) (xy 2.7603 -0.9993) (xy 2.7542 -1.0028)
						(xy 2.7479 -1.006) (xy 2.7415 -1.009) (xy 2.7349 -1.0116) (xy 2.7281 -1.0139) (xy 2.7212 -1.0158)
						(xy 2.7142 -1.0174) (xy 2.707 -1.0187) (xy 2.6998 -1.0196) (xy 2.6924 -1.0202) (xy 2.6849 -1.0204)
						(xy 2.1517 -1.0204) (xy 2.1442 -1.0202) (xy 2.1368 -1.0196) (xy 2.1296 -1.0187) (xy 2.1224 -1.0174)
						(xy 2.1154 -1.0158) (xy 2.1085 -1.0139) (xy 2.1017 -1.0116) (xy 2.0951 -1.009) (xy 2.0887 -1.006)
						(xy 2.0824 -1.0028) (xy 2.0763 -0.9993) (xy 2.0704 -0.9956) (xy 2.0647 -0.9915) (xy 2.0592 -0.9872)
						(xy 2.0539 -0.9826) (xy 2.0489 -0.9778) (xy 2.0441 -0.9727) (xy 2.0395 -0.9675) (xy 2.0352 -0.962)
						(xy 2.0311 -0.9563) (xy 2.0273 -0.9504) (xy 2.0238 -0.9443) (xy 2.0206 -0.938) (xy 2.0177 -0.9316)
						(xy 2.0151 -0.925) (xy 2.0128 -0.9182) (xy 2.0109 -0.9113) (xy 2.0092 -0.9043) (xy 2.008 -0.8971)
						(xy 2.007 -0.8898) (xy 2.0065 -0.8825) (xy 2.0063 -0.875) (xy 2.0063 -0.3418) (xy 2.0065 -0.3343)
						(xy 2.007 -0.3269) (xy 2.008 -0.3196) (xy 2.0092 -0.3125) (xy 2.0109 -0.3054) (xy 2.0128 -0.2985)
						(xy 2.0151 -0.2918) (xy 2.0177 -0.2852) (xy 2.0206 -0.2787) (xy 2.0238 -0.2725) (xy 2.0273 -0.2664)
						(xy 2.0311 -0.2605) (xy 2.0352 -0.2548) (xy 2.0395 -0.2493) (xy 2.0441 -0.244) (xy 2.0489 -0.239)
						(xy 2.0539 -0.2341) (xy 2.0592 -0.2296) (xy 2.0647 -0.2253) (xy 2.0704 -0.2212) (xy 2.0763 -0.2174)
						(xy 2.0824 -0.2139) (xy 2.0887 -0.2107) (xy 2.0951 -0.2078) (xy 2.1017 -0.2052) (xy 2.1085 -0.2029)
						(xy 2.1154 -0.2009) (xy 2.1224 -0.1993) (xy 2.1296 -0.198) (xy 2.1368 -0.1971) (xy 2.1442 -0.1965)
						(xy 2.1517 -0.1964) (xy 2.6849 -0.1964) (xy 2.6924 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6924 -1.4084) (xy 2.6998 -1.4089) (xy 2.707 -1.4099) (xy 2.7142 -1.4111) (xy 2.7212 -1.4128)
						(xy 2.7281 -1.4147) (xy 2.7349 -1.417) (xy 2.7415 -1.4196) (xy 2.7479 -1.4225) (xy 2.7542 -1.4257)
						(xy 2.7603 -1.4292) (xy 2.7662 -1.433) (xy 2.7719 -1.4371) (xy 2.7774 -1.4414) (xy 2.7827 -1.446)
						(xy 2.7877 -1.4508) (xy 2.7925 -1.4558) (xy 2.7971 -1.4611) (xy 2.8014 -1.4666) (xy 2.8055 -1.4723)
						(xy 2.8093 -1.4782) (xy 2.8128 -1.4843) (xy 2.816 -1.4906) (xy 2.8189 -1.497) (xy 2.8215 -1.5036)
						(xy 2.8238 -1.5104) (xy 2.8257 -1.5173) (xy 2.8274 -1.5243) (xy 2.8286 -1.5315) (xy 2.8296 -1.5387)
						(xy 2.8301 -1.5461) (xy 2.8303 -1.5536) (xy 2.8303 -2.0868) (xy 2.8301 -2.0943) (xy 2.8296 -2.1017)
						(xy 2.8286 -2.1089) (xy 2.8274 -2.1161) (xy 2.8257 -2.1231) (xy 2.8238 -2.13) (xy 2.8215 -2.1368)
						(xy 2.8189 -2.1434) (xy 2.816 -2.1498) (xy 2.8128 -2.1561) (xy 2.8093 -2.1622) (xy 2.8055 -2.1681)
						(xy 2.8014 -2.1738) (xy 2.7971 -2.1793) (xy 2.7925 -2.1846) (xy 2.7877 -2.1896) (xy 2.7827 -2.1944)
						(xy 2.7774 -2.199) (xy 2.7719 -2.2033) (xy 2.7662 -2.2074) (xy 2.7603 -2.2112) (xy 2.7542 -2.2147)
						(xy 2.7479 -2.2179) (xy 2.7415 -2.2208) (xy 2.7349 -2.2234) (xy 2.7281 -2.2257) (xy 2.7212 -2.2276)
						(xy 2.7142 -2.2293) (xy 2.707 -2.2306) (xy 2.6998 -2.2315) (xy 2.6924 -2.232) (xy 2.6849 -2.2322)
						(xy 2.1517 -2.2322) (xy 2.1442 -2.232) (xy 2.1368 -2.2315) (xy 2.1296 -2.2306) (xy 2.1224 -2.2293)
						(xy 2.1154 -2.2276) (xy 2.1085 -2.2257) (xy 2.1017 -2.2234) (xy 2.0951 -2.2208) (xy 2.0887 -2.2179)
						(xy 2.0824 -2.2147) (xy 2.0763 -2.2112) (xy 2.0704 -2.2074) (xy 2.0647 -2.2033) (xy 2.0592 -2.199)
						(xy 2.0539 -2.1944) (xy 2.0489 -2.1896) (xy 2.0441 -2.1846) (xy 2.0395 -2.1793) (xy 2.0352 -2.1738)
						(xy 2.0311 -2.1681) (xy 2.0273 -2.1622) (xy 2.0238 -2.1561) (xy 2.0206 -2.1498) (xy 2.0177 -2.1434)
						(xy 2.0151 -2.1368) (xy 2.0128 -2.13) (xy 2.0109 -2.1231) (xy 2.0092 -2.1161) (xy 2.008 -2.1089)
						(xy 2.007 -2.1017) (xy 2.0065 -2.0943) (xy 2.0063 -2.0868) (xy 2.0063 -1.5536) (xy 2.0065 -1.5461)
						(xy 2.007 -1.5387) (xy 2.008 -1.5315) (xy 2.0092 -1.5243) (xy 2.0109 -1.5173) (xy 2.0128 -1.5104)
						(xy 2.0151 -1.5036) (xy 2.0177 -1.497) (xy 2.0206 -1.4906) (xy 2.0238 -1.4843) (xy 2.0273 -1.4782)
						(xy 2.0311 -1.4723) (xy 2.0352 -1.4666) (xy 2.0395 -1.4611) (xy 2.0441 -1.4558) (xy 2.0489 -1.4508)
						(xy 2.0539 -1.446) (xy 2.0592 -1.4414) (xy 2.0647 -1.4371) (xy 2.0704 -1.433) (xy 2.0763 -1.4292)
						(xy 2.0824 -1.4257) (xy 2.0887 -1.4225) (xy 2.0951 -1.4196) (xy 2.1017 -1.417) (xy 2.1085 -1.4147)
						(xy 2.1154 -1.4128) (xy 2.1224 -1.4111) (xy 2.1296 -1.4099) (xy 2.1368 -1.4089) (xy 2.1442 -1.4084)
						(xy 2.1517 -1.4082) (xy 2.6849 -1.4082) (xy 2.6924 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 3.9042 -0.1965) (xy 3.9116 -0.1971) (xy 3.9189 -0.198) (xy 3.926 -0.1993) (xy 3.9331 -0.2009)
						(xy 3.94 -0.2029) (xy 3.9467 -0.2052) (xy 3.9533 -0.2078) (xy 3.9598 -0.2107) (xy 3.966 -0.2139)
						(xy 3.9721 -0.2174) (xy 3.978 -0.2212) (xy 3.9837 -0.2253) (xy 3.9892 -0.2296) (xy 3.9945 -0.2341)
						(xy 3.9995 -0.239) (xy 4.0044 -0.244) (xy 4.0089 -0.2493) (xy 4.0133 -0.2548) (xy 4.0173 -0.2605)
						(xy 4.0211 -0.2664) (xy 4.0246 -0.2725) (xy 4.0278 -0.2787) (xy 4.0307 -0.2852) (xy 4.0333 -0.2918)
						(xy 4.0356 -0.2985) (xy 4.0376 -0.3054) (xy 4.0392 -0.3125) (xy 4.0405 -0.3196) (xy 4.0414 -0.3269)
						(xy 4.042 -0.3343) (xy 4.0422 -0.3418) (xy 4.0422 -0.875) (xy 4.042 -0.8825) (xy 4.0414 -0.8898)
						(xy 4.0405 -0.8971) (xy 4.0392 -0.9043) (xy 4.0376 -0.9113) (xy 4.0356 -0.9182) (xy 4.0333 -0.925)
						(xy 4.0307 -0.9316) (xy 4.0278 -0.938) (xy 4.0246 -0.9443) (xy 4.0211 -0.9504) (xy 4.0173 -0.9563)
						(xy 4.0133 -0.962) (xy 4.0089 -0.9675) (xy 4.0044 -0.9727) (xy 3.9995 -0.9778) (xy 3.9945 -0.9826)
						(xy 3.9892 -0.9872) (xy 3.9837 -0.9915) (xy 3.978 -0.9956) (xy 3.9721 -0.9993) (xy 3.966 -1.0028)
						(xy 3.9598 -1.006) (xy 3.9533 -1.009) (xy 3.9467 -1.0116) (xy 3.94 -1.0139) (xy 3.9331 -1.0158)
						(xy 3.926 -1.0174) (xy 3.9189 -1.0187) (xy 3.9116 -1.0196) (xy 3.9042 -1.0202) (xy 3.8967 -1.0204)
						(xy 3.3635 -1.0204) (xy 3.3561 -1.0202) (xy 3.3487 -1.0196) (xy 3.3414 -1.0187) (xy 3.3342 -1.0174)
						(xy 3.3272 -1.0158) (xy 3.3203 -1.0139) (xy 3.3135 -1.0116) (xy 3.3069 -1.009) (xy 3.3005 -1.006)
						(xy 3.2942 -1.0028) (xy 3.2881 -0.9993) (xy 3.2822 -0.9956) (xy 3.2765 -0.9915) (xy 3.271 -0.9872)
						(xy 3.2658 -0.9826) (xy 3.2607 -0.9778) (xy 3.2559 -0.9727) (xy 3.2513 -0.9675) (xy 3.247 -0.962)
						(xy 3.243 -0.9563) (xy 3.2392 -0.9504) (xy 3.2357 -0.9443) (xy 3.2325 -0.938) (xy 3.2295 -0.9316)
						(xy 3.2269 -0.925) (xy 3.2247 -0.9182) (xy 3.2227 -0.9113) (xy 3.2211 -0.9043) (xy 3.2198 -0.8971)
						(xy 3.2189 -0.8898) (xy 3.2183 -0.8825) (xy 3.2181 -0.875) (xy 3.2181 -0.3418) (xy 3.2183 -0.3343)
						(xy 3.2189 -0.3269) (xy 3.2198 -0.3196) (xy 3.2211 -0.3125) (xy 3.2227 -0.3054) (xy 3.2247 -0.2985)
						(xy 3.2269 -0.2918) (xy 3.2295 -0.2852) (xy 3.2325 -0.2787) (xy 3.2357 -0.2725) (xy 3.2392 -0.2664)
						(xy 3.243 -0.2605) (xy 3.247 -0.2548) (xy 3.2513 -0.2493) (xy 3.2559 -0.244) (xy 3.2607 -0.239)
						(xy 3.2658 -0.2341) (xy 3.271 -0.2296) (xy 3.2765 -0.2253) (xy 3.2822 -0.2212) (xy 3.2881 -0.2174)
						(xy 3.2942 -0.2139) (xy 3.3005 -0.2107) (xy 3.3069 -0.2078) (xy 3.3135 -0.2052) (xy 3.3203 -0.2029)
						(xy 3.3272 -0.2009) (xy 3.3342 -0.1993) (xy 3.3414 -0.198) (xy 3.3487 -0.1971) (xy 3.3561 -0.1965)
						(xy 3.3635 -0.1964) (xy 3.8967 -0.1964) (xy 3.9042 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 3.9042 -2.6029) (xy 3.9116 -2.6035) (xy 3.9189 -2.6044) (xy 3.926 -2.6057) (xy 3.9331 -2.6073)
						(xy 3.94 -2.6093) (xy 3.9467 -2.6115) (xy 3.9533 -2.6141) (xy 3.9598 -2.6171) (xy 3.966 -2.6203)
						(xy 3.9721 -2.6238) (xy 3.978 -2.6276) (xy 3.9837 -2.6316) (xy 3.9892 -2.6359) (xy 3.9945 -2.6405)
						(xy 3.9995 -2.6453) (xy 4.0044 -2.6504) (xy 4.0089 -2.6556) (xy 4.0133 -2.6611) (xy 4.0173 -2.6668)
						(xy 4.0211 -2.6727) (xy 4.0246 -2.6788) (xy 4.0278 -2.6851) (xy 4.0307 -2.6915) (xy 4.0333 -2.6981)
						(xy 4.0356 -2.7049) (xy 4.0376 -2.7118) (xy 4.0392 -2.7188) (xy 4.0405 -2.726) (xy 4.0414 -2.7333)
						(xy 4.042 -2.7406) (xy 4.0422 -2.7481) (xy 4.0422 -3.2813) (xy 4.042 -3.2888) (xy 4.0414 -3.2962)
						(xy 4.0405 -3.3035) (xy 4.0392 -3.3106) (xy 4.0376 -3.3177) (xy 4.0356 -3.3246) (xy 4.0333 -3.3313)
						(xy 4.0307 -3.3379) (xy 4.0278 -3.3444) (xy 4.0246 -3.3506) (xy 4.0211 -3.3567) (xy 4.0173 -3.3626)
						(xy 4.0133 -3.3683) (xy 4.0089 -3.3738) (xy 4.0044 -3.3791) (xy 3.9995 -3.3841) (xy 3.9945 -3.389)
						(xy 3.9892 -3.3935) (xy 3.9837 -3.3979) (xy 3.978 -3.4019) (xy 3.9721 -3.4057) (xy 3.966 -3.4092)
						(xy 3.9598 -3.4124) (xy 3.9533 -3.4153) (xy 3.9467 -3.4179) (xy 3.94 -3.4202) (xy 3.9331 -3.4222)
						(xy 3.926 -3.4238) (xy 3.9189 -3.4251) (xy 3.9116 -3.426) (xy 3.9042 -3.4266) (xy 3.8967 -3.4268)
						(xy 3.3635 -3.4268) (xy 3.3561 -3.4266) (xy 3.3487 -3.426) (xy 3.3414 -3.4251) (xy 3.3342 -3.4238)
						(xy 3.3272 -3.4222) (xy 3.3203 -3.4202) (xy 3.3135 -3.4179) (xy 3.3069 -3.4153) (xy 3.3005 -3.4124)
						(xy 3.2942 -3.4092) (xy 3.2881 -3.4057) (xy 3.2822 -3.4019) (xy 3.2765 -3.3979) (xy 3.271 -3.3935)
						(xy 3.2658 -3.389) (xy 3.2607 -3.3841) (xy 3.2559 -3.3791) (xy 3.2513 -3.3738) (xy 3.247 -3.3683)
						(xy 3.243 -3.3626) (xy 3.2392 -3.3567) (xy 3.2357 -3.3506) (xy 3.2325 -3.3444) (xy 3.2295 -3.3379)
						(xy 3.2269 -3.3313) (xy 3.2247 -3.3246) (xy 3.2227 -3.3177) (xy 3.2211 -3.3106) (xy 3.2198 -3.3035)
						(xy 3.2189 -3.2962) (xy 3.2183 -3.2888) (xy 3.2181 -3.2813) (xy 3.2181 -2.7481) (xy 3.2183 -2.7406)
						(xy 3.2189 -2.7333) (xy 3.2198 -2.726) (xy 3.2211 -2.7188) (xy 3.2227 -2.7118) (xy 3.2247 -2.7049)
						(xy 3.2269 -2.6981) (xy 3.2295 -2.6915) (xy 3.2325 -2.6851) (xy 3.2357 -2.6788) (xy 3.2392 -2.6727)
						(xy 3.243 -2.6668) (xy 3.247 -2.6611) (xy 3.2513 -2.6556) (xy 3.2559 -2.6504) (xy 3.2607 -2.6453)
						(xy 3.2658 -2.6405) (xy 3.271 -2.6359) (xy 3.2765 -2.6316) (xy 3.2822 -2.6276) (xy 3.2881 -2.6238)
						(xy 3.2942 -2.6203) (xy 3.3005 -2.6171) (xy 3.3069 -2.6141) (xy 3.3135 -2.6115) (xy 3.3203 -2.6093)
						(xy 3.3272 -2.6073) (xy 3.3342 -2.6057) (xy 3.3414 -2.6044) (xy 3.3487 -2.6035) (xy 3.3561 -2.6029)
						(xy 3.3635 -2.6027) (xy 3.8967 -2.6027) (xy 3.9042 -2.6029)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 3.9042 -2.6202) (xy 3.9116 -2.6208) (xy 3.9189 -2.6217) (xy 3.926 -2.623) (xy 3.9331 -2.6246)
						(xy 3.94 -2.6266) (xy 3.9467 -2.6288) (xy 3.9533 -2.6314) (xy 3.9598 -2.6344) (xy 3.966 -2.6376)
						(xy 3.9721 -2.6411) (xy 3.978 -2.6449) (xy 3.9837 -2.6489) (xy 3.9892 -2.6532) (xy 3.9945 -2.6578)
						(xy 3.9995 -2.6626) (xy 4.0044 -2.6677) (xy 4.0089 -2.673) (xy 4.0133 -2.6784) (xy 4.0173 -2.6841)
						(xy 4.0211 -2.69) (xy 4.0246 -2.6961) (xy 4.0278 -2.7024) (xy 4.0307 -2.7088) (xy 4.0333 -2.7155)
						(xy 4.0356 -2.7222) (xy 4.0376 -2.7291) (xy 4.0392 -2.7361) (xy 4.0405 -2.7433) (xy 4.0414 -2.7506)
						(xy 4.042 -2.758) (xy 4.0422 -2.7654) (xy 4.0422 -3.2986) (xy 4.042 -3.3061) (xy 4.0414 -3.3135)
						(xy 4.0405 -3.3208) (xy 4.0392 -3.3279) (xy 4.0376 -3.335) (xy 4.0356 -3.3419) (xy 4.0333 -3.3486)
						(xy 4.0307 -3.3552) (xy 4.0278 -3.3617) (xy 4.0246 -3.3679) (xy 4.0211 -3.374) (xy 4.0173 -3.3799)
						(xy 4.0133 -3.3856) (xy 4.0089 -3.3911) (xy 4.0044 -3.3964) (xy 3.9995 -3.4015) (xy 3.9945 -3.4063)
						(xy 3.9892 -3.4108) (xy 3.9837 -3.4152) (xy 3.978 -3.4192) (xy 3.9721 -3.423) (xy 3.966 -3.4265)
						(xy 3.9598 -3.4297) (xy 3.9533 -3.4326) (xy 3.9467 -3.4352) (xy 3.94 -3.4375) (xy 3.9331 -3.4395)
						(xy 3.926 -3.4411) (xy 3.9189 -3.4424) (xy 3.9116 -3.4433) (xy 3.9042 -3.4439) (xy 3.8967 -3.4441)
						(xy 3.3635 -3.4441) (xy 3.3561 -3.4439) (xy 3.3487 -3.4433) (xy 3.3414 -3.4424) (xy 3.3342 -3.4411)
						(xy 3.3272 -3.4395) (xy 3.3203 -3.4375) (xy 3.3135 -3.4352) (xy 3.3069 -3.4326) (xy 3.3005 -3.4297)
						(xy 3.2942 -3.4265) (xy 3.2881 -3.423) (xy 3.2822 -3.4192) (xy 3.2765 -3.4152) (xy 3.271 -3.4108)
						(xy 3.2658 -3.4063) (xy 3.2607 -3.4015) (xy 3.2559 -3.3964) (xy 3.2513 -3.3911) (xy 3.247 -3.3856)
						(xy 3.243 -3.3799) (xy 3.2392 -3.374) (xy 3.2357 -3.3679) (xy 3.2325 -3.3617) (xy 3.2295 -3.3552)
						(xy 3.2269 -3.3486) (xy 3.2247 -3.3419) (xy 3.2227 -3.335) (xy 3.2211 -3.3279) (xy 3.2198 -3.3208)
						(xy 3.2189 -3.3135) (xy 3.2183 -3.3061) (xy 3.2181 -3.2986) (xy 3.2181 -2.7654) (xy 3.2183 -2.758)
						(xy 3.2189 -2.7506) (xy 3.2198 -2.7433) (xy 3.2211 -2.7361) (xy 3.2227 -2.7291) (xy 3.2247 -2.7222)
						(xy 3.2269 -2.7155) (xy 3.2295 -2.7088) (xy 3.2325 -2.7024) (xy 3.2357 -2.6961) (xy 3.2392 -2.69)
						(xy 3.243 -2.6841) (xy 3.247 -2.6784) (xy 3.2513 -2.673) (xy 3.2559 -2.6677) (xy 3.2607 -2.6626)
						(xy 3.2658 -2.6578) (xy 3.271 -2.6532) (xy 3.2765 -2.6489) (xy 3.2822 -2.6449) (xy 3.2881 -2.6411)
						(xy 3.2942 -2.6376) (xy 3.3005 -2.6344) (xy 3.3069 -2.6314) (xy 3.3135 -2.6288) (xy 3.3203 -2.6266)
						(xy 3.3272 -2.6246) (xy 3.3342 -2.623) (xy 3.3414 -2.6217) (xy 3.3487 -2.6208) (xy 3.3561 -2.6202)
						(xy 3.3635 -2.62) (xy 3.8967 -2.62) (xy 3.9042 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 1.0153) (xy 5.1288 1.0147) (xy 5.136 1.0138) (xy 5.1432 1.0125) (xy 5.1502 1.0109)
						(xy 5.1571 1.0089) (xy 5.1639 1.0067) (xy 5.1705 1.004) (xy 5.1769 1.0011) (xy 5.1832 0.9979)
						(xy 5.1893 0.9944) (xy 5.1952 0.9906) (xy 5.2009 0.9866) (xy 5.2064 0.9823) (xy 5.2117 0.9777)
						(xy 5.2167 0.9729) (xy 5.2215 0.9678) (xy 5.2261 0.9625) (xy 5.2304 0.9571) (xy 5.2345 0.9513)
						(xy 5.2383 0.9454) (xy 5.2418 0.9394) (xy 5.245 0.9331) (xy 5.2479 0.9266) (xy 5.2505 0.92) (xy 5.2528 0.9133)
						(xy 5.2547 0.9064) (xy 5.2564 0.8994) (xy 5.2576 0.8922) (xy 5.2586 0.8849) (xy 5.2591 0.8775)
						(xy 5.2593 0.8701) (xy 5.2593 0.3369) (xy 5.2591 0.3294) (xy 5.2586 0.322) (xy 5.2576 0.3147)
						(xy 5.2564 0.3076) (xy 5.2547 0.3005) (xy 5.2528 0.2936) (xy 5.2505 0.2869) (xy 5.2479 0.2803)
						(xy 5.245 0.2738) (xy 5.2418 0.2676) (xy 5.2383 0.2615) (xy 5.2345 0.2556) (xy 5.2304 0.2499)
						(xy 5.2261 0.2444) (xy 5.2215 0.2391) (xy 5.2167 0.234) (xy 5.2117 0.2292) (xy 5.2064 0.2247)
						(xy 5.2009 0.2203) (xy 5.1952 0.2163) (xy 5.1893 0.2125) (xy 5.1832 0.209) (xy 5.1769 0.2058)
						(xy 5.1705 0.2029) (xy 5.1639 0.2003) (xy 5.1571 0.198) (xy 5.1502 0.196) (xy 5.1432 0.1944) (xy 5.136 0.1931)
						(xy 5.1288 0.1922) (xy 5.1214 0.1916) (xy 5.1139 0.1914) (xy 5.0226 0.1914) (xy 4.5807 0.1914)
						(xy 3.8967 0.1914) (xy 3.4549 0.1914) (xy 3.3635 0.1914) (xy 3.3561 0.1916) (xy 3.3487 0.1922)
						(xy 3.3414 0.1931) (xy 3.3342 0.1944) (xy 3.3272 0.196) (xy 3.3203 0.198) (xy 3.3135 0.2003) (xy 3.3069 0.2029)
						(xy 3.3005 0.2058) (xy 3.2942 0.209) (xy 3.2881 0.2125) (xy 3.2822 0.2163) (xy 3.2765 0.2203)
						(xy 3.271 0.2247) (xy 3.2658 0.2292) (xy 3.2607 0.234) (xy 3.2559 0.2391) (xy 3.2513 0.2444) (xy 3.247 0.2499)
						(xy 3.243 0.2556) (xy 3.2392 0.2615) (xy 3.2357 0.2676) (xy 3.2325 0.2738) (xy 3.2295 0.2803)
						(xy 3.2269 0.2869) (xy 3.2247 0.2936) (xy 3.2227 0.3005) (xy 3.2211 0.3076) (xy 3.2198 0.3147)
						(xy 3.2189 0.322) (xy 3.2183 0.3294) (xy 3.2181 0.3369) (xy 3.2181 0.8701) (xy 3.2183 0.8775)
						(xy 3.2189 0.8849) (xy 3.2198 0.8922) (xy 3.2211 0.8994) (xy 3.2227 0.9064) (xy 3.2247 0.9133)
						(xy 3.2269 0.92) (xy 3.2295 0.9266) (xy 3.2325 0.9331) (xy 3.2357 0.9394) (xy 3.2392 0.9454) (xy 3.243 0.9513)
						(xy 3.247 0.9571) (xy 3.2513 0.9625) (xy 3.2559 0.9678) (xy 3.2607 0.9729) (xy 3.2658 0.9777)
						(xy 3.271 0.9823) (xy 3.2765 0.9866) (xy 3.2822 0.9906) (xy 3.2881 0.9944) (xy 3.2942 0.9979)
						(xy 3.3005 1.0011) (xy 3.3069 1.004) (xy 3.3135 1.0067) (xy 3.3203 1.0089) (xy 3.3272 1.0109)
						(xy 3.3342 1.0125) (xy 3.3414 1.0138) (xy 3.3487 1.0147) (xy 3.3561 1.0153) (xy 3.3635 1.0155)
						(xy 3.4549 1.0155) (xy 3.8967 1.0155) (xy 4.5807 1.0155) (xy 5.0226 1.0155) (xy 5.1139 1.0155)
						(xy 5.1214 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 -0.1965) (xy 5.1288 -0.1971) (xy 5.136 -0.198) (xy 5.1432 -0.1993) (xy 5.1502 -0.2009)
						(xy 5.1571 -0.2029) (xy 5.1639 -0.2052) (xy 5.1705 -0.2078) (xy 5.1769 -0.2107) (xy 5.1832 -0.2139)
						(xy 5.1893 -0.2174) (xy 5.1952 -0.2212) (xy 5.2009 -0.2253) (xy 5.2064 -0.2296) (xy 5.2117 -0.2341)
						(xy 5.2167 -0.239) (xy 5.2215 -0.244) (xy 5.2261 -0.2493) (xy 5.2304 -0.2548) (xy 5.2345 -0.2605)
						(xy 5.2383 -0.2664) (xy 5.2418 -0.2725) (xy 5.245 -0.2787) (xy 5.2479 -0.2852) (xy 5.2505 -0.2918)
						(xy 5.2528 -0.2985) (xy 5.2547 -0.3054) (xy 5.2564 -0.3125) (xy 5.2576 -0.3196) (xy 5.2586 -0.3269)
						(xy 5.2591 -0.3343) (xy 5.2593 -0.3418) (xy 5.2593 -0.875) (xy 5.2591 -0.8825) (xy 5.2586 -0.8898)
						(xy 5.2576 -0.8971) (xy 5.2564 -0.9043) (xy 5.2547 -0.9113) (xy 5.2528 -0.9182) (xy 5.2505 -0.925)
						(xy 5.2479 -0.9316) (xy 5.245 -0.938) (xy 5.2418 -0.9443) (xy 5.2383 -0.9504) (xy 5.2345 -0.9563)
						(xy 5.2304 -0.962) (xy 5.2261 -0.9675) (xy 5.2215 -0.9727) (xy 5.2167 -0.9778) (xy 5.2117 -0.9826)
						(xy 5.2064 -0.9872) (xy 5.2009 -0.9915) (xy 5.1952 -0.9956) (xy 5.1893 -0.9993) (xy 5.1832 -1.0028)
						(xy 5.1769 -1.006) (xy 5.1705 -1.009) (xy 5.1639 -1.0116) (xy 5.1571 -1.0139) (xy 5.1502 -1.0158)
						(xy 5.1432 -1.0174) (xy 5.136 -1.0187) (xy 5.1288 -1.0196) (xy 5.1214 -1.0202) (xy 5.1139 -1.0204)
						(xy 4.5807 -1.0204) (xy 4.5732 -1.0202) (xy 4.5658 -1.0196) (xy 4.5586 -1.0187) (xy 4.5514 -1.0174)
						(xy 4.5444 -1.0158) (xy 4.5375 -1.0139) (xy 4.5307 -1.0116) (xy 4.5241 -1.009) (xy 4.5177 -1.006)
						(xy 4.5114 -1.0028) (xy 4.5053 -0.9993) (xy 4.4994 -0.9956) (xy 4.4937 -0.9915) (xy 4.4882 -0.9872)
						(xy 4.4829 -0.9826) (xy 4.4779 -0.9778) (xy 4.4731 -0.9727) (xy 4.4685 -0.9675) (xy 4.4642 -0.962)
						(xy 4.4601 -0.9563) (xy 4.4563 -0.9504) (xy 4.4528 -0.9443) (xy 4.4496 -0.938) (xy 4.4467 -0.9316)
						(xy 4.4441 -0.925) (xy 4.4418 -0.9182) (xy 4.4399 -0.9113) (xy 4.4382 -0.9043) (xy 4.437 -0.8971)
						(xy 4.436 -0.8898) (xy 4.4355 -0.8825) (xy 4.4353 -0.875) (xy 4.4353 -0.3418) (xy 4.4355 -0.3343)
						(xy 4.436 -0.3269) (xy 4.437 -0.3196) (xy 4.4382 -0.3125) (xy 4.4399 -0.3054) (xy 4.4418 -0.2985)
						(xy 4.4441 -0.2918) (xy 4.4467 -0.2852) (xy 4.4496 -0.2787) (xy 4.4528 -0.2725) (xy 4.4563 -0.2664)
						(xy 4.4601 -0.2605) (xy 4.4642 -0.2548) (xy 4.4685 -0.2493) (xy 4.4731 -0.244) (xy 4.4779 -0.239)
						(xy 4.4829 -0.2341) (xy 4.4882 -0.2296) (xy 4.4937 -0.2253) (xy 4.4994 -0.2212) (xy 4.5053 -0.2174)
						(xy 4.5114 -0.2139) (xy 4.5177 -0.2107) (xy 4.5241 -0.2078) (xy 4.5307 -0.2052) (xy 4.5375 -0.2029)
						(xy 4.5444 -0.2009) (xy 4.5514 -0.1993) (xy 4.5586 -0.198) (xy 4.5658 -0.1971) (xy 4.5732 -0.1965)
						(xy 4.5807 -0.1964) (xy 5.1139 -0.1964) (xy 5.1214 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 -1.4084) (xy 5.1288 -1.4089) (xy 5.136 -1.4099) (xy 5.1432 -1.4111) (xy 5.1502 -1.4128)
						(xy 5.1571 -1.4147) (xy 5.1639 -1.417) (xy 5.1705 -1.4196) (xy 5.1769 -1.4225) (xy 5.1832 -1.4257)
						(xy 5.1893 -1.4292) (xy 5.1952 -1.433) (xy 5.2009 -1.4371) (xy 5.2064 -1.4414) (xy 5.2117 -1.446)
						(xy 5.2167 -1.4508) (xy 5.2215 -1.4558) (xy 5.2261 -1.4611) (xy 5.2304 -1.4666) (xy 5.2345 -1.4723)
						(xy 5.2383 -1.4782) (xy 5.2418 -1.4843) (xy 5.245 -1.4906) (xy 5.2479 -1.497) (xy 5.2505 -1.5036)
						(xy 5.2528 -1.5104) (xy 5.2547 -1.5173) (xy 5.2564 -1.5243) (xy 5.2576 -1.5315) (xy 5.2586 -1.5387)
						(xy 5.2591 -1.5461) (xy 5.2593 -1.5536) (xy 5.2593 -2.0868) (xy 5.2591 -2.0943) (xy 5.2586 -2.1017)
						(xy 5.2576 -2.1089) (xy 5.2564 -2.1161) (xy 5.2547 -2.1231) (xy 5.2528 -2.13) (xy 5.2505 -2.1368)
						(xy 5.2479 -2.1434) (xy 5.245 -2.1498) (xy 5.2418 -2.1561) (xy 5.2383 -2.1622) (xy 5.2345 -2.1681)
						(xy 5.2304 -2.1738) (xy 5.2261 -2.1793) (xy 5.2215 -2.1846) (xy 5.2167 -2.1896) (xy 5.2117 -2.1944)
						(xy 5.2064 -2.199) (xy 5.2009 -2.2033) (xy 5.1952 -2.2074) (xy 5.1893 -2.2112) (xy 5.1832 -2.2147)
						(xy 5.1769 -2.2179) (xy 5.1705 -2.2208) (xy 5.1639 -2.2234) (xy 5.1571 -2.2257) (xy 5.1502 -2.2276)
						(xy 5.1432 -2.2293) (xy 5.136 -2.2306) (xy 5.1288 -2.2315) (xy 5.1214 -2.232) (xy 5.1139 -2.2322)
						(xy 5.0226 -2.2322) (xy 4.5807 -2.2322) (xy 3.8967 -2.2322) (xy 3.4549 -2.2322) (xy 3.3635 -2.2322)
						(xy 3.3561 -2.232) (xy 3.3487 -2.2315) (xy 3.3414 -2.2306) (xy 3.3342 -2.2293) (xy 3.3272 -2.2276)
						(xy 3.3203 -2.2257) (xy 3.3135 -2.2234) (xy 3.3069 -2.2208) (xy 3.3005 -2.2179) (xy 3.2942 -2.2147)
						(xy 3.2881 -2.2112) (xy 3.2822 -2.2074) (xy 3.2765 -2.2033) (xy 3.271 -2.199) (xy 3.2658 -2.1944)
						(xy 3.2607 -2.1896) (xy 3.2559 -2.1846) (xy 3.2513 -2.1793) (xy 3.247 -2.1738) (xy 3.243 -2.1681)
						(xy 3.2392 -2.1622) (xy 3.2357 -2.1561) (xy 3.2325 -2.1498) (xy 3.2295 -2.1434) (xy 3.2269 -2.1368)
						(xy 3.2247 -2.13) (xy 3.2227 -2.1231) (xy 3.2211 -2.1161) (xy 3.2198 -2.1089) (xy 3.2189 -2.1017)
						(xy 3.2183 -2.0943) (xy 3.2181 -2.0868) (xy 3.2181 -1.5536) (xy 3.2183 -1.5461) (xy 3.2189 -1.5387)
						(xy 3.2198 -1.5315) (xy 3.2211 -1.5243) (xy 3.2227 -1.5173) (xy 3.2247 -1.5104) (xy 3.2269 -1.5036)
						(xy 3.2295 -1.497) (xy 3.2325 -1.4906) (xy 3.2357 -1.4843) (xy 3.2392 -1.4782) (xy 3.243 -1.4723)
						(xy 3.247 -1.4666) (xy 3.2513 -1.4611) (xy 3.2559 -1.4558) (xy 3.2607 -1.4508) (xy 3.2658 -1.446)
						(xy 3.271 -1.4414) (xy 3.2765 -1.4371) (xy 3.2822 -1.433) (xy 3.2881 -1.4292) (xy 3.2942 -1.4257)
						(xy 3.3005 -1.4225) (xy 3.3069 -1.4196) (xy 3.3135 -1.417) (xy 3.3203 -1.4147) (xy 3.3272 -1.4128)
						(xy 3.3342 -1.4111) (xy 3.3414 -1.4099) (xy 3.3487 -1.4089) (xy 3.3561 -1.4084) (xy 3.3635 -1.4082)
						(xy 3.4549 -1.4082) (xy 3.8967 -1.4082) (xy 4.5807 -1.4082) (xy 5.0226 -1.4082) (xy 5.1139 -1.4082)
						(xy 5.1214 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 -2.6029) (xy 5.1288 -2.6035) (xy 5.136 -2.6044) (xy 5.1432 -2.6057) (xy 5.1502 -2.6073)
						(xy 5.1571 -2.6093) (xy 5.1639 -2.6115) (xy 5.1705 -2.6141) (xy 5.1769 -2.6171) (xy 5.1832 -2.6203)
						(xy 5.1893 -2.6238) (xy 5.1952 -2.6276) (xy 5.2009 -2.6316) (xy 5.2064 -2.6359) (xy 5.2117 -2.6405)
						(xy 5.2167 -2.6453) (xy 5.2215 -2.6504) (xy 5.2261 -2.6556) (xy 5.2304 -2.6611) (xy 5.2345 -2.6668)
						(xy 5.2383 -2.6727) (xy 5.2418 -2.6788) (xy 5.245 -2.6851) (xy 5.2479 -2.6915) (xy 5.2505 -2.6981)
						(xy 5.2528 -2.7049) (xy 5.2547 -2.7118) (xy 5.2564 -2.7188) (xy 5.2576 -2.726) (xy 5.2586 -2.7333)
						(xy 5.2591 -2.7406) (xy 5.2593 -2.7481) (xy 5.2593 -3.2813) (xy 5.2591 -3.2888) (xy 5.2586 -3.2962)
						(xy 5.2576 -3.3035) (xy 5.2564 -3.3106) (xy 5.2547 -3.3177) (xy 5.2528 -3.3246) (xy 5.2505 -3.3313)
						(xy 5.2479 -3.3379) (xy 5.245 -3.3444) (xy 5.2418 -3.3506) (xy 5.2383 -3.3567) (xy 5.2345 -3.3626)
						(xy 5.2304 -3.3683) (xy 5.2261 -3.3738) (xy 5.2215 -3.3791) (xy 5.2167 -3.3841) (xy 5.2117 -3.389)
						(xy 5.2064 -3.3935) (xy 5.2009 -3.3979) (xy 5.1952 -3.4019) (xy 5.1893 -3.4057) (xy 5.1832 -3.4092)
						(xy 5.1769 -3.4124) (xy 5.1705 -3.4153) (xy 5.1639 -3.4179) (xy 5.1571 -3.4202) (xy 5.1502 -3.4222)
						(xy 5.1432 -3.4238) (xy 5.136 -3.4251) (xy 5.1288 -3.426) (xy 5.1214 -3.4266) (xy 5.1139 -3.4268)
						(xy 4.5807 -3.4268) (xy 4.5732 -3.4266) (xy 4.5658 -3.426) (xy 4.5586 -3.4251) (xy 4.5514 -3.4238)
						(xy 4.5444 -3.4222) (xy 4.5375 -3.4202) (xy 4.5307 -3.4179) (xy 4.5241 -3.4153) (xy 4.5177 -3.4124)
						(xy 4.5114 -3.4092) (xy 4.5053 -3.4057) (xy 4.4994 -3.4019) (xy 4.4937 -3.3979) (xy 4.4882 -3.3935)
						(xy 4.4829 -3.389) (xy 4.4779 -3.3841) (xy 4.4731 -3.3791) (xy 4.4685 -3.3738) (xy 4.4642 -3.3683)
						(xy 4.4601 -3.3626) (xy 4.4563 -3.3567) (xy 4.4528 -3.3506) (xy 4.4496 -3.3444) (xy 4.4467 -3.3379)
						(xy 4.4441 -3.3313) (xy 4.4418 -3.3246) (xy 4.4399 -3.3177) (xy 4.4382 -3.3106) (xy 4.437 -3.3035)
						(xy 4.436 -3.2962) (xy 4.4355 -3.2888) (xy 4.4353 -3.2813) (xy 4.4353 -2.7481) (xy 4.4355 -2.7406)
						(xy 4.436 -2.7333) (xy 4.437 -2.726) (xy 4.4382 -2.7188) (xy 4.4399 -2.7118) (xy 4.4418 -2.7049)
						(xy 4.4441 -2.6981) (xy 4.4467 -2.6915) (xy 4.4496 -2.6851) (xy 4.4528 -2.6788) (xy 4.4563 -2.6727)
						(xy 4.4601 -2.6668) (xy 4.4642 -2.6611) (xy 4.4685 -2.6556) (xy 4.4731 -2.6504) (xy 4.4779 -2.6453)
						(xy 4.4829 -2.6405) (xy 4.4882 -2.6359) (xy 4.4937 -2.6316) (xy 4.4994 -2.6276) (xy 4.5053 -2.6238)
						(xy 4.5114 -2.6203) (xy 4.5177 -2.6171) (xy 4.5241 -2.6141) (xy 4.5307 -2.6115) (xy 4.5375 -2.6093)
						(xy 4.5444 -2.6073) (xy 4.5514 -2.6057) (xy 4.5586 -2.6044) (xy 4.5658 -2.6035) (xy 4.5732 -2.6029)
						(xy 4.5807 -2.6027) (xy 5.1139 -2.6027) (xy 5.1214 -2.6029)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 -2.6202) (xy 5.1288 -2.6208) (xy 5.136 -2.6217) (xy 5.1432 -2.623) (xy 5.1502 -2.6246)
						(xy 5.1571 -2.6266) (xy 5.1639 -2.6288) (xy 5.1705 -2.6314) (xy 5.1769 -2.6344) (xy 5.1832 -2.6376)
						(xy 5.1893 -2.6411) (xy 5.1952 -2.6449) (xy 5.2009 -2.6489) (xy 5.2064 -2.6532) (xy 5.2117 -2.6578)
						(xy 5.2167 -2.6626) (xy 5.2215 -2.6677) (xy 5.2261 -2.673) (xy 5.2304 -2.6784) (xy 5.2345 -2.6841)
						(xy 5.2383 -2.69) (xy 5.2418 -2.6961) (xy 5.245 -2.7024) (xy 5.2479 -2.7088) (xy 5.2505 -2.7155)
						(xy 5.2528 -2.7222) (xy 5.2547 -2.7291) (xy 5.2564 -2.7361) (xy 5.2576 -2.7433) (xy 5.2586 -2.7506)
						(xy 5.2591 -2.758) (xy 5.2593 -2.7654) (xy 5.2593 -3.2986) (xy 5.2591 -3.3061) (xy 5.2586 -3.3135)
						(xy 5.2576 -3.3208) (xy 5.2564 -3.3279) (xy 5.2547 -3.335) (xy 5.2528 -3.3419) (xy 5.2505 -3.3486)
						(xy 5.2479 -3.3552) (xy 5.245 -3.3617) (xy 5.2418 -3.3679) (xy 5.2383 -3.374) (xy 5.2345 -3.3799)
						(xy 5.2304 -3.3856) (xy 5.2261 -3.3911) (xy 5.2215 -3.3964) (xy 5.2167 -3.4015) (xy 5.2117 -3.4063)
						(xy 5.2064 -3.4108) (xy 5.2009 -3.4152) (xy 5.1952 -3.4192) (xy 5.1893 -3.423) (xy 5.1832 -3.4265)
						(xy 5.1769 -3.4297) (xy 5.1705 -3.4326) (xy 5.1639 -3.4352) (xy 5.1571 -3.4375) (xy 5.1502 -3.4395)
						(xy 5.1432 -3.4411) (xy 5.136 -3.4424) (xy 5.1288 -3.4433) (xy 5.1214 -3.4439) (xy 5.1139 -3.4441)
						(xy 4.5807 -3.4441) (xy 4.5732 -3.4439) (xy 4.5658 -3.4433) (xy 4.5586 -3.4424) (xy 4.5514 -3.4411)
						(xy 4.5444 -3.4395) (xy 4.5375 -3.4375) (xy 4.5307 -3.4352) (xy 4.5241 -3.4326) (xy 4.5177 -3.4297)
						(xy 4.5114 -3.4265) (xy 4.5053 -3.423) (xy 4.4994 -3.4192) (xy 4.4937 -3.4152) (xy 4.4882 -3.4108)
						(xy 4.4829 -3.4063) (xy 4.4779 -3.4015) (xy 4.4731 -3.3964) (xy 4.4685 -3.3911) (xy 4.4642 -3.3856)
						(xy 4.4601 -3.3799) (xy 4.4563 -3.374) (xy 4.4528 -3.3679) (xy 4.4496 -3.3617) (xy 4.4467 -3.3552)
						(xy 4.4441 -3.3486) (xy 4.4418 -3.3419) (xy 4.4399 -3.335) (xy 4.4382 -3.3279) (xy 4.437 -3.3208)
						(xy 4.436 -3.3135) (xy 4.4355 -3.3061) (xy 4.4353 -3.2986) (xy 4.4353 -2.7654) (xy 4.4355 -2.758)
						(xy 4.436 -2.7506) (xy 4.437 -2.7433) (xy 4.4382 -2.7361) (xy 4.4399 -2.7291) (xy 4.4418 -2.7222)
						(xy 4.4441 -2.7155) (xy 4.4467 -2.7088) (xy 4.4496 -2.7024) (xy 4.4528 -2.6961) (xy 4.4563 -2.69)
						(xy 4.4601 -2.6841) (xy 4.4642 -2.6784) (xy 4.4685 -2.673) (xy 4.4731 -2.6677) (xy 4.4779 -2.6626)
						(xy 4.4829 -2.6578) (xy 4.4882 -2.6532) (xy 4.4937 -2.6489) (xy 4.4994 -2.6449) (xy 4.5053 -2.6411)
						(xy 4.5114 -2.6376) (xy 4.5177 -2.6344) (xy 4.5241 -2.6314) (xy 4.5307 -2.6288) (xy 4.5375 -2.6266)
						(xy 4.5444 -2.6246) (xy 4.5514 -2.623) (xy 4.5586 -2.6217) (xy 4.5658 -2.6208) (xy 4.5732 -2.6202)
						(xy 4.5807 -2.62) (xy 5.1139 -2.62) (xy 5.1214 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Power_Protection:TPD3E001DRLR"
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at -5.08 6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "TPD3E001DRLR"
				(at 8.89 -7.62 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" "Package_TO_SOT_SMD:SOT-553"
				(at -17.78 -7.62 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "http://www.ti.com/lit/ds/symlink/tpd3e001.pdf"
				(at -5.08 6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "TPD3E001 Low-Capacitance 3-Channel ESD-Protection for High-Speed Data Interfaces"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "ESD-Protection"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "SOT?553*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "TPD3E001DRLR_0_1"
				(rectangle
					(start -7.62 5.08)
					(end 7.62 -5.08)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(polyline
					(pts
						(xy -7.62 0) (xy -7.112 0) (xy -7.112 4.572) (xy -5.08 4.572)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center -5.08 4.572)
					(radius 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -5.08 1.016) (xy -6.096 -1.016) (xy -4.064 -1.016) (xy -5.08 1.016)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -4.318 1.016) (xy -6.096 1.016) (xy -6.096 0.508)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center -2.54 4.572)
					(radius 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -2.54 4.572) (xy -2.54 -4.572)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -2.54 4.064) (xy -3.048 3.048) (xy -2.032 3.048) (xy -2.54 4.064)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center -2.54 2.54)
					(radius 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -2.54 2.54) (xy 3.81 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -2.54 2.032) (xy -3.048 1.016) (xy -2.032 1.016) (xy -2.54 2.032)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center -2.54 -4.572)
					(radius 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -2.032 4.064) (xy -3.048 4.064)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -2.032 2.032) (xy -3.048 2.032)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 4.572)
					(radius 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0 1.524) (xy -0.508 0.508) (xy 0.508 0.508) (xy 0 1.524)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 3.81 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0 -0.508) (xy -0.508 -1.524) (xy 0.508 -1.524) (xy 0 -0.508)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 -4.572)
					(radius 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0 -5.08) (xy 0 4.572)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.508 1.524) (xy -0.508 1.524)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.508 -0.508) (xy -0.508 -0.508)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 2.54 -1.016) (xy 2.032 -2.032) (xy 3.048 -2.032) (xy 2.54 -1.016)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 2.54 -2.54) (xy 3.81 -2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 2.54 -2.54)
					(radius 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.54 -3.048) (xy 2.032 -4.064) (xy 3.048 -4.064) (xy 2.54 -3.048)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 2.54 -4.572)
					(end -5.08 4.572)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 3.048 -1.016) (xy 2.032 -1.016)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 3.048 -3.048) (xy 2.032 -3.048)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "TPD3E001DRLR_1_1"
				(pin passive line
					(at -10.16 0 0)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -7.62 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 10.16 2.54 180)
					(length 2.54)
					(name "IO1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 10.16 0 180)
					(length 2.54)
					(name "IO2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 10.16 -2.54 180)
					(length 2.54)
					(name "IO3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "hlord2000-Device:Crystal_GND24_Small_EZRoute"
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "Y"
				(at 1.27 4.445 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "Crystal_GND24_Small_EZRoute"
				(at 1.27 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Four pin crystal, GND on pins 2 and 4, small symbol"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "quartz ceramic resonator oscillator"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Crystal*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Crystal_GND24_Small_EZRoute_0_1"
				(polyline
					(pts
						(xy -1.27 1.27) (xy -1.27 1.905) (xy 1.27 1.905) (xy 1.27 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -0.762) (xy -1.27 0.762)
					)
					(stroke
						(width 0.381)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -1.27) (xy -1.27 -1.905) (xy 1.27 -1.905) (xy 1.27 -1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -0.762 -1.524)
					(end 0.762 1.524)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -0.762) (xy 1.27 0.762)
					)
					(stroke
						(width 0.381)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "Crystal_GND24_Small_EZRoute_1_1"
				(pin passive line
					(at -2.54 0 0)
					(length 1.27)
					(name "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 0.762 0.762)
							)
						)
					)
				)
				(pin passive line
					(at 0 -2.54 90)
					(length 0.635)
					(name "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 0.762 0.762)
							)
						)
					)
				)
				(pin passive line
					(at 0 -2.54 90)
					(length 0.635)
					(hide yes)
					(name "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 0.762 0.762)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 0 180)
					(length 1.27)
					(name "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 0.762 0.762)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "nordic-lib-kicad-nrf52:nRF52833-QDXX"
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at 0 0 0)
				(do_not_autoplace)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "nRF52833-QDXX"
				(at 0 -2.54 0)
				(do_not_autoplace)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" "Package_DFN_QFN:QFN-40-1EP_5x5mm_P0.4mm_EP3.6x3.6mm"
				(at 5.08 35.56 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 5.08 35.56 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "nRF52833-QDXX_0_0"
				(pin unspecified line
					(at -25.4 45.72 0)
					(length 2.54)
					(name "D+"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "13"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 43.18 0)
					(length 2.54)
					(name "D-"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "12"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 33.02 0)
					(length 2.54)
					(name "XC1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "28"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 27.94 0)
					(length 2.54)
					(name "XC2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "29"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 17.78 0)
					(length 2.54)
					(name "P0.00/XL1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 12.7 0)
					(length 2.54)
					(name "P0.01/XL2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 2.54 0)
					(length 2.54)
					(name "P0.09/NFC1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "22"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 -2.54 0)
					(length 2.54)
					(name "P0.10/NFC2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "23"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 -12.7 0)
					(length 2.54)
					(name "P0.02/AIN0"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "32"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 -15.24 0)
					(length 2.54)
					(name "P0.03/AIN1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "31"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 -17.78 0)
					(length 2.54)
					(name "P0.04/AIN2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 -20.32 0)
					(length 2.54)
					(name "P0.05/AIN3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 -22.86 0)
					(length 2.54)
					(name "P0.11"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 -25.4 0)
					(length 2.54)
					(name "P0.15"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "14"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 -27.94 0)
					(length 2.54)
					(name "P0.17"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "15"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 -30.48 0)
					(length 2.54)
					(name "P0.20"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "17"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 -33.02 0)
					(length 2.54)
					(name "P0.28/AIN4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "33"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 -35.56 0)
					(length 2.54)
					(name "P0.29/AIN5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "34"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 -38.1 0)
					(length 2.54)
					(name "P0.30/AIN6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "35"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 -40.64 0)
					(length 2.54)
					(name "P0.31/AIN7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "36"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -25.4 -43.18 0)
					(length 2.54)
					(name "P1.09"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at -5.08 58.42 270)
					(length 2.54)
					(name "VBUS"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "10"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 0 58.42 270)
					(length 2.54)
					(name "VDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "18"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 2.54 58.42 270)
					(length 2.54)
					(name "DCC"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "39"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 7.62 58.42 270)
					(length 2.54)
					(name "VDDH"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 25.4 20.32 180)
					(length 2.54)
					(name "DECUSB"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "11"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 25.4 0 180)
					(length 2.54)
					(name "ANT"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "24"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 25.4 -30.48 180)
					(length 2.54)
					(name "SWDCLK"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "20"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 25.4 -33.02 180)
					(length 2.54)
					(name "SWDIO"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "19"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "nRF52833-QDXX_1_0"
				(pin unspecified line
					(at 0 58.42 270)
					(length 2.54)
					(hide yes)
					(name "VDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "30"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 0 58.42 270)
					(length 2.54)
					(hide yes)
					(name "VDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "40"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 0 58.42 270)
					(length 2.54)
					(hide yes)
					(name "VDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 0 -58.42 90)
					(length 2.54)
					(hide yes)
					(name "VSS_PA"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "25"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 0 -58.42 90)
					(length 2.54)
					(hide yes)
					(name "VSS"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "37"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -58.42 90)
					(length 2.54)
					(name "VSS"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "41"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 25.4 33.02 180)
					(length 2.54)
					(name "DEC1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 25.4 30.48 180)
					(length 2.54)
					(name "DEC3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "27"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 25.4 27.94 180)
					(length 2.54)
					(name "DEC4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "38"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 25.4 25.4 180)
					(length 2.54)
					(name "DEC5/NC"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "21"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 25.4 22.86 180)
					(length 2.54)
					(name "DEC6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "26"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin unspecified line
					(at 25.4 -25.4 180)
					(length 2.54)
					(name "P0.18/~{RESET}"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "16"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "nRF52833-QDXX_1_1"
				(rectangle
					(start -22.86 55.88)
					(end 22.86 -55.88)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:GND"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "GND"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "GND_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "GND_1_1"
				(pin power_in line
					(at 0 0 270)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:VBUS"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "VBUS"
				(at 0 3.556 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"VBUS\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VBUS_0_1"
				(polyline
					(pts
						(xy -0.762 1.27) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 2.54) (xy 0.762 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "VBUS_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:VDD"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "VDD"
				(at 0 3.556 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"VDD\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VDD_0_1"
				(polyline
					(pts
						(xy -0.762 1.27) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 2.54) (xy 0.762 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "VDD_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
	)
	(junction
		(at 208.28 24.13)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "0496cc5b-2587-4556-b8fc-************")
	)
	(junction
		(at 212.09 124.46)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "082ac196-5518-4b74-a9e2-0acf66cb7285")
	)
	(junction
		(at 228.6 78.74)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "123b8be6-02d7-42c8-9628-292b1a5d96f1")
	)
	(junction
		(at 144.78 60.96)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "1d3798ef-1580-442f-bb2a-4b0de95bd961")
	)
	(junction
		(at 231.14 29.21)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "251cf04e-b5fe-4d8e-b562-dede0b438f16")
	)
	(junction
		(at 95.25 39.37)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "273a915e-7cc0-465e-b59b-a0653d75a7da")
	)
	(junction
		(at 127 113.03)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "2b5c15cc-a841-4028-8e2e-5407843bfa0f")
	)
	(junction
		(at 219.71 29.21)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "2debe9e5-9509-486e-88e0-def77d74cb7e")
	)
	(junction
		(at 127 118.11)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "399614f8-4fcb-4753-a9be-ac69019e859b")
	)
	(junction
		(at 220.98 78.74)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "4066e148-c830-4a87-a9a1-c8e44f60f0b4")
	)
	(junction
		(at 133.35 63.5)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "466646ad-f840-4b20-b218-c020897e60a5")
	)
	(junction
		(at 92.71 96.52)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "52293118-1b2d-4328-9742-2888eaa24d84")
	)
	(junction
		(at 219.71 24.13)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "5483d51c-2f9e-479a-93e0-677ee59b95cb")
	)
	(junction
		(at 95.201 41.91)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "61768edc-80d9-45d6-bb24-89bfd100acc9")
	)
	(junction
		(at 175.26 24.13)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "6d2baa99-6306-4035-9e89-2d61e7e40b0d")
	)
	(junction
		(at 198.12 24.13)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "8b3b7c59-4b13-4941-8625-97065a5703de")
	)
	(junction
		(at 144.78 66.04)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "9418fad6-2f3a-4de8-ae26-4b47188620cb")
	)
	(junction
		(at 180.34 33.02)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "9950cd2b-e0b5-4e51-a949-94fad942c8cd")
	)
	(junction
		(at 212.09 71.12)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "99eb739f-a613-41b4-81a5-7433c4756151")
	)
	(junction
		(at 238.76 78.74)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "9fcfbf52-2320-430b-b3da-4923f9d9930f")
	)
	(junction
		(at 213.36 127)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "a4a1c60b-dfa6-4e70-a72f-45f9e6c3f33a")
	)
	(junction
		(at 101.6 96.52)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "ac4cd7ad-a8b1-438f-83ff-c4b69b21e33c")
	)
	(junction
		(at 74.93 62.23)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "ae2bdad0-fb25-4d63-90c4-4c22e35db739")
	)
	(junction
		(at 95.25 24.13)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "bc919f1d-beac-4398-b9ea-796955dc0266")
	)
	(junction
		(at 210.82 119.38)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "bdf9749f-0793-4ab2-a7c4-c2bda161a0d3")
	)
	(junction
		(at 208.28 29.21)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "e4c45aa2-392d-47db-8231-33b856d01f49")
	)
	(junction
		(at 90.17 41.91)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "e6f50609-1069-49ef-a239-9da1190836f7")
	)
	(junction
		(at 90.17 39.37)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "efaf1c7c-6aea-4987-a215-015a030c8c16")
	)
	(no_connect
		(at 187.96 35.56)
		(uuid "2360def9-7420-4716-a5b0-0509a8a7a6f8")
	)
	(no_connect
		(at 160.02 121.92)
		(uuid "2ff4bfc1-8f61-4ac6-ae7d-91a60a6babc0")
	)
	(no_connect
		(at 160.02 114.3)
		(uuid "30833784-a6c9-4e3b-be1e-27a0018361c0")
	)
	(no_connect
		(at 160.02 91.44)
		(uuid "41f45216-405b-4901-8645-c842abde30f4")
	)
	(no_connect
		(at 160.02 81.28)
		(uuid "46116810-394d-4fea-9eef-90c608a3d645")
	)
	(no_connect
		(at 90.17 54.61)
		(uuid "4882614f-9526-4b6e-8f32-c585d3559ea9")
	)
	(no_connect
		(at 160.02 119.38)
		(uuid "49d369ff-f619-4b5f-a954-602851b3ef30")
	)
	(no_connect
		(at 160.02 137.16)
		(uuid "4b99e575-7690-4576-a469-3863f075b950")
	)
	(no_connect
		(at 160.02 76.2)
		(uuid "54311caf-7f0e-42d4-ad23-80194afa9396")
	)
	(no_connect
		(at 160.02 111.76)
		(uuid "746a64cd-48dd-43e4-bb0e-a9e111e83596")
	)
	(no_connect
		(at 160.02 109.22)
		(uuid "b673ce98-f7a1-4e53-a595-a952c795ab49")
	)
	(no_connect
		(at 90.17 52.07)
		(uuid "b75f879d-87eb-4a0d-81ac-111ff2452556")
	)
	(no_connect
		(at 101.6 36.83)
		(uuid "bb2a8be5-9c46-4a1d-b247-c450e59515a4")
	)
	(no_connect
		(at 160.02 116.84)
		(uuid "df333a70-0906-4358-a99b-baf471da5166")
	)
	(no_connect
		(at 160.02 124.46)
		(uuid "e6072cb2-6ae3-47e0-8a58-0546060847b6")
	)
	(no_connect
		(at 210.82 93.98)
		(uuid "e83dc900-cf9e-4af0-9887-8f61f2addd85")
	)
	(no_connect
		(at 210.82 68.58)
		(uuid "ed31827f-3b45-4039-8407-10ecbd498357")
	)
	(no_connect
		(at 160.02 96.52)
		(uuid "eefde614-8b29-41e8-a30d-be1fc9fcbb32")
	)
	(no_connect
		(at 214.63 129.54)
		(uuid "f55edd72-9107-4926-b790-fb0e3a51a183")
	)
	(bus_entry
		(at 66.04 120.65)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "19bbe1e0-2c46-4470-b2b7-a5c1b1ae6761")
	)
	(bus_entry
		(at 257.81 30.48)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2083ec71-5513-4938-8da3-779b32886559")
	)
	(bus_entry
		(at 66.04 125.73)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "29b507f2-4b83-486e-be64-09b05e80917b")
	)
	(bus_entry
		(at 66.04 123.19)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "31d944fa-3372-4e67-9000-b748de866102")
	)
	(bus_entry
		(at 66.04 128.27)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "605a0cc6-d006-4691-8c6c-dca08e739876")
	)
	(bus_entry
		(at 66.04 130.81)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "936a54f9-3f76-49e1-a93f-9dffe9c29288")
	)
	(bus_entry
		(at 257.81 33.02)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ce3dcfce-fb44-4f00-b7d6-b92df47dfb8a")
	)
	(bus_entry
		(at 257.81 35.56)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d85e5aac-a380-4fbe-9c6d-a2e702b065bf")
	)
	(wire
		(pts
			(xy 68.58 118.11) (xy 85.09 118.11)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0082e6f3-6df7-4044-afef-56520c030593")
	)
	(wire
		(pts
			(xy 208.28 24.13) (xy 219.71 24.13)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "06d4a6df-d6e2-4ef9-9d19-5a8d4b56ddd2")
	)
	(wire
		(pts
			(xy 213.36 118.11) (xy 213.36 127)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "09db9b74-2683-474e-abfc-8f1c9d32eb95")
	)
	(wire
		(pts
			(xy 68.58 123.19) (xy 85.09 123.19)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0c610075-6136-4e80-bb2b-068dfe4f9d72")
	)
	(wire
		(pts
			(xy 198.12 24.13) (xy 208.28 24.13)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0e28eb49-e035-471d-b597-f5d18b059c4d")
	)
	(wire
		(pts
			(xy 257.81 33.02) (xy 256.54 33.02)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0f28070e-b5ff-468d-9fd6-cc5c6c282de0")
	)
	(wire
		(pts
			(xy 95.201 46.99) (xy 95.201 41.91)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "115c7a8e-4dc4-42dd-b1ad-56e2468d2b67")
	)
	(wire
		(pts
			(xy 95.201 41.91) (xy 101.6 41.91)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "156d9df0-ec44-4b1f-a334-f0da3c413f69")
	)
	(wire
		(pts
			(xy 97.79 96.52) (xy 97.79 105.41)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "16a55889-0dad-45a4-9537-92106afee820")
	)
	(wire
		(pts
			(xy 210.82 63.5) (xy 228.6 63.5)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1971a27f-e89f-4f6b-9d23-0d39c8d182ae")
	)
	(bus
		(pts
			(xy 66.04 125.73) (xy 66.04 128.27)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1b5dfba1-9664-43e5-99c4-8e1e59512d21")
	)
	(wire
		(pts
			(xy 138.43 66.04) (xy 144.78 66.04)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1bd1a7f7-62c3-4e6d-9334-7cc98944fba3")
	)
	(wire
		(pts
			(xy 138.43 118.11) (xy 138.43 113.03)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1d86c53e-1b59-4f12-82a1-************")
	)
	(bus
		(pts
			(xy 66.04 120.65) (xy 66.04 123.19)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1e3688df-4946-4269-b13f-e02ee77650b8")
	)
	(wire
		(pts
			(xy 219.71 24.13) (xy 231.14 24.13)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1fe2e332-91be-4f73-9644-fa88897c5e1f")
	)
	(wire
		(pts
			(xy 138.43 60.96) (xy 144.78 60.96)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "22aeb130-a657-47be-b4ce-ebd0ff6635c5")
	)
	(wire
		(pts
			(xy 129.54 125.73) (xy 124.46 125.73)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "24c18c84-0380-42c3-9601-57e3fa485555")
	)
	(wire
		(pts
			(xy 124.46 115.57) (xy 124.46 118.11)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2a410f99-8f9b-4337-86e2-91a3eb651c4b")
	)
	(wire
		(pts
			(xy 68.58 125.73) (xy 85.09 125.73)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2dd74155-9136-4954-be3c-0f7f134c9c7f")
	)
	(wire
		(pts
			(xy 133.35 60.96) (xy 133.35 63.5)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "3577803f-62d2-4616-a8e8-b9801bb77775")
	)
	(wire
		(pts
			(xy 92.71 96.52) (xy 92.71 105.41)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "365987ad-59a3-4a10-a584-e2d87c0314c7")
	)
	(wire
		(pts
			(xy 214.63 127) (xy 213.36 127)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "3afec7e4-4b6f-4656-a09c-3b72b1adaefb")
	)
	(bus
		(pts
			(xy 66.04 128.27) (xy 66.04 130.81)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "3ea47756-6d34-43f4-9313-9001d0d2df71")
	)
	(wire
		(pts
			(xy 157.48 50.8) (xy 160.02 50.8)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "40054e8a-0b76-400d-b0fb-6061860c3463")
	)
	(bus
		(pts
			(xy 260.35 27.94) (xy 260.35 30.48)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "41d56245-136b-4148-8ac8-0f2a4d51afec")
	)
	(wire
		(pts
			(xy 212.09 71.12) (xy 220.98 71.12)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "431b1a59-ad8f-4ab0-89d7-e0e2d8a6a82d")
	)
	(wire
		(pts
			(xy 133.35 63.5) (xy 133.35 66.04)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "452fed2c-8c3e-4ab8-a053-cf1ebca313b3")
	)
	(bus
		(pts
			(xy 260.35 30.48) (xy 260.35 33.02)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4c53d2f8-d27e-4721-a154-878fd9c51849")
	)
	(wire
		(pts
			(xy 127 96.52) (xy 127 113.03)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "51a9bf8d-30fa-43a0-a100-61c656002910")
	)
	(wire
		(pts
			(xy 185.42 24.13) (xy 185.42 35.56)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "52d39d86-0cc4-433e-a33b-58f0c81673ae")
	)
	(wire
		(pts
			(xy 78.74 107.95) (xy 78.74 115.57)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "563147c0-7fde-4075-8789-7b65bfadb3c6")
	)
	(wire
		(pts
			(xy 121.92 24.13) (xy 95.25 24.13)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "58000f6d-1160-4195-b30d-d28dcf27bd00")
	)
	(wire
		(pts
			(xy 90.17 41.91) (xy 90.17 44.45)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5be11a5b-5531-4981-973a-fe051d056833")
	)
	(wire
		(pts
			(xy 90.17 31.75) (xy 100.33 31.75)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5e5ee85b-4758-44e1-8d22-91285ce0a29d")
	)
	(wire
		(pts
			(xy 90.17 41.91) (xy 95.201 41.91)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5fb8fe39-bad5-4ef2-bce9-d57743a372f9")
	)
	(wire
		(pts
			(xy 90.17 39.37) (xy 95.25 39.37)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5fbbf6ca-23d9-44bb-b84a-7b5b3d3ae90a")
	)
	(wire
		(pts
			(xy 257.81 35.56) (xy 256.54 35.56)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6035fc24-6ed7-43f0-9c2a-89053a6744ad")
	)
	(wire
		(pts
			(xy 124.46 118.11) (xy 127 118.11)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "61702140-9b15-422a-b587-20e118ca481c")
	)
	(wire
		(pts
			(xy 138.43 123.19) (xy 129.54 123.19)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6245916b-f20c-4a06-9750-11762b94e599")
	)
	(wire
		(pts
			(xy 157.48 48.26) (xy 160.02 48.26)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "62571254-c2ee-4f2b-8d71-3fcf8ae46d40")
	)
	(wire
		(pts
			(xy 210.82 78.74) (xy 220.98 78.74)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "639492a7-121b-43f4-b841-4df4555e82d9")
	)
	(wire
		(pts
			(xy 101.6 96.52) (xy 127 96.52)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "67bd93f0-2c4e-40d1-a99b-642ec98d2999")
	)
	(wire
		(pts
			(xy 68.58 128.27) (xy 85.09 128.27)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "68662be6-cde7-46cf-b600-37745eb0017c")
	)
	(wire
		(pts
			(xy 208.28 29.21) (xy 219.71 29.21)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6a0eadae-22e9-4155-9961-f50e32d41a68")
	)
	(wire
		(pts
			(xy 220.98 78.74) (xy 228.6 78.74)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6ae86336-43bf-45dd-84a8-4e94fdedb60b")
	)
	(wire
		(pts
			(xy 95.25 24.13) (xy 90.17 24.13)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "72f66f15-0510-4427-8e71-30ab7449e657")
	)
	(wire
		(pts
			(xy 129.54 123.19) (xy 129.54 125.73)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "73ceeea5-494c-4dce-ab83-db3247c9f4be")
	)
	(wire
		(pts
			(xy 105.41 113.03) (xy 127 113.03)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "762bd633-9578-4294-b3e3-a88da74d4c82")
	)
	(wire
		(pts
			(xy 224.79 110.49) (xy 224.79 114.3)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "797337d0-74d0-418c-b8af-4d2b6a362ad0")
	)
	(wire
		(pts
			(xy 238.76 66.04) (xy 238.76 78.74)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "79c04f6e-bf29-459f-a659-18e1dc8ea951")
	)
	(wire
		(pts
			(xy 180.34 24.13) (xy 180.34 33.02)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "7a730dc5-fe10-4856-b95e-070d205804ae")
	)
	(wire
		(pts
			(xy 92.71 96.52) (xy 88.9 96.52)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "8182e015-0e3f-4cb4-8afb-aad0d5a3d488")
	)
	(wire
		(pts
			(xy 257.81 30.48) (xy 256.54 30.48)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "85c89f14-06a2-499f-826e-63676a2ac945")
	)
	(wire
		(pts
			(xy 124.46 125.73) (xy 124.46 123.19)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "8c29f549-ae1d-4966-942e-61962e1c8537")
	)
	(wire
		(pts
			(xy 210.82 66.04) (xy 212.09 66.04)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "930cc8fc-e6ee-4e58-8404-10a5f546edcf")
	)
	(wire
		(pts
			(xy 212.09 66.04) (xy 212.09 71.12)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9f3d016e-bd42-4e12-9b8b-ec5edc041286")
	)
	(wire
		(pts
			(xy 180.34 33.02) (xy 193.04 33.02)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a62fe9c0-9777-4173-8c8a-9dbb0088a1d9")
	)
	(wire
		(pts
			(xy 210.82 113.03) (xy 210.82 119.38)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a83991c9-0a7d-4f2b-9a2f-4f75178e9194")
	)
	(wire
		(pts
			(xy 193.04 33.02) (xy 193.04 35.56)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "af296f93-4566-4cc2-a8e3-a5f095ed3d25")
	)
	(wire
		(pts
			(xy 68.58 120.65) (xy 85.09 120.65)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b4b42165-6a09-45e4-abf4-e6be9b5bc6c5")
	)
	(wire
		(pts
			(xy 121.92 24.13) (xy 121.92 39.37)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b52905ae-2ef7-4fe9-98e8-bd1810c0c9cd")
	)
	(wire
		(pts
			(xy 105.41 115.57) (xy 124.46 115.57)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b540bf5a-2929-4514-913d-2a77425d1b71")
	)
	(wire
		(pts
			(xy 144.78 60.96) (xy 160.02 60.96)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b8614b34-7953-49ee-b8eb-a6e38c601e4e")
	)
	(wire
		(pts
			(xy 219.71 29.21) (xy 231.14 29.21)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "bcb438b6-7b84-4ac2-961a-b1ca3357ad0c")
	)
	(wire
		(pts
			(xy 101.6 96.52) (xy 97.79 96.52)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "bd66dfa9-1f45-4dfc-8515-baa50e85981b")
	)
	(wire
		(pts
			(xy 175.26 24.13) (xy 180.34 24.13)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "bd800510-ad40-4f73-9114-df4d96b2239a")
	)
	(wire
		(pts
			(xy 214.63 124.46) (xy 212.09 124.46)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "be524f75-39e3-4311-ab52-05e8043f416d")
	)
	(wire
		(pts
			(xy 212.09 124.46) (xy 210.82 124.46)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "beaf093e-4b13-4a06-9392-6c80c4fa75d6")
	)
	(wire
		(pts
			(xy 78.74 115.57) (xy 85.09 115.57)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "c21ff87d-5207-4771-86f4-d120aaaf087a")
	)
	(wire
		(pts
			(xy 95.25 39.37) (xy 101.6 39.37)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "c2557194-ef2b-45c6-83b8-0391a68ce3ec")
	)
	(wire
		(pts
			(xy 67.31 62.23) (xy 74.93 62.23)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "c6979b1c-f434-46f9-992e-1a44371d0a8c")
	)
	(wire
		(pts
			(xy 210.82 60.96) (xy 238.76 60.96)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "c97c5855-0537-4fc7-b6e5-fe32c163c8c1")
	)
	(wire
		(pts
			(xy 90.17 36.83) (xy 90.17 39.37)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ca4260a0-9a27-4d30-9f6f-0d75119a4cc2")
	)
	(wire
		(pts
			(xy 185.42 24.13) (xy 198.12 24.13)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cadf26cc-b5fb-4735-8d50-1223ad7df2fc")
	)
	(wire
		(pts
			(xy 228.6 78.74) (xy 238.76 78.74)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cb9138b4-f7af-4bdb-b8af-b058b9324aa8")
	)
	(wire
		(pts
			(xy 213.36 127) (xy 210.82 127)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cebdaf5b-9a08-4515-82a4-34b16a7b9641")
	)
	(wire
		(pts
			(xy 124.46 123.19) (xy 105.41 123.19)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cf5789e4-7caa-406d-bdf2-210bc4985629")
	)
	(wire
		(pts
			(xy 210.82 71.12) (xy 212.09 71.12)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d1120353-bb9d-4729-a8d0-dce52413a01d")
	)
	(wire
		(pts
			(xy 133.35 63.5) (xy 142.24 63.5)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d12daa7f-b743-44c2-851d-dcd509082b07")
	)
	(wire
		(pts
			(xy 180.34 33.02) (xy 180.34 35.56)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d27f3481-6dcc-4856-9798-a4c5425ce980")
	)
	(wire
		(pts
			(xy 95.25 34.29) (xy 95.25 39.37)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d4710fce-d093-47c0-8775-8915bb856f68")
	)
	(wire
		(pts
			(xy 138.43 113.03) (xy 127 113.03)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d5bb1573-18cd-4231-a02c-ea983c25fbe7")
	)
	(wire
		(pts
			(xy 228.6 68.58) (xy 228.6 78.74)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "db897ce1-208e-451e-9f61-92eac5f8ec4b")
	)
	(wire
		(pts
			(xy 212.09 115.57) (xy 212.09 124.46)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e65a7c9e-ead9-4191-a7b2-ede6329162cb")
	)
	(wire
		(pts
			(xy 144.78 66.04) (xy 160.02 66.04)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e9866ae4-f4d6-4d0c-b9eb-99334d4571d6")
	)
	(wire
		(pts
			(xy 82.55 107.95) (xy 78.74 107.95)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ed40e367-2c64-4afb-a7be-7c055187920e")
	)
	(wire
		(pts
			(xy 130.81 63.5) (xy 133.35 63.5)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f100822a-903a-407f-94f3-05117d384bcf")
	)
	(wire
		(pts
			(xy 214.63 119.38) (xy 210.82 119.38)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f5a7d5d6-4ea9-40fb-8c33-a012c81e693e")
	)
	(wire
		(pts
			(xy 198.12 29.21) (xy 208.28 29.21)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f5d42374-8c82-41b7-b8b2-efa4bc618035")
	)
	(wire
		(pts
			(xy 220.98 76.2) (xy 220.98 78.74)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "faf54525-3306-4e52-b894-ef613aae6221")
	)
	(label "SWD_TRG.SWDCLK"
		(at 85.09 128.27 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "04e06066-266b-47e0-8e3c-dafca8893275")
	)
	(label "UART_TX_NRF52"
		(at 160.02 132.08 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "053a4cbd-e688-4bf3-ab84-7d15be80caa8")
	)
	(label "D-"
		(at 157.48 50.8 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "0887df8c-35e8-49d0-87ca-a7ba2b794174")
	)
	(label "D+"
		(at 157.48 48.26 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "1ab078dc-8935-4f0c-b147-4a0a1f6af118")
	)
	(label "USB.VBUS"
		(at 121.92 24.13 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "1c0f6ea3-9c51-4a0f-b9c2-f77d5185d43f")
	)
	(label "nRF52_VDD"
		(at 97.79 96.52 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "1e2a9b6f-8b64-4a99-96d0-c504774dbd2d")
	)
	(label "UART_TX_NRF52"
		(at 105.41 120.65 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "2806ef9d-7180-4bc1-8778-92131b003692")
	)
	(label "USB.CC1"
		(at 90.17 29.21 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "2abe9e6f-2168-48ba-a87b-06a60786b640")
	)
	(label "USB.CC2"
		(at 90.17 31.75 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "3b79d5e3-78c9-48e1-9e9f-48bd75fc5892")
	)
	(label "D-"
		(at 90.17 39.37 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "439bb80d-c526-4a74-89d4-a6fe849a7035")
	)
	(label "nRF52_VDD"
		(at 224.79 114.3 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "5ee6c253-c268-44df-98a3-cf239abeab2c")
	)
	(label "SWD_TRG.~{RESET}"
		(at 85.09 123.19 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "6847d8ef-e7c8-4ae0-b372-c74a0ea5b523")
	)
	(label "UART_RX_NRF52"
		(at 105.41 118.11 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "69b93089-9fcb-4faa-8082-6637c86215da")
	)
	(label "TRG_SWDCLK_NRF52"
		(at 105.41 128.27 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "7d72a5ce-fcef-4c94-9eb9-5b9600cd0d7d")
	)
	(label "USB.CC2"
		(at 256.54 35.56 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "83132603-3c98-4e85-87e9-5531a2d9640d")
	)
	(label "TRG_SWDCLK_NRF52"
		(at 160.02 106.68 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "84e36316-da33-4d09-a128-c26bbf60a5e1")
	)
	(label "TRG_~{RESET}_NRF52"
		(at 160.02 129.54 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "85af5d46-c41f-4329-9ed0-618b0f98fdb1")
	)
	(label "TRG_SWDIO_NRF52"
		(at 160.02 127 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "9af78a69-2e22-401d-ad85-22706621eb94")
	)
	(label "UART_TRG.TX"
		(at 85.09 120.65 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "a0c3fde0-93b7-461e-ad15-e5645f33e62a")
	)
	(label "nRF52_VDD"
		(at 185.42 24.13 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "a63abc41-7b99-463d-a035-3d8a70c7bf75")
	)
	(label "UART_TRG.RX"
		(at 85.09 118.11 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "b1e0411a-78e9-44b4-ab51-e215bcf20482")
	)
	(label "USB.CC1"
		(at 256.54 33.02 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "b4cf6668-13be-47cb-bc64-9ba249738f59")
	)
	(label "SWD_TRG.SWDIO"
		(at 85.09 125.73 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "b6483f12-2cfb-4063-847e-d4a645921217")
	)
	(label "USB.VBUS"
		(at 256.54 30.48 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "bb678332-cf2a-446c-85ed-84702d0815a7")
	)
	(label "UART_RX_NRF52"
		(at 160.02 134.62 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "eb1f7946-3560-4937-b0c0-15b983e3a8b6")
	)
	(label "TRG_SWDIO_NRF52"
		(at 105.41 125.73 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "f63f8cfc-02e3-4cd2-a0c1-74f622207052")
	)
	(label "TRG_~{RESET}_NRF52"
		(at 105.41 123.19 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "f7112cb5-fe5d-4385-864e-13a0b0040495")
	)
	(label "D+"
		(at 90.17 41.91 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "f831ce21-3f03-4a12-86eb-d7276abe37dc")
	)
	(hierarchical_label "UART_TRG{TX, RX}"
		(shape input)
		(at 66.04 123.19 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "03da3dcc-70a8-492b-ba98-a979b466151e")
	)
	(hierarchical_label "USB{VBUS, CC1, CC2}"
		(shape input)
		(at 260.35 27.94 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "351955dd-5e99-40b9-a316-5b1a71ef854c")
	)
	(hierarchical_label "SWD_TRG{~{RESET}, SWDIO, SWDCLK}"
		(shape input)
		(at 66.04 130.81 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "b4aa6668-a178-4696-9687-23f030616d2e")
	)
	(rule_area
		(polyline
			(pts
				(xy 157.48 45.72) (xy 157.48 52.07) (xy 160.02 52.07) (xy 160.02 45.72)
			)
			(stroke
				(width 0)
				(type dash)
			)
			(fill
				(type none)
			)
			(uuid 0697da67-542b-46e0-bfe3-45be181e1f51)
		)
	)
	(rule_area
		(polyline
			(pts
				(xy 91.44 35.56) (xy 91.44 45.72) (xy 97.79 45.72) (xy 100.33 45.72) (xy 100.33 43.18) (xy 100.33 35.56)
			)
			(stroke
				(width 0)
				(type dash)
			)
			(fill
				(type none)
			)
			(uuid 4662e168-e481-4ea7-a982-70c5cc671a7b)
		)
	)
	(netclass_flag ""
		(length 2.54)
		(shape round)
		(at 91.44 45.72 180)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "20dacfc5-f753-4b6a-ad75-2cf558743ae1")
		(property "Netclass" "USB_DIFF"
			(at 92.1385 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
					(italic yes)
				)
				(justify left)
			)
		)
	)
	(netclass_flag ""
		(length 2.54)
		(shape round)
		(at 158.75 45.72 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "f8ad1a9e-c2e9-4227-a9d6-78b28384377f")
		(property "Netclass" "USB_DIFF"
			(at 158.0515 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
					(italic yes)
				)
				(justify right)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 88.9 99.06 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "063acb77-b8e3-4783-b0d5-82d6c7d14784")
		(property "Reference" "C24"
			(at 86.36 97.7962 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "100nF"
			(at 86.36 100.3362 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 88.9 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 88.9 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 88.9 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C307331"
			(at 88.9 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "ebbeae6d-da78-4be6-aedd-8083c0b8506e")
		)
		(pin "1"
			(uuid "b1d8e1e0-284d-4586-a57c-c6d15dab489e")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "C24")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 74.93 62.23 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "197af8b2-d644-4378-ac3a-7268da81933e")
		(property "Reference" "#PWR027"
			(at 74.93 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 74.93 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 74.93 62.23 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 74.93 62.23 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 74.93 62.23 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "06e8e41a-aab8-447a-a92e-4f6c538aeb3e")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR027")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Logic_LevelTranslator:TXB0106RGY")
		(at 95.25 120.65 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "21edf4d2-768b-4aec-913b-69a6a1cb2b7b")
		(property "Reference" "U6"
			(at 92.964 134.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "TXB0106RGY"
			(at 92.964 137.16 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Package_DFN_QFN:Texas_RGY_R-PVQFN-N16_EP2.05x2.55mm"
			(at 95.25 139.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "http://www.ti.com/lit/ds/symlink/txb0106.pdf"
			(at 95.25 125.73 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "6-Bit Bidirectional Voltage-Level Translator, Auto Direction Sensing and ±15-kV ESD Protection, 1.2 - 3.6V APort, 1.65 - 5.5V BPort, VQFN-16"
			(at 95.25 120.65 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C2652978"
			(at 95.25 120.65 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "14"
			(uuid "f9ffce33-3ee8-4a49-98d3-47c73c8022a7")
		)
		(pin "5"
			(uuid "84a11571-**************-20d802294d9b")
		)
		(pin "15"
			(uuid "13b4e166-e347-4fde-9326-ac84d371a444")
		)
		(pin "16"
			(uuid "6dba3628-aaec-46b9-8985-90872c241b6f")
		)
		(pin "2"
			(uuid "3f4c1cfb-49b1-42ce-9be1-2a2fac424e06")
		)
		(pin "7"
			(uuid "d34d96fe-b49e-4fda-89ff-db309ec6ef2c")
		)
		(pin "12"
			(uuid "a375b133-c80c-44a3-a9f1-45a97bdeaa31")
		)
		(pin "11"
			(uuid "5f6d1830-ad8a-4132-b49a-d48a19dd5e91")
		)
		(pin "17"
			(uuid "ab55e55b-0ba6-4f04-8be8-945d8d9aa45d")
		)
		(pin "3"
			(uuid "e12af9fd-92dc-4455-8f65-f0f8750b725f")
		)
		(pin "10"
			(uuid "c39b2924-5424-47c0-8b04-a9b975a9493b")
		)
		(pin "6"
			(uuid "757369c3-2a5a-4c01-b954-bf8aabb1ddc2")
		)
		(pin "13"
			(uuid "1ac2c866-3616-4680-86b7-52cd6a0e6a7e")
		)
		(pin "4"
			(uuid "409f62a7-3e7d-486e-b69f-1d35ce5824e9")
		)
		(pin "1"
			(uuid "cb71f333-a23a-4be6-b593-3cfa54af6f51")
		)
		(pin "8"
			(uuid "5bb76716-d262-45b2-839c-316721aec21c")
		)
		(pin "9"
			(uuid "51fdb270-1a41-431a-ae9a-0aac6953e22b")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "U6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 238.76 63.5 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "234abd64-44e3-45e6-a6ee-03d6d6b0d513")
		(property "Reference" "C19"
			(at 241.3 62.2362 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "100nF"
			(at 241.3 64.7762 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 238.76 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 238.76 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 238.76 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C307331"
			(at 238.76 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "8287e0ed-ee6b-493c-a147-5ce9ccd71438")
		)
		(pin "1"
			(uuid "9064f6fa-e796-4af6-bc38-f80f144d31c2")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "C19")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Graphic:Logo_Open_Hardware_Small")
		(at 278.13 172.72 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom no)
		(on_board no)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "26618a43-0834-4e02-844a-58ec6e6273ab")
		(property "Reference" "#SYM6"
			(at 278.13 165.735 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "Logo_Open_Hardware_Small"
			(at 278.13 178.435 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 278.13 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 278.13 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 278.13 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#SYM6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 208.28 26.67 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "2e6a8c7a-5613-47bf-a7e7-8b52dd63a549")
		(property "Reference" "C15"
			(at 210.82 25.4062 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "1uF"
			(at 210.82 27.9462 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 208.28 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 208.28 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 208.28 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C52923"
			(at 208.28 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "21714c97-b6bb-44e0-a902-14f25ba34e38")
		)
		(pin "1"
			(uuid "*************-4fbf-94e0-378c3acd3776")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "C15")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 111.76 46.99 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "2ffaa8e6-e587-4b4e-b70e-4a18b1264ef1")
		(property "Reference" "#PWR025"
			(at 111.76 53.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 111.76 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 111.76 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 111.76 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 111.76 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "054dabe4-251a-4c74-9fd7-c899fae32571")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR025")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:TestPoint_Small")
		(at 224.79 110.49 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "379eee47-72f4-4618-a459-9c93f4ee70f4")
		(property "Reference" "TP9"
			(at 222.25 115.0621 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Value" "TestPoint"
			(at 222.25 112.5221 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "TestPoint:TestPoint_Pad_D1.5mm"
			(at 219.71 110.49 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 219.71 110.49 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "test point"
			(at 224.79 110.49 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "d06f328e-941b-4931-b5d0-86f7ab4f5dfe")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "TP9")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:TestPoint_Small")
		(at 100.33 31.75 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "3c2e333b-9dda-4a37-bf26-312ff0974a20")
		(property "Reference" "TP6"
			(at 97.79 36.3221 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Value" "TestPoint"
			(at 97.79 33.7821 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "TestPoint:TestPoint_Pad_D1.5mm"
			(at 95.25 31.75 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 95.25 31.75 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "test point"
			(at 100.33 31.75 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "ef7f369d-df66-4c9a-b47a-099e19589313")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "TP6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 95.25 135.89 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "3e2a9436-0bdb-472b-aa0c-545a22ee0866")
		(property "Reference" "#PWR03"
			(at 95.25 142.24 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 95.25 140.97 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 95.25 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 95.25 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 95.25 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "f53717a5-8621-42e8-895f-bc57caf9c2e6")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR03")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "nordic-lib-kicad-nrf52:nRF52833-QDXX")
		(at 185.42 93.98 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "4581220c-6b80-44a1-b3fe-f22780e7b12e")
		(property "Reference" "U5"
			(at 185.42 93.98 0)
			(do_not_autoplace yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "nRF52833-QDXX"
			(at 185.42 96.52 0)
			(do_not_autoplace yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Package_DFN_QFN:QFN-40-1EP_5x5mm_P0.4mm_EP3.6x3.6mm_ThermalVias"
			(at 190.5 58.42 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 190.5 58.42 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 185.42 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C2895249"
			(at 185.42 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "9"
			(uuid "7ac131e4-22ef-411f-bdca-0209ee617111")
		)
		(pin "11"
			(uuid "4c44feba-cf7d-4982-93d9-a4cb6150f76c")
		)
		(pin "6"
			(uuid "cc6dd7d6-4c72-4fe2-ae31-a1822809feb7")
		)
		(pin "24"
			(uuid "aa34e143-bf3a-427e-ad40-1b80b088403b")
		)
		(pin "17"
			(uuid "2b7e84dd-b997-4da9-a21b-629d24a5b8f0")
		)
		(pin "20"
			(uuid "643b66b1-cd1d-4def-ada5-27edfa77faf5")
		)
		(pin "32"
			(uuid "5d6c9af9-1aed-44c9-ab3c-e754621bac54")
		)
		(pin "39"
			(uuid "1e5ec4ed-874a-40a1-8bd7-f1c0c387dd3e")
		)
		(pin "19"
			(uuid "997c7231-f9e7-43bb-8e55-7b78d9aad361")
		)
		(pin "8"
			(uuid "fe8f147d-2c6e-49c2-be53-11cbb0db479a")
		)
		(pin "26"
			(uuid "e451c073-c70f-4f97-9b72-f38daff7d815")
		)
		(pin "13"
			(uuid "6eab10b7-9c8a-41f9-89f6-e936d91ffe5a")
		)
		(pin "30"
			(uuid "0c492ae2-e8b9-4c68-bf1d-cc74a153032d")
		)
		(pin "40"
			(uuid "4f05f7d9-1d01-41d3-bebd-dbfbea60d822")
		)
		(pin "21"
			(uuid "321eb1d9-56e1-4839-8102-ec10db860d3d")
		)
		(pin "41"
			(uuid "92a89dd3-8f32-4f0f-8ce3-104ea0ab0c6d")
		)
		(pin "37"
			(uuid "d25793e2-4eac-4cc2-b70f-7ddceea898c8")
		)
		(pin "38"
			(uuid "e2c4f119-1e22-426d-b28e-da6f34604058")
		)
		(pin "27"
			(uuid "0f9188ed-84c4-4b06-90f5-2c4863a34baf")
		)
		(pin "4"
			(uuid "2e3c7eac-6d57-464c-9acd-528d07d96587")
		)
		(pin "5"
			(uuid "94c1ca1e-4929-4db1-ac34-5a0b132c0b6c")
		)
		(pin "7"
			(uuid "47e2290f-ef86-49af-a12f-29cf9dfd80d0")
		)
		(pin "33"
			(uuid "6f512c33-de17-4657-a0aa-8a5d1508e70d")
		)
		(pin "31"
			(uuid "895c3713-c224-430b-ad3c-4a1d70e7dabb")
		)
		(pin "16"
			(uuid "df5789f1-734d-4eb9-adb1-f140825954dd")
		)
		(pin "1"
			(uuid "74ac4590-7515-4c60-b15a-8f95678f30ee")
		)
		(pin "10"
			(uuid "135e39b6-ae85-4bfa-8682-7c0ba713a237")
		)
		(pin "35"
			(uuid "6cd56cc4-74a2-41f1-a5e9-35edb1896898")
		)
		(pin "12"
			(uuid "5b144ca0-6d92-468e-9232-7850b48d8125")
		)
		(pin "28"
			(uuid "9d435597-000f-4e81-95bf-a6a23d5e2732")
		)
		(pin "29"
			(uuid "0be91da5-6c16-46ef-b691-71693eaf963a")
		)
		(pin "2"
			(uuid "c228b211-01ef-40ce-aa31-56cff70522da")
		)
		(pin "3"
			(uuid "52da109f-736a-40c1-9bc7-66aafb123a9d")
		)
		(pin "34"
			(uuid "d67258b6-e660-4c52-8c5c-2f779df0d2d9")
		)
		(pin "18"
			(uuid "7453a644-44c1-47e6-88f7-440840b0ec9c")
		)
		(pin "25"
			(uuid "7c03ef67-ee79-4a05-b855-9f425967af7f")
		)
		(pin "22"
			(uuid "35fd0279-b8fd-429c-9cc6-f16922058f8e")
		)
		(pin "36"
			(uuid "303b6aad-ce8f-4bb7-983c-0a1c56e9226e")
		)
		(pin "15"
			(uuid "16ffb57b-864f-4683-ac92-6cdd487c197d")
		)
		(pin "14"
			(uuid "6277f0d0-ad1c-4951-b589-4a71484493d4")
		)
		(pin "23"
			(uuid "ad8ae0cd-1c61-4f25-9a41-d2e36efce7c5")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "U5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:TestPoint_Small")
		(at 95.25 24.13 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "479a150b-f394-4f6b-9299-a6fff5b9e303")
		(property "Reference" "TP5"
			(at 92.71 28.7021 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Value" "TestPoint"
			(at 92.71 26.1621 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "TestPoint:TestPoint_Pad_D1.5mm"
			(at 90.17 24.13 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 90.17 24.13 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "test point"
			(at 95.25 24.13 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "6b1f31a8-d929-4647-bbea-ad8d16f485dc")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "TP5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 175.26 29.21 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "61ae2d73-d0b8-4ad8-8ec5-d6804877a9d1")
		(property "Reference" "#PWR023"
			(at 175.26 35.56 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 175.26 34.29 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 175.26 29.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 175.26 29.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 175.26 29.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "be4e9d8a-0892-40ab-aaf8-0eb099002969")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR023")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R_Small_US")
		(at 127 115.57 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "6429458d-de44-44bb-8d75-36a358de7b45")
		(property "Reference" "R1"
			(at 129.54 114.2999 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "100kR"
			(at 129.54 116.8399 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 127 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 127 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor, small US symbol"
			(at 127 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C25741"
			(at 127 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b4e3dc16-1844-491e-9656-1f26693caa3a")
		)
		(pin "2"
			(uuid "92ffe0cf-a752-415e-ac5a-1c6333282224")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "R1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:TestPoint_Small")
		(at 95.201 46.99 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "7932e4c8-8ad6-408a-8f82-b046d0617ff8")
		(property "Reference" "TP7"
			(at 97.741 42.4179 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Value" "TestPoint"
			(at 97.741 44.9579 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "TestPoint:TestPoint_Pad_D1.5mm"
			(at 100.281 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 100.281 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "test point"
			(at 95.201 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "94f9e3d7-6a3a-4853-82b2-3e3673c47a5e")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "TP7")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 135.89 66.04 90)
		(mirror x)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "7a110dc4-d051-4e72-8c9a-030377b13a92")
		(property "Reference" "C20"
			(at 138.43 69.596 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "12pF"
			(at 138.176 71.628 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 135.89 66.04 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 135.89 66.04 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 135.89 66.04 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C1547"
			(at 135.89 66.04 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "384ab5c1-561e-401a-9b57-14722192cdd8")
		)
		(pin "1"
			(uuid "13a3a2cb-3187-4df3-8b46-9116f0c3ead2")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "C20")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:Conn_ARM_SWD_TagConnect_TC2030-NL")
		(at 222.25 124.46 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom no)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "81ea7639-7150-406c-8fec-9b990108e0b4")
		(property "Reference" "J8"
			(at 228.6 123.1899 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "Conn_ARM_SWD_TagConnect_TC2030-NL"
			(at 228.6 125.7299 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Connector:Tag-Connect_TC2030-IDC-NL_2x03_P1.27mm_Vertical"
			(at 222.25 142.24 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://www.tag-connect.com/wp-content/uploads/bsk-pdf-manager/TC2030-CTX_1.pdf"
			(at 222.25 139.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Tag-Connect ARM Cortex SWD JTAG connector, 6 pin, no legs"
			(at 222.25 124.46 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "3"
			(uuid "f374deae-b030-4b9e-a8a3-ef53ed92fe22")
		)
		(pin "1"
			(uuid "d2c5c84b-7761-48f3-8ceb-6134a2022f63")
		)
		(pin "5"
			(uuid "2d8bb14b-2721-48fa-8bd9-1188d4c03d49")
		)
		(pin "4"
			(uuid "b5451575-a811-4e93-a3bd-ed741d7585ff")
		)
		(pin "2"
			(uuid "e6f5ef1e-2934-4516-bbf5-4b83b63a49a1")
		)
		(pin "6"
			(uuid "07a08e98-18ce-460e-b1d6-11a40425dbe5")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "J8")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 88.9 101.6 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "896814f6-d80b-4c02-aea0-f162a6b17e55")
		(property "Reference" "#PWR040"
			(at 88.9 107.95 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 88.9 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 88.9 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 88.9 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 88.9 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "fc887d51-a398-42e1-8d69-0c9cc24e5df8")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR040")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:TestPoint_Small")
		(at 210.82 113.03 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "906970f4-a47c-416e-974a-b9af5858343b")
		(property "Reference" "TP10"
			(at 208.28 117.6021 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Value" "TestPoint"
			(at 208.28 115.0621 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "TestPoint:TestPoint_Pad_D1.5mm"
			(at 205.74 113.03 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 205.74 113.03 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "test point"
			(at 210.82 113.03 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "abfb4609-c571-40b3-899c-6a76f2a98812")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "TP10")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 198.12 26.67 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "92a86197-e7d3-49a7-8001-2da8b3f0d1cc")
		(property "Reference" "C10"
			(at 200.66 25.4062 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "4.7uF"
			(at 200.66 27.9462 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 198.12 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 198.12 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 198.12 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C23733"
			(at 198.12 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "7295b1c9-534b-47b8-a832-b0ef1748736f")
		)
		(pin "1"
			(uuid "3b19464c-8942-4d12-a510-3f60ccc5cf80")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "C10")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:TestPoint_Small")
		(at 95.25 34.29 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "94aa0c1c-2f7c-493d-966b-350972620ef0")
		(property "Reference" "TP8"
			(at 92.71 38.8621 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Value" "TestPoint"
			(at 92.71 36.3221 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "TestPoint:TestPoint_Pad_D1.5mm"
			(at 90.17 34.29 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 90.17 34.29 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "test point"
			(at 95.25 34.29 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b16fe571-54a6-44da-bf48-f6dcf4dc44d4")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "TP8")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 82.55 107.95 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "9846a8bb-57ae-49ea-8c01-2fabb4d7e7a2")
		(property "Reference" "#PWR049"
			(at 82.55 114.3 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 82.55 113.03 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 82.55 107.95 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 82.55 107.95 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 82.55 107.95 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "7c229672-eda4-4b59-b4b2-d8262cb1dba4")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR049")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "hlord2000-Device:Crystal_GND24_Small_EZRoute")
		(at 144.78 63.5 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "986f9885-366b-40f1-939b-474cda9de7b6")
		(property "Reference" "Y2"
			(at 147.32 62.2299 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "32MHz"
			(at 147.32 64.7699 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Crystal:Crystal_SMD_2016-4Pin_2.0x1.6mm"
			(at 144.78 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 144.78 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Four pin crystal, GND on pins 2 and 4, small symbol"
			(at 144.78 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C2682773"
			(at 144.78 63.5 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "4"
			(uuid "52f6da9a-9a15-4032-be84-dc3cb25f0176")
		)
		(pin "3"
			(uuid "d43af5f0-8262-48d0-8eaf-5fc5cbb1df07")
		)
		(pin "2"
			(uuid "578936ef-e35b-4ea8-bf32-0c53a7ec0a9e")
		)
		(pin "1"
			(uuid "5cb41736-139d-4c7e-8a6f-5a5d2c3b8e7f")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "Y2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 231.14 26.67 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "98d166b2-cfa1-4ee2-bbe7-b04aeb587672")
		(property "Reference" "C17"
			(at 233.68 25.4062 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "100nF"
			(at 233.68 27.9462 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 231.14 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 231.14 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 231.14 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C307331"
			(at 231.14 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "0e441718-711f-4c92-afab-6c31d02e9f02")
		)
		(pin "1"
			(uuid "3122534a-5773-4f34-926f-a83d07cd4be7")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "C17")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Power_Protection:TPD3E001DRLR")
		(at 111.76 39.37 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "9a5883a0-06a6-478a-8a09-021dea1b1e41")
		(property "Reference" "U3"
			(at 111.76 29.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "TPD3E001DRLR"
			(at 111.76 31.75 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Package_TO_SOT_SMD:SOT-553"
			(at 129.54 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "http://www.ti.com/lit/ds/symlink/tpd3e001.pdf"
			(at 116.84 33.02 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "TPD3E001 Low-Capacitance 3-Channel ESD-Protection for High-Speed Data Interfaces"
			(at 111.76 39.37 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C2999113"
			(at 111.76 39.37 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "5"
			(uuid "7364a3ce-d778-4eb0-9e81-d46aa0677fe4")
		)
		(pin "1"
			(uuid "8077c3e8-2846-4a2c-9394-95b4535ea7be")
		)
		(pin "4"
			(uuid "704fbc18-539c-47d4-b44a-375ffa83d6ac")
		)
		(pin "2"
			(uuid "b8e3821a-5600-46d7-a148-e24ed825d4b8")
		)
		(pin "3"
			(uuid "03571ae2-08f6-473a-b589-fc608b0489fb")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "U3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 175.26 26.67 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "9ab554a2-0e0e-427f-bb22-5edc0c7396eb")
		(property "Reference" "C7"
			(at 172.72 25.4062 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "4.7uF"
			(at 172.72 27.9462 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 175.26 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 175.26 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 175.26 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C23733"
			(at 175.26 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "603a3b26-8637-4e6b-85d3-f0874f6a0dad")
		)
		(pin "1"
			(uuid "885616a6-c089-46a8-89d0-120be5b22814")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "C7")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:USB_C_Receptacle_USB2.0_16P")
		(at 74.93 39.37 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a0dbcd30-1ef9-4507-8d8c-6396d1a9c85c")
		(property "Reference" "J3"
			(at 74.93 16.51 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "USB_C_Receptacle_USB2.0_16P"
			(at 74.93 19.05 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "marbastlib-various:USB_C_Receptacle_HRO_TYPE-C-31-M-12"
			(at 78.74 39.37 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://www.usb.org/sites/default/files/documents/usb_type-c.zip"
			(at 78.74 39.37 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "USB 2.0-only 16P Type-C Receptacle connector"
			(at 74.93 39.37 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C165948"
			(at 74.93 39.37 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "A12"
			(uuid "8103a9b4-b20c-4661-8e13-085d744a9be0")
		)
		(pin "A7"
			(uuid "84aa0bac-5e06-4ebb-b8c0-01f341616534")
		)
		(pin "B1"
			(uuid "27e5b49b-bf7a-4e47-a3ad-f66d93f9911c")
		)
		(pin "A1"
			(uuid "eee34f41-c58d-40fd-9f8f-139705da7120")
		)
		(pin "B7"
			(uuid "bf034887-eeea-4c33-9c85-5b4f4880ad6e")
		)
		(pin "B6"
			(uuid "5309a361-c7ab-4a6f-ad06-ffa6e4c82b44")
		)
		(pin "B12"
			(uuid "1e053598-9e87-42b9-9ae8-0d5814c0656f")
		)
		(pin "A9"
			(uuid "9176d2ef-599c-4fa3-a6bc-b48dcc8ee284")
		)
		(pin "A6"
			(uuid "5ee4f351-c19f-492c-a026-ec54f3d92f7c")
		)
		(pin "A5"
			(uuid "dfed457c-fcd5-425e-bf81-5ec697a3005f")
		)
		(pin "B4"
			(uuid "2d967fc6-be16-402c-813c-fc9b7d6f7c7b")
		)
		(pin "A4"
			(uuid "92e61408-6970-467b-86b1-af74e9618a40")
		)
		(pin "B8"
			(uuid "fd0016ea-d8be-40a6-9f79-a191cfef2ed1")
		)
		(pin "B9"
			(uuid "08c6c325-8a66-490c-b557-36362c8b31a6")
		)
		(pin "B5"
			(uuid "190528c0-edcf-4115-8e6e-dbe3ba3d486a")
		)
		(pin "S1"
			(uuid "6426c417-0361-414c-a3ce-fcd535487941")
		)
		(pin "A8"
			(uuid "d2567a1e-e6ad-4034-9dd4-be2f987b1f21")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "J3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:TestPoint_Small")
		(at 212.09 115.57 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "a2f07137-c861-4b29-ac58-523d75d5b5aa")
		(property "Reference" "TP11"
			(at 209.55 120.1421 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Value" "TestPoint"
			(at 209.55 117.6021 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "TestPoint:TestPoint_Pad_D1.5mm"
			(at 207.01 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 207.01 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "test point"
			(at 212.09 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "1c0d9da2-7cbd-416d-8f10-ab7b55a74138")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "TP11")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 185.42 152.4 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a6bfd99b-5ee0-46ef-aa15-769d3c8fd3dd")
		(property "Reference" "#PWR047"
			(at 185.42 158.75 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 185.42 157.48 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 185.42 152.4 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 185.42 152.4 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 185.42 152.4 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "ffa8abce-8f89-4d7c-aa0b-523af6998278")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR047")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 231.14 29.21 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a9446c83-87d7-4c10-a7e8-f91b3e76b94f")
		(property "Reference" "#PWR024"
			(at 231.14 35.56 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 231.14 34.29 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 231.14 29.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 231.14 29.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 231.14 29.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "e6db47a9-0d70-4b96-8924-fd9fd247afc7")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR024")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 127 118.11 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a9c8fd91-8187-4616-8a89-a66531812714")
		(property "Reference" "#PWR012"
			(at 127 124.46 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 127 123.19 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 127 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 127 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 127 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "f50314d0-7aa1-4b63-a585-4398b739350f")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR012")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:TestPoint_Small")
		(at 213.36 118.11 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "bd00db69-cc6d-406c-b303-f842bd9a94ef")
		(property "Reference" "TP12"
			(at 210.82 122.6821 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Value" "TestPoint"
			(at 210.82 120.1421 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "TestPoint:TestPoint_Pad_D1.5mm"
			(at 208.28 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 208.28 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "test point"
			(at 213.36 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "5c669f7b-027d-4581-b04e-f6fbfd667c98")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "TP12")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VBUS")
		(at 175.26 24.13 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "c0718e79-ea4b-4d4c-8cea-1dbf8bb82e2f")
		(property "Reference" "#PWR029"
			(at 175.26 27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VBUS"
			(at 175.26 19.05 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 175.26 24.13 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 175.26 24.13 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VBUS\""
			(at 175.26 24.13 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "c8c9dc39-1978-4d2f-9250-4151111f7f94")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR029")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 101.6 101.6 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "c72d4d33-9382-413e-afb2-649f8f61fffb")
		(property "Reference" "#PWR044"
			(at 101.6 107.95 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 101.6 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 101.6 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 101.6 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 101.6 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "96887faa-c195-4608-97b4-e8826fc74b42")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR044")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 135.89 60.96 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "ca70fe83-6c80-4ce4-a6d5-31e680d12b8c")
		(property "Reference" "C18"
			(at 138.176 55.118 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "12pF"
			(at 138.176 57.404 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 135.89 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 135.89 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 135.89 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C1547"
			(at 135.89 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "6823b80c-b36d-42bc-aac5-3ff285c6d256")
		)
		(pin "1"
			(uuid "b90ba457-38af-4839-8871-12a91a794c9a")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "C18")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 238.76 78.74 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "d741c9b4-77f5-43b8-848f-9d85158b005c")
		(property "Reference" "#PWR036"
			(at 238.76 85.09 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 238.76 83.82 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 238.76 78.74 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 238.76 78.74 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 238.76 78.74 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "29aba79e-5cdd-4b9c-8c52-98fc94200a9c")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR036")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 210.82 76.2 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "d8727f2f-76a6-45e4-b643-5bd522a491c0")
		(property "Reference" "C23"
			(at 213.36 74.9362 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "4.7uF"
			(at 213.36 77.4762 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 210.82 76.2 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 210.82 76.2 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 210.82 76.2 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C23733"
			(at 210.82 76.2 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "6355df57-1303-4063-abd3-4a6d4a3e6122")
		)
		(pin "1"
			(uuid "3a67bb60-d08d-4e0a-a9da-d9ed3a282690")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "C23")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 220.98 73.66 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "dc307cb6-a63e-48d9-9d52-cd5d78a88ce3")
		(property "Reference" "C22"
			(at 223.52 72.3962 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "1uF"
			(at 223.52 74.9362 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 220.98 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 220.98 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 220.98 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C52923"
			(at 220.98 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "1695a41c-fabf-4b81-9590-bbee4722f87c")
		)
		(pin "1"
			(uuid "5714ec04-f1a6-4c2f-a009-ce43aa33d890")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "C22")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 228.6 66.04 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "e2d4e6f3-e109-425e-8e61-92da7da124f6")
		(property "Reference" "C21"
			(at 231.14 64.7762 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "100pF"
			(at 231.14 67.3162 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 228.6 66.04 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 228.6 66.04 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 228.6 66.04 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C1546"
			(at 228.6 66.04 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "f22fc04d-c3b1-40df-bb4d-278377b61054")
		)
		(pin "1"
			(uuid "a6868cec-2d2b-4ef0-a39c-051be5431994")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "C21")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VDD")
		(at 92.71 96.52 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "e585b774-b6d4-489d-bac3-0601d4aa150e")
		(property "Reference" "#PWR013"
			(at 92.71 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VDD"
			(at 92.71 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 92.71 96.52 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 92.71 96.52 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VDD\""
			(at 92.71 96.52 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "113f49a7-2c0b-49a1-a79f-ff9ea74cf2d1")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR013")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "LordsBoards-Graphic:LordsBoardsLogo")
		(at 185.42 172.72 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom no)
		(on_board no)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "e8fe4d31-f12f-4118-9f7c-b29ab7d61799")
		(property "Reference" "#SYM5"
			(at 185.42 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "~"
			(at 185.42 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 185.42 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 185.42 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 185.42 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#SYM5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 130.81 63.5 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "ec3ce0f0-25f1-40ce-88ff-acec8edf4892")
		(property "Reference" "#PWR028"
			(at 130.81 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 130.81 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 130.81 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 130.81 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 130.81 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "8897d425-91f7-4c3e-b917-418de1f38d15")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR028")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 101.6 99.06 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "f7f4839a-2b8d-44fc-9089-d774d6ec984b")
		(property "Reference" "C25"
			(at 104.14 97.7962 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "100nF"
			(at 104.14 100.3362 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 101.6 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 101.6 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 101.6 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C307331"
			(at 101.6 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "6668ac10-33e2-41af-a7fa-42b45372140f")
		)
		(pin "1"
			(uuid "15990cd9-6144-4c86-9831-cb352f02a85b")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "C25")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 224.79 134.62 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "fad8bd29-df81-406e-a1e3-ffe2db25c23d")
		(property "Reference" "#PWR042"
			(at 224.79 140.97 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 224.79 139.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 224.79 134.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 224.79 134.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 224.79 134.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "5b7b29d8-0152-4608-a373-bae1f97a7b5a")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "#PWR042")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 219.71 26.67 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "fe2c4d9a-**************-27d3f55c3373")
		(property "Reference" "C16"
			(at 222.25 25.4062 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "100nF"
			(at 222.25 27.9462 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 219.71 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 219.71 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 219.71 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C307331"
			(at 219.71 26.67 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "f6fb78a5-5abb-478c-b976-82957ba3958f")
		)
		(pin "1"
			(uuid "b45a4a1e-4697-45a4-853c-710aece88fc3")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "C16")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R_Small_US")
		(at 138.43 120.65 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "fedb7c5f-3ca7-4773-b02f-181679f19f0f")
		(property "Reference" "R8"
			(at 140.97 119.3799 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "100kR"
			(at 140.97 121.9199 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 138.43 120.65 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 138.43 120.65 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor, small US symbol"
			(at 138.43 120.65 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C25741"
			(at 138.43 120.65 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "3109f903-7baf-492d-ab2b-35673d2f1d9f")
		)
		(pin "2"
			(uuid "6f4ea3ab-a2ba-4a06-b261-d7fcb0726958")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/bee6e003-c9d0-4aff-a9a9-7e1c876b28cb"
					(reference "R8")
					(unit 1)
				)
			)
		)
	)
)
