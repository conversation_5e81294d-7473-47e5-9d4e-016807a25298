
# CMake script file to process a text file for use as an immediate string value in C/C++.
# It escapes double-quotes and then wraps every line in (unescaped) double-quotes.

set( lines "" )
file( STRINGS ${inputFile} lines )

file( WRITE ${outputFile} "// Do not edit this file, it is autogenerated by CMake from a .txt file\n" )

foreach( line IN LISTS lines )
    STRING(REGEX REPLACE "\"" "\\\\\"" linem "${line}" )
    file( APPEND ${outputFile} "\"" "${linem}" "\\n\"\n" )
endforeach( line "${lines}" )
