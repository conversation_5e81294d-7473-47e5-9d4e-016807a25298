(footprint "Altech_AK300_1x02_P5.00mm_45-Degree"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "Altech AK300 serie terminal block (Script generated with StandardBox.py) (http://www.altechcorp.com/PDFS/PCBMETRC.PDF)")
	(tags "Altech AK300 serie connector")
	(property "Reference" "REF**"
		(at 2.032 -7.366 180)
		(layer "F.SilkS")
		(uuid "5b13e0af-9f62-4e2a-b7ae-3e58e91805aa")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "CONN_2"
		(at -4.191 1.27 270)
		(layer "F.Fab")
		(uuid "2cae4e32-d5d5-4131-a908-082c7f63f771")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "ea527162-3400-4823-a675-66649e6defcc")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "bb5ad53c-f2ca-4fc5-982b-e7d191753d1b")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -3 -6.5)
		(end 0 -6.5)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "28c479fd-baa3-4fe2-af0d-39de7550cf0b")
	)
	(fp_line
		(start -3 -3.5)
		(end -3 -6.5)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "d28691c2-f68e-480c-b69d-9fac7a748f2e")
	)
	(fp_line
		(start -2.62 -6.12)
		(end -2.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "074453b8-b7a8-4f56-81d0-5b19f6411ad5")
	)
	(fp_line
		(start -2.62 -6.12)
		(end -2.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "d00eefcd-b9cb-4fb4-9d3c-44b1238ee785")
	)
	(fp_line
		(start -2.62 -6.12)
		(end 7.62 -6.12)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "deb7d729-5df6-493d-9d0c-7f71f3366c88")
	)
	(fp_line
		(start -2.62 -6.12)
		(end 7.62 -6.12)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "e276e2ed-5a38-440d-95e7-3463dd9777cb")
	)
	(fp_line
		(start -2.62 6.62)
		(end 7.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "3d338c03-7884-4ed7-a7c9-042537f08a4c")
	)
	(fp_line
		(start -2.62 6.62)
		(end 7.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "c59fff11-54c1-49d2-b1c4-996c54764d80")
	)
	(fp_line
		(start 7.62 -6.12)
		(end 7.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "3f3bc39d-0936-4423-82c9-cdaab244fbc7")
	)
	(fp_line
		(start 7.62 -6.12)
		(end 7.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "edae2b5b-469c-4ab3-a047-d7a2fb56c80b")
	)
	(fp_line
		(start -2.75 -6.25)
		(end -2.75 6.75)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "7937c3c8-4ce3-40d3-8155-321f296a3c47")
	)
	(fp_line
		(start -2.75 -6.25)
		(end 7.75 -6.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "8d252914-69ae-47b2-a930-a206c3020518")
	)
	(fp_line
		(start -2.75 6.75)
		(end 7.75 6.75)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "9a3aa7a8-25a9-4443-ab91-fd6760251bf9")
	)
	(fp_line
		(start 7.75 -6.25)
		(end 7.75 6.75)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "9a297ad1-dcd0-4c62-8193-4a7164bb8b1e")
	)
	(fp_line
		(start -2.5 -5.5)
		(end -2 -6)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "75bdceaf-f214-49c6-b94f-0671afaba154")
	)
	(fp_line
		(start -2.5 6.5)
		(end -2.5 -5.5)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "f9aa3aca-2d7a-4421-83e6-495e3311a7df")
	)
	(fp_line
		(start -2 -6)
		(end 7.5 -6)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "1bc0bb24-d614-4222-a822-f10be09921ed")
	)
	(fp_line
		(start 7.5 -6)
		(end 7.5 6.5)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "b115b6f4-6d93-496e-bf71-76e5bc2e40a7")
	)
	(fp_line
		(start 7.5 6.5)
		(end -2.5 6.5)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "acd42e03-1413-4a37-acc2-ee19d15f9105")
	)
	(fp_text user "${REFERENCE}"
		(at 2.5 0.25 180)
		(layer "F.Fab")
		(uuid "205b4704-a304-4515-8a9f-88200cb8397a")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 3 3)
		(drill 1.5)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "06da2526-4d8c-4b9a-8e3f-fbf9597956ed")
	)
	(pad "2" thru_hole circle
		(at 5 0)
		(size 3 3)
		(drill 1.5)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "95f4a709-86d0-414d-80aa-caf9a7abb1e1")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/TerminalBlock_Altech.3dshapes/Altech_AK300_1x02_P5.00mm_45-Degree.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
