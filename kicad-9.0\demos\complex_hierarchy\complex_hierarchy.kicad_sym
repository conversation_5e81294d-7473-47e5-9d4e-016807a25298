(kicad_symbol_lib
	(version 20241209)
	(generator "kicad_symbol_editor")
	(generator_version "9.0")
	(symbol "+12V"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+12V"
			(at 0 3.556 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "+12V_0_1"
			(polyline
				(pts
					(xy -0.762 1.27) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 2.54) (xy 0.762 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "+12V_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "+12V"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "-VAA"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 2.54 0)
			(effects
				(font
					(size 0.508 0.508)
				)
				(hide yes)
			)
		)
		(property "Value" "-VAA"
			(at 0 2.54 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "-VAA_0_0"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "-VAA"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
		)
		(symbol "-VAA_0_1"
			(polyline
				(pts
					(xy 0 2.032) (xy 0.762 1.27) (xy -0.508 1.27) (xy -0.762 1.27) (xy 0 2.032) (xy 0 2.032) (xy 0 2.032)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 1.27) (xy 0 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "7805"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 3.81 -4.9784 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "7805"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "7805_0_1"
			(rectangle
				(start -5.08 -3.81)
				(end 5.08 3.81)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "7805_1_1"
			(pin input line
				(at -10.16 1.27 0)
				(length 5.08)
				(name "VI"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
			)
			(pin input line
				(at 0 -6.35 90)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
			)
			(pin power_out line
				(at 10.16 1.27 180)
				(length 5.08)
				(name "VO"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "C"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.254)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "C"
			(at 0.635 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "C"
			(at 0.635 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0.9652 -3.81 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "C? C_????_* C_???? SMD*_c Capacitor*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "C_0_1"
			(polyline
				(pts
					(xy -2.032 0.762) (xy 2.032 0.762)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.032 -0.762) (xy 2.032 -0.762)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "C_1_1"
			(pin passive line
				(at 0 3.81 270)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 0 -3.81 90)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CONN_2"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "P"
			(at -1.27 0 90)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "CONN_2"
			(at 1.27 0 90)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "CONN_2_0_1"
			(rectangle
				(start -2.54 3.81)
				(end 2.54 -3.81)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "CONN_2_1_1"
			(pin passive inverted
				(at -8.89 2.54 0)
				(length 6.35)
				(name "P1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive inverted
				(at -8.89 -2.54 0)
				(length 6.35)
				(name "PM"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CP"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.254)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "C"
			(at 0.635 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "CP"
			(at 0.635 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0.9652 -3.81 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "CP* Elko* TantalC* C*elec c_elec* SMD*_Pol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "CP_0_1"
			(rectangle
				(start -2.286 0.508)
				(end -2.286 1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -2.286 0.508)
				(end 2.286 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.778 2.286)
				(end -0.762 2.286)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 1.778)
				(end -1.27 2.794)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start 2.286 1.016)
				(end -2.286 1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start 2.286 1.016)
				(end 2.286 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start 2.286 -0.508)
				(end -2.286 -1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "CP_1_1"
			(pin passive line
				(at 0 3.81 270)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 0 -3.81 90)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "D_Small"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.254)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "D"
			(at -1.27 2.032 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "D_Small"
			(at -3.81 -2.032 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0 0 90)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 90)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Diode_* D-Pak_TO252AA *SingleDiode *SingleDiode* *_Diode_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "D_Small_0_1"
			(polyline
				(pts
					(xy -0.762 -1.016) (xy -0.762 1.016)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0.762 -1.016) (xy -0.762 0) (xy 0.762 1.016) (xy 0.762 -1.016)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "D_Small_1_1"
			(pin passive line
				(at -2.54 0 0)
				(length 1.778)
				(name "K"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 0 180)
				(length 1.778)
				(name "A"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "GND"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 0 -3.1242 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "GND_0_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "GND_1_1"
			(pin power_in line
				(at 0 0 270)
				(length 0)
				(hide yes)
				(name "GND"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "HT"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 3.048 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "HT"
			(at 0 2.286 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "HT_0_0"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "HT"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
		)
		(symbol "HT_0_1"
			(polyline
				(pts
					(xy 0 1.016) (xy 0.508 0.508) (xy 0 1.778) (xy -0.508 0.508) (xy 0 1.016) (xy 0 1.016)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 1.016) (xy 0 1.016)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "ICL7660"
		(pin_names
			(offset 1.016)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 5.08 10.16 0)
			(effects
				(font
					(size 1.778 1.778)
				)
				(justify left)
			)
		)
		(property "Value" "ICL7660"
			(at 1.27 -11.43 0)
			(effects
				(font
					(size 1.778 1.778)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "ICL7660_0_1"
			(rectangle
				(start -13.97 -8.89)
				(end 13.97 8.89)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "ICL7660_1_1"
			(pin input line
				(at -21.59 6.35 0)
				(length 7.62)
				(name "CAP+"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -21.59 1.27 0)
				(length 7.62)
				(name "CAP-"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -21.59 -3.81 0)
				(length 7.62)
				(name "OSC"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at -1.27 16.51 270)
				(length 7.62)
				(name "V+"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at -1.27 -16.51 90)
				(length 7.62)
				(name "GND"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_out line
				(at 21.59 3.81 180)
				(length 7.62)
				(name "VOUT"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at 21.59 -3.81 180)
				(length 7.62)
				(name "LV"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "LM358N"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at -1.27 5.08 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(property "Value" "LM358N"
			(at -1.27 -6.35 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "LM358N_0_1"
			(polyline
				(pts
					(xy -5.08 5.08) (xy 5.08 0) (xy -5.08 -5.08) (xy -5.08 5.08)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin power_in line
				(at -2.54 10.16 270)
				(length 6.35)
				(name "V+"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin power_in line
				(at -2.54 -10.16 90)
				(length 6.35)
				(name "V-"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(symbol "LM358N_1_1"
			(pin input line
				(at -12.7 2.54 0)
				(length 7.62)
				(name "+"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin input line
				(at -12.7 -2.54 0)
				(length 7.62)
				(name "-"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin output line
				(at 12.7 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(symbol "LM358N_2_1"
			(pin input line
				(at -12.7 2.54 0)
				(length 7.62)
				(name "+"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin input line
				(at -12.7 -2.54 0)
				(length 7.62)
				(name "-"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin output line
				(at 12.7 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "MPSA42"
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "Q"
			(at 3.81 -3.81 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(property "Value" "MPSA42"
			(at 3.81 3.81 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(property "Footprint" "TO92-CBE"
			(at 3.81 0 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "TO92-CBE"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "MPSA42_0_1"
			(polyline
				(pts
					(xy 0 1.905) (xy 0 -1.905) (xy 0 -1.905)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 2.54 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 0)
				(radius 2.8194)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.27 -1.27) (xy 0 0) (xy 0 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 2.286 -2.286) (xy 2.54 -2.54) (xy 2.54 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 2.286 -2.286) (xy 1.778 -0.762) (xy 0.762 -1.778) (xy 2.286 -2.286) (xy 2.286 -2.286)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "MPSA42_1_1"
			(pin input line
				(at -5.08 0 0)
				(length 5.08)
				(name "B"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 5.08 270)
				(length 2.54)
				(name "C"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 -5.08 90)
				(length 2.54)
				(name "E"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "MPSA92"
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "Q"
			(at 3.81 -3.81 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(property "Value" "MPSA92"
			(at 3.81 3.81 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(property "Footprint" "TO92-CBE"
			(at 3.81 0 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "TO92-CBE"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "MPSA92_0_1"
			(polyline
				(pts
					(xy 0 1.905) (xy 0 -1.905) (xy 0 -1.905)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 2.54 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0.635 -0.635) (xy 0 0) (xy 0 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0.635 -0.635) (xy 1.27 -1.905) (xy 1.905 -1.27) (xy 0.635 -0.635) (xy 0.635 -0.635)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(circle
				(center 1.27 0)
				(radius 2.8194)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 2.54 -2.54) (xy 1.651 -1.651) (xy 1.651 -1.651)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "MPSA92_1_1"
			(pin input line
				(at -5.08 0 0)
				(length 5.08)
				(name "B"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 5.08 270)
				(length 2.54)
				(name "C"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 -5.08 90)
				(length 2.54)
				(name "E"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "POT"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "RV"
			(at 0 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "POT"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "POT_0_1"
			(rectangle
				(start -3.81 1.27)
				(end 3.81 -1.27)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 1.27) (xy -0.508 1.778) (xy 0.508 1.778)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "POT_1_1"
			(pin passive line
				(at -6.35 0 0)
				(length 2.54)
				(name "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 0 3.81 270)
				(length 2.032)
				(name "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 6.35 0 180)
				(length 2.54)
				(name "3"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "PWR_FLAG"
		(power)
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#FLG"
			(at 0 2.413 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "PWR_FLAG"
			(at 0 4.572 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "PWR_FLAG_0_0"
			(pin power_out line
				(at 0 0 90)
				(length 0)
				(name "pwr"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
		)
		(symbol "PWR_FLAG_0_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 1.27) (xy -1.905 2.54) (xy 0 3.81) (xy 1.905 2.54) (xy 0 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "R"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "R"
			(at 2.032 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "R"
			(at 0 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at -1.778 0 90)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "R_* Resistor_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "R_0_1"
			(rectangle
				(start -1.016 -2.54)
				(end 1.016 2.54)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "R_1_1"
			(pin passive line
				(at 0 3.81 270)
				(length 1.27)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 0 -3.81 90)
				(length 1.27)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "VCC"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "VCC_0_1"
			(circle
				(center 0 1.905)
				(radius 0.635)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "VCC_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "VCC"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
)
