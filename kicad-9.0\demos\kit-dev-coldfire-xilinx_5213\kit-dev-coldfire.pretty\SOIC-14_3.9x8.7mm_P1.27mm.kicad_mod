(footprint "SOIC-14_3.9x8.7mm_P1.27mm"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "SOIC, 14 Pin (JEDEC MS-012AB, https://www.analog.com/media/en/package-pcb-resources/package/pkg_pdf/soic_narrow-r/r_14.pdf), generated with kicad-footprint-generator ipc_gullwing_generator.py")
	(tags "SOIC SO")
	(property "Reference" "REF**"
		(at 0 -5.375 0)
		(layer "F.SilkS")
		(uuid "9d8da155-2f2b-4d8d-99ce-68865b9884fa")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "74HC125"
		(at 0 5.375 0)
		(layer "F.Fab")
		(uuid "d93b1582-5da8-40ab-95cf-b5755c6c628f")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "0514f686-ecbb-42f3-9c12-c049806d45ec")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "4afa8aa5-1fd6-4c5b-860e-c516bdf54f99")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr smd)
	(fp_line
		(start 0 -4.435)
		(end -1.95 -4.435)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f7838ab3-e240-45c0-abef-4d03fb444f0d")
	)
	(fp_line
		(start 0 -4.435)
		(end 1.95 -4.435)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "b8c68d3f-19f6-48b5-84be-10f0caa7de69")
	)
	(fp_line
		(start 0 4.435)
		(end -1.95 4.435)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "4fe3dc52-4d9e-4975-85b8-8735accc6936")
	)
	(fp_line
		(start 0 4.435)
		(end 1.95 4.435)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "cffda096-4372-427f-b6f0-20d7ef1cd08f")
	)
	(fp_poly
		(pts
			(xy -2.7 -4.37) (xy -2.94 -4.7) (xy -2.46 -4.7) (xy -2.7 -4.37)
		)
		(stroke
			(width 0.12)
			(type solid)
		)
		(fill yes)
		(layer "F.SilkS")
		(uuid "dbbfa1ab-3192-465c-b1bf-3ee6437ddb85")
	)
	(fp_line
		(start -3.7 -4.58)
		(end -3.7 4.58)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "3a43cb29-**************-fd2d84055752")
	)
	(fp_line
		(start -3.7 4.58)
		(end 3.7 4.58)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "e1fdcd94-83e4-402c-990d-7b4bdf874eeb")
	)
	(fp_line
		(start 3.7 -4.58)
		(end -3.7 -4.58)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "a3e683a4-8f32-47fd-bda7-c10566b5fa29")
	)
	(fp_line
		(start 3.7 4.58)
		(end 3.7 -4.58)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "387ab3c2-10af-4646-8f62-c3f575c25d60")
	)
	(fp_line
		(start -1.95 -3.35)
		(end -0.975 -4.325)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "cf462ffd-3627-4b1a-8b34-1e4c1d64f1d2")
	)
	(fp_line
		(start -1.95 4.325)
		(end -1.95 -3.35)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "f8830b63-a94a-495f-8ecb-07687d1f0cfc")
	)
	(fp_line
		(start -0.975 -4.325)
		(end 1.95 -4.325)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "507d3ec1-b6a1-4311-a76e-7f9c0a5dac63")
	)
	(fp_line
		(start 1.95 -4.325)
		(end 1.95 4.325)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "1075f303-d3d5-466a-999f-15c32f15d346")
	)
	(fp_line
		(start 1.95 4.325)
		(end -1.95 4.325)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "d2af902b-658e-4ba4-b6b3-3511edca4ea8")
	)
	(fp_text user "${REFERENCE}"
		(at 0 0 0)
		(layer "F.Fab")
		(uuid "0fd930e0-03a5-4cbc-81c0-52b15b03b1a4")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(pad "1" smd roundrect
		(at -2.475 -3.81)
		(size 1.95 0.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "4ee74a17-ee09-4b45-b01f-05a9e06bd7ac")
	)
	(pad "2" smd roundrect
		(at -2.475 -2.54)
		(size 1.95 0.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "b23f8326-4849-4986-b227-def117b8c6e6")
	)
	(pad "3" smd roundrect
		(at -2.475 -1.27)
		(size 1.95 0.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "b6e13d65-56e6-4445-8466-4161870140b8")
	)
	(pad "4" smd roundrect
		(at -2.475 0)
		(size 1.95 0.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "9c7b6e2c-c9ab-441a-8d8b-a75ab9d8a3f1")
	)
	(pad "5" smd roundrect
		(at -2.475 1.27)
		(size 1.95 0.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "cae814b2-a6fa-4fb4-b1a0-475869bd308d")
	)
	(pad "6" smd roundrect
		(at -2.475 2.54)
		(size 1.95 0.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "772132e3-f7cf-4fff-98cd-6cc8bb925f1e")
	)
	(pad "7" smd roundrect
		(at -2.475 3.81)
		(size 1.95 0.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "0d4926fd-1dae-47bd-8fa7-26d210993387")
	)
	(pad "8" smd roundrect
		(at 2.475 3.81)
		(size 1.95 0.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "28279ea5-ed3a-4a03-9ca1-3d3a6dd4c801")
	)
	(pad "9" smd roundrect
		(at 2.475 2.54)
		(size 1.95 0.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "35267a81-0358-40dc-89a1-5cbdbebd86b9")
	)
	(pad "10" smd roundrect
		(at 2.475 1.27)
		(size 1.95 0.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "3739114a-d295-4004-89fe-d7be7a661852")
	)
	(pad "11" smd roundrect
		(at 2.475 0)
		(size 1.95 0.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "c1ba9e4f-d762-4ac1-bdd3-86c4f6859295")
	)
	(pad "12" smd roundrect
		(at 2.475 -1.27)
		(size 1.95 0.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "9b6f1f04-e88f-48fe-a339-0ba4b12bc6b9")
	)
	(pad "13" smd roundrect
		(at 2.475 -2.54)
		(size 1.95 0.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "d5808892-e171-4c53-aeae-f1a953d106c0")
	)
	(pad "14" smd roundrect
		(at 2.475 -3.81)
		(size 1.95 0.6)
		(layers "F.Cu" "F.Mask" "F.Paste")
		(roundrect_rratio 0.25)
		(uuid "5be40ffc-8d96-4c1f-a9e4-bbfd5dcdf64f")
	)
	(embedded_fonts no)
	(model "${KICAD8_3DMODEL_DIR}/Package_SO.3dshapes/SOIC-14_3.9x8.7mm_P1.27mm.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
