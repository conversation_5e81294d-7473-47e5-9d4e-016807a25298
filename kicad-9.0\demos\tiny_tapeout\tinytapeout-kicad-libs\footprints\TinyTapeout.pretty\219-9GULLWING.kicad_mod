(footprint "219-9GULLWING" (version 20221018) (generator pcbnew)
  (layer "F.Cu")
  (descr "Dip Switch SPST 9 Position Surface Mount Slide (Standard) Actuator 100mA 20VDC")
  (tags "SWITCH SLIDE DIP SPST 100MA 20V, GULL WING")
  (attr smd)
  (fp_text reference "REF**" (at -13.4 2 90 unlocked) (layer "F.SilkS")
      (effects (font (size 1.2 1.2) (thickness 0.2) bold))
    (tstamp a57f9519-6e1e-42f1-a24d-78cd73a3976d)
  )
  (fp_text value "219-9XXX" (at 0.5 0 unlocked) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 58c0d3f1-0555-4bf8-9c58-1459b6d54dae)
  )
  (fp_text user "${REFERENCE}" (at -5.5 -1.1 unlocked) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)) (justify left bottom))
    (tstamp ce5155c9-4404-450b-9771-5b45b624b1f5)
  )
  (fp_line (start -12.5 -4) (end -12.5 4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp edae452b-bb12-45bf-b13d-da3c42b2480c))
  (fp_line (start -12.5 4) (end -11 4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 7ec43abd-4d18-4c91-a355-2aee6cbf855d))
  (fp_line (start -11.882843 4.8) (end -11.317157 4.8)
    (stroke (width 0.4) (type default)) (layer "F.SilkS") (tstamp a3cc6661-cb21-4a2f-9469-0ce79a09fe5b))
  (fp_line (start -11 -4) (end -12.5 -4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp d229807f-ac0d-4b69-a41a-4a0f031b6952))
  (fp_line (start -10.2 -1.2) (end -10.2 -3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 5c21f019-2d33-42f4-8e8a-5619432b939a))
  (fp_line (start -10.2 1) (end -10.8 -1.4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp fecf0249-bcf8-4a4b-88aa-38ee49555d3f))
  (fp_line (start -10.2 1) (end -10.2 3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 493912a8-8a69-46dc-aad1-f8ec3456f825))
  (fp_line (start -9.4 -4) (end -8.4 -4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 09c4c289-93c2-4417-a36c-29d6ba4a8d86))
  (fp_line (start -9.4 4) (end -8.4 4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp d6db40a6-e9dc-4816-9ff1-147b1fc556c0))
  (fp_line (start -7.6 -1.2) (end -7.6 -3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp abb29465-752f-4883-9649-2b64ce2f74a4))
  (fp_line (start -7.6 1) (end -8.2 -1.4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 4a047898-ae51-4ec7-bfe9-1fd628bae6ae))
  (fp_line (start -7.6 1) (end -7.6 3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 3606d2d3-cdf4-4f1f-89f4-a53a05d7cb88))
  (fp_line (start -6.8 -4) (end -5.8 -4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp a0e670e0-773c-47ba-b5e0-274988443cba))
  (fp_line (start -6.8 4) (end -5.8 4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 2ba0d669-9faf-4959-8bd3-c9e930b314e0))
  (fp_line (start -5 -1.2) (end -5 -3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 11c765e6-71d6-4b4f-b30c-a2db925cae58))
  (fp_line (start -5 1) (end -5.6 -1.4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp fa20b2bf-85d2-4ced-9587-8e241e9f2f10))
  (fp_line (start -5 1) (end -5 3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 2f0356dd-8453-4f0b-b4c9-bb7bbc4c3d6a))
  (fp_line (start -4.4 -4) (end -3.4 -4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp aa7d3086-7b1e-4868-b680-86d8d388f068))
  (fp_line (start -4.4 4) (end -3.4 4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 60058ae2-8040-4ecf-b9f3-cfadab144f33))
  (fp_line (start -2.6 -1.2) (end -2.6 -3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 65b3b4d5-ec0b-454e-950b-22832bcd626c))
  (fp_line (start -2.6 1) (end -3.2 -1.4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 8d9afce4-b119-46f2-878d-a602610b59ab))
  (fp_line (start -2.6 1) (end -2.6 3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp f99a6c20-2785-459a-93a7-d029b86687eb))
  (fp_line (start -1.8 -4) (end -0.8 -4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp b4dd2627-d1e5-48d2-8e00-f62388881016))
  (fp_line (start -1.8 4) (end -0.8 4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp a0d9afb4-54cb-4b0c-886e-c031d141bca4))
  (fp_line (start 0 -1.2) (end 0 -3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp d50fd351-bfa0-47a2-98dc-65182cea6837))
  (fp_line (start 0 1) (end -0.6 -1.4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp cf188e08-2656-4db4-bc9e-9b9cf73f8d04))
  (fp_line (start 0 1) (end 0 3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp eb3ab402-2bf3-4bf7-9add-9c5d241be2b0))
  (fp_line (start 0.8 -4) (end 1.8 -4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp ee98edd8-2a73-49cc-a957-08a6592fb5cb))
  (fp_line (start 0.8 4) (end 1.8 4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 8626c931-4a8d-4fa6-b996-10be9a65f88d))
  (fp_line (start 2.6 -1.2) (end 2.6 -3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp b46ee2f6-43b8-4c09-a607-deb1a4ebad80))
  (fp_line (start 2.6 1) (end 2 -1.4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 1c271f58-eef6-4d47-8ca8-343b1f5dcb95))
  (fp_line (start 2.6 1) (end 2.6 3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp da84cb20-bddb-49fc-8472-db1cee451891))
  (fp_line (start 3.2 -4) (end 4.2 -4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 0e74ad6b-e71e-4155-98d1-f6bf8d021e29))
  (fp_line (start 3.2 4) (end 4.2 4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp a15bad26-e880-4cd9-a447-d877de8be1b1))
  (fp_line (start 5 -1.2) (end 5 -3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp cb7355b5-bfec-4dab-af1b-5ff8585cafc8))
  (fp_line (start 5 1) (end 4.4 -1.4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 176f009b-9521-4c5b-9331-804417f41d83))
  (fp_line (start 5 1) (end 5 3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 452474a8-172d-44bb-bbfc-5109dfe7ef64))
  (fp_line (start 5.8 -4) (end 6.8 -4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 5d915751-523a-4118-b59e-f7b10744d53a))
  (fp_line (start 5.8 4) (end 6.8 4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp d04ecb76-dea7-4914-9813-0349da8ea7c6))
  (fp_line (start 7.6 -1.2) (end 7.6 -3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp c8285a78-776d-435f-b481-609222291d9b))
  (fp_line (start 7.6 1) (end 7 -1.4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 7cf11a46-a436-4860-a736-9c7ddf547bb3))
  (fp_line (start 7.6 1) (end 7.6 3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp c3b5212a-a4af-4423-a631-ae580e2c6c49))
  (fp_line (start 8.4 -4) (end 9.4 -4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 7adfa21f-e738-493e-81dd-5859fbd19006))
  (fp_line (start 8.4 4) (end 9.4 4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp b6136f53-a8fd-475d-b2f3-a455a424af24))
  (fp_line (start 10.2 -1.2) (end 10.2 -3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp a829c677-e5fb-49a8-bd13-c233aeb5fba9))
  (fp_line (start 10.2 1) (end 9.6 -1.4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 4f9fa9b1-f146-45a5-851c-78dba81c0713))
  (fp_line (start 10.2 1) (end 10.2 3)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 45524b3d-2245-4c38-b88a-f05a00cef589))
  (fp_line (start 10.8 4) (end 12.3 4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 15a13539-b261-4806-9d98-45d59c8841c3))
  (fp_line (start 12.3 -4) (end 10.8 -4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp c93fca1c-bd3c-4736-927a-d2d6d19d9db5))
  (fp_line (start 12.3 4) (end 12.3 -4)
    (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 2a21e6e9-8156-414c-bcbc-087cf49f842d))
  (fp_circle (center -11.6 4.8) (end -11.317157 4.8)
    (stroke (width 0.4) (type default)) (fill none) (layer "F.SilkS") (tstamp 361d03e7-bf48-4424-aa84-996660e1c9ff))
  (fp_circle (center -10.2 -1.2) (end -10 -1.2)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp 52aedd96-bf04-47b1-805e-bb239efeb662))
  (fp_circle (center -10.2 1) (end -10 1)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp 4d203b3d-1632-494f-a694-7f7d92dba04b))
  (fp_circle (center -7.6 -1.2) (end -7.4 -1.2)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp a91916b9-0b3c-49ea-a02e-915b57cd1d22))
  (fp_circle (center -7.6 1) (end -7.4 1)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp 407e74c8-35cf-4bbb-b11b-b00b88c622e5))
  (fp_circle (center -5 -1.2) (end -4.8 -1.2)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp fcd2e964-e611-46e5-93a9-24039b107467))
  (fp_circle (center -5 1) (end -4.8 1)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp 0ada3ab1-a383-44d4-9828-2021a306c038))
  (fp_circle (center -2.6 -1.2) (end -2.4 -1.2)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp d9ad7655-d61f-44de-8357-84042bfd8d3b))
  (fp_circle (center -2.6 1) (end -2.4 1)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp f28949c2-0248-4bd4-8560-49525cc84c18))
  (fp_circle (center 0 -1.2) (end 0.2 -1.2)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp 498aa09f-0975-4d99-b36f-bf2d61ce2a81))
  (fp_circle (center 0 1) (end 0.2 1)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp fc60f785-541c-457c-b2cc-54fdde2559f9))
  (fp_circle (center 2.6 -1.2) (end 2.8 -1.2)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp 55e8ec04-aba2-4192-8048-03db24a8c1f1))
  (fp_circle (center 2.6 1) (end 2.8 1)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp cee57b0d-4da7-45db-b294-d25a130987fe))
  (fp_circle (center 5 -1.2) (end 5.2 -1.2)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp 8576f151-d840-4db9-b119-b6ee159b70df))
  (fp_circle (center 5 1) (end 5.2 1)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp c0c16fc7-11d3-4554-946c-9beee3ba218d))
  (fp_circle (center 7.6 -1.2) (end 7.8 -1.2)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp 4c72a4bc-dcbc-4531-9360-9a77b58fe03c))
  (fp_circle (center 7.6 1) (end 7.8 1)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp beb1c329-b9bf-45f9-820f-4d57e1e7cafe))
  (fp_circle (center 10.2 -1.2) (end 10.4 -1.2)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp 875df8eb-3f06-4286-848f-13561b0c82b2))
  (fp_circle (center 10.2 1) (end 10.4 1)
    (stroke (width 0.25) (type default)) (fill none) (layer "F.SilkS") (tstamp 7a98004b-6c5b-466f-b6cb-655296a06af0))
  (fp_line (start -13 -4.5) (end -13 4.5)
    (stroke (width 0.12) (type default)) (layer "F.CrtYd") (tstamp 3ef6daf5-4144-4a75-bc9a-f52f0f132378))
  (fp_line (start -13 4.5) (end -11 4.5)
    (stroke (width 0.12) (type default)) (layer "F.CrtYd") (tstamp 2d1de330-8b9b-4c20-a242-d1276f19c185))
  (fp_line (start -11 -6) (end -11 -4.5)
    (stroke (width 0.12) (type default)) (layer "F.CrtYd") (tstamp 27fc4084-86f9-4a8c-9aa4-cafef02ab8c8))
  (fp_line (start -11 -4.5) (end -13 -4.5)
    (stroke (width 0.12) (type default)) (layer "F.CrtYd") (tstamp 33b8c966-8cec-4703-8dcf-ece2553c3e83))
  (fp_line (start -11 4.5) (end -11 6)
    (stroke (width 0.12) (type default)) (layer "F.CrtYd") (tstamp 2f5e156d-da79-42d3-ad39-9caeb4ec643e))
  (fp_line (start -11 6) (end 11 6)
    (stroke (width 0.12) (type default)) (layer "F.CrtYd") (tstamp 3fc48e98-f3da-48fb-b0e1-595c2ad972fb))
  (fp_line (start 11 -6) (end -11 -6)
    (stroke (width 0.12) (type default)) (layer "F.CrtYd") (tstamp 224c664d-8a41-4475-afc0-ceb09f708089))
  (fp_line (start 11 -4.5) (end 11 -6)
    (stroke (width 0.12) (type default)) (layer "F.CrtYd") (tstamp 613c02d7-6353-454f-b262-dfbbc0ea89e6))
  (fp_line (start 11 4.5) (end 13 4.5)
    (stroke (width 0.12) (type default)) (layer "F.CrtYd") (tstamp 4248a9a6-1aa4-408e-afed-a3ee2f419f39))
  (fp_line (start 11 6) (end 11 4.5)
    (stroke (width 0.12) (type default)) (layer "F.CrtYd") (tstamp 3c04c99b-35f3-42dc-bcea-42bf0477ca3b))
  (fp_line (start 13 -4.5) (end 11 -4.5)
    (stroke (width 0.12) (type default)) (layer "F.CrtYd") (tstamp 340d2ae3-f6f9-4485-9d42-8a67100e5ab2))
  (fp_line (start 13 4.5) (end 13 -4.5)
    (stroke (width 0.12) (type default)) (layer "F.CrtYd") (tstamp 4d27c8db-a34f-4eef-a13c-464f3d30ec11))
  (fp_line (start -13 -4.5) (end -13 4.5)
    (stroke (width 0.1) (type default)) (layer "F.Fab") (tstamp 2b8b7be6-26ea-4e75-96d9-d5d7bee50f41))
  (fp_line (start -13 4.5) (end 13 4.5)
    (stroke (width 0.1) (type default)) (layer "F.Fab") (tstamp 4cf23cc6-f70b-478b-823d-e21a2584c71d))
  (fp_line (start 13 -4.5) (end -13 -4.5)
    (stroke (width 0.1) (type default)) (layer "F.Fab") (tstamp 68682eef-428e-4e19-870c-a0bb886ef1ec))
  (fp_line (start 13 4.5) (end 13 -4.5)
    (stroke (width 0.1) (type default)) (layer "F.Fab") (tstamp 7115c8d8-e60d-45a5-b798-e02c6b6176fd))
  (pad "1" smd roundrect (at -10.16 4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp 84454540-39b9-4c7e-9fd0-f13432dfca6b))
  (pad "2" smd roundrect (at -7.62 4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp 1e72000b-7b08-44ca-a9ac-e3f191a3bb39))
  (pad "3" smd roundrect (at -5.08 4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp 0274514c-3a42-4077-8d85-51d282190935))
  (pad "4" smd roundrect (at -2.54 4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp 16464584-db14-47c2-b58c-755dc695bafc))
  (pad "5" smd roundrect (at 0 4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp 6d2aa7b5-eccc-49a1-ab39-b0f615bba568))
  (pad "6" smd roundrect (at 2.54 4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp 069cd0db-6518-4a29-91c7-de42d2767095))
  (pad "7" smd roundrect (at 5.08 4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp 9e9ac8ee-dd17-44da-b72d-56daab26ea45))
  (pad "8" smd roundrect (at 7.62 4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp 5b12b30a-b2b9-4b40-b3e4-1413ec1afc71))
  (pad "9" smd roundrect (at 10.16 4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp 5abbde57-9e10-4c6c-9f51-f83a1ade9c5e))
  (pad "10" smd roundrect (at 10.16 -4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp 93d5653b-b0f2-495b-aac6-0e94595c6cea))
  (pad "11" smd roundrect (at 7.62 -4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp e726ebc0-5061-4a66-b13b-394e6dbe57c8))
  (pad "12" smd roundrect (at 5.08 -4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp 13e0aab4-4d9b-45d9-b35b-e7367657ee73))
  (pad "13" smd roundrect (at 2.54 -4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp 00b65496-5406-4863-806b-c4335d46f964))
  (pad "14" smd roundrect (at 0 -4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp 290b7c83-6c70-4d6d-a9da-887ea41a4138))
  (pad "15" smd roundrect (at -2.54 -4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp d2fc39f7-a255-45c7-9351-98715c95d789))
  (pad "16" smd roundrect (at -5.08 -4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp b8ff850b-d465-42ff-bb02-0032a0c805ce))
  (pad "17" smd roundrect (at -7.62 -4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp cca07145-bf55-4a46-b121-8d4783158c68))
  (pad "18" smd roundrect (at -10.16 -4.3 90) (size 2.44 1.13) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1) (tstamp 6f6bdb43-9f7a-4db8-829a-ab5fcb3e57f0))
)
