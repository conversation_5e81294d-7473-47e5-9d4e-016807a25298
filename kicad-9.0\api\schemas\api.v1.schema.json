{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://go.kicad.org/api/schemas/v1", "title": "KiCad API Plugin Schema", "description": "KiCad IPC API Plugin and Action schema", "$ref": "#/definitions/Plugin", "definitions": {"Plugin": {"type": "object", "properties": {"identifier": {"type": "string", "description": "Plugin identifier", "pattern": "^[a-zA-Z][-_a-zA-Z0-9.]{0,98}[a-zA-Z0-9]$"}, "name": {"type": "string", "maxLength": 200, "description": "A human-readable name for your plugin"}, "description": {"type": "string", "maxLength": 500, "description": "A longer-form description for your plugin (not currently displayed in KiCad)"}, "runtime": {"type": "object", "properties": {"type": {"type": "string", "enum": ["python", "exec"], "description": "How KiCad should launch the plugin"}, "min_version": {"type": "string", "description": "Minimum required Python version if runtime is 'python'.  Optional; not yet used by KiCad"}}, "required": ["type"]}, "actions": {"type": "array", "description": "A list of actions your plugin provides, each of which can appear on the toolbar as a button", "items": {"type": "object", "properties": {"identifier": {"type": "string", "description": "An identifier for the action, must be unique within a plugin", "pattern": "^[a-zA-Z][-_a-zA-Z0-9.]{0,48}[a-zA-Z0-9]$"}, "name": {"type": "string", "maxLength": 200, "description": "A human-readable name for the action (normally a verb or verb phrase)"}, "description": {"type": "string", "maxLength": 500, "description": "A human-readable description for the action"}, "show-button": {"type": "boolean", "description": "Whether or not to show the action in the toolbar by default"}, "scopes": {"type": "array", "description": "Which areas within KiCad this action is valid for", "items": {"type": "string", "enum": ["pcb", "schematic", "footprint", "symbol", "project_manager"]}}, "entrypoint": {"type": "string", "description": "The way KiCad should launch this action (for example, the name of a Python script)"}, "icons-light": {"type": "array", "description": "A list of one or more paths to PNG files (use multiple files for high DPI support) to be shown in light mode", "items": {"type": "string", "pattern": "^.*\\.png$"}}, "icons-dark": {"type": "array", "description": "A list of one or more paths to PNG files (use multiple files for high DPI support) to be shown in dark mode", "items": {"type": "string", "pattern": "^.*\\.png$"}}}, "required": ["identifier", "name", "description", "entrypoint"]}}}, "required": ["identifier", "name", "description", "runtime", "actions"]}}}