(kicad_symbol_lib
	(version 20241209)
	(generator "kicad_symbol_editor")
	(generator_version "9.0")
	(symbol "C"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.254)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "C"
			(at 0.635 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "C"
			(at 0.635 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0.9652 -3.81 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "C? C_????_* C_???? SMD*_c Capacitor*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "C_0_1"
			(polyline
				(pts
					(xy -2.032 0.762) (xy 2.032 0.762)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.032 -0.762) (xy 2.032 -0.762)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "C_1_1"
			(pin passive line
				(at 0 3.81 270)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 0 -3.81 90)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CONN_1"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.762)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "P"
			(at 2.032 0 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(justify left)
			)
		)
		(property "Value" "CONN_1"
			(at 0 1.397 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" "MountingHole:MountingHole_3.2mm_M3_DIN965_Pad"
			(at 0 -1.27 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "CONN_1_0_1"
			(polyline
				(pts
					(xy -0.762 0) (xy -1.27 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 0 0)
				(radius 0.7874)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "CONN_1_1_1"
			(pin passive line
				(at -3.81 0 0)
				(length 2.54)
				(name "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CONN_2"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "P"
			(at -1.27 0 90)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "CONN_2"
			(at 1.27 0 90)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "CONN_2_0_1"
			(rectangle
				(start -2.54 3.81)
				(end 2.54 -3.81)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "CONN_2_1_1"
			(pin passive inverted
				(at -8.89 2.54 0)
				(length 6.35)
				(name "P1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive inverted
				(at -8.89 -2.54 0)
				(length 6.35)
				(name "PM"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CP"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.254)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "C"
			(at 0.635 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "CP"
			(at 0.635 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0.9652 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Polarised capacitor"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "cap capacitor"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "CP_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "CP_0_1"
			(rectangle
				(start -2.286 0.508)
				(end -2.286 1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -2.286 0.508)
				(end 2.286 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.778 2.286) (xy -0.762 2.286)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 2.794) (xy -1.27 1.778)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start 2.286 1.016)
				(end -2.286 1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start 2.286 1.016)
				(end 2.286 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start 2.286 -0.508)
				(end -2.286 -1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "CP_1_1"
			(pin passive line
				(at 0 3.81 270)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 0 -3.81 90)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "ECC83"
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 3.302 7.874 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "ECC83"
			(at 8.89 -7.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "VALVE-NOVAL_P"
			(at 10.16 -8.89 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_locked" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(symbol "ECC83_0_1"
			(polyline
				(pts
					(xy -5.08 2.54) (xy -5.08 -2.54) (xy -5.08 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -5.08 2.54)
				(mid 0 7.5979)
				(end 5.08 2.54)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start 5.08 -2.54)
				(mid 0 -7.5979)
				(end -5.08 -2.54)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 2.54) (xy 5.08 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "ECC83_1_0"
			(polyline
				(pts
					(xy -2.54 -5.08) (xy -2.54 -7.62)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 5.08) (xy 0 7.62)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "ECC83_1_1"
			(polyline
				(pts
					(xy -5.08 0) (xy -3.175 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.54 5.08) (xy 2.794 5.08) (xy 2.794 5.08)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.905 0) (xy -3.175 0)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -0.635 0) (xy 0.635 0)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -2.54 -5.08)
				(mid 0 -3.0968)
				(end 2.54 -5.08)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.905 0) (xy 3.175 0)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin input line
				(at -7.62 0 0)
				(length 2.54)
				(name "G"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at -2.54 -10.16 90)
				(length 2.54)
				(name "K"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 0 10.16 270)
				(length 2.54)
				(name "A"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(symbol "ECC83_2_0"
			(polyline
				(pts
					(xy -2.54 -5.08) (xy -2.54 -7.62)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 5.08) (xy 0 7.62)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "ECC83_2_1"
			(polyline
				(pts
					(xy -5.08 0) (xy -3.175 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.54 5.08) (xy 2.794 5.08) (xy 2.794 5.08)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.905 0) (xy -3.175 0)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -0.635 0) (xy 0.635 0)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -2.54 -5.08)
				(mid 0 -3.0968)
				(end 2.54 -5.08)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.905 0) (xy 3.175 0)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin input line
				(at -7.62 0 0)
				(length 2.54)
				(name "G"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at -2.54 -10.16 90)
				(length 2.54)
				(name "K"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 0 10.16 270)
				(length 2.54)
				(name "A"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(symbol "ECC83_3_1"
			(arc
				(start -2.54 -6.35)
				(mid -1.27 -5.5651)
				(end 0 -6.35)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start 0 -6.35)
				(mid 1.27 -5.5651)
				(end 2.54 -6.35)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin input line
				(at -2.54 -11.43 90)
				(length 5.08)
				(name "F1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin input line
				(at 0 -11.43 90)
				(length 5.08)
				(name "F2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin input line
				(at 2.54 -11.43 90)
				(length 5.08)
				(name "F1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "GND"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -6.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "GND_0_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "GND_1_1"
			(pin power_in line
				(at 0 0 270)
				(length 0)
				(hide yes)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "PWR_FLAG"
		(power)
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#FLG"
			(at 0 2.413 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "PWR_FLAG"
			(at 0 4.572 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "PWR_FLAG_0_0"
			(pin power_out line
				(at 0 0 90)
				(length 0)
				(name "pwr"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
		)
		(symbol "PWR_FLAG_0_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 1.27) (xy -1.905 2.54) (xy 0 3.81) (xy 1.905 2.54) (xy 0 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "R"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "R"
			(at 2.032 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "R"
			(at 0 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at -1.778 0 90)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "R_* Resistor_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "R_0_1"
			(rectangle
				(start -1.016 -2.54)
				(end 1.016 2.54)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "R_1_1"
			(pin passive line
				(at 0 3.81 270)
				(length 1.27)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 0 -3.81 90)
				(length 1.27)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
)
