(kicad_sch
	(version 20231120)
	(generator "eeschema")
	(generator_version "7.99")
	(uuid "0f2b0518-a480-4225-861e-30a30561b94c")
	(paper "A4")
	(lib_symbols
		(symbol "Simulation_SPICE:VPULSE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "VPULSE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Voltage source, pulse"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Pins" "1=+ 2=-"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Type" "PULSE"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Device" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Sim.Params" "y1=0 y2=1 td=2n tr=2n tf=2n tw=50n per=100n"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "ki_keywords" "simulation"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VPULSE_0_0"
				(polyline
					(pts
						(xy -2.032 -0.762) (xy -1.397 -0.762) (xy -1.143 0.762) (xy -0.127 0.762) (xy 0.127 -0.762) (xy 1.143 -0.762)
						(xy 1.397 0.762) (xy 2.032 0.762)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VPULSE_0_1"
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "VPULSE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "power:GND"
			(power)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "GND"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "GND_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "GND_1_1"
				(pin power_in line
					(at 0 0 270)
					(length 0) hide
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "up-down:Up-Down"
			(pin_names
				(offset 1.016)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at -7.62 8.89 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "up-down"
				(at -7.62 -8.89 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "3 Bit Up-Down Counter"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "CMOS"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "DIP?16*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Up-Down_1_0"
				(pin input clock
					(at -12.7 0 0)
					(length 5.08)
					(name "CK"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at -12.7 -2.54 0)
					(length 5.08)
					(name "Up-down"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin output line
					(at 12.7 2.54 180)
					(length 5.08)
					(name "Q1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin output line
					(at 12.7 0 180)
					(length 5.08)
					(name "Q2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin output line
					(at 12.7 -2.54 180)
					(length 5.08)
					(name "Q3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "Up-Down_1_1"
				(rectangle
					(start -7.62 5.08)
					(end 7.62 -5.08)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
		)
	)
	(wire
		(pts
			(xy 213.36 80.01) (xy 226.06 80.01)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "39204084-959a-4a4e-bf04-9a006db6ef8f")
	)
	(wire
		(pts
			(xy 213.36 101.6) (xy 213.36 80.01)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "956646e7-e2cf-4ba9-a348-09c6f495a7a7")
	)
	(wire
		(pts
			(xy 222.25 87.63) (xy 222.25 82.55)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a0524b66-0999-4dd1-ac02-3162f1336bd2")
	)
	(wire
		(pts
			(xy 222.25 82.55) (xy 226.06 82.55)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f53f4d3a-d38b-47d4-9c67-56f3b6ab6f71")
	)
	(text "Up-Down Counter"
		(exclude_from_sim no)
		(at 242.57 115.57 0)
		(effects
			(font
				(size 1.27 1.27)
			)
		)
		(uuid "caec7e6e-c8c7-4993-950a-07b811b7bc9d")
	)
	(label "q1"
		(at 251.46 77.47 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "26ff1228-8180-4483-83ab-dec59e66dc93")
	)
	(label "clk"
		(at 213.36 87.63 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "5d21d9e8-311f-4e39-82f8-f7c9ee72241a")
	)
	(label "ud"
		(at 222.25 87.63 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "a6c6f0ac-ff43-4b1f-bc10-5b15e65a8a93")
	)
	(label "q2"
		(at 251.46 80.01 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "eb8ed79c-f431-4735-8ee9-d4364eb83a6d")
	)
	(label "q3"
		(at 251.46 82.55 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "f0be6da6-7c3e-45e1-8dc1-a9b5cd502fb7")
	)
	(symbol
		(lib_id "power:GND")
		(at 213.36 111.76 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "18aed326-7f41-466a-bdbe-930e35f64124")
		(property "Reference" "#PWR02"
			(at 213.36 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 213.36 116.84 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 213.36 111.76 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 213.36 111.76 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 213.36 111.76 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b3dbeae3-4577-41d9-863f-3280a952427c")
		)
		(instances
			(project "up-down-c"
				(path "/0f2b0518-a480-4225-861e-30a30561b94c"
					(reference "#PWR02")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Simulation_SPICE:VPULSE")
		(at 222.25 92.71 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "72e70303-0e4f-4137-9bc3-7ba2387fb34b")
		(property "Reference" "Vupd1"
			(at 226.06 90.0401 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "VPULSE"
			(at 226.06 92.5801 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 222.25 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 222.25 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Voltage source, pulse"
			(at 222.25 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 222.25 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Type" "PULSE"
			(at 222.25 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "V"
			(at 222.25 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Params" "y1=0 y2=5 td=1u tr=1u tf=1u tw=500u per=1m"
			(at 226.06 95.1201 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(pin "2"
			(uuid "3bd31ede-4ded-41e7-acab-4ae1b3fc2817")
		)
		(pin "1"
			(uuid "ffbb82e4-fb25-435a-9ee4-214b87137b01")
		)
		(instances
			(project "up-down-c"
				(path "/0f2b0518-a480-4225-861e-30a30561b94c"
					(reference "Vupd1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "up-down:Up-Down")
		(at 238.76 80.01 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "9bce7a6f-e202-49d4-af45-aaa764400f75")
		(property "Reference" "U1"
			(at 238.76 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "up-down"
			(at 238.76 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 238.76 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 238.76 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "3 Bit Up-Down Counter"
			(at 238.76 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Library" "state-machine-3b-count.lib"
			(at 238.76 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Name" "3bcounter"
			(at 238.76 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "SUBCKT"
			(at 238.76 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=clock 2=updown 3=out_b0 4=out_b1 5=out_b2"
			(at 238.76 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "5"
			(uuid "e56db106-d0f8-499a-ae37-9c65d3d28a9e")
		)
		(pin "1"
			(uuid "28bcb2d8-a2e8-4e7e-9922-61c4943b722b")
		)
		(pin "3"
			(uuid "4abc789d-9f82-497e-85b6-3bf86e007549")
		)
		(pin "4"
			(uuid "fb3a5557-3e62-4b69-8c0f-d8ea43993f3a")
		)
		(pin "2"
			(uuid "63c53177-5bad-4070-8943-1e5c685cff17")
		)
		(instances
			(project "up-down-c"
				(path "/0f2b0518-a480-4225-861e-30a30561b94c"
					(reference "U1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Simulation_SPICE:VPULSE")
		(at 213.36 106.68 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "c1912663-3a96-4fe0-a757-d65b4184483e")
		(property "Reference" "Vclk1"
			(at 217.17 104.0101 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "VPULSE"
			(at 217.17 106.5501 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 213.36 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 213.36 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Voltage source, pulse"
			(at 213.36 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 213.36 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Type" "PULSE"
			(at 213.36 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "V"
			(at 213.36 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Params" "y1=0 y2=5 td=1u tr=1u tf=1u tw=10u per=24u"
			(at 217.17 109.0901 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(pin "1"
			(uuid "806fccef-afcc-4442-8850-213ce3c6b9da")
		)
		(pin "2"
			(uuid "329181b6-1566-43fe-beb9-a445358e0622")
		)
		(instances
			(project "up-down-c"
				(path "/0f2b0518-a480-4225-861e-30a30561b94c"
					(reference "Vclk1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 222.25 97.79 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "e5e9efc2-83ab-45ed-9770-635173d255e5")
		(property "Reference" "#PWR01"
			(at 222.25 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 224.79 101.092 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 222.25 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 222.25 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 222.25 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "5aed7ad5-04f3-442a-a1f2-b64ae018f28e")
		)
		(instances
			(project "up-down-c"
				(path "/0f2b0518-a480-4225-861e-30a30561b94c"
					(reference "#PWR01")
					(unit 1)
				)
			)
		)
	)
	(sheet_instances
		(path "/"
			(page "1")
		)
	)
)