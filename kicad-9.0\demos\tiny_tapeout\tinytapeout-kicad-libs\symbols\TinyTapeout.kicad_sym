(kicad_symbol_lib
	(version 20241209)
	(generator "kicad_symbol_editor")
	(generator_version "9.0")
	(symbol "+1V8"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+1V8"
			(at 0 3.556 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"+1V8\""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "power-flag"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "+1V8_0_1"
			(polyline
				(pts
					(xy -0.762 1.27) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 2.54) (xy 0.762 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "+1V8_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "+1V8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "+3V3"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+3V3"
			(at 0 3.556 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"+3V3\""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "global power"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "+3V3_0_1"
			(polyline
				(pts
					(xy -0.762 1.27) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 2.54) (xy 0.762 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "+3V3_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "+3V3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "+5V"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+5V"
			(at 0 3.556 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"+5V\""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "power-flag"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "+5V_0_1"
			(polyline
				(pts
					(xy -0.762 1.27) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 2.54) (xy 0.762 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "+5V_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "+5V"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "434121025816"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 1.016)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "SW"
			(at -0.635 2.286 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify bottom)
			)
		)
		(property "Value" "434121025816"
			(at -5.08 -3.048 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left bottom)
			)
		)
		(property "Footprint" "434121025816:434121025816"
			(at 0 -7.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify bottom)
				(hide yes)
			)
		)
		(property "Datasheet" "https://www.we-online.com/redexpert/spec/434121025816?ae"
			(at 2.54 -7.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "WS-TASV SMT Tact Switch 6.0x3.8 mm"
			(at 2.54 -7.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify bottom)
				(hide yes)
			)
		)
		(property "MF" "Wurth Electronics"
			(at 0 -7.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify bottom)
				(hide yes)
			)
		)
		(property "ki_keywords" "MOM, switch, Tact"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "434121025816_0_0"
			(polyline
				(pts
					(xy -2.54 0) (xy -1.805 0)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.47 0.47) (xy 1.34 1.04)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.27 0)
				(radius 0.508)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 0)
				(radius 0.508)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.805 0) (xy 2.54 0)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at -5.08 0 0)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 5.08 0 180)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "74CBTLV3257"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 17.78 1.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "74CBTLV3257"
			(at 15.24 -1.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at -1.27 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "http://www.ti.com/lit/ds/symlink/sn74cbtlv3257.pdf"
			(at -1.27 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Quad 1:2 FET Multiplexer/Demultiplexer, Low-Voltage"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_locked" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "ki_keywords" "mux demux low-voltage"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "SSOP*3.9x4.9mm*P0.635mm* TSSOP*4.4x5mm*P0.65mm* TVSOP*4.4x3.6mm*P0.4mm* SOIC*3.9x9.9mm*P1.27mm*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "74CBTLV3257_1_1"
			(polyline
				(pts
					(xy -5.08 0) (xy -2.794 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -3.81 4.445)
				(end 3.81 -4.445)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(circle
				(center -2.159 0)
				(radius 0.508)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.651 0.127) (xy 2.54 1.905)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.159 2.54)
				(radius 0.508)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.159 -2.54)
				(radius 0.508)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 2.54) (xy 2.794 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 -2.54) (xy 2.794 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at -7.62 0 0)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 2.54 180)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 -2.54 180)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "74CBTLV3257_2_1"
			(polyline
				(pts
					(xy -5.08 0) (xy -2.794 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -3.81 4.445)
				(end 3.81 -4.445)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(circle
				(center -2.159 0)
				(radius 0.508)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.651 0.127) (xy 2.54 1.905)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.159 2.54)
				(radius 0.508)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.159 -2.54)
				(radius 0.508)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 2.54) (xy 2.794 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 -2.54) (xy 2.794 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at -7.62 0 0)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 2.54 180)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 -2.54 180)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "74CBTLV3257_3_1"
			(polyline
				(pts
					(xy -5.08 0) (xy -2.794 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -3.81 4.445)
				(end 3.81 -4.445)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(circle
				(center -2.159 0)
				(radius 0.508)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.651 0.127) (xy 2.54 1.905)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.159 2.54)
				(radius 0.508)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.159 -2.54)
				(radius 0.508)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 2.54) (xy 2.794 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 -2.54) (xy 2.794 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at -7.62 0 0)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 2.54 180)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 -2.54 180)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "74CBTLV3257_4_1"
			(polyline
				(pts
					(xy -5.08 0) (xy -2.794 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -3.81 4.445)
				(end 3.81 -4.445)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(circle
				(center -2.159 0)
				(radius 0.508)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.651 0.127) (xy 2.54 1.905)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.159 2.54)
				(radius 0.508)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.159 -2.54)
				(radius 0.508)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 2.54) (xy 2.794 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 -2.54) (xy 2.794 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at -7.62 0 0)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 2.54 180)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 -2.54 180)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "74CBTLV3257_5_1"
			(rectangle
				(start 3.81 6.35)
				(end -6.35 -6.35)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin input line
				(at -10.16 2.54 0)
				(length 3.81)
				(name "S"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -10.16 -2.54 0)
				(length 3.81)
				(name "~{OE}"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 0 10.16 270)
				(length 3.81)
				(name "VCC"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 0 -10.16 90)
				(length 3.81)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "AP2112K-1.8"
		(pin_names
			(offset 0.254)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at -5.08 5.715 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "AP2112K-1.8"
			(at 0 5.715 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Package_TO_SOT_SMD:SOT-23-5"
			(at 0 8.255 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://www.diodes.com/assets/Datasheets/AP2112.pdf"
			(at 0 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "600mA low dropout linear regulator, with enable pin, 2.5V-6V input voltage range, 1.8V fixed positive output, SOT-23-5"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "linear regulator ldo fixed positive"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "SOT?23?5*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "AP2112K-1.8_0_1"
			(rectangle
				(start -5.08 4.445)
				(end 5.08 -5.08)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
		)
		(symbol "AP2112K-1.8_1_1"
			(pin power_in line
				(at -7.62 2.54 0)
				(length 2.54)
				(name "VIN"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -7.62 0 0)
				(length 2.54)
				(name "EN"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 0 -7.62 90)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin no_connect line
				(at 5.08 0 180)
				(length 2.54)
				(hide yes)
				(name "NC"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_out line
				(at 7.62 2.54 180)
				(length 2.54)
				(name "VOUT"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "AZ1117-3.3"
		(pin_names
			(offset 0.254)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at -3.81 3.175 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "AZ1117-3.3"
			(at 0 3.175 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0 6.35 0)
			(effects
				(font
					(size 1.27 1.27)
					(italic yes)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://www.diodes.com/assets/Datasheets/AZ1117.pdf"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "1A 20V Fixed LDO Linear Regulator, 3V3, SOT-89/SOT-223/TO-220/TO-252/TO-263"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "Fixed Voltage Regulator 1A Positive LDO"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "SOT?223* SOT?89* TO?220* TO?252* TO?263*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "AZ1117-3.3_0_1"
			(rectangle
				(start -5.08 1.905)
				(end 5.08 -5.08)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
		)
		(symbol "AZ1117-3.3_1_1"
			(pin power_in line
				(at -7.62 0 0)
				(length 2.54)
				(name "VI"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 0 -7.62 90)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_out line
				(at 7.62 0 180)
				(length 2.54)
				(name "VO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CARAVEL_M2"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at -15.24 25.4 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "2199230-4"
			(at 10.16 -27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "CaravelM2:TE_21992304"
			(at -6.35 -1.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at -6.35 -1.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Efabless caravel m2 breakout pinout, TE 2199230-4 M.2 67 pos NGFF mini, E key."
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_locked" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(symbol "CARAVEL_M2_1_1"
			(rectangle
				(start -7.62 10.16)
				(end 6.35 -16.51)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin power_in line
				(at -10.16 8.89 0)
				(length 2.54)
				(name "VDDA"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 6.35 0)
				(length 2.54)
				(name "VDDA2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 3.81 0)
				(length 2.54)
				(name "VDDA1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 0 0)
				(length 2.54)
				(name "VDDIO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -2.54 0)
				(length 2.54)
				(name "VDDIO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -7.62 0)
				(length 2.54)
				(name "VCCD2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -10.16 0)
				(length 2.54)
				(name "VCCD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -12.7 0)
				(length 2.54)
				(name "VCCD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -15.24 0)
				(length 2.54)
				(name "VCCD1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "35"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -10.16 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "37"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -12.7 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -15.24 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "CARAVEL_M2_2_1"
			(rectangle
				(start -11.43 2.54)
				(end 8.89 -7.62)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin input line
				(at -13.97 1.27 0)
				(length 2.54)
				(name "XCLK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -13.97 -2.54 0)
				(length 2.54)
				(name "~{RST}"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -13.97 -6.35 0)
				(length 2.54)
				(name "GPIO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 11.43 1.27 180)
				(length 2.54)
				(name "CARAVEL_SCK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 -1.27 180)
				(length 2.54)
				(name "CARAVEL_D1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 -3.81 180)
				(length 2.54)
				(name "CARAVEL_D0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 11.43 -6.35 180)
				(length 2.54)
				(name "CARAVEL_CSB"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "CARAVEL_M2_3_1"
			(rectangle
				(start -16.51 24.13)
				(end 15.24 -26.67)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin bidirectional line
				(at -19.05 21.59 0)
				(length 2.54)
				(name "mprj_io[0]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "49"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 19.05 0)
				(length 2.54)
				(name "mprj_io[1]/SDO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "51"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 16.51 0)
				(length 2.54)
				(name "mprj_io[2]/SDI"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "53"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 13.97 0)
				(length 2.54)
				(name "mprj_io[3]/CSB"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "55"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 11.43 0)
				(length 2.54)
				(name "mprj_io[4]/SCK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "57"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 8.89 0)
				(length 2.54)
				(name "mprj_io[5]/RX"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "59"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 6.35 0)
				(length 2.54)
				(name "mprj_io[6]/TX"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "61"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 3.81 0)
				(length 2.54)
				(name "mprj_io[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "63"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 1.27 0)
				(length 2.54)
				(name "mprj_io[8]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "65"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -1.27 0)
				(length 2.54)
				(name "mprj_io[9]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "67"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -3.81 0)
				(length 2.54)
				(name "mprj_io[10]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "69"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -6.35 0)
				(length 2.54)
				(name "mprj_io[11]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "71"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -8.89 0)
				(length 2.54)
				(name "mprj_io[12]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "73"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -11.43 0)
				(length 2.54)
				(name "mprj_io[13]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "75"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -13.97 0)
				(length 2.54)
				(name "mprj_io[14]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "74"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -16.51 0)
				(length 2.54)
				(name "mprj_io[15]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "72"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -19.05 0)
				(length 2.54)
				(name "mprj_io[16]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "70"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -21.59 0)
				(length 2.54)
				(name "mprj_io[17]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "68"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -24.13 0)
				(length 2.54)
				(name "mprj_io[18]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "66"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 21.59 180)
				(length 2.54)
				(name "mprj_io[37]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 19.05 180)
				(length 2.54)
				(name "mprj_io[36]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 16.51 180)
				(length 2.54)
				(name "mprj_io[35]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "32"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 13.97 180)
				(length 2.54)
				(name "mprj_io[34]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "34"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 11.43 180)
				(length 2.54)
				(name "mprj_io[33]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "36"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 8.89 180)
				(length 2.54)
				(name "mprj_io[32]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "38"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 6.35 180)
				(length 2.54)
				(name "mprj_io[31]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "40"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 3.81 180)
				(length 2.54)
				(name "mprj_io[30]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "42"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 1.27 180)
				(length 2.54)
				(name "mprj_io[29]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "44"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -1.27 180)
				(length 2.54)
				(name "mprj_io[28]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "46"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -3.81 180)
				(length 2.54)
				(name "mprj_io[27]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "48"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -6.35 180)
				(length 2.54)
				(name "mprj_io[26]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "50"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -8.89 180)
				(length 2.54)
				(name "mprj_io[25]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "52"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -11.43 180)
				(length 2.54)
				(name "mprj_io[24]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "54"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -13.97 180)
				(length 2.54)
				(name "mprj_io[23]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "56"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -16.51 180)
				(length 2.54)
				(name "mprj_io[22]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "58"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -19.05 180)
				(length 2.54)
				(name "mprj_io[21]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "60"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -21.59 180)
				(length 2.54)
				(name "mprj_io[20]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "62"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -24.13 180)
				(length 2.54)
				(name "mprj_io[19]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "64"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CARAVEL_TT03_BREAKOUT"
		(pin_numbers
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at -16.51 25.4 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "TT03_BREAKOUT"
			(at 10.16 -33.02 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "TinyTapeout:TT03_BREAKOUT_SMB"
			(at -6.35 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at -8.89 1.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Efabless caravel breakout pinout, aisler board"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_locked" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(symbol "CARAVEL_TT03_BREAKOUT_1_1"
			(rectangle
				(start -8.89 15.24)
				(end 6.35 -30.48)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin power_in line
				(at -11.43 13.97 0)
				(length 2.54)
				(name "VDDIO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 11.43 0)
				(length 2.54)
				(name "VDDA"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A30"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 8.89 0)
				(length 2.54)
				(name "VDDA1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 6.35 0)
				(length 2.54)
				(name "VDDA2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A32"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 1.27 0)
				(length 2.54)
				(name "VCCD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A34"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -1.27 0)
				(length 2.54)
				(name "VCCD1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -3.81 0)
				(length 2.54)
				(name "VCCD2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -6.35 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -8.89 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -11.43 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -13.97 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -16.51 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -19.05 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -21.59 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B31"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -24.13 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B32"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -26.67 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B39"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -29.21 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B40"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 13.97 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 11.43 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 8.89 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 6.35 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 3.81 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 1.27 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -1.27 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -3.81 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -6.35 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -8.89 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A27"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -11.43 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A28"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -13.97 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A29"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -16.51 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A31"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -19.05 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -21.59 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A35"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -24.13 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A36"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -26.67 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A39"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -29.21 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A40"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "CARAVEL_TT03_BREAKOUT_2_1"
			(rectangle
				(start -11.43 2.54)
				(end 8.89 -7.62)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin input line
				(at -13.97 1.27 0)
				(length 2.54)
				(name "XCLK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A38"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -13.97 -2.54 0)
				(length 2.54)
				(name "RST"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A37"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -13.97 -6.35 0)
				(length 2.54)
				(name "GPIO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 11.43 1.27 180)
				(length 2.54)
				(name "CARAVEL_SCK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 -1.27 180)
				(length 2.54)
				(name "CARAVEL_D1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 -3.81 180)
				(length 2.54)
				(name "CARAVEL_D0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A25"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 11.43 -6.35 180)
				(length 2.54)
				(name "CARAVEL_CSB"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A26"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "CARAVEL_TT03_BREAKOUT_3_1"
			(rectangle
				(start -17.78 22.86)
				(end 15.24 -26.67)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin bidirectional line
				(at -20.32 21.59 0)
				(length 2.54)
				(name "mprj_io[0]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 19.05 0)
				(length 2.54)
				(name "mprj_io[1]/SDO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 16.51 0)
				(length 2.54)
				(name "mprj_io[2]/SDI"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 13.97 0)
				(length 2.54)
				(name "mprj_io[3]/CSB"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 11.43 0)
				(length 2.54)
				(name "mprj_io[4]/SCK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 8.89 0)
				(length 2.54)
				(name "mprj_io[5]/RX"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 6.35 0)
				(length 2.54)
				(name "mprj_io[6]/TX"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 3.81 0)
				(length 2.54)
				(name "mprj_io[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 1.27 0)
				(length 2.54)
				(name "mprj_io[8]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 -1.27 0)
				(length 2.54)
				(name "mprj_io[9]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at -20.32 -3.81 0)
				(length 2.54)
				(name "slow_clk"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 -6.35 0)
				(length 2.54)
				(name "set_clk_div"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 -8.89 0)
				(length 2.54)
				(name "select[0]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 -11.43 0)
				(length 2.54)
				(name "select[1]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 -13.97 0)
				(length 2.54)
				(name "select[2]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 -16.51 0)
				(length 2.54)
				(name "select[3]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 -19.05 0)
				(length 2.54)
				(name "select[4]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 -21.59 0)
				(length 2.54)
				(name "select[5]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 -24.13 0)
				(length 2.54)
				(name "select[6]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 17.78 21.59 180)
				(length 2.54)
				(name "ready"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B37"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 17.78 19.05 180)
				(length 2.54)
				(name "out[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B38"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 17.78 16.51 180)
				(length 2.54)
				(name "out[6]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B35"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 17.78 13.97 180)
				(length 2.54)
				(name "out[5]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B36"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 17.78 11.43 180)
				(length 2.54)
				(name "out[4]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 17.78 8.89 180)
				(length 2.54)
				(name "out[3]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B34"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 17.78 6.35 180)
				(length 2.54)
				(name "out[2]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B29"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 17.78 3.81 180)
				(length 2.54)
				(name "ext_scan_data/out[1]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B30"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 17.78 1.27 180)
				(length 2.54)
				(name "ext_scan_clk/out[0]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B27"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 17.78 -1.27 180)
				(length 2.54)
				(name "in[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B28"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 17.78 -3.81 180)
				(length 2.54)
				(name "in[6]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B25"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 17.78 -6.35 180)
				(length 2.54)
				(name "in[5]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B26"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 17.78 -8.89 180)
				(length 2.54)
				(name "in[4]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 17.78 -11.43 180)
				(length 2.54)
				(name "ext_scan_latch/in[3]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 17.78 -13.97 180)
				(length 2.54)
				(name "ext_scan_sel/in[2]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 17.78 -16.51 180)
				(length 2.54)
				(name "ext_scan_data/in[1]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 17.78 -19.05 180)
				(length 2.54)
				(name "ext_scan_clk/in[0]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 17.78 -21.59 180)
				(length 2.54)
				(name "select[8]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 17.78 -24.13 180)
				(length 2.54)
				(name "select[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CARAVEL_TT04_BREAKOUT"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at -16.51 25.4 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "TT04_BREAKOUT"
			(at 10.16 -33.02 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "TinyTapeout:TT03_BREAKOUT_SMB"
			(at -6.35 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at -8.89 1.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Efabless caravel breakout pinout, aisler board"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_locked" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(symbol "CARAVEL_TT04_BREAKOUT_1_1"
			(rectangle
				(start -8.89 15.24)
				(end 6.35 -30.48)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin power_in line
				(at -11.43 13.97 0)
				(length 2.54)
				(name "VDDIO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 11.43 0)
				(length 2.54)
				(name "VDDA"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A30"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 8.89 0)
				(length 2.54)
				(name "VDDA1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 6.35 0)
				(length 2.54)
				(name "VDDA2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A32"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 1.27 0)
				(length 2.54)
				(name "VCCD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A34"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -1.27 0)
				(length 2.54)
				(name "VCCD1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -3.81 0)
				(length 2.54)
				(name "VCCD2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -6.35 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -8.89 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -11.43 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -13.97 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -16.51 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -19.05 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -21.59 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B31"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -24.13 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B32"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -26.67 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B39"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -11.43 -29.21 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B40"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 13.97 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 11.43 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 8.89 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 6.35 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 3.81 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 1.27 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -1.27 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -3.81 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -6.35 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -8.89 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A27"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -11.43 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A28"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -13.97 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A29"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -16.51 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A31"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -19.05 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -21.59 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A35"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -24.13 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A36"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -26.67 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A39"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -29.21 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A40"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "CARAVEL_TT04_BREAKOUT_2_1"
			(rectangle
				(start -11.43 2.54)
				(end 8.89 -7.62)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin input line
				(at -13.97 1.27 0)
				(length 2.54)
				(name "XCLK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A38"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -13.97 -2.54 0)
				(length 2.54)
				(name "RST"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A37"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -13.97 -6.35 0)
				(length 2.54)
				(name "GPIO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 11.43 1.27 180)
				(length 2.54)
				(name "CARAVEL_SCK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 -1.27 180)
				(length 2.54)
				(name "CARAVEL_D1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 -3.81 180)
				(length 2.54)
				(name "CARAVEL_D0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A25"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 11.43 -6.35 180)
				(length 2.54)
				(name "CARAVEL_CSB"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A26"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "CARAVEL_TT04_BREAKOUT_3_1"
			(rectangle
				(start -20.32 22.86)
				(end 20.32 -26.67)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin bidirectional line
				(at -22.86 20.32 0)
				(length 2.54)
				(name "JTAG/mio[0]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -22.86 17.78 0)
				(length 2.54)
				(name "SDO/mio[1]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -22.86 15.24 0)
				(length 2.54)
				(name "SDI/mio[2]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -22.86 12.7 0)
				(length 2.54)
				(name "CSB/mio[3]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -22.86 10.16 0)
				(length 2.54)
				(name "SCK/mio[4]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at -22.86 7.62 0)
				(length 2.54)
				(name "usrclk2/mio[5]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 5.08 0)
				(length 2.54)
				(name "clk/mio[6]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 2.54 0)
				(length 2.54)
				(name "~{rst}/mio[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 0 0)
				(length 2.54)
				(name "ui_in[0]/mio[8]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 -2.54 0)
				(length 2.54)
				(name "ui_in[1]/mio[9]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 -5.08 0)
				(length 2.54)
				(name "ui_in[2]/mio[10]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 -7.62 0)
				(length 2.54)
				(name "ui_in[3]/mio[11]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 -10.16 0)
				(length 2.54)
				(name "ui_in[4]/mio[12]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 -12.7 0)
				(length 2.54)
				(name "ui_in[5]/mio[13]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 -15.24 0)
				(length 2.54)
				(name "ui_in[6]/mio[14]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 -17.78 0)
				(length 2.54)
				(name "ui_in[7]/mio[15]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at -22.86 -20.32 0)
				(length 2.54)
				(name "uo_out[0]/mio[16]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at -22.86 -22.86 0)
				(length 2.54)
				(name "uo_out[1]/mio[17]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at -22.86 -25.4 0)
				(length 2.54)
				(name "uo_out[2]/mio[18]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 20.32 180)
				(length 2.54)
				(name "mio[37]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B37"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 22.86 17.78 180)
				(length 2.54)
				(name "mio[36]/~{ctrl_sel_rst}"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B38"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 15.24 180)
				(length 2.54)
				(name "mio[35]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B35"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 22.86 12.7 180)
				(length 2.54)
				(name "mio[34]/ctrl_sel_inc"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B36"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 10.16 180)
				(length 2.54)
				(name "mio[33]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 22.86 7.62 180)
				(length 2.54)
				(name "mio[32]/ctrl_ena"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B34"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 5.08 180)
				(length 2.54)
				(name "mio[31]/uio[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B29"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 2.54 180)
				(length 2.54)
				(name "mio[30]/uio[6]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B30"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 0 180)
				(length 2.54)
				(name "mio[29]/uio[5]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B27"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 -2.54 180)
				(length 2.54)
				(name "mio[28]/uio[4]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B28"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 -5.08 180)
				(length 2.54)
				(name "mio[27]/uio[3]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B25"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 -7.62 180)
				(length 2.54)
				(name "mio[26]/uio[2]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B26"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 -10.16 180)
				(length 2.54)
				(name "mio[25]/uio[1]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 -12.7 180)
				(length 2.54)
				(name "mio[24]/uio[0]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 22.86 -15.24 180)
				(length 2.54)
				(name "mio[23]/uo_out[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 22.86 -17.78 180)
				(length 2.54)
				(name "mio[22]/uo_out[6]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 22.86 -20.32 180)
				(length 2.54)
				(name "mio[21]/uo_out[5]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 22.86 -22.86 180)
				(length 2.54)
				(name "mio[20]/uo_out[4]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 22.86 -25.4 180)
				(length 2.54)
				(name "mio[19]/uo_out[3]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CARAVEL_TT04_BREAKOUT_REVB"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at -16.51 25.4 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "TT04_BREAKOUT_REVB"
			(at 7.62 -30.48 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "TinyTapeout:TT04_BREAKOUT_SMB"
			(at 2.54 -35.56 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at -7.62 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Efabless caravel breakout pinout, aisler board"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_locked" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(symbol "CARAVEL_TT04_BREAKOUT_REVB_1_1"
			(rectangle
				(start -7.62 13.97)
				(end 7.62 -27.94)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin power_in line
				(at -10.16 12.7 0)
				(length 2.54)
				(name "VDDIO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 12.7 0)
				(length 2.54)
				(name "VDDIO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 10.16 0)
				(length 2.54)
				(name "VDDA"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 7.62 0)
				(length 2.54)
				(name "VDDA1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B37"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 7.62 0)
				(length 2.54)
				(name "VDDA1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B39"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 5.08 0)
				(length 2.54)
				(name "VDDA2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 5.08 0)
				(length 2.54)
				(name "VDDA2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 0 0)
				(length 2.54)
				(name "VCCD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -2.54 0)
				(length 2.54)
				(name "VCCD1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -2.54 0)
				(length 2.54)
				(name "VCCD1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B35"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -5.08 0)
				(length 2.54)
				(name "VCCD2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -5.08 0)
				(length 2.54)
				(name "VCCD2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -7.62 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -10.16 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -12.7 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -15.24 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -17.78 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -20.32 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -22.86 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 12.7 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 10.16 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 7.62 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 5.08 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 2.54 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A25"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 0 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 -2.54 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A30"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 -5.08 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 -7.62 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B36"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 -10.16 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B38"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 -12.7 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B40"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 -15.24 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A29"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 -17.78 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A40"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 -20.32 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B34"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 -22.86 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B31"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 -25.4 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B25"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "CARAVEL_TT04_BREAKOUT_REVB_2_1"
			(rectangle
				(start -11.43 2.54)
				(end 8.89 -7.62)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin input line
				(at -13.97 1.27 0)
				(length 2.54)
				(name "XCLK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -13.97 -2.54 0)
				(length 2.54)
				(name "RST"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -13.97 -6.35 0)
				(length 2.54)
				(name "GPIO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 11.43 1.27 180)
				(length 2.54)
				(name "CARAVEL_SCK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 -1.27 180)
				(length 2.54)
				(name "CARAVEL_D1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 -3.81 180)
				(length 2.54)
				(name "CARAVEL_D0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 11.43 -6.35 180)
				(length 2.54)
				(name "CARAVEL_CSB"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "CARAVEL_TT04_BREAKOUT_REVB_3_1"
			(rectangle
				(start -20.32 22.86)
				(end 20.32 -26.67)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin bidirectional line
				(at -22.86 20.32 0)
				(length 2.54)
				(name "JTAG/mio[0]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -22.86 17.78 0)
				(length 2.54)
				(name "SDO/mio[1]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -22.86 15.24 0)
				(length 2.54)
				(name "SDI/mio[2]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -22.86 12.7 0)
				(length 2.54)
				(name "CSB/mio[3]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -22.86 10.16 0)
				(length 2.54)
				(name "SCK/mio[4]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A26"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at -22.86 7.62 0)
				(length 2.54)
				(name "usrclk2/mio[5]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A27"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 5.08 0)
				(length 2.54)
				(name "clk/mio[6]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A28"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 2.54 0)
				(length 2.54)
				(name "~{rst}/mio[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A31"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 0 0)
				(length 2.54)
				(name "ui_in[0]/mio[8]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A32"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 -2.54 0)
				(length 2.54)
				(name "ui_in[1]/mio[9]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 -5.08 0)
				(length 2.54)
				(name "ui_in[2]/mio[10]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A34"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 -7.62 0)
				(length 2.54)
				(name "ui_in[3]/mio[11]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A35"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 -10.16 0)
				(length 2.54)
				(name "ui_in[4]/mio[12]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A36"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 -12.7 0)
				(length 2.54)
				(name "ui_in[5]/mio[13]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A37"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 -15.24 0)
				(length 2.54)
				(name "ui_in[6]/mio[14]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A38"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -22.86 -17.78 0)
				(length 2.54)
				(name "ui_in[7]/mio[15]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A39"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at -22.86 -20.32 0)
				(length 2.54)
				(name "uo_out[0]/mio[16]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B32"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at -22.86 -22.86 0)
				(length 2.54)
				(name "uo_out[1]/mio[17]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B29"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at -22.86 -25.4 0)
				(length 2.54)
				(name "uo_out[2]/mio[18]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B30"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 20.32 180)
				(length 2.54)
				(name "mio[37]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 22.86 17.78 180)
				(length 2.54)
				(name "mio[36]/~{ctrl_sel_rst}"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 15.24 180)
				(length 2.54)
				(name "mio[35]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 22.86 12.7 180)
				(length 2.54)
				(name "mio[34]/ctrl_sel_inc"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 10.16 180)
				(length 2.54)
				(name "mio[33]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 22.86 7.62 180)
				(length 2.54)
				(name "mio[32]/ctrl_ena"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 5.08 180)
				(length 2.54)
				(name "mio[31]/uio[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 2.54 180)
				(length 2.54)
				(name "mio[30]/uio[6]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 0 180)
				(length 2.54)
				(name "mio[29]/uio[5]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 -2.54 180)
				(length 2.54)
				(name "mio[28]/uio[4]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 -5.08 180)
				(length 2.54)
				(name "mio[27]/uio[3]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 -7.62 180)
				(length 2.54)
				(name "mio[26]/uio[2]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 -10.16 180)
				(length 2.54)
				(name "mio[25]/uio[1]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 22.86 -12.7 180)
				(length 2.54)
				(name "mio[24]/uio[0]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 22.86 -15.24 180)
				(length 2.54)
				(name "mio[23]/uo_out[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 22.86 -17.78 180)
				(length 2.54)
				(name "mio[22]/uo_out[6]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B26"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 22.86 -20.32 180)
				(length 2.54)
				(name "mio[21]/uo_out[5]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 22.86 -22.86 180)
				(length 2.54)
				(name "mio[20]/uo_out[4]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B28"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 22.86 -25.4 180)
				(length 2.54)
				(name "mio[19]/uo_out[3]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B27"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CARAVEL_TTGENERIC_BREAKOUT"
		(pin_numbers
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at -15.24 25.4 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "2199230-4"
			(at 10.16 -33.02 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "TinyTapeout:TT03_BREAKOUT_SMB"
			(at -6.35 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at -6.35 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Efabless caravel breakout pinout, aisler board"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_locked" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(symbol "CARAVEL_TTGENERIC_BREAKOUT_1_1"
			(rectangle
				(start -7.62 16.51)
				(end 6.35 -30.48)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin power_in line
				(at -10.16 13.97 0)
				(length 2.54)
				(name "VDDIO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 11.43 0)
				(length 2.54)
				(name "VDDA"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A30"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 8.89 0)
				(length 2.54)
				(name "VDDA1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 6.35 0)
				(length 2.54)
				(name "VDDA2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A32"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 2.54 0)
				(length 2.54)
				(name "VCCD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A34"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 0 0)
				(length 2.54)
				(name "VCCD1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -2.54 0)
				(length 2.54)
				(name "VCCD2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -5.08 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -7.62 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -10.16 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -12.7 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -15.24 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -17.78 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -20.32 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B31"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -22.86 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B32"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -25.4 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B39"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -10.16 -27.94 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B40"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 13.97 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 11.43 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 8.89 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 6.35 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 3.81 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 1.27 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -1.27 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -3.81 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -6.35 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -8.89 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A27"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -11.43 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A28"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -13.97 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A29"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -16.51 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A31"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -19.05 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -21.59 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A35"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -24.13 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A36"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -26.67 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A39"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 8.89 -29.21 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A40"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "CARAVEL_TTGENERIC_BREAKOUT_2_1"
			(rectangle
				(start -11.43 2.54)
				(end 8.89 -7.62)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin input line
				(at -13.97 1.27 0)
				(length 2.54)
				(name "XCLK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A38"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -13.97 -2.54 0)
				(length 2.54)
				(name "~{RST}"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A37"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -13.97 -6.35 0)
				(length 2.54)
				(name "GPIO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 11.43 1.27 180)
				(length 2.54)
				(name "CARAVEL_SCK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 -1.27 180)
				(length 2.54)
				(name "CARAVEL_D1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 -3.81 180)
				(length 2.54)
				(name "CARAVEL_D0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A25"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 11.43 -6.35 180)
				(length 2.54)
				(name "CARAVEL_CSB"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A26"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "CARAVEL_TTGENERIC_BREAKOUT_3_1"
			(rectangle
				(start -16.51 24.13)
				(end 15.24 -26.67)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin bidirectional line
				(at -19.05 21.59 0)
				(length 2.54)
				(name "mprj_io[0]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 19.05 0)
				(length 2.54)
				(name "mprj_io[1]/SDO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 16.51 0)
				(length 2.54)
				(name "mprj_io[2]/SDI"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 13.97 0)
				(length 2.54)
				(name "mprj_io[3]/CSB"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 11.43 0)
				(length 2.54)
				(name "mprj_io[4]/SCK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 8.89 0)
				(length 2.54)
				(name "mprj_io[5]/RX"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 6.35 0)
				(length 2.54)
				(name "mprj_io[6]/TX"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 3.81 0)
				(length 2.54)
				(name "mprj_io[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "A11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 1.27 0)
				(length 2.54)
				(name "mprj_io[8]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -1.27 0)
				(length 2.54)
				(name "mprj_io[9]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -3.81 0)
				(length 2.54)
				(name "mprj_io[10]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -6.35 0)
				(length 2.54)
				(name "mprj_io[11]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -8.89 0)
				(length 2.54)
				(name "mprj_io[12]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -11.43 0)
				(length 2.54)
				(name "mprj_io[13]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -13.97 0)
				(length 2.54)
				(name "mprj_io[14]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -16.51 0)
				(length 2.54)
				(name "mprj_io[15]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -19.05 0)
				(length 2.54)
				(name "mprj_io[16]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -21.59 0)
				(length 2.54)
				(name "mprj_io[17]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -19.05 -24.13 0)
				(length 2.54)
				(name "mprj_io[18]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 21.59 180)
				(length 2.54)
				(name "mprj_io[37]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B37"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 19.05 180)
				(length 2.54)
				(name "mprj_io[36]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B38"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 16.51 180)
				(length 2.54)
				(name "mprj_io[35]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B35"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 13.97 180)
				(length 2.54)
				(name "mprj_io[34]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B36"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 11.43 180)
				(length 2.54)
				(name "mprj_io[33]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 8.89 180)
				(length 2.54)
				(name "mprj_io[32]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B34"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 6.35 180)
				(length 2.54)
				(name "mprj_io[31]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B29"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 3.81 180)
				(length 2.54)
				(name "mprj_io[30]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B30"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 1.27 180)
				(length 2.54)
				(name "mprj_io[29]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B27"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -1.27 180)
				(length 2.54)
				(name "mprj_io[28]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B28"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -3.81 180)
				(length 2.54)
				(name "mprj_io[27]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B25"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -6.35 180)
				(length 2.54)
				(name "mprj_io[26]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B26"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -8.89 180)
				(length 2.54)
				(name "mprj_io[25]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -11.43 180)
				(length 2.54)
				(name "mprj_io[24]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -13.97 180)
				(length 2.54)
				(name "mprj_io[23]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -16.51 180)
				(length 2.54)
				(name "mprj_io[22]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -19.05 180)
				(length 2.54)
				(name "mprj_io[21]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -21.59 180)
				(length 2.54)
				(name "mprj_io[20]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -24.13 180)
				(length 2.54)
				(name "mprj_io[19]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "B15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "C_Small"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.254)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "C"
			(at 0.254 1.778 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "C_Small"
			(at 0.254 -2.032 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "capacitor cap"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "C_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "C_Small_0_1"
			(polyline
				(pts
					(xy -1.524 0.508) (xy 1.524 0.508)
				)
				(stroke
					(width 0.3048)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.524 -0.508) (xy 1.524 -0.508)
				)
				(stroke
					(width 0.3302)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "C_Small_1_1"
			(pin passive line
				(at 0 2.54 270)
				(length 2.032)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 0 -2.54 90)
				(length 2.032)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Caravel_QFN"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U1"
			(at 3.81 -10.16 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "Caravel_QFN"
			(at 0 -12.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Package_DFN_QFN:QFN-64-1EP_9x9mm_P0.5mm_EP7.65x7.65mm"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Caravel IC QFN packaging"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Caravel_QFN_0_1"
			(rectangle
				(start -26.67 40.64)
				(end 24.13 -33.02)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "Caravel_QFN_1_1"
			(pin power_in line
				(at -29.21 30.48 0)
				(length 2.54)
				(name "ThermalPad"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "65"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -29.21 22.86 0)
				(length 2.54)
				(name "vssa2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 20.32 0)
				(length 2.54)
				(name "mprj_io[25]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 17.78 0)
				(length 2.54)
				(name "mprj_io[26]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 15.24 0)
				(length 2.54)
				(name "mprj_io[27]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 12.7 0)
				(length 2.54)
				(name "mprj_io[28]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 10.16 0)
				(length 2.54)
				(name "mprj_io[29]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 7.62 0)
				(length 2.54)
				(name "mprj_io[30]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 5.08 0)
				(length 2.54)
				(name "mprj_io[31]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -29.21 2.54 0)
				(length 2.54)
				(name "vdda2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -29.21 0 0)
				(length 2.54)
				(name "vssd2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -2.54 0)
				(length 2.54)
				(name "mprj_io[32]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -5.08 0)
				(length 2.54)
				(name "mprj_io[33]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -7.62 0)
				(length 2.54)
				(name "mprj_io[34]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -10.16 0)
				(length 2.54)
				(name "mprj_io[35]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -12.7 0)
				(length 2.54)
				(name "mprj_io[36]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -15.24 0)
				(length 2.54)
				(name "mprj_io[37]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -16.51 43.18 270)
				(length 2.54)
				(name "vddio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "64"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -16.51 -35.56 90)
				(length 2.54)
				(name "vddio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -13.97 43.18 270)
				(length 2.54)
				(name "vccd2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "63"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -13.97 -35.56 90)
				(length 2.54)
				(name "vccd"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -11.43 43.18 270)
				(length 2.54)
				(name "mprj_io[24]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "62"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin no_connect line
				(at -11.43 -35.56 90)
				(length 2.54)
				(name "N/C"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -8.89 43.18 270)
				(length 2.54)
				(name "mprj_io[23]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "61"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -8.89 -35.56 90)
				(length 2.54)
				(name "vssa"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -6.35 43.18 270)
				(length 2.54)
				(name "mprj_io[22]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "60"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -6.35 -35.56 90)
				(length 2.54)
				(name "resetb"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -3.81 43.18 270)
				(length 2.54)
				(name "mprj_io[21]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "59"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -3.81 -35.56 90)
				(length 2.54)
				(name "clock"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -1.27 43.18 270)
				(length 2.54)
				(name "mprj_io[20]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "58"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -1.27 -35.56 90)
				(length 2.54)
				(name "vssd"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 1.27 43.18 270)
				(length 2.54)
				(name "mprj_io[19]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "57"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 1.27 -35.56 90)
				(length 2.54)
				(name "flash_csb"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 3.81 43.18 270)
				(length 2.54)
				(name "vssio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "56"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 3.81 -35.56 90)
				(length 2.54)
				(name "flash_clk"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "25"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 6.35 43.18 270)
				(length 2.54)
				(name "mprj_io[18]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "55"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 6.35 -35.56 90)
				(length 2.54)
				(name "flash_io0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "26"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 8.89 43.18 270)
				(length 2.54)
				(name "mprj_io[17]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "54"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 8.89 -35.56 90)
				(length 2.54)
				(name "flash_io1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "27"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 43.18 270)
				(length 2.54)
				(name "mprj_io[16]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "53"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 -35.56 90)
				(length 2.54)
				(name "gpio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "28"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 13.97 43.18 270)
				(length 2.54)
				(name "vssa1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "52"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 13.97 -35.56 90)
				(length 2.54)
				(name "vssio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "29"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 16.51 43.18 270)
				(length 2.54)
				(name "mprj_io[15]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "51"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 16.51 -35.56 90)
				(length 2.54)
				(name "vdda"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "30"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 43.18 270)
				(length 2.54)
				(name "mprj_io[14]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "50"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 -35.56 90)
				(length 2.54)
				(name "mprj_io[0]_JTAG"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "31"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 21.59 43.18 270)
				(length 2.54)
				(name "vccd1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "49"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 21.59 -35.56 90)
				(length 2.54)
				(name "mprj_io[1]_SDO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "32"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 22.86 180)
				(length 2.54)
				(name "mprj_io[13]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "48"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 26.67 20.32 180)
				(length 2.54)
				(name "vdda1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "47"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 17.78 180)
				(length 2.54)
				(name "mprj_io[12]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "46"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 15.24 180)
				(length 2.54)
				(name "mprj_io[11]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "45"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 12.7 180)
				(length 2.54)
				(name "mprj_io[10]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "44"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 10.16 180)
				(length 2.54)
				(name "mprj_io[9]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "43"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 7.62 180)
				(length 2.54)
				(name "mprj_io[8]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "42"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 5.08 180)
				(length 2.54)
				(name "mprj_io[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "41"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 26.67 2.54 180)
				(length 2.54)
				(name "vdda1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "40"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 26.67 0 180)
				(length 2.54)
				(name "vssd1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "39"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 26.67 -2.54 180)
				(length 2.54)
				(name "vssa1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "38"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -5.08 180)
				(length 2.54)
				(name "mprj_io[6]_ser_tx"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "37"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -7.62 180)
				(length 2.54)
				(name "mprj_io[5]_ser_rx"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "36"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -10.16 180)
				(length 2.54)
				(name "mprj_io[4]_SCK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "35"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -12.7 180)
				(length 2.54)
				(name "mprj_io[3]_CSB"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "34"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -15.24 180)
				(length 2.54)
				(name "mprj_io[2]_SDI"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Caravel_QFN_TT3"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U1"
			(at -26.416 41.656 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "Caravel_QFN_TT3"
			(at 26.035 -33.655 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Package_DFN_QFN:QFN-64-1EP_9x9mm_P0.5mm_EP7.65x7.65mm"
			(at 1.27 -49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://github.com/tinytapeout/tinytapeout-mpw7/blob/mpw7/INFO.md#pinout"
			(at 2.54 -50.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Caravel IC QFN packaging, with TinyTapeout 123 pin mapping"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Caravel_QFN_TT3_0_1"
			(rectangle
				(start -26.67 40.64)
				(end 24.13 -33.02)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -4.064 24.892) (xy -4.064 24.257) (xy -11.684 24.257) (xy -11.684 24.892)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "Caravel_QFN_TT3_1_1"
			(text "* I/O pin names:\n  TT / caravel"
				(at -18.796 -18.796 0)
				(effects
					(font
						(size 1 1)
					)
				)
			)
			(text "ext_scan"
				(at -8.128 24.892 0)
				(effects
					(font
						(size 0.5 0.5)
					)
				)
			)
			(pin power_in line
				(at -29.21 30.48 0)
				(length 2.54)
				(name "ThermalPad"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "65"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -29.21 22.86 0)
				(length 2.54)
				(name "vssa2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 20.32 0)
				(length 2.54)
				(name "in[4]/crvio[25]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 17.78 0)
				(length 2.54)
				(name "in[5]/crvio[26]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 15.24 0)
				(length 2.54)
				(name "in[6]/crvio[27]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 12.7 0)
				(length 2.54)
				(name "in[7]/crvio[28]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 10.16 0)
				(length 2.54)
				(name "out[0]/crvio[29]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 7.62 0)
				(length 2.54)
				(name "out[1]/crvio[30]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 5.08 0)
				(length 2.54)
				(name "out[2]/crvio[31]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -29.21 2.54 0)
				(length 2.54)
				(name "vdda2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -29.21 0 0)
				(length 2.54)
				(name "vssd2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -2.54 0)
				(length 2.54)
				(name "out[3]/crvio[32]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -5.08 0)
				(length 2.54)
				(name "out[4]/crvio[33]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -7.62 0)
				(length 2.54)
				(name "out[5]/crvio[34]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -10.16 0)
				(length 2.54)
				(name "out[6]/crvio[35]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -12.7 0)
				(length 2.54)
				(name "out[7]/crvio[36]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -15.24 0)
				(length 2.54)
				(name "ready/crvio[37]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -16.51 43.18 270)
				(length 2.54)
				(name "vddio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "64"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -16.51 -35.56 90)
				(length 2.54)
				(name "vddio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -13.97 43.18 270)
				(length 2.54)
				(name "vccd2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "63"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -13.97 -35.56 90)
				(length 2.54)
				(name "vccd"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -11.43 43.18 270)
				(length 2.54)
				(name "in[3]/crvio[24]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "62"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin no_connect line
				(at -11.43 -35.56 90)
				(length 2.54)
				(name "N/C"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -8.89 43.18 270)
				(length 2.54)
				(name "in[2]/crvio[23]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "61"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -8.89 -35.56 90)
				(length 2.54)
				(name "vssa"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -6.35 43.18 270)
				(length 2.54)
				(name "in[1]/crvio[22]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "60"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -6.35 -35.56 90)
				(length 2.54)
				(name "resetb"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -3.81 43.18 270)
				(length 2.54)
				(name "in[0]/crvio[21]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "59"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -3.81 -35.56 90)
				(length 2.54)
				(name "clock"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -1.27 43.18 270)
				(length 2.54)
				(name "sel[8]/crvio[20]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "58"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -1.27 -35.56 90)
				(length 2.54)
				(name "vssd"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 1.27 43.18 270)
				(length 2.54)
				(name "sel[7]/crvio[19]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "57"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 1.27 -35.56 90)
				(length 2.54)
				(name "flash_csb"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 3.81 43.18 270)
				(length 2.54)
				(name "vssio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "56"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 3.81 -35.56 90)
				(length 2.54)
				(name "flash_clk"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "25"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 6.35 43.18 270)
				(length 2.54)
				(name "sel[6]/crvio[18]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "55"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 6.35 -35.56 90)
				(length 2.54)
				(name "flash_io0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "26"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 8.89 43.18 270)
				(length 2.54)
				(name "sel[5]/crvio[17]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "54"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 8.89 -35.56 90)
				(length 2.54)
				(name "flash_io1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "27"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 43.18 270)
				(length 2.54)
				(name "sel[4]/crvio[16]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "53"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 -35.56 90)
				(length 2.54)
				(name "gpio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "28"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 13.97 43.18 270)
				(length 2.54)
				(name "vssa1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "52"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 13.97 -35.56 90)
				(length 2.54)
				(name "vssio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "29"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 16.51 43.18 270)
				(length 2.54)
				(name "sel[3]/crvio[15]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "51"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 16.51 -35.56 90)
				(length 2.54)
				(name "vdda"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "30"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 43.18 270)
				(length 2.54)
				(name "sel[2]/crvio[14]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "50"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 -35.56 90)
				(length 2.54)
				(name "crvio[0]_JTAG"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "31"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 21.59 43.18 270)
				(length 2.54)
				(name "vccd1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "49"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 21.59 -35.56 90)
				(length 2.54)
				(name "crvio[1]_SDO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "32"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 22.86 180)
				(length 2.54)
				(name "sel[1]/crvio[13]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "48"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 26.67 20.32 180)
				(length 2.54)
				(name "vdda1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "47"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 17.78 180)
				(length 2.54)
				(name "sel[0]/crvio[12]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "46"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 15.24 180)
				(length 2.54)
				(name "set_clk_div/crvio[11]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "45"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 12.7 180)
				(length 2.54)
				(name "slow_clk/crvio[10]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "44"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 10.16 180)
				(length 2.54)
				(name "driver_sel1/crvio[9]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "43"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 7.62 180)
				(length 2.54)
				(name "driver_sel0/crvio[8]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "42"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 5.08 180)
				(length 2.54)
				(name "crvio[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "41"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 26.67 2.54 180)
				(length 2.54)
				(name "vdda1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "40"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 26.67 0 180)
				(length 2.54)
				(name "vssd1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "39"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 26.67 -2.54 180)
				(length 2.54)
				(name "vssa1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "38"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -5.08 180)
				(length 2.54)
				(name "crvio[6]_ser_tx"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "37"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -7.62 180)
				(length 2.54)
				(name "crvio[5]_ser_rx"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "36"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -10.16 180)
				(length 2.54)
				(name "crvio[4]_SCK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "35"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -12.7 180)
				(length 2.54)
				(name "crvio[3]_CSB"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "34"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -15.24 180)
				(length 2.54)
				(name "crvio[2]_SDI"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Caravel_QFN_TT4"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U1"
			(at -26.416 41.656 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "Caravel_QFN_TT4"
			(at 26.035 -33.655 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Package_DFN_QFN:QFN-64-1EP_9x9mm_P0.5mm_EP7.65x7.65mm"
			(at 1.27 -49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://github.com/tinytapeout/tinytapeout-mpw7/blob/mpw7/INFO.md#pinout"
			(at 2.54 -50.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Caravel IC QFN packaging, with TinyTapeout 4+ pin mapping"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Caravel_QFN_TT4_0_1"
			(rectangle
				(start -26.67 40.64)
				(end 24.13 -33.02)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "Caravel_QFN_TT4_1_1"
			(text "* I/O pin names:\n  TT / caravel"
				(at -18.796 -18.796 0)
				(effects
					(font
						(size 1 1)
					)
				)
			)
			(pin power_in line
				(at -29.21 30.48 0)
				(length 2.54)
				(name "ThermalPad"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "65"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -29.21 22.86 0)
				(length 2.54)
				(name "vssa2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 20.32 0)
				(length 2.54)
				(name "uio[1]/mio[25]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 17.78 0)
				(length 2.54)
				(name "uio[2]/mio[26]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 15.24 0)
				(length 2.54)
				(name "uio[3]/mio[27]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 12.7 0)
				(length 2.54)
				(name "uio[4]/mio[28]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 10.16 0)
				(length 2.54)
				(name "uio[5]/mio[29]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 7.62 0)
				(length 2.54)
				(name "uio[6]/mio[30]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 5.08 0)
				(length 2.54)
				(name "uio[7]/mio[31]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -29.21 2.54 0)
				(length 2.54)
				(name "vdda2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -29.21 0 0)
				(length 2.54)
				(name "vssd2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -2.54 0)
				(length 2.54)
				(name "ctrl_ena/mio[32]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -5.08 0)
				(length 2.54)
				(name "mio[33]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -7.62 0)
				(length 2.54)
				(name "ctrl_sel_inc/mio[34]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -10.16 0)
				(length 2.54)
				(name "mio[35]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -12.7 0)
				(length 2.54)
				(name "~{ctrl_sel_rst}/mio[36]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -15.24 0)
				(length 2.54)
				(name "mio[37]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -16.51 43.18 270)
				(length 2.54)
				(name "vddio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "64"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -16.51 -35.56 90)
				(length 2.54)
				(name "vddio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -13.97 43.18 270)
				(length 2.54)
				(name "vccd2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "63"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -13.97 -35.56 90)
				(length 2.54)
				(name "vccd"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -11.43 43.18 270)
				(length 2.54)
				(name "uio[0]/mio[24]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "62"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin no_connect line
				(at -11.43 -35.56 90)
				(length 2.54)
				(name "N/C"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -8.89 43.18 270)
				(length 2.54)
				(name "uo_out[7]/mio[23]"
					(effects
						(font
							(size 1.15 1.15)
						)
					)
				)
				(number "61"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -8.89 -35.56 90)
				(length 2.54)
				(name "vssa"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -6.35 43.18 270)
				(length 2.54)
				(name "uo_out[6]/mio[22]"
					(effects
						(font
							(size 1.15 1.15)
						)
					)
				)
				(number "60"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -6.35 -35.56 90)
				(length 2.54)
				(name "resetb"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -3.81 43.18 270)
				(length 2.54)
				(name "uo_out[5]/mio[21]"
					(effects
						(font
							(size 1.15 1.15)
						)
					)
				)
				(number "59"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -3.81 -35.56 90)
				(length 2.54)
				(name "clock"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -1.27 43.18 270)
				(length 2.54)
				(name "uo_out[4]/mio[20]"
					(effects
						(font
							(size 1.15 1.15)
						)
					)
				)
				(number "58"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -1.27 -35.56 90)
				(length 2.54)
				(name "vssd"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 1.27 43.18 270)
				(length 2.54)
				(name "uo_out[3]/mio[19]"
					(effects
						(font
							(size 1.15 1.15)
						)
					)
				)
				(number "57"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 1.27 -35.56 90)
				(length 2.54)
				(name "flash_csb"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 3.81 43.18 270)
				(length 2.54)
				(name "vssio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "56"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 3.81 -35.56 90)
				(length 2.54)
				(name "flash_clk"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "25"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 6.35 43.18 270)
				(length 2.54)
				(name "uo_out[2]/mio[18]"
					(effects
						(font
							(size 1.15 1.15)
						)
					)
				)
				(number "55"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 6.35 -35.56 90)
				(length 2.54)
				(name "flash_io0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "26"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 8.89 43.18 270)
				(length 2.54)
				(name "uo_out[1]/mio[17]"
					(effects
						(font
							(size 1.15 1.15)
						)
					)
				)
				(number "54"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 8.89 -35.56 90)
				(length 2.54)
				(name "flash_io1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "27"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 43.18 270)
				(length 2.54)
				(name "uo_out[0]/mio[16]"
					(effects
						(font
							(size 1.15 1.15)
						)
					)
				)
				(number "53"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 11.43 -35.56 90)
				(length 2.54)
				(name "gpio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "28"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 13.97 43.18 270)
				(length 2.54)
				(name "vssa1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "52"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 13.97 -35.56 90)
				(length 2.54)
				(name "vssio"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "29"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 16.51 43.18 270)
				(length 2.54)
				(name "ui_in[7]/mio[15]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "51"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 16.51 -35.56 90)
				(length 2.54)
				(name "vdda"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "30"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 43.18 270)
				(length 2.54)
				(name "ui_in[6]/mio[14]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "50"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 -35.56 90)
				(length 2.54)
				(name "JTAG/mio[0]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "31"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 21.59 43.18 270)
				(length 2.54)
				(name "vccd1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "49"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 21.59 -35.56 90)
				(length 2.54)
				(name "SDO/mio[1]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "32"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 22.86 180)
				(length 2.54)
				(name "ui_in[5]/mio[13]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "48"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 26.67 20.32 180)
				(length 2.54)
				(name "vdda1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "47"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 17.78 180)
				(length 2.54)
				(name "ui_in[4]/mio[12]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "46"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 15.24 180)
				(length 2.54)
				(name "ui_in[3]/mio[11]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "45"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 12.7 180)
				(length 2.54)
				(name "ui_in[2]/mio[10]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "44"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 10.16 180)
				(length 2.54)
				(name "ui_in[1]/mio[9]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "43"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 7.62 180)
				(length 2.54)
				(name "ui_in[0]/mio[8]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "42"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 5.08 180)
				(length 2.54)
				(name "~{rst}/mio[7]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "41"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 26.67 2.54 180)
				(length 2.54)
				(name "vdda1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "40"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 26.67 0 180)
				(length 2.54)
				(name "vssd1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "39"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 26.67 -2.54 180)
				(length 2.54)
				(name "vssa1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "38"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -5.08 180)
				(length 2.54)
				(name "clk/mio[6]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "37"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -7.62 180)
				(length 2.54)
				(name "usrclk2/mio[5]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "36"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -10.16 180)
				(length 2.54)
				(name "SCK/mio[4]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "35"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -12.7 180)
				(length 2.54)
				(name "CSB/mio[3]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "34"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 26.67 -15.24 180)
				(length 2.54)
				(name "SDI/mio[2]"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Conn_01x02"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at 0 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x02"
			(at 0 -5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Generic connector, single row, 01x02, script generated (kicad-library-utils/schlib/autogen/connector/)"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "connector"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Connector*:*_1x??_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Conn_01x02_1_1"
			(rectangle
				(start -1.27 1.27)
				(end 1.27 -3.81)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(rectangle
				(start -1.27 0.127)
				(end 0 -0.127)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -2.413)
				(end 0 -2.667)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at -5.08 0 0)
				(length 3.81)
				(name "Pin_1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -2.54 0)
				(length 3.81)
				(name "Pin_2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Conn_01x03"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x03"
			(at 0 -5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Generic connector, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "connector"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Connector*:*_1x??_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Conn_01x03_1_1"
			(rectangle
				(start -1.27 3.81)
				(end 1.27 -3.81)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(rectangle
				(start -1.27 2.667)
				(end 0 2.413)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 0.127)
				(end 0 -0.127)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -2.413)
				(end 0 -2.667)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at -5.08 2.54 0)
				(length 3.81)
				(name "Pin_1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 0 0)
				(length 3.81)
				(name "Pin_2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -2.54 0)
				(length 3.81)
				(name "Pin_3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Conn_01x04"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x04"
			(at 0 -7.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Generic connector, single row, 01x04, script generated (kicad-library-utils/schlib/autogen/connector/)"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "connector"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Connector*:*_1x??_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Conn_01x04_1_1"
			(rectangle
				(start -1.27 3.81)
				(end 1.27 -6.35)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(rectangle
				(start -1.27 2.667)
				(end 0 2.413)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 0.127)
				(end 0 -0.127)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -2.413)
				(end 0 -2.667)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -4.953)
				(end 0 -5.207)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at -5.08 2.54 0)
				(length 3.81)
				(name "Pin_1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 0 0)
				(length 3.81)
				(name "Pin_2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -2.54 0)
				(length 3.81)
				(name "Pin_3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -5.08 0)
				(length 3.81)
				(name "Pin_4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Conn_01x06"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at 0 7.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x06"
			(at 0 -10.16 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Generic connector, single row, 01x06, script generated (kicad-library-utils/schlib/autogen/connector/)"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "connector"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Connector*:*_1x??_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Conn_01x06_1_1"
			(rectangle
				(start -1.27 6.35)
				(end 1.27 -8.89)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(rectangle
				(start -1.27 5.207)
				(end 0 4.953)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 2.667)
				(end 0 2.413)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 0.127)
				(end 0 -0.127)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -2.413)
				(end 0 -2.667)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -4.953)
				(end 0 -5.207)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -7.493)
				(end 0 -7.747)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at -5.08 5.08 0)
				(length 3.81)
				(name "Pin_1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 2.54 0)
				(length 3.81)
				(name "Pin_2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 0 0)
				(length 3.81)
				(name "Pin_3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -2.54 0)
				(length 3.81)
				(name "Pin_4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -5.08 0)
				(length 3.81)
				(name "Pin_5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -7.62 0)
				(length 3.81)
				(name "Pin_6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Conn_01x24"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at 0 30.48 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x24"
			(at 0 -33.02 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Generic connector, single row, 01x24, script generated (kicad-library-utils/schlib/autogen/connector/)"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "connector"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Connector*:*_1x??_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Conn_01x24_1_1"
			(rectangle
				(start -1.27 29.21)
				(end 1.27 -31.75)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(rectangle
				(start -1.27 28.067)
				(end 0 27.813)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 25.527)
				(end 0 25.273)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 22.987)
				(end 0 22.733)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 20.447)
				(end 0 20.193)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 17.907)
				(end 0 17.653)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 15.367)
				(end 0 15.113)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 12.827)
				(end 0 12.573)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 10.287)
				(end 0 10.033)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 7.747)
				(end 0 7.493)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 5.207)
				(end 0 4.953)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 2.667)
				(end 0 2.413)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 0.127)
				(end 0 -0.127)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -2.413)
				(end 0 -2.667)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -4.953)
				(end 0 -5.207)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -7.493)
				(end 0 -7.747)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -10.033)
				(end 0 -10.287)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -12.573)
				(end 0 -12.827)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -15.113)
				(end 0 -15.367)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -17.653)
				(end 0 -17.907)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -20.193)
				(end 0 -20.447)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -22.733)
				(end 0 -22.987)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -25.273)
				(end 0 -25.527)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -27.813)
				(end 0 -28.067)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -30.353)
				(end 0 -30.607)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at -5.08 27.94 0)
				(length 3.81)
				(name "Pin_1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 25.4 0)
				(length 3.81)
				(name "Pin_2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 22.86 0)
				(length 3.81)
				(name "Pin_3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 20.32 0)
				(length 3.81)
				(name "Pin_4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 17.78 0)
				(length 3.81)
				(name "Pin_5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 15.24 0)
				(length 3.81)
				(name "Pin_6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 12.7 0)
				(length 3.81)
				(name "Pin_7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 10.16 0)
				(length 3.81)
				(name "Pin_8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 7.62 0)
				(length 3.81)
				(name "Pin_9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 5.08 0)
				(length 3.81)
				(name "Pin_10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 2.54 0)
				(length 3.81)
				(name "Pin_11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 0 0)
				(length 3.81)
				(name "Pin_12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -2.54 0)
				(length 3.81)
				(name "Pin_13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -5.08 0)
				(length 3.81)
				(name "Pin_14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -7.62 0)
				(length 3.81)
				(name "Pin_15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -10.16 0)
				(length 3.81)
				(name "Pin_16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -12.7 0)
				(length 3.81)
				(name "Pin_17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -15.24 0)
				(length 3.81)
				(name "Pin_18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -17.78 0)
				(length 3.81)
				(name "Pin_19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -20.32 0)
				(length 3.81)
				(name "Pin_20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -22.86 0)
				(length 3.81)
				(name "Pin_21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -25.4 0)
				(length 3.81)
				(name "Pin_22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -27.94 0)
				(length 3.81)
				(name "Pin_23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -30.48 0)
				(length 3.81)
				(name "Pin_24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Crystal_GND24"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "Y"
			(at 3.175 5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "Crystal_GND24"
			(at 3.175 3.175 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Four pin crystal, GND on pins 2 and 4"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "quartz ceramic resonator oscillator"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Crystal*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Crystal_GND24_0_1"
			(polyline
				(pts
					(xy -2.54 2.286) (xy -2.54 3.556) (xy 2.54 3.556) (xy 2.54 2.286)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.54 0) (xy -2.032 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.54 -2.286) (xy -2.54 -3.556) (xy 2.54 -3.556) (xy 2.54 -2.286)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.032 -1.27) (xy -2.032 1.27)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.143 2.54)
				(end 1.143 -2.54)
				(stroke
					(width 0.3048)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 3.556) (xy 0 3.81)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 -3.81) (xy 0 -3.556)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 2.032 0) (xy 2.54 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 2.032 -1.27) (xy 2.032 1.27)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "Crystal_GND24_1_1"
			(pin passive line
				(at -3.81 0 0)
				(length 1.27)
				(name "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 0 5.08 270)
				(length 1.27)
				(name "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 0 -5.08 90)
				(length 1.27)
				(name "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 3.81 0 180)
				(length 1.27)
				(name "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "ECS-2520MV-xxx-xx"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "X"
			(at -5.08 6.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "ECS-2520MV-xxx-xx"
			(at 1.27 -6.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Oscillator:Oscillator_SMD_ECS_2520MV-xxx-xx-4Pin_2.5x2.0mm"
			(at 11.43 -8.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://www.ecsxtal.com/store/pdf/ECS-2520MV.pdf"
			(at -4.445 3.175 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "HCMOS Crystal Clock Oscillator, 2.5x2.0 mm SMD"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "Crystal Clock Oscillator ECS SMD"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Oscillator*SMD*ECS*2520MV*2.5x2.0mm*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "ECS-2520MV-xxx-xx_0_1"
			(rectangle
				(start -7.62 5.08)
				(end 7.62 -5.08)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(polyline
				(pts
					(xy -4.445 2.54) (xy -3.81 2.54) (xy -3.81 3.81) (xy -3.175 3.81) (xy -3.175 2.54) (xy -2.54 2.54)
					(xy -2.54 3.81) (xy -1.905 3.81) (xy -1.905 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "ECS-2520MV-xxx-xx_1_1"
			(pin input line
				(at -10.16 0 0)
				(length 2.54)
				(name "Tri-State"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 0 7.62 270)
				(length 2.54)
				(name "VDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 0 -7.62 90)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 10.16 0 180)
				(length 2.54)
				(name "OUT"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Fiducial"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "FID"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Fiducial"
			(at 0 3.175 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Fiducial Marker"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "fiducial marker"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Fiducial*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Fiducial_0_1"
			(circle
				(center 0 0)
				(radius 1.27)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type background)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "GND"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -6.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "power-flag"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "GND_0_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "GND_1_1"
			(pin power_in line
				(at 0 0 270)
				(length 0)
				(hide yes)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "KCSC02-106"
		(pin_names
			(offset 0.635)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at -2.54 13.97 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "KCSC02-106"
			(at 1.27 13.97 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Display_7Segment:KCSC02-106"
			(at 0 -15.24 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "http://www.kingbright.com/attachments/file/psearch/000/00/00/KCSC02-106(Ver.10A).pdf"
			(at -12.7 12.065 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Description" "One digit 7 segment super bright orange LED, common cathode"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "display LED 7-segment"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "KCSC02?106*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "KCSC02-106_0_0"
			(text "E"
				(at -2.54 1.778 0)
				(effects
					(font
						(size 0.508 0.508)
					)
				)
			)
			(text "F"
				(at -2.286 4.826 0)
				(effects
					(font
						(size 0.508 0.508)
					)
				)
			)
			(text "D"
				(at -0.254 1.016 0)
				(effects
					(font
						(size 0.508 0.508)
					)
				)
			)
			(text "G"
				(at 0 4.064 0)
				(effects
					(font
						(size 0.508 0.508)
					)
				)
			)
			(text "A"
				(at 0.254 5.588 0)
				(effects
					(font
						(size 0.508 0.508)
					)
				)
			)
			(text "C"
				(at 2.286 1.778 0)
				(effects
					(font
						(size 0.508 0.508)
					)
				)
			)
			(text "B"
				(at 2.54 4.826 0)
				(effects
					(font
						(size 0.508 0.508)
					)
				)
			)
			(text "DP"
				(at 3.556 0.254 0)
				(effects
					(font
						(size 0.508 0.508)
					)
				)
			)
		)
		(symbol "KCSC02-106_0_1"
			(rectangle
				(start -5.08 12.7)
				(end 5.08 -12.7)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(polyline
				(pts
					(xy -1.524 2.794) (xy -1.778 0.762)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 5.842) (xy -1.524 3.81)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 0.254) (xy 0.762 0.254)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.016 3.302) (xy 1.016 3.302)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -0.762 6.35) (xy 1.27 6.35)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.524 2.794) (xy 1.27 0.762)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.778 5.842) (xy 1.524 3.81)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 2.54 0.254) (xy 2.54 0.254)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "KCSC02-106_1_1"
			(pin input line
				(at -7.62 7.62 0)
				(length 2.54)
				(name "A"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -7.62 5.08 0)
				(length 2.54)
				(name "B"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -7.62 2.54 0)
				(length 2.54)
				(name "C"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -7.62 0 0)
				(length 2.54)
				(name "D"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -7.62 -2.54 0)
				(length 2.54)
				(name "E"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -7.62 -5.08 0)
				(length 2.54)
				(name "F"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -7.62 -7.62 0)
				(length 2.54)
				(name "G"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -7.62 -10.16 0)
				(length 2.54)
				(name "DP"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 7.62 -7.62 180)
				(length 2.54)
				(name "CC"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 7.62 -10.16 180)
				(length 2.54)
				(name "CC"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "LED_Small"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.254)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "D"
			(at -1.27 3.175 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "LED_Small"
			(at -4.445 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Light emitting diode, small symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "LED diode light-emitting-diode"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "LED* LED_SMD:* LED_THT:*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "LED_Small_0_1"
			(polyline
				(pts
					(xy -0.762 -1.016) (xy -0.762 1.016)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0.762) (xy -0.508 1.27) (xy -0.254 1.27) (xy -0.508 1.27) (xy -0.508 1.016)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0.508 1.27) (xy 0 1.778) (xy 0.254 1.778) (xy 0 1.778) (xy 0 1.524)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0.762 -1.016) (xy -0.762 0) (xy 0.762 1.016) (xy 0.762 -1.016)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.016 0) (xy -0.762 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "LED_Small_1_1"
			(pin passive line
				(at -2.54 0 0)
				(length 1.778)
				(name "K"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 0 180)
				(length 1.778)
				(name "A"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "MountingHole_Pad"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "H"
			(at 0 6.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "MountingHole_Pad"
			(at 0 4.445 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Mounting Hole with connection"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "mounting hole"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "MountingHole*Pad*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "MountingHole_Pad_0_1"
			(circle
				(center 0 1.27)
				(radius 1.27)
				(stroke
					(width 1.27)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "MountingHole_Pad_1_1"
			(pin input line
				(at 0 -2.54 90)
				(length 2.54)
				(name "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "NetTie_2"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom no)
		(on_board yes)
		(property "Reference" "NT"
			(at 0 1.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "NetTie_2"
			(at 0 -1.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Net tie, 2 pins"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "net tie short"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Net*Tie*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "NetTie_2_0_1"
			(polyline
				(pts
					(xy -1.27 0) (xy 1.27 0)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "NetTie_2_1_1"
			(pin passive line
				(at -2.54 0 0)
				(length 2.54)
				(name "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 0 180)
				(length 2.54)
				(name "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "PMOD_HOST_2x6"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at -3.81 12.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "613012243121"
			(at 2.54 -5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "TinyTapeout:PinSocket_2x06_P2.54mm_PMODHost1A"
			(at 1.27 -7.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Female PMOD Host connector"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "PMOD_HOST_2x6_1_1"
			(rectangle
				(start -5.08 11.43)
				(end 5.08 -3.81)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(text "PMOD Host"
				(at 0 3.81 900)
				(effects
					(font
						(size 0.8 0.8)
					)
				)
			)
			(pin bidirectional line
				(at -7.62 10.16 0)
				(length 2.54)
				(name "IO1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -7.62 7.62 0)
				(length 2.54)
				(name "IO2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -7.62 5.08 0)
				(length 2.54)
				(name "IO3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -7.62 2.54 0)
				(length 2.54)
				(name "IO4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -7.62 0 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -7.62 -2.54 0)
				(length 2.54)
				(name "VCC"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 7.62 10.16 180)
				(length 2.54)
				(name "IO5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 7.62 7.62 180)
				(length 2.54)
				(name "IO6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 7.62 5.08 180)
				(length 2.54)
				(name "IO7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 7.62 2.54 180)
				(length 2.54)
				(name "IO8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 7.62 0 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 7.62 -2.54 180)
				(length 2.54)
				(name "VCC"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "PMOD_PERIPH_2x6"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at -3.81 12.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "10129382-912001BLF"
			(at 2.54 -5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "TinyTapeout:PinHeader_2x06_P2.54mm_PMODPeriph2B"
			(at 1.27 -7.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://cdn.amphenol-cs.com/media/wysiwyg/files/drawing/10129382.pdf"
			(at 1.905 -8.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Male PMOD Peripheral connector"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "PMOD, peripheral"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "PMOD_PERIPH_2x6_1_1"
			(rectangle
				(start -5.08 11.43)
				(end 5.08 -3.81)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(text "PMOD Peripheral"
				(at 0 3.81 900)
				(effects
					(font
						(size 0.8 0.8)
					)
				)
			)
			(pin bidirectional line
				(at -7.62 10.16 0)
				(length 2.54)
				(name "IO1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -7.62 7.62 0)
				(length 2.54)
				(name "IO2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -7.62 5.08 0)
				(length 2.54)
				(name "IO3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -7.62 2.54 0)
				(length 2.54)
				(name "IO4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -7.62 0 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -7.62 -2.54 0)
				(length 2.54)
				(name "VCC"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 7.62 10.16 180)
				(length 2.54)
				(name "IO5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 7.62 7.62 180)
				(length 2.54)
				(name "IO6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 7.62 5.08 180)
				(length 2.54)
				(name "IO7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 7.62 2.54 180)
				(length 2.54)
				(name "IO8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 7.62 0 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 7.62 -2.54 180)
				(length 2.54)
				(name "VCC"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "PWR_FLAG"
		(power)
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#FLG"
			(at 0 1.905 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "PWR_FLAG"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Special symbol for telling ERC where power comes from"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "flag power"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "PWR_FLAG_0_0"
			(pin power_out line
				(at 0 0 90)
				(length 0)
				(name "pwr"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "PWR_FLAG_0_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 1.27) (xy -1.016 1.905) (xy 0 2.54) (xy 1.016 1.905) (xy 0 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Polyfuse"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "F"
			(at -2.54 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Polyfuse"
			(at 2.54 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 1.27 -5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resettable fuse, polymeric positive temperature coefficient"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "resettable fuse PTC PPTC polyfuse polyswitch"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "*polyfuse* *PTC*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Polyfuse_0_1"
			(polyline
				(pts
					(xy -1.524 2.54) (xy -1.524 1.524) (xy 1.524 -1.524) (xy 1.524 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -0.762 2.54)
				(end 0.762 -2.54)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 2.54) (xy 0 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "Polyfuse_1_1"
			(pin passive line
				(at 0 3.81 270)
				(length 1.27)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 0 -3.81 90)
				(length 1.27)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "RP2040"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 17.78 45.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "RP2040"
			(at 17.78 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Package_DFN_QFN:QFN-56-1EP_7x7mm_P0.4mm_EP3.2x3.2mm"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://datasheets.raspberrypi.com/rp2040/rp2040-datasheet.pdf"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "A microcontroller by Raspberry Pi"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "RP2040 ARM Cortex-M0+ USB"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "QFN*1EP*7x7mm?P0.4mm*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "RP2040_0_1"
			(rectangle
				(start -21.59 41.91)
				(end 21.59 -41.91)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
		)
		(symbol "RP2040_1_1"
			(pin input line
				(at -25.4 38.1 0)
				(length 3.81)
				(name "RUN"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "26"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -25.4 30.48 0)
				(length 3.81)
				(name "USB_DP"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "47"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -25.4 27.94 0)
				(length 3.81)
				(name "USB_DM"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "46"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -25.4 20.32 0)
				(length 3.81)
				(name "QSPI_SS"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "56"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -25.4 15.24 0)
				(length 3.81)
				(name "QSPI_SD0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "53"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -25.4 12.7 0)
				(length 3.81)
				(name "QSPI_SD1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "55"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -25.4 10.16 0)
				(length 3.81)
				(name "QSPI_SD2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "54"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -25.4 7.62 0)
				(length 3.81)
				(name "QSPI_SD3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "51"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at -25.4 5.08 0)
				(length 3.81)
				(name "QSPI_SCLK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "52"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -25.4 -7.62 0)
				(length 3.81)
				(name "XIN"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -25.4 -17.78 0)
				(length 3.81)
				(name "XOUT"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -25.4 -27.94 0)
				(length 3.81)
				(name "SWCLK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -25.4 -30.48 0)
				(length 3.81)
				(name "SWD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "25"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -25.4 -38.1 0)
				(length 3.81)
				(name "TESTEN"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_out line
				(at -5.08 45.72 270)
				(length 3.81)
				(name "VREG_VOUT"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "45"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -2.54 45.72 270)
				(length 3.81)
				(name "DVDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -2.54 45.72 270)
				(length 3.81)
				(hide yes)
				(name "DVDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "50"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 0 -45.72 90)
				(length 3.81)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "57"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 2.54 45.72 270)
				(length 3.81)
				(name "IOVDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 45.72 270)
				(length 3.81)
				(hide yes)
				(name "IOVDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 45.72 270)
				(length 3.81)
				(hide yes)
				(name "IOVDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 45.72 270)
				(length 3.81)
				(hide yes)
				(name "IOVDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 45.72 270)
				(length 3.81)
				(hide yes)
				(name "IOVDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "42"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 45.72 270)
				(length 3.81)
				(hide yes)
				(name "IOVDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "49"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 5.08 45.72 270)
				(length 3.81)
				(name "USB_VDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "48"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 7.62 45.72 270)
				(length 3.81)
				(name "ADC_AVDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "43"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 10.16 45.72 270)
				(length 3.81)
				(name "VREG_IN"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "44"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 38.1 180)
				(length 3.81)
				(name "GPIO0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 35.56 180)
				(length 3.81)
				(name "GPIO1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 33.02 180)
				(length 3.81)
				(name "GPIO2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 30.48 180)
				(length 3.81)
				(name "GPIO3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 27.94 180)
				(length 3.81)
				(name "GPIO4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 25.4 180)
				(length 3.81)
				(name "GPIO5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 22.86 180)
				(length 3.81)
				(name "GPIO6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 20.32 180)
				(length 3.81)
				(name "GPIO7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 17.78 180)
				(length 3.81)
				(name "GPIO8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 15.24 180)
				(length 3.81)
				(name "GPIO9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 12.7 180)
				(length 3.81)
				(name "GPIO10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 10.16 180)
				(length 3.81)
				(name "GPIO11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 7.62 180)
				(length 3.81)
				(name "GPIO12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 5.08 180)
				(length 3.81)
				(name "GPIO13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 2.54 180)
				(length 3.81)
				(name "GPIO14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 0 180)
				(length 3.81)
				(name "GPIO15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 -2.54 180)
				(length 3.81)
				(name "GPIO16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "27"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 -5.08 180)
				(length 3.81)
				(name "GPIO17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "28"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 -7.62 180)
				(length 3.81)
				(name "GPIO18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "29"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 -10.16 180)
				(length 3.81)
				(name "GPIO19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "30"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 -12.7 180)
				(length 3.81)
				(name "GPIO20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "31"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 -15.24 180)
				(length 3.81)
				(name "GPIO21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "32"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 -17.78 180)
				(length 3.81)
				(name "GPIO22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "34"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 -20.32 180)
				(length 3.81)
				(name "GPIO23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "35"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 -22.86 180)
				(length 3.81)
				(name "GPIO24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "36"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 -25.4 180)
				(length 3.81)
				(name "GPIO25"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "37"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 -30.48 180)
				(length 3.81)
				(name "GPIO26_ADC0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "38"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 -33.02 180)
				(length 3.81)
				(name "GPIO27_ADC1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "39"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 -35.56 180)
				(length 3.81)
				(name "GPIO28_ADC2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "40"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 25.4 -38.1 180)
				(length 3.81)
				(name "GPIO29_ADC3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "41"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "R_Small"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.254)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "R"
			(at 0.762 0.508 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "R_Small"
			(at 0.762 -1.016 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor, small symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "R resistor"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "R_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "R_Small_0_1"
			(rectangle
				(start -0.762 1.778)
				(end 0.762 -1.778)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "R_Small_1_1"
			(pin passive line
				(at 0 2.54 270)
				(length 0.762)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 0 -2.54 90)
				(length 0.762)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "SW_DIP_x08"
		(pin_names
			(offset 0)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "SW"
			(at 0 13.97 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "SW_DIP_x08"
			(at 0 -11.43 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "8x DIP Switch, Single Pole Single Throw (SPST) switch, small symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "dip switch"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "SW?DIP?x8*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "SW_DIP_x08_0_0"
			(circle
				(center -2.032 10.16)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -2.032 7.62)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -2.032 5.08)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -2.032 2.54)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -2.032 0)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -2.032 -2.54)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -2.032 -5.08)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -2.032 -7.62)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.524 10.287) (xy 2.3622 11.3284)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.524 7.747) (xy 2.3622 8.7884)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.524 5.207) (xy 2.3622 6.2484)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.524 2.667) (xy 2.3622 3.7084)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.524 0.127) (xy 2.3622 1.1684)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.524 -2.3876) (xy 2.3622 -1.3462)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.524 -4.9276) (xy 2.3622 -3.8862)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.524 -7.4676) (xy 2.3622 -6.4262)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.032 10.16)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.032 7.62)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.032 5.08)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.032 2.54)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.032 0)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.032 -2.54)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.032 -5.08)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.032 -7.62)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "SW_DIP_x08_0_1"
			(rectangle
				(start -3.81 12.7)
				(end 3.81 -10.16)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
		)
		(symbol "SW_DIP_x08_1_1"
			(pin passive line
				(at -7.62 10.16 0)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -7.62 7.62 0)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -7.62 5.08 0)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -7.62 2.54 0)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -7.62 0 0)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -7.62 -2.54 0)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -7.62 -5.08 0)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -7.62 -7.62 0)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 10.16 180)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 7.62 180)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 5.08 180)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 2.54 180)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 0 180)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 -2.54 180)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 -5.08 180)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 -7.62 180)
				(length 5.08)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "SW_Push"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "SW"
			(at 1.27 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "SW_Push"
			(at 0 -1.524 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Push button switch, generic, two pins"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "switch normally-open pushbutton push-button"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "SW_Push_0_1"
			(circle
				(center -2.032 0)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 1.27) (xy 0 3.048)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.032 0)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 2.54 1.27) (xy -2.54 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at -5.08 0 0)
				(length 2.54)
				(name "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 5.08 0 180)
				(length 2.54)
				(name "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "SolderJumper_2_Bridged"
		(pin_names
			(offset 0)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "JP"
			(at 0 2.032 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "SolderJumper_2_Bridged"
			(at 0 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Solder Jumper, 2-pole, closed/bridged"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "solder jumper SPST"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "SolderJumper*Bridged*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "SolderJumper_2_Bridged_0_1"
			(rectangle
				(start -0.508 0.508)
				(end 0.508 -0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy -0.254 1.016) (xy -0.254 -1.016)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -0.254 -1.016)
				(mid -1.2656 0)
				(end -0.254 1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -0.254 -1.016)
				(mid -1.2656 0)
				(end -0.254 1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(arc
				(start 0.254 1.016)
				(mid 1.2656 0)
				(end 0.254 -1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start 0.254 1.016)
				(mid 1.2656 0)
				(end 0.254 -1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy 0.254 1.016) (xy 0.254 -1.016)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "SolderJumper_2_Bridged_1_1"
			(pin passive line
				(at -3.81 0 0)
				(length 2.54)
				(name "A"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 3.81 0 180)
				(length 2.54)
				(name "B"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "TestPoint"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.762)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "TP"
			(at 0 6.858 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "TestPoint"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 5.08 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 5.08 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "test point"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "test point tp"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Pin* Test*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "TestPoint_0_1"
			(circle
				(center 0 3.302)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "TestPoint_1_1"
			(pin passive line
				(at 0 0 90)
				(length 2.54)
				(name "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "TestPoint_Small"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.762)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "TP"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "TestPoint_Small"
			(at 0 2.032 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 5.08 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 5.08 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "test point"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "test point tp"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Pin* Test*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "TestPoint_Small_0_1"
			(circle
				(center 0 0)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "TestPoint_Small_1_1"
			(pin passive line
				(at 0 0 90)
				(length 0)
				(name "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "UJC-HP-3-SMT-TR"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 1.016)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at -7.62 8.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left bottom)
			)
		)
		(property "Value" "UJC-HP-3-SMT-TR"
			(at -7.62 -10.922 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left top)
			)
		)
		(property "Footprint" "TinyTapeout:CUI_UJC-HP-3-SMT-TR"
			(at -1.27 -16.51 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify bottom)
				(hide yes)
			)
		)
		(property "Datasheet" "https://www.cuidevices.com/product/resource/ujc-hp-3-smt-tr.pdf"
			(at 1.27 -17.78 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "USB Connectors USB jack, C type, power only, 6 pin, horizonal, gold flash plating, surface mount, T&R"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MAXIMUM_PACKAGE_HEIGHT" "3.16mm"
			(at 2.54 -20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify bottom)
				(hide yes)
			)
		)
		(property "PARTREV" "04/30/2020"
			(at 2.54 -22.86 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify bottom)
				(hide yes)
			)
		)
		(property "MPN" "UJC-HP-3-SMT-TR"
			(at 2.54 -25.4 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify bottom)
				(hide yes)
			)
		)
		(property "MANUFACTURER" "CUI Devices"
			(at -16.51 -12.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify bottom)
				(hide yes)
			)
		)
		(property "ki_keywords" "USB-C, power, USB"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "UJC-HP-3-SMT-TR_0_0"
			(rectangle
				(start -7.62 7.62)
				(end 8.128 -10.16)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin power_in line
				(at -12.7 5.08 0)
				(length 5.08)
				(name "V-BUS"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "A9"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin power_in line
				(at -12.7 5.08 0)
				(length 5.08)
				(name "V-BUS"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "B9"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin bidirectional line
				(at -12.7 0 0)
				(length 5.08)
				(name "CC1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "A5"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin bidirectional line
				(at -12.7 -2.54 0)
				(length 5.08)
				(name "CC2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "B5"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin power_in line
				(at -12.7 -5.08 0)
				(length 5.08)
				(name "GND"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "A12"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin power_in line
				(at -12.7 -5.08 0)
				(length 5.08)
				(name "GND"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "B12"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at -12.7 -7.62 0)
				(length 5.08)
				(name "SHIELD"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "S1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at -12.7 -7.62 0)
				(length 5.08)
				(name "SHIELD"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "S2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at -12.7 -7.62 0)
				(length 5.08)
				(name "SHIELD"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "S3"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at -12.7 -7.62 0)
				(length 5.08)
				(name "SHIELD"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "S4"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(symbol "UJC-HP-3-SMT-TR_0_1"
			(polyline
				(pts
					(xy 3.81 -4.572) (xy 3.81 3.048)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start 5.08 -4.572)
				(end 6.35 3.048)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(arc
				(start 5.08 3.048)
				(mid 5.715 3.6803)
				(end 6.35 3.048)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start 5.08 3.048)
				(mid 5.715 3.6803)
				(end 6.35 3.048)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(arc
				(start 3.81 3.048)
				(mid 5.715 4.9447)
				(end 7.62 3.048)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start 7.62 -4.572)
				(mid 5.715 -6.4687)
				(end 3.81 -4.572)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start 6.35 -4.572)
				(mid 5.715 -5.2043)
				(end 5.08 -4.572)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start 6.35 -4.572)
				(mid 5.715 -5.2043)
				(end 5.08 -4.572)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy 7.62 3.048) (xy 7.62 -4.572)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "UJC-HP-3-SMT-TR_1_1"
			(circle
				(center -2.54 -1.524)
				(radius 0.635)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy -1.27 1.651) (xy 0 4.191) (xy 1.27 1.651) (xy -1.27 1.651)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy 0 -4.699) (xy 2.54 -2.159) (xy 2.54 -0.889)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 -5.969) (xy -2.54 -3.429) (xy -2.54 -2.159)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 -8.509) (xy 0 1.651)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 0 -8.509)
				(radius 1.27)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(rectangle
				(start 1.905 -0.889)
				(end 3.175 0.381)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "USB4500-03-0-A_REVA"
		(pin_names
			(offset 1.016)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at -12.7 16.002 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left bottom)
			)
		)
		(property "Value" "USB4500-03-0-A_REVA"
			(at 2.54 -20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left top)
			)
		)
		(property "Footprint" "TinyTapeout:GCT_USB4500-03-0-A_REVA"
			(at -2.54 -38.1 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify bottom)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "USB-C (USB TYPE-C) USB 2.0 Receptacle Connector 24 (16+8 Dummy) Position Board Edge, Cutout; Surface Mount; Through Hole, Right Angle"
			(at 7.62 -33.02 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify bottom)
				(hide yes)
			)
		)
		(property "MAXIMUM_PACKAGE_HEIGHT" "2.46mm"
			(at 0 -35.56 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify bottom)
				(hide yes)
			)
		)
		(property "MPN" "USB4500-03-0-A"
			(at -2.54 -35.56 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify bottom)
				(hide yes)
			)
		)
		(property "MANUFACTURER" "GCT"
			(at -2.54 -38.1 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify bottom)
				(hide yes)
			)
		)
		(symbol "USB4500-03-0-A_REVA_0_0"
			(rectangle
				(start -10.16 17.78)
				(end 7.62 -17.78)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin power_in line
				(at -7.62 -20.32 90)
				(length 2.54)
				(name "SHIELD"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "S2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin power_in line
				(at -7.62 -20.32 90)
				(length 2.54)
				(name "SHIELD"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "S3"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin power_in line
				(at -7.62 -20.32 90)
				(length 2.54)
				(name "SHIELD"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "S4"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin bidirectional line
				(at 12.7 10.16 180)
				(length 5.08)
				(name "CC1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "A5"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin bidirectional line
				(at 12.7 7.62 180)
				(length 5.08)
				(name "CC2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "B5"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin bidirectional line
				(at 12.7 2.54 180)
				(length 5.08)
				(name "DN1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "A7"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin bidirectional line
				(at 12.7 0 180)
				(length 5.08)
				(name "DN2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "B7"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin bidirectional line
				(at 12.7 -2.54 180)
				(length 5.08)
				(name "DP1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "A6"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin bidirectional line
				(at 12.7 -5.08 180)
				(length 5.08)
				(name "DP2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "B6"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin bidirectional line
				(at 12.7 -12.7 180)
				(length 5.08)
				(name "SBU1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "A8"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin bidirectional line
				(at 12.7 -15.24 180)
				(length 5.08)
				(name "SBU2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "B8"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(symbol "USB4500-03-0-A_REVA_0_1"
			(polyline
				(pts
					(xy -6.35 3.81) (xy -6.35 11.43)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -5.08 3.81)
				(end -3.81 11.43)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(arc
				(start -5.08 11.43)
				(mid -4.445 12.0623)
				(end -3.81 11.43)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -5.08 11.43)
				(mid -4.445 12.0623)
				(end -3.81 11.43)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(arc
				(start -6.35 11.43)
				(mid -4.445 13.3267)
				(end -2.54 11.43)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -2.54 3.81)
				(mid -4.445 1.9133)
				(end -6.35 3.81)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -3.81 3.81)
				(mid -4.445 3.1777)
				(end -5.08 3.81)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -3.81 3.81)
				(mid -4.445 3.1777)
				(end -5.08 3.81)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy -2.54 11.43) (xy -2.54 3.81)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "USB4500-03-0-A_REVA_1_0"
			(pin power_in line
				(at -7.62 -20.32 90)
				(length 2.54)
				(name "SHIELD"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "S1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin power_in line
				(at -2.54 -20.32 90)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "A1_B12"
					(effects
						(font
							(size 0.5 0.5)
						)
					)
				)
			)
			(pin power_in line
				(at 0 -20.32 90)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "B1_A12"
					(effects
						(font
							(size 0.5 0.5)
						)
					)
				)
			)
			(pin power_in line
				(at 12.7 15.24 180)
				(length 5.08)
				(name "VBUS"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "A4_B9"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin power_in line
				(at 12.7 15.24 180)
				(length 5.08)
				(name "VBUS"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "B4_A9"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(symbol "USB4500-03-0-A_REVA_1_1"
			(circle
				(center -6.35 -7.62)
				(radius 0.635)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy -5.08 -4.445) (xy -3.81 -1.905) (xy -2.54 -4.445) (xy -5.08 -4.445)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy -3.81 -10.795) (xy -1.27 -8.255) (xy -1.27 -6.985)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -12.065) (xy -6.35 -9.525) (xy -6.35 -8.255)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -3.81 -14.605)
				(radius 1.27)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy -3.81 -14.605) (xy -3.81 -4.445)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.905 -6.985)
				(end -0.635 -5.715)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "VBUS"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VBUS"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VBUS\""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "power-flag"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "VBUS_0_1"
			(polyline
				(pts
					(xy -0.762 1.27) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 2.54) (xy 0.762 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "VBUS_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "VBUS"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "W25Q32JVSS"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at -8.89 8.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "W25Q32JVSS"
			(at 7.62 8.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Package_SO:SOIC-8_5.23x5.23mm_P1.27mm"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "http://www.winbond.com/resource-files/w25q32jv%20revg%2003272018%20plus.pdf"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "32Mb Serial Flash Memory, Standard/Dual/Quad SPI, SOIC-8"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "flash memory SPI"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "SOIC*5.23x5.23mm*P1.27mm*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "W25Q32JVSS_0_1"
			(rectangle
				(start -10.16 7.62)
				(end 10.16 -7.62)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
		)
		(symbol "W25Q32JVSS_1_1"
			(pin input line
				(at -12.7 2.54 0)
				(length 2.54)
				(name "~{CS}"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -12.7 -2.54 0)
				(length 2.54)
				(name "CLK"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 0 10.16 270)
				(length 2.54)
				(name "VCC"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 0 -10.16 90)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 12.7 5.08 180)
				(length 2.54)
				(name "DI(IO0)"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 12.7 2.54 180)
				(length 2.54)
				(name "DO(IO1)"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 12.7 -2.54 180)
				(length 2.54)
				(name "IO2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 12.7 -5.08 180)
				(length 2.54)
				(name "IO3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
)
