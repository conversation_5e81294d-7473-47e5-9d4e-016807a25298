/*
 * This program source code file is part of KiCad, a free EDA CAD application.
 *
 * Copyright (C) 2024 KiCad Developers, see AUTHORS.txt for contributors.
 *
 * This program is free software: you can redistribute it and/or modify it
 * under the terms of the GNU General Public License as published by the
 * Free Software Foundation, either version 3 of the License, or (at your
 * option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License along
 * with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

syntax = "proto3";

package kiapi.common.commands;

import "common/types/base_types.proto";
import "common/types/project_settings.proto";


message GetNetClasses
{
}

message NetClassesResponse
{
  repeated kiapi.common.project.NetClass net_classes = 1;
}


message SetNetClasses
{
  repeated kiapi.common.project.NetClass net_classes = 1;

  // Whether to merge or replace the existing netclasses with the contents of this message
  // Note that this only happens at the level of netclass name: for example, if merge_mode is set to
  // MMM_MERGE, the design has netclasses ["Default", "HV"], and this message has netclasses
  // ["Default", "LV"], the resulting set will be ["Default", "HV", "LV"] -- the Default netclass
  // will have its properties replaced with those in this message, the "LV" netclass will be added,
  // and the "HV" netclass will be left alone.  If merge_mode is set to MMM_REPLACE, the "HV" class
  // will be erased.  Note that there must always be a "Default" netclass, so it will not be erased
  // even if merge_mode is MMM_REPLACE and there is no "Default" class specified in this message.
  kiapi.common.types.MapMergeMode merge_mode = 3;
}


message ExpandTextVariables
{
  kiapi.common.types.DocumentSpecifier document = 1;

  repeated string text = 2;
}


message ExpandTextVariablesResponse
{
  repeated string text = 1;
}


// returns kiapi.common.project.TextVariables
message GetTextVariables
{
  kiapi.common.types.DocumentSpecifier document = 1;
}


message SetTextVariables
{
  kiapi.common.types.DocumentSpecifier document = 1;

  kiapi.common.project.TextVariables variables = 2;
  // Whether to merge or replace the existing text variables map with the contents of this message
  kiapi.common.types.MapMergeMode merge_mode = 3;
}
