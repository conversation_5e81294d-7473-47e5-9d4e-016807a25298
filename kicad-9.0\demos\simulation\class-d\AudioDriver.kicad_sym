(kicad_symbol_lib (version 20230620) (generator kicad_symbol_editor)
  (symbol "AudioDriver1" (pin_names (offset 1.016)) (exclude_from_sim no) (in_bom yes) (on_board yes)
    (property "Reference" "U" (at -7.62 8.89 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "AudioDriver1" (at -7.62 -8.89 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Description" "Class D audio driver" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "ki_locked" "" (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "ki_keywords" "CMOS" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "ki_fp_filters" "DIP?16*" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "AudioDriver1_1_0"
      (pin output line (at 12.7 2.54 180) (length 5.08)
        (name "HO+" (effects (font (size 1.27 1.27))))
        (number "3" (effects (font (size 1.27 1.27))))
      )
      (pin output line (at 12.7 0 180) (length 5.08)
        (name "HO-" (effects (font (size 1.27 1.27))))
        (number "4" (effects (font (size 1.27 1.27))))
      )
      (pin output line (at 12.7 -2.54 180) (length 5.08)
        (name "LO+" (effects (font (size 1.27 1.27))))
        (number "5" (effects (font (size 1.27 1.27))))
      )
      (pin output line (at 12.7 -5.08 180) (length 5.08)
        (name "LO-" (effects (font (size 1.27 1.27))))
        (number "6" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -12.7 -2.54 0) (length 5.08)
        (name "A_in" (effects (font (size 1.27 1.27))))
        (number "7" (effects (font (size 1.27 1.27))))
      )
    )
    (symbol "AudioDriver1_1_1"
      (rectangle (start -7.62 5.08) (end 7.62 -7.62)
        (stroke (width 0.254) (type default))
        (fill (type background))
      )
    )
  )
)
