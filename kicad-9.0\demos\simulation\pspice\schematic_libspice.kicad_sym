(kicad_symbol_lib (version 20220331) (generator kicad_symbol_editor)
  (symbol "+12V" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
    (property "Reference" "#PWR" (id 0) (at 0 -3.81 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+12V" (id 1) (at 0 3.556 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "ki_keywords" "Power Flag Symbol" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "ki_description" "power-flag symbol" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "+12V_0_1"
      (polyline
        (pts
          (xy -0.762 1.27)
          (xy 0 2.54)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 0 0)
          (xy 0 2.54)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 0 2.54)
          (xy 0.762 1.27)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
    )
    (symbol "+12V_1_1"
      (pin power_in line (at 0 0 90) (length 0) hide
        (name "+12V" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "0" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
    (property "Reference" "#GND" (id 0) (at 0 -2.54 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "0" (id 1) (at 0 -1.778 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (symbol "0_0_1"
      (polyline
        (pts
          (xy -1.27 0)
          (xy 0 -1.27)
          (xy 1.27 0)
          (xy -1.27 0)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
    )
    (symbol "0_1_1"
      (pin power_in line (at 0 0 0) (length 0) hide
        (name "0" (effects (font (size 1.016 1.016))))
        (number "1" (effects (font (size 1.016 1.016))))
      )
    )
  )
  (symbol "CAP" (pin_names (offset 0.254)) (in_bom yes) (on_board yes)
    (property "Reference" "C" (id 0) (at 2.54 3.81 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "CAP" (id 1) (at 2.54 -3.81 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (symbol "CAP_0_1"
      (polyline
        (pts
          (xy -3.81 -1.27)
          (xy 3.81 -1.27)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy -3.81 1.27)
          (xy 3.81 1.27)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
    )
    (symbol "CAP_1_1"
      (pin passive line (at 0 6.35 270) (length 5.08)
        (name "~" (effects (font (size 1.016 1.016))))
        (number "1" (effects (font (size 1.016 1.016))))
      )
      (pin passive line (at 0 -6.35 90) (length 5.08)
        (name "~" (effects (font (size 1.016 1.016))))
        (number "2" (effects (font (size 1.016 1.016))))
      )
    )
  )
  (symbol "C" (extends "CAP")
    (property "Reference" "C" (id 0) (at 2.54 3.81 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "C" (id 1) (at 2.54 -3.81 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
  )
  (symbol "DIODE" (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
    (property "Reference" "D" (id 0) (at 0 3.81 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "DIODE" (id 1) (at 0 -4.445 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (symbol "DIODE_0_1"
      (polyline
        (pts
          (xy -1.905 2.54)
          (xy -1.905 -2.54)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 1.905 2.54)
          (xy 1.905 -2.54)
          (xy -1.905 0)
        )
        (stroke (width 0) (type default))
        (fill (type outline))
      )
    )
    (symbol "DIODE_1_1"
      (pin input line (at -5.08 0 0) (length 3.81)
        (name "K" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at 5.08 0 180) (length 3.81)
        (name "A" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
    (property "Reference" "#PWR" (id 0) (at 0 -5.08 0)
      (effects (font (size 0.762 0.762)) hide)
    )
    (property "Value" "GND" (id 1) (at 0 -3.81 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "ki_keywords" "Power Flag Symbol" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "ki_description" "GROUND power-flag symbol" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "GND_0_1"
      (polyline
        (pts
          (xy 0 0)
          (xy 0 -1.27)
          (xy 1.27 -1.27)
          (xy 0 -2.54)
          (xy -1.27 -1.27)
          (xy 0 -1.27)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
    )
    (symbol "GND_1_1"
      (pin power_in line (at 0 0 270) (length 0) hide
        (name "GND" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "INDUCTOR" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
    (property "Reference" "L" (id 0) (at 0 2.54 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "INDUCTOR" (id 1) (at 0 -1.27 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (symbol "INDUCTOR_0_1"
      (arc (start -2.54 0) (mid -3.81 1.2645) (end -5.08 0)
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (arc (start 0 0) (mid -1.27 1.2645) (end -2.54 0)
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (arc (start 2.54 0) (mid 1.27 1.2645) (end 0 0)
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (arc (start 5.08 0) (mid 3.81 1.2645) (end 2.54 0)
        (stroke (width 0) (type default))
        (fill (type none))
      )
    )
    (symbol "INDUCTOR_1_1"
      (pin input line (at -6.35 0 0) (length 1.27)
        (name "1" (effects (font (size 0.762 0.762))))
        (number "1" (effects (font (size 0.762 0.762))))
      )
      (pin input line (at 6.35 0 180) (length 1.27)
        (name "2" (effects (font (size 0.762 0.762))))
        (number "2" (effects (font (size 0.762 0.762))))
      )
    )
  )
  (symbol "ISOURCE" (pin_names (offset 1.016)) (in_bom yes) (on_board yes)
    (property "Reference" "I" (id 0) (at 0 -4.572 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "ISOURCE" (id 1) (at 0.254 4.318 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (symbol "ISOURCE_0_1"
      (circle (center 0 -3.81) (radius 6.35)
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy -8.89 -5.08)
          (xy -8.89 5.08)
        )
        (stroke (width 0) (type default))
        (fill (type outline))
      )
      (polyline
        (pts
          (xy -10.16 5.08)
          (xy -8.89 7.62)
          (xy -7.62 5.08)
        )
        (stroke (width 0) (type default))
        (fill (type outline))
      )
      (circle (center 0 3.81) (radius 6.35)
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (text "I" (at -8.128 -0.254 0)
        (effects (font (size 2.54 2.54)))
      )
    )
    (symbol "ISOURCE_1_1"
      (pin input line (at 0 17.78 270) (length 7.62)
        (name "E1" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at 0 -17.78 90) (length 7.62)
        (name "E2" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "PWR_FLAG" (power) (pin_numbers hide) (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
    (property "Reference" "#FLG" (id 0) (at 0 1.905 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "PWR_FLAG" (id 1) (at 0 3.81 0)
      (effects (font (size 1.016 1.016)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "ki_keywords" "Power Flag Symbol" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "ki_description" "general power-flag symbol" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "PWR_FLAG_0_0"
      (pin power_out line (at 0 0 90) (length 0)
        (name "pwr" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
    )
    (symbol "PWR_FLAG_0_1"
      (polyline
        (pts
          (xy 0 0)
          (xy 0 1.27)
          (xy -1.016 1.905)
          (xy 0 2.54)
          (xy 1.016 1.905)
          (xy 0 1.27)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
    )
  )
  (symbol "QNPN" (pin_names (offset 0)) (in_bom yes) (on_board yes)
    (property "Reference" "Q" (id 0) (at -2.54 7.62 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "QNPN" (id 1) (at -2.54 5.08 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (symbol "QNPN_0_0"
      (polyline
        (pts
          (xy 0 0)
          (xy 3.81 -3.81)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 3.81 -3.81)
          (xy 3.81 -1.27)
          (xy 1.27 -3.81)
          (xy 3.81 -3.81)
        )
        (stroke (width 0) (type default))
        (fill (type outline))
      )
    )
    (symbol "QNPN_0_1"
      (polyline
        (pts
          (xy 0 -3.81)
          (xy 0 3.81)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 0 0)
          (xy 3.81 3.81)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
    )
    (symbol "QNPN_1_1"
      (pin passive line (at 3.81 8.89 270) (length 5.08)
        (name "C" (effects (font (size 1.016 1.016))))
        (number "1" (effects (font (size 1.016 1.016))))
      )
      (pin input line (at -7.62 0 0) (length 7.62)
        (name "B" (effects (font (size 1.016 1.016))))
        (number "2" (effects (font (size 1.016 1.016))))
      )
      (pin passive line (at 3.81 -8.89 90) (length 5.08)
        (name "E" (effects (font (size 1.016 1.016))))
        (number "3" (effects (font (size 1.016 1.016))))
      )
    )
  )
  (symbol "QPNP" (pin_names (offset 0)) (in_bom yes) (on_board yes)
    (property "Reference" "Q" (id 0) (at -2.54 7.62 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "QPNP" (id 1) (at -2.54 5.08 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (symbol "QPNP_0_1"
      (polyline
        (pts
          (xy 0 -3.81)
          (xy 0 3.81)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 0 0)
          (xy 3.81 -3.81)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 0 0)
          (xy 3.81 3.81)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 3.048 -4.572)
          (xy 4.572 -3.048)
          (xy 2.159 -2.159)
          (xy 3.048 -4.572)
        )
        (stroke (width 0) (type default))
        (fill (type outline))
      )
    )
    (symbol "QPNP_1_1"
      (pin open_collector line (at 3.81 8.89 270) (length 5.08)
        (name "C" (effects (font (size 1.016 1.016))))
        (number "1" (effects (font (size 1.016 1.016))))
      )
      (pin input line (at -7.62 0 0) (length 7.62)
        (name "B" (effects (font (size 1.016 1.016))))
        (number "2" (effects (font (size 1.016 1.016))))
      )
      (pin open_emitter line (at 3.81 -8.89 90) (length 5.08)
        (name "E" (effects (font (size 1.016 1.016))))
        (number "3" (effects (font (size 1.016 1.016))))
      )
    )
  )
  (symbol "R" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
    (property "Reference" "R" (id 0) (at 2.032 0 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "R" (id 1) (at 0 0 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "ki_keywords" "R DEV" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "ki_description" "Resistance" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "R_0_1"
      (rectangle (start -1.016 3.81) (end 1.016 -3.81)
        (stroke (width 0) (type default))
        (fill (type none))
      )
    )
    (symbol "R_1_1"
      (pin passive line (at 0 6.35 270) (length 2.54)
        (name "1" (effects (font (size 0.508 0.508))))
        (number "1" (effects (font (size 0.508 0.508))))
      )
      (pin passive line (at 0 -6.35 90) (length 2.54)
        (name "2" (effects (font (size 0.508 0.508))))
        (number "2" (effects (font (size 0.508 0.508))))
      )
    )
  )
  (symbol "VSOURCE" (pin_names (offset 1.016)) (in_bom yes) (on_board yes)
    (property "Reference" "V" (id 0) (at 0 -2.54 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "VSOURCE" (id 1) (at 0 2.54 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (symbol "VSOURCE_0_1"
      (polyline
        (pts
          (xy -6.35 -6.35)
          (xy -6.35 3.81)
        )
        (stroke (width 0) (type default))
        (fill (type outline))
      )
      (polyline
        (pts
          (xy -7.62 3.81)
          (xy -6.35 6.35)
          (xy -5.08 3.81)
        )
        (stroke (width 0) (type default))
        (fill (type outline))
      )
      (circle (center 0 0) (radius 10.16)
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (text "V" (at -8.128 -0.254 0)
        (effects (font (size 2.54 2.54)))
      )
    )
    (symbol "VSOURCE_1_1"
      (pin input line (at 0 17.78 270) (length 7.62)
        (name "E1" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at 0 -17.78 90) (length 7.62)
        (name "E2" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
)
