(kicad_symbol_lib
	(version 20241209)
	(generator "kicad_symbol_editor")
	(generator_version "9.0")
	(symbol "+12V"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+12V"
			(at 0 3.556 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "power-flag symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "Power Flag Symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "+12V_0_1"
			(polyline
				(pts
					(xy -0.762 1.27) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 2.54) (xy 0.762 1.27)
				)
				(stroke
					(width 0)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type solid)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "+12V_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "+12V"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "+1V1"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+1V1"
			(at 0 3.556 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "power-flag symbol +1.1V"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "Power Flag Symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "+1V1_0_1"
			(polyline
				(pts
					(xy -0.762 1.27) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 2.54) (xy 0.762 1.27)
				)
				(stroke
					(width 0)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type solid)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "+1V1_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "+1V1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Audio-Jack-2_Switch_1"
		(pin_names
			(offset 1.016)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J1"
			(at -1.27 7.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "CH1"
			(at -1.27 5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Connector_Audio:Jack_6.35mm_Neutrik_NMJ6HCD2_Horizontal"
			(at 6.35 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 6.35 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "2-pin audio jack receptable (mono/TS connector) with switching contact"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "audio jack receptable mono headphones phone TS connector"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Audio-Jack-2_Switch_1_0_0"
			(text "1"
				(at -2.54 1.27 0)
				(effects
					(font
						(size 0.762 0.762)
					)
				)
			)
		)
		(symbol "Audio-Jack-2_Switch_1_0_1"
			(rectangle
				(start -4.699 3.175)
				(end 2.54 -0.635)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type background)
				)
			)
			(rectangle
				(start -4.445 0)
				(end -5.207 2.54)
				(stroke
					(width 0)
					(type solid)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy -0.635 2.413) (xy -0.254 1.651)
				)
				(stroke
					(width 0)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 2.54 2.54) (xy -1.905 2.54) (xy -2.54 1.905) (xy -3.175 2.54)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 2.54 0) (xy -0.635 0) (xy -0.635 2.413) (xy -1.016 1.651)
				)
				(stroke
					(width 0)
					(type solid)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "Audio-Jack-2_Switch_1_1_1"
			(pin passive line
				(at -5.08 -2.54 90)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "S"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 5.08 2.54 180)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "T"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 5.08 0 180)
				(length 2.54)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "R"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CC1206_100NF_50V_10%_X7R"
		(pin_names
			(offset 1.27)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "C"
			(at 3.81 3.048 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "CC1206_100NF_50V_10%_X7R"
			(at 0 -6.223 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "Capacitors SMD:CAPC3216X140N"
			(at 0 -8.128 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Datasheet" "\\\\cern.ch\\dfs\\Applications\\Altium\\Datasheets\\CC1206_X7R_VISHAY_VJ.pdf"
			(at 0 -10.033 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Description" "100nF"
			(at 3.81 -3.048 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Part Number" "CC1206_100NF_50V_10%_X7R"
			(at 0 -11.938 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Ref" "Capacitor - non polarized"
			(at 0 -13.843 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Path" "SchLib\\Capacitors.SchLib"
			(at 0 -15.748 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Comment" "100nF"
			(at 0 -17.653 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Component Kind" "Standard"
			(at 0 -19.558 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Component Type" "Standard"
			(at 0 -21.463 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Pin Count" "2"
			(at 0 -23.368 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Path" "PcbLib\\Capacitors SMD.PcbLib"
			(at 0 -25.273 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Ref" "CAPC3216X140N"
			(at 0 -27.178 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "PackageDescription" " "
			(at 0 -29.083 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Status" "Preferred"
			(at 0 -30.988 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Status Comment" " "
			(at 0 -32.893 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Voltage" "50V"
			(at 0 -34.798 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "TC" "X7R"
			(at 0 -36.703 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Tolerance" "±10%"
			(at 0 -38.608 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Part Description" "SMD Multilayer Chip Ceramic Capacitor"
			(at 0 -40.513 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer" "GENERIC"
			(at 0 -42.418 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer Part Number" "CC1206_100NF_50V_10%_X7R"
			(at 0 -44.323 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Case" "1206"
			(at 0 -46.228 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Mounted" "Yes"
			(at 0 -48.133 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Socket" "No"
			(at 0 -50.038 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "SMD" "Yes"
			(at 0 -51.943 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "PressFit" " "
			(at 0 -53.848 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sense" "No"
			(at 0 -55.753 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sense Comment" " "
			(at 0 -57.658 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ComponentHeight" " "
			(at 0 -59.563 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer1 Example" "VISHAY VITRAMON"
			(at 0 -61.468 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer1 Part Number" "VJ1206Y104KXAAT"
			(at 0 -63.373 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer1 ComponentHeight" "1.7mm"
			(at 0 -65.278 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "HelpURL" "\\\\cern.ch\\dfs\\Applications\\Altium\\Datasheets\\CC1206_X7R_VISHAY_VJ.pdf"
			(at 0 -67.183 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Author" "CERN DEM JLC"
			(at 0 -69.088 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "CreateDate" "12/03/07 00:00:00"
			(at 0 -70.993 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "LatestRevisionDate" "12/03/07 00:00:00"
			(at 0 -72.898 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Database Table Name" "Capacitors"
			(at 0 -74.803 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Name" "Capacitors SMD"
			(at 0 -76.708 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Library" "Capacitors SMD"
			(at 0 -78.613 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "License" "This work is licensed under the Creative Commons CC-BY-SA 4.0 License. To the extent that circuit schematics that use Licensed Material can be considered to be ‘Adapted Material’, then the copyright holder waives article 3.b of the license with respect to these schematics."
			(at 0 -80.518 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ki_locked" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(symbol "CC1206_100NF_50V_10%_X7R_0_1"
			(polyline
				(pts
					(xy 2.54 0) (xy 3.302 0)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.302 -2.032) (xy 3.302 2.032)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 4.318 2.032) (xy 4.318 -2.032)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 0) (xy 4.318 0)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at 0 0 0)
				(length 2.54)
				(name "1"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 0 180)
				(length 2.54)
				(name "2"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CLIFF_FC68148(DC-10A)"
		(pin_names
			(offset 1.27)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at 3.81 1.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "CLIFF_FC68148(DC-10A)"
			(at 0 -12.065 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "Miscellaneous THD:CLIFF_FC68148(DC-10A)"
			(at 0 -13.97 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Datasheet" "\\\\cern.ch\\dfs\\Applications\\Altium\\Datasheets\\CLIFF_FC68148(DC-10A).pdf"
			(at 0 -15.875 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Description" "Jack"
			(at 3.81 -0.762 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Part Number" "CLIFF_FC68148(DC-10A)"
			(at 0 -17.78 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Ref" "CLIFF_FC68148(DC-10A)"
			(at 0 -19.685 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Path" "SchLib\\Connectors.SchLib"
			(at 0 -21.59 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Comment" " "
			(at 0 -23.495 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Component Kind" "Standard"
			(at 0 -25.4 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Component Type" "Standard"
			(at 0 -27.305 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Device" " "
			(at 0 -29.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "PackageDescription" " "
			(at 0 -31.115 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Pin Count" "3"
			(at 0 -33.02 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Case" " "
			(at 0 -34.925 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Path" "PcbLib\\Miscellaneous THD.PcbLib"
			(at 0 -36.83 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Ref" "CLIFF_FC68148(DC-10A)"
			(at 0 -38.735 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Family" "Jack"
			(at 0 -40.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Mounted" "Yes"
			(at 0 -42.545 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Socket" "No"
			(at 0 -44.45 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "PressFit" "No"
			(at 0 -46.355 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "SMD" "No"
			(at 0 -48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sense" "No"
			(at 0 -50.165 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sense Comment" " "
			(at 0 -52.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Status Comment" " "
			(at 0 -53.975 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Status" "None"
			(at 0 -55.88 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "SCEM" " "
			(at 0 -57.785 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Part Description" "12V/5A, 2.1mm Diameter Pin, DC Power Socket with  Switch (DC10A Type)"
			(at 0 -59.69 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer" "CLIFF ELECTRONIC COMPONENTS"
			(at 0 -61.595 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer Part Number" "FC68148 (DC-10A)"
			(at 0 -63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ComponentHeight" "11mm"
			(at 0 -65.405 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "HelpURL" "\\\\cern.ch\\dfs\\Applications\\Altium\\Datasheets\\CLIFF_FC68148(DC-10A).pdf"
			(at 0 -67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ComponentLink1URL" " "
			(at 0 -69.215 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ComponentLink1Description" " "
			(at 0 -71.12 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ComponentLink2URL" " "
			(at 0 -73.025 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ComponentLink2Description" " "
			(at 0 -74.93 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Author" "CERN DEM JLC"
			(at 0 -76.835 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "CreateDate" "11/23/10 00:00:00"
			(at 0 -78.74 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "LatestRevisionDate" "11/23/10 00:00:00"
			(at 0 -80.645 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Database Table Name" "Connectors"
			(at 0 -82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Name" "Miscellaneous"
			(at 0 -84.455 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Library" "Miscellaneous THD"
			(at 0 -86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "License" "This work is licensed under the Creative Commons CC-BY-SA 4.0 License. To the extent that circuit schematics that use Licensed Material can be considered to be ‘Adapted Material’, then the copyright holder waives article 3.b of the license with respect to these schematics."
			(at 0 -88.265 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ki_locked" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(symbol "CLIFF_FC68148(DC-10A)_0_1"
			(polyline
				(pts
					(xy 0 -2.54) (xy 2.54 -2.54)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 -5.08) (xy 3.81 -5.08)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 -7.62) (xy 6.35 -7.62)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start 0 -10.16)
				(end 10.16 0)
				(stroke
					(width 0)
					(type solid)
				)
				(fill
					(type background)
				)
			)
			(polyline
				(pts
					(xy 2.54 -1.27) (xy 2.54 -3.81)
				)
				(stroke
					(width 0.762)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.81 -7.112) (xy 3.81 -5.08)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.81 -7.62) (xy 3.302 -6.604) (xy 4.318 -6.604) (xy 3.81 -7.62)
				)
				(stroke
					(width 0)
					(type solid)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy 6.35 -7.62) (xy 7.62 -6.35)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 7.62 -2.54) (xy 2.54 -2.54)
				)
				(stroke
					(width 0.762)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 8.89 -7.62) (xy 7.62 -6.35)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(pin bidirectional line
				(at -5.08 -2.54 0)
				(length 5.08)
				(name "1"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -5.08 -5.08 0)
				(length 5.08)
				(name "2"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -5.08 -7.62 0)
				(length 5.08)
				(name "3"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CTEB_2.2UF_35V_10%_254-500X840"
		(pin_names
			(offset 1.27)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "C"
			(at 3.81 3.048 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "CTEB_2.2UF_35V_10%_254-500X840"
			(at 0 -6.223 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "Capacitors THD:CAPPRB254-500X840"
			(at 0 -8.128 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Datasheet" "\\\\cern.ch\\dfs\\Applications\\Altium\\Datasheets\\CTEB_KEMET_T350.pdf"
			(at 0 -10.033 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Description" "2.2uF"
			(at 3.81 -3.048 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Part Number" "CTEB_2.2UF_35V_10%_254-500X840"
			(at 0 -11.938 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Ref" "Capacitor - polarized"
			(at 0 -13.843 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Path" "SchLib\\Capacitors.SchLib"
			(at 0 -15.748 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Comment" "2.2uF"
			(at 0 -17.653 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Component Kind" "Standard"
			(at 0 -19.558 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Component Type" "Standard"
			(at 0 -21.463 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "PackageDescription" " "
			(at 0 -23.368 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Pin Count" "2"
			(at 0 -25.273 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Path" "PcbLib\\Capacitors THD.PcbLib"
			(at 0 -27.178 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Ref" "CAPPRB254-500X840"
			(at 0 -29.083 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Status" "None"
			(at 0 -30.988 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Status Comment" " "
			(at 0 -32.893 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Voltage" "35V"
			(at 0 -34.798 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "TC" " "
			(at 0 -36.703 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Tolerance" "±10%"
			(at 0 -38.608 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Part Description" "Solid Tantalum Resin Dipped Capacitor"
			(at 0 -40.513 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer" "KEMET"
			(at 0 -42.418 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer Part Number" "T350C225K035AT"
			(at 0 -44.323 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Case" "LS2.54 D5 x H8.4"
			(at 0 -46.228 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Mounted" "Yes"
			(at 0 -48.133 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "PressFit" " "
			(at 0 -50.038 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Socket" "No"
			(at 0 -51.943 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "SMD" "No"
			(at 0 -53.848 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sense" "No"
			(at 0 -55.753 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sense Comment" " "
			(at 0 -57.658 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ComponentHeight" "8.4mm"
			(at 0 -59.563 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer1 Example" " "
			(at 0 -61.468 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer1 Part Number" " "
			(at 0 -63.373 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer1 ComponentHeight" " "
			(at 0 -65.278 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "HelpURL" "\\\\cern.ch\\dfs\\Applications\\Altium\\Datasheets\\CTEB_KEMET_T350.pdf"
			(at 0 -67.183 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Author" "CERN DEM JLC"
			(at 0 -69.088 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "CreateDate" "12/16/09 00:00:00"
			(at 0 -70.993 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "LatestRevisionDate" "12/16/09 00:00:00"
			(at 0 -72.898 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Database Table Name" "Capacitors"
			(at 0 -74.803 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Name" "Capacitors THD"
			(at 0 -76.708 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Library" "Capacitors THD"
			(at 0 -78.613 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "License" "This work is licensed under the Creative Commons CC-BY-SA 4.0 License. To the extent that circuit schematics that use Licensed Material can be considered to be ‘Adapted Material’, then the copyright holder waives article 3.b of the license with respect to these schematics."
			(at 0 -80.518 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ki_locked" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(symbol "CTEB_2.2UF_35V_10%_254-500X840_0_1"
			(polyline
				(pts
					(xy 2.032 0) (xy 3.048 0)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.048 2.032) (xy 3.048 -2.032)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 4.064 0) (xy 5.08 0)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start 4.6228 -2.032)
				(mid 4.2033 -1.0545)
				(end 4.064 0)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start 4.064 0)
				(mid 4.1926 1.0523)
				(end 4.5974 2.032)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(text "+"
				(at 2.54 0.762 900)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left bottom)
					(hide yes)
				)
			)
			(pin passive line
				(at 0 0 0)
				(length 2.54)
				(name "1"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 0 180)
				(length 2.54)
				(name "2"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "GND"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -6.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "GROUND power-flag symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "Power Flag Symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "GND_0_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
				)
				(stroke
					(width 0)
					(type solid)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "GND_1_1"
			(pin power_in line
				(at 0 0 270)
				(length 0)
				(hide yes)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "POT"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "RV"
			(at -4.445 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "POT"
			(at -2.54 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Potentiometer"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "resistor variable"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Potentiometer*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "POT_0_1"
			(rectangle
				(start 1.016 2.54)
				(end -1.016 -2.54)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.143 0) (xy 2.286 0.508) (xy 2.286 -0.508)
				)
				(stroke
					(width 0)
					(type solid)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy 2.54 0) (xy 1.524 0)
				)
				(stroke
					(width 0)
					(type solid)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "POT_1_1"
			(pin passive line
				(at 0 3.81 270)
				(length 1.27)
				(name "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 0 -3.81 90)
				(length 1.27)
				(name "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 3.81 0 180)
				(length 1.27)
				(name "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "R1206_10K_1%_0.25W_100PPM"
		(pin_names
			(offset 1.27)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "R"
			(at 3.81 1.778 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "R1206_10K_1%_0.25W_100PPM"
			(at 0 -4.953 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "Resistors SMD:RESC3216X65N"
			(at 0 -6.858 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Datasheet" "\\\\cern.ch\\dfs\\Applications\\Altium\\Datasheets\\R1206_NIC_NRC.pdf"
			(at 0 -8.763 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Description" "±1%"
			(at 3.81 0 0)
			(effects
				(font
					(size 0.635 0.635)
				)
			)
		)
		(property "Val" "10k"
			(at 3.81 -1.778 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Part Number" "R1206_10K_1%_0.25W_100PPM"
			(at 0 -10.668 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Ref" "Resistor - 1%"
			(at 0 -12.573 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Path" "SchLib\\Resistors.SchLib"
			(at 0 -14.478 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Comment" "10k"
			(at 0 -16.383 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Component Kind" "Standard"
			(at 0 -18.288 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Component Type" "Standard"
			(at 0 -20.193 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "PackageDescription" " "
			(at 0 -22.098 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Pin Count" "2"
			(at 0 -24.003 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Path" "PcbLib\\Resistors SMD.PcbLib"
			(at 0 -25.908 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Ref" "RESC3216X65N"
			(at 0 -27.813 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Status" "Preferred"
			(at 0 -29.718 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Power" "0.25W"
			(at 0 -31.623 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "TC" "±100ppm/°C"
			(at 0 -33.528 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Voltage" " "
			(at 0 -35.433 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Tolerance" "±1%"
			(at 0 -37.338 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Part Description" "General Purpose Thick Film Chip Resistor"
			(at 0 -39.243 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer" "GENERIC"
			(at 0 -41.148 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer Part Number" "R1206_10K_1%_0.25W_100PPM"
			(at 0 -43.053 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Case" "1206"
			(at 0 -44.958 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "PressFit" "No"
			(at 0 -46.863 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Mounted" "Yes"
			(at 0 -48.768 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sense Comment" " "
			(at 0 -50.673 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sense" "No"
			(at 0 -52.578 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Status Comment" " "
			(at 0 -54.483 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Socket" "No"
			(at 0 -56.388 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "SMD" "Yes"
			(at 0 -58.293 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ComponentHeight" " "
			(at 0 -60.198 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer1 Example" "NIC COMPONENT"
			(at 0 -62.103 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer1 Part Number" "NRC12F1002TRF"
			(at 0 -64.008 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer1 ComponentHeight" "0.65mm"
			(at 0 -65.913 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "HelpURL" "\\\\cern.ch\\dfs\\Applications\\Altium\\Datasheets\\R1206_NIC_NRC.pdf"
			(at 0 -67.818 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Author" "CERN DEM JLC"
			(at 0 -69.723 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "CreateDate" "12/03/07 00:00:00"
			(at 0 -71.628 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "LatestRevisionDate" "03/13/08 00:00:00"
			(at 0 -73.533 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Database Table Name" "Resistors"
			(at 0 -75.438 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Name" "Resistors SMD"
			(at 0 -77.343 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Library" "Resistors SMD"
			(at 0 -79.248 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "License" "This work is licensed under the Creative Commons CC-BY-SA 4.0 License. To the extent that circuit schematics that use Licensed Material can be considered to be ‘Adapted Material’, then the copyright holder waives article 3.b of the license with respect to these schematics."
			(at 0 -81.153 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ki_locked" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(symbol "R1206_10K_1%_0.25W_100PPM_0_1"
			(polyline
				(pts
					(xy 1.524 0.762) (xy 1.524 -0.762)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.524 -0.762) (xy 6.096 -0.762)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 6.096 0.762) (xy 1.524 0.762)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 6.096 -0.762) (xy 6.096 0.762)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at 0 0 0)
				(length 1.524)
				(name "1"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 0 180)
				(length 1.524)
				(name "2"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "TL072CD"
		(pin_names
			(offset 1.27)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "IC"
			(at 8.89 -1.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "TL072CD"
			(at 0 -18.923 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "ICs And Semiconductors SMD:SOIC127P600X175-8N"
			(at 0 -20.828 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Datasheet" "\\\\cern.ch\\dfs\\Applications\\Altium\\Datasheets\\TL072CD.pdf"
			(at 0 -22.733 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Description" "TL072CD"
			(at 0 -24.638 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Ref" "Operational Amplifier x2 Type1"
			(at 0 -26.543 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Path" "SchLib\\Operational Amplifiers.SchLib"
			(at 0 -28.448 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Comment" "TL072CD"
			(at 0 -30.353 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Component Kind" "Standard"
			(at 0 -32.258 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Component Type" "Standard"
			(at 0 -34.163 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Device" "TL072CD"
			(at 0 -36.068 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "PackageDescription" "SOIC 8, Pitch 1.27mm - Body 4x5mm, IPC Medium Density"
			(at 0 -37.973 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Status" " "
			(at 0 -39.878 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Part Description" "Dual Low-Noise JFET-Input Operational Amplifier"
			(at 0 -41.783 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer" "TEXAS INSTRUMENTS"
			(at 0 -43.688 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Manufacturer Part Number" "TL072CD"
			(at 0 -45.593 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Pin Count" "8"
			(at 0 -47.498 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Case" "SOIC8"
			(at 0 -49.403 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Mounted" "Yes"
			(at 0 -51.308 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Socket" "No"
			(at 0 -53.213 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "SMD" "Yes"
			(at 0 -55.118 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "PressFit" "No"
			(at 0 -57.023 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sense" "No"
			(at 0 -58.928 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sense Comment" " "
			(at 0 -60.833 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Bonding" "No"
			(at 0 -62.738 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Status Comment" " "
			(at 0 -64.643 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ComponentHeight" "1.75mm"
			(at 0 -66.548 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Path" "PcbLib\\ICs And Semiconductors SMD.PcbLib"
			(at 0 -68.453 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Ref" "SOIC127P600X175-8N"
			(at 0 -70.358 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Author" "CERN DEM JMW"
			(at 0 -72.263 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "HelpURL" "\\\\cern.ch\\dfs\\Applications\\Altium\\Datasheets\\TL072CD.pdf"
			(at 0 -74.168 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ComponentLink1URL" " "
			(at 0 -76.073 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ComponentLink2URL" " "
			(at 0 -77.978 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ComponentLink2Description" " "
			(at 0 -79.883 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ComponentLink1Description" " "
			(at 0 -81.788 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "CreateDate" "05/07/08 00:00:00"
			(at 0 -83.693 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "LatestRevisionDate" "05/07/08 00:00:00"
			(at 0 -85.598 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "SCEM" " "
			(at 0 -87.503 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "LatestRevisionDate1" " "
			(at 0 -89.408 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Field1" " "
			(at 0 -91.313 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Database Table Name" "ICs And Semiconductors"
			(at 0 -93.218 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Library Name" "Operational Amplifiers"
			(at 0 -95.123 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint Library" "ICs And Semiconductors SMD"
			(at 0 -97.028 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "License" "This work is licensed under the Creative Commons CC-BY-SA 4.0 License. To the extent that circuit schematics that use Licensed Material can be considered to be ‘Adapted Material’, then the copyright holder waives article 3.b of the license with respect to these schematics."
			(at 0 -98.933 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "ki_locked" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(symbol "TL072CD_0_1"
			(pin power_in line
				(at 5.08 5.08 270)
				(length 5.08)
				(name "V+"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at 5.08 -15.24 90)
				(length 5.08)
				(name "V-"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "TL072CD_0_2"
			(pin power_in line
				(at 5.08 5.08 270)
				(length 5.08)
				(name "V+"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at 5.08 -15.24 90)
				(length 5.08)
				(name "V-"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "TL072CD_1_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 -10.16)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 10.16 -5.08)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 -10.16) (xy 10.16 -5.08)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.016 -2.54) (xy 2.54 -2.54)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.016 -7.62) (xy 2.54 -7.62)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.778 -6.858) (xy 1.778 -8.382)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 0) (xy 5.08 -2.54)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 -7.62) (xy 5.08 -10.16)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(text "V+"
				(at 5.08 -1.27 900)
				(effects
					(font
						(size 1.016 1.016)
					)
					(justify left bottom)
					(hide yes)
				)
			)
			(text "V-"
				(at 5.08 -8.89 900)
				(effects
					(font
						(size 1.016 1.016)
					)
					(justify right bottom)
					(hide yes)
				)
			)
			(pin input line
				(at -5.08 -2.54 0)
				(length 5.08)
				(name "IN-"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -5.08 -7.62 0)
				(length 5.08)
				(name "IN+"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 15.24 -5.08 180)
				(length 5.08)
				(name "OUT"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "TL072CD_1_2"
			(polyline
				(pts
					(xy 0 0) (xy 0 -10.16)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 10.16 -5.08)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 -10.16) (xy 10.16 -5.08)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.016 -2.54) (xy 2.54 -2.54)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.016 -7.62) (xy 2.54 -7.62)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.778 -1.778) (xy 1.778 -3.302)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 0) (xy 5.08 -2.54)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 -7.62) (xy 5.08 -10.16)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(text "V+"
				(at 5.08 -1.27 900)
				(effects
					(font
						(size 1.016 1.016)
					)
					(justify left bottom)
					(hide yes)
				)
			)
			(text "V-"
				(at 5.08 -8.89 900)
				(effects
					(font
						(size 1.016 1.016)
					)
					(justify right bottom)
					(hide yes)
				)
			)
			(pin input line
				(at -5.08 -2.54 0)
				(length 5.08)
				(name "IN+"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -5.08 -7.62 0)
				(length 5.08)
				(name "IN-"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 15.24 -5.08 180)
				(length 5.08)
				(name "OUT"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "TL072CD_2_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 -10.16)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 10.16 -5.08)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 -10.16) (xy 10.16 -5.08)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.016 -2.54) (xy 2.54 -2.54)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.016 -7.62) (xy 2.54 -7.62)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.778 -6.858) (xy 1.778 -8.382)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 0) (xy 5.08 -2.54)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 -7.62) (xy 5.08 -10.16)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(text "V+"
				(at 5.08 -1.27 900)
				(effects
					(font
						(size 1.016 1.016)
					)
					(justify left bottom)
					(hide yes)
				)
			)
			(text "V-"
				(at 5.08 -8.89 900)
				(effects
					(font
						(size 1.016 1.016)
					)
					(justify right bottom)
					(hide yes)
				)
			)
			(pin input line
				(at -5.08 -2.54 0)
				(length 5.08)
				(name "IN-"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -5.08 -7.62 0)
				(length 5.08)
				(name "IN+"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 15.24 -5.08 180)
				(length 5.08)
				(name "OUT"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "TL072CD_2_2"
			(polyline
				(pts
					(xy 0 0) (xy 0 -10.16)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 10.16 -5.08)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 -10.16) (xy 10.16 -5.08)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.016 -2.54) (xy 2.54 -2.54)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.016 -7.62) (xy 2.54 -7.62)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.778 -1.778) (xy 1.778 -3.302)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 0) (xy 5.08 -2.54)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 -7.62) (xy 5.08 -10.16)
				)
				(stroke
					(width 0.254)
					(type solid)
				)
				(fill
					(type none)
				)
			)
			(text "V+"
				(at 5.08 -1.27 900)
				(effects
					(font
						(size 1.016 1.016)
					)
					(justify left bottom)
					(hide yes)
				)
			)
			(text "V-"
				(at 5.08 -8.89 900)
				(effects
					(font
						(size 1.016 1.016)
					)
					(justify right bottom)
					(hide yes)
				)
			)
			(pin input line
				(at -5.08 -2.54 0)
				(length 5.08)
				(name "IN+"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -5.08 -7.62 0)
				(length 5.08)
				(name "IN-"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 15.24 -5.08 180)
				(length 5.08)
				(name "OUT"
					(effects
						(font
							(size 0.0254 0.0254)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
)
