// Copyright (c) 2015-2021 Dr<PERSON> <PERSON> and <PERSON>
// Distributed under the Boost Software License, Version 1.0.
// (See accompanying file LICENSE_1_0.txt or copy at https://www.boost.org/LICENSE_1_0.txt)

#ifndef TAO_PEGTL_CONTRIB_ALPHABET_HPP
#define TAO_PEGTL_CONTRIB_ALPHABET_HPP

#include "../config.hpp"

namespace TAO_PEGTL_NAMESPACE::alphabet
{
   static const char a = 'a';
   static const char b = 'b';
   static const char c = 'c';
   static const char d = 'd';
   static const char e = 'e';
   static const char f = 'f';
   static const char g = 'g';
   static const char h = 'h';
   static const char i = 'i';
   static const char j = 'j';
   static const char k = 'k';
   static const char l = 'l';
   static const char m = 'm';
   static const char n = 'n';
   static const char o = 'o';
   static const char p = 'p';
   static const char q = 'q';
   static const char r = 'r';
   static const char s = 's';
   static const char t = 't';
   static const char u = 'u';
   static const char v = 'v';
   static const char w = 'w';
   static const char x = 'x';
   static const char y = 'y';
   static const char z = 'z';

   static const char A = 'A';  // NOLINT(readability-identifier-naming)
   static const char B = 'B';  // NOLINT(readability-identifier-naming)
   static const char C = 'C';  // NOLINT(readability-identifier-naming)
   static const char D = 'D';  // NOLINT(readability-identifier-naming)
   static const char E = 'E';  // NOLINT(readability-identifier-naming)
   static const char F = 'F';  // NOLINT(readability-identifier-naming)
   static const char G = 'G';  // NOLINT(readability-identifier-naming)
   static const char H = 'H';  // NOLINT(readability-identifier-naming)
   static const char I = 'I';  // NOLINT(readability-identifier-naming)
   static const char J = 'J';  // NOLINT(readability-identifier-naming)
   static const char K = 'K';  // NOLINT(readability-identifier-naming)
   static const char L = 'L';  // NOLINT(readability-identifier-naming)
   static const char M = 'M';  // NOLINT(readability-identifier-naming)
   static const char N = 'N';  // NOLINT(readability-identifier-naming)
   static const char O = 'O';  // NOLINT(readability-identifier-naming)
   static const char P = 'P';  // NOLINT(readability-identifier-naming)
   static const char Q = 'Q';  // NOLINT(readability-identifier-naming)
   static const char R = 'R';  // NOLINT(readability-identifier-naming)
   static const char S = 'S';  // NOLINT(readability-identifier-naming)
   static const char T = 'T';  // NOLINT(readability-identifier-naming)
   static const char U = 'U';  // NOLINT(readability-identifier-naming)
   static const char V = 'V';  // NOLINT(readability-identifier-naming)
   static const char W = 'W';  // NOLINT(readability-identifier-naming)
   static const char X = 'X';  // NOLINT(readability-identifier-naming)
   static const char Y = 'Y';  // NOLINT(readability-identifier-naming)
   static const char Z = 'Z';  // NOLINT(readability-identifier-naming)

}  // namespace TAO_PEGTL_NAMESPACE::alphabet

#endif
