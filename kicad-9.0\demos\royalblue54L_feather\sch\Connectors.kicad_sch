(kicad_sch
	(version 20250114)
	(generator "eeschema")
	(generator_version "9.0")
	(uuid "53a33a29-bf08-48c3-baa5-881fd42556d8")
	(paper "A4")
	(lib_symbols
		(symbol "Connector:Conn_01x12_Pin"
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "J"
				(at 0 15.24 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "Conn_01x12_Pin"
				(at 0 -17.78 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Generic connector, single row, 01x12, script generated"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_locked" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "ki_keywords" "connector"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Connector*:*_1x??_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Conn_01x12_Pin_1_1"
				(rectangle
					(start 0.8636 12.827)
					(end 0 12.573)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 10.287)
					(end 0 10.033)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 7.747)
					(end 0 7.493)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 5.207)
					(end 0 4.953)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 2.667)
					(end 0 2.413)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 0.127)
					(end 0 -0.127)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -2.413)
					(end 0 -2.667)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -4.953)
					(end 0 -5.207)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -7.493)
					(end 0 -7.747)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -10.033)
					(end 0 -10.287)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -12.573)
					(end 0 -12.827)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -15.113)
					(end 0 -15.367)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 1.27 12.7) (xy 0.8636 12.7)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 10.16) (xy 0.8636 10.16)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 7.62) (xy 0.8636 7.62)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 5.08) (xy 0.8636 5.08)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 2.54) (xy 0.8636 2.54)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 0) (xy 0.8636 0)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -2.54) (xy 0.8636 -2.54)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -5.08) (xy 0.8636 -5.08)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -7.62) (xy 0.8636 -7.62)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -10.16) (xy 0.8636 -10.16)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -12.7) (xy 0.8636 -12.7)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -15.24) (xy 0.8636 -15.24)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(pin passive line
					(at 5.08 12.7 180)
					(length 3.81)
					(name "Pin_1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 10.16 180)
					(length 3.81)
					(name "Pin_2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 7.62 180)
					(length 3.81)
					(name "Pin_3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 5.08 180)
					(length 3.81)
					(name "Pin_4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 2.54 180)
					(length 3.81)
					(name "Pin_5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 0 180)
					(length 3.81)
					(name "Pin_6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -2.54 180)
					(length 3.81)
					(name "Pin_7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -5.08 180)
					(length 3.81)
					(name "Pin_8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -7.62 180)
					(length 3.81)
					(name "Pin_9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -10.16 180)
					(length 3.81)
					(name "Pin_10"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "10"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -12.7 180)
					(length 3.81)
					(name "Pin_11"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "11"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -15.24 180)
					(length 3.81)
					(name "Pin_12"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "12"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Connector:Conn_01x16_Pin"
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "J"
				(at 0 20.32 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "Conn_01x16_Pin"
				(at 0 -22.86 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Generic connector, single row, 01x16, script generated"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_locked" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "ki_keywords" "connector"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Connector*:*_1x??_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Conn_01x16_Pin_1_1"
				(rectangle
					(start 0.8636 17.907)
					(end 0 17.653)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 15.367)
					(end 0 15.113)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 12.827)
					(end 0 12.573)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 10.287)
					(end 0 10.033)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 7.747)
					(end 0 7.493)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 5.207)
					(end 0 4.953)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 2.667)
					(end 0 2.413)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 0.127)
					(end 0 -0.127)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -2.413)
					(end 0 -2.667)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -4.953)
					(end 0 -5.207)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -7.493)
					(end 0 -7.747)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -10.033)
					(end 0 -10.287)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -12.573)
					(end 0 -12.827)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -15.113)
					(end 0 -15.367)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -17.653)
					(end 0 -17.907)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -20.193)
					(end 0 -20.447)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 1.27 17.78) (xy 0.8636 17.78)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 15.24) (xy 0.8636 15.24)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 12.7) (xy 0.8636 12.7)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 10.16) (xy 0.8636 10.16)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 7.62) (xy 0.8636 7.62)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 5.08) (xy 0.8636 5.08)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 2.54) (xy 0.8636 2.54)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 0) (xy 0.8636 0)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -2.54) (xy 0.8636 -2.54)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -5.08) (xy 0.8636 -5.08)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -7.62) (xy 0.8636 -7.62)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -10.16) (xy 0.8636 -10.16)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -12.7) (xy 0.8636 -12.7)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -15.24) (xy 0.8636 -15.24)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -17.78) (xy 0.8636 -17.78)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -20.32) (xy 0.8636 -20.32)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(pin passive line
					(at 5.08 17.78 180)
					(length 3.81)
					(name "Pin_1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 15.24 180)
					(length 3.81)
					(name "Pin_2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 12.7 180)
					(length 3.81)
					(name "Pin_3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 10.16 180)
					(length 3.81)
					(name "Pin_4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 7.62 180)
					(length 3.81)
					(name "Pin_5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 5.08 180)
					(length 3.81)
					(name "Pin_6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 2.54 180)
					(length 3.81)
					(name "Pin_7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 0 180)
					(length 3.81)
					(name "Pin_8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -2.54 180)
					(length 3.81)
					(name "Pin_9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -5.08 180)
					(length 3.81)
					(name "Pin_10"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "10"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -7.62 180)
					(length 3.81)
					(name "Pin_11"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "11"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -10.16 180)
					(length 3.81)
					(name "Pin_12"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "12"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -12.7 180)
					(length 3.81)
					(name "Pin_13"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "13"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -15.24 180)
					(length 3.81)
					(name "Pin_14"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "14"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -17.78 180)
					(length 3.81)
					(name "Pin_15"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "15"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -20.32 180)
					(length 3.81)
					(name "Pin_16"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "16"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Connector_Generic_MountingPin:Conn_01x02_MountingPin"
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "J"
				(at 0 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "Conn_01x02_MountingPin"
				(at 1.27 -5.08 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Generic connectable mounting pin connector, single row, 01x02, script generated (kicad-library-utils/schlib/autogen/connector/)"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "connector"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Connector*:*_1x??-1MP*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Conn_01x02_MountingPin_1_1"
				(rectangle
					(start -1.27 1.27)
					(end 1.27 -3.81)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(rectangle
					(start -1.27 0.127)
					(end 0 -0.127)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 -2.413)
					(end 0 -2.667)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.016 -4.572) (xy 1.016 -4.572)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "Mounting"
					(at 0 -4.191 0)
					(effects
						(font
							(size 0.381 0.381)
						)
					)
				)
				(pin passive line
					(at -5.08 0 0)
					(length 3.81)
					(name "Pin_1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 -2.54 0)
					(length 3.81)
					(name "Pin_2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -7.62 90)
					(length 3.048)
					(name "MountPin"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "MP"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Connector_Generic_MountingPin:Conn_01x04_MountingPin"
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "J"
				(at 0 5.08 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "Conn_01x04_MountingPin"
				(at 1.27 -7.62 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Generic connectable mounting pin connector, single row, 01x04, script generated (kicad-library-utils/schlib/autogen/connector/)"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "connector"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Connector*:*_1x??-1MP*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Conn_01x04_MountingPin_1_1"
				(rectangle
					(start -1.27 3.81)
					(end 1.27 -6.35)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(rectangle
					(start -1.27 2.667)
					(end 0 2.413)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 0.127)
					(end 0 -0.127)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 -2.413)
					(end 0 -2.667)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 -4.953)
					(end 0 -5.207)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.016 -7.112) (xy 1.016 -7.112)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "Mounting"
					(at 0 -6.731 0)
					(effects
						(font
							(size 0.381 0.381)
						)
					)
				)
				(pin passive line
					(at -5.08 2.54 0)
					(length 3.81)
					(name "Pin_1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 0 0)
					(length 3.81)
					(name "Pin_2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 -2.54 0)
					(length 3.81)
					(name "Pin_3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 -5.08 0)
					(length 3.81)
					(name "Pin_4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -10.16 90)
					(length 3.048)
					(name "MountPin"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "MP"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:+BATT"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "+BATT"
				(at 0 3.556 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"+BATT\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power battery"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "+BATT_0_1"
				(polyline
					(pts
						(xy -0.762 1.27) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 2.54) (xy 0.762 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "+BATT_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:GND"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "GND"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "GND_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "GND_1_1"
				(pin power_in line
					(at 0 0 270)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:VBUS"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "VBUS"
				(at 0 3.556 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"VBUS\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VBUS_0_1"
				(polyline
					(pts
						(xy -0.762 1.27) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 2.54) (xy 0.762 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "VBUS_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:VDD"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "VDD"
				(at 0 3.556 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"VDD\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VDD_0_1"
				(polyline
					(pts
						(xy -0.762 1.27) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 2.54) (xy 0.762 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "VDD_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
	)
	(rectangle
		(start 52.07 74.93)
		(end 162.56 130.81)
		(stroke
			(width 0)
			(type default)
		)
		(fill
			(type none)
		)
		(uuid 100af569-b5d6-4958-8e64-cf73d0d977a7)
	)
	(rectangle
		(start 196.85 74.93)
		(end 247.65 130.81)
		(stroke
			(width 0)
			(type default)
		)
		(fill
			(type none)
		)
		(uuid 99c61f43-1897-4e77-a9ba-5b336ffbdbf4)
	)
	(rectangle
		(start 162.56 74.93)
		(end 196.85 130.81)
		(stroke
			(width 0)
			(type default)
		)
		(fill
			(type none)
		)
		(uuid 9bbebfe3-ac5f-4100-a8f6-84d83aa82765)
	)
	(text "Qwiic Connector"
		(exclude_from_sim no)
		(at 198.12 80.01 0)
		(effects
			(font
				(size 2.54 2.54)
				(thickness 0.508)
				(bold yes)
			)
			(justify left bottom)
		)
		(uuid "272b015f-230b-4315-9ab1-0d9b3aeb06e6")
	)
	(text "Battery"
		(exclude_from_sim no)
		(at 163.83 80.01 0)
		(effects
			(font
				(size 2.54 2.54)
				(thickness 0.508)
				(bold yes)
			)
			(justify left bottom)
		)
		(uuid "38106d91-fc3b-4d93-97f2-fea93a464c65")
	)
	(text "Headers"
		(exclude_from_sim no)
		(at 53.34 80.01 0)
		(effects
			(font
				(size 2.54 2.54)
				(thickness 0.508)
				(bold yes)
			)
			(justify left bottom)
		)
		(uuid "fdf1bacc-04c2-493c-a795-b0ad97798d1a")
	)
	(junction
		(at 176.53 106.68)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "589d3d2d-4e66-4499-8536-edcbe57a1dce")
	)
	(no_connect
		(at 68.58 93.98)
		(uuid "3edc10e4-eac3-4713-9ba8-0afebf56fb17")
	)
	(bus_entry
		(at 144.78 116.84)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0136ee8e-c027-48e6-97f5-f2bb4e7a6ba7")
	)
	(bus_entry
		(at 88.9 109.22)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "113ca11c-768f-42b2-a922-6d988efaaec6")
	)
	(bus_entry
		(at 88.9 127)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "22a17ace-346f-4a98-a369-fc69baa65808")
	)
	(bus_entry
		(at 144.78 114.3)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "478293fd-3b0e-434a-aa17-a5a2c4b27c6a")
	)
	(bus_entry
		(at 144.78 106.68)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4842650c-4297-4331-a9a4-465132522d5a")
	)
	(bus_entry
		(at 88.9 106.68)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "54555368-c900-4e1f-84b9-b39e4010235d")
	)
	(bus_entry
		(at 88.9 101.6)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "574f7062-9cea-4025-854b-d68b9c9015df")
	)
	(bus_entry
		(at 88.9 114.3)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "57c319ad-8dfe-4616-9df3-1a545a5fd58f")
	)
	(bus_entry
		(at 144.78 111.76)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5c64037b-673b-4ce3-ab59-8165ad859b5f")
	)
	(bus_entry
		(at 144.78 101.6)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5f1a3ba6-8a99-4c4a-b937-49525b9f5d0b")
	)
	(bus_entry
		(at 144.78 99.06)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "76ac3914-d038-423b-b60b-2136ee9bc05c")
	)
	(bus_entry
		(at 88.9 116.84)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "7f426db6-005f-4323-9354-0bf27925cf12")
	)
	(bus_entry
		(at 88.9 111.76)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "880205c7-d748-4a8a-bac3-a05a1426185f")
	)
	(bus_entry
		(at 144.78 104.14)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a1d873dc-7035-4d5f-b169-47e52f627e44")
	)
	(bus_entry
		(at 88.9 104.14)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b1539351-f501-45fc-832c-f1d41f014a84")
	)
	(bus_entry
		(at 144.78 109.22)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b9fdc62c-4bad-47c2-81dc-a06dbd4528cf")
	)
	(bus_entry
		(at 88.9 124.46)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d4f93c2e-0b93-426a-9ee6-fdc86a73e8ae")
	)
	(bus_entry
		(at 144.78 119.38)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "dee95322-4e7d-4dbb-bf56-79c922519cbd")
	)
	(bus_entry
		(at 88.9 119.38)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f3ab1d1a-30c1-47b8-9bd4-4f41a43ba6f3")
	)
	(bus_entry
		(at 88.9 121.92)
		(size -2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f7cd65a3-ab48-4763-baf4-15945c813250")
	)
	(bus
		(pts
			(xy 88.9 116.84) (xy 88.9 119.38)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "007c47c3-0ba5-4b29-be67-b78b580d124d")
	)
	(bus
		(pts
			(xy 88.9 111.76) (xy 88.9 114.3)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "00eaf49f-bf65-4039-b24c-1d18e2073030")
	)
	(wire
		(pts
			(xy 68.58 124.46) (xy 86.36 124.46)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "015c3346-0da9-47c7-8df5-7fe32d4ab707")
	)
	(wire
		(pts
			(xy 120.65 96.52) (xy 142.24 96.52)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0f76b6b0-b370-4504-a549-b663358349cf")
	)
	(bus
		(pts
			(xy 144.78 106.68) (xy 144.78 109.22)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1f156b0e-3685-43a0-aa00-b371a3611411")
	)
	(wire
		(pts
			(xy 120.65 88.9) (xy 134.62 88.9)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "23bd7e1c-6292-4486-b41a-94848aab1b7d")
	)
	(bus
		(pts
			(xy 144.78 101.6) (xy 144.78 104.14)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "28276a14-efd3-4367-9a54-715996cde916")
	)
	(wire
		(pts
			(xy 68.58 111.76) (xy 86.36 111.76)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2a23a428-d26d-474c-92d8-7566efea4665")
	)
	(bus
		(pts
			(xy 144.78 116.84) (xy 144.78 119.38)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2c798020-e6d4-4e02-86be-b6ecb55b0829")
	)
	(wire
		(pts
			(xy 120.65 99.06) (xy 142.24 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "3473f5a9-5b1e-44d0-b79c-2051096ce6ab")
	)
	(wire
		(pts
			(xy 68.58 121.92) (xy 86.36 121.92)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "38ddeabe-c32b-4286-aa59-d2a4fbce86fb")
	)
	(bus
		(pts
			(xy 88.9 106.68) (xy 88.9 109.22)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4059485e-77cd-4c3d-9836-67f7ed539bb6")
	)
	(wire
		(pts
			(xy 238.76 106.68) (xy 234.95 106.68)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "45bd828c-374d-4188-a2ce-a7fc085f5434")
	)
	(wire
		(pts
			(xy 120.65 101.6) (xy 142.24 101.6)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "487b3672-39fa-4e1c-a3ae-cdeb7b43a7a0")
	)
	(bus
		(pts
			(xy 88.9 101.6) (xy 88.9 104.14)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4c082dee-f38a-4afa-96b3-83ce03718285")
	)
	(wire
		(pts
			(xy 68.58 119.38) (xy 86.36 119.38)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4d1893a2-1110-4598-8924-e8fc6e577040")
	)
	(wire
		(pts
			(xy 72.39 88.9) (xy 68.58 88.9)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4fd58ab2-a740-4c7b-a242-5c8802b6f0b1")
	)
	(wire
		(pts
			(xy 215.9 99.06) (xy 243.84 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5846e272-75f7-4768-b5f4-5c651c28aaf7")
	)
	(wire
		(pts
			(xy 120.65 93.98) (xy 138.43 93.98)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "584e44f6-d861-4765-91a4-ca7403cf9991")
	)
	(wire
		(pts
			(xy 234.95 106.68) (xy 234.95 101.6)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5f53ccac-b41e-49e1-8d6a-ca4c919f0ccc")
	)
	(bus
		(pts
			(xy 88.9 104.14) (xy 88.9 106.68)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "64ca5279-a66a-4a90-aee6-3bf3f35dc500")
	)
	(wire
		(pts
			(xy 72.39 96.52) (xy 68.58 96.52)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6674e2ab-c6a8-4d98-bb0b-e34fa97a13cf")
	)
	(wire
		(pts
			(xy 181.61 101.6) (xy 181.61 106.68)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "66a123ce-f263-44f5-883a-a26e4a6ed038")
	)
	(wire
		(pts
			(xy 72.39 127) (xy 68.58 127)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6b986ce6-e9e8-43a0-b997-1059b8fca30c")
	)
	(wire
		(pts
			(xy 68.58 109.22) (xy 86.36 109.22)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6bb4377c-29c2-4ed3-9a7a-47d8d01f0075")
	)
	(wire
		(pts
			(xy 120.65 104.14) (xy 142.24 104.14)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "763045a9-0a3d-432e-bca3-a65dda3c4dc7")
	)
	(wire
		(pts
			(xy 215.9 101.6) (xy 234.95 101.6)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "78e7ca5c-9714-4e0f-865b-a02d4acf9cf0")
	)
	(wire
		(pts
			(xy 124.46 91.44) (xy 120.65 91.44)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "7fa567e6-37eb-4c98-8b96-be956573232e")
	)
	(wire
		(pts
			(xy 68.58 101.6) (xy 86.36 101.6)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "80067939-d309-4004-9bb7-4d2b190ae146")
	)
	(bus
		(pts
			(xy 144.78 111.76) (xy 144.78 114.3)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "81e8b80b-23da-45f9-83e6-7dca1d4f15c7")
	)
	(bus
		(pts
			(xy 144.78 99.06) (xy 144.78 101.6)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "82499360-91cc-4af4-9c49-a9a87c79b5ed")
	)
	(bus
		(pts
			(xy 88.9 109.22) (xy 88.9 111.76)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "85361dcc-4093-42e2-96d1-edd5582017ec")
	)
	(wire
		(pts
			(xy 68.58 116.84) (xy 86.36 116.84)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "8711c62b-486e-4fdb-869c-9d819b7e4c3c")
	)
	(wire
		(pts
			(xy 72.39 92.71) (xy 72.39 96.52)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "8a80f48c-877d-4f2d-bd65-46e3e891a8ea")
	)
	(wire
		(pts
			(xy 181.61 106.68) (xy 176.53 106.68)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9034cf00-fa34-4c19-a919-1710e151f2c7")
	)
	(wire
		(pts
			(xy 120.65 106.68) (xy 142.24 106.68)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "988fba36-e808-4b0d-a434-1f83f17d3367")
	)
	(bus
		(pts
			(xy 144.78 104.14) (xy 144.78 106.68)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9d6e7330-82b0-4647-90f6-4f7456a9f160")
	)
	(wire
		(pts
			(xy 68.58 114.3) (xy 86.36 114.3)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9e29ed0a-5786-4ec7-9a87-febdf0a4e985")
	)
	(wire
		(pts
			(xy 68.58 104.14) (xy 86.36 104.14)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a9517477-ac82-4c72-aabf-991972c320eb")
	)
	(wire
		(pts
			(xy 120.65 114.3) (xy 142.24 114.3)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "af7f2380-06b4-48d9-9fb4-f08d59a23cb7")
	)
	(wire
		(pts
			(xy 181.61 99.06) (xy 191.77 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "bf407ef2-dee7-4fc9-8d3c-298c16050c27")
	)
	(bus
		(pts
			(xy 88.9 119.38) (xy 88.9 121.92)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "bf846078-dc08-4bfa-a662-3c60d04069e9")
	)
	(bus
		(pts
			(xy 144.78 109.22) (xy 144.78 111.76)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "c24b4a4b-f416-43fd-83ee-fc369da19604")
	)
	(wire
		(pts
			(xy 120.65 116.84) (xy 142.24 116.84)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cbfd8041-8fb8-4a8c-88f8-0757f80a659a")
	)
	(wire
		(pts
			(xy 120.65 109.22) (xy 142.24 109.22)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cdc8d025-5a14-4b8c-988c-e08da5194585")
	)
	(wire
		(pts
			(xy 120.65 111.76) (xy 142.24 111.76)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "df86e715-9621-4301-b02e-22422a6b0886")
	)
	(wire
		(pts
			(xy 68.58 99.06) (xy 86.36 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e517d71a-be1c-4174-8a61-c42fc545a14a")
	)
	(wire
		(pts
			(xy 72.39 92.71) (xy 86.36 92.71)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e6d9a064-6138-471a-9f0f-39f575d6213c")
	)
	(bus
		(pts
			(xy 88.9 124.46) (xy 88.9 127)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "fc431dd1-9d33-428a-8932-453d7770603a")
	)
	(wire
		(pts
			(xy 68.58 91.44) (xy 86.36 91.44)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "fcc7f8cb-7c66-4d90-b3bd-50916884983e")
	)
	(wire
		(pts
			(xy 68.58 106.68) (xy 86.36 106.68)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ff9b59b2-0607-4d88-b78b-cf510585b428")
	)
	(label "DIG.D5"
		(at 124.46 111.76 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "0b14e27b-666c-4d52-8350-6736bdaf21ca")
	)
	(label "DIG.D10"
		(at 124.46 104.14 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "0db5d55f-5b33-4b04-aeb9-bf7d120ede81")
	)
	(label "ANALOG.A2"
		(at 72.39 104.14 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "0f6f9640-8f5a-413f-a86f-b2128d126ebf")
	)
	(label "UART.RX"
		(at 72.39 121.92 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "1a35d9f3-ae54-46d2-973e-642853fe30b5")
	)
	(label "DIG.D11"
		(at 124.46 101.6 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "1bc6907b-3234-4bb7-ad46-edefcb24d3bf")
	)
	(label "I2C.SCL"
		(at 124.46 114.3 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "3180cf1d-5770-4026-b496-338a99172aca")
	)
	(label "I2C.SDA"
		(at 215.9 104.14 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "3b1e704d-e93c-4eed-bee1-a6676224f857")
	)
	(label "DIG.D12"
		(at 124.46 99.06 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "4d90bb7a-c791-4855-a01c-2d569320d40e")
	)
	(label "DIG.D6"
		(at 124.46 109.22 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "5e9cea0e-6e60-41f4-93df-436d5c7779ff")
	)
	(label "SPI.MOSI"
		(at 72.39 116.84 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "6e835533-aeba-43fd-a15d-6db2e1a4c328")
	)
	(label "ANALOG.A4"
		(at 72.39 109.22 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "723d34c5-5969-41e5-baac-a2950658bb7a")
	)
	(label "DIG.D13"
		(at 124.46 96.52 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "7c616473-580d-41ff-bb92-916d93b24668")
	)
	(label "SPI.SCK"
		(at 72.39 114.3 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "99e34ce2-405f-4074-96d5-fab3fd77c631")
	)
	(label "I2C.SCL"
		(at 215.9 106.68 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "9f04b341-5e4e-4ddb-a070-05ad5d21bbaa")
	)
	(label "DIG.D9"
		(at 124.46 106.68 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "9fb10010-493a-4caa-89f7-f1c90e44647b")
	)
	(label "ANALOG.A3"
		(at 72.39 106.68 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "c4d11c32-ccf4-4c57-95a6-196bbe8c96ce")
	)
	(label "I2C.SDA"
		(at 124.46 116.84 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "d32a0ede-5e7f-4e15-97c5-4138394112cc")
	)
	(label "ANALOG.A0"
		(at 72.39 99.06 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "dd7e8182-3330-4851-8ebc-64b61547507b")
	)
	(label "SPI.MISO"
		(at 72.39 119.38 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "dd9ebec4-a7c6-4f0b-8607-04f7123a5121")
	)
	(label "ANALOG.A5"
		(at 72.39 111.76 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "e5bdbc28-d3d1-4b0d-8c5b-c91ab542407e")
	)
	(label "UART.TX"
		(at 72.39 124.46 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "ef7ea261-e56e-4b58-b34d-fec8543b88ec")
	)
	(label "ANALOG.A1"
		(at 72.39 101.6 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "f8590fc8-4567-4898-b660-d8eed9d7b034")
	)
	(hierarchical_label "ANALOG{A[0..5]}"
		(shape input)
		(at 88.9 114.3 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "1de8e45f-0ff4-4a5d-af0d-81ed09df0998")
	)
	(hierarchical_label "DIG{D5 D6 D[9..13]}"
		(shape input)
		(at 144.78 114.3 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "51862b86-f32c-4b89-8a44-d312f70e5230")
	)
	(hierarchical_label "ENABLE"
		(shape input)
		(at 124.46 91.44 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "58583fa2-5c68-4d3f-bc94-67417f5aaeee")
	)
	(hierarchical_label "~{RESET}"
		(shape input)
		(at 72.39 88.9 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "5f29d7bd-8d55-4fb7-b337-e07594fd6a62")
	)
	(hierarchical_label "I2C{SCL, SDA}"
		(shape input)
		(at 144.78 119.38 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "6328d6ea-21f8-499d-87a1-110cac767e86")
	)
	(hierarchical_label "SHPHLD"
		(shape input)
		(at 72.39 127 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "6c210cf8-c365-487e-8947-b5997f093987")
	)
	(hierarchical_label "SPI{SCK, MOSI, MISO}"
		(shape input)
		(at 88.9 121.92 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "98cbf1c5-11a7-408b-b14f-da245f3e7002")
	)
	(hierarchical_label "UART{TX, RX}"
		(shape input)
		(at 88.9 127 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "c41dec97-fc2b-4514-a081-0bd9f2dbd174")
	)
	(symbol
		(lib_id "power:+BATT")
		(at 191.77 99.06 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "14d69d7b-c7a7-4f87-9022-b2803ce92804")
		(property "Reference" "#PWR06"
			(at 191.77 102.87 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+BATT"
			(at 191.77 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 191.77 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 191.77 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"+BATT\""
			(at 191.77 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "7b9c46b7-a0f1-4b34-b17d-144f80375fbd")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/0a43c989-a862-4ff1-8fb5-5f7c93f777a1"
					(reference "#PWR06")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 210.82 111.76 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "20d18e22-f74b-4d2a-af34-009951dddc11")
		(property "Reference" "#PWR05"
			(at 210.82 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 210.82 116.84 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 210.82 111.76 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 210.82 111.76 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 210.82 111.76 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "18cba1f5-2774-4c16-8439-d55aae12d321")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/0a43c989-a862-4ff1-8fb5-5f7c93f777a1"
					(reference "#PWR05")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 243.84 99.06 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "32ee406e-1e7d-4a43-b0ae-c1089f2f04b7")
		(property "Reference" "#PWR02"
			(at 243.84 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 243.84 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 243.84 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 243.84 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 243.84 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "f7714c3e-9a80-417f-a368-e9ff9c9319f6")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/0a43c989-a862-4ff1-8fb5-5f7c93f777a1"
					(reference "#PWR02")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 176.53 106.68 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "4d514eb4-2b86-4313-bf4d-4bef80248320")
		(property "Reference" "#PWR04"
			(at 176.53 113.03 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 176.53 111.76 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 176.53 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 176.53 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 176.53 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "cdb945ca-4b4d-4fd1-abb2-07411c97d144")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/0a43c989-a862-4ff1-8fb5-5f7c93f777a1"
					(reference "#PWR04")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VBUS")
		(at 138.43 93.98 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "6e36d7b2-1385-4ab5-9a1b-241a6df2f8d6")
		(property "Reference" "#PWR048"
			(at 138.43 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VSYS"
			(at 138.43 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 138.43 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 138.43 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VBUS\""
			(at 138.43 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "f3238896-5c3b-41c9-a73d-352b8d67458b")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/0a43c989-a862-4ff1-8fb5-5f7c93f777a1"
					(reference "#PWR048")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector_Generic_MountingPin:Conn_01x04_MountingPin")
		(at 210.82 101.6 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "76d6e63b-255f-4236-8640-76c18fdd9d4e")
		(property "Reference" "J5"
			(at 210.82 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x04_MountingPin"
			(at 210.82 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Connector_JST:JST_SH_SM04B-SRSS-TB_1x04-1MP_P1.00mm_Horizontal"
			(at 210.82 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 210.82 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 210.82 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "LCSC" "C3029343"
			(at 210.82 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "MP"
			(uuid "2bfac5fd-2911-4cd8-a455-5aadff0176d0")
		)
		(pin "1"
			(uuid "c7b600ec-ffa4-4af9-9c04-4a8ce0aa54c5")
		)
		(pin "3"
			(uuid "*************-491d-97a4-fd55bf183bff")
		)
		(pin "4"
			(uuid "1c9995cb-de44-4855-9a56-47f6b10fd428")
		)
		(pin "2"
			(uuid "142b7007-75e7-40a8-8c28-b6a60884d597")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/0a43c989-a862-4ff1-8fb5-5f7c93f777a1"
					(reference "J5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:+BATT")
		(at 134.62 88.9 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "79043d78-3fb0-4d3e-8a8d-6e8ccfad1d74")
		(property "Reference" "#PWR09"
			(at 134.62 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+BATT"
			(at 134.62 83.82 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 134.62 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 134.62 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"+BATT\""
			(at 134.62 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "539dc263-9885-4e66-a521-2333be9b64f4")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/0a43c989-a862-4ff1-8fb5-5f7c93f777a1"
					(reference "#PWR09")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VDD")
		(at 238.76 106.68 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "866914cd-fb45-4fa8-964c-d16e104bd0e2")
		(property "Reference" "#PWR01"
			(at 238.76 110.49 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VDD"
			(at 238.76 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 238.76 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 238.76 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VDD\""
			(at 238.76 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "202d6535-3850-4be9-973f-b9d7e6c52a61")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/0a43c989-a862-4ff1-8fb5-5f7c93f777a1"
					(reference "#PWR01")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 86.36 92.71 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "94e3e61d-5e47-49ae-b9f2-2dabc30c7f50")
		(property "Reference" "#PWR08"
			(at 86.36 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 86.36 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 86.36 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 86.36 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 86.36 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "f9d3c4c1-2407-441c-8b82-1367a96c96a7")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/0a43c989-a862-4ff1-8fb5-5f7c93f777a1"
					(reference "#PWR08")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:Conn_01x12_Pin")
		(at 115.57 101.6 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom no)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "987eccd1-7a3f-45eb-94d6-47ac762a166c")
		(property "Reference" "J2"
			(at 116.205 83.82 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x12_Pin"
			(at 116.205 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "RoyalBlue54L-Feather-Connector_PinHeader_2.54mm:PinHeader_1x12_P2.54mm_Vertical"
			(at 115.57 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 115.57 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 115.57 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "9"
			(uuid "96d3c675-f26f-4ae3-a817-93f648ac7345")
		)
		(pin "6"
			(uuid "96d865af-e584-4aa7-985c-a548494d1b3b")
		)
		(pin "8"
			(uuid "eb1a082a-ab05-49ab-bc2c-c7c77423a813")
		)
		(pin "4"
			(uuid "d3f09570-87b8-421d-970a-920ac721aa66")
		)
		(pin "2"
			(uuid "897a256e-1499-45d1-b606-2358dcf230ab")
		)
		(pin "3"
			(uuid "0c8df9ca-a181-44f5-b722-1dfe2fb0bc8e")
		)
		(pin "7"
			(uuid "44374a31-2d48-4770-a9b6-edf95ecc0545")
		)
		(pin "1"
			(uuid "c0f65df1-65a4-4c29-beda-d58bb091bf0d")
		)
		(pin "10"
			(uuid "658ddaba-4534-4d44-8e63-b31d52c263bb")
		)
		(pin "5"
			(uuid "27655640-7ada-477b-a9fa-70f39739e9f0")
		)
		(pin "12"
			(uuid "7a216827-dd4d-468d-943f-38073598f539")
		)
		(pin "11"
			(uuid "fd37bbc9-0dfe-4b47-b6d4-d37599a23eee")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/0a43c989-a862-4ff1-8fb5-5f7c93f777a1"
					(reference "J2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:Conn_01x16_Pin")
		(at 63.5 106.68 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom no)
		(on_board yes)
		(dnp yes)
		(uuid "9a7e5117-7e04-44f3-ad23-c802d2e76ac2")
		(property "Reference" "J1"
			(at 64.135 83.82 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x16_Pin"
			(at 64.135 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "RoyalBlue54L-Feather-Connector_PinHeader_2.54mm:PinHeader_1x16_P2.54mm_Vertical"
			(at 63.5 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 63.5 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 63.5 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "4"
			(uuid "467c266b-107e-45a2-a552-6bd2c6a015ca")
		)
		(pin "2"
			(uuid "86afbb9f-c243-41d1-bc1d-1ff63f53f3e1")
		)
		(pin "7"
			(uuid "82c66c25-8af0-4b52-90ce-bfc3d9d33dbd")
		)
		(pin "5"
			(uuid "029abc29-03ee-4179-8d81-f183b5a4e4f8")
		)
		(pin "14"
			(uuid "985411e7-e25d-4265-bce9-5d2777b1ae1f")
		)
		(pin "3"
			(uuid "de619971-6f4e-4cd1-9722-21efee517353")
		)
		(pin "12"
			(uuid "92a673c1-b3ec-45ea-b275-bec2b29224c3")
		)
		(pin "10"
			(uuid "b4af4026-e63b-4006-9e44-97d6950949f0")
		)
		(pin "6"
			(uuid "213b395d-7fae-4097-bc89-7e82c90f6203")
		)
		(pin "8"
			(uuid "84eaa56d-c783-4c5a-b5cd-60eeedb8d503")
		)
		(pin "13"
			(uuid "98a19b55-7427-409d-8e61-5155ab0534a1")
		)
		(pin "11"
			(uuid "26b6c653-344b-4728-80d8-f26e7b8356f9")
		)
		(pin "1"
			(uuid "26635c26-a56f-44b2-85f2-bd576564239c")
		)
		(pin "15"
			(uuid "482f7697-2dcd-48fe-ae13-012f36825733")
		)
		(pin "9"
			(uuid "f6098cb3-d662-47f6-8b1f-ef4e2a337ed1")
		)
		(pin "16"
			(uuid "bd94fd21-ea3a-40ee-b997-9c8e930f30de")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/0a43c989-a862-4ff1-8fb5-5f7c93f777a1"
					(reference "J1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VDD")
		(at 86.36 91.44 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a2303ab3-29ba-4429-ae37-23a2ab12f04f")
		(property "Reference" "#PWR07"
			(at 86.36 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VDD"
			(at 86.36 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 86.36 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 86.36 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VDD\""
			(at 86.36 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "94d47378-62cb-4fb8-aa43-91315f16dbd9")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/0a43c989-a862-4ff1-8fb5-5f7c93f777a1"
					(reference "#PWR07")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector_Generic_MountingPin:Conn_01x02_MountingPin")
		(at 176.53 99.06 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "b7e88a6d-b8f6-4056-8811-5b3fa9cde1f2")
		(property "Reference" "J4"
			(at 176.53 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x02_MountingPin"
			(at 176.53 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Connector_JST:JST_PH_S2B-PH-SM4-TB_1x02-1MP_P2.00mm_Horizontal"
			(at 176.53 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 176.53 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 176.53 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "LCSC" "C295747"
			(at 176.53 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "MP"
			(uuid "44a2a955-8ad7-479e-b754-1ec7387ad35c")
		)
		(pin "2"
			(uuid "5fab6937-d5f8-4af4-8d5f-cdc98ea4c24e")
		)
		(pin "1"
			(uuid "8568685a-1a3f-433e-a5e8-aa1168fff8bd")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/0a43c989-a862-4ff1-8fb5-5f7c93f777a1"
					(reference "J4")
					(unit 1)
				)
			)
		)
	)
)
