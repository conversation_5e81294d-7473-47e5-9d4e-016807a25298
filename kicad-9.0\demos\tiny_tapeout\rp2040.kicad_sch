(kicad_sch
	(version 20250114)
	(generator "eeschema")
	(generator_version "9.0")
	(uuid "e51bfb35-e08d-4165-8625-c40f0ed9d061")
	(paper "A4")
	(title_block
		(title "RP2040 Basic Support")
		(date "2023-11-22")
		(rev "1.0.3")
		(company "Psychogenic Technologies")
		(comment 1 "See LICENSE.txt for details")
		(comment 2 "Licensed under Apache 2.0")
		(comment 3 "(C) 2023 Pat Deegan")
	)
	(lib_symbols
		(symbol "TinyTapeout:+3V3"
			(power)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "+3V3"
				(at 0 3.556 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"+3V3\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "+3V3_0_1"
				(polyline
					(pts
						(xy -0.762 1.27) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 2.54) (xy 0.762 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "+3V3_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(hide yes)
					(name "+3V3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "TinyTapeout:C_Small"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.254)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "C"
				(at 0.254 1.778 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "C_Small"
				(at 0.254 -2.032 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Unpolarized capacitor, small symbol"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "capacitor cap"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "C_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "C_Small_0_1"
				(polyline
					(pts
						(xy -1.524 0.508) (xy 1.524 0.508)
					)
					(stroke
						(width 0.3048)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.524 -0.508) (xy 1.524 -0.508)
					)
					(stroke
						(width 0.3302)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "C_Small_1_1"
				(pin passive line
					(at 0 2.54 270)
					(length 2.032)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -2.54 90)
					(length 2.032)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "TinyTapeout:Conn_01x02"
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "J"
				(at 0 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "Conn_01x02"
				(at 0 -5.08 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Generic connector, single row, 01x02, script generated (kicad-library-utils/schlib/autogen/connector/)"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "connector"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Connector*:*_1x??_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Conn_01x02_1_1"
				(rectangle
					(start -1.27 1.27)
					(end 1.27 -3.81)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(rectangle
					(start -1.27 0.127)
					(end 0 -0.127)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 -2.413)
					(end 0 -2.667)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(pin passive line
					(at -5.08 0 0)
					(length 3.81)
					(name "Pin_1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 -2.54 0)
					(length 3.81)
					(name "Pin_2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "TinyTapeout:Conn_01x06"
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "J"
				(at 0 7.62 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "Conn_01x06"
				(at 0 -10.16 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Generic connector, single row, 01x06, script generated (kicad-library-utils/schlib/autogen/connector/)"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "connector"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Connector*:*_1x??_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Conn_01x06_1_1"
				(rectangle
					(start -1.27 6.35)
					(end 1.27 -8.89)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(rectangle
					(start -1.27 5.207)
					(end 0 4.953)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 2.667)
					(end 0 2.413)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 0.127)
					(end 0 -0.127)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 -2.413)
					(end 0 -2.667)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 -4.953)
					(end 0 -5.207)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 -7.493)
					(end 0 -7.747)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(pin passive line
					(at -5.08 5.08 0)
					(length 3.81)
					(name "Pin_1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 2.54 0)
					(length 3.81)
					(name "Pin_2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 0 0)
					(length 3.81)
					(name "Pin_3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 -2.54 0)
					(length 3.81)
					(name "Pin_4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 -5.08 0)
					(length 3.81)
					(name "Pin_5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 -7.62 0)
					(length 3.81)
					(name "Pin_6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "TinyTapeout:Crystal_GND24"
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "Y"
				(at 3.175 5.08 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "Crystal_GND24"
				(at 3.175 3.175 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Four pin crystal, GND on pins 2 and 4"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "quartz ceramic resonator oscillator"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Crystal*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Crystal_GND24_0_1"
				(polyline
					(pts
						(xy -2.54 2.286) (xy -2.54 3.556) (xy 2.54 3.556) (xy 2.54 2.286)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -2.54 0) (xy -2.032 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -2.54 -2.286) (xy -2.54 -3.556) (xy 2.54 -3.556) (xy 2.54 -2.286)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -2.032 -1.27) (xy -2.032 1.27)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.143 2.54)
					(end 1.143 -2.54)
					(stroke
						(width 0.3048)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 3.556) (xy 0 3.81)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 -3.81) (xy 0 -3.556)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 2.032 0) (xy 2.54 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 2.032 -1.27) (xy 2.032 1.27)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "Crystal_GND24_1_1"
				(pin passive line
					(at -3.81 0 0)
					(length 1.27)
					(name "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 5.08 270)
					(length 1.27)
					(name "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 1.27)
					(name "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 3.81 0 180)
					(length 1.27)
					(name "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "TinyTapeout:GND"
			(power)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "GND"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "power-flag"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "GND_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "GND_1_1"
				(pin power_in line
					(at 0 0 270)
					(length 0)
					(hide yes)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "TinyTapeout:RP2040"
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at 17.78 45.72 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "RP2040"
				(at 17.78 43.18 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" "Package_DFN_QFN:QFN-56-1EP_7x7mm_P0.4mm_EP3.2x3.2mm"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "https://datasheets.raspberrypi.com/rp2040/rp2040-datasheet.pdf"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "A microcontroller by Raspberry Pi"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "RP2040 ARM Cortex-M0+ USB"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "QFN*1EP*7x7mm?P0.4mm*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "RP2040_0_1"
				(rectangle
					(start -21.59 41.91)
					(end 21.59 -41.91)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "RP2040_1_1"
				(pin input line
					(at -25.4 38.1 0)
					(length 3.81)
					(name "RUN"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "26"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -25.4 30.48 0)
					(length 3.81)
					(name "USB_DP"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "47"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -25.4 27.94 0)
					(length 3.81)
					(name "USB_DM"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "46"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -25.4 20.32 0)
					(length 3.81)
					(name "QSPI_SS"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "56"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -25.4 15.24 0)
					(length 3.81)
					(name "QSPI_SD0"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "53"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -25.4 12.7 0)
					(length 3.81)
					(name "QSPI_SD1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "55"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -25.4 10.16 0)
					(length 3.81)
					(name "QSPI_SD2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "54"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -25.4 7.62 0)
					(length 3.81)
					(name "QSPI_SD3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "51"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin output line
					(at -25.4 5.08 0)
					(length 3.81)
					(name "QSPI_SCLK"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "52"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at -25.4 -7.62 0)
					(length 3.81)
					(name "XIN"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "20"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -25.4 -17.78 0)
					(length 3.81)
					(name "XOUT"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "21"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at -25.4 -27.94 0)
					(length 3.81)
					(name "SWCLK"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "24"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -25.4 -30.48 0)
					(length 3.81)
					(name "SWD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "25"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at -25.4 -38.1 0)
					(length 3.81)
					(name "TESTEN"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "19"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_out line
					(at -5.08 45.72 270)
					(length 3.81)
					(name "VREG_VOUT"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "45"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at -2.54 45.72 270)
					(length 3.81)
					(name "DVDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "23"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -2.54 45.72 270)
					(length 3.81)
					(hide yes)
					(name "DVDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "50"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 0 -45.72 90)
					(length 3.81)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "57"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 2.54 45.72 270)
					(length 3.81)
					(name "IOVDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 45.72 270)
					(length 3.81)
					(hide yes)
					(name "IOVDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "10"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 45.72 270)
					(length 3.81)
					(hide yes)
					(name "IOVDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "22"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 45.72 270)
					(length 3.81)
					(hide yes)
					(name "IOVDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "33"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 45.72 270)
					(length 3.81)
					(hide yes)
					(name "IOVDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "42"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 45.72 270)
					(length 3.81)
					(hide yes)
					(name "IOVDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "49"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 5.08 45.72 270)
					(length 3.81)
					(name "USB_VDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "48"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 7.62 45.72 270)
					(length 3.81)
					(name "ADC_AVDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "43"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 10.16 45.72 270)
					(length 3.81)
					(name "VREG_IN"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "44"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 38.1 180)
					(length 3.81)
					(name "GPIO0"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 35.56 180)
					(length 3.81)
					(name "GPIO1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 33.02 180)
					(length 3.81)
					(name "GPIO2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 30.48 180)
					(length 3.81)
					(name "GPIO3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 27.94 180)
					(length 3.81)
					(name "GPIO4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 25.4 180)
					(length 3.81)
					(name "GPIO5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 22.86 180)
					(length 3.81)
					(name "GPIO6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 20.32 180)
					(length 3.81)
					(name "GPIO7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 17.78 180)
					(length 3.81)
					(name "GPIO8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "11"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 15.24 180)
					(length 3.81)
					(name "GPIO9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "12"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 12.7 180)
					(length 3.81)
					(name "GPIO10"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "13"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 10.16 180)
					(length 3.81)
					(name "GPIO11"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "14"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 7.62 180)
					(length 3.81)
					(name "GPIO12"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "15"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 5.08 180)
					(length 3.81)
					(name "GPIO13"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "16"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 2.54 180)
					(length 3.81)
					(name "GPIO14"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "17"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 0 180)
					(length 3.81)
					(name "GPIO15"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "18"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 -2.54 180)
					(length 3.81)
					(name "GPIO16"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "27"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 -5.08 180)
					(length 3.81)
					(name "GPIO17"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "28"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 -7.62 180)
					(length 3.81)
					(name "GPIO18"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "29"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 -10.16 180)
					(length 3.81)
					(name "GPIO19"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "30"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 -12.7 180)
					(length 3.81)
					(name "GPIO20"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "31"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 -15.24 180)
					(length 3.81)
					(name "GPIO21"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "32"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 -17.78 180)
					(length 3.81)
					(name "GPIO22"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "34"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 -20.32 180)
					(length 3.81)
					(name "GPIO23"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "35"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 -22.86 180)
					(length 3.81)
					(name "GPIO24"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "36"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 -25.4 180)
					(length 3.81)
					(name "GPIO25"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "37"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 -30.48 180)
					(length 3.81)
					(name "GPIO26_ADC0"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "38"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 -33.02 180)
					(length 3.81)
					(name "GPIO27_ADC1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "39"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 -35.56 180)
					(length 3.81)
					(name "GPIO28_ADC2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "40"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 25.4 -38.1 180)
					(length 3.81)
					(name "GPIO29_ADC3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "41"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "TinyTapeout:R_Small"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.254)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "R"
				(at 0.762 0.508 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "R_Small"
				(at 0.762 -1.016 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Resistor, small symbol"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "R resistor"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "R_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "R_Small_0_1"
				(rectangle
					(start -0.762 1.778)
					(end 0.762 -1.778)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "R_Small_1_1"
				(pin passive line
					(at 0 2.54 270)
					(length 0.762)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -2.54 90)
					(length 0.762)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "TinyTapeout:W25Q32JVSS"
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at -8.89 8.89 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "W25Q32JVSS"
				(at 7.62 8.89 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" "Package_SO:SOIC-8_5.23x5.23mm_P1.27mm"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "http://www.winbond.com/resource-files/w25q32jv%20revg%2003272018%20plus.pdf"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "32Mb Serial Flash Memory, Standard/Dual/Quad SPI, SOIC-8"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "flash memory SPI"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "SOIC*5.23x5.23mm*P1.27mm*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "W25Q32JVSS_0_1"
				(rectangle
					(start -10.16 7.62)
					(end 10.16 -7.62)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "W25Q32JVSS_1_1"
				(pin input line
					(at -12.7 2.54 0)
					(length 2.54)
					(name "~{CS}"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at -12.7 -2.54 0)
					(length 2.54)
					(name "CLK"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 0 10.16 270)
					(length 2.54)
					(name "VCC"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 0 -10.16 90)
					(length 2.54)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 12.7 5.08 180)
					(length 2.54)
					(name "DI(IO0)"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 12.7 2.54 180)
					(length 2.54)
					(name "DO(IO1)"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 12.7 -2.54 180)
					(length 2.54)
					(name "IO2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 12.7 -5.08 180)
					(length 2.54)
					(name "IO3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
	)
	(text "async reset"
		(exclude_from_sim no)
		(at 71.12 55.88 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "16babe8e-6bbe-4f2d-bc4e-5b21816d6676")
	)
	(text "RP2040 Basic Support"
		(exclude_from_sim no)
		(at 129.54 17.78 0)
		(effects
			(font
				(size 3 3)
				(thickness 0.6)
				(bold yes)
			)
			(justify left bottom)
		)
		(uuid "19b4eb58-ed96-4d28-93e4-b9d6743e7e2b")
	)
	(text "Flash"
		(exclude_from_sim no)
		(at 27.94 157.48 0)
		(effects
			(font
				(size 2 2)
				(thickness 0.4)
				(bold yes)
			)
			(justify left bottom)
		)
		(uuid "1fff7074-878a-4980-8b52-4655a0070d09")
	)
	(text "Short to hold in reset"
		(exclude_from_sim no)
		(at 22.86 53.34 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "364eef10-53d4-4b25-bdfa-a0773c637f99")
	)
	(text "VREG_VOUT: int core\nregulator, 1.1V\nCan supply DVDD\nPlace 1uF in/out \nbypass near pin."
		(exclude_from_sim no)
		(at 78.74 43.18 0)
		(effects
			(font
				(size 1 1)
			)
			(justify left bottom)
		)
		(uuid "41cd1e41-00da-4d48-afd4-259c839b6ed4")
	)
	(text "When held low on powerup, flash\nSS determines boot mode \n(HIGH == flash boot, LOW == USB \ndevice)"
		(exclude_from_sim no)
		(at 20.32 40.64 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "420217d2-1485-49fb-9da8-cef1a1c1a916")
	)
	(text "WE ************:\n CFPX-180 model\n 10 ppm tol, 20ppm stab\n CL 8pF"
		(exclude_from_sim no)
		(at 60.96 96.52 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "442674dc-8828-4616-bde3-c7226a48499c")
	)
	(text "1v1"
		(exclude_from_sim no)
		(at 99.06 45.72 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "5736c1b9-d49b-4574-a2bf-8aac12cb3238")
	)
	(text "USB_VDD supplies USB PHY, nominal 3v3. If IOVDD is 3v3, can share supply.\n\nIn fact, in this and many applications, IOVDD, USB_VDD and ADC_AVDD are\nall powered directly from a single 3v3 supply, with the 1v1 digital \ncore being handle by on-board regulator."
		(exclude_from_sim no)
		(at 68.58 30.48 0)
		(effects
			(font
				(size 1.1 1.1)
			)
			(justify left bottom)
		)
		(uuid "59333b66-26db-4aa2-80c6-6f1a9e1690a8")
	)
	(text "Flash program header\nNote: should we replace\n3v3 with RUN, to be able\nto reset/hold while updating\nflash?"
		(exclude_from_sim no)
		(at 15.24 99.06 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "59fc0cc0-0541-4ccf-99b3-2eb1686e4ec7")
	)
	(text "Supply bypass, place near\n1, 10, 22, 33, 42, 49"
		(exclude_from_sim no)
		(at 111.76 162.56 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "5be1e41c-2a97-4a51-b38e-e917ee8aaa7b")
	)
	(text "Quad SPI requires QE bit in \nstatus register-2 to be set.\nIn this case, ~{WP} becomes \nIO2 and ~{HOLD} becomes IO3."
		(exclude_from_sim no)
		(at 66.04 195.58 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "7ed1775c-4c98-4d65-80dc-744a506ea92c")
	)
	(text "Factory test\nmode: GND"
		(exclude_from_sim no)
		(at 83.82 137.16 0)
		(effects
			(font
				(size 1 1)
			)
			(justify left bottom)
		)
		(uuid "c46e28aa-55e9-4a4a-be5e-849f45ac0eea")
	)
	(text "Rule of thumb\n C1, C2 = 2 * CL - 2 * Cstray\nUsing a stray cap of 5pF, gives\nCn = 6pF\n\nInto:\n CL = (C1 * C2) / (C1 + C2) + Cstray\nThese Cn = 6p give\nCL = 8pF -- just what we need."
		(exclude_from_sim no)
		(at 30.48 132.08 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "c6023b1c-bb53-4a80-b2bc-b5350c588efc")
	)
	(text "Logic supply, nominally 3v3."
		(exclude_from_sim no)
		(at 22.86 27.94 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "cd6213b7-b15c-4b96-b1e9-8af889209bf6")
	)
	(text "Supply bypass,\nnear 23, 50"
		(exclude_from_sim no)
		(at 152.4 167.64 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "e635fa72-0c73-4130-b730-3e15cdc9dcf0")
	)
	(text "Note: SS pulled-up\nexternally, from \nbootmode switch"
		(exclude_from_sim no)
		(at 29.21 170.18 0)
		(effects
			(font
				(size 1 1)
			)
			(justify left bottom)
		)
		(uuid "ed85e011-494f-4e23-9a35-f7608b0c4738")
	)
	(junction
		(at 73.66 185.42)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "006df172-4479-4f8c-83e3-0cc7045ffed0")
	)
	(junction
		(at 106.68 43.18)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "106b42eb-69f8-45fe-98a9-f629189caecc")
	)
	(junction
		(at 67.31 182.88)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "1aaaa1be-6485-4557-98a0-e8e4b6f4f1fb")
	)
	(junction
		(at 149.86 170.18)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "1c5ff08e-a2cc-488c-98a5-365a8d4fd487")
	)
	(junction
		(at 80.01 175.26)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "27d14f3b-f9fa-4781-8823-2116de1fb8c1")
	)
	(junction
		(at 119.38 175.26)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "3bc639c5-cbb1-4136-b986-e39021a761a5")
	)
	(junction
		(at 119.38 43.18)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "412b1677-8d62-4a2e-85ae-2ace0c389b55")
	)
	(junction
		(at 119.38 165.1)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "5c65cc82-400b-4017-a326-d7130f78568b")
	)
	(junction
		(at 60.96 109.22)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "61f99254-67cc-428a-927a-2d17d9c3e7cd")
	)
	(junction
		(at 104.14 43.18)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "7addf5be-63fe-446f-8c31-a1becd65cee5")
	)
	(junction
		(at 60.96 99.06)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "9444bfee-73e7-4018-a2c4-1915262a579b")
	)
	(junction
		(at 50.8 167.64)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "9c57d792-ea1d-4cbc-9189-06f44ee948bf")
	)
	(junction
		(at 106.68 165.1)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "a34d68aa-de21-4546-9a14-e7e9bb38b369")
	)
	(junction
		(at 73.66 53.34)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "b08a1a82-f494-4282-a2d3-39d4b0a43075")
	)
	(junction
		(at 80.01 163.83)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "b6ed58cf-5444-49de-a73b-b19ecb364692")
	)
	(junction
		(at 73.66 163.83)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "c3c351c6-b9fa-49cd-b08c-7120b8ba3b49")
	)
	(junction
		(at 109.22 165.1)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "cb10bd08-d144-44e3-a84e-4ce0b47e509c")
	)
	(junction
		(at 109.22 175.26)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "db334d50-9f9e-473a-a28c-975adb78e8b3")
	)
	(junction
		(at 86.36 177.8)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "eaf71884-844c-4821-b5f7-740710a02adb")
	)
	(junction
		(at 71.12 163.83)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "eb832529-5e05-45c2-9615-6c74195d51d2")
	)
	(junction
		(at 50.8 109.22)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "fde8072e-6881-4eb5-a7a3-084f6997c3ed")
	)
	(wire
		(pts
			(xy 81.28 119.38) (xy 83.82 119.38)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "001c6273-453f-4837-80aa-7ecaa5f398aa")
	)
	(wire
		(pts
			(xy 73.66 86.36) (xy 83.82 86.36)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "02c9db0e-f86e-4ffa-bb48-1450da9468ad")
	)
	(wire
		(pts
			(xy 33.02 30.48) (xy 43.18 30.48)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "07f713a0-1879-476f-a63c-b4650d707646")
	)
	(wire
		(pts
			(xy 58.42 109.22) (xy 60.96 109.22)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0a6afaab-e166-4808-a1ab-2d6487feeb5f")
	)
	(wire
		(pts
			(xy 111.76 33.02) (xy 111.76 45.72)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0b69f3a7-1cea-4230-98b4-6acd481648c2")
	)
	(wire
		(pts
			(xy 30.48 22.86) (xy 38.1 22.86)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0cad4270-8f18-441c-bf6e-ad913f1483ca")
	)
	(wire
		(pts
			(xy 137.16 116.84) (xy 134.62 116.84)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0e807b81-a5d1-46fd-aec6-77da639bc1dc")
	)
	(wire
		(pts
			(xy 60.96 99.06) (xy 83.82 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "100a6a2d-fe15-47a5-8c3c-5af4031a51b3")
	)
	(wire
		(pts
			(xy 50.8 167.64) (xy 50.8 170.18)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "10a848dc-7105-472a-8e75-74c92feef503")
	)
	(wire
		(pts
			(xy 73.66 78.74) (xy 83.82 78.74)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "10dbdbe8-d46e-4112-bfad-838fdfd115d9")
	)
	(wire
		(pts
			(xy 25.4 73.66) (xy 35.56 73.66)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "11eb391e-db9c-4a9c-b967-9529178d6e67")
	)
	(wire
		(pts
			(xy 25.4 55.88) (xy 30.48 55.88)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "11f1bcb9-fba1-466a-a5d7-ed46c93d0d48")
	)
	(wire
		(pts
			(xy 83.82 109.22) (xy 76.2 109.22)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "13de631d-eeaa-41d2-a537-8842dee76835")
	)
	(wire
		(pts
			(xy 149.86 170.18) (xy 149.86 172.72)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "15042b07-7fe2-4ce2-a0a5-3ea0d38f0bae")
	)
	(wire
		(pts
			(xy 137.16 96.52) (xy 134.62 96.52)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1839cf92-ad2b-43bd-a02f-fe0c8e5fa37c")
	)
	(wire
		(pts
			(xy 137.16 81.28) (xy 134.62 81.28)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "187c32e0-b136-46c5-ad84-291031cb2ad3")
	)
	(wire
		(pts
			(xy 137.16 99.06) (xy 134.62 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1c755beb-3663-4df9-82cd-b59b3584bffe")
	)
	(wire
		(pts
			(xy 53.34 104.14) (xy 55.88 104.14)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1dd0dff3-28ce-4650-a09e-788ef702edfc")
	)
	(wire
		(pts
			(xy 104.14 43.18) (xy 106.68 43.18)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "20321f16-b5b0-4bc4-ab75-fcedca8da453")
	)
	(wire
		(pts
			(xy 60.96 100.33) (xy 60.96 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "21a3cd71-18f9-4dd8-a2a5-4aaa5e95ec2a")
	)
	(wire
		(pts
			(xy 50.8 99.06) (xy 50.8 109.22)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "223292b8-8dc8-45c1-9889-b75e175d4cc0")
	)
	(wire
		(pts
			(xy 25.4 68.58) (xy 35.56 68.58)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "258d6dd0-b15a-4c4e-b7ea-79056a629a93")
	)
	(wire
		(pts
			(xy 119.38 43.18) (xy 121.92 43.18)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "270399d3-874a-46bb-8e52-0d64bb3bea48")
	)
	(wire
		(pts
			(xy 73.66 81.28) (xy 83.82 81.28)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "29e7c6e5-f9e2-4ffb-b388-b96b1f36fa31")
	)
	(wire
		(pts
			(xy 137.16 104.14) (xy 134.62 104.14)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "29ebbd97-8c5e-4b94-9128-6ece996010b0")
	)
	(wire
		(pts
			(xy 83.82 129.54) (xy 81.28 129.54)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2f2a2a86-0fb1-47ae-a41e-8f2ed2e5d758")
	)
	(wire
		(pts
			(xy 80.01 170.18) (xy 80.01 175.26)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "31673040-c8b1-4411-8c2a-715dd74272d1")
	)
	(wire
		(pts
			(xy 25.4 172.72) (xy 25.4 182.88)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "344fc23a-e193-4da1-929a-da2ca5254dd0")
	)
	(wire
		(pts
			(xy 25.4 78.74) (xy 35.56 78.74)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "38be980b-3e07-440b-8082-217a04c3c009")
	)
	(wire
		(pts
			(xy 25.4 71.12) (xy 35.56 71.12)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "39047a7c-cbc9-4056-9c4f-b394e13c487b")
	)
	(wire
		(pts
			(xy 53.34 99.06) (xy 50.8 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "3ea8bf16-95b7-44f3-bce4-7733a0fdd8b2")
	)
	(wire
		(pts
			(xy 106.68 157.48) (xy 106.68 165.1)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "*************-495e-a970-1163bcb8a5fd")
	)
	(wire
		(pts
			(xy 80.01 163.83) (xy 86.36 163.83)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "425bd872-b8d2-43bb-8158-d2900fd67b4e")
	)
	(wire
		(pts
			(xy 137.16 127) (xy 134.62 127)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "42be4ee7-fbad-49eb-bd17-bacd31460099")
	)
	(wire
		(pts
			(xy 137.16 63.5) (xy 134.62 63.5)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4331e290-d3ef-46af-8d55-7a13c59f2665")
	)
	(wire
		(pts
			(xy 59.69 167.64) (xy 60.96 167.64)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4436276c-7003-4fbf-bafa-dc246c94160a")
	)
	(wire
		(pts
			(xy 119.38 175.26) (xy 129.54 175.26)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4b825fc1-a835-4b2a-a38d-3ec6fa12d389")
	)
	(wire
		(pts
			(xy 86.36 163.83) (xy 86.36 165.1)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "50a77488-67ab-4b4e-a94a-1a9358d037f2")
	)
	(wire
		(pts
			(xy 137.16 129.54) (xy 134.62 129.54)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "51208bde-acd5-4e9a-967e-a9af93a220eb")
	)
	(wire
		(pts
			(xy 73.66 71.12) (xy 83.82 71.12)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "518ac6c4-e8d0-4185-885b-8718778a814a")
	)
	(wire
		(pts
			(xy 25.4 81.28) (xy 27.94 81.28)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5263f1d5-e560-4cdb-8138-6f1d3a3033ac")
	)
	(wire
		(pts
			(xy 63.5 182.88) (xy 67.31 182.88)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "56b05dd4-a4d5-4dc8-902c-124aa1f5c1d2")
	)
	(wire
		(pts
			(xy 73.66 163.83) (xy 73.66 165.1)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "56fca5db-f8e6-400d-b158-77e6ef1c7002")
	)
	(wire
		(pts
			(xy 27.94 82.55) (xy 27.94 81.28)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "585f470f-1398-4bc4-bb91-549fd359bdcf")
	)
	(wire
		(pts
			(xy 137.16 66.04) (xy 134.62 66.04)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "58b29f41-4815-4490-987a-09e5b4434423")
	)
	(wire
		(pts
			(xy 137.16 91.44) (xy 134.62 91.44)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5aa8baba-10b1-499e-a521-26c0f954199d")
	)
	(wire
		(pts
			(xy 149.86 170.18) (xy 157.48 170.18)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5b53642b-17f0-41b9-8092-7922d350a92d")
	)
	(wire
		(pts
			(xy 137.16 73.66) (xy 134.62 73.66)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5d6ac871-c11c-4e54-9ac3-5c52721f6763")
	)
	(wire
		(pts
			(xy 137.16 93.98) (xy 134.62 93.98)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "602266eb-4eb0-4cc4-9c84-21002629ca75")
	)
	(wire
		(pts
			(xy 116.84 40.64) (xy 116.84 45.72)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "64db4f66-a67e-4391-b6b3-a2ffc5d58c3c")
	)
	(wire
		(pts
			(xy 80.01 163.83) (xy 80.01 165.1)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6637c759-0bbe-435a-86aa-bc5b584524fc")
	)
	(wire
		(pts
			(xy 106.68 175.26) (xy 109.22 175.26)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "671e84ff-1e50-4cab-aadf-d425c3ac6fcf")
	)
	(wire
		(pts
			(xy 104.14 43.18) (xy 99.06 43.18)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6a4de0e0-4cc2-4dcc-9306-ab5cf29702b6")
	)
	(wire
		(pts
			(xy 80.01 175.26) (xy 97.79 175.26)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6a5b823d-98a8-4413-b419-2efd15053491")
	)
	(wire
		(pts
			(xy 73.66 76.2) (xy 83.82 76.2)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6dc5294c-1263-4e6a-b476-c70146c04e21")
	)
	(wire
		(pts
			(xy 63.5 185.42) (xy 73.66 185.42)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6e618473-102f-4692-bfeb-f0f3fd419171")
	)
	(wire
		(pts
			(xy 67.31 182.88) (xy 67.31 170.18)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6f959353-ccef-40af-ae6e-6d30bc08b46f")
	)
	(wire
		(pts
			(xy 71.12 53.34) (xy 73.66 53.34)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6f96c424-d26a-4c43-a0dc-dcb77c214238")
	)
	(wire
		(pts
			(xy 73.66 163.83) (xy 80.01 163.83)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6fa77c07-da33-4511-98d0-ab41105d397e")
	)
	(wire
		(pts
			(xy 137.16 109.22) (xy 134.62 109.22)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "7220d654-b77d-4ea4-a1d4-34efb8fe85a0")
	)
	(wire
		(pts
			(xy 81.28 63.5) (xy 83.82 63.5)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "768b51a9-7ce1-4e93-88c0-c086a798eae6")
	)
	(wire
		(pts
			(xy 25.4 182.88) (xy 38.1 182.88)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "76ce7126-92dc-4a1d-a937-0476fada3304")
	)
	(wire
		(pts
			(xy 119.38 33.02) (xy 119.38 43.18)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "7b986c04-2f47-4b8e-a246-60ed9ad22364")
	)
	(wire
		(pts
			(xy 73.66 50.8) (xy 73.66 53.34)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "7d6c0786-d2d2-4c89-a674-94fc13580d54")
	)
	(wire
		(pts
			(xy 25.4 58.42) (xy 27.94 58.42)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "7d837e3a-1e1a-4b33-a5dd-40687c4749c2")
	)
	(wire
		(pts
			(xy 81.28 121.92) (xy 83.82 121.92)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "7f5fc2b6-103e-4c08-a9ef-47f7fe40a34a")
	)
	(wire
		(pts
			(xy 71.12 154.94) (xy 71.12 163.83)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "80d906e0-bb52-47ef-86f5-785c9eeda96b")
	)
	(wire
		(pts
			(xy 25.4 76.2) (xy 35.56 76.2)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "81d30887-8fbf-4fc9-b526-1e33f4c01039")
	)
	(wire
		(pts
			(xy 137.16 71.12) (xy 134.62 71.12)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "84128b1e-51f6-43dd-9af8-cf62d8e4065f")
	)
	(wire
		(pts
			(xy 137.16 55.88) (xy 134.62 55.88)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "841c21bc-0a5b-49df-9954-a09f6b55baa7")
	)
	(wire
		(pts
			(xy 137.16 83.82) (xy 134.62 83.82)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "8447ffe4-0d67-4089-af5d-70f002b1ce43")
	)
	(wire
		(pts
			(xy 104.14 45.72) (xy 104.14 43.18)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "8599b6de-cbbd-4e75-980e-6faa35ffecf2")
	)
	(wire
		(pts
			(xy 137.16 60.96) (xy 134.62 60.96)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "8630e9d5-d5a1-400b-bf9a-a963acf427f1")
	)
	(wire
		(pts
			(xy 27.94 177.8) (xy 38.1 177.8)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "87ecf63f-17fc-47a9-9f1f-cfd7263a385e")
	)
	(wire
		(pts
			(xy 106.68 43.18) (xy 106.68 45.72)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "907a8094-5d9e-4e41-a28b-d579f76d7952")
	)
	(wire
		(pts
			(xy 106.68 33.02) (xy 106.68 43.18)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "919273f7-7127-4ee3-a21a-e782b2002568")
	)
	(wire
		(pts
			(xy 86.36 177.8) (xy 97.79 177.8)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "963afb81-feac-49a3-8fc5-df290cd6c11c")
	)
	(wire
		(pts
			(xy 73.66 83.82) (xy 83.82 83.82)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "99905e23-12af-42ff-9827-7e1fb15f877a")
	)
	(wire
		(pts
			(xy 109.22 175.26) (xy 119.38 175.26)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9c278c3d-3de2-473f-acf9-d2769c08c92f")
	)
	(wire
		(pts
			(xy 86.36 170.18) (xy 86.36 177.8)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9c80c7d7-a0a7-476f-a232-20ba5096d48d")
	)
	(wire
		(pts
			(xy 137.16 124.46) (xy 134.62 124.46)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9dd78414-861c-4693-920b-30d9707fc7d8")
	)
	(wire
		(pts
			(xy 58.42 99.06) (xy 60.96 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a03ef3db-2b87-4cfc-a2c2-286ac507d715")
	)
	(wire
		(pts
			(xy 73.66 38.1) (xy 73.66 45.72)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a17ccbf7-0148-48da-88ee-ac5618909079")
	)
	(wire
		(pts
			(xy 27.94 58.42) (xy 27.94 60.96)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a26be039-6c62-4f31-be37-d483285659d4")
	)
	(wire
		(pts
			(xy 137.16 78.74) (xy 134.62 78.74)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a60f0147-e737-4e2f-ae25-62dca2fec9c5")
	)
	(wire
		(pts
			(xy 50.8 167.64) (xy 54.61 167.64)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a7692c54-50b0-45eb-a494-85008a7f7441")
	)
	(wire
		(pts
			(xy 67.31 165.1) (xy 67.31 163.83)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a9363ad1-dc6c-4060-9c6c-be7a2f42be11")
	)
	(wire
		(pts
			(xy 137.16 53.34) (xy 134.62 53.34)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "abc5761c-ffc3-47fa-8a38-a7b6504fa8ea")
	)
	(wire
		(pts
			(xy 50.8 109.22) (xy 53.34 109.22)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ae339866-815f-4909-9c6d-366233cec221")
	)
	(wire
		(pts
			(xy 73.66 53.34) (xy 83.82 53.34)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b079cdd9-b5c9-4b50-be23-01f82b7490e4")
	)
	(wire
		(pts
			(xy 109.22 165.1) (xy 119.38 165.1)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b3594c93-869f-4afd-a695-3c6e63e990bc")
	)
	(wire
		(pts
			(xy 67.31 163.83) (xy 71.12 163.83)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b3978ac9-e485-47ec-a448-1d2ba4474f25")
	)
	(wire
		(pts
			(xy 157.48 170.18) (xy 157.48 172.72)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b83fd899-c4c7-4d24-b32d-8b20491d60e5")
	)
	(wire
		(pts
			(xy 137.16 88.9) (xy 134.62 88.9)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b9884aaf-0c33-490e-b7a9-da3d395d09b6")
	)
	(wire
		(pts
			(xy 71.12 163.83) (xy 73.66 163.83)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "bc440b7d-64dc-482a-a4fc-df76379afe35")
	)
	(wire
		(pts
			(xy 63.5 175.26) (xy 80.01 175.26)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "c197544d-8003-4cc2-a1a0-8b0e7a6da271")
	)
	(wire
		(pts
			(xy 137.16 121.92) (xy 134.62 121.92)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "c268e7d2-79ba-4d45-a6d1-d7508349cb73")
	)
	(wire
		(pts
			(xy 137.16 111.76) (xy 134.62 111.76)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "c49316dc-f95f-4875-8364-0f7c0d072644")
	)
	(wire
		(pts
			(xy 81.28 129.54) (xy 81.28 132.08)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "c6438936-14e0-4830-a09b-fd6a7ee23119")
	)
	(wire
		(pts
			(xy 73.66 185.42) (xy 85.09 185.42)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "c9640048-9d67-478b-b47f-efeb9e5347df")
	)
	(wire
		(pts
			(xy 119.38 43.18) (xy 119.38 45.72)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ce806434-bf59-4b5e-a131-1f7325256d77")
	)
	(wire
		(pts
			(xy 137.16 68.58) (xy 134.62 68.58)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cebe7c67-639c-4921-8291-0020b574af19")
	)
	(wire
		(pts
			(xy 114.3 40.64) (xy 114.3 45.72)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cecd90b1-ab22-46b7-b92f-97d44526f04b")
	)
	(wire
		(pts
			(xy 119.38 165.1) (xy 129.54 165.1)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cf56483b-f922-422a-b206-c8120ef331e6")
	)
	(wire
		(pts
			(xy 63.5 177.8) (xy 86.36 177.8)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d15de7e8-faec-4cb0-93f7-cd7e7d2a7383")
	)
	(wire
		(pts
			(xy 25.4 157.48) (xy 25.4 167.64)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d18f1678-3a65-46bd-a40a-31596de260e5")
	)
	(wire
		(pts
			(xy 67.31 182.88) (xy 85.09 182.88)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d33d2113-2bad-4c39-ae11-39e042075247")
	)
	(wire
		(pts
			(xy 91.44 43.18) (xy 93.98 43.18)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d3e8d8a0-244f-4b44-b7a3-38ebf4cbf0ac")
	)
	(wire
		(pts
			(xy 127 43.18) (xy 129.54 43.18)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d44c844b-f134-49c7-b560-1ffc35aebfcb")
	)
	(wire
		(pts
			(xy 137.16 58.42) (xy 134.62 58.42)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d4f1d872-a13f-4b46-844e-aac3a128ba70")
	)
	(wire
		(pts
			(xy 106.68 165.1) (xy 106.68 175.26)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d5933863-a919-4bca-92d0-955b239212d6")
	)
	(wire
		(pts
			(xy 50.8 154.94) (xy 50.8 167.64)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d772c7f7-357e-4ff2-965b-11272c4d7217")
	)
	(wire
		(pts
			(xy 71.12 109.22) (xy 60.96 109.22)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d87c25b7-2f5c-4f48-ac0d-04b7021d0c5a")
	)
	(wire
		(pts
			(xy 106.68 165.1) (xy 109.22 165.1)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "db785962-3ac5-4165-b2c6-736827c963a4")
	)
	(wire
		(pts
			(xy 109.22 137.16) (xy 109.22 139.7)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "dc8a5332-c762-4d57-82dc-9e3f95001465")
	)
	(wire
		(pts
			(xy 149.86 165.1) (xy 149.86 170.18)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e0e5b7d3-c112-47b8-8515-67475755443e")
	)
	(wire
		(pts
			(xy 137.16 86.36) (xy 134.62 86.36)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e1611754-1d12-4663-b46b-cef07313be8f")
	)
	(wire
		(pts
			(xy 137.16 76.2) (xy 134.62 76.2)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e1899a10-dea8-4716-bd9b-7eddf17549b1")
	)
	(wire
		(pts
			(xy 137.16 114.3) (xy 134.62 114.3)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "edb3047a-98aa-46aa-8a94-bdabe184668d")
	)
	(wire
		(pts
			(xy 137.16 106.68) (xy 134.62 106.68)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ef8d10ef-a680-4d4e-ad47-33f0f5b317ef")
	)
	(wire
		(pts
			(xy 73.66 170.18) (xy 73.66 185.42)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f5b4c785-edf4-40fa-b97f-04ac6b7cabfa")
	)
	(wire
		(pts
			(xy 66.04 104.14) (xy 68.58 104.14)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "fb8aff5c-3222-40cc-8870-8dde5feb864a")
	)
	(wire
		(pts
			(xy 81.28 60.96) (xy 83.82 60.96)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "fbb374c7-1c85-4a82-8b88-da9a34bb2637")
	)
	(wire
		(pts
			(xy 60.96 109.22) (xy 60.96 107.95)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "fc9f654d-d766-4d8c-a774-21ceced25734")
	)
	(wire
		(pts
			(xy 137.16 101.6) (xy 134.62 101.6)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ff1d29d0-675e-464c-a473-bd099113fa97")
	)
	(image
		(at 228.6 93.98)
		(scale 0.556668)
		(uuid "5d5b47b6-9b5a-49af-a4c0-e95801a8e8b7")
		(data "iVBORw0KGgoAAAANSUhEUgAAAgAAAALKCAIAAADLaX5/AAAAA3NCSVQICAjb4U/gAAAACXBIWXMA"
			"AArwAAAK8AFCrDSYAAAgAElEQVR4nOydd0AU1/bHz84WlraILEUBESSghhawgQKagBGVFDSJjyg+"
			"jeZZE0xiEoP6VDAQjWJi21h+sWt40aCComBHY8SCCIiCSu+9Ljvt9wcrws6uDLirm3A/f87OnPO9"
			"55x778zszFwOTdOAQCAQiN4H9qoFIBAIBOLVgCYABAKB6KWgCQCBQCB6KWgCQCAQiF4KmgAQCASi"
			"l8JT9UNNTU1+fj6Pp3IHtdDQ0KCjoyMQCDTqBYFAIHo5FEX17dvX0tKy40aV4/vp06fLysqEQl2N"
			"aiovL9PX19fXN9CoFwQCgejlEATR1Nz07TffdNyocgLg8/kDB9r1699fo5qSEs9YWlrZ2g3SqBcE"
			"AoHo5TQ2NNy5c1thI/oPAIFAIHopaAJAIBCIXgqaABAIBKKX8s+ZACiCIF+1BgQCgfgbodmnPLsD"
			"WXR28/YLJRQAcHScPloR7MpncxhVlnG9UM/ZhXd5w+bSt1Z9MlxPwzoRCATiH4L2TABAtTY2mb35"
			"1axRBhwOX9iQk/II7D3s8Ac3ivRGDJTdfkybGjVWNJk4ONuIyOrHGTk1Qush9oLcSydi7vZpNJzk"
			"4O1vaa0DQNTlZWRX8vo5DLHUr8259YQ2EzWWN/V1dLER/XMudxAIBOLF0aIJAADo5vJHD+4LsT62"
			"zsSN2GMQ7Dag8WJMUn+P9xr+2HfPaKgN+SgnJXCxa8Yv8Q025s0xZ10/eKuxlSaa6+sqMi7ElXFH"
			"2+Qf3PBHpZU1mReb8kGoR9Yfe9KNhg6gHj1KeW/9gjGvun0IBAKhRWjXSTHdWHj/Xlpa+pNqQvEn"
			"joHzpNmfvGlH1ZSl3XloNPrjT+fPm+M/eKCbkwXXcNBwFxMOAEDL/ZsZ+qOnz/00eCT/7q0HBHAM"
			"nCbPmfXmILKm6uU3B4FAILQZ7boC4PbznDZ7rBEGgGfcBoogaJKkOvzO4XAAODw+JpO1UjjU1DTo"
			"AwAH4OmiNhiPh+FSKUXKWnAu9+mfCJyX2woEAoH4W6BdVwDP4FraD8LuHAqPTixQ/Ilv7ztG8Ncv"
			"q76XnC4ghFxzK7OaywdOPqEAAHSdx43Tv/XL6tW/3us7buwQVn8jIxAIRO+Eo2pJyP/97388nuAl"
			"fApi0CB75Z+CoPDGhlYdQwO+kkmKkjXUNnON+uhxAQBvqm3BDEW6XPmPeEN1A8ewrwEa/hEIBKKN"
			"tk9BLF36VceN2nULqBMY38BI1RiOCQz7tn9BlK/fp9N+fMO+fTWqDIFAIP4JaOstIAQCgUBoGDQB"
			"IBAIRC8FTQAIBALRS0ETAAKBQPRS0ASAQCAQvRSVTwERBFFf30DRmv3CJkmStbW1RUWMh/1fNTQN"
			"nL/l+2Md3orTLrRWmJqgX8ILh//0GL5selc8pdJWiqIUNqqcAKqqqqqrq0tLSzSqiabp8vKy6mqt"
			"+04DjuMcDofH0+LHZBlQFEWSJJ+vje8/4DiOYRiXy+1615eC2vOr6YJ5CcnVthxpFIIgaJrWzs6i"
			"ISiKwjDFWz4q69Xc3NzX19fZ2VmjmqKiory9vUePHq1RLz1g586dQqFwxowZr1pIN8jMzDx69GhY"
			"WNirFqKEtWvXBgQEuLu7v2ohciQSiUgkCg4OVpfB6OhoR0fHiRMnqsugAnfv3j158qRGkxsVFeXj"
			"4+Pl5aU5F9rDkSNHampq5s+f/6qFvDzKy8tjYmIUNqL/ABAIBKKXgiYABAKB6KWgCQCBQCB6KWgC"
			"QCAQiF4KmgAQCASil8L2qTWqPiNuz5HzD6q5ps5vzwgZb9uStDHstwckh8MVGA8c+d6/p3n2TZV8"
			"8X+N72//6i1ofHh676HErBrBAM+psz4YJmb1YBmRdWj5psu1AADA6TNmUfj0Ia15KUmnk2rcvpw1"
			"StjTFqoBRWX/fbP64M7Y1EruAN+QeUFO2rnWsKLoqGl9rx3Yfex2Bd/u7U/+M9FeV2uUhU9/nQ8A"
			"dX9uDz9v/cW3k/u/7OcQFQWtnlC4afnvOSQA8F6fvj7U1+AFDYZPH9Jy/9SB3y6WD/3P8g9fe/FH"
			"RTs7GOVnn37hTgsNAEBRYr+vV0+xVauDMYvCgy3SDm0/fL0MsxkXMu89J0OtrPmewmjtv8xu75fE"
			"3Ko2dJ06b9Zoi3/wg7HsapEsPBb2WXSB84fvDWv987eVoVUGhz4pf5ia2frO4nfs6m4d+2VpluzX"
			"LZaP796pHwNk4bHlC358PDho4uCay5LQO1Vb/2+hK4vxm2ooyBG9e2BdgHxfPPNA1P4Sg4a/Kiw1"
			"+zZaN5WRhUc+X1c1bf0Gl9Ldn0XtG/zrIidtfJhYIZxU6e+hWwuDNv3oXrRt0Q+/DZX8e+CrKmvF"
			"RAMAQGPKrt0n0mVTWl7BmzmMyks790A8Zf03owXA4Qm7O/orMUjmHln5Q6bPV99+MlSslhcFmCGc"
			"AgAA9RfXfJ06yFL9DmQ3flj/58Dv13/evPezdQeG/N/8IX+nN2S6QrGv1CQs35w9JirSNWP9Nz+e"
			"fG3de2b/qPmuI6zSSBYmxd/kjA2P+NzPgJwweODpYkELBQC8/sPfm+ovGAN3LvycntXaVnfE47ij"
			"1zG/qB++GqtPvMX/94z/Hbv+ievY7p/B84dOj4jE7266vb/bh2oUjtAleOmbI810uLp2/XgPFF+t"
			"01Kwvv7Lf54oNhEAWPaRVUq16w3IltQ9+xreDrI//aqFAAAAVVUlg8o/z100GDLG2/HF7eGZJ5ME"
			"b03Xzbl+Q+gz2l5jV4xkYXxs1ZgvnQVd79ptKJpnJDYW6uuZirDSv0nN9xQyLyu3/+gVVga6ZpOc"
			"9sVn4O+Z6bxqTZqC3QRQUVZJ9/WwENTdjtl3uZjkGJlJaQCQ5ZzZuimt+cGlNJ2h84bqwBUAALK4"
			"tBzMx1kLAYBnbdOP11haUgtgwcIPTWQeCfvqHAe41hOWLPaz0J5pV0GZlxiAqruxKxafvOx1bTz9"
			"BwBmOM2Mcv6I2HLidqntoi0Or/QETkGZcdb+X4v9v/lIGnm6XhsEeQnNLKCmqrYyMey3tLBdn73R"
			"/bOXTgb/3fQo5z6Z6u6re27FV9mrt81WQ/SV9JXWe7+fM5j4o6V6ruwUHAz/aNLeBTOCdxONtot3"
			"/aNO/wFAobULXazNCm7fqp3wRkn64+pWd+06WVIvrDLJ4evwQdospTlcjFN3L+50Je75liUATTTW"
			"1zXrOLy3YnHQ24N4ZwEAADMw0IeW5hYKgEs1N7aQXKEuyw7E4Tm+uzRsnA5weHradZORoUz64Mjq"
			"XdJp3wfZaO/9QYZont3kL1cNu703Yl9sjs+cVzgHdFJG5uz/+ZrFu5+VZyU3NTfmPKkc8Jr4ZU+q"
			"nUOlY7HwR08AgHeNv1p06QH+hmu39XQ0qC87SQ2ctHjRNAdsLJ624mrhTIcXv/3GSC5Vff63tNc/"
			"nN9HTR2ns4O6C/931nLZvq2e0vgVYftu+n094p91TtyptTydyZ9Nf7xl+RenrEVgIjbR3i7+4rCq"
			"Fr79sDf6ll+Nu9z0etC/xw/Sf7pdZ/CUZf9d8d2S2QFDn5Udf8hId+PCi38klzZXp8WcTuc7jfQw"
			"ZCuHq9vH2NjYuI+hjrYFvaMyojB+7YbMcSuWjhVr1TTFoINo4vEfUb/8JdPtY+3hYV1XUPSK/1Z5"
			"pozT0mDg7GGQc/XKXznVDXm304paX60gLpH12w/777YCUDKplCcU9uwbb88M8g2t+hGF+U0AVENd"
			"k45uDw0+RzEAkE+OH68b976LGsflDg7ImoISgbWdEcY3sx9AFOU3/vNuAnUKp47dpKUbt6xf5ERS"
			"Lt5DtfYaXw2wOwvU95q/fHpY+H/f81nBoQUWw4N97HiPVO2sN3pBWPCyiO8mH8e5Bnb+X0ROtda2"
			"0fyFoEqORa6/Xj+4bv0XiYCJRs39b7D23gZqh2c1dMDDtUu+TTRrfFz75ufDteYEDhO9MXXhGwBA"
			"VcY8zKz/8F3XHvzrqlZ4A53679z45QNrYeGjPlMj7V/4Skkw7F9Tj//3i6UXDcpLneeu08Q/is03"
			"/nfe+J2fB2iop3Gt/d83X77imycDiNwip39HGWv3ic+Lg6fuWSZJbrbw++yL4a/uebmXAa2CmJiY"
			"tLS0TpvwxoqCgpK6VlWHdN65obywuFpKPn+vyMjI5ORkVgZfLjt27Ni3b9+rVtE9MjIy1qxZ87w9"
			"ZHUl+UXV0pclqAMRERG3bt16BY5VsH379oMHDz5nB1l9SUFJPc7a4MaNG+Pj459nsLakoKKRYG1Q"
			"gdTU1PDw8J4ezYrIyMirV6+q/p1oLC8oqGjqokv/TTh8+PC2bdtetYqXSllZ2ebNmxU2dufshqcv"
			"ttLvejf5zgamlq/6VA6hAF9kYS161SL+HvANLaxY37lkZdDIwkqd9l4+XH1T9v0f8ffgn34ph0Ag"
			"EAgVoAkAgUAgeiloAkAgEIheCpoAEAgEopeCJgAEAoHopah8CoggiLy8PJLU7AtDBEEUFRWlpqZq"
			"1EsPaGxsxHFcC4U9h+LiYpIktVMzSZL5+fnMNalfFc3NzTRNqzFWra2tFRUVmgt+fn6+ppOrtZ1R"
			"E9TV1TU3N/eSxrZRX1/PHM85NK38Sxdbtmyprq7mcjX7DheO4xwOh8fTum+L4DgOAHy+9r/h9QyC"
			"IGia1k7N2pZotedX0wXzEpKrbTnSKH/HDv6CUBSFYVhYWFjHjSqTbW5u7uvr6+zsrFFNUVFR3t7e"
			"o0eP1qiXHrBz506hUDhjxoxXLaQbZGZmHj16VCHBWsLatWsDAgLc3d1ftRA5EolEJBIFBwery2B0"
			"dLSjo+PEiRPVZVCBu3fvnjx5UqPJjYqK8vHx8fLy0pwL7eHIkSM1NTXz589/1UJeHuXl5TExMQob"
			"teWSHIFAIBAvGTQBIBAIRC8FTQAIBALRS0ETAAKBQPRS0ASAQCAQvRSWj3xRdWnHJPsvFtD9vf71"
			"n2keJhqZN6jatGO7Dl1+0mIwyGfarPddDB8eWr7pci0Ah2dgOXzKnOmepgWxkbFGi0N96dSYbQeu"
			"lnJt/GYveMdRr+14POPA8s3JdQBcoemQ8SGz3ta9sE5SG7h8upOQqk3e/sNd12/mj+nJgklNV7Zs"
			"KAv4buogHlCVZzfuaJ32TaAVF8jCk5Fbat4LD3HiM73PdM/ZGn48lwSaBg4HgDc0OHKRyyNF2VTp"
			"6XWdd5u22O7P/WpQTea2xWqcfvGVPTtjUyu5A3xD5gU5ibDmR2f37k/IqOJaeX40Z9owMUbVKgun"
			"inRweAb9nPyDZ7xtL/8yZOcwsIDIUsire+GvkVmjwqY78fG0PSsvDfpusbch8ej3yFNmi/0Lo7aU"
			"vR3++bi2L9ATDw6v+KnorfAv3uSmKZHcMz3Mpory5bEzanmcuHff6Ywaoa3PtNnvu7DMA6OFI6Qn"
			"5QaBYVF59LvpoL1rKJVMNealJJ1OqnH7ctao7i9v+c9HSQefZJChfDDi6po5TZo922/gsxU1Okfc"
			"iTgfvbkycMU0Bx4A4BkHVidZffX5WEHnXqd7VdHlF3YX/9tx+Jr4mryDyZPdoeO1px8AQNdjyptV"
			"sfGdh5rPfFh+ypZdQTdd+3nZphSBm4fBPcl30efruhFb9jRdio5ItpwdsTFitu2NqB8SqoiGghzR"
			"u5skki1RC9we/vT9sRKyuTgrpwKXpe7+8cqAhesjQwxOrDuYTcgN0PX52aJ3N0kk0WGT8COrf71v"
			"7uOtG7fp4ENZ482d224OHDeih8vlkVWPMgsbaQAAWlqW/bC4hQYA4knCidsPkk6mtCjzvrfS79ut"
			"EsmmoL5VA2b+LJFs+cxHR4lszCJAYbexjupRTbfFiiz8/ftdVW99s2HtdIO4qH2ZrVWJkSvj9IOW"
			"b4hc8Hr6j+HHiknl4VSVju0blwWJL6xZeayw7Z0ShTCwgFLMa4WZoPRSShEJ+IPLSdfOnL8jBar2"
			"zpVsTCxoKHiYeiLmTEmbL+nt2KPJGdnlqiqgZ3qYTZXHjqi9uH5lLG/y12tXhtjdWbciJo/la5GM"
			"FpY0FmflVOAUxbT4nKZ0x4G8ayhxAHjmoSjJ5az0v9IrXvE6cNoKs4M3KpZEe8Sjv/Yq2h4dV/l0"
			"TTRGxAtEr5mWxp3NwgEAZOkJ8TXmjgaMXidjuKQUhq90/KkHRsdrFyORSCTRc/0DFYca9h8yZzW2"
			"4I9uptYMHD/r3zM/neTQkHrzgYy1/W6ANzYROoaGejqG9pM//3aqw7PzN66Bne8Iy9I8+TBAlqdn"
			"G4waO0Co7+jrjt/PaFK0JDB1cR7QUllJibzmf2p59seoqJ/veYXOHCpQq9yshOvi2Uvccs5ca1Du"
			"XeGALmU/Ra2qOUKX4KWzPM10RLZ2/Xgk1XDtTI77rH+59NUxsA2YO2eYYTOhXJfKdAhMnIJCJ0pP"
			"xD0inhcGFjzNa7nYdXBLRnojUXgj08zfveRmJi7LyKxwcB3EA46+hwd97tQTAgAarsVnDBxpz31e"
			"KHuiR1VTm66dznKbFTLMQmQyOHDeeFnSufxujqDtlUupsviEbVV04YBULZk/dHpEZGiAvb56lqLs"
			"FRAqByOByMRICHR772ZGvKi//7g+189n4QCytHO3LPz9zJsYve45C82rGkA6dzw1weoWEN3S0gp8"
			"gQ4HhEIhSFuep77n9Hl70adZUUuCY21G+QVNCxopghSgyu7E/SGF5qLr8eWeSxz5cB0AQNoi1THR"
			"4wCArq6gVdpCgVHbREYTjxO3brpPVD64Vzvp6xE6gOn4zg08/NEe61XHXNV77duaevpm/7dmepo/"
			"+XX/papxk02UeO8MrVK2Ilhf9anGxEO9xABU3Y1dsfjkZYMbztWKTE3b/PJs/WbYApFzrrMuACNQ"
			"no52o8YWpi3Z5SQ4kIphYHe1opBXPYc6+9LrGaVNaUKvJWMz16dk55BP+rs46UA2TRqOnqB7PC59"
			"5mdWF04VjRg/MjX2ORXASAsbPcymZgIAkNVlVYZmFm3vwnPFFn3rbpeTYMvu3fjOLXTgpQAAkFVM"
			"i1JjllXx/BC2dQ1lDlhLRjzDiFn9NHFv35KFx1sKcxpGrfr06bqeyiJOm/v5mS05l4k7yM6nWvt/"
			"IqarKxR7XX0s02kXAwh06Hg0nrb383nHOYCJx4auarvb1CPY9VcuhgFNkwAURQKXq6F3xQUDJyyV"
			"HNqz+kO7XEloRFI1BQAcDOPxhCZO09f9smT40+WIMC6XJEgAoEmSwrBncjiYeIjP2DEDWuttAt91"
			"0gOA1qy4pLrX+mfHnS1U4+UvB5r+OnWpsTl9/85L5URawvlySqn3zgeplM1A3aqlD46s3iWd9m2Q"
			"DU9HwJPJWuUzONHS0CRTpUtZOtoFNjRy9fUxpWFghUJedZydxNl/Hb9JuY+y9HA3vH/h8n0dJ1dD"
			"AAAZLvSZZH8n7s/HZxJbfCba0AStugJ6qEexqXKNQqGgtaVFHitcKuvWAvHKKleJRV32VdEzB2pa"
			"gr53wVFSEhyec0j0VsmemB2BuZK99+Q3aJRGHBP7+lnePnc75dw9O39vY+Awe50Sl10NIADPOh6H"
			"7zLzJ4lEItkW8QKjP7CcAHiWNv2x0kc59U0Psougn7WVJmYAPHX7/NUJNaBn6fHOJxPMHqYXkgCY"
			"mevEwMDJE8a6WT2LB9fU0rSmMI8AkBUUSs37Gz5rBCaycnYb8cF/xpcdOJTeCvjDA9EXX/ssOmoG"
			"/7dNccU9HUx1+hqTZSXNAAB4aUmdkbhPQ3LCQ9cPgzw9PIZP+vjNxqQz+STTuwLPka0QCfWofgpR"
			"GL92Q+a4FUvHijHATJ2H4LeuF5EAQJUeWzp3+z1KUVebCGXpkNN8//f4smG+Q/h1SsPAAsW8Yn1c"
			"h3ISTze5jLLgGY5wh8RTDYPd+redVtGgN3LyGzm7wxME/gHWGA2qQ9kzPcymykWauDrTN6/k4gBA"
			"VSZfKxnsZs/6f2WllYuJGRYd2FZFTx2wl9x7YXRw/XvKSwIAgC8y1m+uq5OfW6iIuLG338DbW39J"
			"d/D3MgRlvQ5jjClYVwNIh46nvqazGsox84BZH8Z/uzzAH3QGvr8mcIAmLin5TpMDT64OXXRxoElr"
			"Xp7elJWD+TWnle9q6D119LFNocsHULl145cNZ1wq8eynznSYu/v4cI/zZyznSryM+9KLpyZ++fMZ"
			"z+8nmvXgH1Udz+CPTq1auOiKrbAslxj/5Ty4tDrvjeBVXh4CACD7Z8euSMj5wLWz95NTNk7t3ylQ"
			"Xclug3j820a1qG6DKjkWuf56/eC69V8kAiYaNXdFyCKnsOUL79mZNOWV9/9kjRvfsEWJLmXpONV2"
			"3UkTvIGTV4R6CCpjzzLC8PE8x56cHvBsXO155w1H2fAAMxnuZrw7x9mBDyA/URI4BY7BEnMD3jTl"
			"lAGAilBSlRd6pIfZVJAlAwDwBn8cOnLl6vlfWps0F1bbzF7j/aJL4iqxqDT66nTwgpJ7A4odfEE/"
			"p5rAuM4l0V790kbcekZY+w0aFRE39PJ3iP5RsGiEHgAA31mx1xmIjTq7FGP3nlpkDCB0544nhJT2"
			"W0AAPOeQ9Qu9dHvadlVLyMfExKSlpXXYQDZXFhRUNJFqXag+MjIyOTm5w4bWupL8goomossjyaaK"
			"gsLKFrWq6cCOHTv27dvXcQvRVFGQW1InezG7mpSdkZGxZs0aVru21hbnF1VLu9TFOh1dERERcevW"
			"rRc205EXCuX27dsPHjzYcYvKpuIN5YXFNVLG9s5s3LgxPj6enXOGRRZNSU1NDQ8PZ2efpWRFIiMj"
			"r1692s2D/q4cPnx427ZtHbcwO3g3qp9VxBm9Tj1jClvKyso2b96ssJH92Rqma2Jl1dNphjUCkYW1"
			"iM2OmJ7YitUj0+qCqye2snlhKy9dtnIERv2sjTpuUKGLdTpeAWoOpcqm8gxMLQ3U50eZRXVXhfol"
			"9wKYHbwb1c8q4oxep54x5YVAbwIjEAhELwVNAAgEAtFLQRMAAoFA9FLQBIBAIBC9FDQBIBAIRC9F"
			"5VNABEHk5eUxV5FXLwRBFBUVpaamatRLD2hsbMRxXAuFPYfi4mKSJLVTM0mS+fn5GKYtJxzNzc00"
			"TasxVq2trRUVFZoLfn5+vqaTq7WdURPU1dU1Nzf3ksa2UV9fzxzPOTSt/MM+W7Zsqa6u5nI1+xkR"
			"HMc5HA6Pp6GPS/QcHMcBgM//O71GSRAETdPaqVnbEq32/Gq6YF5CcrUtRxrl79jBXxCKojAMCwsL"
			"67hRZbLNzc19fX2dnZ01qikqKsrb23v06NEa9dIDdu7cKRQKZ8yY8aqFdIPMzMyjR48qJFhLWLt2"
			"bUBAgLu7+6sWIkcikYhEouDgYHUZjI6OdnR0nDhxoroMKnD37t2TJ09qNLlRUVE+Pj5eXl6ac6E9"
			"HDlypKamZv78+a9ayMujvLw8JiZGYaO2XJIjEAgE4iWDJgAEAoHopaAJAIFAIHopaAJAIBCIXgqa"
			"ABAIBKKXwvqRL6q58HZi3JmaN77698gX/Ga5She1acd2Hbr8pMVgkM+0We+7GD48tHzT5VoADs/A"
			"cviUOdM9TQtiI2ONFof60qkx2w5cLeXa+M1e8I7jsw8pNj86u3d/QkYV18rzoznTholBwaRxjya8"
			"pitbNpQFfDd1EA+oyrMbd7RO+ybQigtk4cnILTXvhYc48QEAzziwfHNyHQBXaDpkfMhM95yt4cdz"
			"SaBp4HAAeEODIxe5PGLKVjhu1kS7mrh1ktrA5dOdhFRt8vYf7rp+M39M9xeGJ3PbYjVOv/jKnp2x"
			"qZXcAb4h84KcRJhikDCqVlk4FdNhjCmGt8/DQ+EXB4XNG9m9ZYuJLIW8uhf+Gpk1Kmy6Ex9P27Py"
			"0qDvFnsbEo9+jzxltti/MGpL2dvhn49ryxzx4PCKn4reCv/iTW6aEsntlrm6Zk6TZs/2G8iuVBWb"
			"KsqXx86o5XHi3n2nM2qEtj7TZr/vwjINjBaOkJ6UGwSGReXR76aD9q6hVDLVmJeSdDqpxu3LWaPU"
			"uzLqPwMlHXySQYbywahzaVGVSdGbKwNXtC3LhWccWJ1ktWR88fqfOu5qeqNL8/KBSZ5XDs+gn5N/"
			"8Iy37fXbMw0AoOsx5c2q2PjOo0p3loBnwq6g8fQdsyZ9tDhyV1xahcbeDGu6FB2RbDk7YmPEbNsb"
			"UT8kVBENBTmidzdJJFuiFrg9/On7YyVkc3FWTgUuS93945UBC9dHhhicWHcw++kSyVRVYuTKOP2g"
			"5RsiF7ye/mP4seJ6BZM9VEZWPcosbKQBAGhpWfbD4hYaAIgnCSduP0g6mdICAAB0fX626N1NEkl0"
			"2CT8yOq9lX7fbpVINgX1rRow82eJZMtnPjpKZSsc92s6ZeXjrRu36eBDWePNndtuDhw3ovujPwDQ"
			"bbEiC3//flfVW99sWDvdIC5qX2YrI0ik8nAy08E4Em8oyHhczXoZyKdQinmtMBOUXkopIgF/cDnp"
			"2pnzd6RA1d65ko2JBQ0FD1NPxJyRr3kuvR17NDkju1xlBTy1HP21V9H26DjGutrKYTRVHjui9uL6"
			"lbG8yV+vXRlid2fdipg8lsXPaGFJY3FWTgVOUUyLKprSXQfyrqHEAeCZh6Ikl7PS/0rXXOf9e8Ps"
			"4I2KJaGitDDj10xL485m4QAAsvSE+BpzR/1GhV3ZmG8TIveyfeOyIPGFNSuPFZLtfiUSiSR6rn+g"
			"4qjyIqM/sF4T2OLNpb/Gho3R6PqieGMToWNoqKdjaD/582+nOjx7RYNrYOc7wrI0Tz4MkOXp2Qaj"
			"xg4Q6jv6uuP3M5rkuzVcO5PjPutfLn11DGwD5s4ZZtisaBLqY7+cOm/ZV4vnTPtw0R5li651Q25W"
			"wnXx7CVuOWeuNXT6QWDq4jygpZIx+KiSzTxO5DX/U8uzP0ZF/XzPK3Tm0O6dXyvAEboEL53laaYj"
			"srXrxyMpZpAI5boY6WAeqfwVQvY8zWu52HVwS0Z6I1F4I9PM373kZiYuy8iscHAdxAOOvocHfe7U"
			"EwIAGuctWGQAACAASURBVK7FZwwcac/tMpQgEJkYCYFmOTepqryma6ez3GaFDLMQmQwOnDdelnSO"
			"7ZKXii0soVRZfNJVU1g6IFVL5g+dHhEZGmCvj1YHZg2hcjBSKC2utf+4PtfPZ+EAsrRztyz8/cwx"
			"FbuyMi8/0sQpKHSi9ETcI3YnBD2H3S0gTGzvJJYWaFZKn7cXfZoVtSQ41maUX9C0oJEiSAGq7E7c"
			"H1JoLroeX+65xJEP1wEApC1SHRM9DgDo6gpapS0UGGEAZHVFrcjUtC38PFu/GbYAss4mQZpOUpaT"
			"Vq3wav19yecXsj92curpm4Ctqadv9n9rpqf5k1/3X6oaN9kEgCYeJ27ddJ+ofHCvdtLXIxTvPtDK"
			"ZSs9rq/v3MDDH+2xXnXM9QUv2THxUC8xAFV3Y1csPnnZ4IZzikEics511gVgBMx06BddVjxSltJT"
			"VQp51XOosy+9nlHalCb0WjI2c31Kdg75pL+Lkw5k06Th6Am6x+PSZ35mdeFU0YjxI1NjVVUAANDE"
			"vX1LFh5vKcxpGLXqU5YraTIrLxMAgKwuqzI0s2h7F54rtuhbd7ucBFt278Z3bqEDLwUAgKxiWpQa"
			"q2hK90LY1jWUOWAtGfEMI+ZgpKK0MHM/P7Ml5zJxB9n5VGv/T8QYPFHYFS50aZ6ZcszYwrQlu5yE"
			"9tUfMfHY0FUvtgQ8E236E1gwcMJSyaE9qz+0y5WERiRVUwDAwTAeT2jiNH3dL0uGP13eFONySYIE"
			"AJokKQyTR4SjI+DJZK3y01KipaFJpmgSADgGYrEQMH0DXUqG91QpB5r+OnWpsTl9/85L5URawvny"
			"Nq3iIT5jxwxorbcJfNeJcS+Xo1y20uNas+KS6l7rnx13tlAdV+3SB0dW75JO+zbIhscMkipdCrGr"
			"4TPD23MU8qrj7CTO/uv4Tcp9lKWHu+H9C5fv6zi5GgIAyHChzyT7O3F/Pj6T2OIz0YYmaFUVAAAc"
			"nnNI9FbJnpgdgbmSvfdYZlix8uS2hEJBa0uLvMW4VMYTduMSWFnlKrGoq7IpanKg0av2fyocJSWh"
			"qrQwsa+f5e1zt1PO3bPz9zYGYOzKwrySq4TWhkauvj4GHL7LzJ8kEolkW4S6R3/QpgkAT90+f3VC"
			"DehZerzzyQSzh+mFJABm5joxMHDyhLFuHZbM45pamtYU5hEAsoJCqXl/w7ZGYKbOQ/Bb14tIAKBK"
			"jy2du/3OTQWTPZSm09eYLCtpBgDAS0vqjMR9GpITHrp+GOTp4TF80sdvNiadyScBABNZObuN+OA/"
			"48sOHGLeX1IhW8lx+MMD0Rdf+yw6agb/t01xxS84BRCF8Ws3ZI5bsXSsGFMSpHuUoi4AUJKO4r6M"
			"I3s8gTLyivVxHcpJPN3kMsqCZzjCHRJPNQx2e7oiNuiNnPxGzu7wBIF/gDVGw/NC+RS+yFi/ua6O"
			"1T0gZuXJRZq4OtM3r+TiAEBVJl8rGexmz/qCUWnlYmKGRYcum/KCDthL7r0wOrj+PeUlAaCstIy9"
			"/Qbe3vpLuoO/V+c78vJdWZhn9PHm+7/Hlw3zHaLp7GnPh5/4TpMDT64OXXRxoElrXp7elJWD+TWn"
			"le9q6D119LFNocsHULl145cNf3qvhe8cssgpbPnCe3YmTXnl/T9ZM8yNqDrd0SRAj+YAHc/gj06t"
			"Wrjoiq2wLJcY/+U8uLQ6743gVV4eAgAg+2fHrkjI+cBVvjfPfupMh7m7T07ZOLV/p2tvFbJB4bj3"
			"Fkk3nrGcK/Ey7ksvnpr45c9nPL+fyPJmBhOq5Fjk+uv1g+vWf5EImGjU3BUKQXLjG7Yo0cVMh56d"
			"teKRhYlPL0+BazVp2fLAHi8azbNxteedNxxlwwPMZLib8e4cZwc+gPwiQ+AUOAZLzA1405RTBgDP"
			"CaX8epmWNuLWM8IYt+GUwmwqyJIBAHiDPw4duXL1/C+tTZoLq21mr/HW78pWV81kWlQafXU6eEHJ"
			"vQHFDr6gn1NNYFznkqg5pbq0DL38HaJ/FCwaIZ+KFavQxqIr8+3j/NNDCd7AyStCPYSQ0n4LCIDn"
			"HLJ+oZeuWtuuagn5mJiYtLQ0Da5RT9M0TUdGRiYnJ3fY0FpXkl9Q0UR0eSTZVFFQWNnC/KG1tji/"
			"qFraA5Md2bFjx759+zpuIZoqCnJL6mTdMsNApWw1kJGRsWbNGla7MoKkQhczdowj2REREXHr1q3u"
			"HvVcXiiU27dvP3jwYMctKssEbygvLK7pqsUbN26Mj49n55xhkUVTUlNTw8PD2dlnKVmRyMjIq1ev"
			"dvOgvyuHDx/etm1bxy3MDt6jkUMVGjbPgrKyss2bNyts1J4rgDYEIgtrEZsdMT2xldJHpgVG/ayN"
			"emTy+XD1xFY2L2xFpeyXCyNIKnQxY8c48pWh5lCqLBOegamlgfr8KLOo7qpQv+ReALODq2nkeCnm"
			"e4r2/AeAQCAQiJcKmgAQCASil4ImAAQCgeiloAkAgUAgeiloAkAgEIheisqngAiCyMvLY64ir14I"
			"gigqKkpNTdWolx7Q2NiI47gWCnsOxcXFJElqp2aSJPPz8zFMW044mpubaZpWY6xaW1srKio0F/z8"
			"/HxNJ1drO6MmqKura25u7iWNbaO+vp45nnNoWvknvbZs2VJdXc3lavYzIjiOczgcHk/bnkYFHMcB"
			"gM//O71GSRAETdPaqVnbEq32/Gq6YF5CcrUtRxrl79jBXxCKojAMCwsL67hRZbLNzc19fX2dnZ01"
			"qikqKsrb23v06NEa9dIDdu7cKRQKZ8yY8aqFdIPMzMyjR48qJFhLWLt2bUBAgLu7+6sWIkcikYhE"
			"ouDgYHUZjI6OdnR0nDhxoroMKnD37t2TJ09qNLlRUVE+Pj5eXl6ac6E9HDlypKamZv78+a9ayMuj"
			"vLw8JiZGYaO2XJIjEAgE4iWDJgAEAoHopaAJAIFAIHopaAJAIBCIXgqaABAIBKKXwvKRL7zk6sHd"
			"f9wsoUzfeG/OTB9LjTw8RdWmHdt16PKTFoNBPtNmve9i+PDQ8k2XawE4PAPL4VPmTPc0LYiNjDVa"
			"HOpLp8ZsO3C1lGvjN3vBO456KgwYY9D86Oze/QkZVVwrz4/mTBvW5+Gh8IuDwuaNFFDlFzZvzvT4"
			"fP4YcZeTYNOVLRvKAr6bOogHVOXZjTtap30TaMUFsvBk5Jaa98JDnPgAgGccWL45uQ6AKzQdMj5k"
			"pnvO1vDjuSTQNHA4ALyhwZGLXB4pk60g0p04s07hyLUhfRIVGtYlZG5brMbpF1/ZszM2tZI7wDdk"
			"XpCTCFOIiRgDqjEvJel0Uo3bl7NGtS9BqSIdHJ5BPyf/4Blv28u/NN85DCwgshTy6l74a2TWqLDp"
			"Tnw8bc/KS4O+W+xtSDz6PfKU2WL/wqgtZW+Hfz6urcnEg8Mrfip6K/wLP+NmJZJ7pofZVFG+PHZG"
			"LY8T9+47nVEjtPWZNvt9lz7sTpgYLRwhPSk3CAyLVK3SYu6eg/auoVSy0vwi2lHSwScZZCgfjLi6"
			"Zk6TZs/2G6gDAEBVJkVvrgxc0bZWF55xYHWS1ZLxxet/6rir6Y0uzct7dHteAQB0h81dO+ZBxAF6"
			"+oqPh+oAyG7uXHvfc/mMHi9jqwxWBU0+PrRi2f5cc8/hevf+b8X6eMZ652qh6VJ0RLLl7IiNEbNt"
			"b0T9kFBFNBTkiN7dJJFsiVrg9vCn74+VkM3FWTkVuCx1949XBixcHxlicGLdwWxChQGgqhIjV8bp"
			"By3fELng9fQfw48V4w0FGY+rKWi480vY/zW8+aFX16M/AJBVjzILG2kAAFpalv2wuIUGAOJJwonb"
			"D5JOprQAAABdn58teneTRBIdNgk/snpvpd+3WyWSTUF9qwbM/Fki2fKZj45S2QyRpaYBCkd63FaI"
			"DJv4022xIgt//35X1VvfbFg73SAual9mKyMmJJ55KEpyOSv9r/SKDq+JqErH9o3LgsQX1qw8Jl/F"
			"SCEMLKAU81phJii9lFJEAv7gctK1M+fvSIGqvXMlGxMLGgoepp6IOSNf81x6O/ZockZ2Oa5ccg/1"
			"MJsqjx1Re3H9ylje5K/Xrgyxu7NuRUwey9ciGS0saSzOyqnAKYppUUUxd9eBvGsocQDPCRYCAJR1"
			"8EbFkmiPePTXXkXbo+PkYyBm/JppadzZLBwAQJaeEF9j7qjfqLArG/NtQtq9SCQSSfQcD15D/r2k"
			"Hev3Z8kAgKp5kpFfr/y1rR7DagKgaSvvWV9//fnH0ya4GuGNja1qFtEG3thE6Bga6ukY2k/+/Nup"
			"Ds/mOa6Bne8Iy9I8+TBAlqdnG4waO0Co7+jrjt/PaFJhABqunclxn/Uvl746BrYBc+cMM2ymAQDw"
			"J/9buf6B/6qvx5m+wB0wPCvhunj2ErecM9caOv0gMHVxHtBSyZglVchWIZJdZLqGI3QJXjrL00xH"
			"ZGvXj0dSStzxh06PiAwNsNfvtHqsSqcCE6eg0InSE3GPiOeFgQVP81oudh3ckpHeSBTeyDTzdy+5"
			"mYnLMjIrHFwH8YCj7+FBnzv1hACAhmvxGQNH2nMBlEuGHupR1dSma6ez3GaFDLMQmQwOnDdelnQu"
			"v5sjaHvlUqosPlFRzN11QKqWrDpYCFUQKrucQGRiJAT6ae/mWvuP63P9fBYOIEs7d8vC388cU7Er"
			"K/OKcHSG+dte2Hgou+frrz4XVreAeIPemjkIoOneL7vPUV5f+fXTyOvBfd5e9GlW1JLgWJtRfkHT"
			"gkaKIAWosjtxf0ihueh6fLnnEkc+XAcAkLZIdUz0OACgqytolbZQYIQpMUAWXa4VmcoHeZ6t3wxb"
			"kKUAcf/XZX+2DlkeZit4EbWtqadv9n9rpqf5k1/3X6oaN9kEgCYeJ27ddJ+ofHCvdtLXjOUIaaWy"
			"yeoKRZFdR6Yb8xYmHuolBqDqbuyKxScvG9xwrkt3KpxCyjOjxhamLdnlJDiQimFgJ00hr3oOdfal"
			"1zNKm9KEXkvGZq5Pyc4hn/R3cdKBbJo0HD1B93hc+szPrC6cKhoxfmRq7HMMM9LCRg+zqZkAAGR1"
			"WZWhmUVbsXPFFn3rbpeTYMuu+Du30IGXAgBAVjEtSo2VFnM3Hci7hjIHrCUjnmHErH6auLdvycLj"
			"LYU5DaNWfdq+Ritm7udntuRcJu4gO59q7f+JGIMnCrvChS7Nt6e8fQFITDw2dFUQAOi5f/ohfLHh"
			"N58N5hpoKOuhpDUnJuzrw/h7q8MmWmjon2PBwAlLJYf2rP7QLlcSGpFUTQEAB8N4PKGJ0/R1vywZ"
			"/nR5U4zLJQkSAGiSpDCMp8IAR0fAk8meXq4QLQ1NMgAahMOX/DS9YVfUCeZKzGzhQNNfpy41Nqfv"
			"33mpnEhLOF/eplU8xGfsmAGt9TaB7zox7uVylMpWKpJNZLqD9MGR1buk074NsuGxcde109aGRq6+"
			"PqY0DKxQyKuOs5M4+6/jNyn3UZYe7ob3L1y+r+PkaggAIMOFPpPs78T9+fhMYovPRBuaeM71Zw/1"
			"KDZVrlEoFLS2tMjd4VIZTyhkfxKtrHKVWNRVVczqctANyYincJSUBIfnHBK9VbInZkdgrmTvvfYz"
			"ckzs62d5+9ztlHP37Py9jQEYu7Iw316oHL7LzJ8kEolkW0Tb/woAnD5jFsztF7/xaJEGbr2zG8vx"
			"grj/Lvk51+PLVSGOUK96zHgR8NTt81cn1ICepcc7n0wwe5heSAJgZq4TAwMnTxjr1mHJPK6ppWlN"
			"YR4BICsolJr3N8SUG8BMnYfgt64XkQBAlR5bOnf7PRw4PNs3RrpPW/6J4HDE/gfsWqLT15gsK2kG"
			"AMBLS+qMxH0akhMeun4Y5OnhMXzSx282Jp3JJwEAE1k5u4344D/jyw4cSm9VtKJctlKRLCLDHqIw"
			"fu2GzHErlo4VY2zcde20+f7v8WXDfIfw65SGgQWKecX6uA7lJJ5uchllwTMc4Q6JpxoGu/VvO3Gl"
			"QW/k5DdydocnCPwDrLHn3X7smR5mU+UiTVyd6ZtXcnEAoCqTr5UMdrNnffdNaeViYoZFB+XFrD4H"
			"7CX3XhgdXP+e8pIAAOCLjPWb6+o6DMbG3n4Db2/9Jd3B38uwk135rizMP79Osb6+Cz8xPnEgRf03"
			"31mdcMjuHtl5vkRKlayelgDA9ww7vfn9PupWwneaHHhydeiiiwNNWvPy9KasHMyvOa18V0PvqaOP"
			"bQpdPoDKrRu/bLiOCgPAtwtZ5BS2fOE9O5OmvPL+n6xx4xcmAQAAZjH+m9C0hWu3O2/73KPL1VN1"
			"PIM/OrVq4aIrtsKyXGL8l/Pg0uq8N4JXeXkIAIDsnx27IiHnA1f53jz7qTMd5u4+OWXj1P6drr1V"
			"yHZmiOw6MmyDCkCVHItcf71+cN36LxIBE42au6Irdyqd1pxquzylCd7AyStCPQSVsWcZYfh4nmNP"
			"PibGs3G15503HGXDA8xkuJvx7hxnBz6AfIIWOAWOwRJzA9405ZQ9p6mVF3qkh9lUkCUDAPAGfxw6"
			"cuXq+V9amzQXVtvMXuOt34WpLpvJtGjYoqwq1OjgBSX3BhQ7+IJ+TjWBcZ1Lor36pY249YywTnd4"
			"Db38HaJ/FCwaIZ+KaYVdbSy6Mt/eCdtvAQHwnEPWjpBvxsRvfjYn6eZm9bdd1RLyMTExaWlpGlyj"
			"nqZpmo6MjExOTu6wobWuJL+goono8kiyqaKgsLKFsZ1poLW2OL+oWto9YTt27Ni3b1/HLURTRUFu"
			"SZ2se3YUUSm7S5FdRyYjI2PNmjWsZLCNCet0dEVERMStW7de2Iza2L59+8GDBztuUdlUvKG8sLim"
			"q1ht3LgxPj6enXOGRVVV0YHU1NTw8HB29llKViQyMvLq1avdPOjvyuHDh7dt29ZxC7ODq636X4J5"
			"FpSVlW3evFlho7Z9+lUgsrAWsdkR0xNbKXtkmmlAYNTP2ujFlXH1xFY2L2xFpewuRbKODBvYxkSt"
			"TrUblU3lGZhadnmR2C0YFlVVhdocILqG2cHVWv0aNt9T0JvACAQC0UtBEwACgUD0UtAEgEAgEL0U"
			"NAEgEAhELwVNAAgEAtFLUfkUEEEQeXl5zFXk1QtBEEVFRampqRr10gMaGxtxHNdCYc+huLiYJEnt"
			"1EySZH5+PoZpywlHc3MzTdNqjFVra2tFRYXmgp+fn6/p5GptZ9QEdXV1zc3NvaSxbdTX1zPHcw5N"
			"K3+5bMuWLdXV1VyuZj8jguM4h8Ph8bTtaVTAcRwA+Py/02uUBEHQNK2dmrUt0WrPr6YL5iUkV9ty"
			"pFH+jh38BaEoCsOwsLCwjhtVJtvc3NzX19fZ2VmjmqKiory9vUePHq1RLz1g586dQqFwxowZr1pI"
			"N8jMzDx69KhCgrWEtWvXBgQEuLu7v2ohciQSiUgkCg4OVpfB6OhoR0fHiRMnqsugAnfv3j158qRG"
			"kxsVFeXj4+Pl5aU5F9rDkSNHampq5s+f/6qFvDzKy8tjYmIUNmrLJTkCgUAgXjJoAkAgEIheCpoA"
			"EAgEopeCJgAEAoHopaAJAIFAIHopLB/5kj5J/HVPfHoVr5/H+3NmjLbQyJNiVG3asV2HLj9pMRjk"
			"M23W+y6GDw8t33S5FoDDM7AcPmXOdE/TgtjIWKPFob50asy2A1dLuTZ+sxe846gHAECVnl4XfjyX"
			"BJoGDgeAN3TaYrs/99cGLp/uJKRqk7f/cNf1m/lj+vRgymu6smVDWcB3UwfxgKo8u3FH67RvAq24"
			"QBaejNxS8154iBMfAPCMA8s3J9cBcIWmQ8aHzHTP2dpZTnDkIpdHDNmKx82aYHhZoR3Ba0P6JHYM"
			"jDGrlexz22I1Tr/4yp6dsamV3AG+IfOCnERY86Oze/cnZFRxrTw/mjNtmBgDqjEvJel0Uo3bl7NG"
			"CVWlwxgDhSP7PDwUfnFQ2LyR3Vtck8hSyKt74a+RWaPCpjvx8bQ9Ky8N+m6xtyHx6PfIU2aL/Quj"
			"tpS9Hf75uLY2Ew8Or/ip6K3wL/yMm5VIbrfM1TVzmjR7tt9Adp/XV2yqKF8eO6OWx4l7953OqBHa"
			"+kyb/b4Ly+phtHCE9KTcIDAsUrXMYu62g/auoVSy0vwi2lHSwScZZCgfjJSUVueIOxHnozdXBq5o"
			"W9ALzziwOsnqq8/HCjr3Hd2rii6/sLv4344DwcTX5Es5yJPN4Rn0c/IPnvG2vX57+gEAdD2mvFkV"
			"G995qPnMx5DZTGWwKmgia8/y//5RZus90jBjd9gP8Yz1ztVC06XoiGTL2REbI2bb3oj6IaGKaCjI"
			"Eb27SSLZErXA7eFP3x8rIZuLs3IqcFnq7h+vDFi4PjLE4MS6g9lEW0ssAr7dKpFsCupbNWDmzxLJ"
			"ls/GOvp468ZtOvhQ1nhz57abA8eN6MnoDwBk1aPMwkYaAICWlmU/LG6hAYB4knDi9oOkkyktAABA"
			"1+dni97dJJFEh03Cj6zeW+mnIMdHR5lsxeN+zTRRbIfH7c6BYSeabosVWfj797uq3vpmw9rpBnFR"
			"+zJbqxIjV8bpBy3fELng9fQfw48Vk3jmoSjJ5az0v9IrOrwmwkwH40i8oSDjcbfXpwRKMa8VZoLS"
			"SylFJOAPLiddO3P+jhSo2jtXsjGxoKHgYeqJmDPyNc+lt2OPJmdkl+PKJT+zHP21V9H26DiWhcpo"
			"qjx2RO3F9StjeZO/XrsyxO7OuhUxeSxfi2S0sKSxOCunAqcopkWlxdx9B/KuocQBqAgWoh1mB29U"
			"LAmVpcWIeIHoNdPSuLNZOACALD0hvsbc0YDRd2QMl5TCQJD+dKk+uevtG5cFiS+sWXmskGwXI5FI"
			"JNFz/QMVhxqWoz+wnAB4tv+KPvZb9OIPJ3naGlCtreyKtLvgjU2EjqGhno6h/eTPv53q8OwVDa6B"
			"ne8Iy9I8+TBAlqdnG4waO0Co7+jrjt/PaFJpUuQ1/1PLsz9GRf18zyt05tAXWgWeITcr4bp49hK3"
			"nDPXGjr9IDB1cR7QUskYfLqSreo4RmC6JZMjdAleOsvTTEdka9ePR1IN187kuM/6l0tfHQPbgLlz"
			"hhk20/yh0yMiQwPs9TutHstIh5Iju6WEydO8lotdB7dkpDcShTcyzfzdS25m4rKMzAoH10E84Oh7"
			"eNDnTj0hAKDhWnzGwJH2XADlkp8hEJkYCYFmOTepqryma6ez3GaFDLMQmQwOnDdelnSO7ZKXii0s"
			"oVRZfMK6mJ/vgFQtuatgIZgQKgcjxdJiRryov/+4PtfPZ+EAsrRztyz8/cybutN3VA0EAhOnoNCJ"
			"0hNxj9Q4ALO7l6NjZGaS+tO0RYeekNYf/aShVeH7vL3o06yoJcGxNqP8gqYFjRRBClBld+L+kEJz"
			"0fX4cs8ljny4DgAgbZHqmOhxAEBXV9AqbaHASIUirK/v3MDDH+2xXnXMVb3Xvq2pp2/2f2ump/mT"
			"X/dfqho32QSAJh4nbt10n6h8cK920tcjFO8+0Kpkd3EcIzDd0omJh3qJAai6G7ti8cnLBjecqxWZ"
			"mraFi2frN8NW1YGKXvWLLiseKUvplpQOKORVz6HOvvR6RmlTmtBrydjM9SnZOeST/i5OOpBNk4aj"
			"J+gej0uf+ZnVhVNFI8aPTI19jmGauLdvycLjLYU5DaNWfWrGrlCZlZcJAEBWl1UZmlm0vQvPFVv0"
			"rbtdToItu3fjO7fQgZcCAEBWMS1KjdkW8/McyLuGMgesJSOeYcQcjJSXlrKI0+Z+fmZLzmXiDrLz"
			"qdb+n4jp6grFvlOvpIq7GggAMGML05bscvLZypGYeGzoqqfLx/cA1kM5//U5kt/3LvNpit3++xPN"
			"XEkKBk5YKjm0Z/WHdrmS0IikagoAOBjG4wlNnKav+2XJ8KfLm2JcLkmQAECTJIVhz2t8a1ZcUt1r"
			"/bPjznZvJfXnw4Gmv05damxO37/zUjmRlnC+vE2reIjP2DEDWuttAt91YtzL5aiS3cVxjMB0X6/0"
			"wZHVu6TTvg2y4ekIeDLZ07WliZaGJpmqgxS81vBZH8kChbzqODuJs/86fpNyH2Xp4W54/8Ll+zpO"
			"roYAADJc6DPJ/k7cn4/PJLb4TLShieddeHB4ziHRWyV7YnYE5kr2qljwvqumVsttCYWC1pYWuTtc"
			"KuMJhexPopVVrhKLut0o5h456IZkxFM4SkpCeWkpjTgm9vWzvH3udsq5e3b+3sbAYdHrOCwGAoDW"
			"hkauvj4GHL7LzJ8kEolkW8QLjP7AcgKQ/vnz3FmR51tMrAeY6hI1VbWa+A8AT90+f3VCDehZerzz"
			"yQSzh+mFJABm5joxMHDyhLFuHZbM45pamtYU5hEAsoJCqXl/Q5WNwB8eiL742mfRUTP4v22KK+7p"
			"FKDT15gsK2kGAMBLS+qMxH0akhMeun4Y5OnhMXzSx282Jp3JJwEAE1k5u4344D/jyw4cSm9VtKJa"
			"9vOPYwSmm+qJwvi1GzLHrVg6VowBZuo8BL91vYgEAKr02NK521UMkgyvxX1ZHskGxbxifVyHchJP"
			"N7mMsuAZjnCHxFMNg936t51W0aA3cvIbObvDEwT+AdYYu/tOfJGxfnNdHatCZVaeXKSJqzN980ou"
			"DgBUZfK1ksFu9qw/HaO0cjExw6ID62LuoQP2knsvjA6uf095SQAolpaKiBt7+w28vfWXdAd/L0NQ"
			"1uswxpiCdTUQADTf/z2+bJjvEDWmlNXkIXzdy4P73dopvpHA6+f31RRnTdQU32ly4MnVoYsuDjRp"
			"zcvTm7JyML/mtPJdDb2njj62KXT5ACq3bvyy4aqe9CAe/7bxjOVciZdxX3rx1MQvfz7j+f1ElncF"
			"OqHjGfzRqVULF12xFZblEuO/nAeXVue9EbzKy0MAAGT/7NgVCTkfuMr35tlPnekwd/fJKRun9u90"
			"7d2FbFXHMQLTLe1UybHI9dfrB9et/yIRMNGouStCFjmFLV94z86kKa+8/ydr3JQnk5kOPTtrxSML"
			"E59eiQLXatKy5YFW3dLWsfE2rva884ajbHiAmQx3M96d4+zAB5CfKAmcAsdgibkBb5pyyp5vR35p"
			"AhO9yAAAIABJREFUTEsbcesZYcquotk0FWTJAAC8wR+Hjly5ev6X1ibNhdU2s9d463dlqwuUWDRs"
			"YVXMPXfwgpJ7A4odfEE/p5rAuM4lUXNKeWmpiLihl79D9I+CRSP0AAD4zoq9zkBs1NmlGLv31CJj"
			"IHha1QRv4OQVoR5CSGm/BQTAcw5Zv9BLt6dtV7WEfExMTFpaWocNrbXF+QWVzaRaF6qPjIxMTk7u"
			"6KWuJL+goono8kiyqaKgsLJFrWo6sGPHjn379nXcQjRVFOSW1MlezG6PZbMITEZGxpo1a9hZqy3O"
			"L6qW9sAr2yMViIiIuHXrVneP0hzbt28/ePBgxy0qA4w3lBcW13TV4o0bN8bHx7NzzrDIoipSU1PD"
			"w8PZ2WcpWZHIyMirV69286C/K4cPH962bVvHLcwOznowYhlxRt9Rz5jClrKyss2bNytsZH/7SGDU"
			"z9qop9MMey8iC2sRmx0xPbEVq0em1QVXT2xl88JWeiybdWDYWWOZTKbXl1MGrwCVAeYZmFoaqNUV"
			"w6K6i1n9knsBzA7ejT7HKuKMvqOeMeWFQG8CIxAIRC8FTQAIBALRS0ETAAKBQPRS0ASAQCAQvRQ0"
			"ASAQCEQvReVTQARB5OXlMVeRVy8EQRQVFaWmpmrUSw9obGzEcVwLhT2H4uJikiS1UzNJkvn5+Rim"
			"LScczc3NNE2rMVatra0VFRWaC35+fr6mk6u1nVET1NXVNTc395LGtlFfX88czzk0rfzVyi1btlRX"
			"V3O5mv2MCI7jHA6Hx9PI56VfBBzHAYDP/zu9RkmSJEVR2qlZ2xKt9vxqumBeQnK1LUca5e/YwV8Q"
			"iqIwDAsLC+u4UWWyzc3NfX19nZ2dNaopKirK29t79OjRGvXSA3bu3CkUCmfMmPGqhXSDzMzMo0eP"
			"KiRYS1i7dm1AQIC7u/urFiJHIpGIRKLg4GB1GYyOjnZ0dJw4caK6DCpw9+7dkydPajS5UVFRPj4+"
			"Xl5emnOhPRw5cqSmpmb+/PmvWsjLo7y8PCYmRmGjtlySIxAIBOIlgyYABAKB6KWgCQCBQCB6KWgC"
			"QCAQiF4KmgAQCASil9KdR77w7N9Wb7gonrYudCz7RYe7AVWbdmzXoctPWgwG+Uyb9b6L4cNDyzdd"
			"rgXg8Awsh0+ZM93TtCA2MtZocagvnRqz7cDVUq6N3+wF7zjKP6SIZxxYvjm5DoArNB0yPmTW27oX"
			"1klqA5dPdxJStcnbf7jr+s38MT1ZF77pypYNZQHfTR3EA6ry7MYdrdO+CbTiAll4MnJLzXvhIU58"
			"pveZ7jlbw4/nkkDTwOEA8IYGR342hpOXknQ6qcbty1mj2laopEpPr+u827TFdn/uV4NqMrctVuP0"
			"i6/s2RmbWskd4BsyL8hJhDU/Ort3f0JGFdfK86M504aJMapWWThVpIPDM+jn5B884217+ZfmO4eB"
			"BUSWQl7dC3+NzBoVNt2Jj6ftWXlp0HeLvQ2JR79HnjJb7F8YtaXs7fDPxxljAADEg8Mrfip6K/yL"
			"sa1XOzeqQ8O7q4fZVFG+PHZGLY8T9+47nVEjtPWZNvt9F5Z5YLRwhPSk3CAwLCqPfjcdtHcNpZKp"
			"RsW6Q3RESQefZJChfDDi6po5TZo922+gDgAAVZkUvbkycEXbslx4xoHVSVZLxhev/6njrqY3ujTf"
			"Vt5P89qhj7VnGgBA12PKm1Wx8QqjSjeWgGfCfmAhc/+3YfuZm3dyKnFNLAgG0HQpOiLZcnbExojZ"
			"tjeifkioIhoKckTvbpJItkQtcHv40/fHSsjm4qycClyWuvvHKwMWro8MMTix7mD20yWS6fr8bNG7"
			"mySS6LBJ+JHVv9439/HWjdt08KGs8ebObTcHjhvRk9EfAMiqR5mFjTQAAC0ty35Y3EIDAPEk4cTt"
			"B0knU1qUed9b6fftVolkU1DfqgEzf5ZItnzmI8w8FCW5nJX+V3pF++sYmEWAwm5jHdWjmm6LFVn4"
			"+/e7qt76ZsPa6QZxUfsyW6sSI1fG6Qct3xC54PX0H8OPFZPKw6kqHds3LgsSX1iz8ph8jU2FMLCA"
			"UsxrhZmg9FJKEQn4g8tJ186cvyMFqvbOlWxMLGgoeJh6IuaMfM1z6e3Yo8kZ2eXSos6N6rg0Wff1"
			"MJsqjx1Re3H9ylje5K/Xrgyxu7NuRUwey9ciGS0saSzOyqnAKYppUUUxd9eBvGsocQC4krpDdITZ"
			"wRsVS6I94tFfexVtj46TL9mOGb9mWhp3NgsHAJClJ8TXmDvqNyrsysZ8mxCK0cfa/UokEkn0XP9A"
			"xVHlBc/F2Y4tZPHJnw7W2znqaW6FUbyxidAxNNTTMbSf/Pm3Ux2enb9xDex8R1iW5smHAbI8Pdtg"
			"***************************************/RkX9fM8rdOZQgVrlZiVcF89e4pZz5lqDcu+K"
			"R/CHTo+IDA2w1+8ihmpVzRG6BC+d5WmmI7K168cjqYZrZ3LcZ/3Lpa+OgW3A3DnDDJsJ5eFUmQ6B"
			"iVNQ6ETpibhHxPPCwIKneS0Xuw5uyUhvJApvZJr5u5fczMRlGZkVDq6DeMDR9/Cgz516QgBAw7X4"
			"jIEj7bnA0encqA42e6JHVVObrp3OcpsVMsxCZDI4cN54WdK5/G6OoO2VS6my+KSrYmbpgFQtmW3d"
			"IZ5BqByMBCITIyHQT2uOa+0/rs/181k4gCzt3C0Lfz9zTMWurMzLj+zUxzQJuwmAqkzcvKvorcVT"
			"bbiaK6I+by/6VBy3JHjONxuPFohd7EUYAFV2J+6PP34/uGX1b+Wevo7yOElbpDq6ehwAjq6uoFXa"
			"0h5hmnicuHXTxh+WL9lTOylohA5gfX3nBtJnrlvPCHFV77Vva+rpm/3f8vX0d8s/c6ltAmd47ynq"
			"VI2Jh3o5mXGpuhu7YvHJ7w1uqKgVmZq2pZ1n6zfjbXuuYjjbDlSWjnajxhamLRXlpNIwsEEhr3oO"
			"rval9zJKb6QJvWaM7f84JTsn40l/FycdAJo0HD3B5GpcuoyqvHCqaMR4Rw6jUa8/6z890sNoattm"
			"srqsytDMou1deK7Yom9deTnrCaBzC+XrdpNVTIsqi7lbDuRdQ5kDdNbfA4yYJUET9/YtWTjv38HL"
			"rjt94N++tCxm7udnlnIuE2+9ez7V2t9XjDF2ZWGeOQ6397G21R/nzZu3YPmRh2qfD1hNAE3Xf9l6"
			"EbOgbl5+JKMK/zx1u1rdMgAAQDBwwlLJoT2rP7TLlYRGJFVTAMDBMB5PaOI0fd0vS4Y/Xd4U43JJ"
			"ggQAmiQpDHv2PwYHEw/xGTtmQGu9TeC7TnoA0JoVl1T3Wv/suLOFauwIHGj669Slxub0/TsvlRNp"
			"CefLKaXee4q6VUsfHFm9Szrt2yAbno6AJ5O1yj//QbQ0NMk4KsKpLB3tAhsaufr6mNIwsEIhrzrO"
			"TuLsv47fpNxHWXq4G96/cPm+jpOrIQCADBf6TLK/E/fn4zOJLT4TbWiCVmzUs8+V9FCPYlPlGoVC"
			"QWtLi9wdLpXxhEL25z/KKleJRV2VxawmB92QjHgKR0lJcHjOIdFbJXtidgTmSvbea7/tiIl9/Sxv"
			"n7udcu6enb+3MQBjVxbmlRTq0z7G4bvM/EkikUi2RUxzUPtnOthdAegM9Jo43LSlplFG0XhTY7Mm"
			"Tirw1O3zVyfUgJ6lxzufTDB7mF5IAmBmrhMDAydPGOvWYck8rqmlaU1hHgEgKyiUmvc3fNYITGTl"
			"7Dbig/+MLztwKL0V8IcHoi++9ll01Az+b5viinsqW6evMVlW0gwAgJeW1BmJ+zQkJzx0/TDI08Nj"
			"+KSP32xMOpNPMr33OBLqUf0UojB+7YbMcSuWjhVjgJk6D8FvXS8iAYAqPbZ07vZ7lGI420QoS4ec"
			"5vu/x5cN8x3Cr1MaBhYo5hXr4zqUk3i6yWWUBc9whDsknmoY7PZ0RWzQGzn5jZzd4QkC/wBrjGY2"
			"qp2e6WE2VS7SxNWZvnklFwcAqjL5WslgN3vW/ysrrVxMzLDooLqY1eOAveTeC6OD699TXhIAAHyR"
			"sX5zXV2HIdvY22/g7a2/pDv4e3W+Iy/flYV5Rp229zGNNPgZrGYUfY+Pl3kAQOu55Rdu1/p9OMZU"
			"A0r4TpMDT64OXXRxoElrXp7elJWD+TWnle9q6D119LFNocsHULl145cNZ9xr4dlPnekwd/fx4R7n"
			"z1jOlXgZ96UXT0388ucznt9PNOvBP6o6nsEfnVq1cNEVW+H/s3fmAU2cW8M/mYQQlgSRsCggghSs"
			"F4SCC6CAWlBR6IK29VLFV6q9rhXrtRvoVYGGugBW1NTlrbuWV71UQEFwR2vFBREQAZV939ckk5l8"
			"fxAQMokMIbH0Y35/DvOcc56zPM/MkJlTXSSevXEl3NxW/F7AVjdnJgBgowviNycXfuLQV3vCgqiF"
			"o5X4lJ745W9RKrG6C7zyAm/nvZbxzTu/TgWE47Jic+Bau5DQNU+tDNqLa0Z/sd1Rg90px53ywnEJ"
			"zTq2fuXvEjFjrO/mYGdmXfwVghs+X2mrzFUKw8LBmnGN7WLBAMRgsqP+kUJ7Gw0AUddfmXZ+05HU"
			"Ip9ZhrRquZP6T8A/NACvu66UPcSpgigdAIAx/vPgqVu2rdpobtBR1mARtN1dpx9R/U6TKFGu91Wp"
			"YJAmDwdkC3z1KLtGv8S+KdGT/YI21HxJSJ8nvGw3b5voXcy1U6RbsUTmVAuT/sT3rPOSvjXGggzp"
			"ERoAMOwDd65x01Lp3BW1kI+Li8vKylJrl3qJRMLj8dLT03sdEDZXlpTWtov7HYm115aW1XWqy7CD"
			"Bw8eP3689xFxe21pUWWzSF0aB09OTs727dtJnSpsqigpbxC8PqDAnaTD0R/h4eEPHz4ctBiVceDA"
			"gVOnTvU+onCqaGtNWUWjgHC8L1FRUUlJSeSUEySSSObMzMywsDBy8kmaLAuPx7tz584AB/1dOXPm"
			"zP79+3sfIRa4yrL/LYgnQXV19d69e2UODrVPvzI5JuYcMici2lyzwTxmHzB0ba6ZxdtUqE6YeqPM"
			"9XofUOBO0uH4+6NwqgxdQ1NdlaoiSFR1Mqve5GEAscBVmv1qFq8s1JvAFBQUFMMUagOgoKCgGKZQ"
			"GwAFBQXFMIXaACgoKCiGKdQGQEFBQTFMUfgrILFYXFxcTOwir1rEYnF5eXlmZqZatShBW1sbiqJD"
			"0LA3UF5ejmHY0LQZw7CSkhIEGSoXHB0dHRKJRIW+EgqFtbW16nN+SUmJuoM7ZItRHTQ3N3d0dAyT"
			"yXbR0tJCXM9pEolE7tmxsbENDQ10uhKvMg0AFEVpNBqDMdR+jQooigKAhsbf6TVKDMNwHB+aNg+1"
			"QKs8vupOmLcQ3KEWI7XydyzwQYLjOIIgISEhvQ8qDLaxsbGnp6e9vb1abYqMjHR3d582bZpatSjB"
			"oUOHWCzWkiVL/mpDBkBubu758+dlAjxEiIiI8PHxcXJy+qsNkcLn8zkcTkBAgKoERkdH29razps3"
			"T1UCZXjy5ElCQoJagxsZGenh4eHm5qY+FUOHs2fPNjY2rlq16q825O1RU1MTFxcnc3Co3JJTUFBQ"
			"ULxlqA2AgoKCYphCbQAUFBQUwxRqA6CgoKAYplAbAAUFBcUwheRPvjrvxm48+gSVAAB93ILt38xV"
			"/gP1isGbsi4cPn3rVafuOI9Fyz6eyM4/HRpzqwmAxtA1nbxg+WJXw9J4XrzeumBPSWbc/pN3qugW"
			"XkGrP7B9/SHFjhdXjp1Izqmnm7l+tnzRJC7IiNRXyuz227G7q31+WDiOAXjdlaiDwkXf+pnRAStL"
			"4MU2fhQWaKcBAGjOydC96c0AdJbhu7MDlzoV7gv7vQgDiQRoNADGhADeV9NpxRlpl9MaHTcuc+nu"
			"9Sgzbtk8q8bEHfwmv9DFdiy8Kf3AT08cvl01feCN4bGiLl/N1Km4ffRQfGYdfYxn4Ep/Ow4i6yQE"
			"b5LnTtlw6COy7h2RfzrsxriQlVMH1rZYnCcTV6eyX3l5LiGL7TTQrKNbbo77YZ07W/ziHO+S0Trv"
			"ssjY6jlh62d2RU78/MzmPeXvh309Q3in76T6SqZrGdnNDwryGkvu8/qyU+WUSH2n1/ky9djxyzmN"
			"LEuPRUEfTyQZBsIMpwgSpAKBIFG+9weooKc05JqMtxHzjuI1cgp8vm6O/MWob2rhdWnRe+v8Nnf1"
			"6kJzTm5LM9swu2Lnnt6nGt7vV7y+TAIDAGhNWhEx/Xn4ScnizZ9P0AQQPTgU8cw1dImdKn+6Si6h"
			"sZr8B0/qDFxnz5kzZ/ZUS2213De034wOTzcNCo8KD7K8H/lTcr24tbSQ82EMnx8budoxf8+PFyqx"
			"joq8wlpUlHlk1+0xa3byAnUv7jhV0N0nE69P5W1J1PEP3c1b/Y/sXWEXKlpkRCppGVb/IresTQIA"
			"IBFUF+RXdEoAQPwq+eKj52kJGV1NdCUtJQWcD2P4/OiQ+ejZbcfqvL7bx+fH+I+sH7P0Zz4/9isP"
			"Vu7pSP6tvOw/s2t7vY4hM+7XbNzMw10rMeZUvqjtwaH9D8bOnDLw1R8AJF2+wsrO/Xi4/v1vd0cs"
			"1k2MPJ4rJDgJk+9OYjgII9HW0pyX8rrZvRlcNq61RsyqmxnlGKDPb6XdTbn2WAB40+PbBQiX2Vqa"
			"n3kxLkXa81zwKP58ek5BjaC876RQWcnR37iVH4hOrCNnG2GqUt+Jm27s3BLP8P0mYkug1eMdm+OK"
			"Sb4WSZhhZVtFXmEtiuNEiQqSeaAKpKUhRwGgcvOO4jXEAm+TTQkFqYXov2NYlXglDwUAEGUnJzUa"
			"2+q0yZxKRnyXIT1a+Hw+P3q5M6O15GnawZ0n8kQAgDe+yilpkf/altKQ3ABqaxpoXHNTbc0RNtOn"
			"26rnU+NoW7tYk83W1mRb+67/bqHN632OrmvlOcW0qli6DGA12QW6LjPGsHRsPZ3QZznt0tNa76YU"
			"Oi3758SRmrqWPiuWT2J3yIqElviNC1d+/+91yxd9uvboILo2AgCal3yPG7TBsTDlbmufPzANJ9qP"
			"6awjLj4aExaH84J9rHXkd2l9PY7jtupL0yu7IiN/fuoWvHTCwK6vZaCxJgZsWuZqpMmxtBrFwHCi"
			"k8Ty3UkIB3HkYHOxO641XIfxnTnZbeKy+7lG3k6VD3JRUU5urY3DOAbQdJydJVcvvRIDQOvdpJyx"
			"U63pQNPsOymCZCbHQI8FEpJ7k6LMa797Oc9xWeAkE47BeL+Vs0VpV8m2vJSdYSWuSOIrBck8UAWY"
			"YpP7yTsKOYgVLkYyqUU395454t61PBRAlHX1oYm3lzGi4FRS4mWhaU7ytrwedboAVXjKoCD5CAjT"
			"0NZreHIlJf1JBv9m2PFtXgaqvwkYMWftl3mRGwLiLVy8/Bf5T+VABuDVjxP/K4CO8ntJNa4bbDXg"
			"HgCAoFOgaaBNAwAtLaZQ0ImDHgKANdQ2cQwNuwxjWHotsQQQ9RUJgmwMN52/dbOb8NyG9dcLPrdT"
			"+nZKmHn5wej3l7oav/r1xM36mb4GABLxy9R9Mc/Edc+fNs3/Zgrp5n5yxo30XOF35rOj5lsvOAzy"
			"lh3hTnDjAuDN9w/Ho77fj2+9KuskceHVvu4E0ANiOHTKb8mOFGUoa5VMXLVtmq2r7uVUtWex3DbM"
			"yN2ZUVCIvRo90U4TCiQYe9pcrd8Ts5d+ZXb9UvmU2VMz4wHhTnAzej2pf7wOokT89PiGNb93lhW2"
			"umz9kuSDSmLm5QIAYA3V9Wwjk6534elck5HNj2owsCT3bnzfGdowMgAAsHqiRIG+vGQesAu7SkOe"
			"AtImU7xGj7gYKUgtxNjLy2jD1VzURnQt09z7Cy4Cr2ROhev9iu8JeU8DSIQ7I3irPwBoO335KXy9"
			"+zeP3cZqmCi5CmFO3XA26dzB3TF7ljs03L72WKQGSwCYY+du4p8+uu1TqyJ+cHhaAw4ANARhMFgG"
			"dot3/LJhcnd7U4ROx8QYAEgwDEcQ6SZG02QyRCKh9LJU3NnaLpIVCQA0XS6XBYiOrhYuUnpTpUH7"
			"n5dutnVknzh0s0aclXytpstW7rseM6aPEbZY+H1oR77Dk5xxwrzEtOZ3RhckXiG2i1YCwfOz2w4L"
			"Fn3nb8EgOokm352yvmvUILpXeWTiqmlvxy348/cHuJOLqbMT+9n1W8807RzYAAAilOUx3/px4h8v"
			"U1I7PeZZSMQS2Un1Wt9oDPvA6H38o3EH/Yr4x56SjLBs5kllsVhMYWenVB0qEDFYLPIX0fIyV45E"
			"LQXeV5mCAZhM0Q1NTkooSi2E6+ll+ujqo4yrT6283fUBCKeSEN9zl0DTmLh0D5/P5+8P7/q/AgBt"
			"xPTVK0YlRZ0vH/DT1v4htQFgFfE/LFoe80eLsKauSaKjq6uO/wGgmQdWbUtuBG1T5w++mGuUn12G"
			"ASBGDvP8/HznznDs1TKPbmhq2FhWLAYQlZYJjEezu8xBDO3fRR/eK8cAAK+6sGnFgccPZEQqaZrm"
			"SH2surIDAACtqmzW445oTU/Od/jU39XZefL8z2e1paWUYACAcMzsHad88q/Z1SdPD+T5kuw4NP9k"
			"9I13voqOXKLxW0xixSC3AHFZUsTu3JmbN83gInKc9BSXdScAyAlHxUjCSOXvSmXjioxwmEBLvdw+"
			"0cWEwZ7iBKmXWsc7ju5a2CWgPdX3vcIjYclMbx9zREKclBw0OPo6Hc3NpCqGmHlSIw0c7CUPbheh"
			"AIDXpd+tHO9oTfqGUW7mIlyCRBv5yaw6BeRNHr4QClznqfyUAJCXWvruXmMf7fsl28bbjd1HrvRU"
			"EuLfXOPISM81X+hfPJkhVPF/AEg+AqKbTJvtdC5s45w4nG7k8fUi50E9lVaAhp2vX8K24LU3xhoI"
			"i4u1F2wZr9F4Wf6pbPeF0y7EBIeOwYuaZ38/uftZi4Z94Fq7kNA1T60M2otrRn+xfZKjuP5yb5EA"
			"Su0Bmq4Bn13aumbtbUtWdZF49saVcHNb8XsBW92cmQCAjS6I35xc+ImD9GyG9cKlNiuOJCyIWjh6"
			"IPfe3eM+WiuISjFdwXfTHylZtzB1488prj/OU/pXV3jlBd7Oey3jm3d+nQoIx2XFZhknOWqwO+W4"
			"kxgObStz2ZFlqd03rEA3m/99qJ+ZklYCw8LBmnGN7WLBAMRgsqP+kUJ7Gw0A6U0G085vOpJa5DPL"
			"kFYtd1L/CZA+BpLeQUsEbaj5khByj+GIUwVROgAAY/znwVO3bFu10dygo6zBImi7u05/svqbJlGi"
			"XO+rUsEgTR4OyBb46lF2jX6JfVOi8ZLi1GK7edtE72KunSLdimWz0MKkP/E9u3TPIyAAhn1gxBTp"
			"YYQ766vlaQ/2qn7uilrIx8XFZWVl9T4ibqutqG1DVdqonsfjpaen9zogbK4sKa1tF/c7EmuvLS2r"
			"6yT+QdhUUVLeIFBCZG8OHjx4/Pjx3kfE7bWlRZXNogGJeavk5ORs376d1KkEJylwJ9F3hJHkCA8P"
			"f/jw4UBHqY8DBw6cOnWq9xGFaYK21pRVNPY346ioqKSkJHLKCRIVJvNrMjMzw8LCyMknabIsPB7v"
			"zp07Axz0d+XMmTP79+/vfYRY4EqtHIpQs3gSVFdX7927V+bgAD79StfhjlL75QSTY2LOIXMios01"
			"k/uYnak3ylxPKZFvhq7NNbMYvJihAcFJCtxJ9B1h5P8vKEwThq6hqWp/90aQqDCZVaWAon+IBa6i"
			"leOtiFcW6k1gCgoKimEKtQFQUFBQDFOoDYCCgoJimEJtABQUFBTDFGoDoKCgoBimKPwVkFgsLi4u"
			"JnaRVy1isbi8vDwzM1OtWpSgra0NRdEhaNgbKC8vxzBsaNqMYVhJSQmCDJULjo6ODolEokJfCYXC"
			"2tpa9Tm/pKRE3cEdssWoDpqbmzs6OobJZLtoaWkhruc0iUT+y2WxsbENDQ10uno/I4KiKI1GYzAG"
			"8GvUtwOKogCgofF3eo0SwzAcx4emzUMt0CqPr7oT5i0Ed6jFSK38HQt8kOA4jiBISEhI74MKg21s"
			"bOzp6Wlvb69WmyIjI93d3adNm6ZWLUpw6NAhFou1ZMmSv9qQAZCbm3v+/HmZAA8RIiIifHx8nJyc"
			"/mpDpPD5fA6HExAQoCqB0dHRtra28+bNU5VAGZ48eZKQkKDW4EZGRnp4eLi5ualPxdDh7NmzjY2N"
			"q1at+qsNeXvU1NTExcXJHBwqt+QUFBQUFG8ZagOgoKCgGKZQGwAFBQXFMIXaACgoKCiGKdQGQEFB"
			"QTFMIf+Tr7bCtN/+Ly1Xd+5/1s1Qzzfs8KasC4dP33rVqTvOY9Gyjyey80+HxtxqAqAxdE0nL1i+"
			"2NWwNJ4Xr7cu2FOSGbf/5J0quoVX0OoPbLUVCNBHoOPFlWMnknPq6Wauny1fNGlE/umwG+NCVk5l"
			"4jXX9+7NdV6/arr8jiK9ab8du7va54eF4xiA112JOihc9K2fGR2wsgRebONHYYF2GgCA5pwM3Zve"
			"DEBnGb47O3CpU+G+sN+LMJBIgEYDYEwI4H01nVackXY5rdFx4zKXXq0e+xrpJE7ZITMyInBEqszE"
			"+gUr6vLVTJ2K20cPxWfW0cd4Bq70t+MgMj7hIoC3ybFLQThoDN1Rdt4BS+ZYSz8N29cNJBDnycTV"
			"qexXXp5LyGI7DTTr6Jab435Y584WvzjHu2S0zrssMrZ6Ttj6mV1TFj8/s3lP+fthX88Q3Tt55MKj"
			"Wg2rOV/8a561Vu+JD9Qe4lQ5JVLf6XW+TD12/HJOI8vSY1HQxxNHkLtgIsxwiiBBKhAIEvEmuck8"
			"MAU9pSHXZLnxpehBToHP182RvxjRtYzs5gcFeY193RCgr8ftxNei99b5be5q6IXmnNyWZvbv9TOY"
			"fatO646syq+tbvyn1wKybN470gKTBrtX4fWEHwBAy3nBrPr4JJmlxoNNnKY8SN4BCHP/d/3y8JSG"
			"0e/ZjxlkywqFtN+MDk83DQqPCg+yvB/5U3K9uLW0kPNhDJ8fG7naMX/PjxcqsY6KvMJaVJT1YCPv"
			"AAAgAElEQVR5ZNftMWt28gJ1L+44VSBWIADw+lTelkQd/9DdvNX/yN4VdqECbS3NedmAQ+vjX0L+"
			"t3XWp279r/4AgNW/yC1rkwAASATVBfkVnRIAEL9KvvjoeVpCRicAAEhaSgo4H8bw+dEh89Gz247V"
			"eX23j8+P8R9ZP2bpz3x+7FcerNzTkfxbedl/Ztf2eh2DYGSVoY/MSOdHMp4h0+hK0uUrrOzcj4fr"
			"3/92d8Ri3cTI47lCgk8wVK5disJxIOp7f+717VsuSLsYybiBBLhsXGuNmFU3M8oxQJ/fSrubcu2x"
			"APCmx7cLEC6ztTQ/82JcirTnueBR/Pn0nIIaYVU8b1/Z9G93hX0GZ3/6raj32y0Dt4c4VanvxE03"
			"dm6JZ/h+E7El0Orxjs1xxSRfiyTMsLKtIq+wFsVxokQFyTxQBdLSkKMA5MeX4jXEAm+TTYkej0d/"
			"41Z+IDqxrrsGCR4v5bxjWJV4JQ8FABBlJyc1GtvqEqpORFCJ911Afs3ubriHEwqvxxg+n8+PXuHt"
			"J7vUkFz9gewG0HL9+KkX4z8LmjXWQIuuriRC29rFmmy2tibb2nf9dwttXl+/0XWtPKeYVhVLlwGs"
			"JrtA12XGGJaOracT+iynXYEAaL2bUui07J8TR2rqWvqsWD6J3SEBAEBf/d+Wnc+9t34z03AQT8DQ"
			"vOR73KANjoUpd1v7/IFpONF+TGddHWGV1piwOJwX7GOt06dLqwIjyXmmf2isiQGblrkaaXIsrUYx"
			"MFyOOvl2KVTKNLDzD54nuJj4QvwmN5CgO641XIfxnTnZbeKy+7lG3k6VD3JRUU5urY3DOAbQdJyd"
			"JVcvvRIDQOvdpJyxU63pgIz0Dv35aw9Dpq6p6QhRp6CXx5SxR9FU2+9eznNcFjjJhGMw3m/lbFHa"
			"1ZIBJn9P5uKKJL5SkMwDVYApNll+fCnehFhhyTE5BnoskPRUN9Hj5aO9Z464dy0PBRBlXX1o4u1l"
			"3N5/kb9G0QLSt/BUBKlHQOirvII2Qf3lU6hmRc7Pqd+d2PnRKNW/ITxiztov8yI3BMRbuHj5L/Kf"
			"yoEMwKsfJ/5XAB3l95JqXDfYasA9AABBp0DTQJsGAFpaTKGgEwc9RI4ArPxWE8dQusgzLL2WWIIo"
			"A8TPfv3+D+G7oSGWg2psKcy8/GD0+0tdjV/9euJm/UxfAwCJ+GXqvphn4rrnT5vmf0OuHSFgDbWy"
			"RvbvmQHsWwh3ghsXAG++fzge9f1+fOvVftUpUAoZr4Xqmxh2FtRgYIPJuoGcaTJx1bZptq66l1PV"
			"nsVy2zAjd2dGQSH2avREO00okGDsaXO1fk/MXvqV2fVL5VNmT82MB2DqGTEK/xsee/FRleXaWJvX"
			"WUwICxl7iFPNBQDAGqrr2UYmXZlO55qMbH5Ug4EluczvO0MbRgYAAFZPlCjQl5vMA3VhV2nIU0Da"
			"ZIrX6BGzXyJ+enzDmt87ywpbXbZ+2d2jVZ7HJcZeXkYbruaiNqJrmebeX3AlxCJviScq7X8B6Sm8"
			"ns6RCHdG8NZFNkq/vU2uXjExhlgu2vnrkYPrXYT3bz7oUFbdG2GOnbuJf/rotk+tivjB4WkNOADQ"
			"EITBYBnYLd7xy4bJ3f3IEDodE2MAIMEwHEEYCgTQNJkMkai7j7K4s7VdBCAB1uQNexa3Ho682E8n"
			"5jdAg/Y/L91s68g+cehmjTgr+VpNl63cdz1mTB8jbLHw+9COZIcnuUaS8cxAEDw/u+2wYNF3/hYM"
			"Mur6VypsbaPr6CBy3UAKmbhq2ttxC/78/QHu5GLq7MR+dv3WM007BzYAgAhlecy3fpz4x8uU1E6P"
			"eRYScZfxDCvfjVu3b3Qt/y2+sOeKSEl7ZKcqtZHFYgo7O6W+QgUiBotF/iJaXubKkailKJlVpWAA"
			"JlN0Q5OTEjSGfWD0Pv7RuIN+RfxjT6UPaOR6HOF6epk+uvoo4+pTK293fVJFTiO1gHQXHk1j4tI9"
			"fD6fvz98EKs/kNwA6GZjzTTqXrxoEjbUNmEsDlsdH9BAMw+s2pbcCNqmzh98MdcoP7sMA0CMHOb5"
			"+fnOneHYq2Ue3dDUsLGsWAwgKi0TGI9mI/IFIIb276IP75VjAIBXXdi04sBTFGgMy/emOi0K/YJ5"
			"JvzEc4WrXx80R+pj1ZUdAABoVWWzHndEa3pyvsOn/q7OzpPnfz6rLS2lBAMAhGNm7zjlk3/Nrj55"
			"OltIbtpyjSThGfKIy5IidufO3LxpBhcho65/pR3PziVVT/J8V6NZrhvIzFomrsgIhwm01MvtE11M"
			"GOwpTpB6qXW84+iuyyoJaE/1fa/wSFgy09vHHJEAAPbyv5G//CnSGmHu7GzeXFrerVU5e4hTlRpp"
			"4GAveXC7CAUAvC79buV4R2vSmS83cxEuQaKN/GRWnQLyJg9fCAWu81R+SgAAaHD0dTqam6XXFgo8"
			"ru/uNfbRvl+ybbzd2HKLHCGsKUj/C0hP4alu6qQ2D8Ro3sqg65vCfT1xuuH09SFu6vglgYadr1/C"
			"tuC1N8YaCIuLtRdsGa/ReFn+qWz3hdMuxASHjsGLmmd/P1lTgQDQsApcaxcSuuaplUF7cc3oL7Y7"
			"apSldc3IZPa3wVlrIg7Y71/v3G/3VE3XgM8ubV2z9rYlq7pIPHvjSri5rfi9gK1uzkwAwEYXxG9O"
			"LvzEQXo2w3rhUpsVRxIWRC0cTeLeW8OeYGT/nulfbDd45QXeznst45t3fp0KCMdlxeb+1ClU2nip"
			"675TImaM9d0c7Mysi79CcMPnK22VuSBhWDhYM66xXSwYgBhMdtQ/UmhvowEg3aCZdn7TkdQin1mG"
			"tGoAALrZhDH5ERu+SzVqe9k0a700A/C660rZQ5wqiNIBABjjPw+eumXbqo3mBh1lDRZB290H2xNb"
			"jkR2p7xkVqGCQZo8HJAt8NWj7Br9EvumRE/2C9pQ8yUhPQ9oFHic7eZtE72LuXaKNoC8Itfl6vVV"
			"yUWedkskLCCSvoXHgoyeR0AADPvAnWvctGTnRBZFLeTj4uKysrJ6HxE2VZZWt6AqbVTP4/HS09N7"
			"K2muLCmtbRf3OxJrry0tq+skHCcKEDZVlJQ3CAZm2MGDB48fP977iLi9trSoslk0MDlk6d/I/j2T"
			"k5Ozfft2Fakjq5Qk4eHhDx8+HLSY3oiaKwce124OHDhw6tSp3kcUThVtrSmraOxPT1RUVFJSEjnl"
			"BImKkrkXmZmZYWFh5OSTNFkWHo93586dAQ76u3LmzJn9+/f3PkIs8AFkPymPE6pOvWuKLNXV1Xv3"
			"7pU5OICrNaaeiZmy2wx5JRwTc1JvGSDaXDN5T8mIAph6o8z1Bm8ZXZtrZjF4MQro30jSnlGNOjUo"
			"VTEaqrVN4VQZuoam/d4kDgiCREXJrDIFFP1DLPABZD8pjxOqTr1rCimoN4EpKCgohinUBkBBQUEx"
			"TKE2AAoKCophCrUBUFBQUAxTqA2AgoKCYpii8FdAYrG4uLiY2EVetYjF4vLy8szMTLVqUYK2tjYU"
			"RYegYW+gvLwcw7ChaTOGYSUlJQgyVC44Ojo6JBKJCn0lFApra2vV5/ySkhJ1B3fIFqM6aG5u7ujo"
			"GCaT7aKlpYW4ntMkEvmfJYqNjW1oaKDT1fsZERRFaTQagzGYl5nVAoqiAKCh8Xd6jRLDMBzHh6bN"
			"Qy3QKo+vuhPmLQR3qMVIrfwdC3yQ4DiOIEhISEjvgwqDbWxs7OnpaW9vr1abIiMj3d3dp02bplYt"
			"SnDo0CEWi7VkyZK/2pABkJube/78eZkADxEiIiJ8fHycnJz+akOk8Pl8DocTEBCgKoHR0dG2trbz"
			"5s1TlUAZnjx5kpCQoNbgRkZGenh4uLm5qU/F0OHs2bONjY2rVq36qw15e9TU1MTFxckcHCq35BQU"
			"FBQUbxlqA6CgoKAYplAbAAUFBcUwhdoAKCgoKIYp1AZAQUFBMUwh9ZOv5utR355+Jv0FKU3TeXnU"
			"yimD6qcoH7wp68Lh07dedeqO81i07OOJ7PzToTG3mgBoDF3TyQuWL3Y1LI3nxeutC/aUZMbtP3mn"
			"im7hFbT6A1ttAAC86vKOsN+LMJBIgEYDYExYtM7qjxNNfqGL7Vh4U/qBn544fLtq+ggltrz227G7"
			"q31+WDiOAXjdlaiDwkXf+pnRAStL4MU2fhQWaKcBAGjOydC96c0AdJbhu7MDlzoV7utrTgDvq+m0"
			"4oy0y2mNjhuXuXT3VJAZt2wu+5bMPAIiAkek9naMPqlO9kVdvpqpU3H76KH4zDr6GM/Alf52HKTj"
			"xZVjJ5Jz6ulmrp8tXzSJiwDeRrSLEA59BGRGjsg/HXZjXMjKqQNLBnGeTFydyn7l5bmELLbTQLOO"
			"brk57od17mzxi3O8S0brvMsiY6vnhK2f2TVn8fMzm/eUvx/29QzRvZNHLjyq1bCa88W/5llryUim"
			"axnZzQ8K8hpL7vP6slPllEh9p9f5MvXY8cs5jSxLj0VBH08kmT2EGU4RJEgFAkEi3kRM5gEr6CkN"
			"uSbLjS9FD3IKfL5ujvzFqG9q4XVp0Xvr/DZ3teVCc05uSzPbMLti557epxre71e8tKSlcaUxdEfZ"
			"eQcsmWOt0xNpAAAt5wWz6uOTZFaVAbSAJ0IqoVmWrr5+fn5+fvMdmK9y21mGank5oP1mdHi6aVB4"
			"VHiQ5f3In5Lrxa2lhZwPY/j82MjVjvl7frxQiXVU5BXWoqLMI7tuj1mzkxeoe3HHqYKuhoCIic93"
			"+/j8GP+R9WOW/sznx341w9bDXSsx5lS+qO3Bof0Pxs6coszqDwBY/YvcsjYJAIBEUF2QX9EpAQDx"
			"q+SLj56nJWR0AgCApKWkgPNhDJ8fHTIfPbvtWJ2XjDkerNzTkfxbedl/Ztf2eh1DZtyvuQay83B+"
			"1Ncx5IyWdPkKKzv34+H697/dHbFYNzHyeK6wPpW3JVHHP3Q3b/U/sneFXajAULl2EcNBGIm2lua8"
			"HHB/SsBl41prxKy6mVGOAfr8VtrdlGuPBYA3Pb5dgHCZraX5mRfjUqQ9zwWP4s+n5xTUCKviefvK"
			"pn+7K+wzOPvTb0WYrOTob9zKD0QnEvpqy4cwVanvxE03dm6JZ/h+E7El0Orxjs1xxSRfiyTMsLKt"
			"Iq+wFsVxokS5yTxwBdLSkKMA5MeX4jXEAm+TTQkFqYXov2NYlXglDwUAEGUnJzUa2+q0yZxKRnyX"
			"IVItB6K+9+de377lQhnWo5fP5/OjV3j7ya4qg1n9geQGoDnW1feDDz74YKZ+3SvJVH9fC7VsAGhb"
			"u1iTzdbWZFv7rv9uoc3rVzToulaeU0yriqXLAFaTXaDrMmMMS8fW0wl9ltOuUCTHbdWXpld2RUb+"
			"/NQteOkEld61oHnJ97hBGxwLU+629vkD03Ci/ZjOOuLiozFhcTgv2MdaR36XVoXjZB0zIDNprIkB"
			"m5a5GmlyLK1GMTC89W5KodOyf04cqalr6bNi+SR2h0S+XYRwyBk5IEuIdMe1huswvjMnu01cdj/X"
			"yNup8kEuKsrJrbVxGMcAmo6zs+TqpVdiAGi9m5Qzdqo1HZCR3qE/f+1hyNQ1NR0h6hQQDGFyDPRY"
			"ICG5NynKvPa7l/MclwVOMuEYjPdbOVuUdpVsy0vZGVbiiiS+Ip3Mb1aAKTa5n7yjkINY4WIkk1p0"
			"c++ZI+5dy0MBRFlXH5p4exkjCk4lJV460sDOP3ie4GLiC3IXBMozgLf+sPJLZ28gMyPe56rnHwcj"
			"5qz9Mi9yQ0C8hYuX/yL/qRzIALz6ceJ/BdBRfi+pxnWDrQbcAwAQdAo0DbRpAKClxRQKOnHQU2AS"
			"MtJzhd+Zz46ab73goNp7X2Hm5Qej31/qavzq1xM362f6GgBIxC9T98U8E9c9f9o0/5sppJv79TdO"
			"1jEDshPhTnDjAuDN9w/Ho77fj2+92sQxNOxyF8PSa4mlooGyWnXKb8mOFGUMyJReyMRV26bZuupe"
			"TlV7Fsttw4zcnRkFhdir0RPtNKFAgrGnzdX6PTF76Vdm1y+VT5k9NTMegKlnxCj8b3jsxUdVlmtj"
			"e3XFloifHt+w5vfOssJWl61fGpHLVGLm5QIAYA3V9Wwjk66rHTrXZGTzoxoMLMld/fSdoQ0jAwAA"
			"qydKFOiTTeY3urCrNOQpIG0yxWv0iIuRgtRCjL28jDZczUVtRNcyzb2/4CLwSuZUuN6veGLIEX0T"
			"w86CGgx6uj8i3BnBWwfXAp4I+bVclHPuXNYo3wVTVdm6qA/MsXM38U8f3fapVRE/ODytAQcAGoIw"
			"GCwDu8U7ftkwubu9KUKnY2IMACQYhiPImzwizEtMa35ndEHilYF1Un8zNGj/89LNto7sE4du1oiz"
			"kq/VdNnKfddjxvQxwhYLvw/tyLup33Eyjhm4vYLnZ7cdFiz6zt+CoclkiERC6SWzuLO1XaRokIzW"
			"Rg3SI0kgE1dNeztuwZ+/P8CdXEydndjPrt96pmnnwAYAEKEsj/nWjxP/eJmS2ukxz0Ii7jKBYeW7"
			"cev2ja7lv8UXvr5MojHsA6P38Y/GHfQr4h9T0PC+v6k2SGWxWExhZ6d0xqhAxGCxyF9Ey8tcORK1"
			"BpDMSikYgMkU3dDkpISi1EK4nl6mj64+yrj61MrbXR+AcCoJ8XLuEoStbXQdHQRoGhOX7uHz+fz9"
			"4ape/WEAG0DTzbNJNQ4ff6BUy28yoJkHVm1LbgRtU+cPvphrlJ9dhgEgRg7z/Px8585w7NUyj25o"
			"athYViwGEJWWCYxHsxVOAs0/GX3jna+iI5do/BaTWKHsFqA5Uh+rruwAAECrKpv1uCNa05PzHT71"
			"d3V2njz/81ltaSklGAAgHDN7xymf/Gt29cnT2ULy8t88juCYAVovLkuK2J07c/OmGVwEEEP7d9GH"
			"98oxAMCrLmxacUDBIknQWjGS5EgyyMYVGeEwgZZ6uX2iiwmDPcUJUi+1jnfs7ogN2lN93ys8EpbM"
			"9PYxRyQAgL38b+Qvf4q0Rpg7O5s3l5YTA6vB0dfpaG4m9QyImHlSIw0c7CUPbhehAIDXpd+tHO9o"
			"TfrTMXIzF+ESJNqQTmYlFZA3efhCKHCdp/JTAkBeaum7e419tO+XbBtvt75P5KWnkhBPyOCOZ+eS"
			"qid5vqvu6JFczrGii7/dprn/Z+5otf1uVMPO1y9hW/DaG2MNhMXF2gu2jNdovCz/VLb7wmkXYoJD"
			"x+BFzbO/n6zoWYv45W9RKaYr+G76IyXrFqZu/DnF9cd5JJ8K9EHTNeCzS1vXrL1tyaouEs/euBJu"
			"bit+L2CrmzMTALDRBfGbkws/cZCezbBeuNRmxZGEBVELRw/k3lvROIJjBmQ7XnmBt/Ney/jmnV+n"
			"AsJxWbE5cK1dSOiap1YG7cU1o7/Y7ig/xYjh0LYylx1Zltp9ewp0s/nfh/op3TSaYeFgzbjGdrFg"
			"AGIw2VH/SKG9jQaA9CaDaec3HUkt8pllSKsGAKCbTRiTH7Hhu1SjtpdNs9b3ygDp/bJE0IaaLwkh"
			"9xiOOFUQpQMAMMZ/Hjx1y7ZVG80NOsoaLIK2u+v0J6u/aRIlsjtJJbPyCgZp8nBAtsBXj7Jr9Evs"
			"mxKNlxSnFtvN2yZ6F3PtFOlWLJuFFib9ie8pwu6hYsZY383BzizI6HkEBMCwD9y5xk1LpXNX1EI+"
			"Li4uKytLrV3qJRIJj8dLT0/vdUDYXFlSWtsu7nck1l5bWlbXqS7DDh48ePz48d5HxO21pUWVzSJ1"
			"aewHEo7JycnZvn07OWlNFSXlDQIltJIdKUN4ePjDhw8HOuqNiJorlbGkiwMHDpw6dar3EYUORltr"
			"yioa+9MTFRWVlJRETjlBIolkzszMDAsLIyefpMmy8Hi8O3fuDHDQ35UzZ87s37+/9xFigZNejMig"
			"ZvEkqK6u3rt3r8zBofbpVybHxJxD5kREm2umtv9GyIOuzTWzeJsK+0LaMeSk6Y0y11NKK9mR6kdD"
			"pR5R7GCGrqGprur0yJOo6mRWvcnDAGKBq7Tm1CxeWag3gSkoKCiGKdQGQEFBQTFMoTYACgoKimEK"
			"tQFQUFBQDFOoDYCCgoJimKLwV0Bisbi4uJjYRV61iMXi8vLyzMxMtWpRgra2NhRFh6Bhb6C8vBzD"
			"sKFpM4ZhJSUlCDJULjg6OjokEokKfSUUCmtra9Xn/JKSEnUHd8gWozpobm7u6OgYJpPtoqWlhbie"
			"0yQS+Z/0io2NbWhooNPV+xkRFEVpNBqDMdR+jQooigKAhsbf6TVKDMNwHB+aNg+1QKs8vupOmLcQ"
			"3KEWI7XydyzwQYLjOIIgISEhvQ8qDLaxsbGnp6e9vb1abYqMjHR3d582bZpatSjBoUOHWCzWkiVL"
			"/mpDBkBubu758+dlAjxEiIiI8PHxcXJy+qsNkcLn8zkcTkBAgKoERkdH29razps3T1UCZXjy5ElC"
			"QoJagxsZGenh4eHm5qY+FUOHs2fPNjY2rlq16q825O1RU1MTFxcnc3Co3JJTUFBQULxlqA2AgoKC"
			"YphCbQAUFBQUwxRqA6CgoKAYplAbAAUFBcUwhWw/gJo/Tx78v/sVmJHzgi+XTh+lll+K4U1ZFw6f"
			"vvWqU3ecx6JlH09k558OjbnVBEBj6JpOXrB8sathaTwvXm9d8Ex2W3FG2uW0RseNy1x6Wj2iOSdD"
			"96Y3A9BZhu/ODlw2R+v6Dn6TX+hiOxbelH7gpycO366arkxf+PbbsburfX5YOI4BeN2VqIPCRd/6"
			"mdEBK0vgxTZ+FBZop0HUvtSpcF/Y70UYSCRAowEwJgTwvppOkzUbr7q8o+9pi9ZZ/XFCBVZjRVJf"
			"6VTcPnooPrOOPsYzcKW/HQfpeHHl2InknHq6metnyxdN4iJ4U2bc/pN3qugWXkGrP7DVfmM4aAzd"
			"UXbeAUvmWEu/NN/XDSQQ58nE1ansV16eS8hiOw006+iWm+N+WOfOFr84x7tktM67LDK2ek7Y+pn6"
			"CACA+PmZzXvK3w/72ssAAYDmPw6EXTP/+jvf3g0UBmwPcaqcEqnv9Dpfph47fjmnkWXpsSjo44kk"
			"40CY4RRBglQgECTK9/4AFbwuDXkm4/LKhaIHOQU+XzdH/mJE1zKymx8U5DVWEwAAr0uL3lvnt7mr"
			"Vxeac3JbmtmG2RU79/Q+1fB+v+K70vt1XAEAtCatiJj+PPykZPHmzydoAogeHIp45hq6hHxek4Bc"
			"QrddiwnhP9ac7KL37NfQXcnyGpgNnvab0eHppkHhUeFBlvcjf0quF7eWFnI+jOHzYyNXO+bv+fFC"
			"JdZRkVdYi6K5pyP5t/Ky/8yu7f1eg6SlpIDzYQyfHx0yHz277ddnxh7uWokxp/JFbQ8O7X8wduYU"
			"ZVZ/AMDqX+SWtUkAACSC6oL8ik4JAIhfJV989DwtIaNTnvZjdV7f7ePzY/xH1o9Z+jOfH/uVB0uO"
			"2YiJj8xpM2xVY7Wky1dY2bkfD9e//+3uiMW6iZHHc4X1qbwtiTr+obt5q/+RvSvsQgUmyjyy6/aY"
			"NTt5gboXd5wqEL85HAeivvfnXt++5YK0i5GMG0iAy8a11ohZdTOjHAP0+a20uynXHgsAb3p8uwDh"
			"MltL8zMvxqVIe54LHsWfT88pqOlqRdaWcfjIxeznVZ193mQZuD3EqUp9J266sXNLPMP3m4gtgVaP"
			"d2yOKyb5WiRhhpVtFXmFtSiOEyXK9/6AFUhLQ44CUFAuFD0QC7xNNiV6PB79jVv5gejEuq41ENF/"
			"x7Aq8UoeCgAgyk5OajS21WmTOZWM+C5DerTw+Xx+9HJnRmvJ07SDO0/kiQAAb3yVU9Ii/7UtpSF5"
			"RSMQoJpjpvj6uo5lYQKBejrVo23tYk02W1uTbe27/ruFNq/3ObqulecU06pi6TIAGhMWh/OCfax1"
			"FLQ7ZRpOtB/TWVeHc9xWfWl6ZVdk5M9P3YKXTmCq1Ny85HvcoA2OhSl3W+Vrlx3Rn9ndqNRqGmti"
			"wKZlrkaaHEurUQwMb72bUui07J8TR2rqWvqsWD6J3SGuyS7QdZkxhqVj6+mEPstpl85PUTiYBnb+"
			"wfMEFxNfiN/kBhJ0x7WG6zC+Mye7TVx2P9fI26nyQS4qysmttXEYxwCajrOz5OqlV2IAaL2blDN2"
			"qnXX1X5n5tHjrXP8rWVuRpWxR9FU2+9eznNcFjjJhGMw3m/lbFHa1ZIBrqA9mYsrkvhKvvcHrABT"
			"bDLZvKN4jVjhYsTkGOixQNJd3XRz75kj7l3LQwFEWVcfmnh7GSMKTiUlXhaa5iRvy+tRpwuU77/6"
			"RshtACO8Pv+I+0f4vLmhN3TnfT5XmbaKJJTMWfslN3FDwPJvo86XcidacxAAvPpx4n//e+5U7Lbf"
			"alw9bfu795GIX6bui4n6KXTD0ab5/lM0ARnpucJPknLPfEmgg2rvfYWZlx+Mft/T1duxJOVm1wZO"
			"0K4sqrQa4U5wszOi4833D8ejvh+Nb61t4hgadgWQYem1ZI41XdAp0NTSpgHQtLSYQoH0ylleOHqE"
			"6psYdtbWYHLdQAaZuGrbOFhXPc2pup/FclsyY/TLjILCnFejJ9ppAkgw9rS5BncSs0V43fVL5VNm"
			"29IAAIS5J36t8P5yhp7MoqaUPYSpdh3GGqrr2UYmXfsNnWsysrmmhvQG0HeG0mbeWD1Roqz3yfpQ"
			"bmnIU0Bd9SuBHjElJOKnxzesWfk/Ad/fs/vEu2cNRIy9vIwyruaiwifXMs29PbkI4VQS4nuKq6sB"
			"5MqVK1eHns0XAwBoO335rzFXdv/2Ui2BJLWUY+UXj/ze6vb1wUPfz0AvHzn/Sj05xRw7dxP/9NFt"
			"n1oV8YPD0xpwAKAhCIPBMrBbvOOXDZP7bW9KQ7jvesyYPkbYYuH3oZ02AAjzEtOa3xldkHiF2HdZ"
			"eWjQ/uelm20d2ScO3awRZyVfq8HlalcWVVsteH5222HBou/8LRiaTIZIJJTeR4o7W9tFNIROx8QY"
			"AEgwDEeQ7ktqeeHoMbC1ja6jg8h1Aylk4qppb8ct+PP3B7iTi6mzE/vZ9VvPNO0c2N+9YUoAACAA"
			"SURBVAAAIpTlMd/6ceIfL1NSOz3mWUjEEkALz/x818TdrCavqr2jpvBVXfflkZL2yE5VaiOLxRR2"
			"dj9gQgUiBotF/iJaXubKkailwPsqUzAAkym6oclJCRrDPjB6H/9o3EG/Iv6xpz1X5AjX08v00dVH"
			"GVefWnm76wMQTiUhvidRaRoTl+7h8/n8/eGLpJcNtBHTV68YlRR1vlwNj95JbQB4XWW1QMvI0tpm"
			"nImOqLpSLY8S0cwDq7YlN4K2qfMHX8w1ys8uwwAQI4d5fn6+c2c4kmuZh3DM7B2nfPKv2dUnT2cL"
			"Ac0/GX3jna+iI5do/BaTWKGs2Zoj9bHqyg4AALSqslmPO6I1PTnf4VN/V2fnyfM/n9WWllKCEbUr"
			"i4qs7kZclhSxO3fm5k0zuAgghvbvog/vlWMAgFdd2LTiwFPc0NSwsaxYDCAqLRMYj2Z3GSEvHFI6"
			"np1Lqp7k+a5Gs1w3kEA2rsgIhwm01MvtE11MGOwpTpB6qXW8o/Q/uxLQnur7XuGRsGSmt485IgGQ"
			"CFt17Z11C+/c/rOwobX4UVa51NnK2UOcqtRIAwd7yYPbRSgA4HXpdyvHO1qT/v+b3MxFuASJNrLe"
			"J3t3TVYBeZOHL4QC13kqPyUAADQ4+jodzc29FmN9d6+xj/b9km3j7cbuI1d6Kgnxb85TZKTnmi/0"
			"L57MEKr4PwAkfwWkMcHvn9OvxQTPvggwYsqajxxU+jC9W4mdr1/CtuC1N8YaCIuLtRdsGa/ReFk5"
			"UQzrhUttVhz5fbLztRTTFXw3/ZGSdQtTN/6c4vrjPGUeX2m6Bnx2aeuatbctWdVF4tkbV8LNbcXv"
			"BWx1c2YCADa6IH5zcuEnDn21JyyIWjhaiU/piV/+FqUSq7vAKy/wdt5rGd+88+tUQDguKzYHrrUL"
			"CV3z1Mqgvbhm9BfbHTXYnQunXYgJDh2DFzXP/n5y16MreeG4hGYdW7/yd4mYMdZ3c7Azsy7+CsEN"
			"n6+0VeYnYgwLB2vGNbaLBQMQg8mO+kcK7W00AERdf2Xa+U1HUot8ZhnSqgGApvvewjXOAIDXxeXn"
			"tnz6oYMuAABed10pe4hTBVE6AABj/OfBU7dsW7XR3KCjrMEiaLt7v/eg/U2TKFGu91WpYJAmDwdk"
			"C3z1KLtGv8S+KdGT/YI21HxJSJ8nvGw3b5voXcy1U6RbsUTmVAuT/sT37NLSoTQAYNgHRkyRHka4"
			"s75anvZgr+rnrqiFfFxcXFZWVq8D4raa0pLKFlSljep5PF56enqvA8LmypLS2naxSrUowcGDB48f"
			"P977iLi9trSosln0V1nUPzk5Odu3byd1qrCpoqS8QfD6ANZeW1pW10k4T1XhCA8Pf/jw4aDFqIwD"
			"Bw6cOnWq9xGFU0Vba8oqGgWE432JiopKSkoip5wgUYH3e5OZmRkWFkZOPkmTZeHxeHfu3BngoL8r"
			"Z86c2b9/f+8jxAJX6WKkZvEkqK6u3rt3r8xB8ldrdB1DM/VfTTA5JuYctWtRBro218zirzZCVTD1"
			"Rpnr9T6AaHPlPWQbuuFQOQqnytA1NNVVqSqCRAXeV50Civ4hFrhKs1/N4pWFehOYgoKCYphCbQAU"
			"FBQUwxRqA6CgoKAYplAbAAUFBcUwhdoAKCgoKIYpCn8FJBaLi4uLiV3kVYtYLC4vL8/MzFSrFiVo"
			"a2tDUXQIGvYGysvLMQwbmjZjGFZSUoIgQ+WCo6OjQyKRqNBXQqGwtrZWfc4vKSlRd3CHbDGqg+bm"
			"5o6OjmEy2S5aWlqI6zlNIpH/cllsbGxDQwOdrsSrTAMARVEajcZgqOXz0oMBRVEA0ND4O71GiWEY"
			"juND0+ahFmiVx1fdCfMWgjvUYqRW/o4FPkhwHEcQJCQkpPdBhcE2Njb29PS0t7dXq02RkZHu7u7T"
			"pk1TqxYlOHToEIvFWrJkyV9tyADIzc09f/68TICHCBERET4+Pk5OTn+1IVL4fD6HwwkICFCVwOjo"
			"aFtb23nz5qlKoAxPnjxJSEhQa3AjIyM9PDzc3NzUp2LocPbs2cbGxlWrVv3Vhrw9ampq4uLiZA4O"
			"lVtyCgoKCoq3DLUBUFBQUAxTqA2AgoKCYphCbQAUFBQUwxRqA6CgoKAYppD8yRfe8Dju4Ok7FYxx"
			"c5Z9Od9GlV8u7KWkKevC4dO3XnXqjvNYtOzjiez806Ext5oAaAxd08kLli92NSyN58XrrQueyW4r"
			"zki7nNbouHGZS++miR0vrhw7kZxTTzdz/Wz5oklckBGpr9SG1347dne1zw8LxzEAr7sSdVC46Fs/"
			"MzpgZQm82MaPwgLtNAAAzTkZuje9GYDOMnx3duBSp8J9Yb8XYSCRAI0GwJgQwPtqOo1otsy4ZfOs"
			"GhN38Jv8QhfbsfCm9AM/PXH4dtX0gTeGx4qkvtKpuH30UHxmHX2MZ+BKfzsOIuskBG/KjNt/8k4V"
			"3cIraPUHttryw6GPyLp3RP7psBvjQlZOHVh/CHGeTFydyn7l5bmELLbTQLOObrk57od17mzxi3O8"
			"S0brvMsiY6vnhK2f2RU58fMzm/eUvx/2tZcBAgDNfxwIu2b+9Xe+0sYLPZLpWkZ284OCvMaS+7y+"
			"7FQ5JVLf6XW+TD12/HJOI8vSY1HQxxNJhoEwwymCBKlAIEiU7/0BKnhdGvJMxhWUC0UXcgp8vm6O"
			"/MVITmr19bid+Fr03jq/zV0NvdCck9vSzP69fgazb+1o3ZFV+bXVjf/0WQjekX58WRpsGkN3lJ13"
			"wJI51jo94QcA0HJeMKs+PklmqfFgE6cpD5IdwS79uGlflq6zA9zmfRObIRiIc0nTfjM6PN00KDwq"
			"PMjyfuRPyfXi1tJCzocxfH5s5GrH/D0/XqjEOiryCmtRNPd0JP9WXvaf2X16k+H1qbwtiTr+obt5"
			"q/+RvSvsQkWLjEglLcPqX+SWtUkAACSC6oL8ik4JAIhfJV989DwtIaOria6kpaSA82EMnx8dMh89"
			"u+1Yndd3+/j8GP+R9WOW/sznx37lwZJrtsy4X7NxMw93rcSYU/mitgeH9j8YO3PKwFd/AJB0+Qor"
			"O/fj4fr3v90dsVg3MfJ4rpDgJEyUeWTX7TFrdvICdS/uOFUg7hpPDAdhJNpamvOyYcB96nDZuNYa"
			"MatuZpRjgD6/lXY35dpjAeBNj28XIFxma2l+5sW4FGnPc8Gj+PPpOQU1Xe342jIOH7mY/byqU0KQ"
			"HP2NW/mB6MQ6crYRpir1nbjpxs4t8QzfbyK2BFo93rE5rpjka5GEGVa2VeQV1qI4TpQo3/sDViAt"
			"DTkKQEG5UPRALPA22ZRQmFoEj5dy3jGsSryShwIAiLKTkxqNbXUJtSMiqMRlF4LuppNS1Qeivvfn"
			"Xt++5UIZ1mMMn8/nR6/w9pNdakiu/kByA0DzHme3285bvmTpF3PG1ty/q54O9Whbu1iTzdbWZFv7"
			"rv9uoc3rVzToulaeU0yriqXLAGhMWBzOC/ax1unb7rT1bkqh07J/ThypqWvps2L5JHaHrEhoid+4"
			"cOX3/163fNGna48OomsjAKB5yfe4QRscC1Putvb5A9Nwov2Yzjri4qPAbOI4jtuqL02v7IqM/Pmp"
			"W/DSCYPqv0ZjTQzYtMzVSJNjaTWKgeFEJ4lrsgt0XWaMYenYejqhz3LapfOTDQdx5GD703XHtYbr"
			"ML4zJ7tNXHY/18jbqfJBLirKya21cRjHAJqOs7Pk6qVXYgBovZuUM3aqddfVfmfm0eOtc/yt5d7D"
			"MjkGeiyQkNybFGVe+93LeY7LAieZcAzG+62cLUq7SrblpewMK3FFEl/J9/6AFWCKTe4n7yjkIFa4"
			"GMmmFtHj5aO9Z464dy0PBRBlXX1o4u1l3D6Q2lG0gDAN7PyD5wkuJr4gd5VAClKPgBijRxvTrt36"
			"b6qh5tNaXCRUfWdKAIARc9Z+mRe5ISDewsXLf5H/VA5kAF79OPG/Augov5dU47rBVgPuKR6PNdQ2"
			"cQwNu7Y0hqXXEksAUV+RIMjGcNP5Wze7Cc9tWH+94HM7O2XfBBRmXn4w+v2lrsavfj1xs36mrwGA"
			"RPwydV/MM3Hd86dN87+ZQrq5n5xxIz1X+J357Kj51gsOg7xlR7gT3LgAePP9w/Go7/fjW6/KOklc"
			"eFWgaaBNAwAtLaZQ0AmgB8Rw6JTfkh0pylDWKpm4ats0W1fdy6lqz2K5bZiRuzOjoBB7NXqinSYU"
			"SDD2tLlavydmL/3K7Pql8imzp2bGA4Aw98SvFd7ffibgXW7pLVgifnp8w5rfO8sKW122fkmykyYx"
			"83IBALCG6nq2kUnXfkPnmoxsflSDgSW5d+P7ztCGkQEAgNUTJQr0+3ofBz1yt+XySkOeAtImU7xG"
			"j7gYyU8teR6XGHt5GW24movaiK5lmnt/wZUQl6aWeKLS/hcQRN/EsLOgBnvdORLhzgje2t0+XglI"
			"5RrdatE3X8/SeHjucm4LRuMaG6snoZhj527inz667VOrIn5weFoDDgA0BGEwWAZ2i3f8smHymxuS"
			"0TSZDJGoe3MSd7a2i2RFAgBNl8tlAaKjq4WLlL6RoUH7n5dutnVknzh0s0aclXytpstW7rseM6aP"
			"EbZY+H1oR/7/JHLGCfMS05rfGV2QeKWfdtHkEDw/u+2wYNF3/hYMopNoCJ2OiTEAkGAYjiDduSTj"
			"u0YNonuVRyaumvZ23II/f3+AO7mYOjuxn12/9UzTzoENACBCWR7zrR8n/vEyJbXTY56FRCwBtPDM"
			"z3dN3M1q8qraO2oKX9X1RJLGsA+M3sc/GnfQr4h/7CnJCMtmnlQWi8UUdnY/YEIFIgaLRf4iWl7m"
			"ypGopcD7KlMwAJMpuqHJSQn5qSXX4wjX08v00dVHGVefWnm768tdmogqySwgwtY2uo4OAjSNiUv3"
			"8Pl8/v7wQaz+QPpXQDr/8F296etF4wXVuLPP+8r0Ou8XNPPAqm3JjaBt6vzBF3ON8rPLMADEyGGe"
			"n5/v3BmO/bfMQwzt30Uf3ivHAACvurBpxYHHD2REKmma5kh9rLqyAwAAraps1uOOaE1Pznf41N/V"
			"2Xny/M9ntaWllGAAgHDM7B2nfPKv2dUnTw/k+ZLsODT/ZPSNd76Kjlyi8VtMYsUgtwBxWVLE7tyZ"
			"mzfN4CJynPQUNzQ1bCwrFgOISssExqO7nh8SwlExkjBS+SeBsnFFRjhMoKVebp/oYsJgT3GC1Eut"
			"4x2laSYB7am+7xUeCUtmevuYIxIAibBV195Zt/DO7T8LG1qLH2WVE5ytwdHX6WhuJvUMiJh5UiMN"
			"HOwlD24XoQCA16XfrRzvaE36hlFu5iJcgkQbWe+T/X8PWQXkTR6+EApc56n8lACQTS0FHtd39xr7"
			"aN8v2Tbebmx5S9NThLCmIP0vIB3PziVVT/J8V4UhJbt5CO/9vHLrA0OXTyP/vcBMLTcAGna+fgnb"
			"gtfeGGsgLC7WXrBlvEbj5YFJsA9caxcSuuaplUF7cc3oL7ZPchTXX+4tEkCpPUDTNeCzS1vXrL1t"
			"yaouEs/euBJubit+L2CrmzMTALDRBfGbkws/cZCezbBeuNRmxZGEBVELB7RTdo/7aK0gKsV0Bd9N"
			"f6Rk3cLUjT+nuP44j+TDDCJ45QXeznst45t3fp0KCMdlxWYZJzlqsDsXTrsQExw6Bi9qnv395K47"
			"T2I4tK3MZUeWpXbfiQLdbP73oX5mSloJDAsHa8Y1tosFAxCDyY76RwrtbTQApBdKTDu/6Uhqkc8s"
			"Q1o1ANB031u4xhkA8Lq4/NyWTz906GmAK701lgjaUPMlIeQewxGnCqJ0AADG+M+Dp27ZtmqjuUFH"
			"WYNF0Hb3wTbFliNRrvdVqWCQJg8HZAt89Si7Rr/EvinReEl+ainwONvN2yZ6F3PtFG0AOUuToy5X"
			"r69KLvK0WyJhAenOajFjrO/mYGcWZPQ8AgJg2AfuXOOmpezcFbWQj4uLy8rKUmuXeolEwuPx0tPT"
			"ex0QNleWlNa2i5UXKWyqKClvEAxS5MGDB48fP977iLi9trSoslmkvGXqJicnZ/v27aROJTgJa68t"
			"LavrJJxH8B1hJDnCw8MfPnw40FHq48CBA6dOnep9RGGaoK01ZRWN/c04KioqKSmJnHKCRAXe701m"
			"ZmZYWBg5+SRNloXH4925c2eAg/6unDlzZv/+/b2PEAt8ACsHKY8TauftrinV1dV79+6VOTjUPv3K"
			"5JiYcwYnQW+UuZ5qRQIAAF2ba2YxeDFDA4KTEG2uvIdsRN8RRv7/gsI0YegamurK+4PSECQq8L7q"
			"FFD0D7HAB7BykPI4oXaGwJpCvQlMQUFBMUyhNgAKCgqKYQq1AVBQUFAMU6gNgIKCgmKYQm0AFBQU"
			"FMMUhb8CEovFxcXFxC7yqkUsFpeXl2dmZqpVixK0tbWhKDoEDXsD5eXlGIYNTZsxDCspKUGQoXLB"
			"0dHRIZFIVOgroVBYW1urPueXlJSoO7hDthjVQXNzc0dHxzCZbBctLS3E9Zwmkcj/sE9sbGxDQwOd"
			"rt7PiKAoSqPRGIyh9mtUQFEUADQ0/k6vUWIYhuP40LQZRVEEQdSdTuRReXzVnclvIbhDthjVwd+x"
			"wAcJjuMIgoSEhPQ+qDDYxsbGnp6e9vb2arUpMjLS3d192rRpatWiBIcOHWKxWEuWLPmrDRkAubm5"
			"58+flwnwECEiIsLHx8fJyemvNkQKn8/ncDgBAQGqEhgdHW1raztv3jxVCZThyZMnCQkJag1uZGSk"
			"h4eHm5ub+lQMHc6ePdvY2Lhq1aq/2pC3R01NTVxcnMzBoXJLTkFBQUHxlqE2AAoKCophCrUBUFBQ"
			"UAxTqA2AgoKCYphCbQAUFBQUwxTSP/nCO8oepSamNL737/+Zqgl4c9YF/okbpZLRbv/81yJnA5Xs"
			"I3hT1oXDp2+96tQd57Fo2ccT2fmnQ2NuNQHQGLqmkxcsX+xqWBrPi9dbFzyT3VackXY5rdFx4zIX"
			"lkIB+gh0vLhy7ERyTj3dzPWz5Ysmjcg/HXZjXMjKqUy85vrevbnO61dN5/ZrfPvt2N3VPj8sHMcA"
			"vO5K1EHhom/9zOiAlSXwYhs/Cgu00wAANOdk6N70ZgA6y/Dd2YFLnQr3hf1ehIFEAjQaAGNCAO+r"
			"6TR5ZssY6SRO2SEzMiJwRKrMxPoFK5L6Sqfi9tFD8Zl19DGegSv97TiIjE+4CODy3KkgHDSG7ig7"
			"74Alc6ylX5rv6wYSiPNk4upU9isvzyVksZ0GmnV0y81xP6xzZ4tfnONdMlrnXRYZWz0nbP3MrimL"
			"n5/ZvKf8/bCvZ0lSd4aeK8QAgPGPxTuDPV9/inHA9hCnyimR+k6v82XqseOXcxpZlh6Lgj6eOIJc"
			"ohNmOEWQIBUIBIl4U2bc/pN3qugWXkGrP7Al9VVQgoLXpSHPZLnxpehBToHP182RvxjRtYzs5gcF"
			"eY3VBADA69Ki99b5be5qy4XmnNyWZrZhdsXOPb1PNbzfr3hpRUvj2qvGeiINAKDlvGBWfXySzKoy"
			"gBbwRMglNJp9cNn8z9bxDidm1WIA0H735+9jMpiOzrpP+T9EX2sehAGvab8ZHZ5uGhQeFR5keT/y"
			"p+R6cWtpIefDGD4/NnK1Y/6eHy9UYh0VeYW1KJp7OpJ/Ky/7z+xa7A0CAK9P5W1J1PEP3c1b/Y/s"
			"XWEXKtDW0pyXDTi0Pv4l5H9bZ33q1v/qDwBY/YvcsjYJAIBEUF2QX9EpAQDxq+SLj56nJWR0AgCA"
			"pKWkgPNhDJ8fHTIfPbvtWJ3Xd/v4/Bj/kfVjlv7M58d+5cGSazbByCpDH5mRzo9kPEOm0ZWky1dY"
			"2bkfD9e//+3uiMW6iZHHc4UEn2Dy3akoHAeivvfnXt++5YK0WaWMG0iAy8a11ohZdTOjHAP0+a20"
			"uynXHgsAb3p8uwDhMltL8zMvxqVIe54LHsWfT88pqEEBq3j+nLsgMioqKmrnv9x6f4h34PYQpyr1"
			"nbjpxs4t8QzfbyK2BFo93rE5rpjka5GEGVa2VeQV1qI4TpQoyjyy6/aYNTt5gboXd5wqINfvm6hA"
			"WhpyFICCcqHogVjgbbIp0ePx6G/cyg9EJ0pbtiP67xhWJV7JQwEARNnJSY3GtjptMqeSEd9lCE6o"
			"sR69fD6fH73C2092VRnM6g9kNwC6yaxNv8aHTJf2F0VfPMhsHDt72f8s/XK+TWvmg+eDs0EK2tYu"
			"1mSztTXZ1r7rv1to8/r6ja5r5TnFtKpYugyAxoTF4bxgH2sd2hsFQOvdlEKnZf+cOFJT19JnxfJJ"
			"7A4JAAD66v+27HzuvfWbmYaDuHNB85LvcYM2OBam3G3t8wem4UT7MZ11dYRVWr7ZCox808QG8vIK"
			"jTUxYNMyVyNNjqXVKAaGy1En3y6FSpkGdv7B8wQXE1+I3+QGEnTHtYbrML4zJ7tNXHY/18jbqfJB"
			"LirKya21cRjHAJqOs7Pk6qVXYgBovZuUM3aqNR0A8Pp6EdT98f/YO/O4Js7t4Z9MQghLgkhABBFR"
			"CtYbloILoIBasKLS3qptvVTxarXXtcVa21rAK4INdQGtW+ry1l3LT72ooCDgita6ILKJgooQ9l0I"
			"JJkl7x9EhEwiQwiVfpjvf47znHOeszzPzJCZk3b1fhnK7ugPbezRNFXJrYv5rguCR1vyzEYGLpki"
			"T00r7uYK2p65hCaJz6tyCow9Jg7lGDn6uqGPciVaKsA1m6yhXGjeAKax5Ng8MxMOKF5VN9PGf9KA"
			"25fzUQB5Vtp9S3+/QYiGUymJV47sVGO9CbUVEOHbC4Zx29/iVLS2ykCPrc8ADocD0tYWnZgy4IMV"
			"X/ITVgUt+j7mdAnf2Z6HABCVDxL+979Tx3ZG/F7l6ev45pVPVQBeV93AM1cu8iw7v3kf2LMAsEe/"
			"rf3lsW3AZDt2T6yVZV68Z/W+r6e/a3HytbYNXIE9S9m1LebnsFUHG6bPpNaOENQb+eaJ8bqxbyH8"
			"UV4CCybReGd/PDrjnyObulbXtVLE1NK8tboKV+sGKqjE1dDBxb4iO7fiThbHa95Eq2d3Cwpzn1s5"
			"C/QBFDh3/FSzmwk5cqLmyoXSsVMcGQAAOMfCEuprG57+L3TlrgfSdsFa2UOaatthvK6ylmth2Zb2"
			"TL7lwMaqKsobQOcZKvt247VkidJWqb6BIQOAYWDAlklbqfpQbWmoU0Bf9WuBCTklFFj24VXLl/w7"
			"aO1twSf+7T1akUF+fhZ30/JQ2cPLmTb+vnyEdCoF8eSKbq+xtu6PS5YsWRZ28onO9wPtLoGZCAIK"
			"BQ5AEDgwmTp6d5w9bOoa0fGDEZ8OLxKFRKXWEQDAQBAWi2MmmLvp11VjumpvqiKAoc9myeUy5QU1"
			"1tokkQMogDNm1fa5Tfujz4m1Lg0GSP68cK25JefIvmtVWFbS5ao2W/nv+kycMFT20jbwIwHFDk9q"
			"jexiYnWUF1ol0scnI/ZL5/ww05ZFRV3XSmVNzUwjI0StGyihEld9JwG/4M+z9wg3D2t3N+6jK9cf"
			"6QtcuAAAcpTjM93+QcIfz5JTWn2m2SowBQAYei7fErVy/r9DIuZZ3rn2+FV7ei3tUZ2q0kYOhy1r"
			"bVX6CpXKWRwO9YtodZmrRqIBwmTiGA4AChwnEIR6LVFU0A2TaV7BUJMSDJZTcOwu0cG4vYFFokPZ"
			"r1IOEL6vn3VGWsbdtOzh/t6mAKRTKYhXk6ivaoyh5zx/u0gkEu2OmuOg8890aLUBsKxtrZCKp4Uv"
			"JY8LSmGwjdatwDuCZu5ZGpFUD4bW7h9+MdXiSY4YB0AsXKYFBs6YOtG165Z5JAGIudO76P3bpTgA"
			"EBVn1izek40Cg2X33ji3OWFfsE9EHXmscfXrhP5AU7yyvAUAAK0obzThD2hKT3ri8ulMT3f3MdM/"
			"n9ycmlyMAwDCG+LkOvaT/0ypPHo8R0Zt2mqNpOAZ6mDixI1b8yaFr5nIR6io61ppy6NTiZWjfd/V"
			"a1TrBiqzVokrMsBlFCPlosTZw5LFHesGKReaRrq+6ogNhuNmvFd4IDKJ7R9ggygAALD8338+8lAG"
			"QMil0teLnHb2kKeqNNLMxUlx70YRCgBETfqt8pGu9pSfvqnNXIRPkuhgbm1eL36BAchLxNJBVlyq"
			"FUlVAXWT+y+kAjfKVp8SAAB6PFOjlsbGDku2qbffsIxdv+Y4+Ht1fiKvPJWCeFKettdYr0z4NVrt"
			"KMiggAWfJv4QFuAP+sM+3hA4VBeW6AlmBJ6PCFlxdZiZ7MULw1nrRurVX+yRANAbHrxCEBq2PHu4"
			"meRFldUXG1z1xKltM7Cc8n1I1vKNe5x2f+3eZS9Pfc+gzy6sX77ihh2nsgibsnoJXIt48V7Qei93"
			"NgDgVgXx4UmFn7goz2bZz57vsPjA+Vkxs60ofPtMz4lkZNeeoe4VovyMcPPtlyMbN3+TAgjPY3F4"
			"V+o0Kq2/gGYd+nrJWQXGGjYjPMSdXRN/ieSGz5c4apNTLFsXe9ZlroctCxCzMa6mBwqdHPQAlBs0"
			"WxA4AUkpCphszqhsO32YwGpfzOrHNhzx0wGzhW2PsYiaK1rZQ54qyNMBAFgjPw8Zty5i6WobsxZx"
			"ne3CDd5d3YN2OU2yRG7r7PFntoWEDSWKGqesHUPtwWF3FPTQ5P6AaoEvGyyoD0zonBLt2S9tRm3m"
			"hXZ6wsv18neI3cJeMVa5FStUTrW17Ep8exEqOtcYB+4qjzAAgOUUvHm5l4FO566phXxcXFxWVtYb"
			"eszjLTUlJdUSvEeN6oVCYXp6eocDssby4pJqCaa1RLIAWUNZcWmdtHti9u7de/jw4Y5HMEl1SVF5"
			"o1xry95I10Z27Znc3NwNGzboSB1VpRSJioq6f/9+j8V0Qv6yvKT8JarV2D179hw7dqzjEY1TRZuq"
			"xGX1XfkqJiYmMTGRmnKSRFxSXSKuaX3TmMzMzMjISGryKZqsilAovHnzZjcH/V05ceLE7t27Ox4h"
			"F7jOsv8vEE+BysrKHTt2qBzU/pkSYmA2RCfPfjrB5lna8HQrgG0y2MakR0YB4OHXXQAAIABJREFU"
			"AADTkD/EtudiNNC1kT32TPfU9YJSXaPHtRzSwx/BdUDjVFnG5tZd3iR2C5JExJDf9SPOniig6Rpy"
			"ges0+3tZvLbQbwLT0NDQ9FPoDYCGhoamn0JvADQ0NDT9FHoDoKGhoemn0BsADQ0NTT9F46+AMAwr"
			"Kipqa53ce2AYVlpampGR0atatKC5uVkul/dBw95AWVkZjuN902Ycx4uLi9+2Fa+RSCQKhUKHvpLJ"
			"ZNXV1b3nfLFY3NvB7bPF2Bs0NDS0tLT0k8m20dTUhGGq35JgKBSkb48BAMDOnTvr6uqYTAqvMvUA"
			"FEUZDAaLpfM3nHtK286np/d3eo0Sx3GCIPqmzSiKIgjS2+lEHZ3Ht7cz+S8Ibp8txt7g71jgPYQg"
			"CARBQkNDOx7UGOxBgwb5+vo6OTn1qk3R0dHe3t7jx4/vVS1asG/fPg6HM2/evLdtSDfIy8s7ffq0"
			"SoD7CBs3bgwICHBzc3vbhigRiUQ8Hi8oKEhXAmNjYx0dHadNm6YrgSo8fPjw/PnzvRrc6OhoHx8f"
			"Ly+v3lPRdzh58mR9ff3SpUvftiF/HVVVVXFxcSoH6b8B0NDQ0PRT6A2AhoaGpp9CbwA0NDQ0/RR6"
			"A6ChoaHpp9AbAA0NDU0/hfJPvogWcUZKQnL9e9/+e5y+mn/rAKIh68z+49eftxqP8Jmz4GNn7pPj"
			"YduuNwAwWMbWY2YtmutpXhIvjDdZGTKJ2/ziburF1HrX1Qs8OMrRFRc3RZ4twkGhAAYDgDVqzsrh"
			"fxxpCAybK+AQDel7fn7o8v3SCQO02PIkN3ZurQz4cfYIFhA1l2L2yuZ8HziECbj4vHBn/T8jgwV6"
			"AIDmHg3bkd4IwOSYvzsleL5b4a7O5gQJv5rAIJmtOm7BVO51lXkEbQwekNLRMaaUOtkXKX1lVHbj"
			"4L74zBrmUN/gJTMFPKTl6aVDR5Jya5lDPD9bNGc0HwGC7E5yOEwRUBk54MnxyKsjQpeM615zTSxf"
			"Ja5u4t+E+R6hcwV6aNbBdddG/LjSm4s9PSW8YLHSXxy9s/KDyK8ntc0Ze3wifHvp+5HfTFakbA47"
			"VYgDAOsfczeH+Bp3ksw0sBBMX7jQbxi11FSdKq9Y6TuT1mcphw5fzK3n2PnMWfixM8XsIc1wrPS8"
			"UiCQJBINmXG7j96sYNr6LVz2oSOlr4KSFLwuDXUmq40vTTtqCny6ca76xahzahE1qbE7agLD23p1"
			"oblHI1KHrJpStnl7x1PN73QpXlnS7XEFADAYvXjjhMdRRxVzwz8fpQ8gv7dv4yPPsHkCXf50lVpC"
			"ozl7F0z/bKVwf0JWNa7m3zpBci02Kt16YVRM1EK7O9E/J9ViTSWFvI+2iUQ7o5e5Ptn+05lyvKUs"
			"v7AaRfOOR4uu5+f8mdNBO2IZ8MMukWjbzIG1Q+f/IhLt/Gqio4+3QcK2Y0/kzff27b43bNJYbVZ/"
			"AMBrn+aJmxUAAAppZcGTslYFAGDPk85lPE49f7cVAAAUL4sLeB9tE4liQ6ejJyMO1fipmOPDUWe2"
			"6rjf8sxU5+Ge0dkx1IxWtPkKF5/6aX/t+99v3TjXOCH6cJ6sNkW4LsFoZthW4bJ/5GyJPFOGq3Wn"
			"mnCQRqJNJbnPut2fEgjVuFZbsCuu3S3FAX18PfVW8uUHUiAaHtwoQPjsppInmefikpU9z6UZ8afT"
			"cwuqUMDLHj/mz4qOiYmJ2fwfL2NVybHfeZXuiU2ooWYbaapK32ENVzevi2fN+G7juuDhDzaFx72g"
			"mOykGZY3l+UXVqMEQZYozzyw5cbQ5ZuFwcbnNh0roNb0laxAWRpqFID6+NK8hlzgzaopoSG1ENN3"
			"zCsSLuWjAADynKTE+kGORs0qp1IR32ZIuxaRSCSKXeTOairOTt27+Ui+HACI+ue5xS/Vv7alNdRW"
			"RKbl5DW/xYdOeNV6T/XfOgFtlmD6XK6hPtd+xtc/zHZ4vc8xjYf7jrWueKFcBkBv1NwoYUiAvVFX"
			"6nleS7+0vrQlOvqXbK+Q+aN61AWeZG5+0m3+wlWuhcm3mjr9B9vc2Wloaw158enCbI3jVB3TLTMZ"
			"HOegNQs8LfR5dsMHs3Ci6VZyoduCfzkP1De2C1i8aDS3RaHeLlI41IzsliVkXsW1iu8ysjU3pxkT"
			"38mz8Hcrv5eHynPzqh1cRrCAYeTurki78BwDgKZbibnDxtkzAYCorZVDzR9pV++XoWzyFRGbZ2bC"
			"AQXFvUlT5kluXcx3XRA82pJnNjJwyRR5ahrVlpeqMywnNEl8XpVTYOwxcSjHyNHXDX2UK9FSAa7Z"
			"ZMrlQtMOpnExUkktpo3/pAG3L+ejAPKstPuW/n6DEA2nUhKvCkN/tL/dlZjjBb30SQZqj4AQvr2A"
			"Ly3R+G+dMOCDFV/mR68Kirf18Js5Z+Y4HtwFovJBwv+k0FJ6O7HKc5WjHtzunkxkoO/iwBOfHbRZ"
			"f8ZFt/e+ssyL96zen+856PlvR67VTpphBqDAnqXs2vYIq3mc3TD9u7GUH4x1NU7VMd2yE+GP8uID"
			"EI139sejM9aObEpr4Jmbt+Uoy85vnp2mgapajUqvq46U3+2WKR1QiauhQ6N9xe3cCkkWx2vVxLzN"
			"dwsK8edWzgJ9KFDg3PFTDc4m5Mz/asiVC6Vjp4zLjAcAnGNhCfW1DTUpob9nhe7/6j1ldBVY9uFV"
			"y8+2igubPNZ/aUHt+oaceXkAAHhdZS3XwrLt5WUm33JgY0YVDnbUXmbuPEMH1l0AALyWLFFqKtU3"
			"M2QAgIEBWyZtJcCEktVqS0OdAsom07zGhLwYaUgtZJCfn8WqtDzUQX4508b/Cz4Cz1VOhStdim8P"
			"eXsDSIQ/MWT9TAAwdPvyU/hm6+8+Wwf1wkT70h+B2cOmrhEdPxjx6fAiUUhUah0BAAwEYbE4ZoK5"
			"m35dNUaL9qay/ITUxnesChIuda+T+pthgOTPC9eaW3KO7LtWhWUlXa5qs5X/rs/ECUNlL20DPxJQ"
			"7/DU5TgVx3TfXunjkxH7pXN+mGnL0mez5HKZ8toda22SyDUNUtFar0d5JAVU4qrvJOAX/Hn2HuHm"
			"Ye3uxn105fojfYELFwBAjnJ8pts/SPjjWXJKq880WwWmAABDz+VbolbO/3dIxDzLO9cet18eMVhO"
			"wbG7RAfj9gYWiQ5paHjf1VTrlLI4HLastVU5Y1Qqf919vvsz1CTRAGEycQwHAAWOEwhC/TsMFBXo"
			"9C69v8BQkxKaUgvh+/pZZ6Rl3E3LHu7vbQpAOpWC+Pa7BIae8/ztIpFItDuq7e8KAIwBE5YtHpwY"
			"c7q0209bu6bvbABo5p6lEUn1YGjt/uEXUy2e5IhxAMTCZVpg4IypE121apmHPjkae/Wdr2Kj5+n9"
			"vi2hTNstQH+gKV5Z3gIAgFaUN5rwBzSlJz1x+XSmp7v7mOmfT25OTS7GAQDhDXFyHfvJf6ZUHj2e"
			"I6Mu/83jSI7ppvWYOHHj1rxJ4Wsm8hFAzJ3eRe/fLsUBgKg4s2bxHg2LJElr2UCKI6mgGldkgMso"
			"RspFibOHJYs71g1SLjSNdLVqu3BVgOG4Ge8VHohMYvsH2CAKAAAs//efjzyUARByqVTdIqfHMzVq"
			"aWykVDHkzFMaaebipLh3owgFAKIm/Vb5SFd7yn9/U5u5CJ8k0cHc2rxe/AIDkJeIpYOsuFQrkqoC"
			"6ib3X0gFbpStPiUA1KWWqbffsIxdv+Y4+Ht1blGqPJWC+DcvTchA3+VfmJ47elem478AdONXQL2O"
			"nmBG4PmIkBVXh5nJXrwwnLVupF79xR5JxJ79HpNsvVjkZTpQsXJ2yupfkj1/mkbxqUAn9D2DPruw"
			"fvmKG3acyiJsyuolcC3ixXtB673c2QCAWxXEhycVfuKiPJtlP3u+w+ID52fFzLbqzr23pnEkx3TL"
			"dqL8jHDz7ZcjGzd/kwIIz2NxePAKQWjY8uzhZpIXVVZfbHBVv0CQw2E43EZ1pDjl1Q0rMIdMXxsW"
			"qHWTaJatiz3rMtfDlgWI2RhX0wOFTg56AMqbDLYgcAKSUhQw2ZxR2Xb6MIHVvpjVj2044qcDZgvt"
			"29NYeQetkDajNvNCqT2GI08V5OkAAKyRn4eMWxexdLWNWYu4znbhBm8t7kE7T5Mskds6e/yZbSFh"
			"Q4mixilrx/TwF3W6N7k/oFrgywYL6gMTOqdE/QXNqcX18neI3cJeMVa5Fatmoa1lV+Lbi7D9ERAA"
			"yyl441jlYYQ/+atFqfd26H7umlrIx8XFZWVl9WqXeoVCIRQK09PTOxyQNZYXl1RLsN5W3BV79+49"
			"fPhwxyOYpLqkqLxR/pYMouCY3NzcDRs2UJPWUFZcWifVQivVkSpERUXdv3+/u6PejPxleUn5S1Sr"
			"sXv27Dl27FjHIxodjDZVicvqu5pxTExMYmIiNeUkibikukRc0/qmMZmZmZGRkdTkUzRZFaFQePPm"
			"zW4O+rty4sSJ3bt3dzxCLnCdLka9LJ4ClZWVO3bsUDnYd+4A2mDzLG14b9sItTAN+UNs35563TqG"
			"bTLYxkQrrVRH/gXocS2HcLs+jSIaHcwyNrc2VvcfWkOSiBjytXrESVkBTdeQC1ynNdfL4rWl7/wN"
			"gIaGhobmL4XeAGhoaGj6KfQGQENDQ9NPoTcAGhoamn4KvQHQ0NDQ9FM0/goIw7CioqK21sm9B4Zh"
			"paWlGRkZvapFC5qbm+VyeR807A2UlZXhON43bcZxvLi4+G1b8RqJRKJQKHToK5lMVl1d3XvOF4vF"
			"vR3cPluMvUFDQ0NLS0s/mWwbTU1NGKb6sUGGQqH+5bKdO3fW1dUxmb37GREURRkMBovV136NCm07"
			"n57e3+k1ShzHCYLomzajKIogSG+nE3V0Ht/ezuS/ILh9thh7g79jgfcQgiAQBAkNDe14UGOwBw0a"
			"5Ovr6+Tk1Ks2RUdHe3t7jx8/vle1aMG+ffs4HM68efPetiHdIC8v7/Tp0yoB7iNs3LgxICDAzc3t"
			"bRuiRCQS8Xi8oKAgXQmMjY11dHScNm2argSq8PDhw/Pnz/dqcKOjo318fLy8vHpPRd/h5MmT9fX1"
			"S5cufduG/HVUVVXFxcWpHKT/BkBDQ0PTT6E3ABoaGpp+Cr0B0NDQ0PRT6A2AhoaGpp9CbwA0NDQ0"
			"/RTKP/kiWsQZKQnJ9e99++9x+oCW3zx24H/3ygnz9/65aL6PtU5+TEU0ZJ3Zf/z681bjET5zFnzs"
			"zH1yPGzb9QYABsvYesysRXM9zUvihfEmK0MmcZtf3E29mFrvunqBR3urRzT3aNiO9EYAJsf83SnB"
			"Cz4wuLJJ1BAYNlfAIRrS9/z80OX7pRO06QsvubFza2XAj7NHsICouRSzVzbn+8AhTMDF54U76/8Z"
			"GSzQI2uf71a4K/JsEQ4KBTAYAKxRQcIVzk/jdh+9WcG09Vu47ENHQwAgKi5u6nzanJXD/ziiA6vx"
			"IqWvjMpuHNwXn1nDHOobvGSmgIe0PL106EhSbi1ziOdni+aM5iNEQ6aqXZrDwWAZDxb4B837wF75"
			"pfnObqAAlq8SVzfxb8J8j9C5Aj006+C6ayN+XOnNxZ6eEl6wWOkvjt5Z+UHk15NMEQAA7PGJ8O2l"
			"70d+42cGLx9dOPr71apR/wn79J2Oadxte8hT5RUrfWfS+izl0OGLufUcO585Cz92phgH0gzHSs8r"
			"BQJJonrvd1PB69JQZzKhrlxo2lFT4NONc9UvRkwDC8H0hQv9hr1uCNDZ4wLscuyOmsDwtoZeaO7R"
			"iNQh3349kd256gxuqqr8ZvjV/3Zcvqa9oywwZbA7FF57+AEADNxnTa6NT+y81HzlQ/E7udQSGs3Z"
			"u2D6ZyuF+xOyqnEA/Nnx8LVHigZ5jjHM/n/hmxNruuFrzUiuxUalWy+MiolaaHcn+uekWqyppJD3"
			"0TaRaGf0Mtcn2386U463lOUXVqNo3vFo0fX8nD9zqjt20lG8LC7gfbRNJIoNnY6ejPjt0SAfb4OE"
			"bceeyJvv7dt9b9iksdqs/gCA1z7NEzcrAAAU0sqCJ2WtCgDAniedy3icev5uqzrth2r8ftglEm2b"
			"ObB26PxfRKKdX/noZx7YcmPo8s3CYONzm44VYAAAiGWAymkTHXVjtaLNV7j41E/7a9//fuvGucYJ"
			"0YfzZLUpwnUJRjPDtgqX/SNnS+SZMlyuxi7N4dgTs3Ym/8qGdWeUXYxU3EABQjWu1Rbsimt3S3FA"
			"H19PvZV8+YEUiIYHNwoQPrup5EnmubhkZc9zaUb86fTcgioU8KK4dT/fsZz9w9rZ73S+iOm+PeSp"
			"Kn2HNVzdvC6eNeO7jeuChz/YFB73gmJPOdIMy5vL8gurUYIgS1Tv/W4rUJaGGgWgoVxo2iEXeLNq"
			"SrR7PPY7r9I9sQk1rzqCkTxewnvHvCLhUj4KACDPSUqsH+RoTKo6OUklobJ85bx6B5cgFV67MSKR"
			"SBS72D9Qdamh/pV0amsL03Lymt/iQycoW+8pFEO8F3z33defz5nqYoI2N3ej++EbQJslmD6Xa6jP"
			"tZ/x9Q+zHV5fvzGNh/uOta54oVwGQG/U3ChhSIC9kYZ2p2xzZ6ehrTU1BM9r6ZfWl7ZER/+S7RUy"
			"fxRbJ3a+Mjc/6TZ/4SrXwuRbTeq1qwzAq3IKjD0mDuUYOfq6oY9yJZok69RqBsc5aM0CTwt9nt3w"
			"wSycaLqVXOi24F/OA/WN7QIWLxrNbcHU26UxHGwzwcyQadJzCU+xN7mBAq/iWsV3Gdmam9OMie/k"
			"Wfi7ld/LQ+W5edUOLiNYwDByd1ekXXiOAUDTrcTcYePsmQBo3vlU9vuTDQpv33n2spOjtbFH01Ql"
			"ty7muy4IHm3JMxsZuGSKPDWtuJsraHvmEpokPqeaFV0owDWb3FW50JDBNC5GbJ6ZCQcU7UlH9nip"
			"lf+kAbcv56MA8qy0+5b+foMkpKp7Q29HTQtI58LTEdQeASF8ewFfWtI+aMT780cASLJ/PZBGeH3r"
			"N1gnpgz4YMWX+dGrguJtPfxmzpk5jgd3gah8kPA/KbSU3k6s8lzlqAe33yhCgT1L2bXtEVbzOLth"
			"+ndj9QHR910ceOKzgzbrz7jo9t5XlnnxntX78z0HPf/tyLXaSTPM1GhXMU7aKtU3M2QAgIEBWyZt"
			"JcBE/f6LDNSd1Qh/lBcfgGi8sz8enbF2ZFNaA8/cvE0vy85vnh1ghWmd7QIwAfXhaBdqamneWlCF"
			"gwOu6gZqVxQqcTV0aLSvuJ1bIcnieK2amLf5bkEh/tzKWaAPBQqcO36qwdmEnPlfDblyoXTslHGZ"
			"8QDSZ08LH+GZbr4GaeHfFkTsXqjsn00OCxV7yFPNAwDA6ypruRaWbS8vM/mWAxszqnCwo/Yyc+cZ"
			"OrDuAgDgtWSJUlOKWfFmF7aVhjoFlE2meY0JOfsVWPbhVcvPtooLmzzWf/mqtaw6jysG+flZrErL"
			"Qx3klzNt/L/gK+qqVavuZTxZaRcLCHQovPbOkQh/Ysj6V+3jtUDrPwLLCuNCvzuB/jMidJqljv6S"
			"zB42dY3o+MGIT4cXiUKiUusIAGAgCIvFMRPM3fTrqjFdtjdlIPx3fSZOGCp7aRv4kcAQAGT5CamN"
			"71gVJFzqou9yt2CA5M8L15pbco7su1aFZSVdriLUalcxjsnEMRwAFDhOIMgbYqZrq6WPT0bsl875"
			"YaYtS5/Nkstf9ZbGWpskck12qQtHu4FNzUwjI0StGyihEld9JwG/4M+z9wg3D2t3N+6jK9cf6Qtc"
			"uAAAcpTjM93+QcIfz5JTWn2m2SowBRBylBg2feWKObMXf/uh3h83272kpT2qU1XayOGwZa2tSl+h"
			"Urm67vNUZ6hJogH1rNBOQTdMpnkFQ01KMFhOwbG7RAfj9gYWiQ5lKx/QqPU4wvf1s85Iy7iblj3c"
			"39sUGOSqU6OyqwUE4HXhMfSc528XiUSi3VE9WP1B2w0ALUn476pfitxXrw92hJdqZqONzMw9SyOS"
			"6sHQ2v3DL6ZaPMkR4wCIhcu0wMAZUye6UmuZh/CGOLmO/eQ/UyqPHs+RAfrkaOzVd76KjZ6n9/u2"
			"hDJtF1P9gaZ4ZXkLAABaUd5owh/QlJ70xOXTmZ7u7mOmfz65OTW5GCdrV4Fpbm1eL36BAchLxNJB"
			"VlxNvteR1a/AxIkbt+ZNCl8zkY8AYu70Lnr/dikOAETFmTWL92QTqna1GaEuHEpaHp1KrBzt+65e"
			"o1o3UEA1rsgAl1GMlIsSZw9LFnesG6RcaBrpatV2WaUAw3Ez3is8EJnE9g+wQRQAgHCHDMbExRIA"
			"oqlRom/wapHTzh7yVJVGmrk4Ke7dKEIBgKhJv1U+0tWe8t+V1WYuwidJdKCaFdoqoG5y/4VU4EbZ"
			"6lMCAECPZ2rU0tiovLbQ4HFTb79hGbt+zXHw9+KCuqpDSGsK0tUC0qHwdDd1rTYP+cOT+y6XS4ny"
			"iDlJAHqeoX/s+LjHlugJZgSejwhZcXWYmezFC8NZ60bq1V/UThTLfvZ8h8UHzo5xv5xsvVjkZTpQ"
			"sXJ2yupfkj1/mmahxZan7xn02YX1y1fcsONUFmFTVi+BaxEv3gta7+XOBgDcqiA+PKnwE5fO2s/P"
			"iplt1enem+s9e/yZbSFhQ4mixilrx6i5wwMAwJ79HqMTq9sgys8IN99+ObJx8zcpgPA8FocHrxCE"
			"hi3PHm4meVFl9cUGVz1uqxq71IXjQtt9pwJjDZsRHuLOrom/RHLD50sctckplq2LPesy18OWBYjZ"
			"GFfTA4VODnoAyksLtiBwApJSFDDZnFHZdmD0v2af/e83a64aV1U4Ld7U5h6i5opW9pCnCvJ0AADW"
			"yM9Dxq2LWLraxqxFXGe7cIN3l/egXU2TLFGt93WpoIcm9wdUC3zZYEF9YELnlGjPfmkzajMvtP0B"
			"jQaPc738HWK3sFeMNQQA0HNSrTpjvklnlXwk+5VE0gKi6Fx4HLjb/ggIgOUUvHm5l4G2c9fUQj4u"
			"Li4rK6tXu9QrFAqhUJient7hgKyxvLikWoL1tuKu2Lt37+HDhzsewSTVJUXljfKeycUl1SXimtae"
			"CdFAbm7uhg0bKJ0qaygrLq2TdmmXzsIRFRV1//79HovphLyhvKS6WTvb9uzZc+zYsY5HNE4VbaoS"
			"l9VLScc7ExMTk5iYSE05SSKFrMjMzIyMjKQmn6LJqgiFwps3b3Zz0N+VEydO7N69u+MRcoF3I/sp"
			"eZxUdbpZU6hSWVm5Y8cOlYN97dOvbJ6lDe9tG6EWpiF/iG2PpSCGfGoPs3oXtslgG5OOBzTY1XfD"
			"AQB6JpZDdCdN41RZxubWxrrTo06irrNC9yb3A8gF3o3sp+RxUtXpZk3pEfSbwDQ0NDT9FHoDoKGh"
			"oemn0BsADQ0NTT+F3gBoaGho+in0BkBDQ0PTT9H4KyAMw4qKitpaJ/ceGIaVlpZmZGT0qhYtaG5u"
			"lsvlfdCwN1BWVobjeN+0Gcfx4uLit23FayQSiUKh0KGvZDJZdXV17zlfLBb3dnD7bDH2Bg0NDS0t"
			"Lf1ksm00NTVhmOpnhBgKhfrPEu3cubOuro7J7N3PiKAoymAwWKy+9mtUaNv59PT+Tq9R4jhOEETf"
			"tBlFUQRBejudqKPz+PZ2Jv8Fwe2zxdgb/B0LvIcQBIEgSGhoaMeDGoM9aNAgX19fJyenXrUpOjra"
			"29t7/PjxvapFC/bt28fhcObNm/e2DekGeXl5p0+fVglwH2Hjxo0BAQFubm5v2xAlIpGIx+MFBQXp"
			"SmBsbKyjo+O0adN0JVCFhw8fnj9/vleDGx0d7ePj4+Xl1Xsq+g4nT56sr69funTp2zbkr6Oqqiou"
			"Lk7lIP03ABoaGpp+Cr0B0NDQ0PRT6A2AhoaGpp9CbwA0NDQ0/RR6A6ChoaHpp1D+yRfRIs5ISUiu"
			"f+/bf4/TB+nzlN8OJubUsga7f7xo3nhLnfxyjGjIOrP/+PXnrcYjfOYs+NiZ++R42LbrDQAMlrH1"
			"mFmL5nqal8QL401WhkziNr+4m3oxtd519QKPjk0TW55eOnQkKbeWOcTzs0VzRvNBRaSpVhue5MbO"
			"rZUBP84ewQKi5lLMXtmc7wOHMAEXnxfurP9nZLBADwDQ3KNhO9IbAZgc83enBM93K9wVebYIB4UC"
			"GAwA1qgg4Qrnp3G7j96sYNr6LVz2oWPb9x9Vxi2YNrw+YZOoITBsroBDNKTv+fmhy/dLJ3S/MTxe"
			"pPSVUdmNg/viM2uYQ32Dl8wU8BBVJyFEQybZLlI4TBFV9w54cjzy6ojQJeO617YYy1eJq5v4N2G+"
			"R+hcgR6adXDdtRE/rvTmYk9PCS9YrPQXR++s/CDy60ltkcMenwjfXvp+5Dd+ZvDy0YWjv1+tGvWf"
			"sE9f9YVvl8w0sBBMX7jQbxi1z+urTpVXrPSdSeuzlEOHL+bWc+x85iz82JliGEgzHCs9rxQIJInq"
			"vd9NBa9LQ53JhIZyoWlDTYFPN85Vvxh1Ti2iJjV2R01geFtbLjT3aETqkFVTyjZv73iq+Z0uxSsX"
			"JmVcGSzjwQL/oHkf2Bu1RxoAwMB91uTa+MTOq0p3WsCToZbQaM7eBdM/Wyncn5BVjQNg+QfD/vu/"
			"SjvvcdzcA6E/J9b0wIDXSK7FRqVbL4yKiVpodyf656RarKmkkPfRNpFoZ/Qy1yfbfzpTjreU5RdW"
			"o2je8WjR9fycP3OqO3Z8ImpThOsSjGaGbRUu+0fOlsgzZS9VRGppGV77NE/crAAAUEgrC56UtSoA"
			"AHuedC7jcer5u60AAKB4WVzA+2ibSBQbOh09GXGoxu+HXSLRtpkDa4fO/0Uk2vmVj37mgS03hi7f"
			"LAw2PrfpWIHylQyVcb/lEEN8vA0Sth17Im++t2/3vWGTxnZ/9QcARZuvcPGpn/bXvv/91o1zjROi"
			"D+fJSE7C5WrtIoeDNBJtKsl9Vke5DeQrCNW4VluwK67dLcUBfXw99Vby5QdSIBoe3ChA+OymkieZ"
			"5+KSlT3PpRnxp9NzC6pQwIvi1v18x3L2D2tnv8MiS479zqt0T2wCqa9TpfMfAAAgAElEQVS2ekhT"
			"VfoOa7i6eV08a8Z3G9cFD3+wKTzuBcWGZ6QZljeX5RdWowRBlqje+91WoCwNNQpAQ7nQtEMu8GbV"
			"lNCQWojpO+YVCZfyUQAAeU5SYv0gR6NmlVOpiG8zRKllT8zamfwrG9adEePtekUikSh2sX+g6qrS"
			"k9UfqG4ATMvJa36LD52gbL3HsvtX7JnfY1d+Ot3TzpiQyXTTpB5tlmD6XK6hPtd+xtc/zHZ4/YoG"
			"03i471jrihfKZQD0Rs2NEoYE2Bt1bnfadCu50G3Bv5wH6hvbBSxeNJrboioSXsavnr1k7bcrF835"
			"dMVBdU3XumFuftJt/sJVroXJt5o6/Qfb3NlpaGsNafHBq3IKjD0mDuUYOfq6oY9yJSr//3ocz2vp"
			"l9aXtkRH/5LtFTJ/VPeur1VgcJyD1izwtNDn2Q0fzMIJspMw9XaRwkEeqf4VQuq8imsV32Vka25O"
			"Mya+k2fh71Z+Lw+V5+ZVO7iMYAHDyN1dkXbhOQYATbcSc4eNs2cCoHnnU9nvTzYovH3n2Us1qzyb"
			"Z2bCAQXFvUlT5kluXcx3XRA82pJnNjJwyRR5ahrVlpeqMywnNEl83kVWUFWAazZZQ7nQvAFM42Kk"
			"klpMG/9JA25fzkcB5Flp9y39/QYhGk6lJF450kwwM2Sa9FzCU92srZqh9uwG4dsL+NKS9n/rm1iY"
			"ZW6fs+L4c9zms+3TLHViyoAPVnyZH70qKN7Ww2/mnJnjeHAXiMoHCf+TQkvp7cQqz1WOenBb83i8"
			"rrqBZ27e5n6Wnd88OwB5Z5EgzcEJ6+nrw71kp1Z9faXgc4FA2zcBZZkX71m9P99z0PPfjlyrnTTD"
			"DECBPUvZte0RVvM4u2H6d2NVnz4opK1SfTNDBgAYGLBl0lYCTBAAteMG+i4OPPHZQZv1Z1x6eMuO"
			"8Ed58QGIxjv749EZa0c2pak6CStM62wXgAmQw2FUel11pPyutlapxNXQodG+4nZuhSSL47VqYt7m"
			"uwWF+HMrZ4E+FChw7vipBmcTcuZ/NeTKhdKxU8ZlxgNInz0tfIRnuvkapIV/WxCxe+GrxtgKLPvw"
			"quVnW8WFTR7rv6TYSZOceXkAAHhdZS3XwrLt5WUm33JgY0YVDnbUXmbuPEMH1l0AALyWLFFqqj4r"
			"uunCttJQp4CyyTSvMSEvRhpSCxnk52exKi0PdZBfzrTx/4KPwHOVU+FKl+LJIUdMLc1bC6pwaO/+"
			"iPAnhqzvWQt4Mlr/EVjvH4tEpw6t9ZHE7zn1XDe2sIdNXSM6fjDi0+FFopCo1DoCABgIwmJxzARz"
			"N/26asyb25sy9NksuVymvCzFWpskclWRAMAw5vM5gBgZGxByrb9zxADJnxeuNbfkHNl3rQrLSrpc"
			"1WYr/12fiROGyl7aBn4kID3LZSBMJo7hAKDAcQJBWK//gzROlp+Q2viOVUHCJbEu7tqlj09G7JfO"
			"+WGmLYvsJE12qfiuXo/sXu1Riau+k4Bf8OfZe4Sbh7W7G/fRleuP9AUuXAAAOcrxmW7/IOGPZ8kp"
			"rT7TbBWYAgg5SgybvnLFnNmLv/1Q74+br53EYDkFx+4SHYzbG1gkOpRNMcKqmaeUxeGwZa2tyhmj"
			"UjmLw6F+Ea0uc9VINNCUFbpS0A2TaV7BUJMSmlIL4fv6WWekZdxNyx7u720KQDqVgng1dwmypmam"
			"kRECDD3n+dtFIpFod5SuV3/QcgOQ/vHL4gXCy61mNkPNDbD62gZdWIJm7lkakVQPhtbuH34x1eJJ"
			"jhgHQCxcpgUGzpg60bXrlnmIudO76P3bpTgAEBVn1ize8+CeikgtTdMfaIpXlrcAAKAV5Y0m/AFN"
			"6UlPXD6d6enuPmb655ObU5OLcQBAeEOcXMd+8p8plUePk58vMc2tzevFLzAAeYlYOsiK2+571XHo"
			"k6OxV9/5KjZ6nt7v2xLKergFYOLEjVvzJoWvmchH1Dgpm1C1CwDUhKNsIGmk9h8KVI0rMsBlFCPl"
			"osTZw5LFHesGKReaRrq+6ogNhuNmvFd4IDKJ7R9ggygAAOEOGYyJiyUARFOjRN+AvMjp8UyNWhob"
			"KT0DImee0kgzFyfFvRtFKAAQNem3yke62lO+YVSbuQifJNFBY1boSAF1k/svpAI3ylafEgDqUsvU"
			"229Yxq5fcxz8vTo/kVeeSkE8qcZbHp1KrBzt+25vR0+rHYXzDy935o8bZ/kKgTXY79tZOvlekJ5g"
			"RuD5iJAVV4eZyV68MJy1bqRe/cXuSXAKXiEIDVuePdxM8qLK6osNo12x2osdRQJotQfoewZ9dmH9"
			"8hU37DiVRdiU1UvgWsSL94LWe7mzAQC3KogPTyr8xEV5Nst+9nyHxQfOz4qZbdXp3pvrPXv8mW0h"
			"YUOJosYpa8eoPiJ6Ne6fK6QxydaLRV6mAxUrZ6es/iXZ86dpFB9mkCHKzwg33345snHzNymA8DwW"
			"h6s4yVWP26rGLnI4DIfbqI4Up7y6PQXmkOlrwwK17tHLsnWxZ13metiyADEb42p6oNDJQQ9AeZPB"
			"FgROQFKKAiabMyrbDoz+1+yz//1mzVXjqgqnxZtee0d5v6yQNqM280JJj+HUQp4qyNMBAFgjPw8Z"
			"ty5i6WobsxZxne3CDd5vvgelME2yRLXe16WCHprcH1At8GWDBfWBCZ1Tov6C5tTievk7xG5hrxir"
			"3IpVs9DWsivx7ev8q6EYa9iM8BB3DtxtfwQEwHIK3rzcy0Cnc9fUQj4uLi4rK+sNPeZlDWXFJTUt"
			"eI8a1QuFwvT09I5SG8uLS6olmPYiZQ1lxaV10h6K3Lt37+HDhzsewSTVJUXljXLtLVMoFApcUl0i"
			"rmntmRAN5ObmbtiwgdKpJCdpsIvsO9JIakRFRd2/f7+7o96MvKG8pLpZu1TZs2fPsWPHOh7RmCZo"
			"U5W4rL6rGcfExCQmJlJTTpJIISsyMzMjIyOpyadosipCofDmzZvdHPR35cSJE7t37+54hFzgPV+M"
			"/jrxFKisrNyxY4fKQe2fKZFa3OsENs/ShtczCap29VwkAAAwDflDbHssBTHkd/0wq/chOUmDXWTf"
			"9U7YtULPxFLrOw4yGtOEZWxubaw7Peok6jordG9yP4Bc4DpaOf4S8dpCvwlMQ0ND00+hNwAaGhqa"
			"fgq9AdDQ0ND0U+gNgIaGhqafQm8ANDQ0NP0Ujb8CwjCsqKiorXVy74FhWGlpaUZGRq9q0YLm5ma5"
			"XN4HDXsDZWVlOI73TZtxHC8uLn7bVrxGIpEoFAod+komk1VXV/ee88VicW8Ht88WY2/Q0NDQ0tLS"
			"TybbRlNTE4apfluIoVCo/6TXzp076+rqmMze/YwIiqIMBoPF0vkbzj2lbefT0/s7vUaJ4zhBEH3T"
			"ZhRFEQTp7XSijs7j29uZ/BcEt88WY2/wdyzwHkIQBIIgoaGhHQ9qDPagQYN8fX2dnHTylq9GoqOj"
			"vb29x48f36tatGDfvn0cDmfevHlv25BukJeXd/r0aZUA9xE2btwYEBDg5ub2tg1RIhKJeDxeUFCQ"
			"rgTGxsY6OjpOmzZNVwJVePjw4fnz53s1uNHR0T4+Pl5eXr2nou9w8uTJ+vr6pUuXvm1D/jqqqqri"
			"4uJUDtJ/A6ChoaHpp9AbAA0NDU0/hd4AaGhoaPop9AZAQ0ND00+hNwAaGhqafgrln3wRLeKMlITk"
			"+ve+/fc45aew0YLfI7Ze5c/ZFDKxh52JlSoass7sP379eavxCJ85Cz525j45HrbtegMAg2VsPWbW"
			"orme5iXxwniTlSGTuM0v7qZeTK13Xb3Ag6NRgCkCLU8vHTqSlFvLHOL52aI5owc8OR55dUToknFs"
			"ourKjh157l8vncDvchOU3Ni5tTLgx9kjWEDUXIrZK5vzfeAQJuDi88Kd9f+MDBboAQCaezRsR3oj"
			"AJNj/u6U4PluhbsizxbhoFAAgwHAGhUkXOH8NG730ZsVTFu/hcs+dGz//mNnI92w5E0qIzcGD0hR"
			"mViX4EVKXxmV3Ti4Lz6zhjnUN3jJTAEPUfEJHwFCnTs1hIPBMh4s8A+a94G98kvznd1AASxfJa5u"
			"4t+E+R6hcwV6aNbBdddG/LjSm4s9PSW8YLHSXxy9s/KDyK8ntU0Ze3wifHvp++ETi7YefNDW+Iog"
			"+H7fhc+ya8/kbttDniqvWOk7k9ZnKYcOX8yt59j5zFn4sfMAahdMpBmOlZ5XCgSSRKIhU21WdEvB"
			"69JQZ7La+NK0o6bApxvnql+MmAYWgukLF/oN0wcAIGpSY3fUBIa39epCc49GpA5ZNaVs8/aOp5rf"
			"6VK8sqLb4woAYDB68cYJj6OOKuaGfz5KH0B+b9/GR55h87RuY6sOagmN5uxdMP2zlcL9CVnVr3rX"
			"4EX/t3VP8r0HhTUoxd7bXSC5FhuVbr0wKiZqod2d6J+TarGmkkLeR9tEop3Ry1yfbP/pTDneUpZf"
			"WI2iecejRdfzc/7MqcbfIACI2hThugSjmWFbhcv+kbMl8kwZ2lSS+6yOgKYHv4b+v6bJn3p1vfoD"
			"AF77NE/crAAAUEgrC56UtSoAAHuedC7jcer5u60AAKB4WVzA+2ibSBQbOh09GXGoxu+HXSLRtpkD"
			"a4fO/0Uk2vmVj37mgS03hi7fLAw2PrfpWIHylQySkRXmASoj3TNUPEPF4Yo2X+HiUz/tr33/+60b"
			"5xonRB/Ok5F8gqt3p6Zw7IlZO5N/ZcO6M8ouRipuoAChGtdqC3bFtbulOKCPr6feSr78QApEw4Mb"
			"BQif3VTyJPNcXLKy57k0I/50em5BFcNt0Zadu3bt2rVL+MlghekI6w7XMd23hzxVpe+whqub18Wz"
			"Zny3cV3w8AebwuNeUGzORppheXNZfmE1ShBkiXK1WdFtBcrSUKMANJQLTTvkAm9WTYl2j8d+51W6"
			"Jzahpq0GEdN3zCsSLuWjAADynKTE+kGORs0qp1IR32ZIuxaRSCSKXeTOairOTt27+Ui+HACI+ue5"
			"xS/Vv7alNdQ2AKbl5DW/xYdOeN16Dy87v/3Yy+GOhrrrOIo2SzB9LtdQn2s/4+sfZju83ueYxsN9"
			"x1pXvFAuA6A3am6UMCTA3ojxRgHQdCu50G3Bv5wH6hvbBSxeNJrbogAAQJ//37rNj/3XfzfJvAdP"
			"wND8pNv8hatcC5NvNXX6D7a5s9PQ1poa1VUar8opMPaYOJRj5Ojrhj7KlbQd1mAkNc90DYPjHLRm"
			"gaeFPs9u+GAWTqhRp96dGpWyzQQzQ6ZJzyU8xd7kBgq8imsV32Vka25OMya+k2fh71Z+Lw+V5+ZV"
			"O7iMYAHDyN1dkXbhOQYATbcSc4eNs3/9MhkuToyvnTDLid3BbC3s0TRVya2L+a4Lgkdb8sxGBi6Z"
			"Ik9NK+7mCtqeuYQmic/VZ0W3FeCaTdZQLjRvANNYcmyemQkHFK+qm2njP2nA7cv5KIA8K+2+pb/f"
			"IETDqZTEq8LQH+1vdyXmeEEvfZKB2gqI8O0Fw7ivC4+oSdmxv/T9lbNtmbpLqgEfrPiSn7AqaNH3"
			"MadL+M72PASAqHyQ8L//nTq2M+L3Kk9fxzevfKoC8LrqBp65cpFn2fnN+8CeBYA9+m3tL49tAybb"
			"sd8orQtkmRfvWb3v6+nvWpx8rW0DV2DPUnZti/k5bNXBhukzSe0IFdJWqb6BIQOAYWDAlklbCQAA"
			"9UZ27RmqIPxRXgILJtF4Z388OuOfI5u6Vte1UsTU0ry1ugpX6wYqqMTV0MHFviI7t+JOFsdr3kSr"
			"Z3cLCnOfWzkL9AEUOHf8VLObCTlyoubKhdKxUxxfZ5ws+1Sa8bQZ1h1eL9bKHtJU2w7jdZW1XAvL"
			"NulMvuXAxqoqyhtA5xkqm3njtWSJarOi2wqUpaFOAX3VrwUm5JRQYNmHVy1f8u+gtbcFn/i3dyFF"
			"Bvn5WdxNy0NlDy9n2vj78hHSqRTEq/Q0XbJkybKwk08wAABDty//M/TS1t+f9UogtboEltz+dddV"
			"xJK4d/2pnBD/cUFH39NgD5u6RnT8YMSnw4tEIVGpdQQAMBCExeKYCeZu+nXVmK7am6oIYOizWXK5"
			"THlBjbU2SeQACuCMWbV9btP+6HPkTsxUYYDkzwvXmltyjuy7VoVlJV2uarOV/67PxAlDZS9tAz8S"
			"kJ7lMhAmE8dwAFDgOIEgbWuCWiOpeKY7SB+fjNgvnfPDTFsWFXVdK5U1NTONjBC1bqCESlz1nQT8"
			"gj/P3iPcPKzd3biPrlx/pC9w4QIAyFGOz3T7Bwl/PEtOafWZZqvAlMYTdZd/z/rHrIkdn8traY/q"
			"VJU2cjhsWWurUh0qlbM45O7zVGeoSaKB2qzQoYJumEzzCoaalGCwnIJjd4kOxu0NLBIdym6/Ikf4"
			"vn7WGWkZd9Oyh/t7mwKQTqUgvj1RGXrO87eLRCLR7qg5yssGxoAJyxYPTow5XaqbZ+2d0O4ZiP4w"
			"r2ljzFvrm+WEApU0t+jCEjRzz9KIpHowtHb/8IupFk9yxDgAYuEyLTBwxtSJrl23zCMJQMyd3kXv"
			"3y7FAYCoOLNm8Z5sFBgsu/fGuc0J+4J9IurIY42rXyf0B5rileUtAABoRXmjCX9AU3rSE5dPZ3q6"
			"u4+Z/vnk5tTkYhwAEN4QJ9exn/xnSuXR4zkyVSlMc2vzevELDEBeIpYOsuIiAABqjaTgGepg4sSN"
			"W/Mmha+ZyEeoqOtaacujU4mVo33f1WtU6wYKqMYVGeAyipFyUeLsYcnijnWDlAtNI12t2i5kFWA4"
			"bsZ7hQcik9j+ATbIqwdk+POzZxsnfezc8UZLO3vIU1UaaebipLh3owgFAKIm/Vb5SFd7yk/f1GYu"
			"widJdFCbFTpUQN3k/gupwI2y1acEAIAez9SopbGxw2Js6u03LGPXrzkO/l6dfw2jPJWC+DfnKTLQ"
			"d/kXpueO3pXp+C8A3fgVUEeM3D9f6w4AsrSwKxkNfp9O0IUleoIZgecjQlZcHWYme/HCcNa6kXr1"
			"F3skAPSGB68QhIYtzx5uJnlRZfXFBlc9cSoAACCWU74PyVq+cY/T7q/du+yequ8Z9NmF9ctX3LDj"
			"VBZhU1YvgWsRL94LWu/lzgYA3KogPjyp8BMX5dks+9nzHRYfOD8rZrZVp2+fcb1njz+zLSRsKFHU"
			"OGXtGOXKpedEMrJrz1D3ClF+Rrj59suRjZu/SQGE57E4vCt1GpXWX0CzDn295KwCYw2bER7izq6J"
			"v0Ryw+dLHLXJKZatiz3rMtfDlgWI2RhX0wOFTg56AMoNmi0InICkFAVMNmdUKge03Pm/y6Yf/jK0"
			"g4eJmita2UOeKsjTAQBYIz8PGbcuYulqG7MWcZ3twg3eXd2DdjlNskRuq7qs0KGCHprcH1At8GWD"
			"BfWBCZ1Toj37pc2ozbzQTk94uV7+DrFb2CvGKrdihcqptpZdiW8vQuVQBgCwnII3jlUeRviTv1qU"
			"em+H7ueuqYV8XFxcVlZWr3apVygUQqEwPT29wwFZY3lxSbUE01oiWYCsoay4tE7aPTF79+49fPhw"
			"xyOYpLqkqLxRrrVlCoVCocAl1SXimlbS8a6N7Nozubm5GzZsoGQGVZ/0OByviIqKun//fo/F6Iw9"
			"e/YcO3as4xGNU0WbqsRl9V35KiYmJjExkZpykkRNWdGBzMzMyMhIavIpmqyKUCi8efNmNwf9XTlx"
			"4sTu3bs7HiEXuM6y/y8QT4HKysodO3aoHOxrn35l8yxteLoVwDYZbGPSI6MAAIBpyB9i22MpiCFf"
			"7cOsro3ssWe6p64XlPZtNE6VZWxu3eVNYrcgSdSUFTpTQNM15ALXafb3snhtod8EpqGhoemn0BsA"
			"DQ0NTT+F3gBoaGho+in0BkBDQ0PTT6E3ABoaGpp+isZfAWEYVlRU1NY6uffAMKy0tDQjQ0fvEuuO"
			"5uZmuVzeBw17A2VlZTiO902bcRwvLi5+21a8RiKRKBQKHfpKJpNVV1f3nvPFYnFvB7fPFmNv0NDQ"
			"0NLS0k8m20ZTUxOGqX5skKFQqH+5bOfOnXV1dUwmU+3/6goURRkMBovV136NCm07n57e3+k1ShzH"
			"CYLomzajKIogSG+nE3V0Ht/ezuS/ILh9thh7g79jgfcQgiAQBAkNDe14UGOwBw0a5Ovr6+Tk1Ks2"
			"RUdHe3t7jx8/vle1aMG+ffs4HM68efPetiHdIC8v7/Tp0yoB7iNs3LgxICDAzc3tbRuiRCQS8Xi8"
			"oKAgXQmMjY11dHScNm2argSq8PDhw/Pnz/dqcKOjo318fLy8vHpPRd/h5MmT9fX1S5cufduG/HVU"
			"VVXFxcWpHKT/BkBDQ0PTT6E3ABoaGpp+Cr0B0NDQ0PRT6A2AhoaGpp9CbwA0NDQ0/RTKP/kiWsQZ"
			"KQnJ9e99++9x+tB6a+fqgw9RBQAwR8za8MNUNW3Pug3RkHVm//Hrz1uNR/jMWfCxM/fJ8bBt1xsA"
			"GCxj6zGzFs31NC+JF8abrAyZxG1+cTf1Ymq96+oFHhzl6IqLmyLPFuGgUACDAcAaNWfl8D+ONASG"
			"zRVwiIb0PT8/dPl+6YQBWmx5khs7t1YG/Dh7BAuImksxe2Vzvg8cwgRcfF64s/6fkcECPQBAc4+G"
			"7UhvBGByzN+dEjzfrXBXZ3OChCucn8btPnqzgmnrt3DZh45t339UGbdgKve6yjyCNgYPSOnoGFNK"
			"neyLlL4yKrtxcF98Zg1zqG/wkpkCHtLy9NKhI0m5tcwhnp8tmjOajwBBdic5HKYIqIwc8OR45NUR"
			"oUvGda+5JpavElc38W/CfI/QuQI9NOvgumsjflzpzcWenhJesFjpL47eWflB5NeT2uaMPT4Rvr30"
			"/fCJRVsPPmhrfEUQfL/vwmfZsTpKZhpYCKYvXOg3jNrn9VWnyitW+s6k9VnKocMXc+s5dj5zFn7s"
			"TDF7SDMcKz2vFAgkiURDJjkruqvgdWmoM1ltfGnaUVPg041z1S9GalKrs8cF2OXYHTWB4W0NvdDc"
			"oxGpQ779eiK7c+0Y3FRV+c3wq//tuBBMe0fZykEZbAbLeLDAP2jeB/ZG7eEHADBwnzW5Nj6x81Lz"
			"lQ+XPE11UEtoNGfvgumfrRTuT8iqxgEAr3py72GNmeeUDz74YMo4O918yVZyLTYq3XphVEzUQrs7"
			"0T8n1WJNJYW8j7aJRDujl7k+2f7TmXK8pSy/sBpF845Hi67n5/yZU/26kw5iGfDDLpFo28yBtUPn"
			"/yIS7fxqoqOPt0HCtmNP5M339u2+N2zSWG1WfwDAa5/miZsVAAAKaWXBk7JWBQBgz5POZTxOPX+3"
			"FQAAFC+LC3gfbROJYkOnoycjDtX4qZjjo595YMuNocs3C4ONz206VqB8JUNl3G95ZqrzcM/o7Bhq"
			"RivafIWLT/20v/b977dunGucEH04T1abIlyXYDQzbKtw2T9ytkSeKcPVulNNOEgj0aaS3Gfd7k8J"
			"hGpcqy3YFdfuluKAPr6eeiv58gMpEA0PbhQgfHZTyZPMc3HJyp7n0oz40+m5BVUMt0Vbdu7atWvX"
			"LuEngxWmI6xZKpJjv/Mq3RObUEPNNtJUlb7DGq5uXhfPmvHdxnXBwx9sCo97QbHhGWmG5c1l+YXV"
			"KEGQJcrVZkW3FShLQ40CUB9fmteQC7xZNSU0phbJ4yW8d8wrEi7lowAA8pykxPpBjsak2pGTVBIq"
			"C0HOq3dwlar3xKydyb+yYd0ZMd5ujEgkEsUu9g9UXWoorv5AdQNgWk5e81t86IRX/UXx6qo6Bt/G"
			"2lB/gMOECY66+fQ42izB9LlcQ32u/Yyvf5jt8PoVDabxcN+x1hUvlMsA6I2aGyUMCbA36qrdKc9r"
			"6ZfWl7ZER/+S7RUyf1SPusCTzM1Pus1fuMq1MPlWU6f/YJs7Ow1trSEtPnhVToGxx8ShHCNHXzf0"
			"Ua5E5f81jSM5pltmMjjOQWsWeFro8+yGD2bhRNOt5EK3Bf9yHqhvbBeweNFobotCvTtJ4VAzsluW"
			"kHkV1yq+y8jW3JxmTHwnz8LfrfxeHirPzat2cBnBAoaRu7si7cJzDACabiXmDhtn//plMlycGF87"
			"YZYTKbBsnpkJBxQU9yZNmSe5dTHfdUHwaEue2cjAJVPkqWlUW16qzrCc0CTxeRdZQVUBrtlkyuVC"
			"0w6mcTFSTS2yx0ut/CcNuH05HwWQZ6Xdt/T3GyTpTu1oWgjYZoKZIdOk5xKeUrtKoAS1R0AI317A"
			"l5a8PoDrGZrUPbyUnP7wruha5OFIP7OemzLggxVf5kevCoq39fCbOWfmOB7cBaLyQcL/pNBSejux"
			"ynOVox7c7p5MZKDv4sATnx20WX/GRbf3vrLMi/es3p/vOej5b0eu1U6aYQagwJ6l7Nr2CKt5nN0w"
			"/buxqk8fFNJWqb6ZIQMADAzYMmkrASYIQJfjSI7plp0If5QXH4BovLM/Hp2xdmRTWgPP3Lxt32fZ"
			"+c2z0zRQVatR6XXVkfK73TKlAypxNXRotK+4nVshyeJ4rZqYt/luQSH+3MpZoA8FCpw7fqrB2YSc"
			"+V8NuXKhdOyUcZnxr6TIsk+lGU/bYt3h9WIFln141fKzreLCJo/1X1pQu74hZ14eAABeV1nLtbBs"
			"k87kWw5szKjCwY7ay8ydZ+jAugsAgNeSJUpN1WdF9xQoS0OdAsom07zGhLwYqU8tdR5XDPLzs1iV"
			"loc6yC9n2vh/wVfUVavWzst4stKuFgIAxNTSvLWgCn/dORLhTwxZ/6p9vBZo90yEPW7VycRTe7du"
			"277Ipe7G5QfaaleROmzqGtHxgxGfDi8ShUSl1hEAwEAQFotjJpi76ddVY7RobyrLT0htfMeqIOFS"
			"9zqpvxkGSP68cK25JefIvmtVWFbS5ao2W/nv+kycMFT20jbwIwHpuRgDYTJxDAcABY4TCMJ6/R9v"
			"HEdyTPftlT4+GbFfOueHmbYsfTZLLn/VWxprbZLINQ1S0VqvR3kkBVTiqu8k4Bf8efYe4eZh7e7G"
			"fXTl+iN9gQsXAECOcnym2z9I+ONZckqrzzRbBaY0gai7/HvWPwibLjwAACAASURBVGZN7PRcj8Fy"
			"Co7dJToYtzewSHRIQ8P7rqZap5TF4bBlra1KdahUzuJwqF9Eq8tcNRINNGWFrhR0w2SaVzDUpIT6"
			"1FLrcYTv62edkZZxNy17uL+3KTAoVB2DwkIAIGtqZhoZIcDQc56/XSQSiXZH9WD1By03ALws/sc5"
			"i7b98VJWVdOgMDLWySMgNHPP0oikejC0dv/wi6kWT3LEOABi4TItMHDG1ImuWrXMQ58cjb36zlex"
			"0fP0ft+WUKbtFqA/0BSvLG8BAEAryhtN+AOa0pOeuHw609Pdfcz0zyc3pyYX4wCA8IY4uY795D9T"
			"Ko8ez5GpSmGaW5vXi19gAPISsXSQFbfd928eR3JMN63HxIkbt+ZNCl8zkY8AYu70Lnr/dikOAETF"
			"mTWL92hYJElaywZSHEkF1bgiA1xGMVIuSpw9LFncsW6QcqFppKtV22WVAgzHzXiv8EBkEts/wAZ5"
			"de+MPz97tnHSx87q/8yrxzM1amlspPQMiJx5SiPNXJwU924UoQBA1KTfKh/pak/50zFqMxfhkyQ6"
			"aMwKHSmgbnL/hVTgRtnqUwJANbU0eNzU229Yxq5fcxz8vbigruoQ0pqCdLUQALQ8OpVYOdr3XR2G"
			"VKvNg2k5forbqcjVH8QRTAufb+a468ISPcGMwPMRISuuDjOTvXhhOGvdSL36iz2SiD37PSbZerHI"
			"y3SgYuXslNW/JHv+NI3iU4FO6HsGfXZh/fIVN+w4lUXYlNVL4FrEi/eC1nu5swEAtyqID08q/MRF"
			"eTbLfvZ8h8UHzs+KmW3V6d6b6z17/JltIWFDiaLGKWvHqK5cmsaRHNMt24nyM8LNt1+ObNz8TQog"
			"PI/F4cErBKFhy7OHm0leVFl9scFVfTaRw2E43EZ1pDjl1Z0oMIdMXxsWOKRbtnWcvK2LPesy18OW"
			"BYjZGFfTA4VODnoAygsltiBwApJSFDDZnFGpHNBy5/8um374y1CVpxvKW2OFtBm1mReq7i6aylRB"
			"ng4AwBr5eci4dRFLV9uYtYjrbBdu8NbiHrTzNMkSua1vzIqeK+ihyf0B1QJfNlhQH5jQOSXqL6hP"
			"LQ0e53r5O8RuYa8YawgAoOekWnXGfJPOKvlI9iuJpIXgVVZjrGEzwkPcOXC3/REQAMspePNyLwNt"
			"566phXxcXFxWVtabmsxjzdVl1c1ojxrVC4XC9PT0DgdkjeXFJdUSrEdSdcDevXsPHz7c8QgmqS4p"
			"Km+U90wuLqkuEde0dn8gBcfk5uZu2LCBmrSGsuLSOqkWWqmOVCEqKur+/fvdHdV77Nmz59ixYx2P"
			"aHQw2lQlLqvvasYxMTGJiYnUlJMkUsiKzMzMyMhIavIpmqyKUCi8efNmNwf9XTlx4sTu3bs7HiEX"
			"eDcWI0oeJ9WObtYUqlRWVu7YsUPlYA8eHzGN+IN1fnnB5lna8HQtVCcwDflDbHssBTHka/UwS8eO"
			"YZsMtjHRSivVkX87NDqYZWxurZvfuWmUqHVWUFVA0zXkAu9GzVHyOKl2dLOm9Aj6TWAaGhqafgq9"
			"AdDQ0ND0U+gNgIaGhqafQm8ANDQ0NP0UegOgoaGh6ado/BUQhmFFRUVtrZN7DwzDSktLMzIyelWL"
			"FjQ3N8vl8j5o2BsoKyvDcbxv2ozjeHFx8du24jUSiUShUOjQVzKZrLq6uvec///ZO/OAJs6t/59M"
			"AoQtiIRFQRGlYL1sBRdAAbVgRaELelsuVXyl2te1YlttLepVwYZqBVtRU5frvlze6kUFBcEdvVYU"
			"kU0EFJCwg4ABEjKZye8PAsLMRIYQqv0xn/8c5znn+5xznudJhiRHJBINdHLf2sU4EDQ1NbW1tQ2S"
			"yXYgFovlcuLPCLEUCuqfJYqLi3vx4gWbPbA/I4KiKIvF4nD682XmAaHj5NPS+it9jRLDMBzH307N"
			"KIoiCDLQ5UQfjed3oCv5T0juW7sYB4K/4gLvJwqFgsViRUREdL+oMtnm5uY+Pj6Ojo4Dqik6OtrL"
			"y2vy5MkD6kUN9u/fz+Vy58+f/6aF9IH8/PwzZ84QEvyWsHXrVn9/f1dX1zctRIlQKOTxeCEhIZoy"
			"GBsba29vP2vWLE0ZJPDo0aMLFy4MaHKjo6O9vb09PT0HzsXbw+nTpxsbG5cuXfqmhfx51NbWxsfH"
			"Ey4yfwNgYGBgGKQwBwADAwPDIIU5ABgYGBgGKcwBwMDAwDBIYQ4ABgYGhkEK7Y984W2izNTElMb3"
			"vv2fSToAAC3Faf/+v7R8g5n/XDlVMz9TiTdlnz1w8maJxGCMd/DCT5wMC0+u33mzCYDFMbCcMGfR"
			"PA/T8gRBgtHKcB9FVvye47er2da+Ycs+tFf+kCKad3z9rvRmADbX9N0ZoQs/0L22TdgUuH6eAxdv"
			"St/70yPn75ZOUacvfOutuB01/j/MHcMBvP5yzL724O8CrdiAiS4I4ho/jgx10CJ7X+BavDvyXCkG"
			"CgWwWACccSGCFU5PibLx6kvbet4WvHL0f49pQDVW2hGrafqVtw7vT8iqZ4/0CV0S5MBD2p5ePnIs"
			"Oa+BbeXx2aLg8XwEb6IKp4p0sDgGwxz8QuZ/YKv8KdieYaCBvICQV1fRIUGBe8Q8By00+/DGG2N+"
			"WOllKH/6u+Ci2Uo/UXRczQeRq6YZIwAA8ienNvxS8X7k19PZ2RSS1dNDnirvuTJ2RpJnqUeOXspr"
			"5Np4B4d94kQzD6QZTpReUBoEkkXq6PfRQdfSoJSMt5RlpF1Ka3T5ZqG7Zjuj/v8BxQKfbZBHvRmx"
			"dc0cZoeF+Y7SAQDA69Nid9UHbuhoy4XmHd+cZrV6RuX2X7rfanqvV/Md5d2Z125rrCvTAAC6bnOm"
			"NyQk9dxV+tICngy9gkZz9y2c/dlKwYHE7DoMAKA9/1+rFkWlvBj+nuPIfraw6KL1RmxUumVYVExU"
			"mM296J+SG+Ti8mLeRzuFwrjoZS6Fv/x4tgprqyworkNlWQd/vjVy+XZBqMH5bSeKOr/boHj5vIj3"
			"0U6hMDZiNnp686HH5t5euok7TxTKWu7v33N/1LSJ6uz+AIA1PM0XtSgAABTSmqLCSokCAOQlyecz"
			"n6RdyJBQeT9S7/v9bqFwZ9DQhpELfhUK477y1qGQjVj4E26baq8Z1YqOWGGi33880PD+dzu2zjNI"
			"jD6a396QKtiYqB+0fodg2d9yf448W4lRh1NVOvbGrAviX9uy8ayyxyYhDDTAiXmtM9OuvpFRgQH6"
			"5GbanZSrD6WANz28VYTwtcXlhVnn41OUPc+lmQln0vOKalVVgHp6yFNVxk7edH37xgROwNqtG0NH"
			"P9y2Ib6MZk850gyrWioLiutQHCdbfM1U+uJAuTQoHACafzJaeLMg94/cOg22Rf3/CfICbyGWRFfE"
			"Y9d6VuyNTVS2bEeM3zGtTrxcgAIAyHKTkxrN7fVbCLfSMd8hBCetsS6/QqFQGLvYL5C4q/Rn9we6"
			"BwDbYvqaQwkRUzr7i768dvTE07GfhU0fZaLL1lRRoS2tch1DQz0dQ9uAVd/PtXv1+o1tMNpnomV1"
			"mXIbwGpziwzcp47k6tv7uKKP81qJlrRNnRxHSurrcZ7n0i8tL/8cHf1rjmf4gnHaGlLaIbcg+S4/"
			"bLVLccodMbV3woBeZXeiUdUsrlPImoUeZjo8m9HDOBguvpNS7LrwH05DdQxs/BcvGm/YJqfWpTId"
			"2iYOQeGzpOcTn8pfFwYadOa1lu88VpKX2yIX3cs383Otup+PyvLy6+ycx3CApe/mprhysUQOAOI7"
			"SXmjJtmyXxdKdfSommrrnUsFLgtDx1vwTMYGLpkhS7vyvI/F3lW5uCqLJXSrohcHmGrJWuPmRQnC"
			"/W31me7AtJGr3Iy0eSZGXFB0rm72CL9pQ+5eLUABZNlXHlj4+ZojKm6lZV45sscaG0joPQJC+LYO"
			"fGl55z/RkoKiFmnDpROoTmXer6nfH4v5eFj/pQz5YMWXBdGrQxKs3X2DgoMm8SAD8JqHif+RQlvF"
			"3aRaj9X2WnAXAEAqkeqY6LEAQFdXu10qwcGoI+gK+bPU3Tsfy+uf5DTNXjtRBxAdn8WBpz47PGLT"
			"WWfNvvdtz7p0f/j7CzzMSw4du9EwLcCEwntPFCplE0GGak41wh/nyQfAm+8dSEAD1o0VX2nimZp2"
			"+OXY+M63AXnxlZ66AIyAOh1dRo0tTCVFtRjYYcQw0HtFQcirnl2zbfXdvOrWbK7n6qn52zOKirGS"
			"4U4OOlCkwAwnz9Q9l5i74CuraxcrJs6YlJXwmgogpYWOHvJU8wEAsBc1DYZmFh1fXmbzLYY2Z9Zi"
			"YEPvy8w9Z2jHyQAAwBrIFqXGNKvi9SHsWBpUDmhLZniFEbn6FfKco6uXn5OIisXum77sai2LmPv6"
			"mq2+ko/aya5mjfD7go9ACeFWuNareXLKu9ZYV/dHhD81fFP/WsCTUfOZiBxDbIK3Hzq4b5V7+70b"
			"9zWjRXvUzDXCk4c3fzq6VBgelfYCBwAWgnA4XBOHedt+Wz2hs/8YwmZjcgwAFBiGI8iriLAQ/rve"
			"U6eMbH9pHfiRgx4AtBckpjW/M7wo8bJIg29/WdD6x8UbLW25x/bfqJVnJ1+txSm99xykUjYJTauW"
			"Pjm9+YA0+Psga46ONkcma1f+/IdcIm6VqdJFlY4ugeIWtr4+QhkGWhDyquPowC/649x93NXd0s3V"
			"8PG1m491HJwNAQBkKNd7tu3DxP8+S0mVeM+yVsgVqitATT3EqSo1crna7RKJMlaoVMbhcum/iKaq"
			"XAqLuvSrQj0HfZDM0AmLoiRYHMfQ2N3Cw/H7AkuFR3K6fiQN4fv4WmZeycy4kjPaz8sYgHQrDfMU"
			"hdq5xlhaTgt+EQqFwj1Rmt79Qc0DgG01ykqr/unTpvYXdU0Yl9fPx1AdoFl7l25ObgQ9S7cPv5hp"
			"VpgrwgAQM+dZgYEBM6e6dGuZxza1NG0UlckBZOUiqflww1eTQHhWji4T//6/M2qOn8xtB7TweOz1"
			"d76KjZ6v9e+diZXqbqY6Q42xmqo2AAC0uqrZiD9EnJ5c6PxpkIeb24TZn09vSUt5jpG9E3iNbEIk"
			"NKO6E7koaeuO/Gkb1kzlI4CYOr6LPrhbgQEAXn12zeK9OThRV4cIqnQoaXv8e1LNeJ93tZopw0AD"
			"Yl6RIc7jWKmXWp3cLTiGE10h9aJ4rEtnR2zQmxTwXvHByGRtP/8RiAJUh1I9PeSpKkWaODsq7t8q"
			"RQEAr0+/UzXWxZb235UpKxfhkyza0a0KdR3Qlzx4IS1w/RzqkgAA0OIZ67c1N3fbso29fEdl7v4t"
			"187Ps+dWqLyVhnlSnXatsQGZ8CvUOlEQs1lLwq6tiQrwwdmmU1ZFaOTHQ7QcAgIvbA5fcX2USXtZ"
			"md6cjWO1Gi9R32roNXfy2Z3h60fipc0z1k0g/RWaYzt3gd3ig+cmuF1NsVws9DQeqlg5N/WbX1M8"
			"fpxlpsaRp+MR8tnFTctX3LLh1pTKZ3yzBG5sLnsvZJOnmzYAYMOLEjYkF//duaf3C3Ni5g7v8d67"
			"N9kdyJ/9O0YjqjvAq84Ktt99ObZ5+9epgPDcF28IXeEQsX55zmiT1rLa4V9scdEylFDookrHxY43"
			"owo5Z1TAhnA37fqEy6QwfL7EXp2a4lg723KuGrpbcwAxmeBifLDY0U4LQNbxv9oOgVOQ1FL/6aas"
			"GgBQEUq8/ppaeshTBVk6AABn7OfhkzZuXvrNCJM20QvrsC1e/e2BTWGRMvqadNBPyYMB4gJfNsyh"
			"MTCxZ0l0Vb+0BR0xP6LHE15DTz+72J+1V0xUHsUKwq3WFr2Z79rnFT3XGBcyuh4BAXAcQ7cv99TV"
			"6NxVtZCPj4/Pzs5+XZP59qaq8pqXaL8a1QsEgvT09O5Gm6uel9e1ynsdibXWlYvqJf3y/hr27dt3"
			"9OjR7lfkrXXlpVXNsv7ZHUjZeXl5W7ZsoXVre1Pl84oX0l510U5Hb0RFRT148KDfZrrTr1Du3bv3"
			"xIkT3a+onCoqrhVVNkpJ13sSExOTlJREzznJIo2pZGVlRUZG0rNPUzIRgUBw+/btPg76q3Lq1Kk9"
			"e/Z0v0Je4Bqr/j/BPA1qamp27dpFuNiPZ0raRhZWmjuIOo3yLEbQ+lYBose3ovWRaU3B1uNbWffb"
			"yp8umxpto2EjjLpfUKGLdjreABoOpcqpcgxMLQ0054fKoqarQvOSBwHkBa7R6h9g8+rCfBOYgYGB"
			"YZDCHAAMDAwMgxTmAGBgYGAYpDAHAAMDA8MghTkAGBgYGAYpKj8FhKJoSUlJR+vkgUMmk4lEoszM"
			"zAH1ogYvX76UyWRvobDXUFlZiWHY26kZRdGysrI3reIVYrEYx3ENxkoikdTW1g5c8EUikVwuH9Dk"
			"ymSyioqKt7N+NE5TU1NLS8sgmWwHYrGYvJ+zFAoF5d1xcXEvXrxgswf2Z0RQFGWxWByOxr/h3F86"
			"IqWl9Vf6GiWGYTiOv52aURRFEGSgy4k+Gs/vQFfyn5Dct3YxDgR/xQXeTxQKBYvFioiI6H5RZbLN"
			"zc19fHwcHR0HVFN0dLSXl9fkyZMH1Isa7N+/n8vlzp8//00L6QP5+flnzpwhJPgtYevWrf7+/q6u"
			"rm9aiBKhUMjj8UJCQjRlMDY21t7eftasWZoySODRo0cXLlwY0ORGR0d7e3t7emrki/1vO6dPn25s"
			"bFy6dOmbFvLnUVtbGx8fT7jI/A2AgYGBYZDCHAAMDAwMgxTmAGBgYGAYpDAHAAMDA8MghTkAGBgY"
			"GAYptD/yhbeJMlMTUxrf+/Z/JkmvxXx38rGyhwFLx21R3JKJGtCCN2WfPXDyZonEYIx38MJPnAwL"
			"T67febMJgMUxsJwwZ9E8D9PyBEGC0cpwH0VW/J7jt6vZ1r5hyz60f/VDim1PLx85lpzXwLby+GxR"
			"8Hg+EEwaq3Xgtd6K21Hj/8PcMRzA6y/H7GsP/i7Qig2Y6IIgrvHjyFAHLQBA846v35XeDMDmmr47"
			"I3SBa/HuyHOlGCgUwGIBcMaFCFY4PSXLJoxbOGt0Y+I2YVPg+nkOXLwpfe9Pj5y/Wzql743hsdKO"
			"WE3Tr7x1eH9CVj17pE/okiAHHkIMEoI3UYWTmA5jhBjeIYUnI6+PiVgyqW9ti+UFhLy6ig4JCtwj"
			"5jloodmHN94Y88NKL0P5098FF81W+omi42o+iFw1rSNz8ienNvxS8X7k19PZ2RSSuyyzdc0cZoeF"
			"+Y6i9/P6xKnynitjZyR5lnrk6KW8Rq6Nd3DYJ04000Ca4UTpBaVBIFmkjn4fHXQtDUrJeEtZRtql"
			"tEaXbxa6a7Yz6v8fUCzw2QZ51JtRz9LC69Nid9UHbujo1YXmHd+cZrV6RuX2X7rfanqvV/PKjakr"
			"rwAAuuMXb53yJOq4Yt6Gz8fpAMju79/62GP9fAdNfnSVXkGjufsWzv5speBAYnYdBsC18QgIDAwM"
			"DJztrF2S38o11YiU1huxUemWYVExUWE296J/Sm6Qi8uLeR/tFArjope5FP7y49kqrK2yoLgOlWUd"
			"/PnWyOXbBaEG57edKOrsm4w3pAo2JuoHrd8hWPa33J8jz1a+JJhUUxnW8DRf1KIAAFBIa4oKKyUK"
			"AJCXJJ/PfJJ2IUMCAACKl8+LeB/tFApjI2ajpzcfqff9frdQuDNoaMPIBb8KhXFfeetQyiaMO5SL"
			"W3l76SbuPFEoa7m/f8/9UdMm9n33BwBFR6ww0e8/Hmh4/7sdW+cZJEYfzW8nBQmjDic5HaSRqLg8"
			"7xlVN7vXgxPzWmemXX0jowID9MnNtDspVx9KAW96eKsI4WuLywuzzsenKHueSzMTzqTnFdWqrIBO"
			"y7FrPSv2xibW09NGmqoydvKm69s3JnAC1m7dGDr64bYN8WU0G56RZljVUllQXIfiONmiiqn01YFy"
			"aVA4ADT/ZLTwZkHuH7l1GmyL+v8T5AXeQiwJFaWFGL9jWp14uQAFAJDlJic1mtvrtxBupWO+Q0iX"
			"F6FQKIxd5MYRP89J27f9WIEMAPDGkrznL6m/tqU29LYWtsX0NYcSIqYo+4vqjPII+PDDDz+cZlxf"
			"opgUFND/n8kHAEBbWuU6hoZ6Ooa2Aau+n2v36pxjG4z2mWhZXabcBrDa3CID96kjufr2Pq7o47xW"
			"5W3iOynFrgv/4TRUx8DGf/Gi8YZtRJPwMuGbuUvWfbtyUfCnKw6Tuzb2SW5B8l1+2GqX4pQ74h7/"
			"oW3q5DhSUk/afFTJJo/jeS790vLyz9HRv+Z4hi8Y17fX1wRYXKeQNQs9zHR4NqOHcTCcHCQ5tS5S"
			"Osgj+1uLnXmt5TuPleTltshF9/LN/Fyr7uejsrz8OjvnMRxg6bu5Ka5cLJEDgPhOUt6oSbbsXkMJ"
			"2jwTIy4oaJ5Nqiqv9c6lApeFoeMteCZjA5fMkKVdodvykjjDKlyVxZLepkLTAaZasta4eVGCcH9b"
			"faY7MG3kKjcjQmmxR/hNG3L3agEKIMu+8sDCz9ccUXErLfNEWDrj/WyuxZwsGqCfZKD3CAjh2zrw"
			"peU9L2IVF09fR6ZtfZ+vmT8kDPlgxZcF0atDEqzdfYOCgybxIAPwmoeJ/5FCW8XdpFqP1fZacBcA"
			"QCqR6pjosQBAV1e7XSrBwQgBwF7UNfFMTTvEcGx859sAyHqaBGkuhlvO3rTBs/331auuFX3uoPbb"
			"qfasS/eHv7/Aw7zk0LEbDdMCTAAU8mepu3c+ltc/yWmavXYi8emDglo25bihPosDT312eMSms879"
			"fMuO8Md58gHw5nsHEtCAdWPFV4hBkhdf6akLwAjI6dCvuEkcKctQVxUhr3p2zbbVd/OqW7O5nqun"
			"5m/PKCrGSoY7OehAkQIznDxT91xi7oKvrK5drJg4Y1JWgqoKAACFPOfo6uXnJKJisfumL2l20iRX"
			"Xj4AAPaipsHQzKLjy8tsvsXQ5sxaDGzofZm55wztOBkAAFgD2aLUWMVU+hbCjqVB5YC2ZIZXGJE3"
			"IxWlhZj7+pqtvpKP2smuZo3w+4KPQAnhVrjWq/mulHc1gET4U8M3BQGAnuuXn8LXO/7tvcN8ACaq"
			"/t4ty/v99+xhAXMmaayVkfaomWuEJw9v/nR0qTA8Ku0FDgAsBOFwuCYO87b9tnpCZ3tThM3G5BgA"
			"KDAMRxDlIcbS0ebIZO3Kl6VyibhVRjQJACwDPp8LiL6BLi5T+1BlQesfF2+0tOUe23+jVp6dfLW2"
			"Qyv/Xe+pU0a2v7QO/MiBFBYWtWzKce0FiWnN7wwvSrxMbhetBtInpzcfkAZ/H2TNIQdJlS5C7Bq1"
			"yOFVH0JedRwd+EV/nLuPu7pburkaPr5287GOg7MhAIAM5XrPtn2Y+N9nKakS71nWCrlCVQUAAIvj"
			"GBq7W3g4fl9gqfBIDs0MEytPaYvL1W6XSJQzRqUyDpdL/0U0VeVSWNRVORUNOeiDZIZOWBQloaq0"
			"EL6Pr2XmlcyMKzmj/byMAUi30jDf9S6BpeW04BehUCjcE9XxdwUA1pApyxYPS4o5U9Hnp629o/YB"
			"0HTjdFKt8ycfqtUCnAo0a+/SzcmNoGfp9uEXM80Kc0UYAGLmPCswMGDmVJduLfPYppamjaIyOYCs"
			"XCQ1H27YMQnE1PFd9MHdCgwA8OqzaxbvfXifYFJNaTpDjbGaqjYAALS6qtmIP0Scnlzo/GmQh5vb"
			"hNmfT29JS3mOAQDCs3J0mfj3/51Rc/wk+fmSCtkU49DC47HX3/kqNnq+1r93Jlb28wiQi5K27sif"
			"tmHNVD5CEaQcnKgLACjSUTmUNFL9d6XEvCJDnMexUi+1OrlbcAwnukLqRfFYl+EdL1wVoDcp4L3i"
			"g5HJ2n7+IxAFvC6UnWjxjPXbmptprRhy5SlFmjg7Ku7fKkUBAK9Pv1M11sWW9htGyspF+CSLdr1O"
			"pZ8O6EsevJAWuH4OdUkAUJWWsZfvqMzdv+Xa+Xka9rCrvJWG+devcWSoz/IvjM8fz2jX8F8A+vAp"
			"oJ5gpef/fYvl9c+ZwzX2OVIth4DAC5vDV1wfZdJeVqY3Z+NYrcZL1Lcaes2dfHZn+PqReGnzjHUT"
			"Op+1aDmGrnCIWL88Z7RJa1nt8C+2jHeRN1zqbhJArTNAxyPks4ublq+4ZcOtKZXP+GYJ3Nhc9l7I"
			"Jk83bQDAhhclbEgu/ruz8m6O7dwFdosPXpgTM3d4j/feKmQDYdzHK6QxKZaLhZ7GQxUr56Z+82uK"
			"x4+zaD7MIINXnRVsv/tybPP2r1MB4bkv3kAIkouWoYRCFzkdeqNHEEeKUjvfsALbava69YFqN4nm"
			"WDvbcq4aultzADGZ4GJ8sNjRTgtA+SZD2yFwCpJa6j/dlFUDAK8JpfIdtELago6YH0F6DEcJeaog"
			"SwcA4Iz9PHzSxs1Lvxlh0iZ6YR22xUu/N1u9TZNskTL6mnTQT8mDAeICXzbMoTEwsWdJNF5UXVqG"
			"nn52sT9rr5ioPIqJVWht0Zv5rlO66xEQAMcxdGvnpysR/vSvFqXd36X5uatqIR8fH5+dnT2gXeoV"
			"CoVAIEhPT+92ob256nl5Xau815FYa125qF5C/o/2psrnFS+kapjszr59+44ePdr9iry1rry0qlnW"
			"JzMkVMrWAHl5eVu2bKF1KylIKnSRY0caSY+oqKgHDx70ddRr6Vco9+7de+LEie5XVJYJKq4VVTb2"
			"NuOYmJikpCR6zkkWaUwlKysrMjKSnn2akokIBILbt2/3cdBflVOnTu3Zs6f7FfICV2vnUMUAm6dB"
			"TU3Nrl27CBfftp9+1eZZjODRuRHR41tR/vVB22jYCCO1TL4eth7fqv8fd1Ip+8+FFCQVusixI418"
			"Y2g4lCrLhGNgammgOT9UFjVdFZqXPAggL3AN7Rx/inl1Yb4JzMDAwDBIYQ4ABgYGhkEKcwAwMDAw"
			"DFKYA4CBgYFhkMIcAAwMDAyDFJWfAkJRtKSkhNxFXrPIZDKRSJSZmTmgXtTg5cuXMpnsLRT2Gior"
			"KzEMezs1oyhaVlb2plW8QiwW4ziuwVhJJJLa2tqBC75ImiLCiwAAIABJREFUJJLL5QOaXJlMVlFR"
			"8XbWj8ZpampqaWkZJJPtQCwWk/dzlkJB/eWyuLi4Fy9esNkD+zMiKIqyWCwO5237NCp0REpL66/0"
			"NUoMw3Acfzs1oyiKIMhAlxN9NJ7fga7kPyG5b+1iHAj+igu8nygUChaLFRER0f2iymSbm5v7+Pg4"
			"OjoOqKbo6GgvL6/JkycPqBc12L9/P5fLnT9//psW0gfy8/PPnDlDSPBbwtatW/39/V1dXd+0ECVC"
			"oZDH44WEhGjKYGxsrL29/axZszRlkMCjR48uXLgwoMmNjo729vb29PQcOBdvD6dPn25sbFy6dOmb"
			"FvLnUVtbGx8fT7jI/A2AgYGBYZDCHAAMDAwMgxTmAGBgYGAYpDAHAAMDA8MghTkAGBgYGAYptD/y"
			"hbeJMlMTUxrf+/Z/JukAVvvH8X3/d68SM3Ob8+WCKcM08skxvCn77IGTN0skBmO8gxd+4mRYeHL9"
			"zptNACyOgeWEOYvmeZiWJwgSjFaG+yiy4vccv13NtvYNW/ahvZ4KA8YItD29fORYcl4D28rjs0XB"
			"44cUnoy8PiZiySRtvPbarl35bquWTum9o2XrrbgdNf4/zB3DAbz+csy+9uDvAq3YgIkuCOIaP44M"
			"ddACADTv+Ppd6c0AbK7puzNCF7gW7448V4qBQgEsFgBnXIhghdNTKtkEka7ylG2EkVtDh6QSJtYr"
			"WGlHrKbpV946vD8hq5490id0SZADDyHEhI8A3lKWkXYprdHlm4XuXS0oVaSDxTEY5uAXMv8DW+Uv"
			"zfcMAw3kBYS8uooOCQrcI+Y5aKHZhzfeGPPDSi9D+dPfBRfNVvqJouNqPohcNa1jyvInpzb8UvF+"
			"5Ne+xm0UktXTQ54q77kydkaSZ6lHjl7Ka+TaeAeHfeI0hN4LJtIMJ0ovKA0CySLeRFnMfXPQtTQo"
			"JVPml6ELigU+2yCPejNi65o5zA4L8x31qiFAz4g7yK/G7qoP3NDR0AvNO745zerbVVO1e6463dtE"
			"l1+Pvv7PbhvIwlnvKBeYMtndFl5X+gEAdN3mTG9ISOq51XzlbUieJhX0ChrN3bdw9mcrBQcSs+sw"
			"AGi5ujNC+FBngrvR40Prf05+QT/Ur6H1RmxUumVYVExUmM296J+SG+Ti8mLeRzuFwrjoZS6Fv/x4"
			"tgprqyworkNlWQd/vjVy+XZBqMH5bSeK5CoMAN6QKtiYqB+0fodg2d9yf448W4mKy/OevcBB/PC3"
			"iH+Jp3/qSaufMdbwNF/UogAAUEhrigorJQoAkJckn898knYhQwIAAIqXz4t4H+0UCmMjZqOnNx+p"
			"9/1+t1C4M2how8gFvwqFcV9561DKJomsNvUnjHTLJESGTqMrRUesMNHvPx5oeP+7HVvnGSRGH81v"
			"J8UEQ/NPRgtvFuT+kVvXrTGRqnTsjVkXxL+2ZeNZZRcjQhhogBPzWmemXX0jowID9MnNtDspVx9K"
			"AW96eKsI4WuLywuzzsenKHueSzMTzqTnFdWi1JLV1EOeqjJ28qbr2zcmcALWbt0YOvrhtg3xZTSb"
			"s5FmWNVSWVBch+I42aKKYu6rA+XSoHAArwkWAwBQLfAWYkl0RTx2rWfF3tjE+s41SIp4Oe8d0+rE"
			"ywUoAIAsNzmp0dzegLTqZCSXeM8N5FBu53e2cNLC6xIjFAqFsYv9AolbDc3dH+geAGyL6WsOJURM"
			"6ewvKpdKUZ2REwMCPEZxMamUXtH2BtrSKtcxNNTTMbQNWPX9XLtXr9/YBqN9JlpWlym3Aaw2t8jA"
			"fepIrr69jyv6OK9VhQEQ30kpdl34D6ehOgY2/osXjTdsUwAAoCX/t3H7E79Na6eZ9uMJGFqQfJcf"
			"ttqlOOWOuMd/aJs6OY6U1NcTd2kVslWIpBeZ3mFxnULWLPQw0+HZjB7GwXAKd1rj5kUJwv1t9Xt0"
			"j1XpVNvEISh8lvR84lP568JAg8681vKdx0ryclvkonv5Zn6uVffzUVlefp2d8xgOsPTd3BRXLpbI"
			"AUB8Jylv1CRbNgC1ZFBTj6qptt65VOCyMHS8Bc9kbOCSGbK0K8/7uIN2VS6uymKJimLuqwNMtWTV"
			"wWJQhVzlktPmmRhxQdG1uskRrxjuN23I3asFKIAs+8oDCz9f89beF/krVG0gPReehqD37Abh2zrw"
			"peVd/x7i+/nHR5ZHzZoJyPCgmJlmGpEy5IMVXxZErw5JsHb3DQoOmsSDDMBrHib+RwptFXeTaj1W"
			"22vBXQAAqUSqY6LHAgBdXe12qQQHI4TCAFZxs4lnqtzkOTa+821AlgHyx4fW/bf93fURNtr9Udue"
			"den+8PcXeJiXHDp2o2FagAmAQv4sdffOx/L6JzlNs9eS2hEqKGVjL+qIInuPTB/OLYQ/zpMPgDff"
			"O5CABqwbK77SqzsVTiHjlVFjC1NJUS0GdhgxDPSkEfKqZ9dsW303r7o1m+u5emr+9oyiYqxkuJOD"
			"DhQpMMPJM3XPJeYu+Mrq2sWKiTMmZSW8xjApLXT0kKeaDwCAvahpMDSz6PjyMptvMbQ5sxYDG3pf"
			"Zu45QztOBgAA1kC2KDWmLOY+OlAuDSoHtCUzvMKIXP0Kec7R1cvPSUTFYvdNX3b2aKWKuMLc19ds"
			"9ZV81E52NWuE3xd8BXmRv6So4l42EOi28Lo6RyL8qeGbOtvHq4FaL4GxivMHz4k9v963f91U9NLB"
			"MyXqeu+J9qiZa4QnD2/+dHSpMDwq7QUOACwE4XC4Jg7ztv22ekJne1OEzcbkGAAoMAxHEI4KAywd"
			"bY5M1tlHWS4Rt8oAFMCdsPqXeeID0ed76cT8GljQ+sfFGy1tucf236iVZydfre3Qyn/Xe+qUke0v"
			"rQM/ciA9y2VRyqYUSScyfUH65PTmA9Lg74OsOXTc9e60XdzC1tdHKMNAC0JedRwd+EV/nLuPu7pb"
			"urkaPr5287GOg7MhAIAM5XrPtn2Y+N9nKakS71nWCvlrXjypqYc4VaVGLle7XSJRukOlMg6XS/9F"
			"NFXlUljUVVXMmnLQB8kMnbAoSoLFcQyN3S08HL8vsFR4JEf5gIYy4gjfx9cy80pmxpWc0X5exrQW"
			"OavXDQTg1cJjaTkt+EUoFAr3RPVj9wc1DwC8vqpGqmtmY2s3xkJfVlNV1w8BXaBZe5duTm4EPUu3"
			"D7+YaVaYK8IAEDPnWYGBATOnunRrmcc2tTRtFJXJAWTlIqn5cEOE2gBi6vgu+uBuBQYAePXZNYv3"
			"5qDA4ti8N8k1eP0X2qeijj1Rufv1QGeoMVZT1QYAgFZXNRvxh4jTkwudPw3ycHObMPvz6S1pKc8x"
			"AEB4Vo4uE//+vzNqjp/MbSdaoZZNKZJGZOgjFyVt3ZE/bcOaqXyEjrvenbY9/j2pZrzPu1rNlGGg"
			"ATGvyBDncazUS61O7hYcw4mukHpRPNZleMfLKgXoTQp4r/hgZLK2n/8I5DXbP6inhzxVpUgTZ0fF"
			"/VulKADg9el3qsa62NJ++kZZuQifZNGOupg154C+5MELaYHr51CXBACAFs9Yv625WfnaQkXEjb18"
			"R2Xu/i3Xzs/TkHKRI6Q9BeltA+m28DQ3dbUOD61xgf+YcnVn+IzzAEMmLv/YWRNKtBwCAi9sDl9x"
			"fZRJe1mZ3pyNY7UaL1Hfaug1d/LZneHrR+KlzTPWTdBRYQC0RoeucIhYvzxntElrWe3wL7a4aInS"
			"AAAAsZjxXXj28q17Hfescuu1e6qOR8hnFzctX3HLhltTKp/xzRK4sbnsvZBNnm7aAIANL0rYkFz8"
			"984ocGznLrBbfPDCnJi5w3u891Yh25EksvfI0A0qAF51VrD97suxzdu/TgWE5754Q2/uVDptvNjx"
			"vlMh54wK2BDupl2fcJkUhs+X2KtTUxxrZ1vOVUN3aw4gJhNcjA8WO9ppASgPaG2HwClIaqn/dFNW"
			"zWumWn9NLT3kqYIsHQCAM/bz8EkbNy/9ZoRJm+iFddgWL/1eTPU6TbJFQwlVVWjQQT8lDwaIC3zZ"
			"MIfGwMSeJdFV/dIWdMT8iK4HNCoibujpZxf7s/aKiXoAVIvcgG/U0yUfyem0SNpAFD0XHhcyuh4B"
			"AXAcQ7cv99RVd+6qWsjHx8dnZ2e/pse8vKW2/HnVS7RfjeoFAkF6enq3C+3NVc/L61rlvY7EWuvK"
			"RfUS0nWygfamyucVL6R9E7Zv376jR492vyJvrSsvrWqW9c0OEZWyexXZe2Ty8vK2bNlCSwbdmNBO"
			"R29ERUU9ePCg32Y0xt69e0+cONH9isqpouJaUWVjb7GKiYlJSkqi55xkUVVVdCMrKysyMpKefZqS"
			"iQgEgtu3b/dx0F+VU6dO7dmzp/sV8gLvQ/XTijhp1WlmT6FLTU3Nrl27CBfVf3zE1je10vyrC22e"
			"xQgenRsRPb4V1VMysgFto2EjjPqvjK3Ht7LutxWVsnsVSTsydKAbE406fbtROVWOgallr28S+wTJ"
			"oqqq0JgDht4hL/A+VD+tiJNWnWb2lH7BfBOYgYGBYZDCHAAMDAwMgxTmAGBgYGAYpDAHAAMDA8Mg"
			"hTkAGBgYGAYpKj8FhKJoSUkJuYu8ZpHJZCKRKDMzc0C9qMHLly9lMtlbKOw1VFZWYhj2dmpGUbSs"
			"rOxNq3iFWCzGcVyDsZJIJLW1tQMXfJFIJJfLBzS5MpmsoqLi7awfjdPU1NTS0jJIJtuBWCwm7+cs"
			"hYL6q5VxcXEvXrxgswf2Z0RQFGWxWByORn5OWpN0REpL66/0NUoMw3Acfzs1oyiKIMhAlxN9NJ7f"
			"ga7kPyG5b+1iHAj+igu8nygUChaLFRER0f2iymSbm5v7+Pg4OjoOqKbo6GgvL6/JkycPqBc12L9/"
			"P5fLnT9//psW0gfy8/PPnDlDSPBbwtatW/39/V1dXd+0ECVCoZDH44WEhGjKYGxsrL29/axZszRl"
			"kMCjR48uXLgwoMmNjo729vb29PQcOBdvD6dPn25sbFy6dOmbFvLnUVtbGx8fT7jI/A2AgYGBYZDC"
			"HAAMDAwMgxTmAGBgYGAYpDAHAAMDA8MghTkAGBgYGAYptD/yhbeJMlMTUxrf+/Z/JukA/uJh/L6T"
			"tys5Yz5Y+OVsO838kiHelH32wMmbJRKDMd7BCz9xMiw8uX7nzSYAFsfAcsKcRfM8TMsTBAlGK8N9"
			"FFnxe47frmZb+4Yt+9BeDwAAr760LfJcKQYKBbBYAJxxwStH//dYU+D6eQ5cvCl970+PnL9bOmWI"
			"Gkde6624HTX+P8wdwwG8/nLMvvbg7wKt2ICJLgjiGj+ODHXQAgA07/j6XenNAGyu6bszQhe4Fu/u"
			"KSdEsMLpKUk2cdzCmYY3CfMI2Ro6JLV7YIxpdbIv7YjVNP3KW4f3J2TVs0f6hC4JcuAhbU8vHzmW"
			"nNfAtvL4bFHweD4CeEtZRtqltEaXbxa6c1WlwxgBwsghhScjr4+JWDKpb8015QWEvLqKDgkK3CPm"
			"OWih2Yc33hjzw0ovQ/nT3wUXzVb6iaLjaj6IXDWtY87yJ6c2/FLxfuTXvsZtFJK7LLN1zRxmh4X5"
			"jqL38/rEqfKeK2NnJHmWeuTopbxGro13cNgnTjSrhzTDidILSoNAsog3kYu5zw66lgalZMr8MnRB"
			"scBnG+RRb0Y9SwuvT4vdVR+4oaMtF5p3fHOa1eoZldt/6X6r6b1ezSuXtDKvLI7BMAe/kPkf2Op3"
			"ZRoAQNdtzvSGhKSeu0pfWsCToVfQaO6+hbM/Wyk4kJhdhwHg9Rd/XLM728DNGW4J1sZlSPsh4BWt"
			"N2Kj0i3DomKiwmzuRf+U3CAXlxfzPtopFMZFL3Mp/OXHs1VYW2VBcR0qyzr4862Ry7cLQg3ObztR"
			"1NEiGbHw/363ULgzaGjDyAW/CoVxX0219/bSTdx5olDWcn//nvujpk1UZ/cHAKzhab6oRQEAoJDW"
			"FBVWShQAIC9JPp/5JO1ChgQAABQvnxfxPtopFMZGzEZPbz5S70uQ461DJZs47lC+CXEebpk9A0NP"
			"tKIjVpjo9x8PNLz/3Y6t8wwSo4/mtzekCjYm6get3yFY9rfcnyPPVmJo/slo4c2C3D9y67o10CKn"
			"gzQSFZfnPetzf0rAiXmtM9OuvpFRgQH65GbanZSrD6WANz28VYTwtcXlhVnn41OUPc+lmQln0vOK"
			"alFqya8sx671rNgbm0jqq00NaarK2Mmbrm/fmMAJWLt1Y+joh9s2xJfRbHhGmmFVS2VBcR2K42SL"
			"lMXcdwfKpUHhAFQEi6EL8gJvIZaEitJCjN8xrU68XIACAMhyk5Maze31Wwi30jHfIUTpZW/MuiD+"
			"tS0bz4qwLr9CoVAYu9gvkLir9Gf3B7oHANti+ppDCRFTlP1F0YKHua32sxbNX/DFB6Nq790p6p8G"
			"JWhLq1zH0FBPx9A2YNX3c+1efUWDbTDaZ6JldZlyG8Bqc4sM3KeO5Orb+7iij/NaVZrkeS790vLy"
			"z9HRv+Z4hi8Y168u8CS5Bcl3+WGrXYpT7oh7/Ie2qZPjSEk9afPpTbaqcaTA9Ekmi+sUsmahh5kO"
			"z2b0MA6Gi++kFLsu/IfTUB0DG//Fi8Ybtim0xs2LEoT72+r36B5LSgfFyD4pIdOZ11q+81hJXm6L"
			"XHQv38zPtep+PirLy6+zcx7DAZa+m5viysUSOQCI7yTljZpkywaglvwKbZ6JERcUNM8mVZXXeudS"
			"gcvC0PEWPJOxgUtmyNKu0G15SZxhFa7KYgntYn69A0y15N6CxUBGrnIzIpQWe4TftCF3rxagALLs"
			"Kw8s/HzNERW30jKvHGniEBQ+S3o+8Sm9FwTqQ+8REMK3deBLy7sGDR9uzrp68z+ppjo5dbisnaJ7"
			"pRoM+WDFlwXRq0MSrN19g4KDJvEgA/Cah4n/kUJbxd2kWo/V9lpwFwBAKpHqmOixAEBXV7tdKsHB"
			"SMVBhgz1WRx46rPDIzadddbse9/2rEv3h7+/wMO85NCxGw3TAkwAFPJnqbt3PpbXP8lpmr12IvHp"
			"g0KV7F7GkQLTJ50If5wnHwBvvncgAQ1YN1Z8pYlnatoRLo6N73wbVQOJXvUrbhJHyjL6JKUbhLzq"
			"2TXbVt/Nq27N5nqunpq/PaOoGCsZ7uSgA0UKzHDyTN1zibkLvrK6drFi4oxJWQmvMayQ5xxdvfyc"
			"RFQsdt/0pRm91zfkyssHAMBe1DQYmll0fHmZzbcY2pxZi4ENvS8z95yhHScDAABrIFuUGtMt5tc5"
			"UC4NKge0JTO8woi8GakoLcTc19ds9ZV81E52NWuE3xd8BEoIt8K1Xs2TU44YW5hKimox6Or+iPCn"
			"hm/qXwt4Mmo9E2GPDl779XStB79fyn+Jsfjm5prRoj1q5hrhycObPx1dKgyPSnuBAwALQTgcronD"
			"vG2/rZ7Q2YAMYbMxOQYACgzDEeR1EWkvSExrfmd4UeLlvnVSfz0saP3j4o2Wttxj+2/UyrOTr9Z2"
			"aOW/6z11ysj2l9aBHzmQnuWyVMnuZRwpMH3XK31yevMBafD3QdYcHW2OTNaufO0ul4hbZaoGEbw2"
			"atEeSQNCXnUcHfhFf5y7j7u6W7q5Gj6+dvOxjoOzIQCADOV6z7Z9mPjfZympEu9Z1gr56954sDiO"
			"obG7hYfj9wWWCo+oaHjf21RfKG1xudrtEonSHSqVcbhc+i+iqSqXwqJuH4pZLQd9kMzQCYuiJFSV"
			"FsL38bXMvJKZcSVntJ+XMQDpVhrmKd4ltItb2Pr6CLC0nBb8IhQKhXuiNL37g9qfAtL/W8CyNV8H"
			"j5XW4G7+7w/XhBI0a+/SzcmNoGfp9uEXM80Kc0UYAGLmPCswMGDmVJduLfPYppamjaIyOYCsXCQ1"
			"H26ochJo4fHY6+98FRs9X+vfOxMr1T0CdIYaYzVVbQAAaHVVsxF/iDg9udD50yAPN7cJsz+f3pKW"
			"8hwDAIRn5egy8e//O6Pm+Mlc0tsi1bJfP44UmD6ql4uStu7In7ZhzVQ+Aoip47vog7sVGADg1WfX"
			"LN6rYpMkea0cSnMkHYh5RYY4j2OlXmp1crfgGE50hdSL4rEunR2xQW9SwHvFByOTtf38RyD0njtp"
			"8Yz125qbaT0DIleeUqSJs6Pi/q1SFADw+vQ7VWNdbGn/dAxl5SJ8kkU72sWspgP6kgcvpAWun0Nd"
			"EgBUpWXs5Tsqc/dvuXZ+nj2fyCtvpWGetDW1Pf49qWa8z7sDnT11T5T2u78u2XTf1P3T6G/nWGnk"
			"HaaWQ0Dghc3hK66PMmkvK9Obs3GsVuMl6lsNveZOPrszfP1IvLR5xroJqj7pIX/275gUy8VCT+Oh"
			"ipVzU7/5NcXjx1k0nwr0QMcj5LOLm5avuGXDrSmVz/hmCdzYXPZeyCZPN20AwIYXJWxILv67s/Ju"
			"ju3cBXaLD16YEzN3eI/I9CJb1ThSYPqkHa86K9h+9+XY5u1fpwLCc1+8IXSFQ8T65TmjTVrLaod/"
			"scWFusTI6dAbPYI4UpTa+fYU2Faz160PtOqTtu6Tt3a25Vw1dLfmAGIywcX4YLGjnRaA8k2GtkPg"
			"FCS11H+6Kavm9XaU75cV0hZ0xPwI8uM0elMFWToAAGfs5+GTNm5e+s0IkzbRC+uwLV79bYJNYdFQ"
			"QquY1XfQT8mDAeICXzbMoTEwsWdJNF5UXVqGnn52sT9rr5ioPIqJVWht0Zv5rkXYOVTOGRWwIdyN"
			"Cxldj4AAOI6h25d76mp07qpayMfHx2dnZw9ol3qFQiEQCNLT07tdaG+uel5e1yrvdSTWWlcuqpcM"
			"lLB9+/YdPXq0+xV5a115aVWzrH921ZZNIzB5eXlbtmyhZ62p8nnFC6kaXumOJBAVFfXgwYO+jho4"
			"9u7de+LEie5XVAYYFdeKKht7m3FMTExSUhI95ySLNKoiKysrMjKSnn2akokIBILbt2/3cdBflVOn"
			"Tu3Zs6f7FfICp70Z0WGAzdOgpqZm165dhItv20+/avMsRvDo3Ijo8a008/UDmrD1+FbW/baitmza"
			"gaFnzWjYCCO1vNId+ZdDZYA5BqaWBhp1RbKo6WLWvORBAHmBa3TNDbB5dWG+CczAwMAwSGEOAAYG"
			"BoZBCnMAMDAwMAxSmAOAgYGBYZDCHAAMDAwMgxSVnwJCUbSkpITcRV6zyGQykUiUmZk5oF7U4OXL"
			"lzKZ7C0U9hoqKysxDHs7NaMoWlZW9qZVvEIsFuM4rsFYSSSS2tragQu+SCSSy+UDmlyZTFZRUfF2"
			"1o/GaWpqamlpGSST7UAsFpP3c5ZCQf3Vyri4uBcvXrDZA/szIiiKslgsDudt+zQqdERKS+uv9DVK"
			"DMNwHH87NaMoiiDIQJcTfTSe34Gu5D8huW/tYhwI/ooLvJ8oFAoWixUREdH9ospkm5ub+/j4ODo6"
			"Dqim6OhoLy+vyZMnD6gXNdi/fz+Xy50/f/6bFtIH8vPzz5w5Q0jwW8LWrVv9/f1dXV3ftBAlQqGQ"
			"x+OFhIRoymBsbKy9vf2sWbM0ZZDAo0ePLly4MKDJjY6O9vb29vT0HDgXbw+nT59ubGxcunTpmxby"
			"51FbWxsfH0+4yPwNgIGBgWGQwhwADAwMDIMU5gBgYGBgGKQwBwADAwPDIIU5ABgYGBgGKTQ/8oVW"
			"3T5x8D/3q3DT9z5etMDbkt2cfVZ47Hq5YrjnP/432M1EI+cI3pR99sDJmyUSgzHewQs/cTIsPLl+"
			"580mABbHwHLCnEXzPEzLEwQJRivDfRRZ8XuO365mW/uGLfvQXvlDimje8fW70psB2FzTd2eELvxA"
			"99o2YVPg+nkOXLwpfe9Pj5y/WzpFnb7wrbfidtT4/zB3DAfw+ssx+9qDvwu0YgMmuiCIa/w4MtRB"
			"i+x9gWvx7shzpRgoFMBiAXDGhQi+msIqy0i7lNbo8s1C944OlXj1pW09bwteOfq/xzSgGivtiNU0"
			"/cpbh/cnZNWzR/qELgly4CFtTy8fOZac18C28vhsUfB4PoI3UYVTRTpYHINhDn4h8z+wVf7SfM8w"
			"0EBeQMirq+iQoMA9Yp6DFpp9eOONMT+s9DKUP/1dcNFspZ8oOq7mg8hV04wRAAD5k1Mbfql4P/Lr"
			"qe23e06q28T7qoc8Vd5zZeyMJM9Sjxy9lNfItfEODvvEiWYeSDOcKL2gNAgki9TR76ODrqVBKRlv"
			"IdYdDYhlghecjLw+JmLJpK7W2hSeCIOGFHaOwWuv7dqV77Zq6RS+MoYtTxIPHUsraOIMGx/0xTzP"
			"YaBqTiuGJW08rpi34fNxOgCy+/u3PvbYPH+cGlFTCcUCn22QR70ZsXXNHGaHhfmO0gEAwOvTYnfV"
			"B27o6NWF5h3fnGa1ekbl9l+632p6r1fzHeX9Kq8AALrjF2+d8iSKMPX18+nXNQ1oFTT27OSGdcdK"
			"zT0m6OX8a8P2pHrxnV/X7czQdnEzyBH+EHu1WSNSWm/ERqVbhkXFRIXZ3Iv+KblBLi4v5n20UyiM"
			"i17mUvjLj2ersLbKguI6VJZ18OdbI5dvF4QanN92oqizb7Li5fMi3kc7hcLYiNno6c2HHpt7e+km"
			"7jxRKGu5v3/P/VHTJqqz+wMA1vA0X9SiAABQSGuKCislCgCQlySfz3ySdiFDQuX9SL3v97uFwp1B"
			"QxtGLvhVKIz7ypubfzJaeLMg94/cuq4GQIiFP+G2qfaaUa3oiBUm+v3HAw3vf7dj6zyDxOij+e0N"
			"qYKNifpB63cIlv0t9+fIs5UYdThVpWNvzLog/rUtG88quxgRwkADnJjXOjPt6hsZFRigT26m3Um5"
			"+lAKeNPDW0UIX1tcXph1Pj5F2fNcmplwJj2vqFZa0XNS3b/d0nc95KkqYydvur59YwInYO3WjaGj"
			"H27bEF9Gs6ccaYZVLZUFxXUojpMtqijmvjpQLg0KB4BS1F2vDshlgovL85696l1I4Yk8CFWOET/8"
			"LeJf4umfenbu/nh9imBzmnHwP2N/WjmhdNfG40/lKuckFz/PSdu3/ViBDADwxpK85y9BraiphLzA"
			"W4gl0aUudq1nxd7YxPqOQCDG75hWJ14uQAEAZLnf9W4JAAAgAElEQVTJSY3m9vothFvpmCfkVSgU"
			"CmMXuXHIU6fXEY82tPYWhcLKa+Hatas+D57pbIS2tLQ8vZ/VOGrGwv9Z8OVsO3HW/ScakYK2tMp1"
			"DA31dAxtA1Z9P9fu1TnHNhjtM9Gyuky5DWC1uUUG7lNHcvXtfVzRx3mtREvapk6OIyX19TjPc+mX"
			"lpd/jo7+NcczfME4beKN/ZJbkHyXH7bapTjljpjaO3GE1rh5UYJwf1v9Xrq0alQ1i+sUsmahh5kO"
			"z2b0MA6Gi++kFLsu/IfTUB0DG//Fi8Ybtsmpw6kyHdomDkHhs6TnE5/KXxcGGnTmtZbvPFaSl9si"
			"F93LN/Nzrbqfj8ry8uvsnMdwgKXv5qa4crFEDgDiO0l5oybZsoGl03NS3Wyqo0fVVFvvXCpwWRg6"
			"3oJnMjZwyQxZ2pXnfewq2lW5uCqLJb0VM00HmGrJdOuuO+QyIW48FJ6aqAehJf+3cfsTv01rp5l2"
			"bTctd1OLnD//1MFYW9/ab8X6UJduba6IcwJg6Yz3s7kWc7Ko86TvdQvoL3KVm5E2z8SIC4rOmmOP"
			"8Js25O7VAhRAln3lgYWfrzmi4lZa5omQpq5haD0C4ox5f8EYgNac3w5ewT2/9bVoP9QOWto6LOBy"
			"uSCVtGlEypAPVnxZEL06JMHa3TcoOGgSDzIAr3mY+B8ptFXcTar1WG2vBXcBAKQSqY6JHgsAdHW1"
			"26USHIw6gq6QP0vdvfOxvP5JTtPstRN1ANHxWRx46rPDIzaddab93pcW7VmX7g9/f4GHecmhYzca"
			"pgWYUHhXF2So5lQj/HGefAC8+d6BBDRg3VjxlSaeqXIhcmx859uAvPhKz3ACGAF1OrqMGluYSopq"
			"MbDDiGGg926FkFc9u2bb6rt51a3ZXM/VU/O3ZxQVYyXDnRx0oEiBGU6eqXsuMXfBV1bXLlZMnDEp"
			"KwEQ/jhPs1eT+tur9UNKCx095KnmAwBgL2oaDM0sOr68zOZbDG3OrMXAht6XmXvO0I6TAQCANZAt"
			"So1VFHPfQtixNKgc0JbcDexFHbFMQJbR4w6yp+p6IA6SZYD88aF1/21/d32ETbdXMlhzw0uemSkb"
			"0LqinLJmXN/IXOWcAABAz/XLT+HrHf/23mEOAKBQuQVoCCNy9SvkOUdXLz8nERWL3Td92dVaFjH3"
			"9TVbfSUftZNdzRrh9wUfgRLCrXCtV/Nd4rsaQCL8qeGbgshT1zC0o9ZeHB+x9hT68eaIWRYIG0FA"
			"ocAAcBwDNltD3x3XHjVzjfDk4c2fji4VhkelvcABgIUgHA7XxGHett9WT+hsb4qw2ZgcAwAFhuEI"
			"8so9C+G/6z11ysj2l9aBHznoAUB7QWJa8zvDixIvk/suqw8LWv+4eKOlLffY/hu18uzkq7U4pXd1"
			"0bRq6ZPTmw9Ig78PsuboaHNksnblyzm5RNwqY6kIJ1U6ugSKW9j6+ghlGGhByKuOowO/6I9z93FX"
			"d0s3V8PH124+1nFwNgQAkKFc79m2DxP/+ywlVeI9y1ohVxAn9Wp/U1MPcapKjVyudrtEonSHSmUc"
			"Lpf+i2iqyqWwqKuymDXkoA+Su9kmlwnxDgpPXIpBCuBOWP3LPPGB6PPdapmlpc2RSiUKkFXl3b2d"
			"tCdi1w2xqjkpRwyZsmzxsKSYMxUdS03dqNGcP0VJsDiOobG7hYfj9wWWCo/kdL0iR/g+vpaZVzIz"
			"ruSM9vMyBiDdSsN8V6GytJwW/CIUCoV7ojr+rkCcuoahdwCg5Yn/XP1rqds3m0Lt4WUrbmk9HKl+"
			"Wvyy9UlRBQwboXYr8B4+svYu3ZzcCHqWbh9+MdOsMFeEASBmzrMCAwNmTnXp1jKPbWpp2igqkwPI"
			"ykVS8+GGryaB8KwcXSb+/X9n1Bw/mdsOaOHx2OvvfBUbPV/r3zsTK9XdTHWGGmM1VW0AAGh1VbMR"
			"f4g4PbnQ+dMgDze3CbM/n96SlvIcI3tXOxKaUd2JXJS0dUf+tA1rpvIRQEwd30Uf3K3AAACvPrtm"
			"8d4cnBjODhFU6VDS9vj3pJrxPu9qNVOGgQbEvCJDnMexUi+1OrlbcAwnukLqRfFYl+EdG7sC9CYF"
			"vFd8MDJZ289/BKIgT6oL9fSQp6oUaeLsqLh/qxQFALw+/U7VWBdb2n9/o6xchE+yaKe6mDXjgL7k"
			"7sbJZUJ4BEE1lWEUg1gcm/cmuQav/0L7VNSxJ7Ku0S5OnD9uFEr1nT5etmyOi/JvoJRz6vI41Gf5"
			"F8bnj2e0K163BagDaYHr51CXBACAFs9Yv625udtmbOzlOypz92+5dn6ehj3sKm+lYf71ddp96hqG"
			"1tEpe3R6/9UqKV61OTgZQMsjIilq4adJ36/39wOdUZ9sCRypCSVaDgGBFzaHr7g+yqS9rExvzsax"
			"Wo2XqG819Jo7+ezO8PUj8dLmGesmkJ61cGznLrBbfPDcBLerKZaLhZ7GQxUr56Z+82uKx4+zzNQo"
			"FR2PkM8ublq+4pYNt6ZUPuObJXBjc9l7IZs83bQBABtelLAhufjvzj29X5gTM3e4Gr99Jn/27xiN"
			"qO4Arzor2H735djm7V+nAsJzX7whdIVDxPrlOaNNWstqh3+xxUXLUEIRTqp0XOx4e6qQc0YFbAh3"
			"065PuEwKw+dL7NV5OcaxdrblXDV0t+YAYjLBxfhgsaOdFoByw9B2CJyCpJb6Tzdl1VBO6p8hf9MC"
			"vP6aWnrIUwVZOgAAZ+zn4ZM2bl76zQiTNtEL67AtXvq9mOp1mmSLlNHXpAO17Gg5ksoEMrseTwDb"
			"ava670ietFikQaI0AABALGZ8F569fOtexz2r3AwAgPPu/G99N21bvnrECL2XZVWjAmYZQGNvohD+"
			"9K8Wpd3fBb1vAX2DuMCXDXNoDEzsWRJd1S9tQUfMj+jxhNfQ088u9mftFROVx5aCcKu1RW/mu07p"
			"VzEGjmPo1onkqWsYVS3k4+Pjs7OzX9NjHmurLy+va8X61aheIBCkp6d3u9DeXPW8vK5V3utIrLWu"
			"XFQv6Zf317Bv376jR492vyJvrSsvrWqWDZTH/pOXl7dlyxZat7Y3VT6veCF9dUFFOGmnozeioqIe"
			"PHjQbzMaY+/evSdOnOh+ReVUUXGtqLJRSrrek5iYmKSkJHrOSRZpFHNWVlZkZCQ9+zQlExEIBLdv"
			"3+5+hVQmdDz1PqgLeVu9qLyqub2PQpX0aws4derUnj17eoghLXCNVf+fYJ4GNTU1u3btIlxU/+EZ"
			"omtipZFnPz3Q5lmM4NFyr8eneJs4gLD1+FbWf6bDgUTbaNgIo+4XVISTdjr++qicKsfA1NJAo65I"
			"FjVdzJqSTCoTOp56H9QFW9fEUv09RMNRIy9wjVb/AJtXF+abwAwMDAyDFOYAYGBgYBikMAcAAwMD"
			"wyCFOQAYGBgYBinMAcDAwMAwSFH5KSAURUtKSshd5DWLTCYTiUSZmZkD6kUNXr58KZPJ3kJhr6Gy"
			"shLDsLdTM4qiZWVlb1rFK8RiMY7jGoyVRCKpra0duOCLRCK5XD6gyZXJZBUVFW9n/WicpqamlpaW"
			"QTLZDsRiMXk/ZykU1F8ui4uLe/HiBZutxleZ+gCKoiwWi8PR9Fe5+01HpLS0NPnLqwMNhmE4jr+d"
			"mlEURRBkoMuJPhrP70BX8p+Q3Ld2MQ4Ef8UF3k8UCgWLxYqIiOh+UWWyzc3NfXx8HB0dB1RTdHS0"
			"l5fX5MmTB9SLGuzfv5/L5c6fP/9NC+kD+fn5Z86cIST4LWHr1q3+/v6urq5vWogSoVDI4/FCQkI0"
			"ZTA2Ntbe3n7WrFmaMkjg0aNHFy5cGNDkRkdHe3t7e3p6DpyLt4fTp083NjYuXbr0TQv586itrY2P"
			"jydcZP4GwMDAwDBIYQ4ABgYGhkEKcwAwMDAwDFKYA4CBgYFhkMIcAAwMDAyDFJof+UKrbp84+J/7"
			"Vbjpex8vWuBtqQV4mygzNTGl8b1v/2dSP3+NuxO8KfvsgZM3SyQGY7yDF37iZFh4cv3Om00ALI6B"
			"5YQ5i+Z5mJYnCBKMVob7KLLi9xy/Xc229g1b9qH9q58EbHt6+cix5LwGtpXHZ4uCx/OBYNJYrQOv"
			"9Vbcjhr/H+aO4QBefzlmX3vwd4FWbMBEFwRxjR9HhjpoAQCad3z9rvRmADbX9N0ZoQtci3dHnivF"
			"QKEAFguAMy5E8NUUVllG2qW0RpdvFrp39nokjFs4a3Rj4jZhU+D6eQ5cvCl970+PnL9bOqXvjeGx"
			"0o5YTdOvvHV4f0JWPXukT+iSIAceQgwSgjdRhZOYDmOEGN4hhScjr4+JWDKpb22L5QWEvLqKDgkK"
			"3CPmOWih2Yc33hjzw0ovQ/nT3wUXzVb6iaLjaj6IXDWtI3PyJ6c2/FLxfuTXU9tv95xUT8tsXTOH"
			"2WFhvqPolSZxqrznytgZSZ6lHjl6Ka+Ra+MdHPaJE800kGY4UXpBaRBIFqmj30cHXUuDUjLeQq67"
			"XiGWCV5AzDaFJ5UVgtde27Ur323V0imdHXxaniQeOpZW0MQZNj7oi3mew4A0Jws2yYWEsBSl7zvn"
			"H7jQc5mtcHrax4BSLPDZBnnUmxFFafUU6SC/GrurPnBDR0MvNO/45jSrb1dN1e4ZGd3bRJdfj77+"
			"zx4bwTv6PZLN4hgMc/ALmf+BrX5X+gEAdN3mTG9ISCJsNd6G5GlSQaugsWcnN6w7VmruMUEv518b"
			"tifVt+fuWzj7s5WCA4nZdRrrtNh6IzYq3TIsKiYqzOZe9E/JDXJxeTHvo51CYVz0MpfCX348W4W1"
			"VRYU16GyrIM/3xq5fLsg1OD8thNFcqUBvCFVsDFRP2j9DsGyv+X+HHm28iXBpJrKsIan+aIWBQCA"
			"QlpTVFgpUQCAvCT5fOaTtAsZEgAAULx8XsT7aKdQGBsxGz29+Ui97/e7hcKdQUMbRi74VSiM+8qb"
			"m38yWnizIPeP3O5BI4w7lItbeXvpJu48UShrub9/z/1R0yb2ffcHAEVHrDDR7z8eaHj/ux1b5xkk"
			"Rh/NbycFCaMOJzkdpJGouDzv2Ys+96nDiXmtM9OuvpFRgQH65GbanZSrD6WANz28VYTwtcXlhVnn"
			"41OU/cGlmQln0vOKaqUVPSeFEi3HrvWs2BubWE9PG2mqytjJm65v35jACVi7dWPo6IfbNsSX0Sx2"
			"0gyrWioLiutQHCdbVFHMfXWgXBoUDgClrLteHJDLBO+ZbQpP5EGdFSJ++FvEv8TTP/Xs3P3x+hTB"
			"5jTj4H/G/rRyQumujcefyslzoog/cSlWGXxAXGY6fQ8oeYG3EEtCZWmR4lDOe8e0OvFyAQoAIMtN"
			"Tmo0tzcgRUZGcokTNwJCVe+NWRfEv7Zl41kR1iVGKBQKYxf7BRJjQHP3B5oHgEJh5bVw7dpVnwfP"
			"dDZCW1raEYvpaw4lRExRq9+oKtCWVrmOoaGejqFtwKrv59q9+ooG22C0z0TL6jLlNoDV5hYZuE8d"
			"ydW393FFH+e1Km8T30kpdl34D6ehOgY2/osXjTdsI5qElwnfzF2y7tuVi4I/XXG4H10bAQAtSL7L"
			"D1vtUpxyR9zjP7RNnRxHSurJm4/WuHlRgnB/W33qqL0ax/Nc+qXl5Z+jo3/N8QxfMK5vr68JsLhO"
			"IWsWepjp8GxGD+NgODlIcupwktJBHtnf/nSdea3lO4+V5OW2yEX38s38XKvu56OyvPw6O+cxHGDp"
			"u7kprlwskQOA+E5S3qhJtmxg6fScFMmyNs/EiAsKmmeTqsprvXOpwGVh6HgLnsnYwCUzZGlX6La8"
			"JM6wCldlsURFMffVAaZaci91R0nvyabw1EQ9CC35v43bn/htWjvNtGu7abmbWuT8+acOxtr61n4r"
			"1oe66FLM6aU68Ve1O/QNucrNiFha5DhUDPebNuTu1QIUQJZ95YGFn695a1/WjqoNRNvEISh8lvR8"
			"4lN6rxJoQesREGfM+wvGALTm/HbwCu75re8wLT7iwP9/7J15QBNX9/dPNghLQCQgChRR6lYQFEVF"
			"waWAdaHto9a6wlOrT12raFv1AS0IFFosWFGJ26N1wUrVUgVFwbXUpbgg+1YFQcK+hZBlksn7BxFh"
			"ZkImIVR/L/P5z3HuOeeee+69k2FmvuJy3YUBANBv5vr/FEQGLEm0m+g1b9G8CSaQAWj1k6TfxND2"
			"8n5yzaSA4Sy4DwAgFon1zQ1pAGBgoCcRi1AwpQPIG2qbTCyUNca091puDyDtahLEOXLUek7wDnfJ"
			"uYCNN4uXOjpq+yagJPPKw0Hv+08a8PzYydv10+eaAyhkz1L378mX1RVmN835xo30jTGCdv2nrvI9"
			"8+lx2+ALzqR/shND545y5wKgzX8dSUTmbh8huI5Nkqzketd0ApgCfjiMXt7BtpRmaBsVZlwNhzU7"
			"VN3PrRJmsd0DpuVFZRSXyJ8PGu2oD8UKOWfyBwa/J+X4f2lz8/JLN58JmYlA545yt3zdqfdeD6JC"
			"ln0iYN3voooSwcTg/5BU0sRXXh4AgLyhup5jadX+8jKDa9W/+XGNHOzJvczctYfDmBkAAPJ6vEWx"
			"GVExa5zC9qlB5IB0yJ0gnEtdRpvAU1UdEFSILP/Y9nuSkUGB9p0uZOTN9S0mlhYMQGqLs8uaUSPT"
			"Afg+ObTk4DvTX13oCuLVQUNM8YsRcWkRZVwxwMvLMuB6HjJMeiPT1vtzrgKfzpZEgtDVLiB0MysL"
			"UXGN/LVyJJ07bVPwK/l4LSCdGklJQuA3Z5CPQwJnW/XSX471Bn/wNS/+eMjCIaW8TWFpDSgA0Oh0"
			"JpNt7rjsh4MB41/Jm9IZDLlMDgAKuRyl05Wdp+nrMaXSV7LJMpFAKMWaBACaMZfLBrqRsQEq1fo7"
			"RzQQPrh8u7Ut5+Th2zWyrJQbNe2xckd6TpvyjqTFzvcjR/JaRQTtJAVJac3vDipOuqZGLpoc4sJf"
			"Qo6IF22bZ8fEJ4lGnE5s7hpZ+PRqD2Zc9Z0cucUPfn+Ijp1o7TqWk3/zTr6+ozMHAECKsD3nODxJ"
			"uvfsaqrIc7adQqbAdqrT+kZjOvnF7OcdTzjkW8r7Gatkrgps5Sltsdl6EpFI6Q4RS5lsDX7yElUu"
			"gUUDFdnXmQOtfqUTzSXMGQSe2ASNFMAeH/DTMsGRyIudSpnG0mOKxSIFSPm59/9MPhAYe1uA65Mx"
			"mc7gD2id0C5W8SVBXFqEGadzp3pZP77+OON69hBvDzMS6QSgkVpAJIJWhpERHWis0f4/8Xg83oGw"
			"Hqz+QHYDQMqTvg3YW+q6JdhvOLT0aOar9pEZtyYkpREMrV0//PwDy6KcCjkA3dJ5tq/v3A+muXQS"
			"f2NYWFs0VpTJAKTlFeIBgzjtnaBbOI1EHt1/KQcAtOrC16vinjzEmNQyNP3+ZvJqfhsAAFLFbzbl"
			"9hOkpxQ5L5w3ydV1/JylM1rTrr6QAwDdxMbJxe2TL3yqT8Vrcn8J2w4pOhVz690vYyKXs87uSars"
			"4RYgq0gO/zFv+o6vp3HpBEnKRrHpBACC4ajsj2up/YcCseNK7+c8ipZ6RTh6ohWT4zYWUi8LRrgM"
			"al/YFWA4Ye6YkqOhKXres2zpCnynCGCZmBm1NTeTugeErzxlkObOToqHf5QiAIDWpd/lj3BxIP2D"
			"kbBy6VycxWHExaw7B+RD7mwcXyaYwSbqykCCRjSm/ZgJYxcFfa53JuxkobSjtcto5oPbRWKj0R+v"
			"XTvfRflwRtc+EXXGGDcVsflSsTp0C26CG2UTlwQAtrRUZNzMw2vw4/0Hc4Z5u3MI00kn6oi6BaQt"
			"/1xy9bipI3X4ASNSm4f06S+Hb/DFKD9kUQoAa1Lgldh/9dNdDO2wHOf6XgrZtP7WYHNJWZnh/J0j"
			"WI1XiE/leCyYfGHPpqB30NJmn+3jX/1UYjn5rXcMDFqXPcRcWFYz6PNd41xk9Vc6mwTQag/Qn7Tk"
			"08vB69b/Yc+uLpX5bFkNt0PKxiwJdnfVAwD5oOLEHSklnzgrz2Y6LPAfturopfnRCwZp8tv7VbuP"
			"14ujr1qv4rmb9VdsWJC6Ze/VSd/NJnkzAw/KvxARdb9lRHPU5lSgm0xctQOTJBcWR0SQTvxwGA6x"
			"xbasSH31SxQYNnO2B/lqLfDKtHN2YN7gTLRjAt18vIvZ0RKnYSwA5YKh5+g7hZ5aOmuGBa2asFPf"
			"LlHeBlL+NFaIWxHb5YHkbsPhuwrSdAAA5oilmybsDFmzxda8raLBbsUuDyN1ttR1E2+RMPu6dKCV"
			"HdxccmHBY0WX0d6K88Si4RpVpAEAAN3KZ+umrHXhcU4HNroaAwBz5PKvvIJ/WBdga2vYUsYfPHe2"
			"MTSS6Iw+2nUqrsXv/ypWh+7ATvC1Ax0bfZO6lkTjZeLSUpFxjrv3sJjdeuvdDInTacw1xXYk+5VF"
			"3ALyqqplzMFzd2xyZUPG66EAppNf1Dp3A2yfyKJKQj4hISErK6tXVeoVCkVERER6enqnA5Jm/ovy"
			"WqFMbUu5sLa8ok6E/w9JU+WLlw1iLUx25tChQydOnOh8RCasLS/lN0s1MvOPkpubu2vXLlKn4pKk"
			"Ip343OFakiMsLOzRo0eatuo94uLiTp8+3fmIyjJBBDUVlY3qehwdHZ2cnEzOOc6iymJ+TWZmZmho"
			"KDn7JEPGEhER8eeff3Y+on6wCTxpUCGytrqKcn6zpNuT8C7UT0X1CT1z5syBAwe6BIOzqsHKQSrj"
			"uMz8s2tKdXV1bGws5uDb9ulXPRMrWxMyJ9INuTaEd8n0TAfammplsnsYhlwbu56beTvAJUlFOvG5"
			"w7X8/wWVZcI0trA21qkrnEWVxawrB1qifrAJPGlQIQwDc2u1PxrxLtRPRW0SireqwcpBKuO4zLwF"
			"awr1JjAFBQVFH4XaACgoKCj6KNQGQEFBQdFHoTYACgoKij4KtQFQUFBQ9FFUisIfPHiwubnZwEDr"
			"50tJUV9fr6+vb2ys26csdEBTUxONRjM1/b/0wItEImltbTU3N3/TgRBQX19vaGjY2+VEnqamJjqd"
			"bmKig8fD2mlsbGSxWL1Xyf/A4DY0NLDZbENDXT6P9NbS0tKComi/fjp/n+ntBUEQqVS6bdu2zgdV"
			"Pgbav39/d3d3ShT+TQeiAe2i8Bs2bHjTgRAQHh7u5eX1VonCm5qaLl68WFcG/xlR+F4d3PbJSInC"
			"//8KJQpPQUFBQfEaagOgoKCg6KNQGwAFBQVFH4XaACgoKCj6KKQ1gdNPH018rQkMmH/r5PukGmoC"
			"n75Xw7Cd5r/m45FGKgyY0UFYnHL81NX8BqZ6jVLV9KomMAAmyLGyqz9gWob79UvVVNq4QxPYsOL2"
			"8SMXsxoZth7t8rmYnHDpxJqxKoajkzSp0lGXNJBAJ5rA0vunjv6WWcca7LPii9kOnR8u0jiet0ET"
			"GF/MmjnoBU1gbJmQ0gQmP99QQX7ysdPXC7vRBFb2af3A5J2nFMt2LB2lDyB9eDg8f1LIcket+qSq"
			"q1prAqN1aTgB4ACfyqifOp9q8Zda82ZdRK17R/6XEFIFLfv7dOD2k6UD3Mcb5R7bEZVcXdz133U9"
			"COA1XZVZI6/UdasJnG6/7ofv/m2SHHWqSKbCAKD11yJCUkwX7FSvUdotmmkC7wn6UHaWvCYwLsgq"
			"i1mYlq6PMZkh85n7Dk3gXyP+1zRze3S4Hyc58kSeBJcTObFmrKrhiIvePo97KyToXHn7yZg0kKDn"
			"msCSqsSIAy+nbdsdtohx9vuzpZ0VEzSPB99VQk3g74MSSnWrCfx9UEKpXEUxa+qAUBO4PWTtNIFx"
			"ZdKtJvD3QQmlcg3mG1p37buQ6/271wRW9kkmeJGddijqZIEUANDG57kvWrTqk2rUaQJHXqnDagLX"
			"tieCboYXADZqxZxKxnyXce00x3Qo/0sIuSsaut2MVdu3blyyaKaTCSJsldAw/+5ZDEoQYWcZzk+G"
			"v1aQI9IEnuBhq2/o4DFGVpgnVGEABHevPRvj/6mTmXqNUi3CVaUJzOK+52hLXhNYRZDkMqMeGttl"
			"6df+blyWsZ29FRNFCdwRx6XSqZ6547yNc6RJySWy7tJAAq01genmM7/dt9ndnGk40MpUKhJ3ypg2"
			"8ajqale515lI2g3dagLPRNJuPFdRzJo6INQEbg9ZO01gdVVJ4KmJ/HxrvZf29xh1msAdyabpj/O2"
			"vxkdX9yhSqNNnzRBpnLKKTWBX6WDYYsTAKarOJWUeWXLLnOsNyGnCWw/fZk9QOtT3pHrCvevvGwc"
			"rJY5dPr3QJ2E0s9nw+qiHwIWn7dx81mwdP5EE8gAlP8o8ZyQJqrMuFwzaXP3msA4A/KXd5o4hBql"
			"96WjArtolGqOrjSB5Q212CDVZ0aDfeuVJnDT/YMXkLk7RghS1bpT4RRea8K2S5PWymG4HJsGcqFh"
			"xlVTTWBgccwZJb+F7bv4iD94/f5Omni4YSETD76rOtAE7tLD3tAEJpoautQExpYJKU1gkvNN3txA"
			"qAlMON0BAAzH/mchbP7xrOePAzTti1aY4qv/tSZwS2e5aTpWAJgOzzGnwk215vFD3jHHdCb/Swjp"
			"pURcfDZw6y+yj4OVmsDYf+sAPTufzfvjfw5b/G7ZwVeawAymnr6+kcXoZVFqNYFxBgiVOBXAdtu8"
			"d7ngaBeNUg3RoSYwGblQwsxogqggPvgosmTbPFsCTWBVjbpz+kqalCgNpMCMqxaawMwhc7eEhG71"
			"qDybWNxxmaRlPNiuth/tmcAuUeXqVBOYpIM3rwlMMN9oLP12TWCkOv+v+1d4QUpNYMLp3t6i35S1"
			"qwYmR59/qWnxawONoCReawJ/2EVuGisADLhTSZgn6JTO5X8JIakJXHYpeHNs2bhXmsDYf+sikh5r"
			"AuMMEAqb0pj2Lm4uCwNX6HfWKO2e3tQEVq++SpwZ8sgqksJ/zH9/pypNYGJl3+6cdkiTNhOmgUyv"
			"e6YJLH/2W+TBB1IDUxsXF9uW8g7VZO3ioTSB8cZ1qQlMMN86NIENHT9c/UXHH9cJ+9Thsf/UdZ+b"
			"XTyVISH+eE0P6IEmMABWABhwp5Iwj6vTXg+qiooAACAASURBVJD/JYSkJnDCkc6awFsj3jnVRSP4"
			"Xuy/ehxJjzWBcQaANaQbjdJvArI7aZR2S69qAhOpr6rNjHqzr0D5FyJ2PxCMaFGtCUwcFsFwXO4q"
			"TapXl3gNl4alq4drc5WimSYww2bUO0XhAdtSLVufNc3YqKwAtO6mVvFQmsB4dK8J3HW+MUeQ0ATG"
			"QufO+HJl2sNYrXrUHT3RBAbACgDjlantrNSZ75iEvSn/S4gqAcn/q5rAOlKx/ac1gdUHqT4zPdEE"
			"1topSXpBE1jazNdGnbiduLi4+Pj4zkcoTeC3VBO4d9CxJrB6etk8CfqGJnCvqdj2rn6n+iB1JG1M"
			"1l0vONUxLN3GRmkC43krNIH/KXqkCfzGzWsL9SYwBQUFRR+F2gAoKCgo+ijUBkBBQUHRR6E2AAoK"
			"Coo+CrUBUFBQUPRRqA2AgoKCoo+i8jHQtra2jIyM58+f96p7sVicnZ1dX1/fq160oK6ujslkXrx4"
			"8U0HogH19fVyufztjBlBkEePHlVUVKg/9R+hoaGhtbVVh7lqbW0tLi7uveTX1tbKZLJeHVyxWJyV"
			"lVVXp5uP+77lVFRUSKXSt3Oy9BJtbW1isRhzUOUGIBaLm5qaBAKNP/GoKQ0NDRKJbr4nqkOkUimC"
			"IL29/+mW9tF9a2Ouq6tra2t701EoQRBEJpPpMFcoira2tvZe8kUiEfT+4L6dk7E3EIlEKIq+tZOl"
			"N2ivecxBlRtA//793d3dnZycejWmyMhIDw+PyZMn96oXLTh8+DCbzV6+fPmbDkQD8vLyzp8/v3Hj"
			"xjcdCAHh4eEzZ84cO3bsmw5ECY/HMzU1Xbx4sa4MxsTEDB8+fPbs2boyiOHp06eXLl3q1cGNjIz0"
			"9PR0d3fvPRdvD7/88ktjY+OaNWvedCD/HDU1NQkJCZiD1N8AKCgoKPoo1AZAQUFB0UehNgAKCgqK"
			"Pgq1AVBQUFD0UUh+DRThp58+mviQj1qM+Xilv6e1/HnqsePJOfXMga7/Wrl8spVOPiqKNmVdOBJ/"
			"57nIeKjnos/+NZpTFB+0504TAI1pbD1+/splkyzKEyMSTTdsmqrITDhw+l4Nw3aa/5qPRxoBAKBV"
			"V34I/b1UDgoF0GgAzFGLNgy5d7LJN2iZIxttSo/7/qnz1jVT+mmx5Qn/2Pdj9az/LhjKBLTuWvQh"
			"yaKtvjYMkFdcitjX+HGonyMLAJDcU0Gx6c0ADLbFSB8//7El+7uGsyTiyym0soy0K2mNLls+m8h+"
			"ldqu7T77gHMH048l4X79UjsnxoyUkn1pe66mG1bcPn7kYlYjw9bDb/U8RxO6sDjl+Kmr+Q1Mm0mf"
			"rlw0jksHtBUfF244zOiAadmvKD701tDA1RM0E9eUFWDGdWzFsYiCiYHLHFlI1vGdt4f+d4MHR/b3"
			"uYjLlhu8KyL3Vc8M3Ti9vc+ywjM7fnr5fujmadL7p47+llnHGuyz4ovZDgYYywwDS8c5K1Z4DSb3"
			"eX1sV01eKHNnKnqW+vOJK7mNbHvPRSs6hEs07qGb+JLSIOAsok34YtbYQcfUIAyZcHzVgS0TtAA7"
			"2gSeVFYIWnMzNjbPdeOaKUpdeFSQn3zs9PXCJubAcfM+X+Y+EHB9smLgXIgwU1H8vnPekUskpln3"
			"XcVP8DnGucSLUdfSQuvSYmLrfHe0a3UhuadC0mwCfCqjfup8qsVfas0rp3THuAIAGIxbFT6lMOyU"
			"YtmOpaP0AaQPD4fnTwpa7qhLkRhSBS37+3Tg9pOlA9zHG+Ue2xGVXJ1/POjb36rtPSZwco8GRibp"
			"5sFh4e2YsHTrFWHRYSvs/4qMvFInE5SXmHy0h8fbF7nWpein7y7w5W2VBSW1iDTz6O50+3U/fPdv"
			"k+SoU0XtTzbRrWZt28/j7ZnXv/4d/7083r4vpw339DBI2nO6SNr68PCBh4Onu2mz+gOAvP7vvIpW"
			"BQCAQlxdXFQpUgCA7HnKxceFaZcyRAAAoGh5UWzy0R4eb0/Qh7KzIT/XeWHC8WTnxUfy7hTkPMip"
			"7SQAhGl3LM8c2w/Xx10TQy5oRXuu5BW/Rvyvaeb26HA/TnLkiTxJ/bWIkBTTBTt/jFj7Xs7u0AuV"
			"coQwLvxw4FoigvLcZxrrUwKKHddaS72q2xkv5YAU3km7e/XGEzGgTU/+KKZz9QTlRZkXE64q9cHF"
			"jxPPp+cW10iqEiMOvJy2bXfYIsbZ78+WyrGWY75xfxkXk1RLLjZcV5W5kzXditqZyJz7TfhOvyFP"
			"vg9KKCUpeIbrIb+1sqCkFkFRvEXCYtbcgXJqEDgA4vFV4wBfJmjX0SbwhG/0qkIETw4G/k8wY6G7"
			"cvUHtO7adyHX+y/6Nub7DeNLY3ee+luG7xNB/rFTkW88k9w06xb8BG/FloSK0qKbvWtRlXStAAEA"
			"kOakJDcOGG7UijmVjHnMuPJ4PF7MSlem4EV22qGokwVSAEAbn+e+aNGxHBq5FZFuN2PV9q0blyya"
			"6WSCCFtldotjLpyN2bBwziR7YxRBdKNcjwiFMn0Ox1Cf4zB347ZPhr++rGQYD5nqZl1VplwG5DU5"
			"xcYTPGz1DR08xsgK84QqTZq4r/mP9bXdkZF7s903+Y/qkQo8LtyClPvcFQEuJVfvdn1XgsV9z9FW"
			"VFeHW3xYo5aFRWya5WBErNKqsh02MRqFSWO7LP3a343LMrazt2KiqODutWdj/D91MtM3tp+1auU4"
			"TpuCOC7ccBC01CgSPK/GtYbrPEKUm9Mqq/grz9J7LP9hHiLNzasd5jyUCTQjV1fF9cvPZQAguJuc"
			"O3iCAwPo5jO/3bfZ3ZxpONDKVCoS4wLRMzE3ZYOCZICqKk9490qBy2d+46xMzEf4rp6JpN0gK3mJ"
			"7SEfVWXxOeli7t6BXHXIauqOEPWDTeCpibgR8vzXnVGF3sHfTLfoWG5a76X9PWbpQkczPSM77/VB"
			"fi6dZK46+tSiVf616S4OmcrFCFNaDFvv6f3u3yhAAKRZ1x9ZeXsNoKs4lZR5LDT9cd72N6Pji4mV"
			"W3sMqXs3TPvpy+wBWp/yjlxXuH/lNdDQlM7K/GnR+vjncttFe2db6SSUfj4bVhf9ELD4vI2bz4Kl"
			"8yeaQAag/EeJ54Q0UWXG5ZpJm4ez4D4AgFgk1jc3pAGAgYGeRCxCwVTFRkbvP3WV75lPj9sGX3Am"
			"/duXFJLMKw8Hve8/acDzYydv10+faw6gkD1L3b8nX1ZXmN005xs30uJ+6tphE6NRnHTuKHcuANp0"
			"/+AFZO6OEYLUJo6FciIy7b2W26tqiPVq9PIOtqU0Q6NQOoEZV8NhzQ5V93OrhFls94BpeVEZxSXy"
			"54NGO+pDsULOmfyBwe9JOf5f2ty8/NLNZ0JmIgCLY84o+S1s38VH/MHr93eSylbIsk8ErPtdVFHS"
			"MjH4P5bkrm/wlZcHACBvqK7nWFq1y3oyuFb9mx/XyMGehMwnrofDmBkAAPJ6vEWxGdli7jaF7VOD"
			"yAHpkDshb6jFlUnX0SbwVFUHBBUiyz+2/b50VGCgfadVTt7c0GJiacEApLY4u6wZNTIdgO+TQ0sO"
			"vjP9Ne2KdpjiFyMVpUUf4OVlGXA9DxkmvZFp6/05lw7PMafCTbXmO4a8QwCSzp22KXgeABiO/c9C"
			"2PzjWc8fB/RCR0nfExEXnw3c+ovs4+DA2VZ0AGC9t5J37uftnsLfDvyqo7fp9Ox8Nu+P/zls8btl"
			"BzeFpTWgAMBg6unrG1mMXhZ1MGD8q9ujdAZDLpMDgEIuR+n07jYxSUFSWvO7g4qTrmmmpN49NBA+"
			"uHy7tS3n5OHbNbKslBs1KADQ6NyRntOmvCNpsfP9yJG8wpPadpjEaB6vqCA++CiyZNs8W6a+HlMq"
			"fSWrLRMJhFJVjTBeG1mkW5IAM676To7c4ge/P0THTrR2HcvJv3knX9/RmQMAIEXYnnMcniTde3Y1"
			"VeQ5204haw+BOWTulpDQrR6VZxOLX/8EpTGd/GL2844nHPqwlPezCsF7dV1tUNpis/UkIpGyx4hY"
			"ymSzyV9VElUugUUDDYpZKwcahPwamtoyIfLEJmikALbb5r3LBUcjL3aagTSWPlMsFikAqc7/6/4V"
			"XlDsbQGuT8ZkOtOTy3zV0AhKQlVp0blTvawfX3+ccT17iLeHGQDuVBLmO37201ij/X/i8Xi8A2GL"
			"lBc2tH5T1q4amBx9/qXGd1vVQ24DQMouBW+OLRu3JdhvOLQIW+7tXfVZxA2Rue07FgayxvomXUSC"
			"ZMatCUlpBENr1w8//8CyKKdCDkC3dJ7t6zv3g2kunSTzGBbWFo0VZTIAaXmFeMAgjspOIEWnYm69"
			"+2VM5HLW2T1JldpuAfr9zeTV/DYAAKSK32zK7SdITylyXjhvkqvr+DlLZ7SmXX0hBwC6iY2Ti9sn"
			"X/hUn4rP0eCF+u7b4RKjYfSyiqTwH/Pf3/n1NC4d6BZOI5FH91/KAQCtuvD1qjgViyTOa2V/ki3J"
			"gB1Xej/nUbTUK8LRE62YHLexkHpZMMJlUPu1nwIMJ8wdU3I0NEXPe5YtXQEA8me/RR58IDUwtXFx"
			"sW0pJxhYlomZUVtzM6kZg688ZZDmzk6Kh3+UIgCA1qXf5Y9wcSD99zfCyqVzcRaHkS5mLR2QD7mz"
			"cbVlQtSVgQSNaEx7FzeXhYEr9M+EnSyUdrR2Gc18cLtIbOj44eovOv643rVPRJ0xxk1FXTzHiJvg"
			"RtnEJQFAVFpmHl6DH+8/mDPM253Txa7yVBLmu1+a6P2nrvvc7OKpDImO/wJA8haQ9GnCkRt8McoP"
			"WZQCwJoUmODvyvhv+PypEcAc6PXVfJ18LoLlONf3Usim9bcGm0vKygzn7xzBalTx906Ox4LJF/Zs"
			"CnoHLW322T5e1b0W2bOz0VetV/HczforNixI3bL36qTvZpO8K9AF/UlLPr0cvG79H/bs6lKZz5bV"
			"cDukbMySYHdXPQCQDypO3JFS8omz8mymwwL/YauOXpofvWCQJr+9VbXDJUaj2FH+hYjdDwQjWqI2"
			"pwLdZOKqHX7rHQOD1mUPMReW1Qz6fJcL8QKBHw7DIbbYlhWpr36wAsNmzvYgX60FXpl2zg7MG5yJ"
			"dkygm493MTta4jSMBaBcMPQcfafQU0tnzbCgVQMAMGxGvVMUHrAt1bL1WdOMjZ0qQPkLWiFuRWyX"
			"B5K7DYfvKkjTAQCYI5ZumrAzZM0WW/O2iga7Fbs8SD2i01038RY5IlLFrL0DreywnPBl8ljRZbS3"
			"4jyxaLhGFe2/V+lWPt8EZK8Lj3M6sNHVGACYI5Z/5RX8w7oAW1vDljL+4LmzjaGRRGf00a5TcS1X"
			"JxsAZoKvHejY6JvUtSQaL6suLY6797CY3Xrr3ZRbMbYK7azUme+YhK9zDEwnv3A35WE6d8aXK9Me"
			"xuqgsxhUScgnJCRkZWV1ozEvaap8UV7XJu+RUH1ERER6enpnq838F+W1QpnalnJhbXlFnahH3rvh"
			"0KFDJ06c6HxEJqwtL+U3S3vLoxpIJCY3N3fXrl3krDVVvnjZINbCK9mWGMLCwh49eqRpq26RNvO1"
			"iaSduLi4+Pj4zkdUJhgR1FRUNqrzEx0dnZycTM45ziKJYs7MzAwNDSVnn2TIWCIiIv7888/OR9QP"
			"NoEnDSpE1lZXUc5vlnR7Et6FLqbimTNnDhw40CUYnFXSixEZetk8Caqrq2NjYzEHtX+AX890oK2p"
			"DncipVUTK1sTMifSDbk25G+z6wCGIdfG7p902BXSiSFnjeTg4b32zrBrA0unGVGdYKaxhbWx7vwQ"
			"WdR1MesqZPWDTeBJgwphGJhbq/3RiHfRO1MRb1Wnc66XzWsL9SYwBQUFRR+F2gAoKCgo+ijUBkBB"
			"QUHRR6E2AAoKCoo+CrUBUFBQUPRRqA2AgoKCoo+i8jFQkUj0+PHj8vLyXnUvFotzc3Obm5t71YsW"
			"1NfXM5nMy5cvv+lANKCurk4ul7+dMSMIkpmZWVVV9aYDUdLY2CgUCnWYK6FQ+OzZs95LfnV1tUwm"
			"69XBFYvFOTk5TU06ebH/baeyslIikbydk6WXEAqFEgn2QwMqN4C2trbGxsaGhoZejgpqa2uFQg2/"
			"gdj7SCQSqVRaWFj4pgPRALFYDABvbczV1dVvz04vkUgQBNFhrmQyWWNjY+8l/58Z3LdzMvYGra2t"
			"CoXirZ0svQGCIHI59psTKjcAc3PzyZMnOznp5DMPKomMjPTw8Jg8eXKvetGCw4cPs9ns5cuXv+lA"
			"NCAvL+/8+fMBAQFvOhACwsPDZ86cOXbs2DcdiBIej2dqarp48WJdGYyJiRk+fPjs2bN1ZRDD06dP"
			"L1261KuDGxkZ6enp6e7u3nsu3h5++eWXxsbGNWvWvOlA/jlqamoSEhIwB6m/AVBQUFD0UagNgIKC"
			"gqKPQm0AFBQUFH0UagOgoKCg6KNQGwAFBQVFH4Xk56ARfvrpo4kP+ajFmI9X+ntaswAAkOKzIT/e"
			"4i76YdM0jjoDZECbsi4cib/zXGQ81HPRZ/8azSmKD9pzpwmAxjS2Hj9/5bJJFuWJEYmmGzZN57SW"
			"ZaRdSWt02fLZxA6tXyT3VFBsejMAg20x0sfvs5kGN3/gNfkGLXNko03pcd8/dd66Zoo2CkLCP/b9"
			"WD3rvwuGMgGtuxZ9SLJoq68NA+QVlyL2NX4c6ufIwnv3H1uyP/T3UjkoFECjATBHLYn4cgoNGzZa"
			"deWHrqct2jDk3kkdRC0vVebKsOL28SMXsxoZth5+q+c5mtCFxSnHT13Nb2DaTPp05aJxXDralJlw"
			"4PS9GobtNP81H4806nY4aEzjgY7eS5bPdFCe1zUNJJAVYMZ1bMWxiIKJgcscWUjW8Z23h/53gwdH"
			"9ve5iMuWG7wrIvdVzwzdON2MDgAgKzyz46eX74du9jKnA0DzvbjQG7abt83trKCjcTz4rpq8UObO"
			"VPQs9ecTV3Ib2faei1Z0KFdp3EM38SWlQcBZJM6+hg5eTw2ikFGi6aIObJmgBfGht4YGrp7Qoe1L"
			"4AnTqF/RqzZozc3Y2DzXjWumKCVcUEF+8rHT1wubmAPHzft8mftAwPXJioFzIcJMRfH7znlHLnWd"
			"ZmuGPO5a8Oq7ip/gc4xziRcjhoGl45wVK7wGv1aE6Rqko+xGTGyd7452RUck91RIms1XG6exumbG"
			"4E+sy81Dbn3befma/a5Rl8HuNPE6hh8AwMB1/oz6xGTMUuNJckkmVdCyv08Hbj9ZOsB9vFHusR1R"
			"yXUoAMhLf/0x7urDJyV1iG6kKoW3Y8LSrVeERYetsP8rMvJKnUxQXmLy0R4eb1/kWpein767wJe3"
			"VRaU1CJIXnwk705BzoOc2s6PtSpaXhSbfLSHx9sT9KHsbMix/AGeHgZJe04XSVsfHj7wcPB0Ny31"
			"4+T1f+dVtCoAABTi6uKiSpECAGTPUy4+Lky7lCEi8v5znde2/Tzennn969/x38vj7fvSk00QNt1q"
			"Fua0acN1E7WiPVfyil8j/tc0c3t0uB8nOfJEnqT+WkRIiumCnT9GrH0vZ3fohUq5NPPo7nT7dT98"
			"92+T5KhTRbLuhyMuevs87q2QoHPl7Z3ApIEEKHZcay31qm5nvJQDUngn7e7VG0/EgDY9+aOYztUT"
			"lBdlXky4ym/3JX6ceD49t7imXZ6wNePI0Ys5hVWiLjp5mseD76oyd7KmW1E7E5lzvwnf6TfkyfdB"
			"CaUkRUVxPeS3VhaU1CIoirdInH2NHSinBoEDUDFduneALxNUUJ777LV4LYEnfCNE2Ubw5GDg/wQz"
			"Frq/EvBC6659F3K9/6JvY77fML40duepv2X4PhHkHzsV+cYzMfNncnPXgiejW4qf4K3YkuiILuYb"
			"95dxMUm1rxKBy8MLk3ctqpKuFSAAANKclOTGAcONcZmR4lyimOUr51XkKG7idQTD4/F4Mau8fbFL"
			"DfkLcnJrC91uxqrtWzcuWTTTyQQRtkoUIK+89NNpwdDhhroTZUaEQpk+h2Ooz3GYu3HbJ8M7LjSA"
			"YTxkqpt1VZlyGQDWqGVhEZtmORipcM7ivudoK6qrQ03c1/zH+truyMi92e6b/EfpEZ+uZbgFKfe5"
			"KwJcSq7eFRB7x4WlJuxX6DRqGttl6df+blyWsZ29FRNFBXevPRvj/6mTmb6x/axVK8dx2mQ1OcXG"
			"Ezxs9Q0dPMbICvOU7wGpHA49c8d5G+dIk5JLZN2lgQSvxrWG6zxClJvTKqv4K8/Seyz/YR4izc2r"
			"HeY8lAk0I1dXxfXLz2UAILibnDt4gkP71b4o8/gJwcx5DpjfsNrEo6qrwrtXClw+8xtnZWI+wnf1"
			"TCTtxgsNZaU7KhdVZfE5cfY1diBXHTLZuusMvkywarQEnpqIGyHPf90ZVegd/M10i47lpvVe2t9j"
			"li50NNMzsvNeH+TnYkDQpxZt8o8tePJ97oxM5WKkZ2JuygZFRzrweXg5yHt6v/s3ChAAadb1R1be"
			"XgOEatPZCVULSNeJpyNI3QJi2k9fZg/Q+pR35LrC/SuvgbS6a7FHKr2C/Ou25+vs3c5+PhtWF/0Q"
			"sPi8jZvPgqXzJ5pABqD8R4nnhDRRZcblmkmbh7PgfrcmFLJnqfv35MvqCrOb5nzjpg90/amrfM98"
			"etw2+IIz6d++pJBkXnk46H3/SQOeHzt5u376XHMC79pC76+7qOncUe5cALTp/sELyNwdIwSpTRwL"
			"5URk2nsttwdZyXWxvrkhDQAMDPQkYhGAKRAPR4dRMysLUXGtHIbLsWkgd0WBGVfDYc0OVfdzq4RZ"
			"bPeAaXlRGcUl8ueDRjvqQ7FCzpn8gcHvSTn+X9rcvPzSzWdCZiIASPJOHqv03vqpOOJKS2fDuGEh"
			"Ew++q3kAAPKG6nqOpVX7fsPgWvVvflwjB3tyOs9deziMmQEAIK/HWxSbdc0+Cqakckg4NYgckA65"
			"E/KGWmyZgDSjyxl4T1V1gG0kzQBZ/rHt96WjAgPtO13JyJsbWkwsLRiA1BZnlzWjRqYD8H1yaMnB"
			"d6a/utAxBf8e6fuAXTDFV79Cln0iYN3vooqSlonB/3mlLU6UccUALy/LgOt5yDDpjUxb78+5Cnw6"
			"WxLxTtUvIB0Tr0M6mM6dtim4/W6TVpC+uyAuPhu49RfZx8GBs61E9w/uv0UfIM+487cUrbh3+bG2"
			"3ruiZ+ezeX/8z2GL3y07uCksrQEFAAZTT1/fyGL0sqiDAePV3h6l0bkjPadNeUfSYuf7kaMhAEgK"
			"ktKa3x1UnHStQsOLt279gPDB5dutbTknD9+ukWWl3KhBCb1ri66jFhXEBx9FlmybZ8vU12NKpRLl"
			"9YdMJBBKaXQGQy6TA4BCLkfp9Fe1RDQcHQEKWhlGRnTCNJACM676To7c4ge/P0THTrR2HcvJv3kn"
			"X9/RmQMAIEXYnnMcniTde3Y1VeQ5204hUwBScmbvXSsPm5qCKmFbTcnzule/lrWMB9vV9qM0NltP"
			"Inp1gwkRS5lsNvmLaKLKJbBooCL7OnOgQcivoeHLBHsGgSc2QSMFsN02710uOBp5sVMt01j6TLFY"
			"pACkOv+v+1d4QbG3Bbg+GZPpDHHvXhe8xnuf0iq+JGhMJ7+Y/bzjCYc+LOX9nI2ozgONzp3qZf34"
			"+uOM69lDvD3MSKQTgEZqAXk18Wis0f4/8Xg83oGwHqz+QHYDQMouBW+OLRu3JdhvOLQIpfqD3WeP"
			"txA1tkpRBSJsbetBAK99ZMatCUlpBENr1w8//8CyKKdCDkC3dJ7t6zv3g2ku5DRT6SY2Ti5un3zh"
			"U30qPkcCSNGpmFvvfhkTuZx1dk9SpbaLqX5/M3k1vw0AAKniN5ty+wnSU4qcF86b5Oo6fs7SGa1p"
			"V1/I8d61RUdRv0JWkRT+Y/77O7+exqUD3cJpJPLo/ks5AKBVF75eFZeNWlhbNFaUyQCk5RXiAYPa"
			"7x8SDoeStvxzydXjpo5kNROmgQTYcaX3cx5FS70iHD3RislxGwuplwUjXJR/2VWA4YS5Y0qOhqbo"
			"ec+ypSsAFBKBsZOrccmffzwoaRCUPc56qUy2dvHgu6oM0tzZSfHwj1IEANC69Lv8ES4OpK8nCSuX"
			"zsVZHIbNPtlLMrIOyIfc2Ti+TDD30om6MpCgEY1p7+LmsjBwhf6ZsJOF0o7WLqOZD24XiQ0dP1z9"
			"Rccf17v2iagzxripiM9Xl4InBW6CG2UTlwQAAMvEzKituRlVmQcHFoCZh9fgx/sP5gzzducQppNO"
			"1BF1C0jHxCPXLTKQ2jykTxOO3OCLUX7IohQA1qTAK7HbdwCA5HrQzcdNXgun6CISluNc30shm9bf"
			"GmwuKSsznL9zBKvxinammA4L/IetOvr7eNcbV61X8dzN+is2LEjdsvfqpO9mW2rxF1X9SUs+vRy8"
			"bv0f9uzqUpnPltVwO6RszJJgd1c9AJAPKk7ckVLyiXNX75fmRy8YpMX1h+zZ2WidRN0Oyr8QsfuB"
			"YERL1OZUoJtMXLXDb71jYNC67CHmwrKaQZ/vcmFxRAsmX9izKegdtLTZZ/v49l+eRMNxuf13p0LG"
			"HDx3xyZXvbrEa7g0LF09XJsLEqadswPzBmeiHRPo5uNdzI6WOA1jASgXDD1H3yn01NJZMyxo1QBA"
			"Mx6zYJ0rAKB1CUV5LQs/cjYGAEDrbmoVD76rIE0HAGCOWLppws6QNVtszdsqGuxW7PIg9YhOd93E"
			"WyTMvi4daGWH5YQrE3jccd8BGDZztm/FeWLRcI0q0gAAgG7l801A9rrwOKcDG12NAYA5YvlXXsE/"
			"rAuwtTVsKeMPnjvbGBpJdEYf7ToV1+LWeFzBf7tE7W0g7ARfO9Cx0Tepa0l0VL+4FbFdHthxg0ZF"
			"xjnu3sNiduutdzMkTqcx1xTbkexXFnELiKLrxGNDxuuhAKaTX9Q6dwNsn8iiUEFCQkJWVpaq/9UV"
			"ERER6enpnQ5ImvkvymuFst52rI5Dhw6dOHGi8xGZsLa8lN8sfVMRqSc3N3fXrl2kTpU0Vb542SB+"
			"fUAurC2vqBPhztPVcISFhT169KjHgOLr4AAAIABJREFUZnRGXFxcfHx85yMqu4oIaioqG8W4412J"
			"jo5OTk4m5xxnUUX2O5OZmRkaGkrOPsmQsURERPz555+dj+DKhIwn9Y06kLXVVZTzmyXdnoR3oYup"
			"eObMmQMHDnQJBmdVg+onlXFcZv7ZNaW6ujo2NhZzsCe3j3oDPRMrW5M3HQQhDEOujd2bDkJX6JkO"
			"tDXtfIBuyCW6yfb2DofOUdlVprGFtbFOXeEsqsi+7hxoCa5MyHhS36gDhoG5tY26k/Auemcq4q1q"
			"UP2kMo7LzFuwplBvAlNQUFD0UagNgIKCgqKPQm0AFBQUFH0UagOgoKCg6KNQGwAFBQVFH0XlU0Bi"
			"sfjJkyeVlZW96l4sFufn57e2tvaqFy1oaGhgMplXr15904FoQG1trVwufztjRhAkKyurtrb2TQei"
			"pKmpSSQS6TBXQqHw+fPnvZf86upqmUzWq4MrFovz8vIEAo2/6fR/kaqqKrFY/HZOll5CKBRKpbg3"
			"uhUK4s8SxcXFNTU16enp9ANqOAQCAYvFYrN1+50eHSAQCOh0upFRT9/8+SeRSqUSiYTD0cm3uXWM"
			"QCDQ09PT1+/he046Q+fjKxAIGAyGoaEun+bsjEQikUqlvTq4b+1k7A2EQqFCoTA21u0Dvm81crlc"
			"Lpdv376980GVvwC4XO6UKVOcnJx6NabIyEgPD4/Jkyf3qhctOHz4MJvNXr58+ZsORAPy8vLOnz+/"
			"ZcuWNx0IAeHh4R988MHYsWPfdCBKeDyeqanp4sWLdWUwJiZm+PDhs2fP1pVBDE+fPr106VKvDm5k"
			"ZKSnp6e7u3vvuXh7+OWXXxobG9esWfOmA/nnqKmpSUhIwByk/gZAQUFB0UehNgAKCgqKPgq1AVBQ"
			"UFD0UagNgIKCgqKPQm0AFBQUFH0Ukl8DRfjpp48mPuSjFmM+XunvaS27u2/L8aeIAgAYQ+fv2vaB"
			"pQ5iQZuyLhyJv/NcZDzUc9Fn/xrNKYoP2nOnCYDGNLYeP3/lskkW5YkRiaYbNk3ntJZlpF1Ja3TZ"
			"8tnEzk+tCYtTjp+6mt/AtJn06cpF47iAMWmm1YYn/GPfj9Wz/rtgKBPQumvRhySLtvraMEBecSli"
			"X+PHoX6OLABAck8FxaY3AzDYFiN9/PzHluwP/b1UDgoF0GgAzFFLIr6cQsOHjWn32ewhjUk/8Jp8"
			"g5Y5stGm9LjvnzpvXTNFc2F4eakyV4YVt48fuZjVyLD18Fs9z9GEjk0SHW3KTDhw+l4Nw3aa/5qP"
			"RxoRD4cZHZvefkXxobeGBq6eoNmzwrICzLiOrTgWUTAxcJkjC8k6vvP20P9u8ODI/j4Xcdlyg3dF"
			"5L7qmaEbp7ePnKzwzI6fXr4futnLnA4AzffiQm/Ybt42V/nd9A7LDANLxzkrVngNJvfYKbarJi+U"
			"uTMVPUv9+cSV3Ea2veeiFR3CJRr30E18SWkQcBaJs6+hg9dTgyhkVMV06RZsmaAF2NEm8KSyQtCa"
			"m7Gxea4b10xRfsEfFeQnHzt9vbCJOXDcvM+XuQ8EXJ+sGDgXIsxUFL/vnHfkUtdptvbd7FNHf8us"
			"Yw32WfHFbAcSX8onmOBzjHOJF6OupYXWpcXE1vnuaJflQnJPhaTZBPhURv3U+VSLv9SaVy5MynGl"
			"MY0HOnovWT7TwahjpAEADFznz6hPTMasKhpIwOMhVdCyv08Hbj9ZOsB9vFHusR1RyXVITdHDp3UW"
			"k2fNnj171qShunmYWng7JizdekVYdNgK+78iI6/UyQTlJSYf7eHx9kWudSn66bsLfHlbZUFJLYLk"
			"xUfy7hTkPMip7az4hNZfiwhJMV2w88eIte/l7A69UNmCMallZPL6v/MqWhUAAApxdXFRpUgBALLn"
			"KRcfF6ZdyhABAICi5UWxyUd7eLw9QR/Kzob8XOe1bT+Pt2de//p3/PfyePu+9GQTho1pdywHtfH0"
			"MEjac7pI2vrw8IGHg6e7ab76A4CiPVfyil8j/tc0c3t0uB8nOfJEngSXJLk08+judPt1P3z3b5Pk"
			"qFNFSsVp/HDgWiKC8txnDRrLbqPYca211Ku6nfFSDkjhnbS7V288EQPa9OSPYjpXT1BelHkx4apS"
			"81z8OPF8em5xTbs6VWvGkaMXcwqrRAqc5Zhv3F/GxSTVkosN11Vl7mRNt6J2JjLnfhO+02/Ik++D"
			"EkpJCp7heshvrSwoqUVQFG+ROPsaO1BODQIHoGK6dO8AXyZo19Em8IRv9KpCBE8OBv5PMGOh+yv9"
			"FrTu2nch1/sv+jbm+w3jS2N3nvpbhu8TQf6xU5FvPBMzzaYIEyMOvJy2bXfYIsbZ78+SGjL8BG/F"
			"loSK0qKbvWtRlXStAAEAkOakJDcOGG7UijmVjPku4xoXvX0e91ZI0LlyeYdfHo/Hi1nl7YtdVXr4"
			"Ygi5pYVuN2PV9q0blyya6WSCCFslstqaBhrXepA+kzN0wqR3dbMBIEKhTJ/DMdTnOMzduO2T4a8v"
			"KxnGQ6a6WVeVKZcBYI1aFhaxaZaDUVdBUMHda8/G+H/qZKZvbD9r1cpxnDasSWhJ3LLgi21fbVi5"
			"aOH64z1QbQQApCDlPndFgEvJ1btdX51kcd9ztBXV1eEWHxVh49uZuK/5j/W13ZGRe7PdN/mP6tG7"
			"eDS2y9Kv/d24LGM7eysmiuKTJKvJKTae4GGrb+jgMUZWmCdU9g87HPiWxK8QkufVuNZwnUeIcnNa"
			"ZRV/5Vl6j+U/zEOkuXm1w5yHMoFm5OqquH75uQwABHeTcwdPcGi/2hdlHj8hmDnPgfA3rJ6JuSkb"
			"VLzjiENV5QnvXilw+cxvnJWJ+Qjf1TORtBtkJS+xPeSjqiw+J86+xg7kqkNWU3eEqB9sAk9NxI2Q"
			"57/ujCr0Dv5mukXHctN6L+3vMUsXOprpGdl5rw/yc+l0nd7RpxZt8k83n/ntvs3u5kzDgVamUpFY"
			"uyKVqVyMMKXFsPWe3u/+jQIEQJp1/ZGVt9cAuopTSZlXtjR3nLdxjjQpuYTcBYH2kLoFxLSfvswe"
			"oPUp78h1hftXXgMZZSxD04anV6+mP/3rwM1dJ8N9uD0PpZ/PhtVFPwQsPm/j5rNg6fyJJpABKP9R"
			"4jkhTVSZcblm0ubhLLivur28obaJY6GsMaa913J7AGlXkyDOkaO2/woOcpecC9h4s3ipo6O28pqS"
			"zCsPB73vP2nA82Mnb9dPn2sOoJA9S92/J19WV5jdNOcbN9IvvRK06z91le+ZT4/bBl9w7uF7mXTu"
			"KHcuANp0/+AFZO6OEYJUbJJkJdfF+uaGNAAwMNCTiEUApoAfDqOXd7AtpRnaRoUZV8NhzQ5V93Or"
			"hFls94BpeVEZxSXy54NGO+pDsULOmfyBwe9JOf5f2ty8/NLNZ0JmIgBI8k4eq/Te+qk44kpLZ8MK"
			"WfaJgHW/iypKWiYG/4ekkia+8vIAAOQN1fUcS6v2/YbBterf/LhGDvbkZD679nAYMwMAQF6Ptyg2"
			"65p9FExJRU04NYgckA65E4RzqctoE3iqqgOCCpHlH9t+XzoqMNC+0yonb25oMbG0YABSW5xd1owa"
			"mQ7A98mhJQffmf5qY2dxzI1Lfgvbd/ERf/D6/VoqppviFyMVpUUf4OVlGXA9DxkmvZFp6/05lw7P"
			"MafCTbXm8UNON7OyEBXXyqFD/ZHOnbYpuGcS8HhI31wQF58N3PqL7OPgwNlWdL0JAb8knzv04569"
			"K12a/ryVqZtY9Ox8Nu+P/zls8btlBzeFpTWgAMBg6unrG1mMXhZ1MGB89780aPp6TKlUotxvZSKB"
			"UIo1CQA0Y3MuG+hGxgaoFOnWXneuQPjg8u3WtpyTh2/XyLJSbtSgAECjc0d6TpvyjqTFzvcjR/Lf"
			"BCBoJylISmt+d1Bx0rWKnmrCAwCICuKDjyJLts2zZeKTRKMzGHKZHAAUcjlKp78qMEzuGln49GoP"
			"Zlz1nRy5xQ9+f4iOnWjtOpaTf/NOvr6jMwcAQIqwPec4PEm69+xqqshztp1CpgCk5Mzeu1YeNjUF"
			"VcK2mpLndR0jSWM6+cXs5x1POPRhKe9nrJK5KrCVp7TFZutJRK9uMCFiKZPNJn8RTVS5BBYNVGRf"
			"Zw40CPk1RHMJcwaBJzZBIwWw3TbvXS44GnmxUynTWPpMsVikAKQ6/6/7V3hBsbcFuD4Zk+kMYe+Y"
			"Q+ZuCQnd6lF5NrFYq0toGkFJqCotOneql/Xj648zrmcP8fYwA8CdSsI8wb1KiaCVYWREBxprtP9P"
			"PB6PdyBM16s/kN0AkLJLwZtjy8ZtCfYbDi1CUWXifxet3HOvRVJd16Qw0s33NJDMuDUhKY1gaO36"
			"4ecfWBblVMgB6JbOs319534wzUW9ZB7dwmkk8uj+SzkAoFUXvl4V9+QhxqSWoen3N5NX89sAAJAq"
			"frMpt58gPaXIeeG8Sa6u4+csndGadvWFHADoJjZOLm6ffOFTfSpek/tL2HZI0amYW+9+GRO5nHV2"
			"T1JlD7cAWUVS+I/57+/8ehqXTpCkbNTC2qKxokwGIC2vEA8Y1H5TETcclf1xLbXeQHHjSu/nPIqW"
			"ekU4eqIVk+M2FlIvC0a4vFLEBsMJc8eUHA1N0fOeZUtXACgkAmMnV+OSP/94UNIgKHuc9RKXbJaJ"
			"mVFbczOpvwHgK08ZpLmzk+LhH6UIAKB16Xf5I1wcSP9gJKxcOhdncRg2+2Qvycg6IB9yZ+P4MsEM"
			"NlFXBhI0ojHtXdxcFgau0D8TdrJQ2tHaZTTzwe0isaHjh6u/6Pjjetc+EXXGGDcVsfmSPfst8uAD"
			"qYGpjYuLbUs5qdmDm+BG2cQlAUBUWmYeXoMf7z+YM8zbvesdeeWpJMzjomzLP5dcPW7qSG3vUJCF"
			"1I4ifZpw5AZfjPJDFqUAsCYFJu/0GXsudMvMBJRh6bl5kasuImE5zvW9FLJp/a3B5pKyMsP5O0ew"
			"GjX7qy3LyW+9Y2DQuuwh5sKymkGf7xrnIqu/0tkkgFZ7gP6kJZ9eDl63/g97dnWpzGfLargdUjZm"
			"SbC7qx4AyAcVJ+5IKfnEWXk202GB/7BVRy/Nj14wSJPf3q/afbxeHH3VehXP3ay/YsOC1C17r076"
			"bjbJmxl4UP6FiN0PBCNaojanAt1k4qodmCS5sDiiBZMv7NkU9A5a2uyzfXz7rSv8cBgOscW2rEh9"
			"9fMUGDZztgf5qhV4Vdl5O2cH5g3ORDsm0M3Hu5gdLXEaxgJQLhh6jr5T6Kmls2ZY0KoBgGY8ZsE6"
			"VwBA6xKK8loWfuTccQmi/L2sELcitssDyd2Gw3cVpOkAAMwRSzdN2BmyZouteVtFg92KXR49/WsX"
			"gUXC7OvSgVZ2cHPJhQWPFV1GeyvOE4uGa1SRBgAAdCufbwKy14XHOR3Y6GoMAMwRy7/yCv5hXYCt"
			"rWFLGX/w3NnG0EiiM/po16m4loudGEybUe8UhQdsS7VsfdY0YyOphGIn+NqBjo2+SV1LovGy6tLi"
			"uHsPi9mtt95NuRVjq9DOSp35jnX+VVMZc/DcHZtc2ZDxOuvAdPKLWudO4rEmDVAlIZ+QkJCVldWd"
			"yLystbaythXpkVB9REREenp6pwOSZv6L8lqhTHuTkqbKFy8bxD00eejQoRMnTnQ+IhPWlpfym6Xa"
			"R9bb5Obm7tq1i9SpuCTJhbXlFXUi3Hm43OFakiMsLOzRo0eatuo94uLi4uPjOx9RWSaIoKaislFd"
			"j6Ojo5OTk8k5x1lUkf3OZGZmhoaGkrNPMmQsERERf/75Z+cj6gebwJMGFSJrq6so5zdLuj0J70L9"
			"VJQ289XEcObMmQMHDnQJBme154vRP2eeBNXV1bGxsZiDPbinxDDiDtT515L1TKxsTXpmwXSgralu"
			"TQIAAMOQa2PXczNvB7gk0Q25RDfZ8LnDtfz/BZVlwjS2sNbtR4NxFlVkX3cOtET9YBN40qBCGAbm"
			"1mp/NOJdqJ+KLC3mPN6qjlaOf8S8tlBvAlNQUFD0UagNgIKCgqKPQm0AFBQUFH0UagOgoKCg6KNQ"
			"GwAFBQVFH0XlU0ASiSQrK6umpqZX3YvF4sLCQrFY3KtetKCxsZHJZF6/fv1NB6IB1dXVcrn87YwZ"
			"QZDc3NzGRtyz3m+IpqYmsVisw1wJhcKysrLeSz6fz5fJZL06uGKxuKCgQCQS9Z6Lt4eqqiqJRPJ2"
			"TpZeorW1VSrFvdGtUPHFrLi4uIaGBj29Hn2JTC1CoZDJZOrr9/D1F90jFAppNJqhoS4fzuttpFIp"
			"giBGRjp/NlcHvG0DrfPxFQqFdDrdwEC3b+m85h8Y3LdtjHqVtrY2hULxdk6WXkIulysUiu3bt3c+"
			"qPIXAJfLnTJlipOTU6/GFBkZ6eHhMXny5F71ogWHDx9ms9nLly9/04FoQF5e3vnz57/++us3HQgB"
			"4eHhs2bNGjt27JsORAmPxzM1NV28eLGuDMbExAwfPnz27Nm6Mojh6dOnly5d6tXBjYyM9PT0dHd3"
			"7z0Xbw+//PJLY2PjmjVr3nQg/xw1NTUJCQmYg9TfACgoKCj6KNQGQEFBQdFHoTYACgoKij4KtQFQ"
			"UFBQ9FGoDYCCgoKij0Lya6AIP/300cSHfNRizMcr/T2tWQCtJWlnf03LM/7g2w3TdPNNO7Qp68KR"
			"+DvPRcZDPRd99q/RnKL4oD13mgBoTGPr8fNXLptkUZ4YkWi6YdN0TmtZRtqVtEaXLZ9NZKs0YEYH"
			"YXHK8VNX8xuYNpM+XbloXL+i+NBbQwNXT9BDa27Gxua5blwzBfdFcRzCP/b9WD3rvwuGMgGtuxZ9"
			"SLJoq68NA+QVlyL2NX4c6ufIAgAk91RQbHozAINtMdLHz39syf7Q30vloFAAjQbAHLUk4sspNKKw"
			"MUGOlV39AdMy3K9fKqZjapGXKnNlWHH7+JGLWY0MWw+/1fMcTeiYnHDpgBKlU8Vw0JjGAx29lyyf"
			"6aB8gq5rGkggK8CM69iKYxEFEwOXObKQrOM7bw/97wYPjuzvcxGXLTd4V0Tuq54ZunF6e5dlhWd2"
			"/PTy/dDNMxSpUUHnSuQAwHxvWdSmqa+/F6lxPPiumrxQ5s5U9Cz15xNXchvZ9p6LVnQIl2jcQzfx"
			"JaVBwFlEmzITDpy+V8Ownea/5uORpJ5LxDl4PTWIQiYcX4oOCCb4HONc4sWIYWDpOGfFCq/B+gAA"
			"aF1aTGyd7452rS4k91RImk2AT2XUT51PtfhLrXnljO4YVwAAg3GrwqcUhp1SLNuxdJQ+gPTh4fD8"
			"SUHLtZaxJYJUQcv+Ph24/WTpAPfxRrnHdkQl16GSvP9tXBWW2mw73sVOV48NC2/HhKVbrwiLDlth"
			"/1dk5JU6maC8xOSjPTzevsi1LkU/fXeBL2+rLCipRZC8+EjenYKcBzm18m4MAFp/LSIkxXTBzh8j"
			"1r6Xszv0QiUiKM991oCC4MnBwP8JZix0V7/6A4C8/u+8ilYFAIBCXF1cVClSAIDsecrFx4VplzLa"
			"X5xRtLwoNvloD4+3J+hD2dmQn+u8tu3n8fbM61//jv9eHm/fl55swrBxQVZZzMK0dH2MyQwZoStF"
			"e67kFb9G/K9p5vbocD9OcuSJPAkuJ3LidKoajrjo7fO4t0KCzpW3n4xJAwlQ7LjWWupV3c54KQek"
			"8E7a3as3nogBbXryRzGdqycoL8q8mHBVqXkufpx4Pj23uAYBeWVhIXd+ZHR0dHTUF+6dvxaseTz4"
			"ripzJ2u6FbUzkTn3m/CdfkOefB+UUEpSnA3XQ35rZUFJLYKieIvSzKO70+3X/fDdv02So04VkVMw"
			"xDtQTg0CB6BiulB0gJ/grdiS6Mh4zDfuL+Nikmrb5yDd7F2LqqRrBQgAgDQnJblxwHCjVsypZMy3"
			"B9Lhhcfj8WJWujIFL7LTDkWdLJACANr4PPdFi3Yi9yohd0VDt5uxavvWjUsWzXQyQYStkuabJ07/"
			"PfzTf0+1NdWj66qoEKFQps/hGOpzHOZu3PbJ8NevoDGMh0x1s64qUy4DwBq1LCxi0ywHI1q3BkBw"
			"99qzMf6fOpnpG9vPWrVyHKdNAQCAPP91Z1Shd/A30y16cAcMKUi5z10R4FJy9a6gy3+wuO852orq"
			"8Ks0cdgqguyuY5q8nEdjuyz92t+NyzK2s7dioiiBO+K4VDrVM3ect3GONCm5RNZdGkjwalxruM4j"
			"RLk5rbKKv/IsvcfyH+Yh0ty82mHOQ5lAM3J1VVy//FwGAIK7ybmDJzgwAACtr5dC3b3rtx69lOp1"
			"viLSJh5VXRXevVLg8pnfOCsT8xG+q2ciaTdeaFjsHZWLqrL4vCan2HiCh62+oYPHGFlhnlBLB3LV"
			"IauYLhTdIFM55fRMzE3Z0PH6LMPWe3q/+zcKEABp1vVHVt5eA+gqTiVlHgtNf5y3/c3o+GLt9Ve7"
			"hdQKyLSfvszP613p0+NHrivcP/LivigobhUXXDl97uTujZ8HJfJ1Eko/nw2rB1wJWPzZV1HnXnBH"
			"O5jQAVD+o8Rz586e3BvyS82kqcO7/+2DNSBvqG3iWCgXeaa91/KZDkwAWf6x7bFFg2fNsO/RS86S"
			"zCsPB70/dZK3y4urt+tRAACF7Fnq/j3R3wcFHG+aM4+cHCEQB9l9x0w02Lfo3FHujpYMtOn+wQvI"
			"3H+NEKh3p94p3czKQlRbKydMAxkw42o4zNmhKju36q8stvvyaYOeZRSX5D4fNNpRH0Ah50z+wPzP"
			"pBwpWnfz8ks3n+E0AAA529KK1tTUWvp70Jexj15f7GsVD66r7YflDdX1HEurdllPBteqf3NNDekN"
			"oGsPlWLe8nq8RbFIrG9gSAOgGRjoScQisjkknBpEDqirfi0wxZeEQpZ9ImDd6n8v2X7P8RPvDo1W"
			"+gAvL8uM63mI5OmNTFvvqVw67lQS5jsmV7sA5OrVq9cG/dL+c9Bw7H++eOfaj2ef9cpAkl5KxMVn"
			"A7f+Ivs4OHC2FV0uk9OHLIo6dvTQxomSv24/1E0senY+m/fH/xy2+N2yg5vC0hpQAGAw9fT1jSxG"
			"L4s6GDBe3e1RjAGavh5TKpUoN2CZSCCUAiiA7bZ573LB0ciLeCVmstBA+ODy7da2nJOHb9fIslJu"
			"1KAAQKNzR3pOm/KOpMXO9yNHkh8ZIAxSTccaSC+0SkQF8cFHkSXb5tkyybhT71QiaGUYGdEJ00AK"
			"zLjqOzlyix/8/hAdO9HadSwn/+adfH1HZw4AgBRhe85xeJJ079nVVJHnbDuFTAEAhpPW7Q5dv2zZ"
			"hmA/q4d/FL26PNIyHmxX24/S2Gw9iUikzBUiljLZbPIX0USVS2DRgM5gyGVyAFDI5SidTl6ij6QD"
			"DUKmeAWNoCRoTCe/mP284wmHPizl/ZzdcUVO5071sn58/XHG9ewh3h5mALhTSZjvKFQaa7T/Tzwe"
			"j3cgbJHysoHWb8raVQOTo8+/1HTek4DcBoCUXQreHFs2bkuw33BoEcptBtuwakqKG8UNtU1ytglH"
			"F5EgmXFrQlIawdDa9cPPP7AsyqmQA9AtnWf7+s79YJqLesk8nAG6hdNI5NH9l3IAQKsufL0qLhsB"
			"GtPexc1lYeAK/TNhJwtVrn5d0O9vJq/mtwEAIFX8ZlNuP0F6SpHzwnmTXF3Hz1k6ozXt6gs5ANBN"
			"bJxc3D75wqf6VHyOhFy3CYMkkRnyyCqSwn/Mf3/n19O4dDLu1Dttyz+XXD1u6khWM2EayPQaM670"
			"fs6jaKlXhKMnWjE5bmMh9bJghMug9gtZBRhOmDum5Ghoip73LFu6AgBAVnD2+5NPJQCoRCR+vchp"
			"Fw++q8ogzZ2dFA//KEUAAK1Lv8sf4eJA+u9vhJVL5+IsDrOwtmisKJMBSMsrxAMGcchekpF1QD7k"
			"vgtughtlE5cEAADLxMyorbm502Js5uE1+PH+gznDvN27LoXKU0mY775O6f2nrvvc7OKpDImO/wJA"
			"8ikg6dOEIzf4YpQfsigFgDUp8Mr3q1fc/Po732kow2LKxkCdfDyE5TjX91LIpvW3BptLysoM5+8c"
			"wWq80iMDwBrit94xMGhd9hBzYVnNoM93ubAq0gAAgG7l801A9rrwOKcDG13VqqfqT1ry6eXgdev/"
			"sGdXl8p8tqyG2yFlY5YEu7vqAYB8UHHijpSST5yVZzMdFvgPW3X00vzoBcolrPuwnXBBqs8M+ayg"
			"/AsRux8IRrREbU4FusnEVTvUuVPptPEykvXzxtW/K2TMwXN3bHLVq0u8hkvD0tXDtdGZZto5OzBv"
			"cCbaMYFuPt7F7GiJ0zAWgHKD1nP0nUJPLZ01w4JW3X76YMdBh6O3FNqyK/7utyCi/TYWWndTq3jw"
			"XQVpOgAAc8TSTRN2hqzZYmveVtFgt2KXR08/HUZgkSNaMPnCnk1B76ClzT7bx/fwkQrdh9wXwE7w"
			"tQMdG32TupZER/WLWxHb5YFd7vBy3L2HxezWW++m3IoVmFPtrNSZ75iEyqY0AGA6+YW7KQ/TuTO+"
			"XJn2MFb3fVclIZ+QkJCVldWdyLykiV9e3YL0SKg+IiIiPT29s9Fm/ovyWqFMa4t4A5KmyhcvG8Sa"
			"mTl06NCJEyc6H5EJa8tL+c1SrSPrFvVBqs9Mbm7url27dOSOrFOShIWFPXr0qMdmuiBt4ZfztSzA"
			"uLi4+Pj4zkdUdhUR1FRUNqrLVXR0dHJyMjnnOItyYW15RZ2ouzaZmZmhoaHk7JMMGUtERMSff/6p"
			"YaP/q5w5c+bAgQOdj+AnuM6q/x8wT4Lq6urY2FjMQW2u1pTomVrZ6G4jemXUxMq2R28V4A3omQ60"
			"Ne1RUAAAwDDk2tj13IwK1AfZ48xo5q4XnOoaFsfKRid3HwHg/7V3ngFRXG3fv7YvZUFkQaREUcQS"
			"EARFwYAlgKKQGCWJFZ4YSTQ21CQ29AbBQNQAiW1jeWLUoHKrD0oRBY0FjREL0kRABSkrvS5bpuz7"
			"gRVhZ1cGWCJ5md/HYc51/ucq5+wMM3PeMlSmrpFZpxeJXYJgka7N7/wWZ086oOgcYoFrNPt72Xx3"
			"od4EpqCgoOinUAsABQUFRT/jbK61AAAgAElEQVSFWgAoKCgo+inUAkBBQUHRT6EWAAoKCop+itqn"
			"gGQyWW5ubk1NTa92L5FICgsLEaSXPnTRferq6lgs1vXr19+1kC5QWVmJYVjf1IwgyJMnTxobG9+1"
			"EAUNDQ1SqVSDvhKJRCUlJb3n/PLychRFezW4EomkoKBAJiP3duS/nIqKColE0jeLpZcQiUTE4NLk"
			"qr5VBAAHDhxonQR7VVNLSwuDweBwNPVFUY3R0tJCo9G0tLTetZAuIJPJUBTV1tbkA4Waoq8FWuPx"
			"7e2E+QeC29di1KuIxWK5XN43i6WXwDBMLpdv2bKl/UG1VwBGRkaurq62tra9qikiIsLV1XXy5Mm9"
			"2ks3OHz4MJfLXbJkybsW0gVyc3PPnTv3/fffv2shKti5c6eXl5eDg8O7FqJAIBDo6+svWLBAUwaj"
			"oqJGjhw5a9YsTRlU4vHjx/Hx8b0a3IiICDc3NxcXjbzY39c5ffp0XV3dihUr3rWQf47KysrY2Fil"
			"g9T/ACgoKCj6KdQCQEFBQdFPoRYACgoKin4KtQBQUFBQ9FOoBYCCgoKin0Lya6CIMO2Po3H3hbjR"
			"uDnL/O3y926MeaLYw4DGcVy2b7nT29uTAq/PPH8k5uYLse5wt/lffDKWlx8TFH2zHoDG1DWbMG/Z"
			"YmejkrjwOP3VgdN4zcXpqZdS6+w3fDGJq2j96tKu0AtFGMjlQKMBMMfMXz3srxP1PkGLbbh4fdrB"
			"Hx/bbVzxwYBuLHmiW/t+qvDa4jucCXj1lchD0vkbfcwZgJXGh++rmxPqZ8MCACTnZNDetAYABtdo"
			"tKefv0Ph/o5yFoav+YBGkK3c7ouZvJtK41i4029ASnvHGJDayb5I4Svt0hvHjlzMrGNYuPotn2uj"
			"RxcVJB87eflJLdPc+fNl88fz6YAT3UkMhwEdlFoOyI8JvT586/KJXdtcE81TiqtD6W/heZO2LrZh"
			"IZnHtt8YvmW1Kw99djY8yXi1R2nEvooZoWuntY4ZfXpq289lH4auny5P2R10thADAOb7i3cHTtHt"
			"YJmhZWwze+lS96HkHmlUHqreS4Xv9MXPU34/fimnjmvpNn/pJ2NJZg9hhE6SeIVBIFjE6zNiD/zx"
			"VyXDYqr/ijmjSX2/n9DBm9JQJVllfCnaUFHgs3VzVE9GKlKro8dt0GtRe6t9trVu6IXknAxJNf92"
			"7VRWx9rRuq3c5fph1//TfiKYNUKRCopg05i6g208Fi6ZYaXTFn4AAC3HedNr4hKVpho3kt/JJZXQ"
			"6LM/tm4+UTTIZYJOzm/bdieKhk6eM2fOnDlzPnLgvMgVcY264my1iG5EhaWZLQ2LDFtqeS8i4lI1"
			"2lRSqPdxtECwL+Ib+/yffzgvxFrK8wqrECQ3JkJwMy/77+yqNzvp0E28Nu0XCKLnDqx5z/8XgWDf"
			"mqkj3Vy1EqL/yJc13z984P7QaU7dmf0BAKt5llvaLAcAkEsqCvLLxXIAQF8kX3z4NDU+vXVHWnnj"
			"ywK9j6MFguigj9AzIb9XuyvJceOqkq3c7rdcQ+VxOD7s6BhyouWtvsJK/xv+v/UzNkfu9OMlRhzP"
			"ldZcCQ9J1vfd/lP4N+9n7wk9X46pdKeKcBBaIk0lOc+7vD8l4MpxrTJmv7qRXoYB8vRm6p3L1x5J"
			"AK9/dKuAzmc3leRnXIy9rNjzXPIw7lxaTkElAlj506f8eRGRkZGRu7920VW2HPW9S9nBqIQqctoI"
			"Q1X4Dq2/vnt7HNP7+53b/YY9+jEotojkhmeEEQqby/MKqxAcJ1qUZRzdk2a5ctcP/6OXuPtk6zaw"
			"3ehAURoqOgDV8aV4A7HAm5VTQm1qETz+Um+E0auEK3kIAIAsOzmxbtBIXULtyAhd4koTQfbrt2MV"
			"XR+M3DyXfz0k6GwJ1iZGIBAIogI8fJSnGvJfSSc3I9KHTA/YvHHtwvkzbPUQUbP8vYkzZ8+ePduN"
			"V/FMPnGut2Y+k4+IRCiHx9Pm8Ky81276dOSbn5UM3WFTnMxeFSumAWCNWRwWHuhlpdPZdqd6Liu+"
			"MruyJyLilyyXQP8xPdoFniA3L/kuf+k6+8LLd5o6/IHFf9/GQlxdTZh8OpGttp2yY7okk8a1X/Sd"
			"vxOfpTvE0oSJ4013rjwf5/+5rQFH19IrYNl4XotctS5COFS07JISIq/jWsm3GyXOyW5GS+/lGns4"
			"CO/nIrKc3Cpru+FMoOk4OsqvJr1AAaDpTmLO0IlWDADAa2pkUP3X1esPymRs4suKbD1DfS6oeceR"
			"gLrME925lGf/hd94Ez3DUT7LZyCp18hueak8QiGuzuKLyuwC3YmuFhxtK9dx6NNcUTc7wNRLJl0u"
			"FG2gaicj5dQierzM1GPagLvX8hAAWebVByYe7oNEXakddRMB29Bm7trZsoTEQnK/EkhB6hYQ03La"
			"YkuA5seCI1flLt+6D2YAAGCliaevM6bt/JCvmX8kDPBcvTx/17oF58ydPH0XzZukB+mACx/EnRXR"
			"xOXpSZXO60ey4G7XbNIHTgnwOfX5MYvg83aavfaVZly6b/qhv/OgF7+duFEzzdsQQI4+T9kf/QSt"
			"fppVP/t7J9IvVHbWTtkxXdJJ549x4QPg9Xd/PY94bxvVlFLPMzJqjRjT0n2JpbqGyr3qlN1UbilL"
			"75KUdijFVdu6werV3ZxXokyuy7qpubvTCwqxF6ZjbThQIMd4k2dqXUjI9l9j/mdSmZPnxIw4AMC4"
			"xia0+vrmuqtBZzI2Hw50VLyBK0ezjq9beUFcWtg4KfgrY3KZScy8XAAArLaihmds0rqtJ4NvMrDh"
			"YSUGliS2+SSM0JqZDgCA1RAtSgwkHENtGgBoabGlEjEO+qRUqywNVR2QlkzxBn3iZKQ6tVR5XD7I"
			"3d143dVcxFp2LcPC40u+vLZKuXYa44iddj6B0A1MjMQFVdibnSPp/KmBwa+3j+8GpBtKCs5s3Xga"
			"nROxdZYJHQBAln32bNZg77UTNfYyNXuI5/r97i1lj66c2BcY1izYog/AYLI5HLbu2MW7l9mba6O5"
			"XbUpzUtIbRhhWpdwpXS6r7mmCoEGor+TbjS3sE4czqtEM59eq5z16UCg0fmj3abaFf6R3ujzsQ15"
			"t3TaTskxsTvcu6hXnBcTfBRZGD7XgvmKzZRJXu8tjYqbpAyejuorI6VeD3xFaNmDbwZ0jCvgtjb8"
			"E39fqMUd1po5cnkX/7yJcWxm8wAAZAjXzdvq/xL+em6ZInZbN0RyXw4A2s4r9zgDAPjoFa+8lY84"
			"2rVeBtCYtn5Ru7y40ufH1mz/PWviGlsy3zJRzrztpgAANC6XLW0UK0aMSGRvdp/v6gjZuelqLGrR"
			"GQwMxQBAjmE4nU6+klWVRs8kU7yGRkiJIH3VqaXS43T+FHez81cfpsuyhnmsMACamFA7KrokM4FI"
			"m5oZOjp0oLHG+v+8y6vnP2rJ/URCiuOD1+8tHr8h2G8kNIpkAHj99dNJVXaffNStLcBV9pFxcEVI"
			"ch1omzl+9OVM4/zsUgyAbmw3y8fHe+ZU+25tmYfkn4y6PmJNVMQS1pnohPLu3gHlDDTAKoQtAADI"
			"K2GDPn9AU1pyvt1nc50dHSfMXjS9OfXySwwA6HrmtvZOn37tWXEyJltK3v7b2xEc00X1aGnCzp+e"
			"fLj9u6l8OtCNbEcjD+6WYQCAvzr/XcDBLNUf4iP0Wj6QZEsyKMeVPsBuDC3lkmjsJBMmz8kBUpKa"
			"RtmbtlaJHLQneo8rPBqazPbwsqDLAQDQvDM/nngsBcClYomqSY6lZ6DT0tBA6n8AxMxTiDS0s5Xf"
			"v1WEAABenXZHOMreivSnsVRmLp1PsGhtZGZUV1qMAshKSiWDTHlkL6fJdkBecv+FUOA6WapTAkA5"
			"tdR43MDVfejD/b9mW3u48EBV1dEJcwq98wmk5cnZxIrxU0ZrMKSkpm/Z49gj14QSXBgyPxmA5bz1"
			"UvS4i7G3aK7BM0019hwpy8bbJz4kcNX1oYbS4mLtedtHsepI/r9TDejzM5GXzQIELgYD5at9Uzb8"
			"ctn5h1kk7wp0gOO88POk4JWrbllyK4pQzw3L4UZI8biFwS6ObADATAvitiUXfmqnOJtp5etvHXA0"
			"fl6kr2lXLjnUtSM4pkvaceH58D1/N41q3L0+Beh6kwK2+a2y2Rq0MmuYoai40vTLHfaqs4kYDu1h"
			"FsotS1NeX4kCw3z25iCfbm8SzRxiZ8W8xps0hAl0wwn2BkcLba1ZAIpvF7JtfD6gpxR5TTeiVbSe"
			"PtTG9HDkhqcW3NJnA3zDrdrSWHFpLJc0IxZLtpK7DUccKsjSAACYoxYFTtwesmKDhWFLae2QpTtc"
			"ST2i87ZhEi3yxL6Tz0cHBr2HFzV4bp7Qwy+xaV5yf0C5wL8ZbFPnk9AxJeqSVKeWGo/zXDyso/aw"
			"VzlpAwCwbJWrTpev37FLPj3rtUXCRPA6q1HmUO9tgY5cSG+7BQTAtPXbvdKl2x8hVLeFfGxsbGZm"
			"Zm9uUi+Xy+Xh4eFpaWntDkgbhC9LqkRob3fcGYcOHTp+/Hj7I6ioqqRI2CB7R4JIOCYnJ2fHjh3k"
			"rNWXvyyrlXSjV7ItlQgLC3vw4EFXW70dWaOwRNiIdKvtwYMHY2Ji2h9R62CkqbK0vK6zEUdGRiYm"
			"JpLrnGARE1WVlFaL39YmIyMjNDSUnH2SkpUJDw+/fft2Fxv9Wzl16tSBAwfaHyEWeBcmI1IeJ9TO"
			"PzunVFRU7N27V+mgpm7gaAq2nomF3rsWoRKGNt9cM487dQvNOoatP9hCv1u9km35D8DimZiTf96t"
			"M9Q6mKlrZKar6g/dhmCRrs3v1i1O0h1QdA6xwLtQc6Q8TqiddzynAFBvAlNQUFD0W6gFgIKCgqKf"
			"Qi0AFBQUFP0UagGgoKCg6KdQCwAFBQVFP0XtU0AymSwvL6+xsbFXu5dKpc+fP+/VLrpHfX09i8W6"
			"ffv2uxbSBYRCIYZhfVMzgiD5+flisfhdC1HQ2Ngok8k06KuWlpaysrLec35ZWRmKor0a3NZilJP9"
			"gtK/m6qqKrFY3DeLpZdoampCEOWXN2nq4r1///7a2loWq3ffI5RIJHQ6nc3W6GfaNIFEIqHRaBxO"
			"D9/L+UdBEATDMC63L37wVyKRMBiM3k4n8mg8vr2dMP9AcPtsMfYGUqlULpf3zWLpJTAMo9FoW7Zs"
			"aX9Q7RWAsbGxm5ubra1tr2qKiIhwdXWdPHlyr/bSDQ4fPszlcpcsWfKuhXSB3Nzcc+fObdq06V0L"
			"UcHOnTu9vLwcHBzetRAFAoFAX19/wYIFmjIYFRU1cuTIWbNmacqgEo8fP46Pj+/V4EZERLi5ubm4"
			"uPReF32H06dP19XVrVix4l0L+eeorKyMjY1VOkj9D4CCgoKin0ItABQUFBT9FGoBoKCgoOinUAsA"
			"BQUFRT+FWgAoKCgo+ikkvwaKCNP+OBp3X4gbjZuzzN/NjF7598lD/71Xjhk7zvvK/4PBGvmoKF6f"
			"ef5IzM0XYt3hbvO/+GQsLz8mKPpmPQCNqWs2Yd6yxc5GJXHhcfqrA6fxmovTUy+l1tlv+GJS24Nc"
			"SM7JoL1pDQAMrtFoT78vZmj9uUtQ7xO02IaL16cd/PGx3cYVH3RnX3jRrX0/VXht8R3OBLz6SuQh"
			"6fyNPuYMwErjw/fVzQn1s2ERe/d3KNwfeqEIA7kcaDQA5piF4avGPos98MdflQyLqf4r5ozWAQD8"
			"1aVdHU+bv3rYXyc0oBorUvhKu/TGsSMXM+sYFq5+y+fa6NFFBcnHTl5+Uss0d/582fzxfDpen6Gs"
			"S304aEzdwTYeC5fMsFKc19ENJEDzlOLqUPpbeN6krYttWEjmse03hm9Z7cpDn50NTzJe7VEasa9i"
			"RujaaQZ0AAD06altP5d9GLre3RAanySdPHO9cszXQZ+NaJ9/XdZDHKreS4Xv9MXPU34/fimnjmvp"
			"Nn/pJ2NJxoEwQidJvMIgECyq9n4XO3hTGqok46rKhaINFQU+WzdH9WTE0DK2mb10qftQDgAAXp0a"
			"tbfaZ1vrpoxIzsmQVPN1nuW7f25/qtG9Ts23pvfruLarsbZIAwBoOc6bXhOX2HFW6coW8ERIJTT6"
			"7I+tm08UDXKZoJPz27bdidWN16K3Ch5xJkzSf/Jb0O5LtT0Q8AbRjaiwNLOlYZFhSy3vRURcqkab"
			"Sgr1Po4WCPZFfGOf//MP54VYS3leYRWC5MZECG7mZf+dXdV+ky9548sCvY+jBYLooI/QMyG/PRnk"
			"5qqVEP1Hvqz5/uED94dOc+rO7A8AWM2z3NJmOQCAXFJRkF8ulgMA+iL54sOnqfHpYlW9/17tvmm/"
			"QBA9d2DNe/6/CAT71rhxMo7uSbNcueuH/9FL3H0yHwUAoJt4KZ02daRmVMtbfYWV/jf8f+tnbI7c"
			"6cdLjDieK625Eh6SrO+7/afwb97P3hN6vhyTqdClPhwHIzfP5V8PCTpb0up7JTeQAFeOa5Ux+9WN"
			"9DIMkKc3U+9cvvZIAnj9o1sFdD67qSQ/42LsZcWe55KHcefScgoqEcCKYrf/eM/Ed9Nm3xEdf310"
			"XQ9xqArfofXXd2+PY3p/v3O737BHPwbFFpHcU44wQmFzeV5hFYLjRIuqvd/lDhSloaIDUFMuFG0Q"
			"C7xZOSXaPB71vUvZwaiEqtYdwegGI4xeJVzJQwAAZNnJiXWDRuo0K51KxnyrEJxQY239CgQCQVSA"
			"h4/yrNLDD6KTm1voQ6YHbN64duH8GbZ6iKhZikokCOc9J29v56FcTCrVzCb1iEiEcng8bQ7Pynvt"
			"pk9HvnkfhaE7bIqT2atixTQArDGLw8IDvax01Gx3yuK/b2Mhrq7G9VxWfGV2ZU9ExC9ZLoH+YzT6"
			"hguSl3yXv3SdfeHlO02qe1dqgFVmF+hOdLXgaFu5jkOf5orUWdaoahrXftF3/k58lu4QSxMmjjfd"
			"ufJ8nP/ntgYcXUuvgGXjeS2oal1qw8E2tJm7drYsIbEQfZsbSPA6rpV8u1HinOxmtPRerrGHg/B+"
			"LiLLya2ythvOBJqOo6P8atILFACa7iTmDJ1oxQBAcuNT2R9O1yq8e+95YwdHd0ePuqGK7lzKs//C"
			"b7yJnuEon+UzkNRrL7s4g7ZlLq7O4guyWdFJB5h6yZ2VCwURVO1kxNYz1OdC2+uzDAuPaQPuXstD"
			"AGSZVx+YeLgPoqs5lZR5RcsONdabkLp3w7ScttgSoPmx4MhVucu37oMH6i+ec3xl2KyZQDedGznT"
			"WCNSBniuXp6/a92Cc+ZOnr6L5k3Sg3TAhQ/izopo4vL0pErn9SNZcPetJuTo85T90U/Q6qdZ9bO/"
			"d+IAnTMlwOfU58csgs/bafbaV5px6b7ph/7Og178duJGzTRvQxW9K4mTiCUcQ20aAGhpsaUSMQ76"
			"qtdf+kDNqabzx7jwAfD6u7+eR7y3jWpKqecZGbX2y7R0X2IJaOHVjroA9EF1ONqMGpgYiQuqMBiJ"
			"KbuB3C8KpbhqWzdYvbqb80qUyXVZNzV3d3pBIfbCdKwNBwrkGG/yTK0LCdn+a8z/TCpz8pyYEQcg"
			"ef6sMBfNcJiqdXXbtwUhB5ZaKxKZEBYyeohDzQUAwGoranjGJq278jH4JgMbHlZiYElum8+OI7Rm"
			"pgMAYDVEixIDklnxdhe2loaqDkhLpniDPjH75WjW8XUrL4hLCxsnBX/VtrUsfZC7u/G6q7mItexa"
			"hoXHl3w6vFA6Ff7s1Dwx5G011rb7I50/NTB4vrVm9/AifXdBUnBm68bT6JzgrbNM5GUXj1xoctlw"
			"6Mjmqcilo+deaEYLe4jn+v0xv4ctGFH8a2BYai0OAAwmm8PRMRq7ePev6yZ0enuURuePdpv6wXvS"
			"xiE+H9toA4A0LyG1YYRpQcKVUg1e/tJA9HfSjeaW7BOHb1SimcnXKnGVvSuJYzAwFAMAOYbhdPpb"
			"Aqlp1eK8mOCjyMJNcy2YHDZTJpMqfpSg4iaRTJ0uVeFoE9jUzNDRoat0AymU4sqxteEX/H3hPu4w"
			"yczRgffkz5tPODZ2PAAAGcJ1m231KOGv55dTxG6zhshROeAyBB/qvWbVfN+Abz9i/XW7zUvd1KM8"
			"1NajNC6XLRWLFb5CJDJVu8+THaE6i1rks6J7HXRBMsVraCpSgsa09YvaLzgWe+ijIsHvWW0f1aHz"
			"p7ibPbz6MP1q1jAPVwMAwqkkzKtI1Nc1RmON9f9ZIBAIDoRpevYHsgsAUhwfvH5v8fgNwX4joVEk"
			"qRZWSLhGQ61GDDfRkVUIqzShBMk4uCIkuQ60zRw/+nKmcX52KQZAN7ab5ePjPXOqPbkt8+h65rb2"
			"Tp9+7VlxMiZbCkj+yajrI9ZERSxhnYlOKO/uZMoZaIBVCFsAAJBXwgZ9/oCmtOR8u8/mOjs6Tpi9"
			"aHpz6uWXGLF3JRhGZkZ1pcUogKykVDLIlKfO9xpS/Rq0NGHnT08+3P7dVD4d6Ea2o5EHd8swAMBf"
			"nf8u4GAWrqyrVYSqcChoeXI2sWL8lNGsBpVuIIFyXOkD7MbQUi6Jxk4yYfKcHCAlqWmU/esdsUF7"
			"ove4wqOhyWwPLwu6HADoPPPBaGmxCABvahBxtF5Pct3TQxyqQqShna38/q0iBADw6rQ7wlH2VqT/"
			"r6wyc+l8gkVrslnR3Q7IS+6/EApcJ0t1SgAAsPQMdFoaGtpN2Qau7kMf7v8129rDpeMdecWpJMwT"
			"8rStxnplwG8gtaLIHsceuSaU4MKQ+ckALOetFwMXfHAtOtDzIsAAp5Vz7DShhGXj7RMfErjq+lBD"
			"aXGx9rzto1h1l7pnimnl628dcPTCBMdrl80CBC4GA+WrfVM2/HLZ+YdZxt34jyrHeeHnScErV92y"
			"5FYUoZ4blsONkOJxC4NdHNkAgJkWxG1LLvz0tRcUvcfPi/Q17XDtzXP1nXw+OjDoPbyowXPzBDWf"
			"DUOfn4nUiOpWcOH58D1/N41q3L0+Beh6kwK2+a2y2Rq0MmuYoai40vTLHfYsnliFLlXhSGq9GJWj"
			"zKHe2wId2dVxVwhuWLR8ZHd+pTCH2Fkxr/EmDWEC3XCCvcHRQltrFoCs9a9sG58P6ClFXtONaBWt"
			"B8Yv8L3wn/Xf3dCtfGUbsKvVPXj1n93SQxwqyNIAAJijFgVO3B6yYoOFYUtp7ZClO1xJPaLztmES"
			"Lar0viY76KHk/oBygX8z2KbOJ6FjSrRlv6QZsViytcMdXp6Lh3XUHvYqJ8VSLFc6dYhJZ+bb5nl5"
			"xxrjQnrbLSAApq3f7pUuWhodu7ot5GNjYzMzM9+yxzzaXFnyUtiI9Gij+vDw8LS0tHYHpA3ClyVV"
			"IrRHVjXAoUOHjh8/3v4IKqoqKRI2yHpmFxNVlZRWi3tmRA05OTk7duwgdaq0vvxlWa2kU10aC0dY"
			"WNiDBw96bKYDsnphSVVz97QdPHgwJiam/RG1Q0WaKkvL6ySE4x2JjIxMTEwk1znBIomsyMjICA0N"
			"JWefpGRlwsPDb9++3cVG/1ZOnTp14MCB9keIBa7RyaiXzZOgoqJi7969Sge7f0+JoWNkrvlfF2w9"
			"Ews9jVvVBAxtvvmQHluha/PJ3czqXdj6gy302x9Qo6vvhgMAWPom5pqzpnaoTF0jM13N9aPKoqaz"
			"QvOS+wHEAtdo9vey+e5CvQlMQUFB0U+hFgAKCgqKfgq1AFBQUFD0U6gFgIKCgqKfQi0AFBQUFP0U"
			"tU8BIQiSn58vEnXx2yRdRCqVFhUVMRh97l31hoaGlpaWu3ff/umJvoVQKMQwrG9qRhCksLBQJpO9"
			"ayEKGhsbURTVoK9aWlrKy8t7z/klJSWaFUxEKpW+ePGCTu8XPwqrq6v/dQXeQ5qamhAEUTpIk6v6"
			"VhEA7N+/v7a2lsnU+LvHHZBKpXQ6ncXqc28rSqVSAOBwevhezj8KiqIYhvVNzVKplMFg9HY6kUfj"
			"8ZVKpTQajc3W6AcH2/EPBLfPFmNv8G8s8B6C4ziNRtuyZUv7g2oL0tjY2M3NzdbWtlc1RUREuLq6"
			"Tp48uVd76QaHDx/mcrlLlix510K6QG5u7rlz5zZv3vyuhahg586dXl5eDg4O71qIAoFAoK+vv2DB"
			"Ak0ZjIqKGjly5KxZszRlUInHjx/Hx8f3anAjIiLc3NxcXFx6r4u+w+nTp+vq6lasWPGuhfxzVFZW"
			"xsbGKh3sF5d7FBQUFBREqAWAgoKCop9CLQAUFBQU/RRqAaCgoKDop1ALAAUFBUU/heRjeYgw7Y+j"
			"cfeFuNG4Ocv83cwYtY9iD8XcLmcO9/wiwNtaM18Fxeszzx+JuflCrDvcbf4Xn4zl5ccERd+sB6Ax"
			"dc0mzFu22NmoJC48Tn914DRec3F66qXUOvsNX0xqv2miqCD52MnLT2qZ5s6fL5s/ng9KJg26teCJ"
			"bu37qcJri+9wJuDVVyIPSedv9DFnAFYaH76vbk6onw0LAJCck0F70xoAGFyj0Z5+/g6F+0MvFGEg"
			"lwONBsAcszB81dhnsQf++KuSYTHVf8Wc0a1eU2r3xaxhdQm7BPU+QYttuHh92sEfH9ttXPFB1zeG"
			"x4oUvtIuvXHsyMXMOoaFq9/yuTZ6dGUn0fH6DKIuQjgM6MruHZAfE3p9+NblE7v27COapxRXh9Lf"
			"wvMmbV1sw0Iyj22/MXzLalce+uxseJLxao/SiH0VM0LXTmuNHPr01Lafyz4MXe9uCI1Pkk6euV45"
			"5uugz17vC99mmaFlbDN76VL3oeSe8lMeqt5Lhe/0xc9Tfj9+KaeOa+k2f+knY0mGgTBCJ0m8wiAQ"
			"LKr2fhc7eFMaqiTjasqFohUVBT5bN0f1ZNQxtfDq1Ki91T7bWvfqQnJOhqSar/Ms3/1z+1ON7nVq"
			"XjExtcUVAEBrfMDOD56GnZQv3rZoDAdAdv/wzifOQUtsNPmcLqmERp/9sXXziaJBLhN0cn7btjux"
			"sjLph+/2Z+o62sGtiO/33ZNoRIroRlRYmtnSsMiwpZb3IiIuVaNNJYV6H0cLBPsivrHP//mH80Ks"
			"pTyvsApBcmMiBDfzskuX65EAAAuGSURBVP/Ormq/kw5ecyU8JFnfd/tP4d+8n70n9Hx5o5LJbirD"
			"ap7lljbLAQDkkoqC/HKxHADQF8kXHz5NjU8XAwCAvPFlgd7H0QJBdNBH6JmQ36vdN+0XCKLnDqx5"
			"z/8XgWDfGjdOxtE9aZYrd/3wP3qJu0/mK7Z7Vmr3WzZu7uaqlRD9R76s+f7hA/eHTnPq+uwPAPJW"
			"X2Gl/w3/3/oZmyN3+vESI47nSglOwmQqdRHDQWiJNJXkPFe1m93bwZXjWmXMfnUjvQwD5OnN1DuX"
			"rz2SAF7/6FYBnc9uKsnPuBh7WbHnueRh3Lm0nIJKBLCi2O0/3jPx3bTZdwSTaDnqe5eyg1EJVeS0"
			"EYaq8B1af3339jim9/c7t/sNe/RjUGwRyQ3PCCMUNpfnFVYhOE60qNr7Xe5AURoqOgA15ULRBrHA"
			"m5VTQk1q0Q1GGL1KuJKHAADIspMT6waN1GlWOpWM+VYhbb0IBAJB1DJHZtPLrNRDu0/kyQAAr3uR"
			"87JR9Wtb3Ybc1EIfMj1g88a1C+fPsNVDRM2ivEfZopGzli3x/3LG0Mr0v/I1IgURiVAOj6fN4Vl5"
			"r9306cg3PysZusOmOJm9KlZMA8AaszgsPNDLSqfjdqdNd648H+f/ua0BR9fSK2DZeF6LsklojNvg"
			"+/Wmb1cvm//ZqmPEXRu7JDcv+S5/6Tr7wst3mjr8gcV/38ZCXF2tPPlgldkFuhNdLTjaVq7j0Ke5"
			"yu9Yv2mn57LiK7MreyIifslyCfQf06N3i2hc+0Xf+TvxWbpDLE2YOE50EqpaFyEcxJY9zcXXca3k"
			"240S52Q3o6X3co09HIT3cxFZTm6Vtd1wJtB0HB3lV5NeoADQdCcxZ+hEKwYAkhufyv5wulbh3XvP"
			"G1XM8mw9Q30uqHnHkYC6zBPduZRn/4XfeBM9w1E+y2cgqdfIbnmpPEIhrs7ii06ygmwHmHrJasqF"
			"4i2gaicjpdRiWHhMG3D3Wh4CIMu8+sDEw30QXc2ppMwrQ+OM97D8MzKmQPkVXg1B6hYQ03LaYkuA"
			"5seCI1flLt+6m5snmdCu3fy/FCNOVhUuk2rm/f4BnquX5+9at+CcuZOn76J5k/QgHXDhg7izIpq4"
			"PD2p0nn9SBa85c1trLaqnmdkRFdIdl9iCSDraBIk2Rhu8UlwkIv07Lq1fxYssun25ZQ049J90w/9"
			"nQe9+O3EjZpp3oYAcvR5yv7oJ2j106z62d87Kd99kEvEEo6hNg0AtLTYUokYB306gMp2A6cE+Jz6"
			"/JhF8Hm7Hl6y0/ljXPgAeP3dX88j3ttGNaUoOwktvNpRF4A+EMOhU3ZTuaUsvbuqlOKqbd1g9epu"
			"zitRJtdl3dTc3ekFhdgL07E2HCiQY7zJM7UuJGT7rzH/M6nMyXNiRhyA5Pmzwlw0w2Gq1tVt3xaE"
			"HFj6erdsOZp1fN3KC+LSwsZJwV+R3EmTmHm5AABYbUUNz9ik9TMlDL7JwIaHlRhYkvtsSccRWjPT"
			"AQCwGqJFiYHqrOiiC1tLQ1UHpCVTvEGfOBmpSS36IHd343VXcxFr2bUMC48v+XR4oXQq/Nmp+baQ"
			"t20ASedPDQyeCwDaDl99But/OuP206BeGCjpmwuSgjNbN55G5wRvnWXCGvb5xm89uBn/l5LXhNH4"
			"gzQkjD3Ec/3+mN/DFowo/jUwLLUWBwAGk83h6BiNXbz713UT3n57lMZhM2UyqWK9RcVNIpmySQCg"
			"6RryuUDX0dXCZd1eVGkg+jvpRnNL9onDNyrRzORrlTgA0Oj80W5TP3hP2jjE52Mbwg5PNDqDgaEY"
			"AMgxDKfTmW/+QGgnzUtIbRhhWpBwhbhddDcQ58UEH0UWbpprwSQ6SZ0uJd/VsYju7T5KceXY2vAL"
			"/r5wH3eYZObowHvy580nHBs7HgCADOG6zbZ6lPDX88spYrdZQ+SoHHAZgg/1XrNqvm/Atx+x/rr9"
			"xkk0pq1f1H7BsdhDHxUJfs8iGWHlzFPY4nLZUrFYMWJEImNyueR/RKvKXBUWtdRlhaY66IJkitfQ"
			"VKSEutSi86e4mz28+jD9atYwD1cDAMKpJMy3XcXSWGP9fxYIBIIDYfMVv2poAz74JmBwYuS5si7f"
			"be0ccgsAUhwfvH5v8fgNwX4joVEkA53Rs75ev/ZTq5ZXuKPXh6aaUIJkHFwRklwH2maOH3050zg/"
			"uxQDoBvbzfLx8Z451b7zLfPoRrajkQd3yzAAwF+d/y7g4KP7Sia7KY0z0ACrELYAACCvhA36/AFN"
			"acn5dp/NdXZ0nDB70fTm1MsvMQCg65nb2jt9+rVnxckY4v0lhpGZUV1pMQogKymVDDLltfleuR2S"
			"fzLq+og1URFLWGeiE8p7uASgpQk7f3ry4fbvpvLpKpyUhSvrAgAV4SgfSGjZ/atS5bjSB9iNoaVc"
			"Eo2dZMLkOTlASlLTKHvT1h+uctCe6D2u8GhoMtvDy4IuBwA6z3wwWlosAsCbGkQcLeIkx9Iz0Glp"
			"aCBVMcTMU4g0tLOV379VhAAAXp12RzjK3or0BaPKzKXzCRat1WaFhjogL7n/QihwnSzVKQGgKrUM"
			"XN2HPtz/a7a1hwuvg13FqSTMv73G6QOnrPzS4OLJdKmG/wNA8haQ7HHskWtCCS4MmZ8MwHLeemnv"
			"rIxflgffN5r0WcS388w1coXJsvH2iQ8JXHV9qKG0uFh73vZRrLqu/deWZeu3ymZr0MqsYYai4krT"
			"L3eMt0drLrU3CdCtNYDjvPDzpOCVq25ZciuKUM8Ny+FGSPG4hcEujmwAwEwL4rYlF35qpzibaeXr"
			"bx1wNH5epK9pB8/wXH0nn48ODHoPL2rw3DxB+RbR63ZzVkkiL5sFCFwMBspX+6Zs+OWy8w+zSN7M"
			"IIILz4fv+btpVOPu9SlA15sUsE3JSfYsnliFLmI4tIdZKLcsTXl9wQoM89mbg3y6vUcvc4idFfMa"
			"b9IQJtANJ9gbHC20tWYBKC4y2DY+H9BTirymG9EqWg+MX+B74T/rv7uhW/nKNmCXsfIVtFzSjFgs"
			"2Uq4DacS4lBBlgYAwBy1KHDi9pAVGywMW0prhyzd4drTx91UWFTpfU120EPJ/QHlAv9msE2dT0LH"
			"lKhLUp9aPBcP66g97FVOiqVYOQuHmHRmvm2VbrsFBMC09dvppDhM509fsyz1/l7Nj13dFvKxsbGZ"
			"mZm9u029XB4eHp6WltbugLRB+LKkSoR236S0vvxlWa2khyYPHTp0/Pjx9kdQUVVJkbBB1n1lcrlc"
			"jomqSkqrxT0zooacnJwdO3aQOpXgJDW6iL4jtCRHWFjYgwcPutrq7cjqhSVVzd1LlYMHD8bExLQ/"
			"ojZNkKbK0vK6zkYcGRmZmJhIrnOCRRJZkZGRERoaSs4+ScnKhIeH3759u4uN/q2cOnXqwIED7Y8Q"
			"C7znk9E/Z54EFRUVe/fuVTrYVz7P+xq2nomFXs8s6A+20NesSQAAYGjzzYf02Apdm9/5zazeh+Ak"
			"NbqIviO0fHew9E26fcVBRG2aMHWNzHQ1148qi5rOCs1L7gcQC1xDM8c/Yr67UG8CU1BQUPRTqAWA"
			"goKCop9CLQAUFBQU/RRqAaCgoKDop1ALAAUFBUU/Re1TQDiO19TUlJSU9Gr3OI7X19f3di/dQCaT"
			"AUAfFPYWampq5HJ539Qsl8vr6ur6jjaZTCYWizWoB0XRpqam3htgbW1tbwcXx/GGhoa+E6NeRSwW"
			"IwjSTwbbSl1dHY4rvxpJk6v5Ytbt27czMjLY7B59iaxTGhsb2Ww2l9vnvlHb2NhIo9F4PF7np/YZ"
			"Wic1ff0+8pRmBxoaGrS0tHo7ncjT1NQEABqMb2NjI5PJ1NburWd8pVKpRCLp1eD22WLsDZqbm+Vy"
			"+b+rwHsIhmGDBw/++OOP2x9UuwBQUFBQUPz/DfU/AAoKCop+CrUAUFBQUPRTqAWAgoKCop9CLQAU"
			"FBQU/ZT/ByQkuFhSZVHcAAAAAElFTkSuQmCCAEEAAABRAAAAQgAAADYAAABKAAAAcAAAAFoAAABE"
			"AAAAZAAAAEMAAABkAAAATgAAADQAAAAxAAAAUgAAAFoAAAB4AAAAdQAAAGIAAABXAAAAeAAAAHIA"
			"AAByAAAAVAAAAG8AAAAwAAAANgAAAGkAAAByAAAAcgAAAE4AAABDAAAAOQAAAHkAAABiAAAAMwAA"
			"AEEAAAA4AAAAZwAAAEYAAAAzAAAAbwAAADMAAABzAAAAcAAAACsAAABSAAAAeAAAAFUAAAB0AAAA"
			"WAAAAHAAAABaAAAAawAAADMAAABwAAAARQAAAGYAAABTAAAAYgAAAHcAAABEAAAAUQAAADAAAABO"
			"AAAARAAAAFQAAAA5AAAARgAAAEgAAABvAAAARAAAAG8AAABLAAAARwAAAGgAAABvAAAAZQAAAG0A"
			"AABuAAAAMAAAAEIAAABzAAAAQQAAAEQAAABRAAAAMAAAAE4AAABUAAAAVAAAACsAAABGAAAAMwAA"
			"AGcAAABCAAAAbwAAAGEAAABHAAAAaAAAAG8AAAArAAAAaQAAAG4AAAAwAAAAQgAAAGsAAABCAAAA"
			"RAAAAFEAAAAwAAAAUAAAAFQAAABUAAAAOQAAAEgAAAA0AAAASwAAAHkAAABBAAAATQAAAHcAAAA0"
			"AAAAcQAAAEsAAABpAAAAdAAAAHAAAABhAAAASgAAAC8AAABjAAAAZQAAAEcAAABJAAAAYQAAAFYA"
			"AABsAAAAcAAAAFoAAABtAAAAWgAAAEcAAABUAAAAMAAAAHEAAABoAAAAWQAAAHQAAABhAAAARwAA"
			"ADUAAAB1AAAAbAAAAHMAAAB2AAAAbAAAAGYAAABkAAAAQwAAAHcAAABOAAAAMQAAAEIAAABXAAAA"
			"VgAAAG8AAABiAAAAagAAAGUAAABOAAAAKwAAADAAAABHAAAAYwAAAGYAAAB4AAAANAAAAHUAAABM"
			"AAAAaQAAAHQAAAAyAAAAMwAAAEYAAABhAAAAeQAAAFEAAABTAAAAaQAAAFUAAABLAAAAaAAAADAA"
			"AABLAAAARwAAAHYAAABaAAAARAAAAEoAAABaAAAAZAAAAFgAAABWAAAAMQAAADcAAAB6AAAAbAAA"
			"AGYAAABMAAAAQgAAAGIAAAAzAAAAZAAAAG4AAABEAAAANwAAAGIAAABEAAAASAAAADIAAABCAAAA"
			"ZwAAADAAAABOAAAARAAAAFMAAAAwAAAAdAAAAEwAAABmAAAAMQAAAGsAAABzAAAAbQAAADAAAAAw"
			"AAAATgAAAFQAAABWAAAAaAAAAG0AAABPAAAAcAAAAG4AAABoAAAAQgAAAGcAAABLAAAAaAAAAGYA"
			"AAByAAAAUAAAAEUAAAB1AAAAMwAAAGMAAA=="
		)
	)
	(label "QSPI_SS"
		(at 35.56 71.12 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "0215c7ed-eb18-40e9-926e-98cd38d42697")
	)
	(label "QSPI_SD2"
		(at 85.09 182.88 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "058c46df-ac2e-4348-9d98-7c3d0f253349")
	)
	(label "QSPI_SD1"
		(at 97.79 177.8 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "14e791ce-4eac-4c24-9c0a-bcc0da537cf1")
	)
	(label "RUN"
		(at 30.48 55.88 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "15ffcf62-c497-47b6-a657-0ba0a9d6b860")
	)
	(label "RUN"
		(at 76.2 53.34 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "1e0dca0f-41f4-4658-9058-537105bf6889")
	)
	(label "DVDD"
		(at 106.68 33.02 270)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "32a78f9c-d53b-4f16-8bbd-41eaff33db13")
	)
	(label "DVDD"
		(at 149.86 165.1 270)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "38d02db3-3ac4-47c7-a558-aa0f5b499ce0")
	)
	(label "IOVDD"
		(at 73.66 38.1 270)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "3a4580ba-71f0-4f2b-a339-0c40ede42695")
	)
	(label "QSPI_CLK"
		(at 27.94 182.88 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "3acfce62-48d6-428f-a56a-c1139b8dbda9")
	)
	(label "IOVDD"
		(at 119.38 33.02 270)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "44bdf7b5-88f0-46c3-9665-bbef97d1905c")
	)
	(label "QSPI_SD1"
		(at 73.66 78.74 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "4d218345-efd1-43db-b849-9d12f0ba1bd7")
	)
	(label "QSPI_SD0"
		(at 73.66 76.2 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "502bc413-b251-4960-9200-c80ce1cd2bc2")
	)
	(label "IOVDD"
		(at 38.1 22.86 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "50f68f14-595d-421c-97a9-b4e7a05a4dd9")
	)
	(label "QSPI_SD0"
		(at 97.79 175.26 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "5712ee64-d643-4c8e-b0e2-78425fb9075b")
	)
	(label "QSPI_SD3"
		(at 73.66 83.82 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "61f4ff13-414b-443d-8bad-0dcb857cb1ad")
	)
	(label "QSPI_SD2"
		(at 73.66 81.28 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "62fe5e8a-b92f-4038-84a6-f15da5ed2895")
	)
	(label "IOVDD"
		(at 106.68 157.48 270)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "6ea99c09-7f9d-4e2e-9a22-63769b85f75d")
	)
	(label "QSPI_SS"
		(at 43.18 30.48 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "800a0382-24d1-49ea-886e-bbf7b7809e3d")
	)
	(label "QSPI_SD1"
		(at 35.56 78.74 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "8cbfc27a-9310-47d0-b75b-6385926db826")
	)
	(label "QSPI_CLK"
		(at 73.66 86.36 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "9fc7d3e6-339a-4b59-b11c-cbabea10abc7")
	)
	(label "QSPI_SD0"
		(at 35.56 76.2 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "b2d8f329-298d-403a-a4d5-a9679c59fc05")
	)
	(label "IOVDD"
		(at 71.12 154.94 270)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "ba08679c-a9ca-41f4-9481-9cfce087843c")
	)
	(label "QSPI_SS"
		(at 27.94 177.8 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "c5197c05-6bd5-4f79-b1f1-35e0a2325b4f")
	)
	(label "QSPI_SS"
		(at 73.66 71.12 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "c5b122a3-7772-4d2a-b8da-9f9fab5662be")
	)
	(label "IOVDD"
		(at 111.76 33.02 270)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "cdce684f-a88a-4ee0-9109-f18234598d4c")
	)
	(label "IOVDD"
		(at 25.4 157.48 270)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "cf805580-fcbc-4a5b-a161-6f2efe6b917b")
	)
	(label "QSPI_CLK"
		(at 35.56 73.66 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "d471789c-85a8-4afb-8a77-82c83ae86210")
	)
	(label "IOVDD"
		(at 50.8 154.94 270)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "dca0d4a9-7092-4cd0-a55f-08913dc463de")
	)
	(label "QSPI_SD3"
		(at 85.09 185.42 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "e6966dd5-558a-481c-b43b-efa4aac43c3f")
	)
	(hierarchical_label "GPIO26{slash}AD0"
		(shape bidirectional)
		(at 137.16 121.92 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "00c5f34b-3d96-481e-8852-bd44fd1b4539")
	)
	(hierarchical_label "GPIO0"
		(shape bidirectional)
		(at 137.16 53.34 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "064b7b3d-b60d-4f77-a4e2-4e608e9d16fd")
	)
	(hierarchical_label "GPIO4"
		(shape bidirectional)
		(at 137.16 63.5 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "1125af30-b42b-4711-aa36-72c5891d1502")
	)
	(hierarchical_label "GPIO25"
		(shape bidirectional)
		(at 137.16 116.84 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "12dfd674-89c4-482f-969c-5ec27aa43c56")
	)
	(hierarchical_label "BOOT_MODE"
		(shape input)
		(at 33.02 30.48 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "31fa2a6a-0d2a-412b-b9af-cf277aca382c")
	)
	(hierarchical_label "GPIO15"
		(shape bidirectional)
		(at 137.16 91.44 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "3359496e-a510-4400-9342-fef91be714a0")
	)
	(hierarchical_label "ADC_AVDD"
		(shape passive)
		(at 116.84 40.64 90)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "37b69aa0-7421-4bc1-b89b-f7afb3ac3401")
	)
	(hierarchical_label "GPIO10"
		(shape bidirectional)
		(at 137.16 78.74 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "39e74e6a-bccd-4404-8d7c-dae1063439f4")
	)
	(hierarchical_label "GPIO13"
		(shape bidirectional)
		(at 137.16 86.36 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "3c86a8d8-0aa0-4165-ac9f-b43495b1b78b")
	)
	(hierarchical_label "GPIO14"
		(shape bidirectional)
		(at 137.16 88.9 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "56fbdcd8-2e2e-429a-b7a5-56eca48dbaf8")
	)
	(hierarchical_label "GPIO18"
		(shape bidirectional)
		(at 137.16 99.06 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "575cde38-d126-4905-a297-04a59aba1247")
	)
	(hierarchical_label "GPIO24"
		(shape bidirectional)
		(at 137.16 114.3 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "5777ac5a-9938-4bc1-803e-6150dd9cf626")
	)
	(hierarchical_label "SWDIO"
		(shape bidirectional)
		(at 81.28 121.92 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "6414f0f0-e650-4d5f-b03d-e8f5ea596d1a")
	)
	(hierarchical_label "USB_D+"
		(shape bidirectional)
		(at 81.28 60.96 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "67f41501-d3aa-4541-9d42-647fd9b92cd3")
	)
	(hierarchical_label "GPIO16"
		(shape bidirectional)
		(at 137.16 93.98 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "73cf3cf8-463c-4a20-903d-1201e77ea605")
	)
	(hierarchical_label "SWCLK"
		(shape input)
		(at 81.28 119.38 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "77cb3fe5-f096-40cc-9f02-c37bafe90b63")
	)
	(hierarchical_label "GPIO27{slash}AD1"
		(shape bidirectional)
		(at 137.16 124.46 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "7a62a720-b29a-4002-968c-2d0ea51f8e57")
	)
	(hierarchical_label "GPIO5"
		(shape bidirectional)
		(at 137.16 66.04 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "8701e260-8e21-4e30-bd41-5fd4af8dad70")
	)
	(hierarchical_label "GPIO28{slash}AD2"
		(shape bidirectional)
		(at 137.16 127 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "8e74e378-fca3-4411-907e-99749e6e9e5d")
	)
	(hierarchical_label "USB_D-"
		(shape bidirectional)
		(at 81.28 63.5 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "a103c2e3-17bd-4563-8110-1b99f0173149")
	)
	(hierarchical_label "USB_VDD"
		(shape passive)
		(at 114.3 40.64 90)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "a3322a88-8f8a-4973-ae32-de18998d7ced")
	)
	(hierarchical_label "GPIO6"
		(shape bidirectional)
		(at 137.16 68.58 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "af6ce6ed-bb41-4bfc-8670-a938ce8a98a6")
	)
	(hierarchical_label "GPIO22"
		(shape bidirectional)
		(at 137.16 109.22 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "b451aad8-13bf-4ae9-be7f-e456a0d646a8")
	)
	(hierarchical_label "GPIO2"
		(shape bidirectional)
		(at 137.16 58.42 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "c3f52e6f-56a9-4b58-9ad9-0cd3124c774c")
	)
	(hierarchical_label "GPIO21"
		(shape bidirectional)
		(at 137.16 106.68 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "c7284906-a1e5-409f-bf74-6da32068d945")
	)
	(hierarchical_label "GPIO29{slash}AD3"
		(shape bidirectional)
		(at 137.16 129.54 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "cb5aa8af-ab63-41a0-8bee-0f44223109c5")
	)
	(hierarchical_label "GPIO20"
		(shape bidirectional)
		(at 137.16 104.14 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "d2bb604b-3a5b-4ac0-b3bf-965e6af54baa")
	)
	(hierarchical_label "GPIO19"
		(shape bidirectional)
		(at 137.16 101.6 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "e02c69ea-1284-4d80-9bad-83fa29fe90a3")
	)
	(hierarchical_label "IOVDD"
		(shape passive)
		(at 30.48 22.86 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "e82f1c13-0d95-45ed-8fc0-ff55b87f396e")
	)
	(hierarchical_label "GPIO23"
		(shape bidirectional)
		(at 137.16 111.76 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "e876c73b-9796-491d-9c9a-1d740090c35d")
	)
	(hierarchical_label "GPIO8"
		(shape bidirectional)
		(at 137.16 73.66 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "ef3ef814-496f-45e2-ad31-f047fabc984b")
	)
	(hierarchical_label "GPIO17"
		(shape bidirectional)
		(at 137.16 96.52 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "f6a10090-0261-445a-b208-f8ae351c5352")
	)
	(hierarchical_label "RUN"
		(shape input)
		(at 71.12 53.34 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "f8d45529-9fde-4dee-a822-e277314d16af")
	)
	(hierarchical_label "GPIO11"
		(shape bidirectional)
		(at 137.16 81.28 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "f9c57f1a-86b5-4356-8695-6ec879cf4747")
	)
	(hierarchical_label "GPIO9"
		(shape bidirectional)
		(at 137.16 76.2 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "fa339a26-e92d-4ebe-9db1-c3a90221cc00")
	)
	(hierarchical_label "GPIO1"
		(shape bidirectional)
		(at 137.16 55.88 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "fa67d37a-3625-4b40-add8-2ca242cd44ab")
	)
	(hierarchical_label "GPIO7"
		(shape bidirectional)
		(at 137.16 71.12 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "fab674c6-9164-4bd8-b7d6-8ff85b161f31")
	)
	(hierarchical_label "GPIO12"
		(shape bidirectional)
		(at 137.16 83.82 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "fe3d0f7e-7168-4475-b5ef-014a189113ab")
	)
	(hierarchical_label "GPIO3"
		(shape bidirectional)
		(at 137.16 60.96 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "fff5b499-5f3b-447a-867e-3bb52b79d98f")
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 149.86 177.8 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "005cc7e9-f8dc-4003-99e9-311e17118936")
		(property "Reference" "#PWR0108"
			(at 149.86 184.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 149.86 181.61 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 149.86 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 149.86 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 149.86 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "0752466e-08e8-4385-a1d3-d709311fdf0e")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR0108")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 60.96 167.64 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "056e3693-c265-473a-898f-055b791e3dbf")
		(property "Reference" "#PWR096"
			(at 60.96 173.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 60.96 171.45 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 60.96 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 60.96 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 60.96 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "080b16cc-0bf4-4f60-a29c-77d1e8280c11")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR096")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:C_Small")
		(at 55.88 109.22 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "05f34c72-269c-44d8-8f8c-a5ea83bf1837")
		(property "Reference" "C30"
			(at 55.8736 112.5022 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "6pF"
			(at 55.8736 114.9264 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 55.88 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 55.88 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 55.88 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "CC0402BRNPO9BN6R0"
			(at 55.88 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "6pF 10% 50V XR 0402"
			(at 55.88 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "95250c34-bf05-431a-bbb0-5bb97106c633")
		)
		(pin "2"
			(uuid "86273907-ed0c-4750-b060-a7e9e84019db")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "C30")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 109.22 170.18 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "0a4ab5aa-15eb-469d-bce1-8f8796cbd553")
		(property "Reference" "#PWR0101"
			(at 109.22 176.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 109.22 173.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 109.22 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 109.22 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 109.22 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "31530e52-dffe-45b6-996f-0ca05f38b59f")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR0101")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:R_Small")
		(at 80.01 167.64 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "0d827006-bd96-4aae-b209-e5199bb2962e")
		(property "Reference" "R39"
			(at 78.105 169.545 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 81.915 169.545 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 80.01 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 80.01 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 80.01 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "JLC" ""
			(at 80.01 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Part number" ""
			(at 80.01 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "RC0402FR-0710KL"
			(at 80.01 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "10k 0402"
			(at 80.01 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "WR04X1002FTL"
			(at 80.01 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "6e5cc66f-8311-471b-952a-06901db9af57")
		)
		(pin "2"
			(uuid "ee125079-260e-4ede-988d-47d1adfd4175")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "R39")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:C_Small")
		(at 119.38 177.8 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "2e8733fc-c126-4d2d-ab01-c5813ba2fb04")
		(property "Reference" "C36"
			(at 121.7041 176.5942 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "100nF"
			(at 121.7041 179.0184 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 119.38 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 119.38 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 119.38 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "CL05A104KA5NNNC"
			(at 119.38 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "100n 10% 25V XR 0402"
			(at 119.38 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "GRM155R71E104KE14J"
			(at 119.38 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "e8a4afc4-87ec-4241-94ac-8ba9cb21fe40")
		)
		(pin "2"
			(uuid "d9218ee5-a9f0-4755-ab7f-3a9f35570ff5")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "C36")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:RP2040")
		(at 109.22 91.44 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "38c7dd1b-7a2f-4363-91fe-c5ee029f0aa9")
		(property "Reference" "U6"
			(at 111.4141 135.0701 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "RP2040"
			(at 111.4141 137.4943 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Package_DFN_QFN:QFN-56-1EP_7x7mm_P0.4mm_EP3.2x3.2mm"
			(at 109.22 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://datasheets.raspberrypi.com/rp2040/rp2040-datasheet.pdf"
			(at 109.22 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 109.22 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "DigikeyPN" "2648-SC0914(13)CT-ND"
			(at 109.22 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "SC0914(13)"
			(at 109.22 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "8f08ecb4-3046-41ab-b8f4-b10d75b402ae")
		)
		(pin "10"
			(uuid "f0a99aec-14b3-49b0-8196-5b28958b4ae5")
		)
		(pin "11"
			(uuid "9e710d10-f97d-4808-a420-f977300d6734")
		)
		(pin "12"
			(uuid "8dc99add-b307-4c14-8a94-abf744005bac")
		)
		(pin "13"
			(uuid "d753749b-a258-460b-bcc6-f2820f6b0f8f")
		)
		(pin "14"
			(uuid "077449a9-e128-4e95-8f9c-4830085394c7")
		)
		(pin "15"
			(uuid "e844433b-7eb4-4925-a979-fbeee16b41ae")
		)
		(pin "16"
			(uuid "5cc095c9-49b4-4237-a9a8-a94be95dcbdc")
		)
		(pin "17"
			(uuid "da9b5e4e-cf2b-420d-b281-f7ec102abb4a")
		)
		(pin "18"
			(uuid "814eb2d6-7dbe-453d-a09e-13dc70ef8dcd")
		)
		(pin "19"
			(uuid "eef7bd26-9e1f-42b3-b6dd-cc8d28b4d7d2")
		)
		(pin "2"
			(uuid "1cf58186-e9f4-407f-aa54-b4b9a17930d6")
		)
		(pin "20"
			(uuid "73611b87-d9c9-4ae6-ba22-7fd449b8185b")
		)
		(pin "21"
			(uuid "dbfbb12a-7610-46e8-8822-7690b666305a")
		)
		(pin "22"
			(uuid "c795e00a-b781-44f2-ad12-a17712189fe5")
		)
		(pin "23"
			(uuid "8ff7a475-b670-4e93-8824-1febee6b922a")
		)
		(pin "24"
			(uuid "61704929-c343-4793-b724-7ceabfbb7c87")
		)
		(pin "25"
			(uuid "8136e843-3f1d-4ef0-9b63-28335f3b1d63")
		)
		(pin "26"
			(uuid "d8f221f1-a56c-4af1-a69d-f41484998c88")
		)
		(pin "27"
			(uuid "cea13af5-16ce-45e0-b39e-505825cbd5b9")
		)
		(pin "28"
			(uuid "c36e59da-5f8a-469c-acc0-66879d2b795c")
		)
		(pin "29"
			(uuid "276e5b08-026e-474e-a067-97a344abd582")
		)
		(pin "3"
			(uuid "e35f376f-a83a-45a1-921b-9a1d302a1b63")
		)
		(pin "30"
			(uuid "2e98b072-9ea4-45db-82cd-ba81ea64b225")
		)
		(pin "31"
			(uuid "c84f828b-f9f7-439e-9583-d4d8dd7d9e86")
		)
		(pin "32"
			(uuid "d98e763e-13a2-4c8f-88c5-a3d38e456071")
		)
		(pin "33"
			(uuid "3635b9d4-8e48-4c81-bc1f-6726a7ec84a2")
		)
		(pin "34"
			(uuid "1b5e9ecb-51b9-49bd-9c59-9125d083e08c")
		)
		(pin "35"
			(uuid "72ab86ea-d382-4ec2-936e-49820f613014")
		)
		(pin "36"
			(uuid "99bd1198-f7ba-4940-b644-6a41cc998389")
		)
		(pin "37"
			(uuid "20636924-bb7f-4435-b76a-de041ddc189e")
		)
		(pin "38"
			(uuid "eeaa175a-9c73-42e3-bb7e-3e75425ea51f")
		)
		(pin "39"
			(uuid "611af30a-9495-4960-aaa6-fb773bdfe1c9")
		)
		(pin "4"
			(uuid "2f393f1c-2c64-4c6e-ac3a-04e50adc9647")
		)
		(pin "40"
			(uuid "6eb3b7f3-c84d-49ad-957d-612fd7607d1a")
		)
		(pin "41"
			(uuid "a0c00cec-9345-469d-b47f-5e70e2fb6cc5")
		)
		(pin "42"
			(uuid "d4526aac-fbbf-4433-9646-10d695d72431")
		)
		(pin "43"
			(uuid "1b8bfdbf-f7e2-447f-a945-6078bfe7b9e6")
		)
		(pin "44"
			(uuid "9f86e448-996d-4d2c-b178-47aa90aa73a7")
		)
		(pin "45"
			(uuid "ab40530a-ac67-4b5e-a324-7c97b619bc46")
		)
		(pin "46"
			(uuid "08665f4c-8aec-48e0-87a3-0719acd8b0f8")
		)
		(pin "47"
			(uuid "b4172f15-b49c-4eed-9176-bfb277c16231")
		)
		(pin "48"
			(uuid "5f76b487-cf5b-409e-8833-8720914a3c57")
		)
		(pin "49"
			(uuid "ef9a7c2e-6868-48e6-b170-90d4e9fc58d7")
		)
		(pin "5"
			(uuid "5eb31a48-01ce-4e83-bf9d-1f9fe6a7bcd5")
		)
		(pin "50"
			(uuid "1087a876-3786-4e3b-b17d-089631999ccf")
		)
		(pin "51"
			(uuid "c8a0bb5e-cb98-417d-be5f-783cdf665890")
		)
		(pin "52"
			(uuid "c6388618-0706-4bc3-839a-7e36fbb5cbd6")
		)
		(pin "53"
			(uuid "72e45bc0-1aa1-428b-95fe-0c0c5a109ae8")
		)
		(pin "54"
			(uuid "52bff24c-bd6f-46ee-883c-64dc30f7734a")
		)
		(pin "55"
			(uuid "0b13fcc6-6116-4d2c-8183-71661f064089")
		)
		(pin "56"
			(uuid "59296621-30f3-4c80-a775-3da29f6e919f")
		)
		(pin "57"
			(uuid "7fcad90f-bf57-4f51-b216-3af5484e7aca")
		)
		(pin "6"
			(uuid "edc8fda1-9dad-409c-a274-61d1e8b797c8")
		)
		(pin "7"
			(uuid "81620dd9-954e-43c5-a760-00fbfe9e32de")
		)
		(pin "8"
			(uuid "347f9935-897a-435a-8ff1-1bd3f6a649f3")
		)
		(pin "9"
			(uuid "6804e9e0-eff3-4b7b-85d5-6cc0cdc6a4d6")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "U6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:C_Small")
		(at 119.38 167.64 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "3d439697-fd37-4c72-b42b-77b6ae548a0a")
		(property "Reference" "C35"
			(at 121.7041 166.4342 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "100nF"
			(at 121.7041 168.8584 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 119.38 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 119.38 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 119.38 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "CL05A104KA5NNNC"
			(at 119.38 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "100n 10% 25V XR 0402"
			(at 119.38 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "GRM155R71E104KE14J"
			(at 119.38 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "229049fa-9ff5-4df3-bcb2-693f02628d89")
		)
		(pin "2"
			(uuid "d9dae6cc-001e-4447-a30b-5705bbd4933d")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "C35")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 27.94 60.96 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "3e31bf03-62a0-4819-bb36-17d6b2155cf3")
		(property "Reference" "#PWR090"
			(at 27.94 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 27.94 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 27.94 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 27.94 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 27.94 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "09eee364-8787-4590-b0fb-65fb950d6a65")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR090")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 157.48 177.8 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "46af2cd9-5291-4724-93d6-1da064259a75")
		(property "Reference" "#PWR0109"
			(at 157.48 184.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 157.48 181.61 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 157.48 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 157.48 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 157.48 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "72ad6d4b-16b2-457c-8a37-77311f3d288d")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR0109")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:C_Small")
		(at 129.54 177.8 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "4fce6e23-81a9-465e-a66f-3618163317b7")
		(property "Reference" "C39"
			(at 131.8641 176.5942 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "100nF"
			(at 131.8641 179.0184 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 129.54 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 129.54 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 129.54 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "CL05A104KA5NNNC"
			(at 129.54 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "100n 10% 25V XR 0402"
			(at 129.54 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "GRM155R71E104KE14J"
			(at 129.54 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "0ae72788-5694-4da6-8d90-1f946b54cc1f")
		)
		(pin "2"
			(uuid "299bd9aa-5af8-4441-a37c-1f9208402095")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "C39")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 129.54 43.18 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "501313dd-a09d-4dca-9932-58c077bd1511")
		(property "Reference" "#PWR0105"
			(at 129.54 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 129.54 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 129.54 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 129.54 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 129.54 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "df4d309d-e5a8-4fd7-a16e-079655dbf728")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR0105")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:C_Small")
		(at 157.48 175.26 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "57030cb7-87b8-42da-8466-564c0bac5930")
		(property "Reference" "C41"
			(at 159.8041 174.0542 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "100nF"
			(at 159.8041 176.4784 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 157.48 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 157.48 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 157.48 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "CL05A104KA5NNNC"
			(at 157.48 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "100n 10% 25V XR 0402"
			(at 157.48 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "GRM155R71E104KE14J"
			(at 157.48 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "d4020424-5ad0-400f-b092-52f69bcc8328")
		)
		(pin "2"
			(uuid "eb090285-ad0b-4874-af26-d28bf6817dd9")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "C41")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:R_Small")
		(at 73.66 109.22 90)
		(mirror x)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "663dfb03-0f58-40bf-89e9-731ba6a27707")
		(property "Reference" "R37"
			(at 75.565 111.125 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "1k"
			(at 75.565 107.315 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 73.66 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 73.66 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 73.66 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Part number" ""
			(at 73.66 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "CR0402-FX-1001GLF"
			(at 73.66 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "WR04X1001FTL"
			(at 73.66 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "1k 0402"
			(at 73.66 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "e7fef33b-ea9f-48b8-9992-600b7766fb87")
		)
		(pin "2"
			(uuid "0763195b-9f46-4cba-a444-276c5f4e46c1")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "R37")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:R_Small")
		(at 25.4 170.18 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "67635d0b-1b2e-4f76-9758-e92055eea9fe")
		(property "Reference" "R34"
			(at 23.495 172.085 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 27.305 172.085 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 25.4 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 25.4 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 25.4 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "JLC" ""
			(at 25.4 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Part number" ""
			(at 25.4 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "RC0402FR-0710KL"
			(at 25.4 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "10k 0402"
			(at 25.4 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "WR04X1002FTL"
			(at 25.4 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "13b527f8-6630-4d3a-90c0-a97943a1c62a")
		)
		(pin "2"
			(uuid "8bb0ea09-98b5-4149-9f5b-17611e017c17")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "R34")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 50.8 109.22 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "67ab1968-db39-474f-9291-bfa820b0c44b")
		(property "Reference" "#PWR093"
			(at 50.8 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 50.8 113.03 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 50.8 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 50.8 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 50.8 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "951dbf44-df68-4adf-975f-c09dc99b5abf")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR093")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:Conn_01x06")
		(at 20.32 73.66 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp yes)
		(uuid "68e7be2f-66fa-4dfe-a842-b9c583c91db1")
		(property "Reference" "J11"
			(at 17.78 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x06"
			(at 20.32 83.82 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Footprint" "Connector_PinHeader_2.54mm:PinHeader_1x06_P2.54mm_Vertical_SMD_Pin1Right"
			(at 20.32 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://www.we-online.com/components/products/datasheet/61300611121.pdf"
			(at 20.32 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 20.32 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Part number" ""
			(at 20.32 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "61300611121"
			(at 20.32 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Status" ""
			(at 20.32 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "9af32446-4ef8-4e19-9905-fc726d5d3efb")
		)
		(pin "2"
			(uuid "e179e261-ab67-4765-91b7-03f9bac5ef9d")
		)
		(pin "3"
			(uuid "addd8055-e26c-479d-9b67-3037b897ec4e")
		)
		(pin "4"
			(uuid "36b693b5-f0a6-4ced-be34-a73fcc6a5c37")
		)
		(pin "5"
			(uuid "112c9fa1-1b1e-445e-ae8c-a8997994e65d")
		)
		(pin "6"
			(uuid "fdb6cfed-dd88-4850-a6da-8c715d417e13")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "J11")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 119.38 180.34 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "6bf54dd6-142c-4133-bed6-707dd8fe0481")
		(property "Reference" "#PWR0104"
			(at 119.38 186.69 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 119.38 184.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 119.38 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 119.38 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 119.38 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b6f48209-026d-45da-a64b-f58a6e8b2cea")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR0104")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 53.34 104.14 270)
		(mirror x)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "7600d888-9002-46fb-880a-95107adc3a5f")
		(property "Reference" "#PWR095"
			(at 46.99 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 49.53 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 53.34 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 53.34 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 53.34 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "5d7d47c2-5f39-43f1-a7c7-4bc57e90abf5")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR095")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:R_Small")
		(at 73.66 48.26 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "7782ae43-c4b5-4721-9f63-0bd67fe4e151")
		(property "Reference" "R36"
			(at 75.1586 47.236 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "10k"
			(at 75.1586 49.284 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 73.66 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 73.66 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 73.66 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "RC0402FR-0710KL"
			(at 73.66 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "10k 0402"
			(at 73.66 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "WR04X1002FTL"
			(at 73.66 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "bcae042e-c417-4917-bb25-c9b2c53e9900")
		)
		(pin "2"
			(uuid "c09581bd-bac4-44ff-8ac5-5ab526d4120c")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "R36")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:C_Small")
		(at 129.54 167.64 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "78e83f15-f032-4352-9de9-114ff30c70fb")
		(property "Reference" "C38"
			(at 131.8641 166.4342 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "100nF"
			(at 131.8641 168.8584 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 129.54 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 129.54 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 129.54 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "CL05A104KA5NNNC"
			(at 129.54 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "100n 10% 25V XR 0402"
			(at 129.54 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "GRM155R71E104KE14J"
			(at 129.54 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "5f8cacfc-94a4-4dd2-bd17-e724c429742f")
		)
		(pin "2"
			(uuid "787ab1fc-f119-4b2c-a054-003ced50fc11")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "C38")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 91.44 43.18 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "7cee0583-df2a-4804-8e2a-c84617313f89")
		(property "Reference" "#PWR099"
			(at 91.44 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 91.44 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 91.44 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 91.44 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 91.44 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "bba0a188-5501-4ad8-8367-5426ef498c2d")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR099")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:C_Small")
		(at 109.22 167.64 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "7ee2a9d7-1b8e-43df-be25-261554f25402")
		(property "Reference" "C33"
			(at 111.5441 166.4342 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "100nF"
			(at 111.5441 168.8584 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 109.22 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 109.22 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 109.22 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "CL05A104KA5NNNC"
			(at 109.22 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "100n 10% 25V XR 0402"
			(at 109.22 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "GRM155R71E104KE14J"
			(at 109.22 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b62b5bcb-e54a-4834-b90e-55a38666a957")
		)
		(pin "2"
			(uuid "2c6eb918-314a-465e-b5f1-3f7b3b0dd141")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "C33")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:Crystal_GND24")
		(at 60.96 104.14 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "80c2e9b5-9114-4a20-9a0e-5bd903331406")
		(property "Reference" "Y1"
			(at 66.04 101.6 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "12.0MHz"
			(at 68.58 106.68 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Crystal:Crystal_SMD_3225-4Pin_3.2x2.5mm"
			(at 60.96 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://www.we-online.com/components/products/datasheet/************.pdf"
			(at 60.96 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "12 MHz ±10ppm Crystal 8pF 120 Ohms 4-SMD, No Lead"
			(at 60.96 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "************"
			(at 60.96 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "DigikeyPN" "1923-************DKR-ND"
			(at 60.96 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "5042e5a4-644a-4bd1-9d49-06e652182c94")
		)
		(pin "2"
			(uuid "fa49163e-9406-4374-ae69-a29cdd2aaacc")
		)
		(pin "3"
			(uuid "1859f6f8-9e44-4e32-b5b5-1bc3835c0215")
		)
		(pin "4"
			(uuid "bfc57b99-10de-4689-be70-fc813ece721b")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "Y1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:C_Small")
		(at 96.52 43.18 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "85c9387b-18de-4820-9eae-2660c01e0952")
		(property "Reference" "C32"
			(at 96.52 40.64 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "1uF"
			(at 96.52 45.72 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0603_1608Metric"
			(at 96.52 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 96.52 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 96.52 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "CL10A105KA8NNNC"
			(at 96.52 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "C0603C105K4RAC7411"
			(at 96.52 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "1u 10V XR 0603"
			(at 96.52 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "fdae9ddb-38f1-4ad1-b26c-ee08b996d17a")
		)
		(pin "2"
			(uuid "a13f70aa-a7dc-496d-afa0-dc4dfba96c1b")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "C32")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:C_Small")
		(at 55.88 99.06 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "861bc144-30be-4e8b-a281-91ed20e971d5")
		(property "Reference" "C29"
			(at 55.88 93.98 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "6pF"
			(at 55.88 96.52 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 55.88 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 55.88 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 55.88 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "CC0402BRNPO9BN6R0"
			(at 55.88 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "6pF 10% 50V XR 0402"
			(at 55.88 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "0402N6R0D500CT"
			(at 55.88 99.06 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "d218e8b6-631a-4e3c-82b6-bcc19c6dcced")
		)
		(pin "2"
			(uuid "887e72c0-f5df-4850-81c4-64c919cbe806")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "C29")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 129.54 170.18 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "9516c1e0-0a83-4889-8a13-6af99f9e891a")
		(property "Reference" "#PWR0106"
			(at 129.54 176.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 129.54 173.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 129.54 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 129.54 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 129.54 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "c276262a-2174-4ee2-8a2a-3ec01f9d4746")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR0106")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:W25Q32JVSS")
		(at 50.8 180.34 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "9e5e3d26-9843-4423-a5f6-61def8e7a4b3")
		(property "Reference" "U5"
			(at 40.64 171.45 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "W25Q32JVSS"
			(at 52.07 189.23 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Package_SO:SOIC-8_5.23x5.23mm_P1.27mm"
			(at 50.8 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "http://www.winbond.com/resource-files/w25q32jv%20revg%2003272018%20plus.pdf"
			(at 50.8 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 50.8 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "W25Q32JVSSIQ"
			(at 50.8 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "DigikeyPN" "W25Q32JVSSIQ-ND"
			(at 50.8 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" ""
			(at 50.8 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "3410712d-f756-4e19-9dbb-f02fc4e77784")
		)
		(pin "2"
			(uuid "e5cab5bd-b21c-43bb-9d77-ee20252817dc")
		)
		(pin "3"
			(uuid "46377a94-3610-4a1b-9d3f-9e79cd68ec37")
		)
		(pin "4"
			(uuid "737acb81-6cd4-42b1-8723-f93643b0e90b")
		)
		(pin "5"
			(uuid "af135b1c-c920-4e3a-94ef-d74c69685238")
		)
		(pin "6"
			(uuid "8baee58c-b804-4850-af96-6d077f8660b8")
		)
		(pin "7"
			(uuid "57d288e9-0e5f-4cdd-9d12-c7540bb9e0b0")
		)
		(pin "8"
			(uuid "74ba7111-c050-4307-889c-715c534783a3")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "U5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:C_Small")
		(at 124.46 43.18 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "a203a77e-8ed2-4810-bfd4-4195f7cc1cde")
		(property "Reference" "C37"
			(at 124.46 40.64 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "1uF"
			(at 124.46 45.72 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0603_1608Metric"
			(at 124.46 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 124.46 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 124.46 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "CL10A105KA8NNNC"
			(at 124.46 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "C0603C105K4RAC7411"
			(at 124.46 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "1u 10V XR 0603"
			(at 124.46 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "9710c501-fa62-4e75-a680-59706da20c68")
		)
		(pin "2"
			(uuid "7fde587f-3bd9-471d-b41b-d8e4f6f79981")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "C37")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:R_Small")
		(at 73.66 167.64 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "a31b26ec-5419-49c2-98ce-7ff7b972de22")
		(property "Reference" "R38"
			(at 71.755 169.545 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 75.565 169.545 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 73.66 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 73.66 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 73.66 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "JLC" ""
			(at 73.66 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Part number" ""
			(at 73.66 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "RC0402FR-0710KL"
			(at 73.66 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "10k 0402"
			(at 73.66 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "WR04X1002FTL"
			(at 73.66 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "fcb8de9a-24de-4e7c-9a30-5c8d89cb687b")
		)
		(pin "2"
			(uuid "0ce0b4a4-6d3a-4c03-8658-192f22641724")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "R38")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:C_Small")
		(at 109.22 177.8 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a343b675-7027-4a8f-a36b-d9c7bd6ff0cf")
		(property "Reference" "C34"
			(at 111.5441 176.5942 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "100nF"
			(at 111.5441 179.0184 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 109.22 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 109.22 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 109.22 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "CL05A104KA5NNNC"
			(at 109.22 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "100n 10% 25V XR 0402"
			(at 109.22 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "GRM155R71E104KE14J"
			(at 109.22 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "0bf4b5fc-1139-468c-8115-ffe4cbf8b9bc")
		)
		(pin "2"
			(uuid "bdb0b6e5-e3c5-4eb4-a52a-de200b5fbe00")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "C34")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:+3V3")
		(at 35.56 68.58 270)
		(mirror x)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a7e04bd7-3b9d-4cc9-816b-5565077b12a5")
		(property "Reference" "#PWR092"
			(at 31.75 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+3V3"
			(at 38.735 68.58 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 35.56 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 35.56 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 35.56 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "f9e8de1b-13bc-4ff8-8612-5d5855f67338")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR092")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 109.22 139.7 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "ae421dda-f88c-4150-855f-be566e0a6ada")
		(property "Reference" "#PWR0100"
			(at 109.22 146.05 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 109.22 143.51 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 109.22 139.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 109.22 139.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 109.22 139.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "559ef646-509c-4e9b-bc42-c86e89c220df")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR0100")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 81.28 132.08 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "b102bc9d-beb7-46c0-ae45-35f1dc2645a9")
		(property "Reference" "#PWR098"
			(at 81.28 138.43 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 81.28 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 81.28 132.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 81.28 132.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 81.28 132.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "821a6729-4983-4801-a2f0-e9464f229f9d")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR098")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:Conn_01x02")
		(at 20.32 58.42 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "ba3d49d1-9362-49b8-938a-1682d5c7ab25")
		(property "Reference" "J10"
			(at 20.32 52.8899 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x02"
			(at 20.32 52.8899 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Footprint" "Connector_PinHeader_2.54mm:PinHeader_1x02_P2.54mm_Vertical"
			(at 20.32 58.42 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 20.32 58.42 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 20.32 58.42 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b725494c-05e1-4333-ad09-68552fa9faea")
		)
		(pin "2"
			(uuid "3a204749-c845-482e-9cfd-1bc9fc44961f")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "J10")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 109.22 180.34 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "ba7bfa19-b879-40b3-b7e9-46e08ebc83f7")
		(property "Reference" "#PWR0102"
			(at 109.22 186.69 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 109.22 184.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 109.22 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 109.22 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 109.22 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "ae6ca8ac-faa2-49df-b9aa-dd971ad1758d")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR0102")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 27.94 82.55 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "cdfbc022-f16f-4b22-9e9f-f72a1052019d")
		(property "Reference" "#PWR091"
			(at 27.94 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 27.94 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 27.94 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 27.94 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 27.94 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "fb62ba6c-0dbf-42fa-9321-99a9bbdba612")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR091")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 129.54 180.34 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "cfd73e8e-5bb3-4f67-b064-a17673285e24")
		(property "Reference" "#PWR0107"
			(at 129.54 186.69 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 129.54 184.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 129.54 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 129.54 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 129.54 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "4ba17896-db58-4ce6-bb17-35d6cda29741")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR0107")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 50.8 190.5 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "d66c766e-61d3-4ba6-8c1a-319763abdfa1")
		(property "Reference" "#PWR094"
			(at 50.8 196.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 50.8 194.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 50.8 190.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 50.8 190.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 50.8 190.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "902c9523-3ff7-4647-a93d-c11f126543c3")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR094")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:R_Small")
		(at 86.36 167.64 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "e0256d6f-2e26-454e-852c-74a6fbd91fd9")
		(property "Reference" "R40"
			(at 84.455 169.545 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 88.265 169.545 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 86.36 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 86.36 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 86.36 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "JLC" ""
			(at 86.36 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Part number" ""
			(at 86.36 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "RC0402FR-0710KL"
			(at 86.36 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "10k 0402"
			(at 86.36 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "WR04X1002FTL"
			(at 86.36 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "a93c5fc3-55a7-4cf4-812e-0e13e8987815")
		)
		(pin "2"
			(uuid "eaf8e6a7-7416-437d-9378-98acb586a842")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "R40")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:C_Small")
		(at 57.15 167.64 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "e1bc654c-b930-42fd-b446-1da05847a337")
		(property "Reference" "C31"
			(at 57.15 162.56 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "100nF"
			(at 57.15 165.1 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 57.15 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 57.15 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 57.15 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "JLC" ""
			(at 57.15 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Part number" ""
			(at 57.15 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "CL05A104KA5NNNC"
			(at 57.15 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "100n 10% 25V XR 0402"
			(at 57.15 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "GRM155R71E104KE14J"
			(at 57.15 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "cd323af0-f436-4537-b857-f2a7638bab23")
		)
		(pin "2"
			(uuid "3486a47e-6744-4467-a967-19d0b55a13d0")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "C31")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 119.38 170.18 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "f9665e03-5a39-4225-a106-2273c608e92c")
		(property "Reference" "#PWR0103"
			(at 119.38 176.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 119.38 173.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 119.38 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 119.38 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 119.38 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "aef962ec-e731-48b3-94fe-6e847ff91fcc")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR0103")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:R_Small")
		(at 67.31 167.64 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "fa151650-9a60-48a4-9107-72ada4e6f63b")
		(property "Reference" "R35"
			(at 65.405 169.545 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 69.215 169.545 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 67.31 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 67.31 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 67.31 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "JLC" ""
			(at 67.31 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Part number" ""
			(at 67.31 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "RC0402FR-0710KL"
			(at 67.31 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "10k 0402"
			(at 67.31 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "WR04X1002FTL"
			(at 67.31 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "46b441f0-7aac-4a85-af70-836faa6ef205")
		)
		(pin "2"
			(uuid "052ae290-f2e0-4293-97cf-39f13ef76425")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "R35")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:C_Small")
		(at 149.86 175.26 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "fb5b524f-0856-45aa-ac2b-fa4e4d2cc1ba")
		(property "Reference" "C40"
			(at 147.536 174.0542 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "100nF"
			(at 147.536 176.4784 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 149.86 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 149.86 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 149.86 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN" "CL05A104KA5NNNC"
			(at 149.86 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Characteristics" "100n 10% 25V XR 0402"
			(at 149.86 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "MPN_ALT" "GRM155R71E104KE14J"
			(at 149.86 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "f304fb36-6a97-446f-8454-0d1baeea2143")
		)
		(pin "2"
			(uuid "a5c2d7a0-bbd4-43ea-9eda-c06e29af9c56")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "C40")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "TinyTapeout:GND")
		(at 68.58 104.14 90)
		(mirror x)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "fbb12d3c-**************-d17613107649")
		(property "Reference" "#PWR097"
			(at 74.93 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 72.39 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 68.58 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 68.58 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 68.58 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "ea2978da-33f5-4358-aaee-231d9f32737b")
		)
		(instances
			(project "tinytapeout-demo"
				(path "/20adca1d-43a1-4784-9682-8b7dd1c7d330/5384e408-8f0c-411c-b558-df35c2e889ec"
					(reference "#PWR097")
					(unit 1)
				)
			)
		)
	)
)
