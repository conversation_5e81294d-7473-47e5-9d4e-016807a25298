/* src/config.h.in.  Generated from configure.ac by autoheader.  */

/* Define to 1 if you have the `arc4random' function. */
#undef HAVE_ARC4RANDOM

/* Define to 1 if you have the <a.out.h> header file. */
#undef HAVE_A_OUT_H

/* define if the compiler supports basic C++17 syntax */
#undef HAVE_CXX17

/* Define to 1 if you have the `getcontext' function. */
#undef HAVE_GETCONTEXT

/* Define to 1 if you have the `getrandom' function. */
#undef HAVE_GETRANDOM

/* Define to 1 if you have the <inttypes.h> header file. */
#undef HAVE_INTTYPES_H

/* Define to 1 if you have the `rustc_demangle' library (-lrustc_demangle). */
#undef HAVE_LIBRUSTC_DEMANGLE

/* Define to 1 if you have the `zstd' library (-lzstd). */
#undef HAVE_LIBZSTD

/* Define to 1 if you have the `memfd_create' function. */
#undef HAVE_MEMFD_CREATE

/* Define if you have POSIX threads libraries and header files. */
#undef HAVE_PTHREAD

/* Define to 1 if you have the <rustc_demangle.h> header file. */
#undef HAVE_RUSTC_DEMANGLE_H

/* Define to 1 if you have the <stdint.h> header file. */
#undef HAVE_STDINT_H

/* Define to 1 if you have the <stdio.h> header file. */
#undef HAVE_STDIO_H

/* Define to 1 if you have the <stdlib.h> header file. */
#undef HAVE_STDLIB_H

/* Define to 1 if you have the <strings.h> header file. */
#undef HAVE_STRINGS_H

/* Define to 1 if you have the <string.h> header file. */
#undef HAVE_STRING_H

/* Define to 1 if you have the <sys/mman.h> header file. */
#undef HAVE_SYS_MMAN_H

/* Define to 1 if you have the <sys/random.h> header file. */
#undef HAVE_SYS_RANDOM_H

/* Define to 1 if you have the <sys/stat.h> header file. */
#undef HAVE_SYS_STAT_H

/* Define to 1 if you have the <sys/types.h> header file. */
#undef HAVE_SYS_TYPES_H

/* Define to 1 if you have the <unistd.h> header file. */
#undef HAVE_UNISTD_H

/* Fallback definition for old systems */
#undef O_CLOEXEC

/* Name of package */
#undef PACKAGE

/* Define to the address where bug reports for this package should be sent. */
#undef PACKAGE_BUGREPORT

/* Define to the full name of this package. */
#undef PACKAGE_NAME

/* Define to the full name and version of this package. */
#undef PACKAGE_STRING

/* Define to the one symbol short name of this package. */
#undef PACKAGE_TARNAME

/* Define to the home page for this package. */
#undef PACKAGE_URL

/* Define to the version of this package. */
#undef PACKAGE_VERSION

/* Define to necessary symbol if this constant uses a non-standard name on
   your system. */
#undef PTHREAD_CREATE_JOINABLE

/* Define to 1 if all of the C90 standard headers exist (not just the ones
   required in a freestanding environment). This macro is provided for
   backward compatibility; new code need not use it. */
#undef STDC_HEADERS

/* Version number of package */
#undef VERSION

/* Number of bits in a file offset, on hosts where this is settable. */
#undef _FILE_OFFSET_BITS

/* Define for large files, on AIX-style hosts. */
#undef _LARGE_FILES
