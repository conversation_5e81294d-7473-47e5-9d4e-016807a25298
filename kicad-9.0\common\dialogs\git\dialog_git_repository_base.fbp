<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<wxFormBuilder_Project>
  <FileVersion major="1" minor="18"/>
  <object class="Project" expanded="true">
    <property name="code_generation">C++</property>
    <property name="cpp_class_decoration"></property>
    <property name="cpp_disconnect_events">1</property>
    <property name="cpp_event_generation">connect</property>
    <property name="cpp_help_provider">none</property>
    <property name="cpp_namespace"></property>
    <property name="cpp_precompiled_header"></property>
    <property name="cpp_use_array_enum">0</property>
    <property name="cpp_use_enum">0</property>
    <property name="embedded_files_path">res</property>
    <property name="encoding">UTF-8</property>
    <property name="file">dialog_git_repository_base</property>
    <property name="first_id">1000</property>
    <property name="internationalize">1</property>
    <property name="lua_skip_events">1</property>
    <property name="lua_ui_table">UI</property>
    <property name="name">DIALOG_GIT_REPOSITORY_BASE</property>
    <property name="path">.</property>
    <property name="php_disconnect_events">0</property>
    <property name="php_disconnect_mode">source_name</property>
    <property name="php_skip_events">1</property>
    <property name="python_disconnect_events">0</property>
    <property name="python_disconnect_mode">source_name</property>
    <property name="python_image_path_wrapper_function_name"></property>
    <property name="python_indent_with_spaces"></property>
    <property name="python_skip_events">1</property>
    <property name="relative_path">1</property>
    <property name="use_microsoft_bom">0</property>
    <property name="use_native_eol">0</property>
    <object class="Dialog" expanded="true">
      <property name="aui_managed">0</property>
      <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
      <property name="bg"></property>
      <property name="center">wxBOTH</property>
      <property name="context_help"></property>
      <property name="context_menu">1</property>
      <property name="drag_accept_files">0</property>
      <property name="enabled">1</property>
      <property name="event_handler">impl_virtual</property>
      <property name="extra_style"></property>
      <property name="fg"></property>
      <property name="font"></property>
      <property name="hidden">0</property>
      <property name="id">wxID_ANY</property>
      <property name="maximum_size"></property>
      <property name="minimum_size">-1,-1</property>
      <property name="name">DIALOG_GIT_REPOSITORY_BASE</property>
      <property name="pos"></property>
      <property name="size">-1,-1</property>
      <property name="style">wxCAPTION|wxDEFAULT_DIALOG_STYLE|wxRESIZE_BORDER</property>
      <property name="subclass">DIALOG_SHIM; dialog_shim.h</property>
      <property name="title">Git Repository</property>
      <property name="tooltip"></property>
      <property name="two_step_creation">0</property>
      <property name="window_extra_style"></property>
      <property name="window_name"></property>
      <property name="window_style"></property>
      <event name="OnClose">OnClose</event>
      <event name="OnUpdateUI">OnUpdateUI</event>
      <object class="wxBoxSizer" expanded="true">
        <property name="minimum_size"></property>
        <property name="name">bSizerMain</property>
        <property name="orient">wxVERTICAL</property>
        <property name="permission">protected</property>
        <object class="sizeritem" expanded="false">
          <property name="border">10</property>
          <property name="flag">wxLEFT|wxTOP</property>
          <property name="proportion">0</property>
          <object class="wxStaticText" expanded="false">
            <property name="BottomDockable">1</property>
            <property name="LeftDockable">1</property>
            <property name="RightDockable">1</property>
            <property name="TopDockable">1</property>
            <property name="aui_layer">0</property>
            <property name="aui_name"></property>
            <property name="aui_position">0</property>
            <property name="aui_row">0</property>
            <property name="best_size"></property>
            <property name="bg"></property>
            <property name="caption"></property>
            <property name="caption_visible">1</property>
            <property name="center_pane">0</property>
            <property name="close_button">1</property>
            <property name="context_help"></property>
            <property name="context_menu">1</property>
            <property name="default_pane">0</property>
            <property name="dock">Dock</property>
            <property name="dock_fixed">0</property>
            <property name="docking">Left</property>
            <property name="drag_accept_files">0</property>
            <property name="enabled">1</property>
            <property name="fg"></property>
            <property name="floatable">1</property>
            <property name="font"></property>
            <property name="gripper">0</property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="label">Connection</property>
            <property name="markup">0</property>
            <property name="max_size"></property>
            <property name="maximize_button">0</property>
            <property name="maximum_size"></property>
            <property name="min_size"></property>
            <property name="minimize_button">0</property>
            <property name="minimum_size"></property>
            <property name="moveable">1</property>
            <property name="name">m_staticText1</property>
            <property name="pane_border">1</property>
            <property name="pane_position"></property>
            <property name="pane_size"></property>
            <property name="permission">protected</property>
            <property name="pin_button">1</property>
            <property name="pos"></property>
            <property name="resize">Resizable</property>
            <property name="show">1</property>
            <property name="size"></property>
            <property name="style"></property>
            <property name="subclass">; ; forward_declare</property>
            <property name="toolbar_pane">0</property>
            <property name="tooltip"></property>
            <property name="window_extra_style"></property>
            <property name="window_name"></property>
            <property name="window_style"></property>
            <property name="wrap">-1</property>
          </object>
        </object>
        <object class="sizeritem" expanded="false">
          <property name="border">5</property>
          <property name="flag">wxALL|wxEXPAND</property>
          <property name="proportion">0</property>
          <object class="wxStaticLine" expanded="false">
            <property name="BottomDockable">1</property>
            <property name="LeftDockable">1</property>
            <property name="RightDockable">1</property>
            <property name="TopDockable">1</property>
            <property name="aui_layer">0</property>
            <property name="aui_name"></property>
            <property name="aui_position">0</property>
            <property name="aui_row">0</property>
            <property name="best_size"></property>
            <property name="bg"></property>
            <property name="caption"></property>
            <property name="caption_visible">1</property>
            <property name="center_pane">0</property>
            <property name="close_button">1</property>
            <property name="context_help"></property>
            <property name="context_menu">1</property>
            <property name="default_pane">0</property>
            <property name="dock">Dock</property>
            <property name="dock_fixed">0</property>
            <property name="docking">Left</property>
            <property name="drag_accept_files">0</property>
            <property name="enabled">1</property>
            <property name="fg"></property>
            <property name="floatable">1</property>
            <property name="font"></property>
            <property name="gripper">0</property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="max_size"></property>
            <property name="maximize_button">0</property>
            <property name="maximum_size"></property>
            <property name="min_size"></property>
            <property name="minimize_button">0</property>
            <property name="minimum_size"></property>
            <property name="moveable">1</property>
            <property name="name">m_staticline1</property>
            <property name="pane_border">1</property>
            <property name="pane_position"></property>
            <property name="pane_size"></property>
            <property name="permission">protected</property>
            <property name="pin_button">1</property>
            <property name="pos"></property>
            <property name="resize">Resizable</property>
            <property name="show">1</property>
            <property name="size"></property>
            <property name="style">wxLI_HORIZONTAL</property>
            <property name="subclass">; ; forward_declare</property>
            <property name="toolbar_pane">0</property>
            <property name="tooltip"></property>
            <property name="window_extra_style"></property>
            <property name="window_name"></property>
            <property name="window_style"></property>
          </object>
        </object>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxEXPAND</property>
          <property name="proportion">0</property>
          <object class="wxFlexGridSizer" expanded="true">
            <property name="cols">2</property>
            <property name="flexible_direction">wxBOTH</property>
            <property name="growablecols">1</property>
            <property name="growablerows"></property>
            <property name="hgap">0</property>
            <property name="minimum_size"></property>
            <property name="name">fgSizer2</property>
            <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
            <property name="permission">none</property>
            <property name="rows">0</property>
            <property name="vgap">5</property>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
              <property name="proportion">0</property>
              <object class="wxStaticText" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">1</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Name:</property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_staticText3</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <property name="wrap">-1</property>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxRIGHT</property>
              <property name="proportion">0</property>
              <object class="wxTextCtrl" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">1</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="maxlength">0</property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_txtName</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="value"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxRIGHT</property>
              <property name="proportion">0</property>
              <object class="wxStaticText" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Location:</property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_staticText4</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <property name="wrap">-1</property>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxRIGHT</property>
              <property name="proportion">0</property>
              <object class="wxTextCtrl" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="maxlength">0</property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_txtURL</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="value"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnKillFocus">OnLocationExit</event>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxRIGHT</property>
              <property name="proportion">0</property>
              <object class="wxStaticText" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">1</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Connection type:</property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_staticText9</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <property name="wrap">-1</property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">1</property>
              <object class="wxBoxSizer" expanded="true">
                <property name="minimum_size"></property>
                <property name="name">bSizer3</property>
                <property name="orient">wxHORIZONTAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="false">
                  <property name="border">5</property>
                  <property name="flag">wxRIGHT</property>
                  <property name="proportion">0</property>
                  <object class="wxChoice" expanded="false">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="choices">&quot;HTTPS&quot; &quot;SSH&quot; &quot;Local&quot;</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">1</property>
                    <property name="id">wxID_ANY</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_ConnType</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="selection">0</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass">; ; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="validator_data_type"></property>
                    <property name="validator_style">wxFILTER_NONE</property>
                    <property name="validator_type">wxDefaultValidator</property>
                    <property name="validator_variable"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <event name="OnChoice">OnSelectConnType</event>
                  </object>
                </object>
                <object class="sizeritem" expanded="false">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND</property>
                  <property name="proportion">0</property>
                  <object class="spacer" expanded="false">
                    <property name="height">0</property>
                    <property name="permission">protected</property>
                    <property name="width">0</property>
                  </object>
                </object>
              </object>
            </object>
          </object>
        </object>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxEXPAND|wxTOP</property>
          <property name="proportion">0</property>
          <object class="wxPanel" expanded="true">
            <property name="BottomDockable">1</property>
            <property name="LeftDockable">1</property>
            <property name="RightDockable">1</property>
            <property name="TopDockable">1</property>
            <property name="aui_layer">0</property>
            <property name="aui_name"></property>
            <property name="aui_position">0</property>
            <property name="aui_row">0</property>
            <property name="best_size"></property>
            <property name="bg"></property>
            <property name="caption"></property>
            <property name="caption_visible">1</property>
            <property name="center_pane">0</property>
            <property name="close_button">1</property>
            <property name="context_help"></property>
            <property name="context_menu">1</property>
            <property name="default_pane">0</property>
            <property name="dock">Dock</property>
            <property name="dock_fixed">0</property>
            <property name="docking">Left</property>
            <property name="drag_accept_files">0</property>
            <property name="enabled">1</property>
            <property name="fg"></property>
            <property name="floatable">1</property>
            <property name="font"></property>
            <property name="gripper">0</property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="max_size"></property>
            <property name="maximize_button">0</property>
            <property name="maximum_size"></property>
            <property name="min_size"></property>
            <property name="minimize_button">0</property>
            <property name="minimum_size"></property>
            <property name="moveable">1</property>
            <property name="name">m_panelAuth</property>
            <property name="pane_border">1</property>
            <property name="pane_position"></property>
            <property name="pane_size"></property>
            <property name="permission">protected</property>
            <property name="pin_button">1</property>
            <property name="pos"></property>
            <property name="resize">Resizable</property>
            <property name="show">1</property>
            <property name="size"></property>
            <property name="subclass">; ; forward_declare</property>
            <property name="toolbar_pane">0</property>
            <property name="tooltip"></property>
            <property name="window_extra_style"></property>
            <property name="window_name"></property>
            <property name="window_style">wxTAB_TRAVERSAL</property>
            <object class="wxBoxSizer" expanded="true">
              <property name="minimum_size"></property>
              <property name="name">m_szAuth</property>
              <property name="orient">wxVERTICAL</property>
              <property name="permission">none</property>
              <object class="sizeritem" expanded="false">
                <property name="border">10</property>
                <property name="flag">wxLEFT|wxTOP</property>
                <property name="proportion">0</property>
                <object class="wxStaticText" expanded="false">
                  <property name="BottomDockable">1</property>
                  <property name="LeftDockable">1</property>
                  <property name="RightDockable">1</property>
                  <property name="TopDockable">1</property>
                  <property name="aui_layer">0</property>
                  <property name="aui_name"></property>
                  <property name="aui_position">0</property>
                  <property name="aui_row">0</property>
                  <property name="best_size"></property>
                  <property name="bg"></property>
                  <property name="caption"></property>
                  <property name="caption_visible">1</property>
                  <property name="center_pane">0</property>
                  <property name="close_button">1</property>
                  <property name="context_help"></property>
                  <property name="context_menu">1</property>
                  <property name="default_pane">0</property>
                  <property name="dock">Dock</property>
                  <property name="dock_fixed">0</property>
                  <property name="docking">Left</property>
                  <property name="drag_accept_files">0</property>
                  <property name="enabled">1</property>
                  <property name="fg"></property>
                  <property name="floatable">1</property>
                  <property name="font"></property>
                  <property name="gripper">0</property>
                  <property name="hidden">0</property>
                  <property name="id">wxID_ANY</property>
                  <property name="label">Authentication</property>
                  <property name="markup">0</property>
                  <property name="max_size"></property>
                  <property name="maximize_button">0</property>
                  <property name="maximum_size"></property>
                  <property name="min_size"></property>
                  <property name="minimize_button">0</property>
                  <property name="minimum_size"></property>
                  <property name="moveable">1</property>
                  <property name="name">m_staticText2</property>
                  <property name="pane_border">1</property>
                  <property name="pane_position"></property>
                  <property name="pane_size"></property>
                  <property name="permission">protected</property>
                  <property name="pin_button">1</property>
                  <property name="pos"></property>
                  <property name="resize">Resizable</property>
                  <property name="show">1</property>
                  <property name="size"></property>
                  <property name="style"></property>
                  <property name="subclass">; ; forward_declare</property>
                  <property name="toolbar_pane">0</property>
                  <property name="tooltip"></property>
                  <property name="window_extra_style"></property>
                  <property name="window_name"></property>
                  <property name="window_style"></property>
                  <property name="wrap">-1</property>
                </object>
              </object>
              <object class="sizeritem" expanded="false">
                <property name="border">5</property>
                <property name="flag">wxEXPAND | wxALL</property>
                <property name="proportion">0</property>
                <object class="wxStaticLine" expanded="false">
                  <property name="BottomDockable">1</property>
                  <property name="LeftDockable">1</property>
                  <property name="RightDockable">1</property>
                  <property name="TopDockable">1</property>
                  <property name="aui_layer">0</property>
                  <property name="aui_name"></property>
                  <property name="aui_position">0</property>
                  <property name="aui_row">0</property>
                  <property name="best_size"></property>
                  <property name="bg"></property>
                  <property name="caption"></property>
                  <property name="caption_visible">1</property>
                  <property name="center_pane">0</property>
                  <property name="close_button">1</property>
                  <property name="context_help"></property>
                  <property name="context_menu">1</property>
                  <property name="default_pane">0</property>
                  <property name="dock">Dock</property>
                  <property name="dock_fixed">0</property>
                  <property name="docking">Left</property>
                  <property name="drag_accept_files">0</property>
                  <property name="enabled">1</property>
                  <property name="fg"></property>
                  <property name="floatable">1</property>
                  <property name="font"></property>
                  <property name="gripper">0</property>
                  <property name="hidden">0</property>
                  <property name="id">wxID_ANY</property>
                  <property name="max_size"></property>
                  <property name="maximize_button">0</property>
                  <property name="maximum_size"></property>
                  <property name="min_size"></property>
                  <property name="minimize_button">0</property>
                  <property name="minimum_size"></property>
                  <property name="moveable">1</property>
                  <property name="name">m_staticline2</property>
                  <property name="pane_border">1</property>
                  <property name="pane_position"></property>
                  <property name="pane_size"></property>
                  <property name="permission">protected</property>
                  <property name="pin_button">1</property>
                  <property name="pos"></property>
                  <property name="resize">Resizable</property>
                  <property name="show">1</property>
                  <property name="size"></property>
                  <property name="style">wxLI_HORIZONTAL</property>
                  <property name="subclass">; ; forward_declare</property>
                  <property name="toolbar_pane">0</property>
                  <property name="tooltip"></property>
                  <property name="window_extra_style"></property>
                  <property name="window_name"></property>
                  <property name="window_style"></property>
                </object>
              </object>
              <object class="sizeritem" expanded="true">
                <property name="border">5</property>
                <property name="flag">wxBOTTOM|wxEXPAND</property>
                <property name="proportion">1</property>
                <object class="wxFlexGridSizer" expanded="true">
                  <property name="cols">2</property>
                  <property name="flexible_direction">wxBOTH</property>
                  <property name="growablecols"></property>
                  <property name="growablerows"></property>
                  <property name="hgap">0</property>
                  <property name="minimum_size"></property>
                  <property name="name">fgSizer21</property>
                  <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                  <property name="permission">none</property>
                  <property name="rows">0</property>
                  <property name="vgap">5</property>
                  <object class="sizeritem" expanded="true">
                    <property name="border">5</property>
                    <property name="flag">wxALL</property>
                    <property name="proportion">0</property>
                    <object class="wxCheckBox" expanded="true">
                      <property name="BottomDockable">1</property>
                      <property name="LeftDockable">1</property>
                      <property name="RightDockable">1</property>
                      <property name="TopDockable">1</property>
                      <property name="aui_layer">0</property>
                      <property name="aui_name"></property>
                      <property name="aui_position">0</property>
                      <property name="aui_row">0</property>
                      <property name="best_size"></property>
                      <property name="bg"></property>
                      <property name="caption"></property>
                      <property name="caption_visible">1</property>
                      <property name="center_pane">0</property>
                      <property name="checked">0</property>
                      <property name="close_button">1</property>
                      <property name="context_help"></property>
                      <property name="context_menu">1</property>
                      <property name="default_pane">0</property>
                      <property name="dock">Dock</property>
                      <property name="dock_fixed">0</property>
                      <property name="docking">Left</property>
                      <property name="drag_accept_files">0</property>
                      <property name="enabled">1</property>
                      <property name="fg"></property>
                      <property name="floatable">1</property>
                      <property name="font"></property>
                      <property name="gripper">0</property>
                      <property name="hidden">0</property>
                      <property name="id">wxID_ANY</property>
                      <property name="label">SSH private key:     </property>
                      <property name="max_size"></property>
                      <property name="maximize_button">0</property>
                      <property name="maximum_size"></property>
                      <property name="min_size"></property>
                      <property name="minimize_button">0</property>
                      <property name="minimum_size"></property>
                      <property name="moveable">1</property>
                      <property name="name">m_cbCustom</property>
                      <property name="pane_border">1</property>
                      <property name="pane_position"></property>
                      <property name="pane_size"></property>
                      <property name="permission">protected</property>
                      <property name="pin_button">1</property>
                      <property name="pos"></property>
                      <property name="resize">Resizable</property>
                      <property name="show">1</property>
                      <property name="size"></property>
                      <property name="style"></property>
                      <property name="subclass">; ; forward_declare</property>
                      <property name="toolbar_pane">0</property>
                      <property name="tooltip"></property>
                      <property name="validator_data_type"></property>
                      <property name="validator_style">wxFILTER_NONE</property>
                      <property name="validator_type">wxDefaultValidator</property>
                      <property name="validator_variable"></property>
                      <property name="window_extra_style"></property>
                      <property name="window_name"></property>
                      <property name="window_style"></property>
                      <event name="OnCheckBox">onCbCustom</event>
                    </object>
                  </object>
                  <object class="sizeritem" expanded="false">
                    <property name="border">5</property>
                    <property name="flag">wxEXPAND|wxRIGHT</property>
                    <property name="proportion">0</property>
                    <object class="wxFilePickerCtrl" expanded="false">
                      <property name="BottomDockable">1</property>
                      <property name="LeftDockable">1</property>
                      <property name="RightDockable">1</property>
                      <property name="TopDockable">1</property>
                      <property name="aui_layer">0</property>
                      <property name="aui_name"></property>
                      <property name="aui_position">0</property>
                      <property name="aui_row">0</property>
                      <property name="best_size"></property>
                      <property name="bg"></property>
                      <property name="caption"></property>
                      <property name="caption_visible">1</property>
                      <property name="center_pane">0</property>
                      <property name="close_button">1</property>
                      <property name="context_help"></property>
                      <property name="context_menu">1</property>
                      <property name="default_pane">0</property>
                      <property name="dock">Dock</property>
                      <property name="dock_fixed">0</property>
                      <property name="docking">Left</property>
                      <property name="drag_accept_files">0</property>
                      <property name="enabled">1</property>
                      <property name="fg"></property>
                      <property name="floatable">1</property>
                      <property name="font"></property>
                      <property name="gripper">0</property>
                      <property name="hidden">0</property>
                      <property name="id">wxID_ANY</property>
                      <property name="max_size"></property>
                      <property name="maximize_button">0</property>
                      <property name="maximum_size"></property>
                      <property name="message">Select SSH private key file</property>
                      <property name="min_size"></property>
                      <property name="minimize_button">0</property>
                      <property name="minimum_size">250,-1</property>
                      <property name="moveable">1</property>
                      <property name="name">m_fpSSHKey</property>
                      <property name="pane_border">1</property>
                      <property name="pane_position"></property>
                      <property name="pane_size"></property>
                      <property name="permission">protected</property>
                      <property name="pin_button">1</property>
                      <property name="pos"></property>
                      <property name="resize">Resizable</property>
                      <property name="show">1</property>
                      <property name="size"></property>
                      <property name="style">wxFLP_DEFAULT_STYLE|wxFLP_FILE_MUST_EXIST|wxFLP_OPEN</property>
                      <property name="subclass"></property>
                      <property name="toolbar_pane">0</property>
                      <property name="tooltip"></property>
                      <property name="validator_data_type"></property>
                      <property name="validator_style">wxFILTER_NONE</property>
                      <property name="validator_type">wxDefaultValidator</property>
                      <property name="validator_variable"></property>
                      <property name="value"></property>
                      <property name="wildcard">*</property>
                      <property name="window_extra_style"></property>
                      <property name="window_name"></property>
                      <property name="window_style"></property>
                      <event name="OnFileChanged">OnFileUpdated</event>
                    </object>
                  </object>
                  <object class="sizeritem" expanded="false">
                    <property name="border">5</property>
                    <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxRIGHT</property>
                    <property name="proportion">0</property>
                    <object class="wxStaticText" expanded="false">
                      <property name="BottomDockable">1</property>
                      <property name="LeftDockable">1</property>
                      <property name="RightDockable">1</property>
                      <property name="TopDockable">1</property>
                      <property name="aui_layer">0</property>
                      <property name="aui_name"></property>
                      <property name="aui_position">0</property>
                      <property name="aui_row">0</property>
                      <property name="best_size"></property>
                      <property name="bg"></property>
                      <property name="caption"></property>
                      <property name="caption_visible">1</property>
                      <property name="center_pane">0</property>
                      <property name="close_button">1</property>
                      <property name="context_help"></property>
                      <property name="context_menu">1</property>
                      <property name="default_pane">0</property>
                      <property name="dock">Dock</property>
                      <property name="dock_fixed">0</property>
                      <property name="docking">Left</property>
                      <property name="drag_accept_files">0</property>
                      <property name="enabled">1</property>
                      <property name="fg"></property>
                      <property name="floatable">1</property>
                      <property name="font"></property>
                      <property name="gripper">0</property>
                      <property name="hidden">0</property>
                      <property name="id">wxID_ANY</property>
                      <property name="label">User name:</property>
                      <property name="markup">0</property>
                      <property name="max_size"></property>
                      <property name="maximize_button">0</property>
                      <property name="maximum_size"></property>
                      <property name="min_size"></property>
                      <property name="minimize_button">0</property>
                      <property name="minimum_size"></property>
                      <property name="moveable">1</property>
                      <property name="name">m_staticText11</property>
                      <property name="pane_border">1</property>
                      <property name="pane_position"></property>
                      <property name="pane_size"></property>
                      <property name="permission">protected</property>
                      <property name="pin_button">1</property>
                      <property name="pos"></property>
                      <property name="resize">Resizable</property>
                      <property name="show">1</property>
                      <property name="size"></property>
                      <property name="style"></property>
                      <property name="subclass">; ; forward_declare</property>
                      <property name="toolbar_pane">0</property>
                      <property name="tooltip"></property>
                      <property name="window_extra_style"></property>
                      <property name="window_name"></property>
                      <property name="window_style"></property>
                      <property name="wrap">-1</property>
                    </object>
                  </object>
                  <object class="sizeritem" expanded="false">
                    <property name="border">5</property>
                    <property name="flag">wxEXPAND|wxRIGHT</property>
                    <property name="proportion">0</property>
                    <object class="wxTextCtrl" expanded="false">
                      <property name="BottomDockable">1</property>
                      <property name="LeftDockable">1</property>
                      <property name="RightDockable">1</property>
                      <property name="TopDockable">1</property>
                      <property name="aui_layer">0</property>
                      <property name="aui_name"></property>
                      <property name="aui_position">0</property>
                      <property name="aui_row">0</property>
                      <property name="best_size"></property>
                      <property name="bg"></property>
                      <property name="caption"></property>
                      <property name="caption_visible">1</property>
                      <property name="center_pane">0</property>
                      <property name="close_button">1</property>
                      <property name="context_help"></property>
                      <property name="context_menu">1</property>
                      <property name="default_pane">0</property>
                      <property name="dock">Dock</property>
                      <property name="dock_fixed">0</property>
                      <property name="docking">Left</property>
                      <property name="drag_accept_files">0</property>
                      <property name="enabled">1</property>
                      <property name="fg"></property>
                      <property name="floatable">1</property>
                      <property name="font"></property>
                      <property name="gripper">0</property>
                      <property name="hidden">0</property>
                      <property name="id">wxID_ANY</property>
                      <property name="max_size"></property>
                      <property name="maximize_button">0</property>
                      <property name="maximum_size"></property>
                      <property name="maxlength">0</property>
                      <property name="min_size"></property>
                      <property name="minimize_button">0</property>
                      <property name="minimum_size">-1,-1</property>
                      <property name="moveable">1</property>
                      <property name="name">m_txtUsername</property>
                      <property name="pane_border">1</property>
                      <property name="pane_position"></property>
                      <property name="pane_size"></property>
                      <property name="permission">protected</property>
                      <property name="pin_button">1</property>
                      <property name="pos"></property>
                      <property name="resize">Resizable</property>
                      <property name="show">1</property>
                      <property name="size"></property>
                      <property name="style"></property>
                      <property name="subclass">; ; forward_declare</property>
                      <property name="toolbar_pane">0</property>
                      <property name="tooltip"></property>
                      <property name="validator_data_type"></property>
                      <property name="validator_style">wxFILTER_NONE</property>
                      <property name="validator_type">wxDefaultValidator</property>
                      <property name="validator_variable"></property>
                      <property name="value"></property>
                      <property name="window_extra_style"></property>
                      <property name="window_name"></property>
                      <property name="window_style"></property>
                    </object>
                  </object>
                  <object class="sizeritem" expanded="false">
                    <property name="border">5</property>
                    <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxRIGHT</property>
                    <property name="proportion">0</property>
                    <object class="wxStaticText" expanded="false">
                      <property name="BottomDockable">1</property>
                      <property name="LeftDockable">1</property>
                      <property name="RightDockable">1</property>
                      <property name="TopDockable">1</property>
                      <property name="aui_layer">0</property>
                      <property name="aui_name"></property>
                      <property name="aui_position">0</property>
                      <property name="aui_row">0</property>
                      <property name="best_size"></property>
                      <property name="bg"></property>
                      <property name="caption"></property>
                      <property name="caption_visible">1</property>
                      <property name="center_pane">0</property>
                      <property name="close_button">1</property>
                      <property name="context_help"></property>
                      <property name="context_menu">1</property>
                      <property name="default_pane">0</property>
                      <property name="dock">Dock</property>
                      <property name="dock_fixed">0</property>
                      <property name="docking">Left</property>
                      <property name="drag_accept_files">0</property>
                      <property name="enabled">1</property>
                      <property name="fg"></property>
                      <property name="floatable">1</property>
                      <property name="font"></property>
                      <property name="gripper">0</property>
                      <property name="hidden">0</property>
                      <property name="id">wxID_ANY</property>
                      <property name="label">SSH key password:</property>
                      <property name="markup">0</property>
                      <property name="max_size"></property>
                      <property name="maximize_button">0</property>
                      <property name="maximum_size"></property>
                      <property name="min_size"></property>
                      <property name="minimize_button">0</property>
                      <property name="minimum_size"></property>
                      <property name="moveable">1</property>
                      <property name="name">m_labelPass1</property>
                      <property name="pane_border">1</property>
                      <property name="pane_position"></property>
                      <property name="pane_size"></property>
                      <property name="permission">protected</property>
                      <property name="pin_button">1</property>
                      <property name="pos"></property>
                      <property name="resize">Resizable</property>
                      <property name="show">1</property>
                      <property name="size"></property>
                      <property name="style"></property>
                      <property name="subclass">; ; forward_declare</property>
                      <property name="toolbar_pane">0</property>
                      <property name="tooltip"></property>
                      <property name="window_extra_style"></property>
                      <property name="window_name"></property>
                      <property name="window_style"></property>
                      <property name="wrap">-1</property>
                    </object>
                  </object>
                  <object class="sizeritem" expanded="false">
                    <property name="border">5</property>
                    <property name="flag">wxEXPAND|wxRIGHT</property>
                    <property name="proportion">0</property>
                    <object class="wxTextCtrl" expanded="false">
                      <property name="BottomDockable">1</property>
                      <property name="LeftDockable">1</property>
                      <property name="RightDockable">1</property>
                      <property name="TopDockable">1</property>
                      <property name="aui_layer">0</property>
                      <property name="aui_name"></property>
                      <property name="aui_position">0</property>
                      <property name="aui_row">0</property>
                      <property name="best_size"></property>
                      <property name="bg"></property>
                      <property name="caption"></property>
                      <property name="caption_visible">1</property>
                      <property name="center_pane">0</property>
                      <property name="close_button">1</property>
                      <property name="context_help"></property>
                      <property name="context_menu">1</property>
                      <property name="default_pane">0</property>
                      <property name="dock">Dock</property>
                      <property name="dock_fixed">0</property>
                      <property name="docking">Left</property>
                      <property name="drag_accept_files">0</property>
                      <property name="enabled">1</property>
                      <property name="fg"></property>
                      <property name="floatable">1</property>
                      <property name="font"></property>
                      <property name="gripper">0</property>
                      <property name="hidden">0</property>
                      <property name="id">wxID_ANY</property>
                      <property name="max_size"></property>
                      <property name="maximize_button">0</property>
                      <property name="maximum_size"></property>
                      <property name="maxlength">0</property>
                      <property name="min_size"></property>
                      <property name="minimize_button">0</property>
                      <property name="minimum_size"></property>
                      <property name="moveable">1</property>
                      <property name="name">m_txtPassword</property>
                      <property name="pane_border">1</property>
                      <property name="pane_position"></property>
                      <property name="pane_size"></property>
                      <property name="permission">protected</property>
                      <property name="pin_button">1</property>
                      <property name="pos"></property>
                      <property name="resize">Resizable</property>
                      <property name="show">1</property>
                      <property name="size"></property>
                      <property name="style"></property>
                      <property name="subclass">; ; forward_declare</property>
                      <property name="toolbar_pane">0</property>
                      <property name="tooltip"></property>
                      <property name="validator_data_type"></property>
                      <property name="validator_style">wxFILTER_NONE</property>
                      <property name="validator_type">wxDefaultValidator</property>
                      <property name="validator_variable"></property>
                      <property name="value"></property>
                      <property name="window_extra_style"></property>
                      <property name="window_name"></property>
                      <property name="window_style"></property>
                    </object>
                  </object>
                </object>
              </object>
            </object>
          </object>
        </object>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxEXPAND</property>
          <property name="proportion">1</property>
          <object class="spacer" expanded="true">
            <property name="height">0</property>
            <property name="permission">protected</property>
            <property name="width">0</property>
          </object>
        </object>
        <object class="sizeritem" expanded="false">
          <property name="border">5</property>
          <property name="flag">wxBOTTOM|wxEXPAND|wxTOP</property>
          <property name="proportion">0</property>
          <object class="wxStdDialogButtonSizer" expanded="false">
            <property name="Apply">0</property>
            <property name="Cancel">1</property>
            <property name="ContextHelp">0</property>
            <property name="Help">1</property>
            <property name="No">0</property>
            <property name="OK">1</property>
            <property name="Save">0</property>
            <property name="Yes">0</property>
            <property name="minimum_size"></property>
            <property name="name">m_sdbSizer</property>
            <property name="permission">protected</property>
            <event name="OnHelpButtonClick">OnTestClick</event>
            <event name="OnOKButtonClick">OnOKClick</event>
          </object>
        </object>
      </object>
    </object>
  </object>
</wxFormBuilder_Project>
