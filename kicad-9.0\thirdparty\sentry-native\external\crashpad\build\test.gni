# Copyright 2017 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("crashpad_buildconfig.gni")

if (crashpad_is_in_chromium) {
  import("//testing/test.gni")
} else {
  template("test") {
    if (crashpad_is_ios) {
      import("//third_party/mini_chromium/mini_chromium/build/ios/rules.gni")

      _launch_image_bundle_target = target_name + "_launch_image"
      bundle_data(_launch_image_bundle_target) {
        forward_variables_from(invoker, [ "testonly" ])
        sources = [ "//build/ios/Default.png" ]
        outputs = [ "{{bundle_contents_dir}}/{{source_file_part}}" ]
      }

      ios_xctest_test(target_name) {
        testonly = true
        xctest_module_target = "//test/ios:google_test_runner"
        info_plist = "//build/ios/Unittest-Info.plist"
        extra_substitutions = [ "GTEST_BUNDLE_ID_SUFFIX=$target_name" ]
        forward_variables_from(invoker, "*")
        if (!defined(deps)) {
          deps = []
        }
        deps += [ ":$_launch_image_bundle_target" ]
      }
    } else {
      executable(target_name) {
        testonly = true
        forward_variables_from(invoker, "*")
      }
    }
  }
}
