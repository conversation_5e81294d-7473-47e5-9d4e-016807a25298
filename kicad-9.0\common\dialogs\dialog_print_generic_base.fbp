<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<wxFormBuilder_Project>
  <FileVersion major="1" minor="18"/>
  <object class="Project" expanded="true">
    <property name="code_generation">C++</property>
    <property name="cpp_class_decoration"></property>
    <property name="cpp_disconnect_events">1</property>
    <property name="cpp_event_generation">connect</property>
    <property name="cpp_help_provider">none</property>
    <property name="cpp_namespace"></property>
    <property name="cpp_precompiled_header"></property>
    <property name="cpp_use_array_enum">0</property>
    <property name="cpp_use_enum">1</property>
    <property name="embedded_files_path">res</property>
    <property name="encoding">UTF-8</property>
    <property name="file">dialog_print_generic_base</property>
    <property name="first_id">8200</property>
    <property name="internationalize">1</property>
    <property name="lua_skip_events">1</property>
    <property name="lua_ui_table">UI</property>
    <property name="name">DIALOG_PRINT_GENERIC</property>
    <property name="path">.</property>
    <property name="php_disconnect_events">0</property>
    <property name="php_disconnect_mode">source_name</property>
    <property name="php_skip_events">1</property>
    <property name="python_disconnect_events">0</property>
    <property name="python_disconnect_mode">source_name</property>
    <property name="python_image_path_wrapper_function_name"></property>
    <property name="python_indent_with_spaces"></property>
    <property name="python_skip_events">1</property>
    <property name="relative_path">1</property>
    <property name="use_microsoft_bom">0</property>
    <property name="use_native_eol">0</property>
    <object class="Dialog" expanded="true">
      <property name="aui_managed">0</property>
      <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
      <property name="bg"></property>
      <property name="center">wxBOTH</property>
      <property name="context_help"></property>
      <property name="context_menu">1</property>
      <property name="drag_accept_files">0</property>
      <property name="enabled">1</property>
      <property name="event_handler">impl_virtual</property>
      <property name="extra_style"></property>
      <property name="fg"></property>
      <property name="font"></property>
      <property name="hidden">0</property>
      <property name="id">wxID_ANY</property>
      <property name="maximum_size"></property>
      <property name="minimum_size">-1,-1</property>
      <property name="name">DIALOG_PRINT_GENERIC_BASE</property>
      <property name="pos"></property>
      <property name="size">-1,-1</property>
      <property name="style">wxDEFAULT_DIALOG_STYLE|wxRESIZE_BORDER</property>
      <property name="subclass">DIALOG_SHIM; dialog_shim.h</property>
      <property name="title">Print</property>
      <property name="tooltip"></property>
      <property name="two_step_creation">0</property>
      <property name="window_extra_style"></property>
      <property name="window_name"></property>
      <property name="window_style"></property>
      <event name="OnClose">onClose</event>
      <object class="wxBoxSizer" expanded="true">
        <property name="minimum_size"></property>
        <property name="name">bMainSizer</property>
        <property name="orient">wxVERTICAL</property>
        <property name="permission">none</property>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxEXPAND|wxALL</property>
          <property name="proportion">1</property>
          <object class="wxBoxSizer" expanded="true">
            <property name="minimum_size"></property>
            <property name="name">m_bUpperSizer</property>
            <property name="orient">wxHORIZONTAL</property>
            <property name="permission">protected</property>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">0</property>
              <object class="wxBoxSizer" expanded="true">
                <property name="minimum_size"></property>
                <property name="name">bRightCol</property>
                <property name="orient">wxVERTICAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND|wxALL</property>
                  <property name="proportion">1</property>
                  <object class="wxStaticBoxSizer" expanded="true">
                    <property name="id">wxID_ANY</property>
                    <property name="label">Options</property>
                    <property name="minimum_size"></property>
                    <property name="name">m_sbOptionsSizer</property>
                    <property name="orient">wxVERTICAL</property>
                    <property name="parent">1</property>
                    <property name="permission">protected</property>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxEXPAND|wxBOTTOM|wxRIGHT</property>
                      <property name="proportion">1</property>
                      <object class="wxGridBagSizer" expanded="true">
                        <property name="empty_cell_size">-1,10</property>
                        <property name="flexible_direction">wxBOTH</property>
                        <property name="growablecols">1</property>
                        <property name="growablerows"></property>
                        <property name="hgap">0</property>
                        <property name="minimum_size"></property>
                        <property name="name">m_gbOptionsSizer</property>
                        <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                        <property name="permission">protected</property>
                        <property name="vgap">3</property>
                        <object class="gbsizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="colspan">1</property>
                          <property name="column">0</property>
                          <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM|wxRIGHT|wxLEFT</property>
                          <property name="row">0</property>
                          <property name="rowspan">1</property>
                          <object class="wxStaticText" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer">0</property>
                            <property name="aui_name"></property>
                            <property name="aui_position">0</property>
                            <property name="aui_row">0</property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Output mode:</property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_outputModeLabel</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="gbsizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="colspan">1</property>
                          <property name="column">1</property>
                          <property name="flag">wxBOTTOM|wxRIGHT|wxLEFT|wxEXPAND</property>
                          <property name="row">0</property>
                          <property name="rowspan">1</property>
                          <object class="wxChoice" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer">0</property>
                            <property name="aui_name"></property>
                            <property name="aui_position">0</property>
                            <property name="aui_row">0</property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="choices">&quot;Color&quot; &quot;Black and white&quot;</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_outputMode</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="selection">0</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                          </object>
                        </object>
                        <object class="gbsizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="colspan">2</property>
                          <property name="column">0</property>
                          <property name="flag">wxEXPAND|wxBOTTOM|wxRIGHT|wxLEFT</property>
                          <property name="row">1</property>
                          <property name="rowspan">1</property>
                          <object class="wxCheckBox" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer">0</property>
                            <property name="aui_name"></property>
                            <property name="aui_position">0</property>
                            <property name="aui_row">0</property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="checked">1</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_FRAME_SEL</property>
                            <property name="label">Print drawing sheet</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_titleBlock</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass"></property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip">Print Frame references.</property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                          </object>
                        </object>
                      </object>
                    </object>
                  </object>
                </object>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxALL|wxEXPAND</property>
                  <property name="proportion">0</property>
                  <object class="wxStaticBoxSizer" expanded="true">
                    <property name="id">wxID_ANY</property>
                    <property name="label">Scale</property>
                    <property name="minimum_size"></property>
                    <property name="name">bScaleSizer</property>
                    <property name="orient">wxVERTICAL</property>
                    <property name="parent">1</property>
                    <property name="permission">none</property>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxRIGHT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxRadioButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">1:1</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_scale1</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value">0</property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag"></property>
                      <property name="proportion">0</property>
                      <object class="spacer" expanded="true">
                        <property name="height">5</property>
                        <property name="permission">protected</property>
                        <property name="width">0</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxRIGHT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxRadioButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Fit to page</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_scaleFit</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value">0</property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxEXPAND</property>
                      <property name="proportion">0</property>
                      <object class="spacer" expanded="true">
                        <property name="height">3</property>
                        <property name="permission">protected</property>
                        <property name="width">0</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxEXPAND|wxBOTTOM</property>
                      <property name="proportion">1</property>
                      <object class="wxBoxSizer" expanded="true">
                        <property name="minimum_size"></property>
                        <property name="name">bSizerScaleCustom</property>
                        <property name="orient">wxHORIZONTAL</property>
                        <property name="permission">none</property>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT|wxLEFT</property>
                          <property name="proportion">0</property>
                          <object class="wxRadioButton" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer">0</property>
                            <property name="aui_name"></property>
                            <property name="aui_position">0</property>
                            <property name="aui_row">0</property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Custom:</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_scaleCustom</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass"></property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">0</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxEXPAND|wxRIGHT</property>
                          <property name="proportion">1</property>
                          <object class="wxTextCtrl" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer">0</property>
                            <property name="aui_name"></property>
                            <property name="aui_position">0</property>
                            <property name="aui_row">0</property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="maxlength">0</property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_scaleCustomText</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass"></property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip">Set X scale adjust for exact scale plotting</property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style"></property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <event name="OnText">onSetCustomScale</event>
                          </object>
                        </object>
                      </object>
                    </object>
                  </object>
                </object>
              </object>
            </object>
          </object>
        </object>
        <object class="sizeritem" expanded="true">
          <property name="border">10</property>
          <property name="flag">wxEXPAND|wxLEFT</property>
          <property name="proportion">0</property>
          <object class="wxBoxSizer" expanded="true">
            <property name="minimum_size"></property>
            <property name="name">bSizer6</property>
            <property name="orient">wxVERTICAL</property>
            <property name="permission">none</property>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxBOTTOM|wxRIGHT|wxLEFT</property>
              <property name="proportion">0</property>
              <object class="wxStaticText" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">1</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Info text</property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_infoText</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <property name="wrap">-1</property>
              </object>
            </object>
          </object>
        </object>
        <object class="sizeritem" expanded="true">
          <property name="border">10</property>
          <property name="flag">wxEXPAND|wxLEFT</property>
          <property name="proportion">0</property>
          <object class="wxBoxSizer" expanded="true">
            <property name="minimum_size"></property>
            <property name="name">bButtonsSizer</property>
            <property name="orient">wxHORIZONTAL</property>
            <property name="permission">none</property>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALL|wxEXPAND</property>
              <property name="proportion">0</property>
              <object class="wxButton" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_PRINT_OPTIONS</property>
                <property name="label">Page Setup...</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size">120,-1</property>
                <property name="moveable">1</property>
                <property name="name">m_buttonOption</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass"></property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">onPageSetup</event>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALL|wxEXPAND</property>
              <property name="proportion">1</property>
              <object class="wxStdDialogButtonSizer" expanded="false">
                <property name="Apply">1</property>
                <property name="Cancel">1</property>
                <property name="ContextHelp">0</property>
                <property name="Help">0</property>
                <property name="No">0</property>
                <property name="OK">1</property>
                <property name="Save">0</property>
                <property name="Yes">0</property>
                <property name="minimum_size"></property>
                <property name="name">m_sdbSizer1</property>
                <property name="permission">protected</property>
                <event name="OnApplyButtonClick">onPrintPreview</event>
                <event name="OnCancelButtonClick">onCancelButtonClick</event>
                <event name="OnOKButtonClick">onPrintButtonClick</event>
              </object>
            </object>
          </object>
        </object>
      </object>
    </object>
  </object>
</wxFormBuilder_Project>
