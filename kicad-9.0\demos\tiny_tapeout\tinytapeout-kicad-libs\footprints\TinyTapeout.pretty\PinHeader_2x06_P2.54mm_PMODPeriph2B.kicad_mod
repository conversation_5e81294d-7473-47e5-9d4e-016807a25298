(footprint "PinHeader_2x06_P2.54mm_PMODPeriph2B" (version 20221018) (generator pcbnew)
  (layer "F.Cu")
  (descr "Through hole angled pin header, 2x06, 2.54mm pitch, 6mm pin length, double rows")
  (tags "PMOD peripheral, digilent numbering, through hole angled pin header THT 2x06 2.54mm double row")
  (attr through_hole)
  (fp_text reference "REF**" (at 5.655 -2.27) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
    (tstamp b6045b20-2409-4c42-a8ab-e1dffac4cfc8)
  )
  (fp_text value "PinHeader_2x06_P2.54mm_PMODPeriph2B" (at 5.655 14.97) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 7a221b19-0590-4985-8fc0-593733cb949e)
  )
  (fp_text user "${REFERENCE}" (at 5.31 6.35 90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 6ed9a615-d5a9-4dac-8831-51d44b25f6ac)
  )
  (fp_line (start -1.27 -1.27) (end 0 -1.27)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 41957f1e-2fd1-4de2-a8ac-993ecb8720dd))
  (fp_line (start -1.27 0) (end -1.27 -1.27)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 5cf0ceec-fccc-4e93-882a-9243221e5d80))
  (fp_line (start -1.2 1.55) (end 3.85 1.55)
    (stroke (width 0.3) (type default)) (layer "F.SilkS") (tstamp 10bf989f-e149-45ce-8ece-eecad77de2f6))
  (fp_line (start -1.2 3.65) (end 3.85 3.65)
    (stroke (width 0.3) (type default)) (layer "F.SilkS") (tstamp 75d824ed-2ce4-4671-8838-7bd69ed56d92))
  (fp_line (start -1.1 1.6) (end -1.1 3.6)
    (stroke (width 0.4) (type default)) (layer "F.SilkS") (tstamp cc2a6b38-7a53-415f-b9ca-03a8c957efca))
  (fp_line (start 1.042929 2.16) (end 1.497071 2.16)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f8a19087-5ca4-43d4-b283-17b1a0672384))
  (fp_line (start 1.042929 2.92) (end 1.497071 2.92)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp bf85f555-1e4b-47e6-9aaa-8cf0bdfb6cc3))
  (fp_line (start 1.042929 4.7) (end 1.497071 4.7)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0b25173f-628c-4582-8e71-7919f4931dd6))
  (fp_line (start 1.042929 5.46) (end 1.497071 5.46)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ba1a18ac-3821-4338-8f77-d48302546a89))
  (fp_line (start 1.042929 7.24) (end 1.497071 7.24)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2799e930-1f62-40ba-87f3-7bfaef0c482e))
  (fp_line (start 1.042929 8) (end 1.497071 8)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 9cd9c3b9-c667-491c-8c01-95a30a006379))
  (fp_line (start 1.042929 9.78) (end 1.497071 9.78)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2e7b2d77-96e9-4c7a-b9af-c5675f7b1d44))
  (fp_line (start 1.042929 10.54) (end 1.497071 10.54)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp cddded79-a33d-4922-8a35-62fc94cf9e76))
  (fp_line (start 1.042929 12.32) (end 1.497071 12.32)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 3b184f93-2202-4d69-937c-17efd4be77d6))
  (fp_line (start 1.042929 13.08) (end 1.497071 13.08)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 09e9ae15-bd92-40e4-8315-7e424bc89547))
  (fp_line (start 1.11 -0.38) (end 1.497071 -0.38)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 91c56a3d-8794-4ca1-8826-2492e32bf6db))
  (fp_line (start 1.11 0.38) (end 1.497071 0.38)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 9c67dc35-7741-43c0-af57-a1a1fdfde8ab))
  (fp_line (start 1.25 1.8) (end 1.25 3.45)
    (stroke (width 0.8) (type default)) (layer "F.SilkS") (tstamp 021bbd01-17e4-42ca-b3cf-ef9dd45d96c0))
  (fp_line (start 3.582929 -0.38) (end 3.98 -0.38)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 4f6235fe-1378-462a-9dd5-05d79e917139))
  (fp_line (start 3.582929 0.38) (end 3.98 0.38)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0243e285-a8f8-45fe-ad82-f6e181083d87))
  (fp_line (start 3.582929 2.16) (end 3.98 2.16)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 3ee8b6ba-3dd3-4e09-960a-39cba2629a1d))
  (fp_line (start 3.582929 2.92) (end 3.98 2.92)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f73be67f-1a6d-4c2c-ba56-1e59a40ae11a))
  (fp_line (start 3.582929 4.7) (end 3.98 4.7)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f55a13f0-a787-4c1a-9c95-dfac325feccb))
  (fp_line (start 3.582929 5.46) (end 3.98 5.46)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ac10780f-4284-4dbf-8552-908cde073ca0))
  (fp_line (start 3.582929 7.24) (end 3.98 7.24)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 061021a7-afe9-49de-a4c7-66ef029b5c60))
  (fp_line (start 3.582929 8) (end 3.98 8)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a04e9c72-f0e5-49aa-8b88-de6632eaf73e))
  (fp_line (start 3.582929 9.78) (end 3.98 9.78)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e4a77459-a4d0-4152-ac8e-e8bb19ce9c85))
  (fp_line (start 3.582929 10.54) (end 3.98 10.54)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c91c6071-f475-4b6a-86e9-f3d815831ea8))
  (fp_line (start 3.582929 12.32) (end 3.98 12.32)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 7bd37b2a-c264-4ac4-ad8f-223fe6f38c9d))
  (fp_line (start 3.582929 13.08) (end 3.98 13.08)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b2d5d8ef-c32d-45a1-92c2-441b03bb8042))
  (fp_line (start 3.6 1.6) (end 3.6 3.6)
    (stroke (width 0.4) (type default)) (layer "F.SilkS") (tstamp 932840fd-f8cd-4660-a4bc-4084cfc46ff5))
  (fp_line (start 3.75 1.6) (end 3.75 3.6)
    (stroke (width 0.4) (type default)) (layer "F.SilkS") (tstamp d05d0a6b-431b-432e-a7af-124fcfee3ec2))
  (fp_line (start 3.98 -1.33) (end 3.98 14.03)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 139b75d6-5105-4150-bb8b-71f19e88a90e))
  (fp_line (start 3.98 1.27) (end 6.64 1.27)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a8612703-8cba-46a8-9374-facf1f174797))
  (fp_line (start 3.98 3.81) (end 6.64 3.81)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp dcb9f0ab-d63d-4f07-a5ed-9bbd859b8ea2))
  (fp_line (start 3.98 6.35) (end 6.64 6.35)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 474563a0-e5f2-4a17-96fc-963792d004fa))
  (fp_line (start 3.98 8.89) (end 6.64 8.89)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 380d1240-a02c-4e15-9d50-bcbdb4882e55))
  (fp_line (start 3.98 11.43) (end 6.64 11.43)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e348681c-14ef-4ea0-9d0c-61090369b760))
  (fp_line (start 3.98 14.03) (end 6.64 14.03)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0fc935df-46c3-4f57-ac8f-206bc91c4d8e))
  (fp_line (start 6.64 -1.33) (end 3.98 -1.33)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e4dc42e3-f2da-4507-9dce-e8c27f47006d))
  (fp_line (start 6.64 -0.38) (end 12.64 -0.38)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 5f5d1862-d509-48a7-b3f8-cd1b50797c0f))
  (fp_line (start 6.64 -0.32) (end 12.64 -0.32)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 29497d51-a789-4bcb-82d0-85ce4d8596d7))
  (fp_line (start 6.64 -0.2) (end 12.64 -0.2)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0f641962-0044-4c72-9643-ce885d266a48))
  (fp_line (start 6.64 -0.08) (end 12.64 -0.08)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 51d2eb33-6471-4018-a544-514af4be79f7))
  (fp_line (start 6.64 0.04) (end 12.64 0.04)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c7860496-9a16-4ed6-a92c-8d3d4885b132))
  (fp_line (start 6.64 0.16) (end 12.64 0.16)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 97e2255b-e3d3-430e-bb60-de31f798b73b))
  (fp_line (start 6.64 0.28) (end 12.64 0.28)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 6bf5e20e-5ad0-4e96-94cc-fe1ad978e415))
  (fp_line (start 6.64 2.16) (end 12.64 2.16)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 023675b6-a95a-4713-a51b-8c989eb9270b))
  (fp_line (start 6.64 4.7) (end 12.64 4.7)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e4266b15-14d0-49d1-8480-4292d30cde26))
  (fp_line (start 6.64 7.24) (end 12.64 7.24)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 80c06781-3590-4ff0-81b8-759793860961))
  (fp_line (start 6.64 9.78) (end 12.64 9.78)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e733ea24-0f09-4396-86f4-0d44a549c907))
  (fp_line (start 6.64 12.32) (end 12.64 12.32)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 4a1bb793-e197-43fd-86a0-3d5821b5fb6e))
  (fp_line (start 6.64 14.03) (end 6.64 -1.33)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 5b583693-fc69-4f37-8b3f-2bc99ee1d730))
  (fp_line (start 12.64 -0.38) (end 12.64 0.38)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 59d47501-37f4-4202-9dd8-ab89c9d38570))
  (fp_line (start 12.64 0.38) (end 6.64 0.38)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0310fa87-a75d-4a5f-8fbb-21c372ccf0f2))
  (fp_line (start 12.64 2.16) (end 12.64 2.92)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp d38f970a-ea11-4da1-ac09-457720c6592f))
  (fp_line (start 12.64 2.92) (end 6.64 2.92)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 7043c5b6-8f7e-4d7e-b8e7-6a55f4d34019))
  (fp_line (start 12.64 4.7) (end 12.64 5.46)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp eab80bb2-8c09-4705-8d96-8706e1d8bd57))
  (fp_line (start 12.64 5.46) (end 6.64 5.46)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 5b4e5573-d128-419b-aee3-4a3fe7596edd))
  (fp_line (start 12.64 7.24) (end 12.64 8)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 6c819838-6d50-4c61-b61a-421f85526e12))
  (fp_line (start 12.64 8) (end 6.64 8)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 080daab9-8428-4dd9-bfbd-1ae4f6b6d9ec))
  (fp_line (start 12.64 9.78) (end 12.64 10.54)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 5ed52143-2808-4e02-9daf-db86c700795d))
  (fp_line (start 12.64 10.54) (end 6.64 10.54)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp d065b17f-da72-4041-8838-e36cf4129a6b))
  (fp_line (start 12.64 12.32) (end 12.64 13.08)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c3904cd7-84d5-4481-9ca5-f9069d09cd5e))
  (fp_line (start 12.64 13.08) (end 6.64 13.08)
    (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b64d5b6c-26a2-4c35-988d-58bbcd8058e3))
  (fp_rect (start -1.3 1.45) (end 3.98 3.81)
    (stroke (width 0.12) (type default)) (fill none) (layer "F.SilkS") (tstamp ac8c05e3-a3fc-47b3-81d6-4dc6ed168dcb))
  (fp_line (start -1.8 -1.8) (end -1.8 14.5)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp d7f61315-4247-4fad-b0af-4a450b0daa22))
  (fp_line (start -1.8 14.5) (end 13.1 14.5)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp af7bf1fc-0ac1-435b-8dd8-9cfa9d9aa4f0))
  (fp_line (start 13.1 -1.8) (end -1.8 -1.8)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 93cc9a11-b4d0-4570-934b-024a0fe5a88f))
  (fp_line (start 13.1 14.5) (end 13.1 -1.8)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 42e6c248-3a1a-4218-ab57-9585a181155b))
  (fp_line (start -0.32 -0.32) (end -0.32 0.32)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d8395b31-59cf-4b1b-8543-d9a17bef3e69))
  (fp_line (start -0.32 -0.32) (end 4.04 -0.32)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp afd2458a-5ef2-4a6d-982e-1f3a3a13b434))
  (fp_line (start -0.32 0.32) (end 4.04 0.32)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b40fd080-29a6-4e19-8f78-22efbafd1fc6))
  (fp_line (start -0.32 2.22) (end -0.32 2.86)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c9bd3160-3366-4b5c-966b-dcac98510e12))
  (fp_line (start -0.32 2.22) (end 4.04 2.22)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b77e4fd3-0467-4961-b9a1-8c51eb8df1fe))
  (fp_line (start -0.32 2.86) (end 4.04 2.86)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ba37e8f5-d327-4080-919e-d4f74de21590))
  (fp_line (start -0.32 4.76) (end -0.32 5.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e9127321-50c1-438d-8f77-90d1a9963c3e))
  (fp_line (start -0.32 4.76) (end 4.04 4.76)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e15883c0-b7af-4b44-8e73-c0324b9452d3))
  (fp_line (start -0.32 5.4) (end 4.04 5.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4374314b-bcdf-4b07-88b1-2330dfff9693))
  (fp_line (start -0.32 7.3) (end -0.32 7.94)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f5f2ee60-c24a-45b4-ba2f-55cc19e1c624))
  (fp_line (start -0.32 7.3) (end 4.04 7.3)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 46d855fc-156d-443c-8717-e0fa6e4e9b79))
  (fp_line (start -0.32 7.94) (end 4.04 7.94)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e11f6c3c-642d-4f86-b649-449b2303cf91))
  (fp_line (start -0.32 9.84) (end -0.32 10.48)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a2ca05fc-6d00-4d57-9c42-fcda3dddfac6))
  (fp_line (start -0.32 9.84) (end 4.04 9.84)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8faecb66-e170-4192-962f-51d5a906aace))
  (fp_line (start -0.32 10.48) (end 4.04 10.48)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0752e86d-d865-4535-86e1-bb3b292dd8a9))
  (fp_line (start -0.32 12.38) (end -0.32 13.02)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2a07b870-6582-455c-8b30-b8aee8cc4248))
  (fp_line (start -0.32 12.38) (end 4.04 12.38)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c9effe15-62a1-4e73-b54c-7a35fddb742d))
  (fp_line (start -0.32 13.02) (end 4.04 13.02)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 836113a4-f275-4e5f-a370-a5fef6023fc6))
  (fp_line (start 4.04 -0.635) (end 4.675 -1.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a3ad1af5-b56d-4133-b3b0-84ecaeee6788))
  (fp_line (start 4.04 13.97) (end 4.04 -0.635)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e0b35e77-b054-4448-a397-760114ccf95e))
  (fp_line (start 4.675 -1.27) (end 6.58 -1.27)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 42f4a0a9-0323-4877-8434-72c66f1ca586))
  (fp_line (start 6.58 -1.27) (end 6.58 13.97)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 220ff5aa-1d6e-449d-9f4d-d0a5c246df95))
  (fp_line (start 6.58 -0.32) (end 12.58 -0.32)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 83282c2f-17eb-4be9-b745-ef589bfe94c8))
  (fp_line (start 6.58 0.32) (end 12.58 0.32)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a3f249fb-ebb9-4e4d-9b7c-efe38983fac7))
  (fp_line (start 6.58 2.22) (end 12.58 2.22)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 109154a9-5132-4f4e-89b6-52ca14bfe88d))
  (fp_line (start 6.58 2.86) (end 12.58 2.86)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7d86002a-0ea0-4b93-8975-ec493f6ec668))
  (fp_line (start 6.58 4.76) (end 12.58 4.76)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 61817503-d729-4a71-81a2-6e1842b33187))
  (fp_line (start 6.58 5.4) (end 12.58 5.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 86b7a02a-9b79-437c-b9ce-b90afc41b9df))
  (fp_line (start 6.58 7.3) (end 12.58 7.3)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 04be26ce-96a4-4624-b559-25f8def8036e))
  (fp_line (start 6.58 7.94) (end 12.58 7.94)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3ca30c03-f293-48ba-943d-f1df58ffd531))
  (fp_line (start 6.58 9.84) (end 12.58 9.84)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a05e2578-2b4e-427c-881b-4c311a7e546b))
  (fp_line (start 6.58 10.48) (end 12.58 10.48)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 02ea3633-d897-42fe-9206-e87cbb01b2ec))
  (fp_line (start 6.58 12.38) (end 12.58 12.38)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 85ce9138-f3df-400a-8131-de804554a26e))
  (fp_line (start 6.58 13.02) (end 12.58 13.02)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8583c64f-3384-4518-a00f-5d7587521c70))
  (fp_line (start 6.58 13.97) (end 4.04 13.97)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp aa0f3494-76d3-4f87-aad4-8eafc461a506))
  (fp_line (start 12.58 -0.32) (end 12.58 0.32)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1f46e6e0-12d9-4077-a37a-ac16a123eeda))
  (fp_line (start 12.58 2.22) (end 12.58 2.86)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 479cef7f-3c9b-4fb3-aeb8-aae76e8ee819))
  (fp_line (start 12.58 4.76) (end 12.58 5.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp baa53cce-c986-4d05-8d43-62bab2a08eb6))
  (fp_line (start 12.58 7.3) (end 12.58 7.94)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6a6c4d36-36f5-49a7-8fb7-3bae4b46bced))
  (fp_line (start 12.58 9.84) (end 12.58 10.48)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e4aca766-a76c-423c-a1b8-7f18780f5c65))
  (fp_line (start 12.58 12.38) (end 12.58 13.02)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2b607aa7-4d9d-4f40-8a40-e0e6249e3da8))
  (pad "1" thru_hole rect (at 0 12.7) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 8a5c19c0-f4ce-4c81-a660-54563f9e5e3b))
  (pad "2" thru_hole oval (at 0 10.16) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 4d5efba8-040d-4286-a267-8dbc7a13a1c9))
  (pad "3" thru_hole oval (at 0 7.62) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 952469bd-d70d-42c8-b623-8ec50c8d84fb))
  (pad "4" thru_hole oval (at 0 5.08) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 0125a801-2e2c-46c8-a3d1-3040d14e9488))
  (pad "5" thru_hole oval (at 0 2.54) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 58088e06-aea3-4f62-a082-1581cbafa62a))
  (pad "6" thru_hole circle (at 0 0) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 1a9ee240-3a1b-4bb0-955a-51296e6f9b3f))
  (pad "7" thru_hole oval (at 2.54 12.7) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp d1fb294b-e5ea-4fc4-b8d2-97f143e941ba))
  (pad "8" thru_hole oval (at 2.54 10.16) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 4a7778b6-8dc2-4ea2-81ac-bc92f33c445d))
  (pad "9" thru_hole oval (at 2.54 7.62) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 607e3b29-c8cc-4915-be21-e40a3c5d2289))
  (pad "10" thru_hole oval (at 2.54 5.08) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp ffea6cf1-ca18-4672-95bc-6fdfbd1a8ddb))
  (pad "11" thru_hole oval (at 2.54 2.54) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp 4c0c5a54-a9bb-47a3-b901-27327651a6aa))
  (pad "12" thru_hole oval (at 2.54 0) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask") (tstamp c2e62490-4101-43e0-8105-1beabde31652))
  (model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_2x06_P2.54mm_Horizontal.wrl"
    (offset (xyz 0 0 0))
    (scale (xyz 1 1 1))
    (rotate (xyz 0 0 0))
  )
)
