(kicad_sch
	(version 20240602)
	(generator "eeschema")
	(generator_version "8.99")
	(uuid "290fe62e-4cea-473b-badf-2a76c0d280cd")
	(paper "A4")
	(lib_symbols
		(symbol "power:GND"
			(power)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "GND"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "power-flag"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "GND_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "GND_1_1"
				(pin power_in line
					(at 0 0 270)
					(length 0) hide
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "power:PWR_FLAG"
			(power)
			(pin_numbers hide)
			(pin_names
				(offset 0) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#FLG"
				(at 0 1.905 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "PWR_FLAG"
				(at 0 3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Special symbol for telling ERC where power comes from"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "power-flag"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "PWR_FLAG_0_0"
				(pin power_out line
					(at 0 0 90)
					(length 0)
					(name "pwr"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "PWR_FLAG_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 1.27) (xy -1.016 1.905) (xy 0 2.54) (xy 1.016 1.905) (xy 0 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
		)
		(symbol "v_i_sources:IAM-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "I"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "IAM-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "I"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "am(1 0 100k 1k 1n)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "IAM-Simulation_SPICE_0_0"
				(arc
					(start -0.508 0.508)
					(mid -0.254 0.7609)
					(end 0 0.508)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "IAM-Simulation_SPICE_0_1"
				(arc
					(start -1.524 0)
					(mid -1.27 0.2529)
					(end -1.016 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -0.508 0)
					(mid -0.762 -0.2529)
					(end -1.016 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.508 0.508) (xy -0.508 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.254 1.778) (xy 0 1.27) (xy 0.254 1.778)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 1.27) (xy 0 2.286)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0.508) (xy 0 -0.508)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(arc
					(start 0.508 -0.508)
					(mid 0.254 -0.7609)
					(end 0 -0.508)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.508 -0.508) (xy 0.508 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 0.508 0)
					(mid 0.762 0.2529)
					(end 1.016 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 1.524 0)
					(mid 1.27 -0.2529)
					(end 1.016 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "IAM-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:IDC-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "I"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "IDC-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "I"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "dc(1)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "IDC-Simulation_SPICE_0_0"
				(polyline
					(pts
						(xy -1.27 0.254) (xy 1.27 0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.762 -0.254) (xy -1.27 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.254 -0.254) (xy -0.254 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -0.254) (xy 0.762 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "IDC-Simulation_SPICE_0_1"
				(polyline
					(pts
						(xy -0.254 1.778) (xy 0 1.27) (xy 0.254 1.778)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 1.27) (xy 0 2.286)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "IDC-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:IEXP-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "I"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "IEXP-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "I"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "exp(0 1 2n 30n 60n 40n)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "IEXP-Simulation_SPICE_0_0"
				(polyline
					(pts
						(xy -1.27 -0.762) (xy -1.778 -0.762)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -1.27 -0.762)
					(mid -0.8506 0.1796)
					(end 0 0.762)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -0.762) (xy 1.778 -0.762)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 1.27 -0.762)
					(mid 0.3591 -0.2299)
					(end 0 0.762)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "IEXP-Simulation_SPICE_0_1"
				(polyline
					(pts
						(xy -0.254 1.778) (xy 0 1.27) (xy 0.254 1.778)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 1.27) (xy 0 2.286)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "IEXP-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:IPULSE-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "I"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "IPULSE-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "I"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "pulse(0 1 2n 2n 2n 50n 100n)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "IPULSE-Simulation_SPICE_0_0"
				(polyline
					(pts
						(xy -2.032 -0.762) (xy -1.397 -0.762) (xy -1.143 0.762) (xy -0.127 0.762) (xy 0.127 -0.762) (xy 1.143 -0.762)
						(xy 1.397 0.762) (xy 2.032 0.762)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "IPULSE-Simulation_SPICE_0_1"
				(polyline
					(pts
						(xy -0.254 1.778) (xy 0 1.27) (xy 0.254 1.778)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 1.27) (xy 0 2.286)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "IPULSE-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:IPWL-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "I"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "IPWL-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "I"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "pwl(0 -1 50n -1 51n 0 97n 1 171n -1 200n -1)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "IPWL-Simulation_SPICE_0_0"
				(polyline
					(pts
						(xy -1.778 -1.016) (xy -0.762 1.016) (xy -0.254 0) (xy 0.762 0) (xy 1.27 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "IPWL-Simulation_SPICE_0_1"
				(polyline
					(pts
						(xy -0.254 1.778) (xy 0 1.27) (xy 0.254 1.778)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 1.27) (xy 0 2.286)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "IPWL-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:ISFFM-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "I"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "ISFFM-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "I"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "sffm(0 1 100k 5 1k)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "ISFFM-Simulation_SPICE_0_1"
				(polyline
					(pts
						(xy -2.032 0.254) (xy -2.032 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -2.032 0.254)
					(mid -1.778 0.5069)
					(end -1.524 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.524 -0.254) (xy -1.524 0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -1.016 -0.254)
					(mid -1.27 -0.5069)
					(end -1.524 -0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.016 0) (xy -1.016 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -1.016 0)
					(mid -0.508 0.5058)
					(end 0 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.254 1.778) (xy 0 1.27) (xy 0.254 1.778)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 1.27) (xy 0 2.286)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(arc
					(start 1.016 0)
					(mid 0.508 -0.5058)
					(end 0 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.016 0.254) (xy 1.016 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 1.016 0.254)
					(mid 1.27 0.5069)
					(end 1.524 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.524 0.254) (xy 1.524 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 2.032 -0.254)
					(mid 1.778 -0.5069)
					(end 1.524 -0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 2.032 -0.254) (xy 2.032 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "ISFFM-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:ISIN-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "I"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "ISIN-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "I"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "sin(0 1 1k)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "ISIN-Simulation_SPICE_0_0"
				(arc
					(start -1.27 0)
					(mid -0.635 0.6323)
					(end 0 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 1.27 0)
					(mid 0.635 -0.6323)
					(end 0 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "ISIN-Simulation_SPICE_0_1"
				(polyline
					(pts
						(xy -0.254 1.778) (xy 0 1.27) (xy 0.254 1.778)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 1.27) (xy 0 2.286)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "ISIN-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:ITRNOISE-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "I"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "ITRNOISE-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "I"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "trnoise(20n 0.5n 0 0)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "ITRNOISE-Simulation_SPICE_0_1"
				(polyline
					(pts
						(xy -2.032 -0.762) (xy -1.651 1.143) (xy -1.651 -0.254) (xy -1.524 0.508) (xy -1.397 -1.143) (xy -0.889 1.016)
						(xy -0.762 -0.762) (xy -0.508 0.254) (xy -0.381 -0.508) (xy -0.254 0.381) (xy -0.127 -0.889) (xy 0.381 1.397)
						(xy 0.508 -1.397) (xy 0.635 0.762) (xy 1.016 -0.381) (xy 1.27 1.397) (xy 1.524 -0.508) (xy 1.778 0.381)
						(xy 2.032 -0.889)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.254 1.778) (xy 0 1.27) (xy 0.254 1.778)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 1.27) (xy 0 2.286)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "ITRNOISE-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:ITRRANDOM-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "I"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "ITRRANDOM-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "I"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "trrandom(2 10m 0 1)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "ITRRANDOM-Simulation_SPICE_0_1"
				(polyline
					(pts
						(xy -2.032 -0.762) (xy -1.651 1.143) (xy -1.651 -0.254) (xy -1.524 0.508) (xy -1.397 -1.143) (xy -0.889 1.016)
						(xy -0.762 -0.762) (xy -0.508 0.254) (xy -0.381 -0.508) (xy -0.254 0.381) (xy -0.127 -0.889) (xy 0.381 1.397)
						(xy 0.508 -1.397) (xy 0.635 0.762) (xy 1.016 -0.381) (xy 1.27 1.397) (xy 1.524 -0.508) (xy 1.778 0.381)
						(xy 2.032 -0.889)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.254 1.778) (xy 0 1.27) (xy 0.254 1.778)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 1.27) (xy 0 2.286)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "ITRRANDOM-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:R-Device"
			(pin_numbers hide)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "R"
				(at 2.032 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "R-Device"
				(at 0 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at -1.778 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "R_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "R-Device_0_1"
				(rectangle
					(start -1.016 -2.54)
					(end 1.016 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "R-Device_1_1"
				(pin passive line
					(at 0 3.81 270)
					(length 1.27)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -3.81 90)
					(length 1.27)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:VAM-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "VAM-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "am(1 0 100k 1k 1n)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "VAM-Simulation_SPICE_0_0"
				(arc
					(start -0.508 0.508)
					(mid -0.254 0.7609)
					(end 0 0.508)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VAM-Simulation_SPICE_0_1"
				(arc
					(start -1.524 0)
					(mid -1.27 0.2529)
					(end -1.016 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -0.508 0)
					(mid -0.762 -0.2529)
					(end -1.016 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.508 0.508) (xy -0.508 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0.508) (xy 0 -0.508)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(arc
					(start 0.508 -0.508)
					(mid 0.254 -0.7609)
					(end 0 -0.508)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.508 -0.508) (xy 0.508 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 0.508 0)
					(mid 0.762 0.2529)
					(end 1.016 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 1.524 0)
					(mid 1.27 -0.2529)
					(end 1.016 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "VAM-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:VDC-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "VDC-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "dc(1)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "VDC-Simulation_SPICE_0_0"
				(polyline
					(pts
						(xy -1.27 0.254) (xy 1.27 0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.762 -0.254) (xy -1.27 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.254 -0.254) (xy -0.254 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -0.254) (xy 0.762 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VDC-Simulation_SPICE_0_1"
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "VDC-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:VEXP-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "VEXP-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "exp(0 1 2n 30n 60n 40n)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "VEXP-Simulation_SPICE_0_0"
				(polyline
					(pts
						(xy -1.27 -0.762) (xy -1.778 -0.762)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -1.27 -0.762)
					(mid -0.8506 0.1796)
					(end 0 0.762)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -0.762) (xy 1.778 -0.762)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 1.27 -0.762)
					(mid 0.3591 -0.2299)
					(end 0 0.762)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VEXP-Simulation_SPICE_0_1"
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "VEXP-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:VPULSE-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "VPULSE-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "pulse(0 1 2n 2n 2n 50n 100n)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "VPULSE-Simulation_SPICE_0_0"
				(polyline
					(pts
						(xy -2.032 -0.762) (xy -1.397 -0.762) (xy -1.143 0.762) (xy -0.127 0.762) (xy 0.127 -0.762) (xy 1.143 -0.762)
						(xy 1.397 0.762) (xy 2.032 0.762)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VPULSE-Simulation_SPICE_0_1"
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "VPULSE-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:VPWL-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "VPWL-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "pwl(0 -1 50n -1 51n 0 97n 1 171n -1 200n -1)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "VPWL-Simulation_SPICE_0_0"
				(polyline
					(pts
						(xy -1.778 -1.016) (xy -0.762 1.016) (xy -0.254 0) (xy 0.762 0) (xy 1.27 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VPWL-Simulation_SPICE_0_1"
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "VPWL-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:VSFFM-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "VSFFM-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "sffm(0 1 100k 5 1k)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "VSFFM-Simulation_SPICE_0_0"
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VSFFM-Simulation_SPICE_0_1"
				(polyline
					(pts
						(xy -2.032 0.254) (xy -2.032 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -2.032 0.254)
					(mid -1.778 0.5069)
					(end -1.524 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.524 -0.254) (xy -1.524 0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -1.016 -0.254)
					(mid -1.27 -0.5069)
					(end -1.524 -0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.016 0) (xy -1.016 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -1.016 0)
					(mid -0.508 0.5058)
					(end 0 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(arc
					(start 1.016 0)
					(mid 0.508 -0.5058)
					(end 0 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.016 0.254) (xy 1.016 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 1.016 0.254)
					(mid 1.27 0.5069)
					(end 1.524 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.524 0.254) (xy 1.524 -0.254)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 2.032 -0.254)
					(mid 1.778 -0.5069)
					(end 1.524 -0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 2.032 -0.254) (xy 2.032 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "VSFFM-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:VSIN-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "VSIN-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "sin(0 1 1k)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "VSIN-Simulation_SPICE_0_0"
				(arc
					(start -1.27 0)
					(mid -0.635 0.6323)
					(end 0 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 1.27 0)
					(mid 0.635 -0.6323)
					(end 0 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VSIN-Simulation_SPICE_0_1"
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "VSIN-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:VTRNOISE-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "VTRNOISE-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "trnoise(20n 0.5n 0 0)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "VTRNOISE-Simulation_SPICE_0_0"
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VTRNOISE-Simulation_SPICE_0_1"
				(polyline
					(pts
						(xy -2.032 -0.762) (xy -1.651 1.143) (xy -1.651 -0.254) (xy -1.524 0.508) (xy -1.397 -1.143) (xy -0.889 1.016)
						(xy -0.762 -0.762) (xy -0.508 0.254) (xy -0.381 -0.508) (xy -0.254 0.381) (xy -0.127 -0.889) (xy 0.381 1.397)
						(xy 0.508 -1.397) (xy 0.635 0.762) (xy 1.016 -0.381) (xy 1.27 1.397) (xy 1.524 -0.508) (xy 1.778 0.381)
						(xy 2.032 -0.889)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "VTRNOISE-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "v_i_sources:VTRRANDOM-Simulation_SPICE"
			(pin_numbers hide)
			(pin_names
				(offset 0.0254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V"
				(at 2.54 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "VTRRANDOM-Simulation_SPICE"
				(at 2.54 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Spice_Netlist_Enabled" "Y"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Primitive" "V"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
					(hide yes)
				)
			)
			(property "Spice_Model" "trrandom(2 10m 0 1)"
				(at 2.54 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(symbol "VTRRANDOM-Simulation_SPICE_0_0"
				(text "+"
					(at 0 1.905 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "VTRRANDOM-Simulation_SPICE_0_1"
				(polyline
					(pts
						(xy -2.032 -0.762) (xy -1.651 1.143) (xy -1.651 -0.254) (xy -1.524 0.508) (xy -1.397 -1.143) (xy -0.889 1.016)
						(xy -0.762 -0.762) (xy -0.508 0.254) (xy -0.381 -0.508) (xy -0.254 0.381) (xy -0.127 -0.889) (xy 0.381 1.397)
						(xy 0.508 -1.397) (xy 0.635 0.762) (xy 1.016 -0.381) (xy 1.27 1.397) (xy 1.524 -0.508) (xy 1.778 0.381)
						(xy 2.032 -0.889)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "VTRRANDOM-Simulation_SPICE_1_1"
				(pin passive line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
	)
	(text "Voltage sources"
		(exclude_from_sim no)
		(at 88.9 86.36 0)
		(effects
			(font
				(size 3.81 3.81)
			)
			(justify left bottom)
		)
		(uuid "56fc0362-05ac-459e-9e2f-e3c52f91dcb7")
	)
	(text "Current sources"
		(exclude_from_sim no)
		(at 88.9 163.83 0)
		(effects
			(font
				(size 3.81 3.81)
			)
			(justify left bottom)
		)
		(uuid "6f3bb422-531d-4104-8f13-c1f844beef33")
	)
	(text ".tran 1n 300n"
		(exclude_from_sim no)
		(at 20.32 170.18 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "7589969a-705b-4263-a557-26b406d8fe3a")
	)
	(junction
		(at 25.4 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "00f121a4-b849-453a-929d-e74691a0da5f")
	)
	(junction
		(at 60.96 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "0504fc30-872e-4569-b6ae-b2a794b8245e")
	)
	(junction
		(at 133.35 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "062a38a4-05ae-41a4-8ad0-cfa2ba93879a")
	)
	(junction
		(at 182.88 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "084f84e6-c99f-4c31-9549-323e5f4acf27")
	)
	(junction
		(at 92.71 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "0bdf7104-be45-49fb-8f33-a9bc014ac05b")
	)
	(junction
		(at 111.76 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "127ad424-341c-4ea6-833d-b6f00ed9c7ee")
	)
	(junction
		(at 151.13 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "15611058-9e7f-4fa6-8605-c274b83d95e8")
	)
	(junction
		(at 160.02 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "15f96786-648a-49da-b88f-d81c2f9872a3")
	)
	(junction
		(at 26.67 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "19354881-b535-4f99-84d1-35d88908b3cb")
	)
	(junction
		(at 121.92 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "3b64bec7-4419-4deb-bd94-28050b575dcf")
	)
	(junction
		(at 113.03 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "3bf13ef6-6278-45b9-8008-7149f4e08bb4")
	)
	(junction
		(at 181.61 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "4286c81d-b438-4940-836d-3fb3bcba5a66")
	)
	(junction
		(at 81.28 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "44f60df6-3d27-4116-8c68-7bb59dd72228")
	)
	(junction
		(at 43.18 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "45389045-4dc7-40f4-bf61-bc4c144f75ce")
	)
	(junction
		(at 120.65 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "513452cf-226b-437f-9df2-39a43f5a0c01")
	)
	(junction
		(at 93.98 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "514dc6d6-97a0-4779-9306-4cc045602ce5")
	)
	(junction
		(at 44.45 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "72f3a97a-a2a7-4440-bed3-edc466d08d40")
	)
	(junction
		(at 71.12 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "75410e56-755b-4661-88f9-539d8120a50e")
	)
	(junction
		(at 173.99 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "843664fb-a872-43e3-a03b-8af235f9bb81")
	)
	(junction
		(at 172.72 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "9dc09ba4-1960-4844-b23c-0e5d1e3408a2")
	)
	(junction
		(at 102.87 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "ab06cfd3-86c4-4c6d-a9b1-8456901fecca")
	)
	(junction
		(at 158.75 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "acfd3020-adb6-4dbc-915c-582063e85a0d")
	)
	(junction
		(at 101.6 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "b021610a-3b05-4414-8d79-17f2f29b65ed")
	)
	(junction
		(at 52.07 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "b680c380-5a81-4cd6-91be-03b31539bd9f")
	)
	(junction
		(at 149.86 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "c22fa600-3e22-4fba-902f-958294ab52ed")
	)
	(junction
		(at 35.56 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "c608d515-1bc6-4a23-9b04-e50a0099ff38")
	)
	(junction
		(at 142.24 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "c80e3ae3-eacc-412d-879e-614f3f31d120")
	)
	(junction
		(at 34.29 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "d4d7863e-129e-4853-9024-0d3c95171e38")
	)
	(junction
		(at 80.01 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "d6c947c4-99b8-497d-b75a-fb58e12ef828")
	)
	(junction
		(at 72.39 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "e3d491bb-131a-4cc6-bc75-11149750cbd3")
	)
	(junction
		(at 62.23 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "ed9f4dfa-5714-4073-87a6-0cfeff9434fc")
	)
	(junction
		(at 53.34 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "f4200816-207c-4766-ab24-0853b5c89e06")
	)
	(junction
		(at 132.08 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "f55d3c77-c9ba-4c45-b4fb-662e12952cce")
	)
	(junction
		(at 33.02 157.48)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "fcc227db-81ec-4c15-a439-48af703c357b")
	)
	(junction
		(at 140.97 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "fe9419b5-d033-440d-92c2-8da52b674365")
	)
	(wire
		(pts
			(xy 181.61 78.74) (xy 181.61 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "0222aa7e-42ba-4244-b173-caad9a6abcd3")
	)
	(wire
		(pts
			(xy 72.39 92.71) (xy 72.39 93.98)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "059ccfef-03ea-4e6c-96a4-6ed04c91d6e0")
	)
	(polyline
		(pts
			(xy 20.32 87.63) (xy 20.32 12.7)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "076c1189-58cb-4179-aa34-19b6e3c57ccf")
	)
	(wire
		(pts
			(xy 71.12 24.13) (xy 71.12 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "076d18a8-724b-41f9-9d16-758b92f14de2")
	)
	(wire
		(pts
			(xy 43.18 24.13) (xy 43.18 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "07d55d42-791a-4a6e-bdbc-2f92aad3fda8")
	)
	(wire
		(pts
			(xy 102.87 157.48) (xy 113.03 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "0dd3b147-0988-490a-89e2-775188b1db8d")
	)
	(wire
		(pts
			(xy 181.61 68.58) (xy 181.61 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "1009f357-1721-47c0-b721-75db7e91dcd1")
	)
	(wire
		(pts
			(xy 71.12 15.24) (xy 71.12 16.51)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "10f0107a-28a4-4ffd-96f4-a0dc4b989b4b")
	)
	(wire
		(pts
			(xy 160.02 149.86) (xy 160.02 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "11f80e1f-97e4-4bcb-b3e1-4c648ec6c321")
	)
	(wire
		(pts
			(xy 182.88 157.48) (xy 173.99 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "12a93d19-e0ad-48f1-8b64-0aa809490b96")
	)
	(wire
		(pts
			(xy 25.4 80.01) (xy 25.4 81.28)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "1456a46f-a578-40bc-8265-c459353c4fe2")
	)
	(wire
		(pts
			(xy 53.34 92.71) (xy 44.45 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "164afe77-a0ff-482b-9c68-f17138fb85f8")
	)
	(wire
		(pts
			(xy 26.67 157.48) (xy 26.67 158.75)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "17c561bb-7eab-4973-a03e-b367c738d599")
	)
	(wire
		(pts
			(xy 80.01 80.01) (xy 92.71 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "1b859a6b-3c60-443f-ac05-c7ce344c8c02")
	)
	(wire
		(pts
			(xy 35.56 157.48) (xy 44.45 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "1ba2bd77-f219-47df-b217-8a3f0dce0e24")
	)
	(wire
		(pts
			(xy 172.72 16.51) (xy 172.72 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "1cda8cd8-e7f1-41f5-966a-0e7829e75d94")
	)
	(wire
		(pts
			(xy 101.6 43.18) (xy 101.6 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "2074dda0-da67-4c49-b971-980ce4a253cf")
	)
	(wire
		(pts
			(xy 151.13 157.48) (xy 142.24 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "2152f383-9c8c-42c1-a095-5632ad2d568a")
	)
	(polyline
		(pts
			(xy 208.28 87.63) (xy 20.32 87.63)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "2220d4c1-754e-4e97-83d2-aeab7026698c")
	)
	(wire
		(pts
			(xy 113.03 93.98) (xy 113.03 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "222f7deb-1445-4654-bf53-d812c6a902f1")
	)
	(wire
		(pts
			(xy 133.35 157.48) (xy 142.24 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "23e65600-0682-4fa2-9325-82a7b14c68cd")
	)
	(wire
		(pts
			(xy 53.34 101.6) (xy 53.34 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "258789e0-e215-44ff-ad99-5115f2550f18")
	)
	(wire
		(pts
			(xy 132.08 80.01) (xy 140.97 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "27166dc3-cc3e-4a7e-b18a-3748c3afe36a")
	)
	(wire
		(pts
			(xy 160.02 157.48) (xy 151.13 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "2e4ca71e-6c41-4238-b6d0-4bdd8b86c633")
	)
	(wire
		(pts
			(xy 120.65 15.24) (xy 132.08 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "2ec5bece-2e1d-4202-80ef-bf490062b516")
	)
	(wire
		(pts
			(xy 142.24 143.51) (xy 142.24 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "2ec90ecd-aef7-455b-909c-7c47a3d47d5c")
	)
	(wire
		(pts
			(xy 80.01 36.83) (xy 80.01 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "2f415448-1cfe-49f0-9084-53e12b986d25")
	)
	(wire
		(pts
			(xy 160.02 92.71) (xy 173.99 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "31fff571-d001-43f3-8a61-925bcd1200ec")
	)
	(wire
		(pts
			(xy 172.72 24.13) (xy 172.72 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "339ee74e-5c55-4a83-8de1-584b943763eb")
	)
	(wire
		(pts
			(xy 60.96 15.24) (xy 71.12 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "343daeee-c379-4d38-a564-ec841e974353")
	)
	(wire
		(pts
			(xy 120.65 49.53) (xy 120.65 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "35d4f9c4-b6d6-4b12-a7c9-b843609edca9")
	)
	(wire
		(pts
			(xy 133.35 93.98) (xy 133.35 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "391cf4e3-4c8d-4e9f-b2cd-fd3afe7ea3b6")
	)
	(wire
		(pts
			(xy 158.75 72.39) (xy 158.75 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "39a57e91-cc1a-4510-8806-ea46c8563564")
	)
	(wire
		(pts
			(xy 62.23 157.48) (xy 53.34 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "39a7a34e-fdef-4ad2-9b12-521f3927f970")
	)
	(wire
		(pts
			(xy 140.97 55.88) (xy 140.97 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "3b1a5079-aa1b-49f4-96da-6df75ffeb849")
	)
	(wire
		(pts
			(xy 101.6 80.01) (xy 111.76 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "3df1ff07-dfc2-47e4-9c51-e253fbae3603")
	)
	(wire
		(pts
			(xy 52.07 15.24) (xy 52.07 16.51)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "3ea0b489-169f-4ff5-ba0f-75eecf9c48e4")
	)
	(wire
		(pts
			(xy 140.97 15.24) (xy 149.86 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "4329ca64-d1b7-491c-bbfd-6543e85e9af9")
	)
	(wire
		(pts
			(xy 133.35 101.6) (xy 133.35 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "43e0b5c9-72ee-4e6c-8f05-8dab987937e9")
	)
	(wire
		(pts
			(xy 111.76 24.13) (xy 111.76 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "440dc4c3-8659-4e05-b209-0ed045246d6b")
	)
	(wire
		(pts
			(xy 44.45 111.76) (xy 44.45 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "4424f4bc-360c-4a14-bfb3-6e8c33dd81bd")
	)
	(wire
		(pts
			(xy 44.45 157.48) (xy 53.34 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "443ac559-76dc-490c-8b56-205420c0cbf0")
	)
	(wire
		(pts
			(xy 33.02 157.48) (xy 33.02 158.75)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "44b5ddab-1ac3-4adf-9b3b-198331e3bfa2")
	)
	(wire
		(pts
			(xy 132.08 24.13) (xy 132.08 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "44ebceec-6a50-49af-bd6b-c4e2a1dad9f2")
	)
	(wire
		(pts
			(xy 34.29 16.51) (xy 34.29 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "457d2c5a-e9fd-4898-b55b-b004cad846db")
	)
	(wire
		(pts
			(xy 80.01 46.99) (xy 80.01 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "4740a3f6-1844-4ee4-a20a-20f3eba6066c")
	)
	(wire
		(pts
			(xy 62.23 92.71) (xy 72.39 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "4858c310-9884-4f65-a508-8fcf7e3d8055")
	)
	(wire
		(pts
			(xy 25.4 15.24) (xy 34.29 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "48d9a607-1ba6-4f3b-a8b4-21db5ba806cb")
	)
	(wire
		(pts
			(xy 26.67 105.41) (xy 26.67 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "4cfef907-b286-4124-8db7-8734651bebe1")
	)
	(wire
		(pts
			(xy 102.87 130.81) (xy 102.87 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "4eb4b6eb-fea1-4176-9545-28fbf41ab5c0")
	)
	(wire
		(pts
			(xy 26.67 92.71) (xy 35.56 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "50f58a58-0c9b-43bc-b36c-ee32f0d9fb59")
	)
	(wire
		(pts
			(xy 198.12 16.51) (xy 198.12 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "512981aa-dd19-4ff1-8c97-44bae63faf16")
	)
	(wire
		(pts
			(xy 151.13 93.98) (xy 151.13 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "52ce06d0-c853-437e-9ebb-746c9d466e56")
	)
	(wire
		(pts
			(xy 60.96 80.01) (xy 71.12 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "53c838a8-d146-4d4e-953a-eb2719681ff4")
	)
	(wire
		(pts
			(xy 149.86 16.51) (xy 149.86 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "545ecb70-ab31-4943-b2a7-adf5cde30eb9")
	)
	(wire
		(pts
			(xy 60.96 80.01) (xy 52.07 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "54f76e07-8231-4f9e-b133-d3c64b2ea096")
	)
	(wire
		(pts
			(xy 149.86 80.01) (xy 140.97 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "562b741f-4f90-4b93-8c47-b854339f71ae")
	)
	(wire
		(pts
			(xy 101.6 15.24) (xy 111.76 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "579e0589-4ec3-4b09-bf77-103d55c88376")
	)
	(wire
		(pts
			(xy 172.72 80.01) (xy 181.61 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "57a00e57-b042-4985-b9cc-9f137b356457")
	)
	(wire
		(pts
			(xy 113.03 101.6) (xy 113.03 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "58d694ff-2146-425b-b440-2c7f017e0026")
	)
	(polyline
		(pts
			(xy 208.28 12.7) (xy 208.28 87.63)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "*************-42cd-9401-575745dcc0dc")
	)
	(wire
		(pts
			(xy 140.97 66.04) (xy 140.97 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "691eeab5-977b-4a39-8ccd-68f81c2408e2")
	)
	(wire
		(pts
			(xy 132.08 16.51) (xy 132.08 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "6938466f-1844-4261-94b4-d0bb9fed7e3b")
	)
	(wire
		(pts
			(xy 81.28 157.48) (xy 93.98 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "6abff720-a370-443e-8f51-64d0042179f0")
	)
	(wire
		(pts
			(xy 72.39 101.6) (xy 72.39 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "6add64b3-6b13-483f-88ed-f0891de45111")
	)
	(wire
		(pts
			(xy 60.96 40.64) (xy 60.96 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "6c04b33c-9dfc-4603-b9cd-ac7d49240f4b")
	)
	(wire
		(pts
			(xy 182.88 92.71) (xy 199.39 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "6c2aab58-0bf1-46b0-8f48-b6b98c3c8339")
	)
	(wire
		(pts
			(xy 25.4 17.78) (xy 25.4 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "7045db78-4bd7-42ae-9847-7cca31965c33")
	)
	(wire
		(pts
			(xy 60.96 30.48) (xy 60.96 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "72e72fdb-910f-48f1-8bcb-31839fc16b4f")
	)
	(wire
		(pts
			(xy 142.24 133.35) (xy 142.24 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "743e4dd9-d61e-440b-94d7-2573684de2de")
	)
	(wire
		(pts
			(xy 26.67 95.25) (xy 26.67 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "7a853fd8-ac1a-4ac2-afbb-e16cab1acbb6")
	)
	(wire
		(pts
			(xy 173.99 101.6) (xy 173.99 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "7b2fe416-afb8-4ee7-9470-2317b1b70733")
	)
	(wire
		(pts
			(xy 158.75 62.23) (xy 158.75 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "802f4bae-d353-40e1-bb7d-e134f664b9c2")
	)
	(wire
		(pts
			(xy 121.92 157.48) (xy 133.35 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "80bdae1e-3800-4ba3-b4f8-398f6dedb333")
	)
	(wire
		(pts
			(xy 93.98 101.6) (xy 93.98 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "81ab90ef-5f7e-4019-a910-0663a5b02d29")
	)
	(wire
		(pts
			(xy 102.87 92.71) (xy 113.03 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "*************-40a2-a5b4-a2d92a5daa90")
	)
	(wire
		(pts
			(xy 120.65 59.69) (xy 120.65 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "8330a4ed-7b35-40ce-a4f1-f383f03c6f16")
	)
	(wire
		(pts
			(xy 81.28 92.71) (xy 93.98 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "8462f3d0-19f0-4b46-b612-f98c1f22a4be")
	)
	(polyline
		(pts
			(xy 20.32 90.17) (xy 208.28 90.17)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "84e07620-9220-4d37-a9d6-4bd865ade50c")
	)
	(wire
		(pts
			(xy 44.45 101.6) (xy 44.45 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "85f8fd8a-8062-46e3-8877-4c10c5dc5656")
	)
	(wire
		(pts
			(xy 173.99 93.98) (xy 173.99 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "86ae0ee4-36e9-4cb1-9e0c-7be8a9c69820")
	)
	(wire
		(pts
			(xy 199.39 101.6) (xy 199.39 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "89067ad2-2f8a-4f68-ab33-601218e9b755")
	)
	(wire
		(pts
			(xy 102.87 120.65) (xy 102.87 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "8a71e710-b5e0-4efd-92a1-f776d7583684")
	)
	(wire
		(pts
			(xy 43.18 34.29) (xy 43.18 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "8fe8793c-1ac1-4b27-972c-ce0e26b8e8dd")
	)
	(polyline
		(pts
			(xy 208.28 90.17) (xy 208.28 165.1)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "9053219c-5cd4-4751-a6be-bd5b03635c47")
	)
	(wire
		(pts
			(xy 53.34 92.71) (xy 53.34 93.98)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "90e33c62-9af7-4323-860f-74d7dab27dd9")
	)
	(wire
		(pts
			(xy 25.4 27.94) (xy 25.4 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "90eaf263-25da-4c76-92c3-e780ddb44426")
	)
	(wire
		(pts
			(xy 35.56 101.6) (xy 35.56 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "9213ffea-9c53-47fb-be05-cfdb40b4a0ca")
	)
	(wire
		(pts
			(xy 142.24 92.71) (xy 151.13 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "93622985-c2d1-4939-a2f1-bc5f3161d0ae")
	)
	(wire
		(pts
			(xy 182.88 146.05) (xy 182.88 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "998b6e92-b454-4f26-a5f5-dc1c0c087e4d")
	)
	(wire
		(pts
			(xy 121.92 127) (xy 121.92 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "9a67126e-6846-493e-9359-08b552be57b9")
	)
	(wire
		(pts
			(xy 62.23 157.48) (xy 72.39 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "9fb9c3ba-1bd7-44b2-8804-5cfd1a61717a")
	)
	(wire
		(pts
			(xy 120.65 80.01) (xy 132.08 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "a084b00b-3e11-4d78-bc7e-8ebf6729ae32")
	)
	(wire
		(pts
			(xy 149.86 24.13) (xy 149.86 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "a0905f1d-90d8-4fad-a6a5-45b1cef2c006")
	)
	(wire
		(pts
			(xy 26.67 157.48) (xy 33.02 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "a33d6633-35c6-43a2-8526-96f355ed92ee")
	)
	(wire
		(pts
			(xy 102.87 157.48) (xy 93.98 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "a3a72d6b-f647-4cb5-b285-544794ade511")
	)
	(wire
		(pts
			(xy 35.56 93.98) (xy 35.56 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "a6b08366-706b-4b0b-b9df-04293df4ea8a")
	)
	(wire
		(pts
			(xy 34.29 24.13) (xy 34.29 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "abe7d0d0-c7dc-45e7-aa03-b01ef984c9df")
	)
	(wire
		(pts
			(xy 80.01 15.24) (xy 92.71 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "ae948ef6-f7bc-4b6e-8004-3dd5c84f1913")
	)
	(wire
		(pts
			(xy 181.61 80.01) (xy 198.12 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "aecc6294-7201-41b2-af4e-c0ff25dc3971")
	)
	(wire
		(pts
			(xy 158.75 80.01) (xy 149.86 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "b5218282-637b-4ebb-8e25-8db82055a055")
	)
	(wire
		(pts
			(xy 81.28 124.46) (xy 81.28 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "b6790e0c-624c-4ddf-95f9-0bb51aa32a6e")
	)
	(wire
		(pts
			(xy 111.76 80.01) (xy 120.65 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "b7f2abe5-c205-4084-9e6a-8d6b265bfc2f")
	)
	(wire
		(pts
			(xy 151.13 101.6) (xy 151.13 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "b8043f4d-2322-4935-be3e-ade4157d5c7d")
	)
	(wire
		(pts
			(xy 158.75 15.24) (xy 172.72 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "b9a61aeb-2cc8-4c8e-a4b4-e1d2d11257f4")
	)
	(wire
		(pts
			(xy 25.4 80.01) (xy 34.29 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "c21f4f02-a39a-473f-b323-a168d37f2e8f")
	)
	(wire
		(pts
			(xy 160.02 157.48) (xy 173.99 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "c32d025f-223a-481b-acf6-dfb2c984eea2")
	)
	(wire
		(pts
			(xy 92.71 15.24) (xy 92.71 16.51)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "c4448915-f2bd-4a63-a2c2-24060ad34b87")
	)
	(wire
		(pts
			(xy 52.07 15.24) (xy 43.18 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "c46f9bdd-804d-4325-9b81-199addf35155")
	)
	(wire
		(pts
			(xy 92.71 24.13) (xy 92.71 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "caaca610-3e80-4f17-bcb8-e4eae1306891")
	)
	(polyline
		(pts
			(xy 20.32 90.17) (xy 20.32 165.1)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "cafd0f86-2ab5-4090-a163-04c251064c16")
	)
	(wire
		(pts
			(xy 80.01 80.01) (xy 71.12 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "cb0568fc-198b-46f0-ac51-fbc63148fe23")
	)
	(wire
		(pts
			(xy 182.88 157.48) (xy 199.39 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "d1cc27a1-1125-4cf8-98b6-a1a2c4b4ec2c")
	)
	(wire
		(pts
			(xy 182.88 156.21) (xy 182.88 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "d311ecc6-355c-44db-a2bf-1a1acbab84fa")
	)
	(polyline
		(pts
			(xy 20.32 12.7) (xy 208.28 12.7)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "d5ec2ada-f27b-4219-a049-9b3033c2b5d2")
	)
	(wire
		(pts
			(xy 101.6 53.34) (xy 101.6 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "d9e17061-23db-4384-b8c4-181cbf24f8db")
	)
	(wire
		(pts
			(xy 111.76 16.51) (xy 111.76 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "da189438-8675-44ef-85f0-11d6af8d23d3")
	)
	(wire
		(pts
			(xy 52.07 24.13) (xy 52.07 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "db0bb5b9-13ba-41dc-a9ad-14faf385f29d")
	)
	(wire
		(pts
			(xy 62.23 118.11) (xy 62.23 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "ddd9a067-596f-48ce-8d8a-bbc46073dd84")
	)
	(wire
		(pts
			(xy 121.92 137.16) (xy 121.92 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "de87babe-63b3-4bb0-8772-fed8a8ffd71f")
	)
	(wire
		(pts
			(xy 101.6 80.01) (xy 92.71 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e0a82756-c1e8-4c87-b3f9-b194d3bd4138")
	)
	(wire
		(pts
			(xy 181.61 15.24) (xy 198.12 15.24)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e10388f1-67df-40ba-a8e0-1fbb4997f232")
	)
	(wire
		(pts
			(xy 158.75 80.01) (xy 172.72 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e3f04829-4ae3-415a-85d1-2d38929c9c64")
	)
	(wire
		(pts
			(xy 198.12 24.13) (xy 198.12 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e4e963c8-0171-4cde-988b-492992392e98")
	)
	(wire
		(pts
			(xy 43.18 80.01) (xy 52.07 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e4eff686-2841-46b2-9a63-054c69a5a91b")
	)
	(wire
		(pts
			(xy 199.39 93.98) (xy 199.39 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e55d5cf7-8ddc-412c-b7f8-51a4c418a2a9")
	)
	(wire
		(pts
			(xy 62.23 107.95) (xy 62.23 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e717fa96-15ae-433a-bdd4-71dadcaed3c0")
	)
	(wire
		(pts
			(xy 81.28 114.3) (xy 81.28 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "ea02abf9-c1bb-4759-86d9-3fb44e6feb3b")
	)
	(wire
		(pts
			(xy 34.29 80.01) (xy 43.18 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "ee2fc6b4-33b5-4869-86bb-ef10a2cb9dd8")
	)
	(wire
		(pts
			(xy 93.98 92.71) (xy 93.98 93.98)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "f3edddde-e44a-4a77-9e7b-4ab450efb447")
	)
	(wire
		(pts
			(xy 113.03 157.48) (xy 121.92 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "f4c9250a-5004-44f1-9b9e-2bf9987b321b")
	)
	(wire
		(pts
			(xy 160.02 139.7) (xy 160.02 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "f5277673-322f-4839-bce3-6698d8a58628")
	)
	(wire
		(pts
			(xy 33.02 157.48) (xy 35.56 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "f6bba47c-1723-4bff-8c72-81b324274870")
	)
	(polyline
		(pts
			(xy 208.28 165.1) (xy 20.32 165.1)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "f6c60e8d-e054-476d-a824-7cf3b34254ac")
	)
	(wire
		(pts
			(xy 81.28 157.48) (xy 72.39 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "f6f840fa-1c7e-40c4-b856-b91a3acfe9b1")
	)
	(wire
		(pts
			(xy 121.92 92.71) (xy 133.35 92.71)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "fbdf9d0e-ba71-4854-bd29-4a31c52273d5")
	)
	(image
		(at 246.38 33.02)
		(uuid "bfa2aa0c-8730-4906-9ef4-dba33beae055")
		(data "iVBORw0KGgoAAAANSUhEUgAAAzIAAAHcCAIAAACChgihAAAAA3NCSVQICAjb4U/gAAAgAElEQVR4"
			"nOy9TY/bWJrv+bBRqwsMxov8ANNVpN02HJtaZI0Yy0bmjBQxgG6hS5sCnIoLDNW56WCgEAkkEEah"
			"4AASU0JNSL2YvCIGbdlAbJSTtwJIh4SpRK0aZtysRQODMOy2yar+ArnQxd30xslZ8EWHr6L4eij9"
			"fwjY0uHh4cPDw8O/nvMcHmG5XBLR9fU1AQAAAAAQHRwcZNntW/XO8N6//OHTHxdsTlnlcsiPvE9/"
			"/dd/XaMdAAAAAOCBf/u3f8u450cH/V/84u+//F8YAcUoqr98+fFPP/8T0YdffPHg89/f+5c/fPrj"
			"b9U7w3tfPPj88ykRffiFo7y+Ve/8YkpERP2vlqOPmAO4RYS3bAsrWSYIQo12AAAAAIB/vv/++3Di"
			"Bx98QEREH42W//Llxz+983lYNn2r/vTzB18t//AR/eXLj39K9IWT/qfP354ulyP6Vr3zi3/89tPR"
			"R0QfjZbLEdmS7st/+IjReP/oFLHOnuaykmV/9Vd/VaMdAAAAAGgErgiL5Mef/mH56V++/Pind1RW"
			"mf3FfP3hF//5IzvHaf/zobvhwy/+4SMioo8O+jQ0/0If/Zh1l334BVv2T+59OP3Fx/7hzERjmsca"
			"Wfbu3bsKjSmMu3fv1m0CAAAAsKv8+NP//MXvf3r9bZZxxr98+fEv6Kvl8iP6y5cf/32gWFvy3bnz"
			"p+0fxAzLsn/913/92c9+9pOf/KRak/Ly5z//+bvvvvubv/mbug0BAAAAdolvv/zyJ59++mMi+sv/"
			"+/s/ffgff0L0Z2fTj8UHf3IGKf/y5XC6GsQM8ee3f/rw3k+cMug/hrb/+NM//At9/PeOY23biJVl"
			"r1+/bqImIyLb5u++++7Bgwd12wIAAADsDB+Jb+/cuWN/7n+1/PTHK1lGH42+6t/5xZ0p0YdffNGn"
			"38cX8g9fDH/60zuf04f9/of+Tb65AFuoyYhI8F6Q8fDhQ3bDq1evGirLyHWYBc4IAAAAAGt59epV"
			"wgsyvv/++7zhXH/58uOfvj3dzkHIvGxzyP/2nREAAADQdL79x8//9OEXjfT6lA9kGQAAAADKZvXK"
			"MW+AE4TZXJaZ431J1b2v8sh4eSwuBkJHsz+mP/ZiIHQ0ZW5N2un32QTIMgAAAIAPfvzpH5af1m0E"
			"/2T1lq3UmCoN7lrddfmDEiwg7tLym9/8hoh+/etfr00kyDIAAAAANIp8g5jSA5kojbhaXGm+rwNJ"
			"fTgakbqpMvv1r3/9m9/85je/+Y0nwuI0GUGWAQAAAKBR5Ft8yXitE8kPJF/iYiB0HBHGjHASEWkd"
			"wfaYtSeWReZ4X81gMavMEjQZYTkpAAAAADSKrN4yXZUElYhImb88FmnhptsSTJlbk7Y53pdUaXDX"
			"mkysOXnJBRjtKTOK12QEbxkAAAAAGsUaWfb+/ftQ0g8WEbX+z3/9538QvTzvLSKyfnj/4vcaEf3v"
			"/9vH79+//+v/9e9apGu/f/F/fdx+bxERWe/Z8uyC/Gmbk7A3ZBkAAAAAGsTmsuwHW0398AO7zU37"
			"wZZf9jY35/v3733pgYI2l2VffPEFEX3++edffPHF+fn5559/HpkNsgwAAAAADSKLt4yIiH7webms"
			"H+y0jw7/E/3f//Tq7bv3H/3kzy/+n/9K9J8OP3r//v2P7/7PRP/1B8vvLSMi8qel4Le//S0RffbZ"
			"Z+/fv//ss89++9vffvHFF5999lk4J2QZAAAAABpEBm+Z9YP9b8hb9oP1w/u/Hf/3/2L9Dz/f+w+f"
			"ERF9+H/8f+O/ff/+Pf1Pn3529NnP/6n7H/7p6L/893/8iCloM2/Z7373OyL61a9+5e30q1/96ne/"
			"+91vf/vbX/3qV4HMkGUAAAAAaBCrNTH/9m//lt3wxz/+8Wc/+1neda9q4vvvv//uu+8CZwQAAACA"
			"tfzxj38sd01MEM/m3rLmAG8ZAAAAUCXf/vGf6zah2UCWAQAAAKAw/u7nh3Wb0GAgywAAAAAAuACy"
			"DAAAAACAC1ay7Pr6Orz53//93ys0pmAizwgAAAAAgE9WsuxnP/tZjXYUzpadDgAAAFAN3333Xd0m"
			"7C4Y5gMAAAAA4IKVtwzqGAAAAACgRlay7Je//GWNdgAAAACABy4vL+s2YXfBICYAAAAAymcxEBwG"
			"CyIyx/vC/tjMX2j+QjgCsgwAAAAAJWOO9zs0tyzLsizjwbsFkXj80np5LNZtGGf8aH0WAAAAAIA8"
			"GK91+YFkfxaPocbigLcMAAAAACXT7iq6+olvuJEZfzTH+4IgCML+eOwmLgb2Nyfd9PZhB0K3D8gy"
			"AAAAAJRNe2IZvZkUJagWA0l9OLcsy3pGM22Vrquvu5ZlWXNFV4cLtxTLTtLOtymkzAOyDAAAAAAV"
			"IB6/tCxj9KrjV2bmu1fy6LRt5zhTVhvc1HZXoVfvbBHmuMs6jHrbKiDLAAAAAFAV4vGzkaxdZRqC"
			"XM0bMEZy0YbxAWQZAAAAAEpmMXbHHM3r2Sr6n4hIvPvQHaQ0x+dJbjBv3oB5PdPLs7VOIMsAAAAA"
			"UDLtu68lO1ZfUh/O/S/GaE/mitYRBEH4hHpKbBFE7dMRqZIgCJ+8fril3jJhuVwS0fX1Nd7yDwAA"
			"AIDLy8uDg4O4rd9///0HH3wQt/XbP/7z3/38MPuxzfG+9PrMmrSzF9Fs4C0DAAAAABcshqp/gHPn"
			"wOtkAQAAAFAj5nhfUp1YMWVu7fS7ZiHLAAAAAFAj4vFL67huIzgBg5gAAAAAAFwAWQYAAAAAwAWQ"
			"ZQAAAAAAXABZBgAAAADABZBlAAAAAABcAFkGAAAAAMAFkGUAAAAAAFwAWQYAAAAAwAWQZQAAAAAA"
			"XABZBgAAAAC+WAwEeWT6vg8W9kdzJHufA5sKPmgdQJYBAAAAoDQEYbM/IiJqd5WbN4ZXxuJKU7pt"
			"IiJz9GjWMyZtd4s5enJ7cdqmImhPjN7sUb3CDLIMAAAAAJwh3W9pV64TjFFlL2bUO1ytZW6+mO09"
			"Vgtb21xUH++dDIvxvWUDsgwAAAAAnCEe9jxdlqzKuu3gwKY7GLkYCA7OtsVAkEeLkeymmc5HZuxS"
			"ut+6fVujvwyyDAAAAAC8IR72HIG0UmVkvLnZu7dSZYvhyV63TUSi+ljxnGuLK015rIpE7YllWZZl"
			"zRXtiau7bk6e0HM7rSM8ouf29hvPRSbe22NHTysHsgwAAAAA3CEe9mj2wiTz7W3rvhSVY3G1Citr"
			"d11dxqQ67rKOttqndfFcFe381HIcb+2uQp6LrGZ3GWQZAAAAAPjD1mWLwLilhzl6cstsaJ9e3D4Z"
			"matUcyR3aG5ZlmVctNIfNeCQqxrIMgAAAABwiHjYo5POCaPKGFdWKNhfPOzRbDj0Uo03N7aXzXwx"
			"u0l9zHjfXDVAlgEAAACAR8TDXssbanQSaPbCpFWwvz83aZqX2j69oBNJEIRHb/bSe8tqdpaRsFwu"
			"iej6+vqXv/xlfWYAAAAAgAsuLy8PDg7itn7//fcffPBB3NZv//jPf/fzw3LsIiIicyQ/ouf6vaH8"
			"9lQv7s0Y/tILLzc9P6rtyAAAAAAAGyGqz3uyNHhs6WrhZS8G0sne3KrRVwZZBgAAAIAGIaq6VU7J"
			"7YlVUsnpQWwZEckjwzJG8pokb4MxkkmZR24FAABQLvLIsNy3UZVRPrp3UCeQZUSkq8OFeNBj7kO5"
			"dyAuhqoet4f8oMZpGgAAsKvII+PlwfW+/ZJ2mpQgoNC9g1qBLCMiIu18TIwuk3sHND7XiJjfZczd"
			"f/fs2bEoHr80RnLkDzdlbn8fjQwnzUkp68cdAADsBHLvgMafOD+ZtfP94Yy8QQzmkzwyjPncsCxr"
			"rrCfKdQbyyNjPp+vUuQR070DUD2QZTb67JqOzxzNpJwd0/VMJ/sONQaCIAgD4/iZe5u+O/9kbJrj"
			"fUnV5dEz54fbYNHuKkREynwijfcFQbi6eywSEckj4/Sd9+MOwgwAADLy8K5ovPbGMXRd0+MGNUTp"
			"3Sfu6929z5G9cZuuVn24rnrdewWnA0AIyDIHXR16uqrbdgcwH94VF1caEZF2tRDvPozYTfqEnlmW"
			"ZU3aJD2QieQHkmlrOnd5rod3RfH4pcVmAgAAUCZOP+z/HNkbm+9eERG9emeiewb1A1nmoV0t2l2F"
			"5NFp25FiKVDm1su7Q2Z5+mhWq9gL+A0GAAAZWaOdHt5d+2oD9MaAbyDLVmjnY+l0fuaFlRHRq3fm"
			"yoXm/KQKYms4d3V70l8bzvQBN+nVO7N9OpLJjkPDICYAAGREn12TF1CizL2oX3swY220PnpjwD2Q"
			"ZQz67JrabWJc37r6yViaWJZlTWjA/LLSXxt2TKh2tWhPLMuyurRwOgatMzCOXzpJdiGSk/Ly2Bh0"
			"0jriAAAABFj1p5Y1kewYMF0d2h3xs7uGmXLv+N7Y695LsR+kZTEQ5JHp++4OSpkj2TdAxWxKX976"
			"DXWxXC6Xy+Xl5WXdhmwfq9lBAAAAQFO4vLxcxmOaZsLWr77+xvJDFm30t5oq602bZb8ZF63WhbEq"
			"Pvg9hrlCcdlSllAR8JYVjteOXh5cf4LQBQAAAGBjpPstd+Ic0eJKc+KCzBczYpYuJ/PFbO9xviUs"
			"RfXx3slwrb+tIiDLCkfrIJ4UAAAAyIN42PN0WbIq67aDA5vewKQ5kgVBEAR59JYp2k31JutJ91u3"
			"bzkZyIQsAwAAAABviIc9RyytVBkZb2727q1U2WJ4stdtE5GoPlY859riSlMeq6K78rhlWc9p5kYS"
			"miNZmvWcMcuJXap4b+/mjVHdqSUBWQYAAAAA7hAPezR7YZL59rZ1P3KS7eLq9uLUeQ1Cu+vqMjfV"
			"fHvbcjaL6mNn3q35YnajhEY9OXKX/ahuAwAAAAAAQoiHPXr0YkEz6j2PiB4zR09umQ3t04snj0am"
			"RL7UdBhvbva6uQLUCgPeMgAAAABwiHjYo5POCRNNxri1QsH+4mGPZsOhlyre27txQvnN0RPNy9TS"
			"ngTeiBHvj6ue3fWW9fv9uk0AAADQDKbTad0m7CLiYa/FqjLHgWaqqmi+mO119WBuOjnZm0+c7+3J"
			"XBE6gkbUurhQaGZnUvX5G0ESToiIlLk1aXPlLNthWUa4zQAAAKQAP+PzYJGVfWdR1S3Vl+DosnvD"
			"We80+LaDUO72xLJckaaqUalkD4ZePJ8QH+y0LAMAAABAkxDV5z1ZGjy2dHV95hQ40zU58ZUhtoxP"
			"OP9lBvPyAPMyw7NtBPPywbl5gCtEVXffbFEA7YlVYGn5gSwDAAAAAOACyDIAAAAAAC6ALOMRzuci"
			"wLw8wLzM8Gwbwbx8cG4eAJUBWQYAAAAAwAWYiQkAADHkmNcPtoqjug0AOwO8ZTzC+aQkmJcHmJeZ"
			"Sm2zoMkAADUAWQYAADEIG/z1j/ob5a/4D+bl+gOgKiDLAAAAAMAXi4EgsytXLgbCYGF/NEey9zmw"
			"qfjjVs9yuVwul5eXl3UakQp5ZFgOcyWwTZlbzDZ5ZFjGSA7u7E/iebAGAFAzGMQEDLv2vLi8vFzG"
			"Y5pmwtavvv7G8uPeTmn/Vg9z/6Pd+WZctFoXxqr44PfcFF7gZjTGWyaPnh0bA0EQBGF/LE38wkx+"
			"IJnjfUEQBEHoaKSrw4V40GNEmNw7EBdDNbh8FgAAAAA4RLrf0q5cJ9jiSlO6bSIi88WMXbqczBez"
			"vcdqkUsnierjvZNhYe63TWmMLNNVSeho9sfZtSk9CIgu4zWrubTzMTG6TO4d0Phcq8pWAAAAAORB"
			"POx5uixZlXXbwYFNbyTSHMm2x0YYLMgcye4ApZNhMRDk0cLJsxq7lO63bt/WNZDZGFnGoJwdGz7P"
			"18O7oimd2u4/x42mz67p+EzxdqDrWYNcZZw7zGFeHmBeZni2jWBePjg3D9SBeNhz1NFKlZHx5mbv"
			"3kqVLYYne902EYnqY8Vzri2uNOWxKtJiIM169nDknJ6MSNXneyfDBS0GHZrrtovt5uQJPbcsy7ig"
			"k0eOMBPv7d28MSo92RWNe2+ZMrdO3+1LPs+X1hFeybKu60TyyJgrmjOQaXUV0jRSuu3FsKNT6M5n"
			"v3rvmPYS600JG8mVhf1+nyt7yP+WcDuRE3uQUmCKR9nHmtI0w7FwX2x3CqgY8bBHj16Y6uHb29b9"
			"06gci6vbi9OJ/bndVTpXi0m77aWab2/pRpOEEzuD0iVqT+ZXgtBR5pa7OHnr4rkqkq3rTq4MIpE8"
			"d1m7yLHR1DQn5D8qbj8qi5dDmbvx/6EpAsT3jzOebSOYlw+Yl5lKbds85J/nqiOYlw/OzSscLkL+"
			"LcuJvp+zIfjsRIDo4P9VqnHRYmcNMENqbupcIa8ENrd/ukG1NEeWxckrcvWXm2uVSR4ZxnxuRCu5"
			"XbvNAAAbgJmYgGHXnhfcyDLLuGgRESO+GClmXLQC2sm4aLUUZZU6V3z7Wq7e8lQXk4FVZbXOxWxM"
			"bJlydixSe+LabYzklRzTOgNytjyjTzreAKc+u6Z2u1lhZQAAAAAgIju+jFpMjL942KPZC5NWwf7+"
			"3KRpq9T2xLigE8kN+h+N5M7txWmb2qcXtx0nwr+19+aRIAiCdLI3nzg7BgLYqkVYLpdEdH19/ctf"
			"/rImG2oCP4UBAACkoH/U36kIs8vLy4ODg7it33///QcffBC39ds//vPf/fywHLuIiMgcyY/ouX5v"
			"KL891XO+GWMxEJ7cNwKlOAco9KUbG9C4kH8AAAAA7Cqi+rwnS4PHlq6WUfxiIJ3sza26fGXNfEFG"
			"MfC8BBvPtsE8mLcTttlwax7ntbd15gGuEFXdmrTX58tEe2KVV3ga4C0DAAAAwA7Snlh1KrBIdtdb"
			"xjOcBzHAvDzAvMzwbBvBvHxwbh4AlQFZBgAAAADABZBlAAAAAABcAFnGI5y/uhDm5QHmZYZn2wjm"
			"5YNz8wCoDMgyAAAAAAAuaJAsk0feUgjhRZiYhawi185MsZwmAAAAAECtNGVNTGZJzPDqmMrc1lze"
			"FjchYm8X+MwBALFgTUzAsGvPi2LXxMwGu4q4851ZstK3kngBC4sHj5Y3Xw4a4y3TVUlwVrvUZ9em"
			"9IAVXQ8k83qmM1u08zEd9Lwscu+AxudaqFAAAAAAlEuWt/e2u8rNG8MrYnGlKfZal+bo0axnrN74"
			"ao6e3F6cVvT6sfbE6M0e2atplkRjZBmDcnZsDFVm+fGHd0XjtU5EpL82xLsPyV6l/PhM8XbAeuUA"
			"AABAY5Dut7SrhfOFUWUvZsQsXU7mi9ne4wrXrxTVx3snw8X6jFlp3Fv+lbl1+m5fWuv50tXhwuoq"
			"pGmkdNuLYUenkCOa/eq9zNBLRApSykjhvNX1+337X07sqStlSs5XTuzZ+hTO7wsC1SMe9lonV4tJ"
			"u+2osgmRo8qe+1VZVycyR7L05rG7bJKzBPm9ofDk/rw365zcELUu7DXJ2eXJw0uVmyNZOrkhIlLm"
			"1qQdkUDS/dbtW5PaZUnBpsSWEcXH7csjw0lefSIiZW7NlaiwMiLiO1aAZ9sI5uUD5mWmUts2jy3j"
			"ueoI5uWDc/MKp+jYMtrwz8G4aNmBXEzwWCCOLHqL+3GuEDmxYMZFi7zCvPAw77P7IRimxmSeK6tP"
			"uWPZEmjOIKY8Ml7eHQqSGh6M1F8b4kFPJpJ7B+5wJhFp52PpdH6GsDIAAACgaYiHPZq9MMl8e9u6"
			"L0XlWFytwsraXcUZ9GRTWxfPVZGIRPWxL1YtBul+S+vIXuyY+faWbk4kQRAEoaO5+zvuspJojCxT"
			"zo5Fak9cOWmMZNcdRkRaZ2Acv7Ssl8fGoLOSYPrsmtrtBoaVce4zh3l5gHmZ4dk2gnn54Nw8UA+2"
			"LlsEosk8zNGTW2ZD+/Ti9snIDKR6md/epjmiqlvWc3okCMLAiR9jHGPOEKnx5mbvXmnRbI2RZVpH"
			"YJFUnUjruJMzvc0dn19MVyUh0r8GAAAAAL4RD3t00jlhVBnjqQoF+4uHPZoNh77Um9kL08l8Y08a"
			"kO63XLfX4ipyKE1UdeOidfvWJPHeHmlPAhMv4513hdAYWQYAAACAnUI87LWoxTi/3IFNJ9i/HcxN"
			"muZLbe29eSQIgiCd7M1tZ5eoPlYcT84VBQPPFwPH9XNia7v2xLggZxTTdaCV6yxrVsh/ofAcwsmz"
			"bQTz8gHzMoOQ/zzAvDxwbl7h8PA62ViciQCrCPx4ynj5qzcPoSwa94IMAAAAAOwqovq8J0uDx5au"
			"Vn/wxUA62ZtbZb4mDbIMAAAAAI1BVPW6lkZrT6zSD41BTAAACII1MQHDrj0vuB7E3HYQ8g8AAAAA"
			"wAWQZQAAAAAAXABZxiOcO8xhXh5gXmZ4to1gXj44Nw+AyoAsAwAAAADggsbJMm/BpWCqi7M6eWBJ"
			"87hVzgEAAAAAeKFRMzFt9RWWZfLI8GsuZe4XYfLICO4GnzkAIBbMxAQMu/a8wEzMGmmQt0weGd0r"
			"b/FQ35begWi8Zle+1M7HdNCTmQw0Po9c+woAAAAAnLEYCDK7GOVi4D3/zZHs0wLMpkKOGTx0tTRI"
			"lumq1IlWVg/viqZ0autMxyemz67p+Mzxjylnx3Q9w4LlAAAAQNUIG/4REVG7q7grihMRLa40e6Vx"
			"MkePZj1j4q17aY6e3F6c+hfHzEd7YvRmj+oSZlvxln+tI7ySZV3XieSRMVe0jka6OlxYXYU0jZRu"
			"ezHs6BRyRLNfp9NpIBEpSCkjhfNW1+/37X85saeulCk5XzmxZ+tTOL8vCFSPdL+lXS0m7TaRo8om"
			"RETmixn1nq9WPzJfzPYe6wWvhiSqj/eE4UKdFKn20tKo2DKKC/n3YKPM7KwRYWVExHesAM+2EczL"
			"B8zLTKW2YanyaoF5XFFwbBlt+OdgXLTc+Xxzxf0UXCncuGgpc39my1ujfK5Q62J+0SIichYtX5XE"
			"LmTulOJPKXdF8lgaNIgZj1+qeVFm2vlYOp2fIawMAAAAaBjiYa91+9YkdgSTjDc3e/dWvrHF8GSv"
			"2yYiUX2saFdOhNniSlMeqyIR0c3JE3puWZZxQSePRia1u262xdVti2YvTLI9cPcl/8Hv7bFjqBXS"
			"aFnmyjGtM6CJLTOf0SerADR9dk3tdgPDyjj3mcO8PMC8zPBsG8G8fHBuHqgF8bBHsxcmmW9vWwHV"
			"5LC4WoWV+QSXl9q6eK6KZMu2mzcGkXTf1nqLq9veY6f8FzPqHQbGQd18ldO42DKtI2jhz2wqg65K"
			"glqRYQAAAAAoDvGwR49eLMgfTeZhjp7cMhvapxdPHo1MiXypXua3t0Rdt0zz3pvb3unkkJ4MDTOy"
			"fOPNzV634JC1VDTaWwYAAACAbUU87NFJ54TxZTFOLPPFbM8Zqlzlng2HvtQbe5iSzBezG3sgVDzs"
			"0ezRk9veoUjiYe/2avgm7CtL8NCVDWQZj3AeXgrz8gDzMsOzbQTz8sG5eaAuxMNei1qManIHNm1V"
			"1m0Hc5Om+VJbe28eCYIgSCd7c2depXjYoxtHiYmHvVvtNqzKgjFsFdK4QUwAAAAANIc8C2aIqm75"
			"YpGcQUj13nDWOw0GjodyE90/1a1JfC7fHu2JZSs3c/Tk9uJ5YLeKgCwDAAAAQEMQ1ec9WRo8tvSy"
			"QscXA+lkb27V4iuDLOMTziclwbw8wLzM8Gwbwbx8cG4e4ApR1UtdsbY9sWpcEReyDAAAAADbhzco"
			"2SQQ8g8AAAAAwAWQZTzC+aQkmJcHmJcZnm0jmJcPzs0DoDIgywAAAAAAuKBxsixmqXJm6VEieWRY"
			"3oLlNhFJAAAAAABcYS/5fnl5WbchabDFV1iWKXNbc8kjw97qJrh4G1bAZw4AiMXK97IlsF3s2vPi"
			"8vJyGY9pmglbv/r6GwvkoEHeMnlkdK+EwSJiywPJvJ7pRPrs2pQeyETa+ZgOep4uk3sHND6PWjUT"
			"AAAAAIATGiTLdFXqRCurh3dF47VORKS/NsS7D4lIn13T8ZnjH1POjul6FnwbcHOwLPxRna+R4Yza"
			"LwQnf8Cm9gvByR/YMhYDQR6Zvu+uW8YcyT4XDbNpfaGC4JTLft7IkpLZ2veW6epwYXUV0jRSuu3F"
			"sKNTyBHNfvVeZugl8pCCviYAV1cnW0qeVvf06ZSAi11FJV2vKU0DByrvWJlT0B4iKa/mQTaE/7ZZ"
			"fut/JCJqd5XOlUHkvGt/caUp3QkRkTl6NOsZuvdCsvQLJS0GHU2ZW5N24PMa2hPjrfxodKir1bz3"
			"v1GxZRQd8i+PDCeSbPXJyxoRVkZEfMcK2LYV8ivQi5ApMFSmlqpLXxU8X1nKYV7+9mClaA881x5r"
			"W+kOks1vmIqrbtP2EDYvTXuojJy1V3Z74Pm+KINiY8toudmfg3HR8mbzWXPF/WxctFoXxqp046Ll"
			"5VrDXCFvV/Zzul3THiUvDRrEjEd/bYgHPZlI7h24w5lEpJ2PpdP5WcPDygQh+752NyUw/8L71lC8"
			"R06e9uCUwPyL9tBQ0B7A9iMe9lralT04ubjSlG6biMh8MaPe4cptZb6Y7XXbwYFNZ9zRHMmCzWCx"
			"GAgdjW5OJEEejZjPppN74eQdLFa7rcYupfut27cVDWQ22VvGfPa9IMNDHhlW3HsxOP/1U6yrLPJr"
			"s9jx8JFCTh/tYZMDcF07aA8B4C0rFi68ZYxjjPFVBdxW0Vucj0EXV5y3bK4QkXck96M1VyjKXVc2"
			"jfOWaR3BC/xnPmsdW9v6JwXoqiQIktq4YP8CfwqDLWCX9SgIg/YAdgTxsEezFyaZb29b96WoHIur"
			"24tTJzys3VUc55qbKt1vaZ100fqti+eqaBdCLccb1+4q5LnIqnOXNU6WbT9FaTJ2BJOYz+jSmwXa"
			"A2BBewA7hK3LFoFxSw9z9OSW2dA+vbh9MjJXqaKqW9ZzemQPTebDeHOzd6+SkH/IMk7h2U/GuT9/"
			"K82rrD3wXHs820bVmpehPaD2QAMRD3t00jlhVBnjtTJfzPYes7MjxcMezYZDf6qo6sZFbk9XvMOu"
			"cCDL+ML+KXx01K/ZDsAHdnvgWaODKkF7ALuGeNhreaOKTgLNXpi0Cl/fhKcAACAASURBVPb35yZN"
			"81LtN5MJgnTil28ZqM5Ztr3vLWsiBYaMhEcoQONAewAsCCkDDcV+D1lGRFW3VF/CYY8evTDVe8NZ"
			"7zQYOe7P3Z5YFvs+s/bEamf6nP7VaEXQtJmYhcGbz7zYd1UnTKpq6HyrXZuJWU17aGhjoN2biYn+"
			"IRnMxCwWztfE9L3RrHx8MzLLB94yvsDwBGApuz0I7mO4pOOUV/JOganZALCIql7lj4f2pFKfAGRZ"
			"nVgRnxqMdxJ4dmRjm9qDxXxAe8hGoBU0XZOhfwAgDQj5rwcr6hnsdbucO8zjVnFhv9ZI42qP1rWH"
			"Kimk9qzEr5lp4pXNTOEavcbaS9M/cH5xAaiMRsmy6Ff5s1vsjfLICL7bPyKJAwQq5RlcV3y3gN/B"
			"+ailPZT3tiq0h8yUqtHRHgDgmQbJMmU+kcb7grA/liYBYSY/kMzxvveef10dLuxFMr0MvQNxMeTl"
			"df+r8R3Grd/cYau4WHKQkoi6QnvYYSL8ZGgPAOwOjZmJKY8Mx921+rTaFPSg+fOE9iA+XPre7KFi"
			"Zz+tLa3sw5UxmWuLZ2KiPWQ5yvbOxER7yHIUzMQsFM5nYm43zfGWPbwrGq91IiL9tSHefejfZEqn"
			"9gk58kyfXdPxmbuO+dkxXc+4cpUJeAsRIKJI1ynYYdAeANhxtmImptYRXsmyrutE8siYK5ozkGl1"
			"FdI0UrrtxbCjU+gXD/t1Op0GEstKmU6J6XOPjvr9PgnTqf0L8qigY3mJZZ+XfR5H/X6fyXPU7z+d"
			"TtkpeKXXasNTAiFE3nsrjvr9Ao7FtIroo7vp+Y/1dDql0HU/IvLaQ2E1VpzNkSlTWh2o0pZgw7QH"
			"7wp6N1TOY9HaPKFuJPOxvPbA5mGbd+aSQymF2bz+6gBQKtswiBmZi0iZu/H/4TkC9TmlrZg3Q7KO"
			"/Zy2pRkjyDOOwJoXV06N4xR1XdmUhGtvbXvIyUaDViW1vUJOJ2BbueNWm1ucv+GV2h4886ocxEzf"
			"HgqovTLbA+e9SuHwMIg5V6h1Yfi+u5P7gq+TZTYlYVy0/EUmHa4+mjOIqb827DB+uXfgDmc62PrL"
			"xdumnY+l0/nZAY3PtWptjYXtNEp6/UHyNKsyjhVJefO5to0SptqFi6+yPUQeC+1hU7amPUSC9gDW"
			"0u4qN28M7+viSlPstS7N0aNZz5h4SySZoye3F6ftiCICiKpu6XFrY7YnRm/2aJRvQfNCaI4sI60z"
			"MI5fWtbLY2PQ0YhWckzrDGhiy8xn9EnH02D67JrabX7CymxiuqLm9lOY9J6f8DO4yvbQ3La3hXDQ"
			"PxR7LPQPICPS/ZZ2tXC+MKrsxYyYpcvJfDHLvRA5ERGJ6uO9k+FifcayacwgZtFU75SOG57wZSjk"
			"KMVly1NI4eOYWzYZE+0h7+E4G8Qs4JhoD3kOh0HM4uBhENM3VrkapjQuWr7BRuOipcyt4MCmMyRp"
			"XLScM1LmFjNQOVeodXHhDLKtiguWXQ8N8pY1n5KHq0DD2LH2AJ9cMlj4EgA/4mGvdfvWJNZXRsab"
			"m717K9/YYniy120Tkag+Vjzn2uJKUx6r4mJ4sucotUlwkPPm5E3Xsixrrtx4LjLx3h47bloTkGVV"
			"s7bPbcpza22QCh7Dadid9gDSkNAemnVDoX8A+REPezR7YZL59rZ1X4rKsbhahZW1u64uc1Ol+y2t"
			"I0fHi7Wc/dpdhWztR0TS/Zb3uTYgyyoijXc9/DqJjY/iL6ckOPfnN8K8jdpDZjK0B55rj2fbKJ95"
			"FYzO9/t9nqUz5xcX1IOtyxaBaDIPc/TkltnQPr24fTIyV6miqlvWc3okCMIgVcxYwBVXD5BlVcDh"
			"8AR+qtbIzrYHtLpI0B4AiEE87NFJ54RRZYxDKxTsLx72aDYc+lNFVTcu0jnB4r1yVQJZVjrswpdb"
			"Q8qeFN1umKdPp86nLWoPIDNb3D9s0QmB2hAPey1qMS4xd2DTVmXddjA3aZqXuhjYS2VLJ+nmanLh"
			"LMNMzPJxZlelVid5JigF9i1vJlSG04ncZSMbuJqJmbn2amwPyXmsPCeV73SyHTdze0h1uKpmYvLW"
			"Hio+UIE5MROzQPiYiRmDM19yrhQ8a5KPeZiWtRWLL3FM5m5i05+b3MiVNZZbUZsiE7OVnz//RqSx"
			"nDWAH1mZwKYnVfZxOXe9eOalt3O1S2J74PzEyybu9HNWS+TuO17VDUNUn/dkafDY0tUCS10MpJO9"
			"uVW/r2w71sQsiDx3ZvSt7h+eqOue30j0BHakHPsK/q9l4FM8zGfeynRKTnclMjw24sRuwi4ZCBeY"
			"oWl56yF6NZzyTK0Npc9asg0erjUgfZ3k6R8y/2yrUXlE2hxoD5F7xYn15HMp+/chqBFR1Qt/prQn"
			"vPxqRmxZcOwm7sLEudDj9t00jDdyCCnBbx+3SYgqKvA5rDzWHo6FB39+bLUXNPoUrsDAX8od2fQy"
			"unzvcNlOmd0rLI9K7aTWVmPgA8U0vOSLEnlPZSChPWxabCBnuH9IuHlTmrc2PU1Rgv9rUcSVlq1X"
			"ieszM9tcdrMHYC2NkmXuG3wjVh73bZJHhhVYzDwiqZI7ME6TpXnAs5kpJnPmPmijp0jgKE+nU8qn"
			"MTZ9WGZ7uuQsIa6oyAxFPRTzWJLnWIXbuWnzTmOG3fAoXYWHNVP+y1R4d8H2D2nU2EYG5LynNr2D"
			"qhE0m17BbDfvRg0YgGJpkCxT5hNpvC8I+2NpEhBmgU26OlzYy5q7yL0DcTFUN10bM/I3cZqfyKwz"
			"lP3pWSNpPBP1Eu4HgynxlbiRpyRDbyukDrdKsCd5l0CK9yFuBCf5sR33pCmDzC08YVFztuRNC497"
			"0G5UAltUeF8h9CHLsYRV5syhAqm6o/iUnL920udZu6OnuQss08vGSRcHQBqaI8vkB5J5PdOJ9Nm1"
			"KT2QEzdp52NidJncO6DxuRZRahkkDF9mfmCEf/dnMSxROiR8FaJGPCPLj0xJCO+IS6+sJ03joghn"
			"q5KANIk0I41qDJSW354MO2arxrXqzUvJEOiWjThZExmuFPiLLCfNEQtpgdnKSfacxUnDODmbx7nF"
			"lpMudVVy5nOHqgNV0hxZ9vCuaLzWiYj014Z492HyJn12Tcdnjk9NOTum61nAVTb1Xh9FFJgQZRHZ"
			"Q6IJ5sRlsKxVD8zmsZxNXh7/XlFmJBw6aSuTL/3Ev7hO1jsieyKrbB6eXrTY4zPnyyzCHDr5Tea1"
			"Rzz3onaPX/M5YP/qc2axG3XKlLq0wNTogKmWP2fwg7+I1Tf3E0U15mDTTDTeu7hxpxbZHsJnEXnR"
			"4++gkA0+/7PbigSfLU62UM4Iou7EOPvj9o3Z6L8mKQuJur8ii117dmzXk2BncJfQmUcf0W9hcKeY"
			"qxldbFTDsOLSwzbEnt2aKx62LaYcAGpja2di6upwYXUV0jRSuu3FsKOTP6p0StOk/dcG6gvCxm87"
			"yPYOb8sK7ljgu8DDhYczbERm29ZaErdX+mzh8gupSbYQ+3PcucSZEZkzaXHEzc1OaELesSKL9RLT"
			"HNQ7/WQKfBYGKj+NJstM+sYmCOv7B9apnlC3XmlpDurhfU5oSOlbY2arMpefvpDkOyWybXsnzs+i"
			"CgAwNEeWvXpnnj6QiXSSH0jmu1drN2lXi0lXoVcPTtuLYYeIiKZs+MLT1UdBEDJ04d5evtElQSDL"
			"/sfZFC6Z3dGKSl9/lHTmxeVfvWggxpJAIRRzaG9rtFXu1gRL1hfC5qRgrcbtG0hPNiA9bC3F1Zjv"
			"6Jb/3QeRzSzq2ZDcEmIPFyhwnaJKsioP65527OUIV2P4NAOXLyJD1IUW/GawF8475diqiy+ckhsS"
			"U0JCr7JBm2cKEdzyI04wvm1EdzVRRUX3Rf46SWgzae6ybD1tsBDWAK+TcRyoQiBb2KQMvYFA1M9o"
			"LAAb05xBTP21YYfxy70Dd8wyeZN2PpZO52fxYWXJ4SkJoSprFJKw2sQqKkpMFJiDHvnniucJmin1"
			"92D0+FSMJWuJPM2ESxC36WiTmfYp45YSLGHtYa2KGFnd3Lzw7plJsD9DA8tsQ7YDpTx3K+bm4pMI"
			"ZeP/LMRsiiS5bmMFors1kCHNb6SU5hXYFwmMwWxKypI3bX4bFQ4KZzEQ5JHp++6uN26OZN/a48ym"
			"gjFHst+KOILW5qA5soy0zsA4fmlZL4+NQUcjIlLmzrsywpuIyA4wa7fDYWUM3l2XUpBRjJyy0iiU"
			"FKWlz5/QJ4a/pjlKOFu2LizOsAxdZ8QuiZW80YVLk22tFItjNUJVebcebsmRjTx5x/CmjfKnwXu+"
			"FlJFGe6joq6LV85Rv8+WGScQizI1zhG+dvfMrbpwNu1h8uy+EVBjBWMPGaf/IyKidle5eWN4ZSyu"
			"NMVe69IcPZr1jIm3GqY5enJ7cdqmCHKLJVHVLT3NcprtidGbPSpGmO3smpgpJRQrA8KfmVDW4Esx"
			"ghkSAlqjbEmj8ayoYuP2itsaaUxcOVb8X/pjxdVD3O4J9bwpkUdPOJGUtgXrMGa+QcIR16bEHTT5"
			"cqc5hbitkfakzJ/S1IQbikKHji3Wii0hz1nb/6095bVFJeTfqJ4jj5K8NVyZKU8nuWUmW5LS/sij"
			"pzQ7vTEp8ycY4MHDO7SrpOA1MeOrOvrPxrhoea8kteaK+zm4dKVx0fJyBZkrVOEylysbc9Egb1k9"
			"JHvRVq6yzYvNTxk+9kinV+aiMuSxQpuKdWwkO8M29aj5Ri0DeVKUlf/USnUYBL7m90RuaoCQ+IwX"
			"yLc5TbtNcAdmaPZxTsTg53T9QxqPZk6SDV7rVCvvKq/N4xHuHwoHfjJeEA97Le3KHpxkfGUvZtQ7"
			"XPmvzBezvW47OLC5GAiyLAsdjW5OJEEYLOykwUAWhMHC9qKNBoIgCALjTzNHsuDgFuX521bbnE2h"
			"BJLut27f5veXQZZlxOtNkhdZCj+/I7VdQzuCuIdZ3BkJKfKEc5ZB5jqP3NH32Ih5BluhnClHDEtq"
			"MOnly6ZKJfmXTPgoVigl2Z61xSYYtinZmmhce0gY4ixJ8aTPmXxvlt1HxWniND2Jj3hnXVw56Tsl"
			"UDniYc/ROStVRsabm717K1W2GJ7sddtEJKqPFVfE0eJKUx7ruuctc0Y8b27vP3e/3Jy86dourpuT"
			"4YLIW6zcsizLuLjt+Ec/F0N3m73/YiDNerYfbk5P7KzivT122DUrkGW5iNNkOR1OfTdIJaUbLs2D"
			"LW7rRgeysWNoyui/Nnc7RlDLcEOgGgPtgT2vjcxLfvbHbQ1LwJQ7kjsjwUqRM3CgzJ6qBKsCDTvz"
			"bIliiTtT9soGf7NlvRzZKLDMDP1DNtLcF9nMSFkbEGQcIh72aPbCJPPtbeu+FJVjcbUKK2t3XV3G"
			"prK0GDdby8nR7ip0+9YkMt/ekqv9RPWx4ldY0v2Wxig18+2t7YcTBKGjuVmLcZdBlhUAJ6+/WeuZ"
			"4MHMNL18xXbG+fyyYBHFt4cEH9LalMqejmWQLEqIy/ZQZIGh4pJ1c6oymXLWYmcrSdSy1VXNefGs"
			"YkHB2LpsERi39DBHT26ZDe3Ti9snIzOQWpAlqm5Zz+kRM2bJxJFNIj15GYEsyw5eEJ2HyMhcanT/"
			"aAX+35hdfjzsTntAt5EN1NtOIh726KRzwqgyxiNlvpjtPWanSYqHPZoNh4HUtIe6t0fuKKg5euKN"
			"mrJZVN24aN2+Ne3MTwITL+OdepuBmZgZ945d3acw0s9p2nT2U10HSignYQ6UL1vJdZ6Z1fpSueem"
			"xR4C7SG8Y3ntId+5oT3kPFxkpdXYHjATs4aZmJYT5tWi4MxL+3vUFEzjouVzY9nv0CJl7p+VGfPZ"
			"uGg5FRDa6hREq9JXmd20gmZiQpZl2rV8TUbb2+1S/N24fkcuZRnbHjKf2vqjoD2Ed+RSlqE95ITD"
			"9gBZlkuWFYujy+ZKhe++SEHwxR2Zac7iS/xRdkiZ4HZGDR7HiUGI6mSbfprO4nulnRraQ7Mouz1s"
			"Mag0kISoPu/J0uCxpat1m7LCmcZZRERbo2LLXPfgypsY3GJvlEeGZYxkNkNEEm3qV139OTFAWXff"
			"4EAWCcVly797zgOFi2L/6q/5TIR/nQuhP5ayf3Zbrg3ZSD8LOOeBAiRXmmdbQUcrBdu8TdtD2XiX"
			"KXPtVdMe+lErztVVaYBzRFW3JpGv9a+N9sQqyqQGyTJlPpHG+4KwP5YmAWEmP5DM8b7gzFUlXR0u"
			"7EUyvQy9A3ExVOMXYQIgC8lvrQO7RpXtodGTcwEAcTRHlskPJPN6phPps2tTesA6vsKLl2vnY2J0"
			"mdwLr1feP+pH/CJL/Ft5VTbccdM/zzZLIGutVSnyrD+1TQ6UoeqK+ktT/xnNo40fcdBkgOXp06n9"
			"Ae0BAJCZ5siyh3dd6aW/NsS7D/2bTOnUN8Cpz67p+MzxqSlnx4nrlW9GBX3udDot/Rg5gHksm7YH"
			"1F5meLbNg1tNxnntcW4eAJWxFSH/Wkd4Jcu6rhPJI2OuaM5AptVVSNNI6bYXw45OofAF9qvXKXiJ"
			"gRTvp3BCnjJSvMSceXKmPJ1OiUio9twjz9ROr71kz1VWds37UkLWZsyzLsVLj8tjn/1Rv9+vqSW4"
			"KSuDiy15umHJ4ZCyCmqDCsqzPiXU1QTyeP1DIefFbQoAVcDzCzJWgfx2HL8Ts7/6FIbdpszd+P/w"
			"HIENA2CreSNG9KHXja3liFbfoJyijpITTl6IwG17KPAyoT2gPdRyoDzgBRlFwfULMrYdrgcxtY67"
			"mHtHI/21YYfxhyPJbP3l4m3TzsfS6fwsHFaWlcqGJ9J3AcXOhksJ5z1UZeZlaw+ovczwbBsRHR31"
			"6zYhCc5rrxDz7FuSw1cbApAermWZH60zMI5fWtbLY2PQ0YhWckzrDGhiy8xn9EnH02D67Jra7dxh"
			"ZfZNzm3IyE7BQ7eL9sAPaA8AgC2jQbJs5T1zhZfWcT96fjXJ9xIMXZWCSZvCyQ+veq2oxSfHJ5y0"
			"h3pBe/BAeyC0B1AOi4Egs4tOLgbeIuHmSPY+BzbVYlrBO/McW1YqaXzmNYaM+MyIl2XVxI5wEjhi"
			"U9YVSXGSaA+FHyU/aA9oDywlXRHOh4ALp9jYsk3f7O3s5l9icvUtuMpRwqpH7NqXBZKz2DXLNDXK"
			"W1Yt/L+VqvDXrBNP3Stv7Fp7AMmgPQBQLtL9lnblOsEWV5rSbRMRmS9m1DtcrXJkvpjtPVaLWPWo"
			"KkT18d7JMNa9B1m2Bm773MpA505NeAYXTpxMh3BnQXtA/wDKQjzsebosWZV128GBzcVAkGVZ6Gh0"
			"cyIJwmBhJw0GsiAMFr7PtBi4kwud/RcDQR6NnNTVgKM5kp2Utysj3cTV3vbuCyd9sFhlWRUl3W/d"
			"vo0byIQsi6begYmAw5w3Pxbn/vzyzCvkGZzfvFLbQ0rzankM89PwIvuHusxL2R74qb1IODcP1IF4"
			"2HPUy0qVkfHmZu/eSpUthid73TYRiepjxXOuLa405bGue6ONzmqVN7f3n7tfVp/bE3doUnviCqeb"
			"kzddO+3G8Ws5a5FblvWcZu7EwlWiZVzcduTV7k/ouV1kR3hEz/1FEYn39m7eGDGnDVkWQSNcI/iR"
			"Whm1Bw+loQk2bgkN6h8AaDTiYY9mL0wy39627ktRORZXtxenzgrh7a6ry9hUlhbjZmM+O+6yDvMm"
			"rZazf7ur0O1bk8h8e+umiepj541c5ttbcuWiqD5WPKnVuniuis7u7oG8ooiS3WWQZbHw3OeWBG9u"
			"OR5oxDPYo1gbw+0BPwY80B4AKB1bly0C45Ye5ujJLbOhfXpx+2RkBlLXYI7kDs2dSPyCzF5PwOfn"
			"A7IsCA9vIQqv9VFXhxj5GOZ8KZIyzCuwPRRiXnntgeeLy4NtCf1DjealaQ9lmFegTOfh4gL+EA97"
			"dNI5YVQZ42cKBfuLhz2aDYcbTQEw3tzYnjjzxewmwZB7e+4YpDl6onmJ5A6cmqMn3kDrWuLdfwRZ"
			"FqARw1VUvj5rSDWUTrPaQ3k/JRpSDaWD9sCWD0AFiIe9lm/w0R3YXAX7+3OTpq1S213FC/mPoX16"
			"QSeSIAiP3uwlecvak7livyH1EfUUL9G4uLVfmyrNesYkpSpLdJbhvWUMnLyFKA7vrS7EfCj1QFbJ"
			"B8pGZe+pQnsIHIjDxkBoDy7oH2zw3rJC4HpNTOe1X3OljFeSVcG2vbfMv/4lm2xZlmVvk0eGFVjM"
			"PCLJD1chRJFdgGdX2f2gkPiVuO+hCjGvvPZQVO2V1B54vrg12pamPdRbdWvbQ0nmFXV/8NzwAF+I"
			"6vPeTBrQRG/U28pcFgPpJHGUtVmyTJlbkU5CZT6RxvuCsD+WJnOFdHW4sJc1d5F7B+JiuH4VJh40"
			"WQJCzOfyDsR3fZQFVxo9gWrag7Dz7cED7YHQHgAHiKoerQWaQHtiJRvfIFkmj4zuVeQQsfxAMq9n"
			"OpE+uzalBzKRdj4mRpfJvQMan2vhPR24HZgII/i7xbIPtMtw/gy2QXuoAPQPcQcCABRP02LLogYx"
			"mTTvozwy2MTwAKYXKcJ5yAgIEL5whfy5a7GhPTQMtAfAUlJ72LUxVq5jy7adH9V99ctCV4cLq6uQ"
			"ppHSbS+GHZ2C4QtTNr/nGvHyeBO2kcJZivO5VASBhzNFyvqUatrD0VG/3+fu3JESmQJAs+HZW7Za"
			"PH7lIIvylskjw/GGrT55WVm3GQvTrQPA5aQyUCNoD4Bh154X8JbVCNexZfY7QoKLIoTRXxt2hL/c"
			"OxCN125gv3Y+lk7nZ8lhZVzCeRcA8/IA8zLDs20E8/LBuXkAVEajBzGVudW9EjoaaZ1B13ppHRMt"
			"BsJKgumza3p5TOPztTMwAbAp30EypSk9Lf0omeHZPJ5tAwCAQmicLNM6K93FfGaTV+iqJKgVGQYA"
			"2C4w1RAAUDmNk2U7Aeexq9tpHp7BIB/beV9UBefmAVAZXMeWAQAAAADsDpBlAAAAAABcAFnGI5xP"
			"SoJ5eYB5meHZNoJ5+eDcPAAqA7IMAAAAAIALIMsAAAAAALgAsoxHOJ+UBPPyAPMyw7NtBPPywbl5"
			"AFQGZBkAAAAAABc0TpZFrYnJrp7pLoO5WhqTiCgqCQAAAACAJ5oly5S5NWlHpMsPJHO87y2fqavD"
			"hb1IppehdyAuhmpTFmHifFJSv9/neRnnJtQev/BsHs+2EczLB+fmAVAZDZJl8sjoXgmDRcQW3wrl"
			"RETa+ZgYXSb3GrheOYdY9t90ioUJAQAAgOJpkCzTVakTrawe3hVN6dQbwySyVyk/PnNGO5WzY7qe"
			"NcVVxh+W+xdOBwAAAEBhbMWamFpHeCXLuq4TySNjrmjOQKbVVUjTSOm2F8OOTiE/OfvVmwfkJdab"
			"EjaycnvCqkvw8rgOM4tI4KTG2JlcrpFc2BNImU6nXNkTSLET+bEnkOLBiT2BlH6/z5U9bIqXyIk9"
			"zbovCIDKWC6Xy+Xy8vKybkMiWAXyr6L8o0P+PeSR4UX221nlkRG5R7ijBy6W/29tNgAA2GZ27Xlx"
			"eXm5jMc0zYStX339jQVywPUgptYRBC+OPwG/VPOizLTzsXQ6P0NYWVoCMktw/+LwNkGZAQAAAAXA"
			"tSxbhyvHtM6AJrbMfEafrCScPrumdruBYWWV/zILOL2S1RhrHo/KjPPftTAvMzzbRjAvH5ybB0Bl"
			"NC62TOsIWvgzm8qgq5KgVmRYEwloqSQpFo/glmNlLQEAAAAARA33loHMbOYeWwePPjMAAACgcTTO"
			"W7YTlDnxJ6DGssD5vCSYlweezePZNoJ5+eDcPAAqA7JsRyhkvDIBDGUCAAAAecEg5tZT7HhlAhjK"
			"BAAAAHIBWcYjxU1KSv+2iw2IN48LZcb5lC6YlxmebSOYlw/OzQOgMiDLtpXAG8gqgwtlBgAAADQR"
			"yLLtI/xK2IqBMgMAAACyAFnGI1knJVUkyFKYV6cy43xKF8zLDM+2EczLB+fmAVAZkGXbQe0esjDw"
			"mQEAAACb0SBZJo8MdyXP8NLj7qrmc8XJ6S1Yvto5kLQlcCjIPKDMAAAAgA1ojCyTR8+OjYEgCIKw"
			"P5YmfmGmzCfSeN/boqvDhXjQY0SY3DsQF0O1KWtjpp6UVI8g22TOVA3KjPMpXTAvMzzbRjAvH5yb"
			"B0BlNEaW6aokOIuQ67NrU3rAiq4Hknk905kt2vmYGF0m9w5ofB61amZjqWuiZQbgMwMAAABS0RhZ"
			"xqCcHRs+z9fDu6LxWici0l8b4t2HRKTPrun4TPF2oOtZU1xlKeB21DIOKDMAAABgPY1bfEmZW6fv"
			"9qW1ni9dHS6srkKaRkq3vRh2dAr5ydmv3jwgL7HelLCR0+mUlTX9/pE3dal6C/v9/oZ7HU2nT4nI"
			"W52pPAu9RN6uqZ0ynU65sieQYifyY08gxYMTe3LfF9WleImc2NOs+4IAqIzlcrlcLi8vL+s2JAI3"
			"jt+N8Y+L25dHhpO8+mTvPldIHhkRUwQaGcpghVZSahxbcAoAgJ2jgc+LXFxeXi7jMU0zYetXX39j"
			"gRxwPYipdQSHjkbyyHh5dyhIEXH7+mvDjvCXewfucCYRaedj6XR+tg1hZQ2KJEuGHc2EOAMAAAB8"
			"cC3LWJSzY5HaE1dOGiPZdYcRkdYZGMcvLevlsTHorCSYPrumdruBYWX+X2bcRZLl++HInkIpyozz"
			"37UwLzM820YwLx+cmwdAZTQmtkzrCCGXl75Ki9psT99USzasZLbDSRZAYM7L2q5TAwAAALLTGG/Z"
			"7rE1A5eRlO4zAwAAABoHZBmPuJMWiZ+BS5aC5iUJJb04g/NpUzAvMzzbRjAvH5ybB0BlQJZxyBY7"
			"ycLglWYAAACAA2QZV2z3wGUcmJ4JAAAAEEGW8cRKkPX7R3Uaso4S5kwVGWrG+ZQumJcZnm0jmJcP"
			"zs0DoDIgyzhhB51kATAJAAAAwK4DWcYD0GQ2UGYAAAB2Gsiy2onQZJxPSirTvAKmZ+5w7RUAz+bx"
			"bBvBvHxwbh4AlQFZVi/wk0WCSQAAAAB2kQbJMnlkuEsvhZYefodKeAAAIABJREFUXy1q7q5OHljS"
			"PG6V81qBJksAA5oAAAB2jsbIMnn07NgYCIIgCPtjaeIXZvIDyRzve6ua6+pwYa9d7mXoHYiLYcQq"
			"5zWx5kUYnE9Kqsq8jAOaqL088Gwez7YRzMsH5+YBUBmNkWW6KgnOKuT67NqUHgREl/Ga1Vza+ZgY"
			"XSb3Dmh8HrFoZi1wt/Q432BAEwAAwK7QGFnGoJwdGz7P18O7oimd+sY39dk1HZ8p3g50PePGVWYD"
			"QZYeDGgCAADYCX5UtwGbosyt03f7ks/zpXWEV7Ks6zqRPDLmiuYMZFpdhTSNlG57MezoFPKTs1+9"
			"eUBeYuEpYUmRsFeaPDWm9Pv9ao9uKzOL+VeI28tL5KrGvJTpdMqVPYEUO5EfewIpHpzYU/d9sUGK"
			"l8iJPc26LwiAylgul8vl8vLysm5DIlgF8ts+sBRx+/LI8HIoczf+PzRFgOoJZcAwXH4s5g8AAKqg"
			"judFnVxeXi7jMU0zYetXX39jgRxw7S3TOsLKKyaPjJd3h4IUESGmzK3ulRt5Rl6UmXY+Pn02PyMa"
			"f8JDWBlkRCEITE363GYAAABA02lMbJlydixSe+LKSWMku+4wIq0zIGfLM/qk42kwfXZN7TYXYWWb"
			"vQuD819mdZsnJEeb1W3eGmBeZni2jWBePjg3D4DK4NpbxuLznDnoXlrUVrKnb6olG5YCvJ+sDOA2"
			"AwAAsG00xlvWWKDJymON2wwAAABoFo3xljWTjJqM84k/nJkXnKTpTtvkFM5qLwjP5vFsG8G8fHBu"
			"HgCVAW9ZecBPViUBtxk8ZwAAAJoHZFlJQJNVT3hME+IMAABAk4AsK4O8mozzSUl8mxeoc+6UGd+1"
			"x7V5PNtGMC8fnJsHQGVAlpUH/GT10O8fwW0GAACgiUCWFQ4UACdgTBMAAEDDgCwrlmJCyjiflNQo"
			"87gTZ42qPb7g2TaCefng3DwAKqNJssxbIjNiiUt321yhyLUzUyynmR+E+XML7wFnAAAAADVJlinz"
			"CQ0EQRCEAU0CwkyZT6TxviDsj6XJXCFdHS7Egx4jwuTegbgYqmUuwgRNxjncuc0AAACAAM2RZVpH"
			"6MSsOC4/kMzrmU6kz65N6YFMpJ2PidFlcu+AxudVrFdejCbjfFJSk82LFGeV6rMm117N8Gwbwbx8"
			"cG4eAJXRHFlGRPZYpDWhgV+gPbwrGq91IiL9tSHefUj2KuXHZ45PTTk7Lnm9cvhdmoUQNayJiwgA"
			"AKBmGrb4kr32uDK35hTrO3NzDhdWVyFNI6XbXgw7OoV+kLFfvYBTLzF1iuUmHhH1c5QTNKmQctKk"
			"PJ0+ZY941D9au1e/X9iZFp7iJa7b64iIpr5z9y15ztt5IYWFE3u28b5ASmwKAFWwXC6Xy+Xl5WXd"
			"hkTgxfgHgvzlkeEL3199921R5tZcIXlkRM0RiOjoM1HDKFjhWGSF/+o2qmKs0B8AADgU9LxoDJeX"
			"l8t4TNNM2PrV199YIAdcD2JqHcGhozkii4jYQUsb/bVhR/jLvQNmi3Y+lk7nZ1WElW1DmL/gP4sd"
			"E2cY1gQAAFA/XMsyH1pnQBNbS56+2+9oRCulpnUGxvFLy3p5bLBhZ/rsmtrtEsPKfGNeW4BAgv3n"
			"pUCcQZwBAACojObIMsZ5JjlvumAmZ7rb/AFnuiqtchdNiU/r2h3mkeLMWkXR9esxKx25zRNKnbC5"
			"7bVXIjzbRjAvH5ybB0BlNEmW8cROvKUsIM4IzjNEngEAACgTyLI8bLMm8wiPbD6dPt1tcUYQZwAA"
			"AMqgYS/I4IPSn8d8zse2lZknyLwPAmfytJza886RvfpZnKZ8XlwPns3j2TaCefng3DwAKgOybFN2"
			"YvgygYA48z7zJs5KozB9BgAAAASALMvGrj99d16cUaI+251KAAAAUCSILduIisKJOJ+U5JkX90KN"
			"eiPPKq+9zWYGNOXicgjPthHMywfn5gFQGfCWpQcDVbHAeYbBTQAAAPmBt2xT8HyNZedfqGETfu0Z"
			"YVknAAAAaYAsS0mlT1POJyUlm1f7yCY3tRepz2g6fcqzPuOm9iLg2TaCefng3DwAKqNJssxbuTy8"
			"8vhqUXN3dXLfWuYUlZQFuMo2AM4zl2h9BhcaAACAAM2RZcp8QgNBEARhQJOAMJMfSOZ431t+SVeH"
			"C3vtci9D70BcDDOvwoTpddmBOGMQUkg0AAAAu0tzZBmzAGYQuXcgGq9ZzaWdj4nRZXLvgMbnMXuv"
			"o4YnJeeTkjKYV+Ui6A2pPU5daDzXHs+2EczLB+fmAVAZzZFlRGSPRVoTGvgF2sO7oimd+gY49dk1"
			"HZ85PjXl7JiuZ/kWLIerrBg4fKFG3aRxocGRBgAAO0HDXpChq5KgkjK35sT4zrSO8EqWdV0nkkfG"
			"XNGcgUyrq5CmkdJtL4YdnUI/yNivXsCplzidTr1noZ0Yk6eUlLCRJR5ruvGx+v1+nqMLJPT7/afT"
			"p95p2srsqH+U/7yojutVUMoRcxZhHcamCNzYvLX3RYaUnPdFqSleIif2NDEFgCpYLpfL5fLy8rJu"
			"QyJYBfL7Y8nkkREXvs9uUuZu/H94jkAqn/mueClq91d5PrPaLeEVa90fAKBEUjwvtorLy8tlPKZp"
			"Jmz96utvLJADrgcxtY4geHH8tsgiIqKHd/2hZMwmIvI2aedj6XR+liOsjIgwfFkBGNlch5A41klQ"
			"aQAAsB1wLct8aJ0BTWwtefpuv6PRSo4xm57RJ6vBTX12Te12xrAyPNuqBnM2U5Ms0QjuNAAAaCjN"
			"kWWM80xy3nSxmpwZ2mSjq1IwaUPqcZVx7jAv1bz84mzHak9I4Uuj9CqN59rj2TaCefng3DwAKqNh"
			"If9VAe9CzXjKzBNku7fIZmbC6z6FiUxE3QIAQM1AliWAp1T9BBZB9z5An6Um8r0bkVhENJ0S0VMm"
			"EfUMAADVAVkWpv53+nM+H7t68wLijBKdZ6i9daQXanGb6rk7OKi6JGBeHjg3D4DKgCwDjSFOnBGc"
			"Z3mJm90ZR8ImXAgAAMgOZFmA+l1lIJlw2Bkh8qwUEmYPJJC8FRcIAACSaNJMzN2B80lJnJgXN2eT"
			"E/PiaL554bmfyZNAWXK9F7f5VVcnMA+ARgBZxgJXWfMIi7On06d421lNJCi2lLdVkmibTp/iTWwA"
			"gO0Gg5ge6OgbTNwLNQgjmxyx9kJsdA9muGHREgAAvNM8b1n0gpju8plzxc5iBfJEJEXDRcfN+aQk"
			"ns3jf6kAnmuPajYv2dm2qeMtzNoh1FwLj+LK5oFz8wCojKbJMnn07FgMpSrziTTeF4T9sTSZK6Sr"
			"w4V40GNEmNw7EBfDhNf9c/TYBvnhX5yBfKQXcHlkXICcqi6v7AMA7ALNkmXy6NmBsTBDyQ8k83qm"
			"E+mza1N6IBNp52NidJncS7deOReuMlAUtjgLL4Jeo0mgJjaVcSUJu2TKln1QkAA0gCbJMnn07OD6"
			"k/N3oQ0P74rGa52ISH9tiHcfkr1K+fGZYm9Xzo7D65XzHD6cdlLSsh7LOZ8zFTYvUpzVpc8aV3sr"
			"Cm9vGxZYa9Wtl279/hHfsq9AateFUKJga2lOyL8tyiSdRqmy6+pwYXUV0jRSuu3FsKOTv1t3IhmW"
			"RER0h7ye0cuzinVYWkTUV4+8lHCefr8/HT0lIroTW44vxS0zLo9H0rES86RPIfdEwxZ6Rgb26vf7"
			"Gx1r9QBOWT/MXmtrnogiKz+QRxgREVl3yMMiS/Bfd7acNMdKOgs3W7FnmnSsxBaVK8W7fEtroyu4"
			"pg4z7DV6ur7mU9xfa1v4puXYeZLuCyZbVJ6odkhp20/c9creohLaqq1Rlu435obKTrbSVr13ETnT"
			"lwZAqSyXy+VyeXl5WbchEbhx/JY1V+SRYTH4wvdXswB88wGUuTVXSB4Z9kSAAE53s7ScvwTS5PGy"
			"pSSxzDVeAW/fjY4YT4TrKLH8LE6LbNam34vJFmseU5rnMAuee/oLnbU9RJuX52oW2h7WmFeUwyxP"
			"eygqZ1H3vpsn7W2bhhLaQ5bbNs0hii2wpL1yt4cia68JXF5eLuMxTTNh61dff2OBHHA9iKl1BIeO"
			"pquS/XF/bJrjfYkN39dfG3aEv9w7cIcziUg7H0un87N0YWV5Yd0J6TPHkGpS0p3SBj6WIYnmZ+M5"
			"U0sr+vNGZqTeJY154WHNvIeOsiSyqLJmnBXUHiLMK3XsMkN7KLUJ5SDpym5kSSFmh9pDYQ1vXf+Q"
			"q8Dy+wcAOIdrWbYO2x1GRFpnYBy/tKyXx8ags5Jg+uya2u1wWJlHvXep3W9y21PcEQpWfpXpyNQE"
			"lVkRkjoLdplNaQ9FWchfe8hSTnnXqyntocACS4LbOgQgiubElrnoqiQ5H7WOoIU/+vMKas7j8XxL"
			"3xEcr3uBPRrP55uHmPMKL3/eYNAe0rOt58VSXnsoT0LVxS60B9AQGu0t21qS4hiq6Ra98qN+snMe"
			"ZpFkXowHosqVAAquvaLbQ7R52/cYtinUI8XFfRHfHkoxr7gKzGheGR5cSDRQK5BlhYK7Ogz7nMhQ"
			"P1VVafj1swUUun3tAU/BPOza+W4K6gcAyDKidH3BjvQX2zpCkYmkd5vtVHvgiuprPnX/EHhtzRbC"
			"YXtIz47cs6D5NC+2bBfYYM5UGeEj6yhrLmFBFGse+26zQgiaV6wUzt0eImqv2KYF6V8lTHso7L4o"
			"pz1MiywUgAYDb1nl5BzUq55GGEkF25n0+oy1ZFYeTalqUA2NaA+NMBKA5gBZBriEA58K+24zLKa5"
			"opbHcIb20FC5wEHLbwCoJbC9QJYVQTWz4Uo4UDa4mHHmEaqTvOb5H+e53GZRFPyydSq4PazM46Ox"
			"saSqOv7Mro7Ec8/b8EquWL56FQDqA7IMuOzy8yyRKl+fAbYQ3FkAgNRAliWC/rRwmjm0ZA9oCksi"
			"IuvOtrx+lgea2R4c0D8UTuHtAdcINI3myTLfauQuq0XN3dXJA3kikjhmszlTFTzY/IfYwLw6+sSd"
			"migaQb72sMF6rJVrKce2Riu56nGrq8SGV8QVqfO2hXQDPNE0WSaPnh2L4dQHkjned1c1J10dLuy1"
			"y70MvQNxMVTj1sYEID2BaDN4zjYAzz9QOJDpYLtoliyTR88OjIUZSu4diMZrVnNp52NidJncO6Dx"
			"ecSimQC4bKIYSlkSAOwC0BAskOkAhGiSLJNHzw6uPzl/F9rw8K5oSqfeGCYRkT67puMz+zMpZ8d0"
			"PQu4ypxXchfeIxTR7VY/KcmOmkpZG5zPmVpjXjnPxfRuM595/D2WHPP4M4wqXtWxlhoo86C5aq/8"
			"2uClV4FuBnXTnLf826JM0mkU2qR1hFeyrOs6kTwy5ormDGRaXYU0jZRuezHs6OS/86dEXsqUKczL"
			"48U6pMmzyubfa2057FeK6ptW5bg6MvLoTmnrjh6Z4tmUtJffnrTHCp9FBgvVo+noqf2m8jTnTqFr"
			"YXeyffVo6t8luZw0xzrqHz2drpbcscg66vuO4r1mva8epaqfcEqcPe5JUcjm8GkmlZPuTPO0+eS9"
			"Uh4rTOTRwztEl8xujSw2RTkZjkXrrg5F1U+2vmVtyenbRnLJCf1YcsmRe609VjhPyqOnKZlC14vW"
			"tUMACma5XC6Xy8vLy7oNiWAVyD9X5JFhMcSF77MTApS5G//vuND82GuShD9vmie8KSFn5NZQitcd"
			"rNkx5RHXYZGVqgQ3T7R56Q3b5Fjrdwldowjz0pSWrz3Y3jLHZ5Z4OJ95KRpDLOW0h77nLdv0GqUh"
			"X3tYVd0m7SFjtlr6h8z1k6LYDW7bjQrfKE/8LqkubtyxcraHFKXlqr0Gcnl5uYzHNM2ErV99/Y0F"
			"csD1IKbWEQQ3jl9XJfvj/tg0x/sSG75v6y8XL8pMOx9Lp/OzBoaVcf7jrHTz8o2Y1FJ77JIAyawx"
			"r5AxlByF8Nj23PawsW1cDVDWSBNWUt/g4u7a5QM7BteybB2uHNM6A5rYMvMZfdLxNJg+u6Z2OxxW"
			"lg2eYw54tm2XKHxJAABAYaCfBE2gObFlLroqSc5HrSNowU/BvIJalWGp4O93nkXWRvH+wMENGgvU"
			"m0ACuYLMVmbNq1b+Wimok5LaA5oZAFE02lu2tXAex5DWvLhut7zfrE34NbwlF7dAUl81rquuCW2P"
			"Z7i+uABUCGQZaDIc/+AORJthTBOAVBQocDnuHwCIA7KMiPBLNx27U0t5ztT/JNhmZbZpLVXwjCzp"
			"ELvT8vOAWgKgCCDLclPCk4DH2XAMnJvHMxZZ7HvOOITni8uzbbHAYZOORl5cAEoAsqwhoHOvnXyX"
			"oGC3GdpD7XB1CdYaA1cWAA0Bsiwerrpd0HzSrnGOJygAAOwqkGU8kmVSUtZneYa3Y2DOVGYEEo76"
			"R1UcKbO24/jXCBpeDVTVHnBxAbCBLAOgalKuBwAAEbynAOwWTZJlqyUyw4tcutvmChHJIyO4aGZE"
			"Unls2o2i22Xh2FtTIOzrM5IGNEFke8AtAwDYUhoky+QHkjned9fI9G1S5hNpvC8I+2NpMldIV4cL"
			"8aDHiDC5dyAuhmohizBlJ7Xg4HdSUl2L623RYzju4kKZrYXf+wLYVLAYK2Q62HaaI8vk3oHorUIe"
			"2PRAMq9nOpE+uzalBzKRdj4mRpfJvQauV95wdsPplYp1C2Y32G2W/nGI9rCWnaqinTpZADahObLs"
			"4V3RlE4jxzAf3nUFm/7aEO8+JHuV8uMzJ5tydlzUeuUAlME2v3U2DfV6Oyo+Olw7nIMLBGqlOUuV"
			"ax3hlSzruk4kj4y5onUSvV+6OlxYXYU0jZRuezHs6OSf7DMl6qtH5KZM3fT+Rimu4z2c4iUGnPP9"
			"fj9ir3Uls0XEHl09mo6e2otnp7Gw3+/bw5HWHUcUpDyv2LNgSyYK5Pflcdf57qtHWc50XT2vrflg"
			"nqVFbnvYtOQ0Vzn6pPyJR3REROzLZgMlp7ymuVLs4enU7Sd8FrF7rcuTtpzU9RyXEnmsQIpXDxuV"
			"nPJYlKI2AimRJdO62siQJ5gSdV8U1eq8RDvFy0CJ9RN3FlP/1thywimbtHkAqmC5XC6Xy8vLy7oN"
			"iWAV4+93j8kjwxe+v/ru26LMrblC8sgIzxCg8HzspRX8eZQmJX9i1Kb1tsWRPieTfzP3TMpDrM0W"
			"l2Gj9MzXKJCS7epnuvSrixtzphZZ3nWJvjQlt4cN8qffpYj24LsvtqU9rElcu2ntVpd+v89ze1h/"
			"cZM3ZW4P6XYM9snbzuXl5TIe0zQTtn719TcWyAHXg5haRxC8EH9bZLn4osz014Yd4e+PP9POx9Lp"
			"/AxhZTtO06JYAq/P2MUxzQIJD0g1rT0AAHYKrmWZD60zoImtJZ/RJx2NaKXUtM7AOH5pWS+PjQEz"
			"tqnPrqndLjCsrKqYA/jMt5gMFxfKzGZN1UFvJcJ5r+IzD9FdYIdpTmwZkdYRtLiE0DYiItJVSVBL"
			"tQlPAlAattvM1mT2v3gPbcNA/wAA2JDmeMu2mwb9OuTH1C1+5jGVvOuTNEGz4Kd/AKCZQJbxSEXh"
			"pUuLiKw7VRyqeZT2dMlwcQNLAhRskMfSnXa3ERU+hnct7LpYNq69an/24OICYANZ5rIdP/K24yya"
			"QrW1nWUEE+2hKLajJrfjLPKAGgDc06TYMtAYtnh4MZmST9xVZk2LNtvZ9gAiQXsAIB54y3jENykJ"
			"XdhGlPRruLirkHdCnH/oufAxTZ7n6/FsG1GZnpgiSua89oo3Dz0naCaQZaAhYPSBIRBttrVTARKe"
			"rGgPNhAfNmgPYFuALKsK3npP3uwBm1PkJE20hyrhX0PU3h74ryIAygGyrBzy9Sm7Oymp9odB+RR7"
			"cQPKLL/bLIt5VT1BOb8vYJ5DpvbAee0BUBmQZVtH+c9I5x0K+CHLB+yAJoXdZvA6lEHhvx8q+0HS"
			"6PawAz/bAGiSLFutXB5ae9y/SR4Zlm8tc4pKAvXS6MdDfop+wDQ+2mzH2wMAABBRo2SZ/EAyx/ve"
			"0uUJm3R1uLDXLvcy9A7ExVDdYG3MWn+W7dycqa2H0Ryl1l7+aDOeLy7PthHMywfn5gFQGc2RZXLv"
			"QDReRwqr8CbtfEyMLpN7BzQ+j1g0E5QAVwMNXBlTCYVHm+WlcZegcQY3C1QvAIk0R5Y9vCua0mnk"
			"GGbEJn12TcdnTjbl7JiuZxu4ysBOsXXPiUC02Xq2rgZ2hZIu3O60h905U9AcmvOWf60jvJJlXdeJ"
			"5JExV7TVQGbUJl0dLqyuQppGSre9GHZ0Ck32Yb96LnQ7cRqVLZCHzRbO46WzKU7h4XKmU7oj0NKi"
			"pWUH1Dt5vGUKmXKS7El5LKZY9lTTlJymftYc3U1hC4lLyVDyqrQ68sRZOA1sJR8R9aMeTUdPaWl5"
			"z4x+vz8dPSUiNiW4F5MikMC6yiyybK2WfBbevwklb5Sy/kwT7qbEvQIls2fB7jUNbI2yJ1xUcjl5"
			"jkUb9iRJd1yakos61ob9D8XdOzEle18D5QTupnBKXMlpziJNHgqdFwDlslwul8vl5eVl3YZEsArk"
			"97vH5JERF77PblLmbvx/cIYAUeR87KW1ijhmP8flSci2Uc5QnpVtafZae+jEDBnGufr9/gYnvpGd"
			"yfumrNJNr2OePJtncy7uJu0hKSUNS4uWVtKFZoqNuC9SH2LjTWtz+lOCtmVoD3HZimgPEfdFOf3D"
			"Znux5qXcK1szS1lCzKY1HXIaw1LeiezWlG1m997fcXl5uYzHNM2ErV99/Y0FcsD1IKbWEQQvjt8W"
			"WS6+ULKYTdr5WDqdn+1gWBkmtQGWOwIRCUui8qLNmtLkMGhFzblYAOwkXMsyH1pnQBNbSz6jTzoa"
			"reRYxCYisgPM2u30YWWb9laldfGc+8ydIVfitWdPY1t99vNwcROUGQ/mxZFkW86bsYg2w3PVUcXm"
			"bX5/8VV7PPdvYNtpTmwZkdYRtJiE0CYbXZUEtXzDGgY/DgM3nI4LY8Jwbt7msNFmXqjZ9lPXdeTn"
			"RtsRtu6GBbtJc7xlnIBfUcmU+ihiK5/DZx6HJoWIeOtsqWZvVPimNxeHNyOHJm0KP82Yk8rMH2kH"
			"wCZAlvEI5+GlZZlX0POA09pznzE8mJfgJ+PBPIdQe+DItihg3nri7/H15vGjFwEoE8iySkCHAlg4"
			"aA9rVtIEZZPf9ZunFXHiiAIAhIAs21Iq6HbRszeImItV5JIAaA9l06wabpa1AHADZFlpZOiV3F3Y"
			"d8kW7FMpoky+5kyFKN68Qi/EBuYFmlAl7eHp9GmR5RfKzjW8QkllXn1+XM5rD4DKgCwDxcHB2BzI"
			"BgY0QemgfwAgBZBlUaD72B1wrV0iJmnuIGnaA9rM7oBrDSoHsoxHipkzVVpsR5FTukowkosZZ/HU"
			"Zl66qj7qH3mf8yqzDI80z8iofXFl8xA0j7Pw0+jaQ4Aa2D0gy3aJwn/5FdhpbmQbfsIWQkw1Zneb"
			"cf4QRbNJhuf64TXQFoDCaZIsk0eGs5JneOlxd1XzueJkDKxlHpHEJ3cEIpqO+A27rpmN+tAt6HYT"
			"vUel0oxoM1YINvpCp6S+9tAMdq09gG2kObJMHj07uN7//9u7e93UtTSM4++SzgWc4lwCptjKFRhN"
			"NZojgadAoxHNLrJ345S4oaNMRwPVUdzsnSIN86FIM0AxmmoUX0GUAqNzBbvIHXgKG9t8hvBhv4T/"
			"TynAmPBgG/Oylu1ljDG1gXW3WJi54ztrUEsfCbzepOK0ckWY3XIqk56349iYb3jvJ7/4ixKt/VfH"
			"s/6cKTW7wmzIzmK8c3tIau4SF9TW7SG/cg862uwE28P+J+sVsrSTeMXsH95v49IrpnXzrbdZwpCd"
			"gEpnU5bZLWc+5njgWaaRHwLT/mTNRsNAJBiOZtYnW8S/HUiuLrNbjgxu142aueyDfVzTt3O6n9fK"
			"+60OxPbw3mYztoczwv4B0OdsyrKraiWUVri2E/OqWglf4ortJaxUr0QkGI6k3U1mc7vteUm3s6Ps"
			"ng78Jx/sO2CT9G0W/H73OBS9yFc8UYa9FHqSZllvs/jt8Gy3h0KVvn+4kOUMNX4qO8A71B2pGROI"
			"uONo7PqNra1fgdebRE1XfF/cZn3SawSycrJP/u5SE/qXL1/SKelsW5rZV+eJpyR3fjZb5lme4n3N"
			"H1u267M2TfnZpD9Vv3hfZfV95d7Tnq+VBp7vvw7NvHVKcuut18rPdoR5dnmtnd9F9sT3rMHjLMPc"
			"9rB2GcZb/tr/81W+phebjSRKz9lcfq3c9rBf5mS69zW9v+1Z8/ey47Yquy/5A//PDvO8750ursHt"
			"+6h99jb7rq83pqSB1+5/5s9K767+n++S2fXztfW18rOl9zfOk/s/QBFeX19fX18fHh7KDrLG/Dj+"
			"KBq77jhrI7P74cLh+9n9hUfip9j9cM0pArrPZi8y2x6NH5oXnRDvMG/GizeY9K+QUIlzX3TlIt4h"
			"lMc7uoeHh9fNZrPZlkf/9o9/RTiA6k5Mv2ESDd9/nNSbSXWVdVrGgpcwPsLfbjm5R/zbgdUZd3c9"
			"rAzA2xjjHABOSHNr2ZL0AhnzBrFcC9rCBTIWn7HhuhiX9utnk8u9njsOVlazGVCwS/u+oLWsROd0"
			"bFngWcbLT/Abxl+9ue0ZAI7HiEkLskiifCsaAGAPqjsxASjHSJoAcESUZRopbzAn3iE+ZLxijjb7"
			"kIuuMMQDzgJlGYAjWKrMaDYDgD1QlgE4Dk7SBIADUZZppPzqhcQ7xIePd7pmsw+/6E6KeMBZoCwD"
			"cGQ0mwHAfijLAJwER5sBwHtRlmmk/KQk4h3iouIdt9nsohbd0REPOAvnVJalV/lfHeMyGz1zPgzm"
			"0rX910wCUAiazQBgR+dTltn9e2dUM8aY2sC6Wxpk6ZM1G9Tmw2dK4PUm8SCZ6QwtpzLpecHyPwVQ"
			"BI42A4CdnMuYmHY/3NjaZffD5Qa0xdnXPZk28xitFyhSfhhNNjyci0v7vmBMzBKdTWvZVbUSSivp"
			"xVyqwa6qlZnVWXgoGI6k3Z2PY95ty2hIUxlQutVmM/7Gui+YAAAII0lEQVT40/9X4kcGl+achiqv"
			"O1IzJhBxx9HY9Rvp2OR+wzzbdhAEInY/jB8KvN4karri++I265NeI5CVXzz5u+lVc9KJlzIluacm"
			"D1MuYMpX+frt+zcBACzR3ImZHcg/dt1x1ka2pUMz/1D8lDU9nCKiu1G6yGx7/BbUvOiEeIfRHE9z"
			"NiHeYYinCp2YJVLdiek3jJkfx+8/TurNpLq6qlbCl1yfZL5kE0kf8m8HVmfcdWRw6wsAAIBuqsuy"
			"BX6jNk2OH+tMaw1fsnLMb9zIXfzQvVxnnZvBcCT1OoeVAQCAs6C5E/OkLq1RehMOaAWA7S7t+4JO"
			"zBKdT2sZAADAh0ZZBgAAoAJlmUbKG8yJdwji7U1zNiHeYZTHAwpDWQYAAKACZRkAAIAKlGUapZdE"
			"14l4hyDe3jRnE+IdRnk8oDCUZQAAACpQlgEAAKhAWaaR8pOSiHcI4u1NczYh3mGUxwMKczZlmd0P"
			"85fBXR6qfD6q+dhN5l2aYc0kAAAATc6mLAs8Kxm2vDaYyaTn5ce5dMd31qBmTG1g3Y1dCbzepOK0"
			"ckWY3XIqS88BAADQ5ezGxJyPT55j98OkJSy9lU2SlXsx2sxjjIkJANtd2vcFY2KW6GxayxJ2v2MN"
			"bv3FiVfVSvgSiIgEL2GleiUiwXAk7W5SvrndtoyGS01l375/iyuSC/8rYKUBAIBd/FR2gPdxu20Z"
			"1Xbpiwy83iRquuL74jbrk14jkKVfPN9PE/EMGTHxjXT5pNcQYgpTmMIUpghQGM2dmPPj+KN5t+Wa"
			"DkyRtZ2Y2dx2P1z7pPQjp5DmbEK8wxBvb5qzCfEOQzxV6MQskepOTL+RHOVvGr6IiP3Jmk2fV+cL"
			"XsL4CH+75cy7M0XEvx1YnXHXkZVeTwAAAHVUl2XLsmPIYmnjmd+4CdtPUfTUDm8aWQkWDEdSr68e"
			"VgYAAKCQ5k7Mk7q0RmkAwH4u7fuCTswSnVVrGQAAwMdFWQYAAKACZZlGyhvMiXcI4u1NczYh3mGU"
			"xwMKQ1kGAACgAmUZAACACpRlAAAAKpjX11cRGY1Gnz9/LjtMoTiUAQCwi0sbf+nh4cFxnE2P/vjx"
			"45dfftn06H/++7+//uXPp8l1GS72umWaKS8ZiXcI4u1NczYh3mGUx7s0XLesRHRiAgAAqEBZBgAA"
			"oAJlGQAAgAqXe8g/AABYxSH/JaK1DAAAQAXKshK44+SEi7G7PCmbUh67Hy7n0xRP8dJzx1mEbCmq"
			"WYz5eNlizOKUGW8hm6YtcN16TALl7quLJ3Y/DPu2tni5+1EURUlCLZ9fQAcukFG0bH+e3nLH8Q7K"
			"7oel75qy3XmaRlO8eZZcUCXx4u+WJEAui5LFuBBv6Vt7PkNZ8RazadoC163HlcDq4iXlz3wF64u3"
			"MEHJ5xcLuEBGiWgtK5zfMA1/YYr9yZqNhoFIMBzNrE/xHmo8XvwJmf3OXPo6PS675choGIiIBJ5l"
			"Gr6yePMsIsFwJE7L1hHP7ofNR3Mzmd+fLzvJYpWZczme2C2nEr4E+VnKireSbcFs+lzmFrhmPa5Z"
			"mKriiYjdv3fCyUxrPBERt9sObxp+qfEAnSjLymH3wyi6k3jHdFWdf0UGL2GleiUiInV5NMaYm0m9"
			"6UqyGzPGGHMTtrun+015Va2E0grz+0RN8YKXsOK04qaUllNREy/wrKVaO+V222HPC0rNuRLvqlqZ"
			"WR0Va3klW+BZ13IfRVF0L9eWFyhZxfP1uHZhKoondv/eGV3fTpXGExGx+x1rcOvriQcoQllWjsCz"
			"jDGPzc2t9rPps4jI8zT+Bfk8ndXvoiiKxrLS2nZkdUeu412g3KmL5zduwvZTFEXRfTWcbZytvKW3"
			"yB1HnWlt8yuWtxjN9XW8lmvTjq617I6j+3gLvJZ7JR+Qt9ajnnhxUZYWQNrixfe77XmTvIZ4gC6U"
			"ZWVK9jrznU/coB/vkJbFdVxtEO+eTnoIxmT+q1ZnPL9hjDHGWI9SCV8CbfEydj+MOtOatbg01eQM"
			"gvR7UdVazvdTe71JvemWnG1pPa7SFM9uOZVK+ymKntqVSvsp7Je9ZtcsPbdZn2QtZ2o2PEAJyrLC"
			"5U7hStrv05651SN+5pLDYQPPMuZmIvmjNI7Lf0z6DXTGy5ae3e/UJ4++6IqXf8mnas/kvo1U5Vw8"
			"j1DVWs71U4vbrM+mz2VmW12PWxIriBcXMHENMxvULK/UNbt26S1VX2o2PEALzsQsXnptgo1nsC+c"
			"jLZ0FvnJj3pND6/VGe/tC2SUFm/hJNu89RcCKDrn+gtk6FjLGy7esekCGUVlW78eVwLri7f1Ahnl"
			"xltcciXGwxaciVkirvIPAAAyXOW/RHRiAgAAqEBZBgAAoMJPZQcAAAAfx9//+e+yI5wxyjIAAHAc"
			"f/rjH8qOcN7oxAQAAFCBsgwAAEAFyjIAAAAVKMuAi5FeKTgT9t38hUcBAGXikH/gYgSeZTwRsfvh"
			"vVynY+J4DP4MADrQWgZctniEG7sfhuNxmDagxTcWB75hjGgAODHKMgAiIlKxptfG1AbS7kh8w2nZ"
			"dj/sTGvGGGNu5I7CDABOik5MACIiMhsNAxE7d0NErqqVSv0paiezTG2RoMSMADT48eNH2RE+LMoy"
			"AFtNbkyDo88AJLaMU47D0YkJYLPn6aze6dsSn8ZJJyYAnBStZQA2CzzrphrFvZiTG0OrGQCcknl9"
			"fRWR0Wj0+fPnssMAAICSPTw8OI5TdooLRScmAACACpRlAAAAKlCWAQAAqEBZBgAAoAJlGQAAOKLf"
			"f/v158Svv/1edprz8n8H5mtjRH7g+wAAAABJRU5ErkJggg=="
		)
	)
	(image
		(at 246.38 110.49)
		(uuid "c159ccf7-7405-4fad-9ee6-1ccd29da5f71")
		(data "iVBORw0KGgoAAAANSUhEUgAAAywAAAHgCAIAAADIWX4sAAAAA3NCSVQICAjb4U/gAAAgAElEQVR4"
			"nOzdTW/jWJov+IcXubrAYGKRH2A6i4zoCIQ3tcga0ctG5oxkD+AuVGmTQIR8gaE6Ny0aBSeQgAOF"
			"QhhITAk1pnsxeUUMOuwAtFFO3jKQtohbiVwVgr5ZiwYGDkR0BFnZXyAXHtxNbyI5C1LUoUhKfOeh"
			"9P/BiJAO3x5RR9Sjcw5J4fb2lgAAAADy+Fa98+szIiLqfXWrffTDlx///I9//y9/+vSDnCsd3su7"
			"En79h7oDAAAAgIb74cuPf01f3d7e3t7e/ss9+1uiDz790+3aJk9Fee/q6qruGAAAAIALOzs7WRb7"
			"65u/fHjvZ+7jDz5F7pXQe0T0N3/zN3WHAQAAADX7t3/7t4xLfrTT+/Wv/+HL/4Vp+2J6En/48uOf"
			"f/4Xog+/+OLB53+89y9/+vSDb9U7w3tfPPj88zMi+vALr8dxoUuT2cBsFeEpTfYeEQmCUHcYAAAA"
			"wLUff/wxXPj+++8TEdFH2u2/fPnxz+98Hk6SvlV//vmDr27/9BH98OXHPyf6wiv/y+dvDm9vNfpW"
			"vfPrf/r2U+0joo+021uN3ATuy3/8iMno/slbxap4muU9IvoP/wEjwwAAAGCFWcoV6YNP/3T76Q9f"
			"fvzzOyqbh/1gv/rwi//8kTvHYe/z4WzCh1/840dERB/t9Gho/0AffcA2hX34Bbvun9378OzXHweH"
			"6C8Nphlik7C3b99WHkwB7t69W3cIAAAAm+qDT//zF3/8+dW3WXoMZ6P7P6Ifvvz4HxZW6yZ4d+78"
			"Zd26I8NJ2L/+67/+4he/+NnPflZHSNn99a9//f777//2b/+27kAAAAA2ybdffvkzdzz+D//1j3/5"
			"8O9/RvRXb9IH4oO/eN2NP3w5PJt3R4b4o/t/+K9//Av9fWj6B5/+6V/o43/wGs3WQUQS9urVqyZm"
			"YETkxvz9998/ePCg7lgAAAA2xkfimzt37riPe1/dfvrBPAmjj7Svend+feeM6MMvvujRH+NX8o9f"
			"DH/+8zuf04e93ofBSYER+2uSgRGRMB6PHz58yBa9fPmyoUkYzRrDFl4RAAAArPTy5csll6j48ccf"
			"8w7D+uHLj3/+5nB9uhPzWs+B+ev3igAAAJru23/6/C8fftHINp5yIAkDAACA8swv8eV3VYIrTRJm"
			"n25Lquk/lTXrxUA0+kJHdx8m36rRFzq6MnVG7ZThJoQkDAAAgA8ffPqn20/rDoJP6VvC5rmXKvXv"
			"Onur5l9MuBZSuaR+97vfEdFvf/vblYWEJAwAAAC4l7U7UnogEyVJpYwLPfC0L6kPNY3UtHnYb3/7"
			"29/97ne/+93v/JQrLgMjJGEAAADAvay3LbJemUTyAylQaPSFjpdyMX2VRER6R3Bbw9ojxyH7dFvN"
			"ECubhy3JwAg3YgIAAADupW8JM1VJUImIlOmLgUjGrNxNuJSpM2rbp9uSKvXvOqORMyW/uIBw/TyM"
			"4jMwQksYAAAAcC82CXv37l2o6CeHiFr/57/++R9Ff553DhE5P727/KNORP/7//bxu3fv/uZ//VWL"
			"TP2Pl//Xx+13DhGR845dn7uiYFl6S5ZGEgYAAACcS5OE/eTmTj/9xE6blf3kJlvutNmc7969C5Qv"
			"rCh9EvbFF18Q0eeff/7FF18cHx9//vnnkbMhCQMAAADOpWsJIyKinwItWM5PbtlHu/+J/u9/fvnm"
			"7buPfvbXy//nvxH9p92P3r1798Hd/5nov/3kBFvCiIiCZQn8/ve/J6LPPvvs3bt3n3322e9///sv"
			"vvjis88+C8+JJAwAAAA4l6olzPnJ/TfUEvaT89O7vzv97//F+R9+ufUfPyMi+vD/+H9P/+7du3f0"
			"P3362f5nv/znvf/4z/v/5b//00fMitK1hP3hD38got/85jf+Qr/5zW/+8Ic//P73v//Nb36zMDOS"
			"MAAAAOCcMB6P/+7v/o4t+u67737xi1/kvT9UTX788cfvv/9+4RUBAADASt999125946EoDQtYc2B"
			"ljAAAIAqffvdn+sOoXmQhAEAAEABfvXL3bpDaBgkYQAAAAA1QBIGAAAAUIP3iOjq6io84d///d8r"
			"D6Ywka8IAAAAgB/vEdEvfvGLusMo0pq9HAAAgGp8//33dYewWdBtBwAAAFCD9wiZLwAAAEDl3iOi"
			"Tz75pO4wAAAAoGbj8bjuEDYLuiMBAACgTEZf8PQNIrJPt4XtUzv/SvOvpGZIwgAAAKA09ul2h6aO"
			"4ziOYz14axCJgxfOi4FYd2AceK/uAAAAAGB9Wa9M+YHkPhYHyL1YaAkDAACA0rT3FFN9HOg4ZHoS"
			"7dNtQRAEYfv0dFZo9N1nXrntL8N2aa4HJGEAAABQnvbIsboTKSp9MvqS+nDqOI5zThN9Xm6qr/Yc"
			"x3GmiqkOjdlaHLdIP276UDAfkjAAAAAolTh44TiW9rITzMPsty9l7bDtznGkzCfMStt7Cr1866Zc"
			"XlNYh8nVGg9JGAAAAJRPHJxrsn6RqTNxPrrf0uSiA6sPkjAAAAAojXE66z20rybzMfpEROLdh7Pu"
			"Rvv0eFkTlz+6376amOXFWjUkYQAAAFCa9t1XkjuiXlIfToOXpmiPporeEQRBeExdJXYVRO1DjVRJ"
			"EITHrx6uUUuYMB6PccV8AAAAGI/HOzs7cVN//PHH999/P27qt9/9+Ve/3M2+bft0W3p15Iza2VfR"
			"PGgJAwAAgJoZQzXYVbkRcLFWAAAAqIV9ui2p3hgvZeps3JVckYQBAABALcTBC2dQdxA1QnckAAAA"
			"QA2QhAEAAADUAEkYAAAAQA2QhAEAAADUAEkYAAAAQA2QhAEAAADUAEkYAAAAQA2QhAEAAADUAEkY"
			"AAAAQA2QhAEAAAAvjL4ga3bged9wH9qa7D+eT7I1ObhE9m1VDkkYAAAAlEAQ0v0REVF7T7l+bfnr"
			"MC50Za9NRGRrjyZda9SeTbG1pzcnh20SVdMx1Ux3nWyPrO7kUY1pGJIwAAAA4IZ0v6VfzNq7mBzs"
			"ckLd3XmuZV9Otp5ky70Yovpk62BorJ6xHEjCAAAAgBvibtfPwpbnYO4UplPR6Aser9fS6AuyZmjy"
			"rMz2HjK9kNL91s2butrCkIQBAAAAP8TdrpcXzXMwsl5fb92b52DG8GCWgzHaI8dxHMeZKvrTWZZ1"
			"ffCUnrtlHeERPXenX/vNX+K9Lbb7s1pIwgAAAIAj4m6XJpc22W9uWvelqDmMi5uTw1AO5jeFdfR5"
			"UevkuSoSUXtPoZbXltbeU8hv/qqzKQxJGAAAAPDEzcKMhR5In609vYmYYGtyh6aO4zjWSSv5xhba"
			"2CqFJAwAAAC4Iu526aBzwORgTHtV3JB86/W123BmX06uE28qvrmtAkjCAAAAgC/ibrfl9x56BTS5"
			"tCkwJD+ofXhCB5IgCI9ebyVvCauzIYyE8Xj8ySef1LR1AAAA4MV4PN7Z2Ymb+uOPP77//vtxU7/9"
			"7s+/+uVuOXEREZGtyY/ouXlvKL85zHhdsNiVFrW6tN6rZ7MAAAAAyYnq864s9Z84plrUKo2+dLA1"
			"depqB0MSBgAAAI0gqqZT6ArbI6fYFaa14WPCZM1yLE1eUeRPsDSZlGnkVAAAKJesWc7sMlBlrB+H"
			"d6jahidhpjo0xJ0u86mTuzuiMVTNuCXkB3WdQwEAsMFkzXqxc7XtXvmcRiWkSzi8Q+U2PAkj0o9P"
			"icnC5O4OnR7rRMxvLuazfvfofCCKgxeWJkf+KFOm7nNNs7wyr6SsH24AABtB7u7Q6WPvB7J+vD2c"
			"kN9BwTySNcuaTi3HcaYK+5hCR2NZs6bT6bxE1pjDO0A1Nj4JI3NyRYMjL0NSjgZ0NTHJ/TxafUEQ"
			"hL41OJ99KN8ePz617dNtSTVl7dz7UdY32nsKEZEyHUmn24IgXNwdiEREsmYdvvV/uCENAwDI6OFd"
			"0Xrl91GYpm7GdViI0tvHs2um+48jj8Ztupgfw03VP7xX8HIAiAhJGLldkrMsaq8964p8eFc0LnQi"
			"Iv3CEO8+jFhMekznjuM4ozZJD2Qi+YFkuxnc7M6jD++K4uCFw84EAABl8o7DwceRR2P77Usiopdv"
			"bRyeoR5IwohIvzDaewrJ2mHbS7wSUKbOi7tD5l7t0ea3dBfw+woAIKMVmdLDuyuvMoCjMfAHSRgR"
			"kX58Kh1Oj/zhYET08q09bx7zfi4tcjO22R3eyXxleYP8Z0Uv39rtQ00md/wYuiMBADIyJ1fkDw1R"
			"pv5oXbejYuWYehyNgUtIwojI/Xi328Q0Ypvq41Np5DiOM6I+86vJfGW5Izf1C6M9chzH2SPDOwzo"
			"nb41eOEVuSuRvJIXA6vfSdrIBgAAC+bHU8cZSe7YLVMdugfi87uWnXDp+KOxf3gvJX5IyugLsmYH"
			"ns86nGxNDnQ+uZNsTQ4uEbma1RNqMR6P6w5h/czP2AEAAGiK8Xh8G8+27SVTv/r6GyeIHEr1Nz99"
			"1T+VlX1mnbRaJ9Z89YvPg6YKxU1dvmCl0BJWIL/WvNi5eowhBwAAAKlJ91uz09uIjAvdG+FjX06I"
			"uaE32ZeTrSeZ7vkoqk+2DobLhnNXBklYgfQORn0CAADkIe52/SxseQ7mjb9mOhhtTRYEQRBk7Q2z"
			"xlmpfyaddL9184aHLkkkYQAAAMAPcbfr5UjzHIys19db9+Y5mDE8mOVgc979uB3HeU6T2cA/W5Ol"
			"SdfrfRy5i4j3tq5fW+W/kpWQhAEAAABHxN0uTS5tst/ctO5HnvhqXNycHC7mYPabm5ZXKqpPvFNg"
			"7cvJtRLqtuSlKQxJGAAAAPDEzcKMhR5In609vYmckNhCu1ptkIQBAAAAV8TdLh10DpgcjGm7ihuS"
			"L97buvYG3NvaU91fU0t/unBNivgmtoq9V3cANej1enWHAAAAzXB2dlZ3CJtI3O222ByMxN0uPbq0"
			"VVW0Lydbe5Fnv7VHU0XoCDpR6+REoYm7oGpOXwuScEBEpEydUdttCNvjoCFsI5MwwocKAAASwI/2"
			"PBxysi8sqqajBgq8LOzecNI9DORg7ZHTnj90Rt5jVaWIUnJ7M0+eMwX12dAkDAAAAJpEVJ93Zan/"
			"xDHV1TPH806h5KEdDGPCeMP5ry6ElwfCy4zn2Ajh5cN5eMAVUTVnF5nIrj1y8q+kKEjCAAAAAGrA"
			"dRIma4F7O3m3Y5zdHGiqzGaa36dR1ixn4a6NEUUAAAAAtWvGDbxlzfKSLmXqplR+ycLNsmfTw0v6"
			"0PoNAABJbNr3RbE38IaVuG4J8ylHA6vf0YlIfiDZVxOTyJxc2dKDcLalH5/STtcvl7s7dHqsR6wT"
			"AAAAoEZNSMJk7VCaJVIP74rWK5OIyHxliXcfzuZRpuf0WOjoRObkigZHXtuXcjSgq8nC5USenT1z"
			"yMFfrpOHAQAAIJ8GXKJCORrQ1Xbkddk84uDFyOgL3iymOjScPYV0nZS9tjHsmLTQpHxWXrBN5e8f"
			"/wpqKCm8hK2EPMSzUNLr9dx/OYkHJRtSwvnnggBKxf2YMGXKjumajwCbPZI1y9KU0MCwqRI1HIyI"
			"+O7jrzK2DI1hPO86Qnj58Bwez7ERwssH4XEFY8Iqxn13pPxAst++9J+aryxxpysTyd2dWcckEb1U"
			"h9bg3E/D9ONT6XB6hOFgAAAAjWL0BZm91aPRF/qG+9DWZP/xfJKtycElcm2uYry3hAUbwrwSx3HC"
			"l6gInBcpa5YTd2WKTftlEwfDwgAAltu074tiW8KI0v05/rf77Is+8Mw6abVOmCtXLT7Pqqj1ZMH9"
			"mDC9I+hLS0xVksLlpioJue5rAAAAANWT7rf0C2PUbhMRGRe6sjciIrIvJ9R9Pr/bkH052XpiFnD3"
			"IVF9siUMDbWOy+hz3x0JAAAAm0Pc7bb0C7fX0bjQlb02kZeD7QZzMHcK26Noa7LgCvZTerMYfUHW"
			"DG+eeS+kdL9186aWLkkkYXzhvOkb4eWB8DLjOTZCePlwHh7UQdzteknRPAcj6/X11r15DmYMD2Y5"
			"2JzRlyZdt2NxSk81Us3p1sHQIKPfoampikRE1wdP6bnjONYJHTzy0jDx3tb1a6uS17YASRgAAABw"
			"RNzt0uTSJvvNTeu+FDWHcXFzcriYg9lvbuj6QBIEQRA6+vVri6g9mlJH6NDU72tsnTxXRSIS1SeK"
			"n3nV1hSGJAwAAAB44mZhxkIPpM/Wnt5ETiB2SP+KMV72mxv/8UIzW3WQhPGF82sDIrw8EF5mPMdG"
			"CC8fzsODmoi7XTroHDA5GNNYZV9Otp6o4ZxJvLdF+tPA9SaMfoemzpQ6/pUtrieXtreS61lXZ3yL"
			"W9mQhAEAAABfxN1ui1pMc9esizIwJH9Be2SdkNcfKQh9TZM7NyeHbWofntx0vHH4ra3XjwRBEKSD"
			"Lb+PsraGsCbctggAAAAax8lzJUpRNZ3AlabE3S49urTVe8NJ9zBwK8P2yGnHLKWqgWKDiO4fms6I"
			"XdzWnt6cPA8UVQYtYXzh/EQhhJcHwsuM59gI4eXDeXjAEVF93p1IfRqZEV2RmRl96SCyc7MSaAkD"
			"AACABhBVs/DbvLRHuRrsckISBgAAAJuA6bjkA7oj+cL5iUIILw+ElxnPsRHCy4fz8ABKhSQMAAAA"
			"oAbojgSAejhU40gMgET2ab/uEGCdoSWML5yfKITw8kB4PoccZGAAAEjCAKBSfvolkJDwb7+3n3zm"
			"6v8Q3hqHV++HBdYeuiMBoCJs6xe+3gAA0BLGF85PFEJ4eWx4eAsNYKmW3fBdlxPCg2Yx+oLM3v/R"
			"6AuzGz/amuw/nk+yNTm4RIaN5J0vK96TMFmzvNuhTxW3xL9Dulcga5alyczs82dxRQBQLTYDqzcS"
			"AKiQkPKPiKi9p1y/tvxVGBe6d59tW3s06Voj/0Jftvb05uSwTaJqOoVeRJ/RHlndyaPy0jC+kzBZ"
			"O9+52hYEQdg+lUZThUiZjqjv3pmTRrO8bM5Uh4a402VSLrm7IxpD1VycEwCqwI7BRwYGAKtJ91v6"
			"xay9i8nBLifE3NCb7MtJFbcbEtUnWwdDY/WMmXCdhMndHbqamEREpioJHZ1I7wgdPXpmzXKmCunH"
			"p8RkYXJ3h06Po5fgEU6gywPh5VFGeHm6IFkbuOsKhPCgYcTdrp+FLc/B3CnzPkOjL8iaocmCIAh+"
			"NyLboxjuXbS9uYVZP2eogKT7rZs3JbWFcZ2EPbwrWtS1gt2R5PVRjqjPpGPK9JweCx2dyJxc0eBo"
			"1nd5NJilcQBQHTSAAUBW4m7XS3vmORhZr6+37s1zMGN4MMvBAq4PntJzx3GsEzpI0I1oDA+2vEFO"
			"ozYRGX1p4qUdU3rqLi/e22L7RwvF+9mR7R3aFgSTSJk6U0V30y5TlQSVlKkzJaHzkkgcvBgZfcHL"
			"tUx1aDh7Cuk6KXttY9gxKfRji33qDwv1C+stCQdZ1rbOsmyr1+vxtsfYgb1uISfxbGyJn37t9/ab"
			"97nIVILPxXqXQMXE3S49urTV3Tc3rfuHUXMYFzcnh6OICa2T56pIRKL6RDm4sIiW91dK91t6R75v"
			"eWPK7Dc3dK1LwoE7VdkjEv2msHYZXZ/j8biEtRZDmc7bvwLj79kSWbMsTQlOdRf0OihDwof1zYQL"
			"ZkLh3EqFqgVrY9O+L8bj8W0827aXTP3q62+cRZTyz2edtFon05NW64Q5O88/Mc9iJzjOVCH3qf/A"
			"ncddgC2MnNOxTlrknvbnLxTAbrpgXHdH6hdGe8/Loh7eFa1XJpuXeSVERPRSHVqDcz8N049PpcPp"
			"UbOGgwE0XFEjwABg44m7XTroHDCjwJiRWcuG5F9PLm1vnmu3J1O635r1JhoXkTmBqJrWSevmjU3i"
			"vS3Sny70Ytpvblr3pSJeVRjXSRjpne23h262ePh22x2Y36dRoMSftc+kYebkitptDAcDqApGgAFA"
			"gcTdbotazEh8cbdLboLFDMkPa229fiQIgiAdbE3dq1mI6hNF7wiCIAgXtNg7ZrjXWxCkAzeta4+s"
			"EzqQAkPzF0ajFYvn7siS8Ny8XGVsGfqMeN51hPDyyRxeBV2Q67rrqoHw8uA8vMIV3R1ZKK8Tcqqw"
			"XZEBgU7GYrdaEt4H5gMAz9AABgAVEdXnXVnqP3FMtbJtGn3pYGvqlHY1MiRhAJAFbgQJABUTVbPi"
			"U37aI6fcLaI7cmPhFDbIDKdAwobYtO8Lrrsj1xFawgAgBTSAAQAUBUkYACSFEWAAAAXi+xIV3Lot"
			"8yywYld+6xS4wuiW+dL2RlqcdxxkDK+q+rA8vBS3ISqhPqznO5tN+t27IryyP7+r1s/5mwtQKrSE"
			"MdyDxZ2CvmCSrC20yFmKuZMF4D9OFUnaTWRef6q9lHDm5O9jUfMki+qMiLRnKXZU4ekX+zjN+5U6"
			"/drw+pAk/uQ1J7xIUfK/X2Wvn+f6AFAEtISFlPfNV0sMPB9H2GN0hqXyz1bUPGWrtT5U1wW5NvWh"
			"7ral6lZSqkbUB4B8kISVg6tPr/u9W0RIgdvZ8vdjMenddqs//tZeH9gAYupDeO+l6IKkcutDufdR"
			"zv3upA6v2voQER7bDlfc8SFiEwnqA3c3ya790wqbBEnYTNoPHucf1CozpJIO4s3SiJeftQsyXRvY"
			"WtaHNTs+uHB8AC4ZfUFm795o9L3bBxHZmuw/nk+yNTm4RNpNLW6xQkjC0kib2aQ6+vgrxzErUtrd"
			"mHyRzKqpD5VL1wBWl4RvdJWfIxwfalRgLyQUSEj5R0RE7T1ldsNtIiLjQndvxE229mjStUb+XSNt"
			"7enNyWGbRNV0zJj7eSfSHlndyaNa0jAkYXwp60Shgg7cnJ/HtJ7hlfGlG7VON7zsDWBlyvvOlpy4"
			"NL7i1ZrYFbn3kKGuB+l+S7+YtXcxOdjlhJgbepN9OXFvul0AUX2ydTA0Vs9YNCRhkEn+dppsv+xx"
			"kM0g8ZuVvQFsYRMZ3ia09FSmgkbWuo4PCaGOcU7c7fpZ2PIczJ0y7040+oKsGZosCIIwL5p1YDK9"
			"jrYm94Mpl3S/dfOm+rYwJGHriL8h88A5h5xnZ8/cx/w0gFUKX8zAQn2ok7jb9TKieQ5G1uvrrXvz"
			"HMwYHsxysIDrg6f03HEc64QOHmk2tfcUL6EzLm5aNLm0yU3o7kvBbd7bYjtBq4IkLKjuDx53JwoF"
			"xYZX935zpdt7lcfM7ZvLZxcki5ddl/jEUq7UH97Sz1r94QF/xN0uTS5tst/ctBaSJY9xcXNyGJGD"
			"UevkuSoSkag+cYeWzZq4jIub7hNvtQuNakRUV1MYkrAS5G+IQgs8JzjZaaWF0Ywx+FABTqo6gMvN"
			"woyoZInIHZIfOYGd580NsyrbuLjp7rZ3u/TaiszBFlvaKsJ7EqZM/XuNTxUiIpI1K1Aga5alyd7s"
			"smY582dxRRwqsAMRfZFroII38Y5ARMItEccNYODBhxo2jrjbpYPOAZMsMS1Vy4bkX7sdjmRfTq7d"
			"nkxxt0uTR27WJu52by6GryNysPhGt1JxnoTJDyT7dFsQBEEQOjoRydr5ztW2IAjC9qk08vKyOVMd"
			"GuJOl0m55O6OaAxVs8qoWSl/XzbjNCtevxIasPeqb2+IerMiG8Cy773y60MD3tnk2N1VSX0IhMff"
			"qHzO31yoi7jbbVGLSZZmXZSBIflhra3XjwRBEKSDral3NQtxt0vXXt4l7nZv9KhWtHoawjhPwuTu"
			"jmi9MgMFdDUxiYhMVXLzstkkzXKmCunHp8RkYXJ3h06PmdmAR+vdFcJZzuqnX9BU6/15Cdu017tO"
			"nJR/rNDVv2ZZmDGcdIPDwdojZs77h6bbWza/oBi7qsBq/QVnlxyrHN9J2MO7oi0dMp2PD++KFnWt"
			"QP8kEREp03N6LHR0InNyRYMjb5JyNJglbQCbrjEjwJr4pctZqs2dJr6nwBtRfd6dSH0a5boyawSj"
			"Lx0UdsmxdN6rYZvJ6R3hpSybpkkka9ZU0S+I2ju0LQgmkTJ1poreeUkkDl6MjL7g5VqmOjScPYV0"
			"nZS9tjHsmBRq8Wafsufm9Hq9s6jZzs7O3MNHT92nJfMEyxfWzM4TuVS4WX7lUkm2Pp9nYUNngflX"
			"rtktYXdRYJ47At06dOv01P0k62HDXdjtEUuxOz+4ofBrX7aeJNvKMM/S1+7PdnZ2lnnrSZaKLPEX"
			"9NOv/d6+Nw8T9tnZmVuYfM0UVXvn86j7Z9ozunX8vGTZerRnbjyxdTX42v35Y7ceWs982dLmmU9N"
			"9dqDLyHpPEmWSnP86fV6FAw7W01YVldjYnY/F4H1sJ/3lK8idusJj/Nsobp/dnZG6C3liaiaZSTy"
			"7ZFT2++D8Xhc16ZTccffK9N5+5c3Il/WLEtT2NH5Xn4266AMCR80icj9Ngo8iJthyTyRU5fPHDdP"
			"kqUSripmKtsuUswmksec9vUm36Ur38ck8yR8rzPMtnzOJfPkqw/uex3xdmdebZKVVFMfCnyDiqoz"
			"yaNaPueSeco/PmTHbX2glO81EW3ekLXxeHwbz7btJVO/+vobB1LiuzuSzbmIrFemfmG097ySh3fn"
			"48VeqkNrcO6nYfrxqXQ4PUo+HOw2ZVcCt03raV8In5vILElsPMdfMq67IDPL+WFEnSlW0/dV0+OH"
			"puE7CdM7fRq52eI5Pe7oRHpn+603SOzw7TYzMF/v9Jk0zJxcUbvdwOFgnP/q4jw8ztWw924dInLu"
			"JLoIRcbwKvnSKmDXlfnDifPPxTw8LjMMzvceQKn4HhNGpHeEhbYsU5UENfhcCs+6OBdAwbj8PvO5"
			"PY9ucOvZALY5+K5p3JmNdIzYY9iTwB++W8I2ROShoaQf7tx2pIIvd33ARSgAABoBSRhfOL+NGufh"
			"pVZtSlrB3kt9EQpmD/D85vIcG9USXpqqmyK8On6k1fPm4uco8AFJGBSHq+MaV8FUgrv7cDfuLWhc"
			"wM2C3QsQgiQsBEcKKFBVw1CacRVWAIBVjL4ga3bged9wH9qa7D+eT7I1ObhEYAZB8Cayj1MFUCYk"
			"YXzh/EShYsLb1OGx3t4rOssv6jr4PNc9nmMjhJcP5+FBHsL/l+7P1XUQxmkAACAASURBVN5Trl9b"
			"/kqMC929ETfZ2qNJ15rfjWh2r6HQDY78RfsdXZk6jqmKgccrtEdWd/KomjQMSdgaKT+5CV+pHOq1"
			"rAuyKcluU+IsSWVN743ez+ig2CjS/ZZ+MWvvYnKwywmxd962Lyer7zXUui9FPl5OVJ9sHQyN1TPm"
			"hiSsaI0+0tUIB9mUuLgR5AbWdlTUJTawPkAZxN2un4Utz8HcKfP+Q1uTBVffMPpCR6frA0mQNY15"
			"bHvzG968Xn+mEOyrlO63bt5U0BaGJKx8JZ3HlBm+RWpS4Jtb5Bj8WX3g+QxEnmOjVOHVkaak23uV"
			"Hx8Ww0MmByTudr0UaJ6DkfX6euvePAczhgezHCxQNnUv5z5qt0fOVKHWieWYqso8dldxffCUnjuO"
			"M1X0jvCInjuO40yVa7/5S7y3xfaJlgZJGHAPB+Wg+hvAKrAkFUB9ABbqwzoSd7s0ubTJfnMT04do"
			"XNycHC7kYCTdb+mdZGPqWyfPVZGI2nsKtbwGtvaeQn7zV0VNYUjCOIYmK2AF68M6Z2CNU14esGTN"
			"OD7AGnOzMGOhB9Jna09vIiaIquk4z+mR28mYz0LDW1mQhPGl8ScK1fjFUNIXYcJXlGC2nG9u2ZcB"
			"K6XuFVQfGv+5qBVHey+qPhQfHtLTdSDudumgc8DkYEzT1NIh+aJqWie5W7Hi2+CKhSRsXaBNvkrV"
			"7m12DH5SqA8ALHwimkbc7bb8jkKvgCaXNgWG5Ae5VwITBOlg9WmTK1TUEMb9DbyrsIEfzjsC3TrC"
			"LTl36o6EQ5zVh+AIsHJ+3N8R6NY5055RqhHcnO2osqzHy0z1KpbcAxsgDed/zLGwqJqOGijY7dKj"
			"S1u9N5x0D012SnvktImIxJHjjCLKUz+2tac3J8/ZVZUFLWF84fossPX4NqpP2jeXi4tQ8IHrzwX3"
			"Ktp7WY8PeHMhKVF93p1IfRoluOBqHka/gKa0hNASllVRPxaR2WSwxr/UZ/UB6VezrXEVBaiPqJoV"
			"jPVrj5zKRhSiJSwZpEpQIWRg6WAg9trAkRY2zCYmYd69d7jE0XlMa4Cz7+ZUb25Jp0A2FD4XeXC+"
			"9zgPD6BUzUjCZM2yNNl9rHiXw3WmSmgayZrlzJ/FFTUIZ2lE43H8OzvRCDDUh+TC7/X67b31e0UA"
			"G6YJSZisnQ9mA+SU6Ug63RYEQdh+exjOrUx1aIg7XaZY7u6IxlA1F2bEYeuOQETCbeXb5S0N4uNr"
			"LPUVKJbLsJP52A/1W8f94LX9oz4A8If/JEzWzncsw7vqmvxAsq8mJhGRObkiNt2SNcuZKqQfn7LF"
			"cneHTo/1imPOLuOJQrxlNhAl7s3d6BFgyapuWSfQVfzBwec0Sm1nR4YTTbxBUDnekzBZO9+5enz8"
			"1ntqvrJmDV1yd4c5f1SZntNjoaO7ydngSPGKjwbkJW0AlVt1TG/wRSiSf12hTQUAIAbfl6hwUzDJ"
			"JG1Wonf6e84LZ0BkG7PmMRIHL0ZGX/ByLVMdGs6eQrpOyl7bGHZMCo79PCNiS85m5elKZr/ewiV+"
			"4cIvvF6vF7HUqjWzq8gzT/6llr0KtkTdP9Oe+SfnJ9k/meMJ7+eVez7zPNm2RZF7bL6LiIj2e/tE"
			"gdcfuX+4Kgm8wmrXM1926Xoi13xWxDzZthVZQu7rml3PoqfuU8r9M58z/ye3iKVWlzAvNtXxMC6e"
			"s+DU2PXErzn51gGKNx6P6w4hlqxZDmNhCJgydaaKNzBfYUfnzyZ5HZRh7vV74p4mLElVmLjc//Cv"
			"XjDtPDmXco+bCc9jWrLm5RtNuOeTz7nyvU4yT+b6EPPmOuS4kyKGgq18U3iqDwXMnKw+BCpepp0f"
			"XVhrfUg0aQPqw+o3d8nU8o8PSQ9662I8Ht/Gs217ydSvvv7GKcJUodaJFXg+OyPPOmnNz87zJ1kn"
			"reASznzuqPLIrdSE6+5IU5Xc+0Btn9r26bakmrPMi0jWDtvGxWyw10t1aA3O/TRMPz6VDqdH1Q8H"
			"SzukAEMQNg+beDWsC5IH+MgArLv2nnL92vKfGhe64t4p0tYeTbrWyL/LkK09vTk5bJOomk7URfTj"
			"yt2tjKzu5JGW7zbf+XGdhEXQO30aOY7jvBhY/Y4emMCkYebkitrtDRkOhq+l5ij4LEhYb/how2aS"
			"7rf0C8N7wuRglxNibuhN9uUk182FRPXJ1sHQWD1jmZqRhJmqJM2uMqF33NYxwUvB/Gl6R/BnchvR"
			"pNCVKbhX5RAE7+7dafopMEIiM4ecZ2feVYJjG8AK+dLNvBKOR9Cj4tWgqvqANxcWibtdPwtbnoO5"
			"U8joC7JmE5GtyV6G0DfYcqMvyJrWdyfJfvOXdL9186betrBmJGFV4/jbCJqIPQUSXZAAAEuJu10v"
			"O5rnYGS9vt66N8/BjOHBLAcLlHkjxkYLk+j64PWe4zjOVLn2m7/Ee1tsx2cdkITlUE2uhoywdvne"
			"goIvQoH6UDuu3oKVwaBPExpI3O3S5NIm+81N674UNYdxcXNyuJhoSfdbekeOHufV8mZv7ynkN3/V"
			"3xSGJIwvnJ+Jw3l43HIbwDjfezyHx3NssbjK1TjWyDcXyuZmYcZCD6TP1p7eREwQVdNxntOjWXfk"
			"SgutazXY+CQMPxOT2Jy9lOeVBr90m3oV1iTS7qUK0pGSNrE5NT8P7CUonrjbpYPOAZODMa1WS4fk"
			"i6ppnSRr4IpvaKvMxidh0FAcNzOw18EHgKQKTOY4Pj5AQuJut0Utprlr1kUZGJIfZHhD76WDZKdN"
			"1t8QxvkV8zcP5ycKJQ1vdlHsxePpJv1iFkigYCrWgDdXe1bpJhPXB6533SbV6jJw/eZCjUTVdNRA"
			"wW6XHl3a6r3hpHsYuPZBe+S0iYjEkeOMIsrnD9jHtvb05uQ5O38NkIRVJS4vqY9AgnPHEW6Jq6gy"
			"qvKLMGZbDjluUYP7H/mrpVCnkuoDqhlkI6rPu7LUf+KY6uqZVzH60sHW1Km3HQxJWCL4pQuroP8R"
			"gF84hq8LUTWLOtS2Rw4PR22MCeNL6hOFqj24lH4eU76RHLWcZpV8BNiK8Oq9UiufJ6nN6gPnnwvI"
			"I8Wbi5FesHaQhAFkt4lXYW10ftOgb/HikvKeul9EQABQPCRh9Ul+kK33mwNfujFWXISivP3WoEwC"
			"KoDjA0BjIQkrThEHo+pPFEp1B8mqw0u5S1eEV+i3RYbLgAXC4y+R8sLjLzAqqeJxlT2Uudtz7b3y"
			"6wMvZ0dyVR9gY2xiEuY1znP2NQO1yfo1s0FdkBsF130FgKpsYhIGkA07Bh/pVzpcNrBBbVAfAIgI"
			"SRhveDxDjcH5eUzrs/eyydfWwvPe82JDY1Iqs93F8ztLeSpe/vqAXBDqhiSsySr4TsLXntsAdoeI"
			"SLjd+AawAutDo6sWvrxdqA9QAqMvyJodeD67HbetyYFbc7uTbE0OLpFd4lUtBplVM5IwWbMsTZ49"
			"dDxTZWGaO3X+LK4oiY06yFbzYpt5kMVVWCG7jTqM8AY7nweCkO6PiIjae8r1a8tfh3GhK+6dIm3t"
			"0aRrjfwbENna05uTwzaJqumYqkgFpEbzVa3QHlndyaP8aVgTkjBZOx+I84eWe4fO7VNp5KZhDFMd"
			"GuJOl0m55O6OaAxVk5qBlxOFYvAVXuggmze8YJpY+AgwvvZeCM/hJYptk790l752nt9Z4j48qIF0"
			"v6VfzNq7mBzsckLMDb3Jvpwku093OUT1ydbB0Fg941L8J2Gydr5jGV62aaqS0NHdh5MrW3ogM/NZ"
			"zlQh/fiUmCxM7u7Q6bFeWnSVHfc3+QumDrxfhbXU+oDKVgtudztXgXEVDJRE3O36WdjyHMyd4jWA"
			"GX2ho9P1gSQIfcMt7PdlQegb7gya24LDtJXZmix4Zp2cflvafJo3KVRA0v3WzZucbWG8J2Gydr5z"
			"9fj4bXiKcjSw5i1cyvScHgsdncicXNHgSPFnoqtJU5rBuFNLByIHB1mcAhmtKfWhmR3fzYP9DGUR"
			"d7teejPPwch6fb11b56DGcODWQ7maY+cqUKtE8txvC7L65v7z2dPrg9e77kDma699ivvFt6O4zjW"
			"yU0n2I9pDGfT3OWNvjTpuqOhpvTUnVW8t8X2m2bC9w283RRMMklbmKBMncO325LXwiUOXoyMvuDl"
			"WqY6NJw9hXSdlL22MeyYFHMCTq/XOws+dR+wzeNJ5lmYLTyPV6Lun2nP6NahO4J/O5HVS4Xi8Ur8"
			"G5L4JYnXM98bgRdBvV6PnSc4Md2aI0rSrCccQORS3qPQWWBpXwU7j5+B7ff2/aCT7g32LWZmc+dh"
			"K+HCaz/TnrkvIXbNq15XthrFlrjvvhdP5CvNumY2TLaOUXD/xK0nsHyyOrZszaG1lThP5nqY4dMd"
			"XHOGGpWhhJK9g5EliwFTQMJ3mVbteSr0OA+VEXe79OjSVnff3LTuH0bNYVzcnByOVqymxbSctU4O"
			"20RE7T2Fnr6xqU1vbkh54mZrovpEObiwiPzZpfstvSPft7wBYvabG7rWJeHAnarsEYl+U1g7T5fo"
			"eDzOsXS5mEH4juPMhtcvjLSXNcvSFHZ0PpEydabKrIMypNfr0a3jfXP7D8LcSUtmYGdbvqrEM7Pf"
			"07HLRj7NZD7sKfKVhkqWhReWOeDkuzT4HkWHl/K9jh2Jn7s+RISXqvLELRv5NL1AeMnqQ2rsGsqv"
			"D9nXVtQ8qYJPO3PcsqGn6T62S1ZeTn3o+dcf4bI+FLD3GmU8Ht/Gs217ydSvvv7GWUCU7o/5wj9p"
			"tU6mJ63WySwPmCqkTNmp8wRh1gA2f5DgsXXSmq/QXzs7p2OdtIhImS7OvLhQDlx3R5qqJHiD8G37"
			"dFtSTZI168XdoSAtjLR/qQ6twbmfhunHp9Lh9GjlcLBZC0oBsRa4qlo0N/KiFdMFifpQuOp3aZIt"
			"+k1W+VfFs+ZGDk0l7nbpoHPAjAJjRmAVMiRfvLdFs5FntvbU7/ZkZ1FN66R188Z2Z366cDKk/eam"
			"dV/KFwXXSViYcjQQqT3yU+V565fe6TNpmDm5ona7gcPBlrV+czBYKl3jPBtwhi+h9IssCy9+bZVd"
			"hILzro0V4RWbRjQ9KQlq9jtbN39wRdnHh+rWBkUQd7utQH+iuNulyaVNgSH5C9p7ij8wf6X2yDq5"
			"6QiCIAhS4NIXRESGN4pfOnDTvfbIOqEDKTA0f2GUWibCeDz+5JNP8q2kYXq9njdgwhX3wWPTnSUf"
			"zoSzsTPPxoSl/syzSxVx1HDzD6/tx38hxa0/4+tNvkuTzBk/z3wA/i3FzZM6qibXh4iVF7t+vutD"
			"8fMkn42dGfUhYSQrFynifVwYxbj2xuPxzs5O3NQff/zx/fffj5v67Xd//tUvd8uJi4iIbE1+RM/N"
			"e0P5zWGii3mVyosmZxx8D8yvl39o4FAZv9t4fr15xLyuwCmQdxI0MfK8f1AfkivqdfG8f1AfklvX"
			"17WWRPV5V5b6TxxTrTuU2amVuTPBhnVHFqauZudVPyITDQIt+5ARP4414xjV8gIO7sOE4dV1H+6I"
			"8AppVCho98buPb8+FNv1U4aiIky5nsLGbpdTHwoeWp5/PH54hSVB9+LaEVXTGUV2RFatPXIKiWQT"
			"k7Czs2fkDwO6k2CB5Z9kf2ot108qdZ35N8GuIfnaMuzSxHOuuAprkvXwWR/KUPhL4K8+FLCe9awP"
			"UeeuLRwt78TMlvzvTqa1BcJYOX+SlS9fG0CJNjEJ89xJloElWpWQ+gib7YhcwXG88O8Mbr57nDsc"
			"X4U1f30oaT83NYfILcOHOrUMCcGq7MQ/pjFpx+xnZ6q/GHdCD3LKs56iYihjbQAJ8XydsJIwjfOb"
			"/luH7ZhbY+7L3JAXCyXI197TyD/w4Dphua4TBqts+MB8AYebNbaQcnHX+gXV4fNjjgoJsOk2PAnz"
			"OTggrhOkX+uu4qQK9QcASrHBY8I8fB1eOW/65j+8hTMfo0ff14T/vVd3CFRaZ5mQ728FPnZdLIQH"
			"wC20hPnQGNZsDjn+rX35SbwgqKgWLLy/ALAO0BJGOKA32sKIe66avjZSzhas6LaoXm8/VdMUADSX"
			"0Rdk9i6NRt+/DZGtyYFbErmTbE0OLlF2REUuvNlnR/o29JygRp8wyJ7z2NxX0Uw45w42xab1lhZ7"
			"dmTaI4S32FQhZeqvZP7MOmm1Tqz56hefO85UoYWSQuRcbTjOObSEufDbukl4Hvi1dtLmUrkGVwEA"
			"kHS/pV/M2ruMC11x79ZtX06IuaE32ZcT9+bavBPVJ1sHw+h7iiMJW4Af61xDz2OZkudbSLMAoDTi"
			"btfPwpbnYO4Ur8fP6Asdna4PJEHoG25hvy8LQt8IPCajL3i8nk2jL8ia5pXOuw5tTfZK3sxjmxXO"
			"l3YXN7xyr3d0YVXS/dbNm8guSSRhPi6+Pzhv+q4rvIQDv7D30siTb1WNs123COHlwXl4UAdxt+sl"
			"LfMcjKzX11v35jmYMTyY5WCe9sjvN/Tu6nh9c//57Mn8cXs062TUn87SpOuD13tu2bXXZuXdodtx"
			"nOc00Wcb9Qsd6+SmI88Xf0rP3VV2hEf0PLgqIvHe1vVrK+qlIgljud8uaAzjyMJ4L7R+ZbU65QoN"
			"fkfLFgDUQ9zt0uTSJvvNTeu+FDWHcXFzcrjqBtotpuWMeew1hXV0Zk5vZe09hW7e2ET2m5tZmag+"
			"Udy57Dc3NMsJRfWJ4idWrZPnqugtPtuQvyqiJU1hSMKAUxj4lUOSVi7kWwDAKzcLMxZ6IH229vQm"
			"csJqtiZ3aOqNl88bZmILzXhzSMIW1NwYdnZ2Vtemk6gmvMwDvzZ47xWQcvG893iOjRBePpyHBzUR"
			"d7t00DlgcjCmMSnPkHzr9bXbuGZfTq6XbP/e1qw30dae6n4hzQar2dpTv6d0pfgWPd6TMP801amy"
			"WOKVyZplabI3UdYsZ/4srigRdErWAOPu01iSdaGVCwCaTdzttgL9ibMuysCQ/AXtPcUfmB+jfXhC"
			"B5IgCI9eby1rCWuPporeEQRBeERdxS+0Tm46giAIgjTpWqOEOVh8Qxjn1wlTprPky38UyLkiCpRp"
			"MOWSNWuewLkSjAPdlGsacXKFLVzxKxlcfwugapt23kCx1wkrmHe9ralSxrXAStTc64TpncDIOSKS"
			"uzui9cqMmNfLtvTjU9rpysz8dHqsR8yfQA3fbZx/4AsPr9hx9+u49xI2dxWA573Hc2yE8PLhPDzg"
			"iKg+706kPo3MJlwdbMboSwfxnad8J2FE5GZXzoj6HZ2IHt4VbenQTSDZBi5lek6PhY5OZE6uaHDk"
			"TVKOBnQ1icrZlkP3Tekw7j5eXOKFHkYA2GiiajqJ+wA50R45S2JuwA28TVUSVFKmzpSEjt4RXsqy"
			"aZpEsmZNFb3zkkgcvBgZfcGczT80nD2FdJ2UvbYx7JgU+rHFPvWHhfqFzEBRp9fbXz5P4SXhIEvc"
			"1lnqbfV6vZxbX2j6KvB1+YVVvl/FlTyjCOz+ybvnG13i4ySewj8X5ZX4hZzE08QSgLJwPSaMERoL"
			"NiuRNcvSlNDAsKkSNRyMiFK0fq//aJuKx2Bh4FcIBngBcC3x98Wa4HpM2DriuztyPjCfHt4VrVcm"
			"W0JE/uiwl+rQGpz7aZh+fCodTo9yDAcjotovV7E2El7vfpMs720EAICNwHcSpnf65N1f4PDtdkcP"
			"lJzTY2bUvt7pM2mYObmidjvTcLAoyMMywvXuGZFtXRjjBQCwufhOwojcq3QIgiCpZnSJqUruI70z"
			"n4lMVWKWya7qr0bOm76Th1fLuHsu915jGr243HsenmMjhJcP5+EBlIr3JIwbaAxLCj2PRBTZ6MXc"
			"nBEAAKAJZ0fWTZh9lTr4+lxiYaz9puZeFNXoBQAAEAFJWBJCZS1hnJ8RHRkeP+lXrXtvde7VxDeX"
			"EzzHRggvH87DAygVkrBU0BgWEL7i1+aJvJA9AADAakjCEkKnZMDGp1/IvQAAIC8MzOcL5ycK9Xo9"
			"nsfdV7L34q4xsRr/b27dIcTiOTZCePlwHh5AqdASltxGN4Z5ideZ95Sr3KsSGG4PAAAFQxKWyibm"
			"YfyMu68J0i8AACgFkjC+cHWiUOMGfhW99xa6HfPi6s0N4zk8nmMjhJcP5+EBlApJWFob0RjWuPSr"
			"aAWnXwAAAGFIwjKo7rJhFdv4nkdC+gUAAJXB2ZHZuF/PxadidZ0olPBO25yfx5QvPPa0x1JuLrTW"
			"e69cPMdGCC8fzsMDKBVawnJah05J9pIT9UZSB4y7BwCAeiAJy2ytBoch/VqDNxEAAJoFSVgexQ8O"
			"q/hEobS5F+fnMSUOr56BX+uy92rAc2yE8PLhPDyAUmFMWCHWc5z+mip34BcAAEBCnCdhsmY5nqmy"
			"WOSWyJplaTIz//xZXFGR/G9x5GH8Wxh6DwAAUCeukzBZOx9YfUEQBGH7VBpNFSJZO9+52mZLAkx1"
			"aIg7XSblkrs7ojFUzRLDLDIP4/xEocaGV/qZj0k0du/Vj+fYCOHlw3l4AKXiOgkzVUno6O7DyZUt"
			"PVhoz7Lfvpw/kTXLmSqkH58Sk4XJ3R06PdbLjhTNKtziIv0CAAAI4zoJYyhHA2uommSq0mM6dxzH"
			"OafH0ryBS5me02OhoxOZkysaHCn+YnQ1KbMZzFfWlcMgK6RfAADAtUacHalMncO325LuPxQE0235"
			"Ggqdl0Ti4MXI6AtermWqQ8PZU0jXSdlrG8OOSaEWb/apf26OX5i1xP3f6fX286wnHGRxERZQ0uv1"
			"uIqHgmdXuYVnZ89mT933gouYz87OeNtjbIlbyE88CyU+TuJp5ueCi3ia9bkggFKNx+O6Q1gqOK4+"
			"MApfmTpTxS1S2HJmihMaNUblDkFwgg0wUD0H7wIAFKXM7wsejcfj23i2bS+Z+tXX3ziQEt/dkbJm"
			"vbg7FObdjuYryx93r+y1/UFhL9WhNTj30zD9+FQ6nB5VMhwsCCdL1gj9jwAA0CRcJ2HK0UCk9miW"
			"MFqaTHqnbw1eOI7jOCPqM4PC9E6fScPMyRW121UNBwvKlYdx/quL4/AakH5xvPeI+A6P59gI4eXD"
			"eXgApeJ6TJjeEcINWYuFpipJ4QmmKglqyeElsA53NOLePNnt9fYxhAMAAJqC65awxkLiVZkGNIAB"
			"AABEQhJWkoydkpyfjMNTeBGXv+cpvAgILzOeYyOElw/n4QGUCklYeTBIvzxoAAMAgMZDElYq5GGF"
			"w/0fAQBgTSAJK1u6PIzzE4XqDm9FA1jd4a2A8DLjOTZCePlwHh5AqZCEVQDtYYVAAxgAAKwVJGHV"
			"QB6WB7ogAQBgDSEJq0yiPIzzE4XqCC/FGHzsvTx4Do/n2Ajh5cN5eAClQhJWJbSHpYUGMAAAWFtI"
			"wiqGPCw5ZGAAALDOkIRVb1kexvmJQlWFl3EQGPZeHjyHx3NshPDy4Tw8gFIhCasF2sOWwIVYAQBg"
			"IyAJqwvysEjoggQAgE2BJKxGbB7mJR+cnyhUcnh5M7DN3nt58Rwez7ERwsuH8/AASoUkrF5strHJ"
			"TWK4EhgAAGwcJGG1Qx6GQWAAALCJkITxoDF5WAnnMRXZAMb5aVYILzOeYyOElw/n4QGUivMkTNYs"
			"xzNVmHJl6j+XNcvSZGb++bO4Ih4JGzlUH12QAACwubhOwmTtfGD1BUEQhO1TaTRLu5SpM2pHL2Gq"
			"Q0Pc6TIpl9zdEY2hapYfbREihuqvL2RgAACw0bhOwkxVEjq6+3ByZUsPZCJZs/YuhL4RmlnWLGeq"
			"kH58SkwWJnd36PRYry7m3LjumizoPKayhuFzfpoVwsuM59gI4eXDeXgApeI6CWMoRwNrqJpEpip1"
			"InIqZXpOj4WOTmROrmhwpPiL0dWkIc1gPq7zsOJgGD4AAGy09+oOIAll6hy+3ZZi27PEwYuR0Re8"
			"XMtUh4azp5Cuk7LXNoYdk0JjP9mn/u8wv5CDEoFJvxw/WeEpwswljl/ORzwoQQlKULKiBKAs4/G4"
			"7hCWih5XvzgwX2FH588mex2UIf4HjEOh2Bzmr365d125r4Xnd5YQXg48x0YILx+Ex5XxeHwbz7bt"
			"JVO/+vobB1LiuztS1qwXd4eCtHJc/Ut1aA3O/TRMPz6VDqdHTRsOFmWduiYxEh8AAGCO6yRMORqI"
			"1B7NEsalF5rQO30mDTMnV9RuN3A4WBSh+WdN4oL4AAAAIbx3R5agsc3LfHVNJtbEmAEAiBr8fZER"
			"uiMrxnVLGAQtdE02Iq1BAxgAAEA0JGHNIjRqlBgyMAAAgFhIwviSrOm7tlFiaVrma8jAOO84QHiZ"
			"8RwbIbx8OA8PoFRIwpqL595JtIEBAACsgCSs0cK9k1ylYsjAAAAAYiEJ40umCzRXN1AsWXi1JYKc"
			"X94a4WXGc2yE8PLhPDyAUiEJWxucNImhIxIAACARJGHrpPbeSWRgAAAASSEJ40sRJwqVmIotDa/+"
			"DIzz06wQXmY8x0YILx/OwwMoFZKwdVVxq1j9GRgAAECzvFd3AFAqNyXyM6SSUiVkYAAAAKmhJYwv"
			"5ZwoJITSo4wNY1HhcZSBcX6aFcLLjOfYCOHlw3l4AKVCErY5CkvFgmvwVw4AAAApoDty07D3O1p4"
			"kDmRQgYGAACQGlrC+FLhiUJxDWPL2saC4XF1dX4i7k+zQniZ8RwbIbx8OA8PoFRoCdtw4YYx//Hy"
			"9i10RAIAAOTSiJYwZepMlWCRrFmWJgcfuU+c+bO4IoiQqmEMGRgAAEBe/CdhytQZtRfKZO18IEbN"
			"bKpDQ9zpMimX3N0RjaFqlhhhkeo+UUhYno2dnZ3xnIHVvfdWQHiZ8RwbIbx8OA8PoFScJ2GyZu1d"
			"CH1jofB8xzLs8KzOVCH9+JSYLEzu7tDpsR5asYO/5X9C1N/CpNqDLO4PAACgcpwnYaYqdRYyKFk7"
			"37l6fPw2WKpMz+mx0NGJzMkVDY683kvlaEBXk4VmsLNnZ6UFDAAAAJBI0wbmuymYZJLGFIqDFyOj"
			"L3i5lqkODWdPIV0nZa9tDDsmBU/AOaOz3j7zdNYY7s+Dko0qL9QqSAAAIABJREFUcRvDevu9srcV"
			"qIR8vHa2pNfruf9yEg9KNqSE888FAZRqPB7XHcJK84H5smY5DEuT3YH5Cjs6f7aA10EZxnH3E3s8"
			"4tB6hldVj+R67r1K8BwbIbx8EB5XxuPxbTzbtpdM/errbxxIifPuyEWmKgmCIAjC9qltn25LswH3"
			"L9WhNTj30zD9+FQ6nB5FDwcDAAAAqF/DkrB4eqfPpGHm5Ira7fBwMO5x3vqN8PJAeJnxHBshvHw4"
			"Dw+gVI0YE6Z3hMUGLVOVpIVHgblMVRLUisIDAAAASG1tWsIAAAAAmgRJGF84HwSK8PJAeJnxHBsh"
			"vHw4Dw+gVEjCAAAAAGqAJAwAAACgBsJ4PP7kk0/qDqNaDoe3PYT6uBcJQ5UAgBD3IsZ1R1Gd8Xi8"
			"s7MTN/XHH398//3346Z++92ff/XL3XLiWltoCQMAAACoAZIwAAAAgBogCeML5ycKIbw8EF5mPMdG"
			"CC8fzsMDKBWSMAAAAIAabGgS5jjzP2gW9r1b+Hv27GzJ1Li/hdVCs2R4xzNUD2gKVABolg09O5L9"
			"ZAk4La4hSjoguu+/v27Uh6ao5gsS9aEpSqoP+/s4O3IOZ0cWbkNbwgRhfmzFb51mcd+7Av/81bpQ"
			"H5ql8PqwUDFQH5qlpGoAUJINTcJcOM7G2cz9gfoQZzP3B+pDHOwPgKJsdBJG/B1nl5wo5Mz+SuVv"
			"InJDnJ/HlD+8UutDsXuv8PoQGd7y+lCZuipewvrAw+diSX0oMLwy6gMPew+gLpuehDWFE/O4vK1s"
			"rEZ0QKA+AAv1AaChkIRx1xgWNh8zXsnm/K3wuj8qgvpQ5Va4hePDAhwfAAq0oWdHhg9X/hGWw4YQ"
			"9t6G5R1wF9bM4Q0V3feo+Dco6qU2qz6UEePm1of4bVW2uVRQH1wl1QfcO5KFsyML17CWMFmzHIal"
			"ySRrlqXJzPT5s7iiKBweWyOVHWZDdkPpmlUfymuTaMhuKB3qA7t+AChKw5IwU5UE1/apTcZQNRen"
			"Dw1xp8ukXHJ3RwzPF8k9zvLW6VDXL86yj+ZNgfoALhwfWDg+ABSiYUmYTzkaWP2OzpTImuVMFdKP"
			"T4nJwuTuDp0e6xFrWKLG42xdJwolPJRzfh5TseEVPhio1L2XP0Y2vHB9qPdLl6uKF64PXIXnYmPM"
			"H16pqR6Hew+gMs1MwmTtUAqmVsr0nB4LHZ3InFzR4Ejxio8GdDVJ0gzm4n8QLtX9dbhRGlQfoAKo"
			"DwBQrPfqDiAL5WhAV9vz1EocvBgZfcErMNWh4ewppOuk7LWNYcek4I+tMzoLPJ0NunQL9/fp2bNA"
			"SXieUkt8vV7v2dkZMUfV+TxFb90vD8wTmhoxTxFbT1wSEc/Cq3ALC3jtRay58BK/PpQeYWi1bGHt"
			"n4vZwxri2d/v+ccHdp5aPhduKrjvfjbiP7lUbN3g73NRQTwAZRmPx3WHkJYydaaK/0zWLEtT2NH5"
			"szm8DsqwVb9i+bl1a9zVFwu/amtlG8qjrDdl1YvctPqwZG0bUR+SbRf1ofAN5VTSmxL+GbDexuPx"
			"bTzbtpdM/errbxxIqYHdkfIDyX77Mlj2Uh1ag3M/DdOPT6XD6VGG4WBE1IROB/RIVgn1AVioDwBQ"
			"lAYmYQ/vitar0DAvvdNn0jBzckXtdqrhYEE8nJFe2TEUJ9ytxE994CAQ4Kg+AECjNTAJ0zsCe1qk"
			"qUqSarrlkn8pClOVBOZpFrWckR5u+q73aL/wk5rzlvlSw8tfH9Zg79XVxMLhrmPrQ43hJTk+lBde"
			"IfWBwzcXoDINTMIqx22nA9QC9YEHVV4ufznUBwDIDEnYMvwP/tgcPHzpoj4Ay68PC+dLQvV4OD4A"
			"ZIB7RyaYvY6Pd5IBQIUMElq5Ek6GIpX4LqR8hXUd7qt5p1Af0sLxIf9W8ivvXejh3pEM3DuycGgJ"
			"S2pjGz9wplWkiusDJ191hPoQA8cHAMgASdhq6IQCFuoDsFAfACAzJGGJVHac5fxEIYTnylYfsPcy"
			"4zk2Itrf77kP+MzDON97nIcHUCokYVAbLr+wklo++qTRL60ua7zTuLqyfFNgj8EmQBKWFIedDvlH"
			"Y9Q10sj/TuJmX2a3UB/Yl9a4V8dDfWjcTnPFHR/YV1TUS6vsbUJ9ACgbzo5MuejskFD2yVAJD385"
			"j5LJFy/wcBw+qiYKgJuzIwOLButDtpeWaEPJ1ob6UK9q6kNlb1P19SEy5aq3PuDsSBbOjiwcWsLS"
			"qeagz8+pcIXzD7IC8wKb+2OXrQ/lvTTUh6ZYUh9wFmFywrrUB4CVkISlVsvtjNYD+7W08KC5u3Oh"
			"PqzTSyvbRtUHX3NfWtmWNBxip8G6QhKWXRl5GOcnChUSnhDzNP/urHnvObEvzbUJb24GSepDI3fd"
			"qvpQmWbtvfKODwAcQhKWBYeD9Pm3ZFc1vqNtVX1ANQnLVh+aMVK7afWBh726zscHgHhIwhIJH6QS"
			"Dg5LcnTj4Qi4kvtyn+UeoJrkRitFqWbHepuIeWFJhtIvDzLVgLA8A4/cN7fiL7wq60NCDvPHliRZ"
			"itj6IES8vKKGrqeqD9k+tpkrXvLPXcRxNUFIAOsESVg64aNGksaw5MeOs7Ozsr90I79jSpXkoMx2"
			"OmROniJPYopcVeH52Tz++PUuOccqbTAR9bC4kwCSrKfsYeYL9T/b6WmRryXzjlqylB9eeJRb8m0V"
			"WyeXrK3ADS3fSkJJcsQ8J3gidQOeIQlLYSFrSXJloMIDqKZpJ0NLQBLFtrJkiGphkZwvamFtbH1g"
			"mwTKy1fK+yotO3VItf7kYdTVcrZAWDVDXGG4PO5F5X+x4d9gmVe45DOVMM7kvzxLzVORsUH1GpiE"
			"KVPHcRzHmSpERCRrlqXJ3jRZs5z5s7iikHDakdCSwWEr+5gyf/7r+m29ZJ2FbGj5SCB28pJmvDLa"
			"OcJzLsw8P7mP6YSK20uZ04UkSy18HTrBBxlWGDln5qQtrtpHLr7QOBoxWYifuiq3WFmHBYeEWXCC"
			"E3i68CcE5wkvJYTmj1zbkpkjZ4uLZ2HBlQEsj3PJJhZWJYT+Vq4kPH/CbUVMdatD/H5IMil6ZoAy"
			"NS4JU6Yj6XRbELZPpZGXhjFMdWiIO10m5ZK7O6IxVM2Y1SX5OlnyMXRoWe6Q5PO7ZOupjgBO/LIL"
			"5VTaNRVXBr98T4Z3xWLhqrj906yWv6fhHbIyvLDM71QqCffhiipakMwvM8mCkTOsbFJKG09kHVso"
			"8b6JAQDK17QkTH4g2VcTk8icXNnSAzbb0ixnqpB+fEpMFiZ3d+j0WF9cTYZjbMxA29k0SnqmZMJv"
			"MjaFSpWKpSovxJI8ppp4nNm/SwaXJNyiv6rM2UZ4e0589sjmxMX+As+W9BSy5jxrWLL/U72D+TnC"
			"ij9aNcPiPOQdQVYu5S+bfGZ//rhFaNUMcZteWCRuDctXS8kCoKi/uPUvzkYRezgceeSaV6wcoExN"
			"S8Ie3hWtVyYRkfnKEu8+nJUr03N6LHR0InNyRYMjr41MORrQ1SSyGWzJ91zkd6EzE7O448/iLxCx"
			"zlBhQoE1M2tzHCdyQ3Evyl9b5NRw/Mv2khP9esOhRobhMGtYjGfhFbFrC4ce94oSlERudCH9XYhz"
			"yUpC9SG4aXbB2b5dHlgguw3uBPbxitfFrC3yNSx7f9Okg/6aF/dY5IaiYnZm62FXGD3n0pfMfEqZ"
			"T2toRznBqctfW9oYouZJ/Fb5UUVGEldbYj6P86lJXqm/6biPc8yLcBa2GIxzXjdWfYIW6w+zhsgg"
			"mRlDR+CoT0q4YkaG5IT3J0A53qs7gCKIgxcjoy94uZapDg1nTyFdJ2WvbQw7JgWvB3hGZ94jx1m8"
			"1ES4hKIPwYvzC0IxFw2LDCBu/W5h2lspLYlzyarCG4rbetyuSL6GJVHluW9U/ntOFXLXqqUZZxb+"
			"Ndozhxf3PmZbMKfK3qacG0q72yM/F+wtDxN+FnJWmPLuvLZkhKw/iZ0nVSRJjorLb2bCHm3YrceF"
			"UdQhHWCppiVhL9/ahw9kIpPkB5L99iUREdmn/audQ03WvaFf+oUx2lPo5YPDtjHsEFHwFPce9cjN"
			"w4IfP4GIhIjRIEJwNic4/7yEmW3JqlZ8rGcrCZ9SF7ksW5jwrHiBjXnpbHFbF2aLBwpjdkXyNcRt"
			"elmQS7YoCIEZVq8rVBmIyF18VgMiQo1Z/+JLYOtGcP8vPo0LOC4T9StMaKclqUL+whm/bVZGtfTl"
			"rHh3ln1JJ8175jtBEJZtLvi5Sx7J8roamhpRS9nPAoUijIg5Pmlg3/ElNTB6aWbZwPzB/eavdsnO"
			"9A+YyffM4toSf+K8nRbIxISIV7HwdMkbym7XnY3v+w1A0zWtO9J8ZbkD7+XuzqxjkoheqkNrcO6f"
			"A6kfn0qH06PI4WApJTnYJ/9Blzy3yLPC3N9d0TOHl/UPx0L8POFy9nHscXxVYHHH6JXLLlknK9wZ"
			"1NAfxey7k6H65RwY49eQJdvKsnJh4f90a46blDCS5RU4/KkR4qYVzUmww8NWvgWF13yhuO0W8jkV"
			"ctdzgMyaloSR3ulbgxeO82Jg9Tv6QrmfhpmTK2q344aDzSz/1GX4THq/ZIPDNlamMksymAzfoHGr"
			"SqXig1FkGrckBvbIy8623+tl+B5dwgk9KFXOjD95jV35lZNwhrhJSQoT5tnL58/zvixsYlnylGyj"
			"Sfd/uLMuxycu1S+ulVtZOcPKpHP5yhde6ZK3b6GdbPWqy4G0DMrWuCSMSO8IgiAIgpeCmaokqaZb"
			"LvmXojBVSWCeppYh/fJ+3gmBQgqmC3E/AQWmmb3wj32SI2+B3wGZ06CE37vh+Yv93Z98u+zT2EWY"
			"L5P9Xo/S5w2pcp0l5clnSC4udY5MbjJsN219WC7y85Xkvc65XXY94Zkzp3S1t9zEfWDT1nD3c5H3"
			"taRfHq1fwIMGJmHlYL8qCvlYLjSGFfVRz9I4V9wKF9KIzKlqqkU8TuCbLPn34vJ3dkkqE7mthN+4"
			"88f+k6ivpsjYlmw0LoFI1RqRbQZh1eaS56ZLylcGGbmJJT9vCidEbTfzqpJPSpU75tl05vkrOMQl"
			"TO7TfmAjN4T8DKqxoUlY+DuvyJXPVrf83JpUX5xJvtLCQ3rjDtzhsVwrt+Lyz2+oMq0UKOmP67g7"
			"DMalXzm/QZdnEoESh4jo2bMztjDV1tMmNKkkb7oo6ZspVRbl7c8l5/gWtG8XN5pvDfOZlx4fIj+2"
			"4c9p2ghjg0kwKcnOybMDwx/bcMaPlAjWVdPOjuTGioPyep3dXNLhr8DV8nCAXvZ95tcHp4pYhVmW"
			"UmzzSY0qbvgpxPL8hq0PlVSKiBjiylc2dSeMdvls4d7h7NWVudDHkmDSjTMDqASSsNTSfXqXHmRz"
			"tsSkasDIvJXMS2XutSxEGS85bftKnJxfuhmWrbI7KU62IY/8fFkW3l5e1O+0lblLkrwq4VZ4+GmZ"
			"uTLzU5cAfBvaHVmB5YOBwty59hNckyZV302xeukvmVNlhBnCYy3Zn/l3dbb60KC9VyqeY6NQeOlq"
			"S3H1Ia4rs8C9V8YxJ3N46KOENYAkrERpv3cbgZ8fxI2zv9/zHq3R7kN9yAzHh2ogVwOeIQkrV3n3"
			"CMmsmiaW6htyGmE+KLvWMFioDzVCfQDYcEjCKpJw/Efc+X0rVfMDNHN41WhEeLE3ZClUho3wvPd4"
			"jo0KCa/M+nB2dsZhA5WP8zcXoFRIwkqX5IoV+F24gVAfgNb3+MBnwgfAGyRhlUhwnG2Qlb+qm/id"
			"Uan12jU8t7I0wzoeH5bA8QHAhySsCgIlPeQ06yww3jQlvPmAbJ6+dMvbe/m/dJvyzmZTdjqy3nsP"
			"oNGQhFUt/L2L34WbKL7xA/VhE62qDwCwlpCEVWiNOh2W9EAhh0iKg/pQ1JuF+lCApfWhmh2I+gBQ"
			"MSRhFREC/8XKc6JQkqE5OY+AnJ/H1LzwyvwuSjtUi+e9x3NsVGB45eTl3mm57pqLXHExOH9zAUqF"
			"JKwea9AYVhknwY3hGmfh1aA+JLfe9WHhdaFerLSW9QE2B+4dWbnZjdwchwShwY3zQtRdopv7cqr/"
			"ulvcS05MeYGbSD9D/m0VXhm8Fa5deuJ/oFzu8cGf1DjrdnwAKAdawqoT/rEbbv/g/EQhhAcbIWWa"
			"UGzFS3LlsFQ4/1xwHh5AqRqWhMma5TAsTSZZsyxNZqbPn8UV1c2pqfG8pJ+hTuhBwwgV/fX2ewsl"
			"zuyPBK/ZwH9ayN/KtbEzhMPLtjkn5mlRu85rLir1zarVwvGh7nDyavzxAaBMDUvCTFUSXNunNhlD"
			"1VycPjTEnS6TcsndHTE8X1384+n8eOQ0+CDLvhwnVAgroT4AK7I+FL7+ypIh1AeA1cbjcd0hZKFM"
			"nalCROS3hMma5UwVCraMLTxz1d767fh/jvdX+MrTTsq5OaeclXubKHoX8Qb1Id0mUB9yrzztpPxb"
			"LK9KlF0fav++qNh4PL6NZ9v2kqlfff2NAyk1rCXMI2uH0umxzpQo03N6LHR0InNyRYMjxSs+GtDV"
			"hJNmMN+832P2q7C5Xyo8deM0FeoDsML1odFQJQCWaOTZkcrRgK6256mVOHgxMvqCV2CqQ8PZU0jX"
			"SdlrG8OOSaFfM+xT/yo1fmGVJYJw5n7jFrVmvzw8D9X6SlGSpEQQyK8PxayZqRiRtcUt5OG1Jyzx"
			"yzmJp+SSM2LOlCx3W6E9XO0rzVwyD7vsWgdQPP67I5XprNnO64Ccd0USeR2OSrDX0Z1j1kG5yP+A"
			"cYLtdMgfW6ndDbXsuuTdDby9swsShldsJ1Tcmx4u53nvsbFx2B1Z6q7LXx8Ce6/y7siVcu49dEcW"
			"C92RFWtAd6Te8cbiCx2diEh+INlvXwbneakOrcG5n4bpx6fS4fRoh4J9lpyqphMKV+hpijXolIQC"
			"FVsf4sbm4/gAUIsGJGGLHt4VrVehYV56p8+kYebkitptDoeDxShw8EfFJ0BBGVAfgLUeg8MAIAL/"
			"3ZGF47Z5uah29chuhbr6GvLjsPupGqgPkVAf8q4H9SENbr8vSoLuyIo1sCVs3W3mFwzEQX0AFuoD"
			"wDpBEsaRogZ/hHugMOCjiVAfgIX6ALB+kITxZX+/V3cIy3DeMr9+4VU5GIjnvcdzbFRheNnqA/Ye"
			"ALeQhHEKnQ7AQn0AVlGDwwCgXkjCuFNIpwPb44C+hkYr8AoFqA9roPArmKA+ANSokVfMX2MLl00v"
			"6nuXCvvVe/bsWTErKgPnl7fOHF5V9YHnN5fn2GqreInrQ+zeK/r4kA3Xby5AqdASBg2A6yQBa8Pr"
			"w4a//DDsEGgutIRxCocVYKE+AAv1AWA9oCWML5yfKITw8kB4mfEcGyG8fDgPD6BUSMIAAAAAaoAk"
			"DAAAAKAGSML4sq7n91UD4eXBc3g8x0YILx/OwwMoFZIwAAAAgBogCQMAAACoAZIwvnB+ohDCywPh"
			"ZcZzbITw8uE8PIBSIQkDAAAAqEEjkjBl6kyVYJGsWZYmBx+5T5z5s7giAAAAgLrxn4QpU2fUXiiT"
			"tfOBGDWzqQ4NcafLpFxyd0c0hqpZYoRF4vxEIYSXB8LLjOfYCOHlw3l4AKXiPAmTNWvvQugbC4Xn"
			"O5Zhh2d1pgrpx6fEZGFyd4dOj/UqYgUAAABIjvMkzFSlzkIGJWvnO1ePj98GS5XpOT0WOjqRObmi"
			"wZHXe6kcDehq0pRmMAAAANgcTbuBt5uCSSZpTKE4eDEy+oKXa5nq0HD2FNJ1UvbaxrBjUugEHPap"
			"3xjuF6IEJWWUcF7rer2e+y8n8aBkQ0o4/1wQQKnG43HdIQQoU8czH4s/H5gva5bDsDTZHZivsKPz"
			"Zwt4HZQh7GeeNzzHRggvH4SXGc+xEcLLB+FxZTwe38azbXvJ1K++/saBlLjrjtQ7gmexH5KIyFQl"
			"d+L2qW2fbkuzAfcv1aE1OPfTMP34VDqcHmE4GAAAAPCKuyQsK73TZ9Iwc3JF7XYDh4Nx3vqN8PJA"
			"eJnxHBshvHw4Dw+gVI0YE6Z3hMUGLVOVpIVHgblMVRLUisIDAAAASG1tWsIAAAAAmgRJGF84HwSK"
			"8PJAeJnxHBshvHw4Dw+gVEjCAAAAAGqAJAwAAACgBkjC+ML5iUIILw+ElxnPsRHCy4fz8ABKhSQM"
			"AAAAoAZIwgAAAABqgCSML5yfKITw8kB4mfEcGyG8fDgPD6BUSMIAAAAAaoAkDAAAAKAGSML4wvmJ"
			"QggvD4SXGc+xEcLLh/PwAEqFJAwAAACgBkjCAAAAAGqAJIwvnJ8ohPDyQHiZ8RwbIbx8OA8PoFRI"
			"wgAAAABqgCQMAAAAoAZIwvjC+YlCCC8PhJcZz7ERwsuH8/AASsV5EiZrluOZKky5MvWfy5plaTIz"
			"//xZXBEAAABA3bhOwmTtfGD1BUEQhO1TaTRLu5SpM2pHL2GqQ0Pc6TIpl9zdEY2hapYfLQAAAEBy"
			"XCdhpioJHd19OLmypQcykaxZexdC3wjNLGuWM1VIPz4lJguTuzt0eqxXF3NOnJ8ohPDyQHiZ8Rwb"
			"Ibx8OA8PoFRcJ2EM5WhgDVWTyFSlTkROpUzP6bHQ0YnMyRUNjhR/MbqaoBkMAAAAePNe3QEkoUyd"
			"w7fbUmx7ljh4MTL6gpdrmerQcPYU0nVS9trGsGNS6McW+9QfFuoX1lsSDpKrCHu9HlfxUHBgr1vI"
			"STwoKbDEx0k8+FxsVAlAWcbjcd0hBCjT4Ej86HH1iwPzFXZ0/myy10EZEj6sAwAAhG3a98V4PL6N"
			"Z9v2kqlfff2NAylx1xKmd4R5i5esWS/uDoX4NrCZl+rQcs61ieSOwNePTw/Pp0dEp4+bMxwMAAAA"
			"NgnXY8KUo4FI7dEsYVx6oQm907cG57M5zMkVtdsYDgYAAAC84joJ0zsCS5pfaELvzE6bJFOV3Al6"
			"h5nDVKXAAk3BedM3wssD4WXGc2yE8PLhPDyAUnGdhAEAAACsKyRhAAAAADVAEsaX/7+9+9dt24ri"
			"OH4u0AfIkCcIRA2Bp440OhUxILODURRaPChZpGaSFg0FPGbLQk+utSQauDhtYaC1hCLoVJhPYBio"
			"KPgJPOQN2IH6Q/1zWsviObG+H3jQP8A/3CtdHV3yXhpfEU28dRDv3ixnE+Ktx3g8YKPMrY4swPt3"
			"7+WddggAgH2vtAPgUWMmDAAAQME2FmEvX70UJzb/LGcjHvHIRrxtiwds1DYWYQAAAOoowgAAABRQ"
			"hNlifKEQ8dZBvHuznE2Itx7j8YCNoggDAABQQBEGAACggCLMFuOXUSPeOoh3b5azCfHWYzwesFEU"
			"YQAAAAoowgAAABRQhAEAAChwURQdHh5qxygUpyAAAP6LbdtBI4qiIAhWPXt7e/v06dNVz3786+8f"
			"vv9uM7keryiKtCNgyniBSLx1EO/eLGcT4q3HeLxtE0XRp9WGw+Edz3749fcU/xOHIwEAABRQhAEA"
			"ACigCAMAAFCwjSfmAwCARZyYXzBmwgAAABRQhBWq3hstiOjV5x+aPqLHD5P5fJbiGW69em8aYdqK"
			"ZpoxH2/ajNM4mvFmsll6By7rx1Gg3H1z8cQPkyT0rcXL3U/TNB0ltPL5BfSwRUVxpqP35Fa9lw1H"
			"fpioD0TTwXuSxlK8cZZcUCPxsm+SUYBcFiPNOBNv7jt6/AKteLPZLL0Dl/XjQmBz8UbFzriD7cWb"
			"ecDI5xcz2KKiYMyEFaiz7/Y7M4/4z73hxVksEp9dDL3n2XjU683+PJz+hpz78nxYfjWQi7NYRCRu"
			"eW6/YyzeOItIfHYhQdW3Ec8Pk4Nz1+iP74/bTqaxNHPOxxO/GpSS6zj/Eq14C9lmDAdXmu/AJf24"
			"pDFNxRMRP+wGSX9oNZ6ISP2omTT2O6rxADsowormh0manko2DO2Ux1+I8XVSKu+IiEhFzp1zrtGv"
			"HNRlNGg555xrJM2jzf1e3CmXEqkm+RHQUrz4OikF1WyapBqUzMSLW95cZT1RP2omb1uxas6FeDvl"
			"0tBrm+jlhWxxy6tJN03TtCs1rxUb6eJxPy5tTEPxxA+7wUXtzcBoPBERP2x7x286duIByijCiha3"
			"POfc+cHq+ffh4EpE5GqQ/Tq8Ggwrp2mapj1ZmEl7YJVAatmAJ6fm4nX2G0nzMk3TtFtOhitfptd6"
			"s+q9tD3YXf0f9ZrR1WpZL+8O2rZ6ud5Lu9k7sCZdIx+Qz/WjnXhZCTYpd6zFy+4fNcfT7RbiAfoo"
			"wnSMxpjxUJNNzWfDz7ysats9zgajjZ460R//YrUZr7PvnHPOeedSSq5ja/Gm/DBJ24Ndb7Y1zeSM"
			"48m3oKlezh9xbr3tVw7qytnm+nGRpXh+NSiVmpdpetkslZqXSajds0tar35Q6U9nxcy88QBFFGEF"
			"yi2rGs3ET46xLZ6pMzY6aTVuec41+pI/u+Jhdc5HRwBsxpu2nh+2K/3zjtiKl/+Xl+W3LvfdYyrn"
			"7No+U72cO+Is9YPKcHClmW2xH+9IbCBeVq5kFcvweNdrqfbs0tabq7XMvPEATayOLNJkd4CVa8hn"
			"FojNrePe+Lmpk5Ngbcb7/BYVavFmFr7mLV+KX3TO5VtU2OjlFdtnrNqioqhsy/txIbC9eHduUaEb"
			"b7blFOPhDqyOLBg75gMAABF2zC8chyMBAAAUUIQBAAAo+Eo7AAAAeAx++e0P7QhfGIowAACwrhff"
			"fqMd4cvD4UgAAAAFFGEAAAAKKMIAAAAUUIQBW2CyD+9UEtbz23oCAIrGifnAFohbnmuJiB8mXalN"
			"ribT4pLIAKCHmTBgW2XXhvHDJOn1ksnkWHZj9pIxXDkZADaAIgzYeiVvUHNu91iabcluBFXfD5P2"
			"YNc551xDTinDAODBcTgS2HrDi7NYxM/dEJGdcqlUuUybo5cMfJFYMSMAC25vb7UjPCoUYQBW6Dfc"
			"PmeNARi54+rduB8ORwJY5mowrLRDX7KllRyOBIAHx0wYgGXiltcop9nxyH7DMSMGAA/NRVF0eHio"
			"HQMAACiLoigIAu0UW4TDkQAAAAoowgAAABRQhAEAACivlIg1AAAAUElEQVSgCAMAAFBAEQYAAB7E"
			"zcnek5G9kxvtNPZRhAEAgLXdnOw9+fqf9qeRn+XPj9qRzGOfMAAAsKabkx9/ev7hU/hi/MCz16+f"
			"aQb6IvwLwDtYTFtJTGcAAAAASUVORK5CYII="
		)
	)
	(label "itrnoise"
		(at 161.29 92.71 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "0324b3e5-288e-486c-806a-b67cee6f4f52")
	)
	(label "isin"
		(at 143.51 92.71 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "24155029-2c2d-43d2-9737-6c0859166459")
	)
	(label "vpulse"
		(at 81.28 15.24 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "460e390e-82b3-4ba2-897e-cac216dbe91b")
	)
	(label "idc"
		(at 45.72 92.71 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "49946b13-1fa6-4fbb-83dd-ecba0fc26a62")
	)
	(label "vdc"
		(at 44.45 15.24 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "579a3da4-93e0-43c1-8489-81871a2db46c")
	)
	(label "iexp"
		(at 63.5 92.71 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "65686e58-96ca-46a1-badd-094ce6209d70")
	)
	(label "isffm"
		(at 123.19 92.71 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "68ce866d-d495-4889-8188-65f9cf6ed54f")
	)
	(label "vtrrandom"
		(at 182.88 15.24 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "778e335b-7956-4b4a-85a4-d67b8a0c539e")
	)
	(label "vpwl"
		(at 102.87 15.24 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "7a21add5-3f4a-4f0d-a8dd-e1cfaf893ee1")
	)
	(label "iam"
		(at 27.94 92.71 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "87143484-ee40-4d77-9691-e9e76f4cec2d")
	)
	(label "vsin"
		(at 142.24 15.24 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "9f2a6bb1-9532-426d-b7ea-e1d8c6152ec9")
	)
	(label "vsffm"
		(at 121.92 15.24 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "a260599b-3b70-4c9a-bbc5-dcae26421b16")
	)
	(label "ipulse"
		(at 82.55 92.71 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "c5ae9033-2763-4d17-a3e3-c7429dd645f0")
	)
	(label "vtrnoise"
		(at 160.02 15.24 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "c6ec5f80-08d3-4bbf-b130-33fde8f5d6ca")
	)
	(label "itrrandom"
		(at 184.15 92.71 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "dbbf3396-87f9-407f-96c4-4802df854bfe")
	)
	(label "vam"
		(at 26.67 15.24 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "e43aacf7-55fb-47de-b4be-b037e0f9c12f")
	)
	(label "vexp"
		(at 62.23 15.24 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "e52d3f5b-d6bf-4efd-95e8-bd4f294c6fa9")
	)
	(label "ipwl"
		(at 104.14 92.71 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "e91d40c5-bdad-4fb9-9c34-de574cf58ef4")
	)
	(symbol
		(lib_id "power:GND")
		(at 25.4 81.28 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda09bd")
		(property "Reference" "#PWR01"
			(at 25.4 87.63 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 25.527 85.6742 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 25.4 81.28 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 25.4 81.28 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 25.4 81.28 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "4a8220fd-4642-4129-afa8-9081b6df95cf")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "#PWR01")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 34.29 20.32 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda3c13")
		(property "Reference" "R1"
			(at 36.068 19.1516 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 36.068 21.463 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 32.512 20.32 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 34.29 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 34.29 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "87bfe486-89f4-478e-8385-38181e7f52df")
		)
		(pin "2"
			(uuid "b419ba03-1bb0-41a6-9bb5-c1977a2ecc4b")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 52.07 20.32 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda54f9")
		(property "Reference" "R2"
			(at 53.848 19.1516 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 53.848 21.463 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 50.292 20.32 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 52.07 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 52.07 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "9311a22f-0af9-495a-9c75-384d269dad50")
		)
		(pin "2"
			(uuid "a35b6e06-e4da-4ce7-8991-40851c7aabe7")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 149.86 20.32 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda56df")
		(property "Reference" "R7"
			(at 151.638 19.1516 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 151.638 21.463 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 148.082 20.32 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 149.86 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 149.86 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "7e1462b4-543a-489e-82ac-bbb6522092f7")
		)
		(pin "2"
			(uuid "5eb3fbc0-ba88-492b-8c3c-f2b8f7948c09")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R7")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 71.12 20.32 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda5e69")
		(property "Reference" "R3"
			(at 72.898 19.1516 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 72.898 21.463 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 69.342 20.32 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 71.12 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 71.12 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "e825e75d-fc85-4966-a329-2bc3c37f2437")
		)
		(pin "2"
			(uuid "730b649a-0653-42ea-849d-b136b8237c1b")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 172.72 20.32 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda631a")
		(property "Reference" "R8"
			(at 174.498 19.1516 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 174.498 21.463 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 170.942 20.32 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 172.72 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 172.72 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "6920c3b3-2f62-4beb-ad02-20c0782cccb0")
		)
		(pin "2"
			(uuid "8ca5b0b7-0057-4a11-9d37-e1b085782aef")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R8")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 92.71 20.32 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda6c25")
		(property "Reference" "R4"
			(at 94.488 19.1516 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 94.488 21.463 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 90.932 20.32 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 92.71 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 92.71 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "00c6acd5-7249-4787-a9a7-a583f1330606")
		)
		(pin "2"
			(uuid "fc5cd870-04db-4d06-b91a-a7649bdebc12")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 26.67 158.75 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda717d")
		(property "Reference" "#PWR02"
			(at 26.67 165.1 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 26.797 163.1442 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 26.67 158.75 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 26.67 158.75 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 26.67 158.75 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "17dd5404-2092-479c-9f11-6e3d26800387")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "#PWR02")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 151.13 97.79 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda719b")
		(property "Reference" "R16"
			(at 152.908 96.6216 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 152.908 98.933 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 149.352 97.79 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 151.13 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 151.13 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "444b9dea-3556-4080-a4f3-31c605424b45")
		)
		(pin "2"
			(uuid "81c336fc-9612-4ab7-92ba-6ce5fbf771f4")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R16")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 35.56 97.79 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda71a5")
		(property "Reference" "R10"
			(at 37.338 96.6216 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 37.338 98.933 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 33.782 97.79 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 35.56 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 35.56 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "97ffd50d-e06c-404f-86f6-b3509fcf9e05")
		)
		(pin "2"
			(uuid "a7932a17-26ab-45f9-8765-46baca7283d5")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R10")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 53.34 97.79 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda71ac")
		(property "Reference" "R11"
			(at 55.118 96.6216 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 55.118 98.933 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 51.562 97.79 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 53.34 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 53.34 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "6c782352-d6f7-46fd-88a0-cdde8c51567d")
		)
		(pin "2"
			(uuid "369dd70a-dbac-490c-a0a6-2673a4f632c8")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R11")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 72.39 97.79 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda71b2")
		(property "Reference" "R12"
			(at 74.168 96.6216 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 74.168 98.933 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 70.612 97.79 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 72.39 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 72.39 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "5ebad891-90e8-4f8e-8055-269bc66949f1")
		)
		(pin "2"
			(uuid "33f440c6-d433-4fb1-94db-9b43fac00926")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R12")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 173.99 97.79 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda71b8")
		(property "Reference" "R17"
			(at 175.768 96.6216 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 175.768 98.933 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 172.212 97.79 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 173.99 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 173.99 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "1f171b98-40c6-42ce-b44d-c4f2f1185555")
		)
		(pin "2"
			(uuid "33b0b732-252d-44ef-9558-f715b8ff97fc")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R17")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 93.98 97.79 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda71be")
		(property "Reference" "R13"
			(at 95.758 96.6216 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 95.758 98.933 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 92.202 97.79 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 93.98 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 93.98 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "2f9b7581-bbf4-46ce-9a1a-3828324d3b07")
		)
		(pin "2"
			(uuid "5b519160-67b2-44f5-ab68-7be4079ec394")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R13")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 113.03 97.79 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda71c4")
		(property "Reference" "R14"
			(at 114.808 96.6216 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 114.808 98.933 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 111.252 97.79 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 113.03 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 113.03 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "060c3ef6-f63e-4495-9d01-0ffa858677a9")
		)
		(pin "2"
			(uuid "ca3ba06b-0aea-4322-a56b-82798cfcaf07")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R14")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 133.35 97.79 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda71ca")
		(property "Reference" "R15"
			(at 135.128 96.6216 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 135.128 98.933 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 131.572 97.79 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 133.35 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 133.35 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "57030162-21f8-4ce2-8119-d95bb410cb89")
		)
		(pin "2"
			(uuid "4a39ec92-4ea9-4f12-8a44-e7098b136cfd")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R15")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 199.39 97.79 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda7207")
		(property "Reference" "R18"
			(at 201.168 96.6216 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 201.168 98.933 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 197.612 97.79 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 199.39 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 199.39 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "a8be6a01-c496-4b1a-8935-ddcc9ebfdf87")
		)
		(pin "2"
			(uuid "9f0eb314-a0b8-4afe-beee-6ecb16246fe8")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R18")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 111.76 20.32 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda74fc")
		(property "Reference" "R5"
			(at 113.538 19.1516 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 113.538 21.463 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 109.982 20.32 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 111.76 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 111.76 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "12b11d37-64bf-4e21-a32b-d795095777e2")
		)
		(pin "2"
			(uuid "56ed50bc-9c83-4eb2-ab32-8e23bc494f24")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 132.08 20.32 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bda7e91")
		(property "Reference" "R6"
			(at 133.858 19.1516 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 133.858 21.463 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 130.302 20.32 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 132.08 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 132.08 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "6aa7b206-5957-4af8-bdf0-cd56b6fbbe15")
		)
		(pin "2"
			(uuid "0c43c108-34e3-40f9-929a-259d4355be0f")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:R-Device")
		(at 198.12 20.32 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005bdc2ddf")
		(property "Reference" "R9"
			(at 199.898 19.1516 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10k"
			(at 199.898 21.463 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 196.342 20.32 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 198.12 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 198.12 20.32 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "9005e327-f1a3-4276-9c80-c4a07972ccf9")
		)
		(pin "2"
			(uuid "020972d3-a8e6-466e-a28e-4051ff1a19c7")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "R9")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:ITRRANDOM-Simulation_SPICE")
		(at 182.88 151.13 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c5263e1")
		(property "Reference" "I9"
			(at 186.182 148.8186 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "ITRRANDOM"
			(at 186.182 151.13 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 182.88 151.13 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 182.88 151.13 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 182.88 151.13 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "I"
			(at 182.88 151.13 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Type" "RANDUNIFORM"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Params" "ts=100ns td=10ns range=1"
			(at 186.182 153.4414 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "e4d6581a-ba1e-49b8-b0ad-a879c84f77d4")
		)
		(pin "2"
			(uuid "789bcb71-994b-4a99-aec3-ce70d0152222")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "I9")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:ITRNOISE-Simulation_SPICE")
		(at 160.02 144.78 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c526b8f")
		(property "Reference" "I8"
			(at 163.322 142.4686 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "ITRNOISE"
			(at 163.322 144.78 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 160.02 144.78 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 160.02 144.78 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 160.02 144.78 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "I"
			(at 160.02 144.78 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Type" "WHITENOISE"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Params" "rms=100m dt=500p ac=0 ph=0"
			(at 163.322 147.0914 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "bbde6d81-a73f-4f07-852e-109cea86b2ff")
		)
		(pin "2"
			(uuid "64028ca2-3e0c-4f48-8d42-1b34a8f4d6bd")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "I8")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:ISIN-Simulation_SPICE")
		(at 142.24 138.43 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c527479")
		(property "Reference" "I7"
			(at 145.542 136.1186 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "ISIN"
			(at 145.542 138.43 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 142.24 138.43 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 142.24 138.43 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 142.24 138.43 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "I"
			(at 142.24 138.43 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Type" "SIN"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Params" "dc=0 ampl=1 f=100M td=1n theta=10G"
			(at 145.542 140.7414 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "54e10e6b-294d-4667-92b7-30eec6ca3d72")
		)
		(pin "2"
			(uuid "13499c7b-0dec-4ad1-9d08-2df89822dd5c")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "I7")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:ISFFM-Simulation_SPICE")
		(at 121.92 132.08 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c527dc7")
		(property "Reference" "I6"
			(at 125.222 129.7686 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "ISFFM"
			(at 125.222 132.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 121.92 132.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 121.92 132.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 121.92 132.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "SPICE"
			(at 121.92 132.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Params" "type=\"I\" model=\"sffm(-5 1 100meg 5 10meg)\" lib=\"\""
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=1 2=2"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "654c4b81-b27d-4c7b-9c43-6c9ca40a1727")
		)
		(pin "2"
			(uuid "368383a1-dcdd-4eb4-abfb-90d268924eea")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "I6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:IPWL-Simulation_SPICE")
		(at 102.87 125.73 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c528b91")
		(property "Reference" "I5"
			(at 106.172 123.4186 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "IPWL"
			(at 106.172 125.73 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 102.87 125.73 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 102.87 125.73 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 102.87 125.73 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "SPICE"
			(at 102.87 125.73 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Params" "type=\"I\" model=\"pwl(0 -7 50n -7 51n -3 97n -4 171n -6.5 200n -6.5)\" lib=\"\""
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=1 2=2"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "beddc87e-e1b9-4ad3-bf36-4b93dafe600b")
		)
		(pin "2"
			(uuid "1df1dca0-2cdf-449d-8474-e977ba6c2d3e")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "I5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:IPULSE-Simulation_SPICE")
		(at 81.28 119.38 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c529462")
		(property "Reference" "I4"
			(at 84.582 117.0686 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "IPULSE"
			(at 84.582 119.38 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 81.28 119.38 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 81.28 119.38 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 81.28 119.38 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "I"
			(at 81.28 119.38 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Type" "PULSE"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Params" "y1=-1 y2=1 td=2n tr=30n tf=2n tw=50n per=100n"
			(at 84.582 121.6914 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "7ed9130e-2b33-447e-b612-755bc409baa2")
		)
		(pin "2"
			(uuid "40ff61b8-8e26-4f05-9d4f-a0f29d685fa4")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "I4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:IEXP-Simulation_SPICE")
		(at 62.23 113.03 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c52a002")
		(property "Reference" "I3"
			(at 65.532 110.7186 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "IEXP"
			(at 65.532 113.03 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 62.23 113.03 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 62.23 113.03 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 62.23 113.03 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "I"
			(at 62.23 113.03 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Type" "EXP"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Params" "y1=-4 y2=-1 td1=2n tau1=30n td2=60n tau2=40n"
			(at 65.532 115.3414 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "017174bb-e1df-493e-b1f4-8918deddfffc")
		)
		(pin "2"
			(uuid "77261519-f995-4c4b-85f1-4008bdb22513")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "I3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:IDC-Simulation_SPICE")
		(at 44.45 106.68 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c52abaa")
		(property "Reference" "I2"
			(at 47.752 104.3686 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "2.345"
			(at 47.752 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 44.45 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 44.45 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 44.45 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "I"
			(at 44.45 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Type" "DC"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "1afb3527-d69a-4e1d-8c32-7352a57061c6")
		)
		(pin "2"
			(uuid "c98acd97-8ace-4f47-9842-c14243a1a5f5")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "I2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:IAM-Simulation_SPICE")
		(at 26.67 100.33 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c52b633")
		(property "Reference" "I1"
			(at 29.972 98.0186 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "IAM"
			(at 29.972 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 26.67 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 26.67 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 26.67 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "SPICE"
			(at 26.67 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Params" "type=\"I\" model=\"am(0.5 1 10meg 50meg 20n)\" lib=\"\""
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=1 2=2"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "24bc80f1-ea2b-42bf-a182-40ae0f059f55")
		)
		(pin "2"
			(uuid "4bb0a1f9-59d6-4284-963f-6c8f007a9c1b")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "I1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:VTRRANDOM-Simulation_SPICE")
		(at 181.61 73.66 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c52c2f7")
		(property "Reference" "V9"
			(at 184.912 71.3486 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "VTRRANDOM"
			(at 184.912 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 181.61 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 181.61 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 181.61 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "V"
			(at 181.61 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Type" "RANDUNIFORM"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Params" "ts=100ns td=10ns range=2 offset=0"
			(at 184.912 75.9714 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "54c01134-ed0e-4d05-8b29-fd0fee4b3a54")
		)
		(pin "2"
			(uuid "a36dff9b-03db-4744-bf5f-2ff2a759fbc4")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "V9")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:VTRNOISE-Simulation_SPICE")
		(at 158.75 67.31 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c52cc31")
		(property "Reference" "V8"
			(at 162.052 64.9986 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "VTRNOISE"
			(at 162.052 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 158.75 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 158.75 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 158.75 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "V"
			(at 158.75 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Type" "WHITENOISE"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Params" "rms=100m dt=500p ac=0 ph=0"
			(at 162.052 69.6214 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b849f051-5bdb-467f-baec-73487da7e690")
		)
		(pin "2"
			(uuid "54fb837b-32cc-4776-a9a5-75c81598ae8b")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "V8")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:VSIN-Simulation_SPICE")
		(at 140.97 60.96 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c52d6bf")
		(property "Reference" "V7"
			(at 144.272 58.6486 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "VSIN"
			(at 144.272 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 140.97 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 140.97 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 140.97 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "V"
			(at 140.97 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Type" "SIN"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Params" "dc=0 ampl=1 f=100M td=1n theta=10G"
			(at 144.272 63.2714 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "da9bd840-85bd-4460-9436-00e156d2fe08")
		)
		(pin "2"
			(uuid "9fe84b6c-bf14-4c8a-9849-0af2e9fcc408")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "V7")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:VSFFM-Simulation_SPICE")
		(at 120.65 54.61 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c52e253")
		(property "Reference" "V6"
			(at 123.952 52.2986 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "VSFFM"
			(at 123.952 54.61 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 120.65 54.61 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 120.65 54.61 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 120.65 54.61 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "SPICE"
			(at 120.65 54.61 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Params" "type=\"V\" model=\"sffm(-5 1 100meg 5 10meg)\" lib=\"\""
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=1 2=2"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "d0440d48-479e-49ad-b501-8ba8ea011253")
		)
		(pin "2"
			(uuid "ff0155b8-6687-4da7-acff-9c62cb9d867e")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "V6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:VPWL-Simulation_SPICE")
		(at 101.6 48.26 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c52ef6f")
		(property "Reference" "V5"
			(at 104.902 45.9486 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "VPWL"
			(at 104.902 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 101.6 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 101.6 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 101.6 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "SPICE"
			(at 101.6 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Params" "type=\"V\" model=\"pwl(0 -7 50n -7 51n -3 97n -4 171n -6.5 200n -6.5)\" lib=\"\""
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=1 2=2"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "73fa552d-091b-4c27-8262-df550a759cb8")
		)
		(pin "2"
			(uuid "17acb785-e2d1-4c03-929f-4c3bca98c497")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "V5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:VPULSE-Simulation_SPICE")
		(at 80.01 41.91 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c52f92f")
		(property "Reference" "V4"
			(at 83.312 39.5986 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "VPULSE"
			(at 83.312 41.91 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 80.01 41.91 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 80.01 41.91 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 80.01 41.91 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "V"
			(at 80.01 41.91 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Type" "PULSE"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Params" "y1=-1 y2=1 td=2n tr=30n tf=2n tw=50n per=100n"
			(at 83.312 44.2214 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "1cd1c486-1ecb-4ce6-a5bc-ab485954ce64")
		)
		(pin "2"
			(uuid "f6079f67-93cb-4076-9cdb-70b7aeeb7d87")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "V4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:VEXP-Simulation_SPICE")
		(at 60.96 35.56 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c53039f")
		(property "Reference" "V3"
			(at 64.262 33.2486 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "VEXP"
			(at 64.262 35.56 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 60.96 35.56 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 60.96 35.56 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 60.96 35.56 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "V"
			(at 60.96 35.56 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Type" "EXP"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Params" "y1=-4 y2=-1 td1=2n tau1=30n td2=60n tau2=40n"
			(at 64.262 37.8714 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "d22c1d55-25f8-4f9d-ae8c-f479b0bfd150")
		)
		(pin "2"
			(uuid "89905da9-6216-4575-9d27-93bc7100e5c7")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "V3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:VDC-Simulation_SPICE")
		(at 43.18 29.21 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c530c73")
		(property "Reference" "V2"
			(at 46.482 26.8986 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "2.345"
			(at 46.482 29.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 43.18 29.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 43.18 29.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 43.18 29.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "V"
			(at 43.18 29.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Type" "DC"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "5cee3d30-d590-497f-8a34-4225f7eacc7e")
		)
		(pin "2"
			(uuid "62091122-84c0-4892-992b-c9b3a04d6103")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "V2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "v_i_sources:VAM-Simulation_SPICE")
		(at 25.4 22.86 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005c531459")
		(property "Reference" "V1"
			(at 28.702 20.5486 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "VAM"
			(at 28.702 22.86 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 25.4 22.86 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 25.4 22.86 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 25.4 22.86 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Sim.Device" "SPICE"
			(at 25.4 22.86 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Sim.Params" "type=\"V\" model=\"am(0.5 1 10meg 50meg 20n)\" lib=\"\""
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=1 2=2"
			(at 0 0 0)
			(effects
				(font
					(size 0.001 0.001)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "f31cd362-ec8d-4dd0-98a6-c812e1606c2b")
		)
		(pin "2"
			(uuid "dcfc3488-7bca-4f68-a563-64db473e6899")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "V1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:PWR_FLAG")
		(at 33.02 158.75 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "a7c49e6c-6736-4a1d-a5d4-9863e74c2de5")
		(property "Reference" "#FLG0101"
			(at 33.02 160.655 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "PWR_FLAG"
			(at 33.02 163.0744 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 33.02 158.75 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 33.02 158.75 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 33.02 158.75 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "06982235-faa6-4e5e-8486-b8bf1f74d827")
		)
		(instances
			(project "v_i_sources"
				(path "/290fe62e-4cea-473b-badf-2a76c0d280cd"
					(reference "#FLG0101")
					(unit 1)
				)
			)
		)
	)
	(sheet_instances
		(path "/"
			(page "1")
		)
	)
)
