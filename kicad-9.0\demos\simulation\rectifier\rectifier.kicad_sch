(kicad_sch (version 20230121) (generator eeschema)

  (uuid faea1833-708f-41af-98f4-4de67f419dbb)

  (paper "A4")

  (lib_symbols
    (symbol "R_1" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "R" (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R_1" (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at -1.778 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "ki_fp_filters" "R_* Resistor_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_1_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "1" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "2" (effects (font (size 0.508 0.508))))
          (number "2" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "power:PWR_FLAG" (power) (pin_numbers hide) (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "#FLG" (at 0 1.905 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "PWR_FLAG" (at 0 3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Special symbol for telling ERC where power comes from" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "PWR_FLAG_0_0"
        (pin power_out line (at 0 0 90) (length 0)
          (name "pwr" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
      (symbol "PWR_FLAG_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 1.27)
            (xy -1.016 1.905)
            (xy 0 2.54)
            (xy 1.016 1.905)
            (xy 0 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
    )
    (symbol "rectifier_schlib:C" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "C" (at 0.635 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "C" (at 0.635 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0.9652 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "ki_fp_filters" "C? C_????_* C_???? SMD*_c Capacitor* Capacitors_ThroughHole:C_Radial_D10_L13_P5 Capacitors_SMD:C_0805 Capacitors_SMD:C_1206" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "C_0_1"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy 2.032 -0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.032 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
      )
      (symbol "C_1_1"
        (pin passive line (at 0 3.81 270) (length 2.794)
          (name "" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
        (pin passive line (at 0 -3.81 90) (length 2.794)
          (name "2" (effects (font (size 0.508 0.508))))
          (number "2" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "rectifier_schlib:D" (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "D" (at 0 2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "D" (at 0 -2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "ki_fp_filters" "Diode_* D-Pak_TO252AA *SingleDiode *_Diode_* *SingleDiode*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "D_0_1"
        (polyline
          (pts
            (xy -1.27 1.27)
            (xy -1.27 -1.27)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 1.27)
            (xy -1.27 0)
            (xy 1.27 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type outline))
        )
      )
      (symbol "D_1_1"
        (pin passive line (at -3.81 0 0) (length 2.54)
          (name "K" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
        (pin passive line (at 3.81 0 180) (length 2.54)
          (name "A" (effects (font (size 0.508 0.508))))
          (number "2" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "rectifier_schlib:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (at 0 -3.1242 0)
        (effects (font (size 0.762 0.762)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 0.762 0.762))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "rectifier_schlib:R" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "R" (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at -1.778 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "ki_fp_filters" "R_* Resistor_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "1" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "2" (effects (font (size 0.508 0.508))))
          (number "2" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "rectifier_schlib:VSOURCE" (pin_names (offset 1.016)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (at 5.08 5.08 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "VSOURCE" (at 6.35 2.54 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Fieldname" "Value" (at 0 0 0)
        (effects (font (size 1.524 1.524)) hide)
      )
      (property "Spice_Primitive" "V" (at 0 0 0)
        (effects (font (size 1.524 1.524)) hide)
      )
      (property "Spice_Node_Sequence" "1 2" (at -7.62 5.08 0)
        (effects (font (size 1.524 1.524)) hide)
      )
      (symbol "VSOURCE_0_1"
        (polyline
          (pts
            (xy 0 -1.905)
            (xy 0 1.905)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 1.905)
            (xy -0.635 0.635)
            (xy 0.635 0.635)
            (xy 0 1.905)
          )
          (stroke (width 0) (type default))
          (fill (type outline))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "VSOURCE_1_1"
        (pin input line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 123.19 109.22) (diameter 1.016) (color 0 0 0 0)
    (uuid 50320142-854c-4317-b468-383f0f7bbdc6)
  )
  (junction (at 137.16 109.22) (diameter 1.016) (color 0 0 0 0)
    (uuid 94030bdc-c0a6-456e-bef7-bfc225a6db7d)
  )
  (junction (at 137.16 93.98) (diameter 1.016) (color 0 0 0 0)
    (uuid 9ddf5ec7-f79b-41d6-b4f8-8599f58e31e9)
  )
  (junction (at 111.76 109.22) (diameter 1.016) (color 0 0 0 0)
    (uuid a72accb4-c2e4-480a-a4b1-0d3b4c79a690)
  )

  (wire (pts (xy 121.92 93.98) (xy 125.73 93.98))
    (stroke (width 0) (type solid))
    (uuid 02454929-8625-4024-aa97-9f8e352f59c0)
  )
  (wire (pts (xy 137.16 97.79) (xy 137.16 93.98))
    (stroke (width 0) (type solid))
    (uuid 0318d67b-d09d-48e9-a283-************)
  )
  (wire (pts (xy 123.19 109.22) (xy 137.16 109.22))
    (stroke (width 0) (type solid))
    (uuid 156f26c8-15ab-4593-ab2f-8bccb3dbf3cc)
  )
  (wire (pts (xy 111.76 109.22) (xy 123.19 109.22))
    (stroke (width 0) (type solid))
    (uuid 39a6efda-4be3-46ec-9eb0-3dffbde514a7)
  )
  (wire (pts (xy 111.76 93.98) (xy 114.3 93.98))
    (stroke (width 0) (type solid))
    (uuid 3d9351f4-95f8-4013-ab81-86bce3bb0fbe)
  )
  (wire (pts (xy 111.76 97.79) (xy 111.76 93.98))
    (stroke (width 0) (type solid))
    (uuid 6cdc0c0e-22f8-4eed-8133-e7e17c16fb16)
  )
  (wire (pts (xy 133.35 93.98) (xy 137.16 93.98))
    (stroke (width 0) (type solid))
    (uuid 70c253a5-c35c-4399-a1bb-f5445780cff6)
  )
  (wire (pts (xy 146.05 109.22) (xy 146.05 105.41))
    (stroke (width 0) (type solid))
    (uuid 7b8de670-8f37-4e85-8c8f-f0ce83bed99e)
  )
  (wire (pts (xy 111.76 109.22) (xy 111.76 107.95))
    (stroke (width 0) (type solid))
    (uuid 937e7285-4bd7-4c16-99e2-b5a91e6d1d70)
  )
  (wire (pts (xy 137.16 93.98) (xy 146.05 93.98))
    (stroke (width 0) (type solid))
    (uuid 9e69f9a3-9c3c-45f6-8c92-56450392ef94)
  )
  (wire (pts (xy 137.16 109.22) (xy 137.16 105.41))
    (stroke (width 0) (type solid))
    (uuid a72c67cc-f909-4f47-acf4-005aacf4b68a)
  )
  (wire (pts (xy 146.05 93.98) (xy 146.05 97.79))
    (stroke (width 0) (type solid))
    (uuid a7f65573-38ca-4e75-977f-ea5d822aa5d4)
  )
  (wire (pts (xy 111.76 110.49) (xy 111.76 109.22))
    (stroke (width 0) (type solid))
    (uuid b32c29c2-b3c3-451d-9235-1422adefa684)
  )
  (wire (pts (xy 137.16 109.22) (xy 146.05 109.22))
    (stroke (width 0) (type solid))
    (uuid c910f183-7238-495c-a495-68e25207643c)
  )
  (wire (pts (xy 123.19 109.22) (xy 123.19 110.49))
    (stroke (width 0) (type solid))
    (uuid dd45a2b4-955f-498b-a38b-3601692badb0)
  )

  (text ".tran 1u 10m\n" (at 109.22 124.46 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
    (uuid 4dd0d58e-463c-4f81-bdbe-13e383dac5a6)
  )
  (text "*.ac dec 10 1 1Meg\n" (at 109.22 127 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
    (uuid 7e46c084-e7fd-4ef3-979f-9efa1a72fcb9)
  )

  (label "rect_out" (at 146.05 93.98 0) (fields_autoplaced)
    (effects (font (size 1.524 1.524)) (justify left bottom))
    (uuid 8900ee88-4367-40ab-b1a8-cdeb2f77ad67)
  )
  (label "signal_in" (at 111.76 93.98 0) (fields_autoplaced)
    (effects (font (size 1.524 1.524)) (justify right bottom))
    (uuid d092ae3c-3a85-43eb-bc55-8de96b129b4f)
  )

  (symbol (lib_id "rectifier_schlib:VSOURCE") (at 111.76 102.87 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-000057336052)
    (property "Reference" "V1" (at 108.5088 104.0384 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "${SIM.PARAMS}" (at 108.5088 101.727 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 111.76 102.87 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 111.76 102.87 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (at 111.76 102.87 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Device" "SPICE" (at 111.76 102.87 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Params" "type=\"V\" model=\"SINE(0 1.5 1k 0 0 0 0)\" lib=\"\"" (at 0 0 0)
      (effects (font (size 0 0)) hide)
    )
    (property "Sim.Pins" "1=1 2=2" (at 119.38 107.95 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid 276a1e63-be89-420f-bfb0-e89853ff2bbe))
    (pin "2" (uuid 1f416875-0f64-4ee1-b7b3-43f99f768e7a))
    (instances
      (project "rectifier"
        (path "/faea1833-708f-41af-98f4-4de67f419dbb"
          (reference "V1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "rectifier_schlib:GND") (at 111.76 110.49 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-0000573360d3)
    (property "Reference" "#PWR01" (at 111.76 116.84 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 111.887 114.8842 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 111.76 110.49 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 111.76 110.49 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 57addc70-d140-4d32-9cdd-4212aef041f7))
    (instances
      (project "rectifier"
        (path "/faea1833-708f-41af-98f4-4de67f419dbb"
          (reference "#PWR01") (unit 1)
        )
      )
    )
  )

  (symbol (lib_name "R_1") (lib_id "rectifier_schlib:R") (at 118.11 93.98 90) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 00000000-0000-0000-0000-0000573360f5)
    (property "Reference" "R1" (at 118.11 90.1319 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "1k" (at 118.11 91.8941 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 118.11 95.758 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 118.11 93.98 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (at 118.11 93.98 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Pins" "1=1 2=2" (at 118.11 93.98 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid a6d1a1a2-c669-4120-b2c0-97bc9a085c59))
    (pin "2" (uuid 3ad15cf0-c875-4ec8-b062-e18378a76de8))
    (instances
      (project "rectifier"
        (path "/faea1833-708f-41af-98f4-4de67f419dbb"
          (reference "R1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "rectifier_schlib:D") (at 129.54 93.98 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 00000000-0000-0000-0000-0000573361b8)
    (property "Reference" "D1" (at 129.5781 89.9287 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "1N4148" (at 129.5781 91.6909 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 129.54 93.98 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 129.54 93.98 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (at 129.54 93.98 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Library" "diode.mod" (at 129.54 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Name" "1N4148" (at 129.54 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Pins" "2=1 1=2" (at 129.54 93.98 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid cec6b17b-b6b0-4984-98e9-f509cd3279db))
    (pin "2" (uuid ae421f05-a66d-4a6d-861b-51239085bbf3))
    (instances
      (project "rectifier"
        (path "/faea1833-708f-41af-98f4-4de67f419dbb"
          (reference "D1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "rectifier_schlib:C") (at 137.16 101.6 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 00000000-0000-0000-0000-00005733628f)
    (property "Reference" "C1" (at 140.081 100.4316 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100n" (at 140.081 102.743 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 138.1252 105.41 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 137.16 101.6 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (at 137.16 101.6 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Pins" "1=1 2=2" (at 137.16 101.6 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid 91d3786e-a107-4b4b-bb3d-5312a687559c))
    (pin "2" (uuid 0a88943b-80d8-43cc-8135-3386da161997))
    (instances
      (project "rectifier"
        (path "/faea1833-708f-41af-98f4-4de67f419dbb"
          (reference "C1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "rectifier_schlib:R") (at 146.05 101.6 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 00000000-0000-0000-0000-0000573362f7)
    (property "Reference" "R2" (at 147.828 101.0055 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100k" (at 147.828 102.7677 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 144.272 101.6 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 146.05 101.6 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (at 146.05 101.6 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Sim.Pins" "1=1 2=2" (at 146.05 101.6 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid 418bbbe1-4f4e-4d80-8202-15228fce5120))
    (pin "2" (uuid a30660e1-fb61-4988-a9e9-d78123566c82))
    (instances
      (project "rectifier"
        (path "/faea1833-708f-41af-98f4-4de67f419dbb"
          (reference "R2") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:PWR_FLAG") (at 123.19 110.49 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 3cf2b621-b5e0-4e3b-ad75-1de14d8fb5dc)
    (property "Reference" "#FLG0101" (at 123.19 112.395 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "PWR_FLAG" (at 123.19 114.8144 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 123.19 110.49 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 123.19 110.49 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid f4dd87f3-0f0d-42c8-a75f-74cd280d6a20))
    (instances
      (project "rectifier"
        (path "/faea1833-708f-41af-98f4-4de67f419dbb"
          (reference "#FLG0101") (unit 1)
        )
      )
    )
  )

  (sheet_instances
    (path "/" (page "1"))
  )
)
