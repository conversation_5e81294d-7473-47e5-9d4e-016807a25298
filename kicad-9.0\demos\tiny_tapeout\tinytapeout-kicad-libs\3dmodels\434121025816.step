ISO-10303-21;
HEADER;
FILE_DESCRIPTION( ( 'Unknown' ), '1' );
FILE_NAME( 'I:/User_Groups_Only/17_EMC_Product_&_Development/Article_Master/Switch/SwTact/Master_Data/01_CAD_drawing/1_Drawing/4341x10258xx/Released_data/4341x10258xx.stp', 'Unknown', ( 'Unknown' ), ( 'Unknown' ), 'XStep 1.0', 'Unknown', '  ' );
FILE_SCHEMA( ( 'automotive_design' ) );
ENDSEC;
DATA;
#1 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION( ' ', ( #26, #27, #28, #29, #30, #31, #32, #33, #34, #35, #36, #37, #38, #39, #40, #41, #42, #43, #44, #45, #46, #47, #48, #49, #50, #51, #52, #53, #54, #55, #56, #57, #58, #59, #60, #61, #62, #63, #64, #65, #66, #67 ), #22 );
#2 = PRODUCT_DEFINITION_CONTEXT( '', #68, 'design' );
#3 = APPLICATION_PROTOCOL_DEFINITION( 'INTERNATIONAL STANDARD', 'automotive_design', 1994, #68 );
#4 = PRODUCT_CATEGORY_RELATIONSHIP( 'NONE', 'NONE', #69, #70 );
#5 = SHAPE_DEFINITION_REPRESENTATION( #71, #72 );
#6 = PRODUCT_DEFINITION_CONTEXT( '', #73, 'design' );
#7 = APPLICATION_PROTOCOL_DEFINITION( 'INTERNATIONAL STANDARD', 'automotive_design', 1994, #73 );
#8 = PRODUCT_CATEGORY_RELATIONSHIP( 'NONE', 'NONE', #74, #75 );
#9 = SHAPE_DEFINITION_REPRESENTATION( #76, #77 );
#10 = PRODUCT_DEFINITION_CONTEXT( '', #78, 'design' );
#11 = APPLICATION_PROTOCOL_DEFINITION( 'INTERNATIONAL STANDARD', 'automotive_design', 1994, #78 );
#12 = PRODUCT_CATEGORY_RELATIONSHIP( 'NONE', 'NONE', #79, #80 );
#13 = SHAPE_DEFINITION_REPRESENTATION( #81, #82 );
#14 = PRODUCT_DEFINITION_CONTEXT( '', #83, 'design' );
#15 = APPLICATION_PROTOCOL_DEFINITION( 'INTERNATIONAL STANDARD', 'automotive_design', 1994, #83 );
#16 = PRODUCT_CATEGORY_RELATIONSHIP( 'NONE', 'NONE', #84, #85 );
#17 = SHAPE_DEFINITION_REPRESENTATION( #86, #87 );
#18 = PRODUCT_DEFINITION_CONTEXT( '', #88, 'design' );
#19 = APPLICATION_PROTOCOL_DEFINITION( 'INTERNATIONAL STANDARD', 'automotive_design', 1994, #88 );
#20 = PRODUCT_CATEGORY_RELATIONSHIP( 'NONE', 'NONE', #89, #90 );
#21 = SHAPE_DEFINITION_REPRESENTATION( #91, #92 );
#22 =  ( GEOMETRIC_REPRESENTATION_CONTEXT( 3 )GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT( ( #95 ) )GLOBAL_UNIT_ASSIGNED_CONTEXT( ( #97, #98, #99 ) )REPRESENTATION_CONTEXT( 'NONE', 'WORKSPACE' ) );
#26 = STYLED_ITEM( '', ( #101 ), #102 );
#27 = STYLED_ITEM( '', ( #103 ), #104 );
#28 = STYLED_ITEM( '', ( #105 ), #106 );
#29 = STYLED_ITEM( '', ( #107 ), #108 );
#30 = STYLED_ITEM( '', ( #109 ), #110 );
#31 = STYLED_ITEM( '', ( #111 ), #112 );
#32 = STYLED_ITEM( '', ( #113 ), #114 );
#33 = STYLED_ITEM( '', ( #115 ), #116 );
#34 = STYLED_ITEM( '', ( #117 ), #118 );
#35 = STYLED_ITEM( '', ( #119 ), #120 );
#36 = STYLED_ITEM( '', ( #121 ), #122 );
#37 = STYLED_ITEM( '', ( #123 ), #124 );
#38 = STYLED_ITEM( '', ( #125 ), #126 );
#39 = STYLED_ITEM( '', ( #127 ), #128 );
#40 = STYLED_ITEM( '', ( #129 ), #130 );
#41 = STYLED_ITEM( '', ( #131 ), #132 );
#42 = STYLED_ITEM( '', ( #133 ), #134 );
#43 = STYLED_ITEM( '', ( #135 ), #136 );
#44 = STYLED_ITEM( '', ( #137 ), #138 );
#45 = STYLED_ITEM( '', ( #139 ), #140 );
#46 = STYLED_ITEM( '', ( #141 ), #142 );
#47 = STYLED_ITEM( '', ( #143 ), #144 );
#48 = STYLED_ITEM( '', ( #145 ), #146 );
#49 = STYLED_ITEM( '', ( #147 ), #148 );
#50 = STYLED_ITEM( '', ( #149 ), #150 );
#51 = STYLED_ITEM( '', ( #151 ), #152 );
#52 = STYLED_ITEM( '', ( #153 ), #154 );
#53 = STYLED_ITEM( '', ( #155 ), #156 );
#54 = STYLED_ITEM( '', ( #157 ), #158 );
#55 = STYLED_ITEM( '', ( #159 ), #160 );
#56 = STYLED_ITEM( '', ( #161 ), #162 );
#57 = STYLED_ITEM( '', ( #163 ), #164 );
#58 = STYLED_ITEM( '', ( #165 ), #166 );
#59 = STYLED_ITEM( '', ( #167 ), #168 );
#60 = STYLED_ITEM( '', ( #169 ), #170 );
#61 = STYLED_ITEM( '', ( #171 ), #172 );
#62 = STYLED_ITEM( '', ( #173 ), #174 );
#63 = STYLED_ITEM( '', ( #175 ), #176 );
#64 = STYLED_ITEM( '', ( #177 ), #178 );
#65 = STYLED_ITEM( '', ( #179 ), #180 );
#66 = STYLED_ITEM( '', ( #181 ), #182 );
#67 = STYLED_ITEM( '', ( #183 ), #184 );
#68 = APPLICATION_CONTEXT( ' ' );
#69 = PRODUCT_CATEGORY( 'part', 'NONE' );
#70 = PRODUCT_RELATED_PRODUCT_CATEGORY( 'detail', ' ', ( #185 ) );
#71 = PRODUCT_DEFINITION_SHAPE( 'NONE', 'NONE', #186 );
#72 = ADVANCED_BREP_SHAPE_REPRESENTATION( 'Assem1', ( #187, #188, #189, #190, #191 ), #22 );
#73 = APPLICATION_CONTEXT( ' ' );
#74 = PRODUCT_CATEGORY( 'part', 'NONE' );
#75 = PRODUCT_RELATED_PRODUCT_CATEGORY( 'detail', ' ', ( #192 ) );
#76 = PRODUCT_DEFINITION_SHAPE( 'NONE', 'NONE', #193 );
#77 = ADVANCED_BREP_SHAPE_REPRESENTATION( 'base', ( #194 ), #22 );
#78 = APPLICATION_CONTEXT( ' ' );
#79 = PRODUCT_CATEGORY( 'part', 'NONE' );
#80 = PRODUCT_RELATED_PRODUCT_CATEGORY( 'detail', ' ', ( #195 ) );
#81 = PRODUCT_DEFINITION_SHAPE( 'NONE', 'NONE', #196 );
#82 = ADVANCED_BREP_SHAPE_REPRESENTATION( 'top cover', ( #197 ), #22 );
#83 = APPLICATION_CONTEXT( ' ' );
#84 = PRODUCT_CATEGORY( 'part', 'NONE' );
#85 = PRODUCT_RELATED_PRODUCT_CATEGORY( 'detail', ' ', ( #198 ) );
#86 = PRODUCT_DEFINITION_SHAPE( 'NONE', 'NONE', #199 );
#87 = ADVANCED_BREP_SHAPE_REPRESENTATION( 'pins', ( #200 ), #22 );
#88 = APPLICATION_CONTEXT( ' ' );
#89 = PRODUCT_CATEGORY( 'part', 'NONE' );
#90 = PRODUCT_RELATED_PRODUCT_CATEGORY( 'detail', ' ', ( #201 ) );
#91 = PRODUCT_DEFINITION_SHAPE( 'NONE', 'NONE', #202 );
#92 = ADVANCED_BREP_SHAPE_REPRESENTATION( 'pins', ( #203 ), #22 );
#95 = UNCERTAINTY_MEASURE_WITH_UNIT( LENGTH_MEASURE( 0.00100000000000000 ), #97, '', '' );
#97 =  ( CONVERSION_BASED_UNIT( 'MILLIMETRE', #206 )LENGTH_UNIT(  )NAMED_UNIT( #209 ) );
#98 =  ( NAMED_UNIT( #211 )PLANE_ANGLE_UNIT(  )SI_UNIT( $, .RADIAN. ) );
#99 =  ( NAMED_UNIT( #211 )SOLID_ANGLE_UNIT(  )SI_UNIT( $, .STERADIAN. ) );
#101 = PRESENTATION_STYLE_ASSIGNMENT( ( #217 ) );
#102 = ADVANCED_FACE( '', ( #218 ), #219, .F. );
#103 = PRESENTATION_STYLE_ASSIGNMENT( ( #220 ) );
#104 = ADVANCED_FACE( '', ( #221 ), #222, .T. );
#105 = PRESENTATION_STYLE_ASSIGNMENT( ( #223 ) );
#106 = ADVANCED_FACE( '', ( #224 ), #225, .T. );
#107 = PRESENTATION_STYLE_ASSIGNMENT( ( #226 ) );
#108 = ADVANCED_FACE( '', ( #227 ), #228, .F. );
#109 = PRESENTATION_STYLE_ASSIGNMENT( ( #229 ) );
#110 = ADVANCED_FACE( '', ( #230 ), #231, .F. );
#111 = PRESENTATION_STYLE_ASSIGNMENT( ( #232 ) );
#112 = ADVANCED_FACE( '', ( #233 ), #234, .T. );
#113 = PRESENTATION_STYLE_ASSIGNMENT( ( #235 ) );
#114 = ADVANCED_FACE( '', ( #236 ), #237, .T. );
#115 = PRESENTATION_STYLE_ASSIGNMENT( ( #238 ) );
#116 = ADVANCED_FACE( '', ( #239 ), #240, .F. );
#117 = PRESENTATION_STYLE_ASSIGNMENT( ( #241 ) );
#118 = ADVANCED_FACE( '', ( #242 ), #243, .T. );
#119 = PRESENTATION_STYLE_ASSIGNMENT( ( #244 ) );
#120 = ADVANCED_FACE( '', ( #245 ), #246, .T. );
#121 = PRESENTATION_STYLE_ASSIGNMENT( ( #247 ) );
#122 = ADVANCED_FACE( '', ( #248 ), #249, .F. );
#123 = PRESENTATION_STYLE_ASSIGNMENT( ( #250 ) );
#124 = ADVANCED_FACE( '', ( #251 ), #252, .F. );
#125 = PRESENTATION_STYLE_ASSIGNMENT( ( #253 ) );
#126 = ADVANCED_FACE( '', ( #254 ), #255, .F. );
#127 = PRESENTATION_STYLE_ASSIGNMENT( ( #256 ) );
#128 = ADVANCED_FACE( '', ( #257 ), #258, .T. );
#129 = PRESENTATION_STYLE_ASSIGNMENT( ( #259 ) );
#130 = ADVANCED_FACE( '', ( #260 ), #261, .T. );
#131 = PRESENTATION_STYLE_ASSIGNMENT( ( #262 ) );
#132 = ADVANCED_FACE( '', ( #263 ), #264, .T. );
#133 = PRESENTATION_STYLE_ASSIGNMENT( ( #265 ) );
#134 = ADVANCED_FACE( '', ( #266, #267 ), #268, .T. );
#135 = PRESENTATION_STYLE_ASSIGNMENT( ( #269 ) );
#136 = ADVANCED_FACE( '', ( #270 ), #271, .T. );
#137 = PRESENTATION_STYLE_ASSIGNMENT( ( #272 ) );
#138 = ADVANCED_FACE( '', ( #273 ), #274, .F. );
#139 = PRESENTATION_STYLE_ASSIGNMENT( ( #275 ) );
#140 = ADVANCED_FACE( '', ( #276, #277 ), #278, .T. );
#141 = PRESENTATION_STYLE_ASSIGNMENT( ( #279 ) );
#142 = ADVANCED_FACE( '', ( #280 ), #281, .T. );
#143 = PRESENTATION_STYLE_ASSIGNMENT( ( #282 ) );
#144 = ADVANCED_FACE( '', ( #283 ), #284, .T. );
#145 = PRESENTATION_STYLE_ASSIGNMENT( ( #285 ) );
#146 = ADVANCED_FACE( '', ( #286 ), #287, .T. );
#147 = PRESENTATION_STYLE_ASSIGNMENT( ( #288 ) );
#148 = ADVANCED_FACE( '', ( #289 ), #290, .T. );
#149 = PRESENTATION_STYLE_ASSIGNMENT( ( #291 ) );
#150 = ADVANCED_FACE( '', ( #292, #293 ), #294, .T. );
#151 = PRESENTATION_STYLE_ASSIGNMENT( ( #295 ) );
#152 = ADVANCED_FACE( '', ( #296 ), #297, .F. );
#153 = PRESENTATION_STYLE_ASSIGNMENT( ( #298 ) );
#154 = ADVANCED_FACE( '', ( #299 ), #300, .T. );
#155 = PRESENTATION_STYLE_ASSIGNMENT( ( #301 ) );
#156 = ADVANCED_FACE( '', ( #302 ), #303, .F. );
#157 = PRESENTATION_STYLE_ASSIGNMENT( ( #304 ) );
#158 = ADVANCED_FACE( '', ( #305 ), #306, .T. );
#159 = PRESENTATION_STYLE_ASSIGNMENT( ( #307 ) );
#160 = ADVANCED_FACE( '', ( #308, #309 ), #310, .T. );
#161 = PRESENTATION_STYLE_ASSIGNMENT( ( #311 ) );
#162 = ADVANCED_FACE( '', ( #312 ), #313, .T. );
#163 = PRESENTATION_STYLE_ASSIGNMENT( ( #314 ) );
#164 = ADVANCED_FACE( '', ( #315 ), #316, .F. );
#165 = PRESENTATION_STYLE_ASSIGNMENT( ( #317 ) );
#166 = ADVANCED_FACE( '', ( #318 ), #319, .F. );
#167 = PRESENTATION_STYLE_ASSIGNMENT( ( #320 ) );
#168 = ADVANCED_FACE( '', ( #321 ), #322, .T. );
#169 = PRESENTATION_STYLE_ASSIGNMENT( ( #323 ) );
#170 = ADVANCED_FACE( '', ( #324 ), #325, .F. );
#171 = PRESENTATION_STYLE_ASSIGNMENT( ( #326 ) );
#172 = ADVANCED_FACE( '', ( #327 ), #328, .T. );
#173 = PRESENTATION_STYLE_ASSIGNMENT( ( #329 ) );
#174 = ADVANCED_FACE( '', ( #330 ), #331, .T. );
#175 = PRESENTATION_STYLE_ASSIGNMENT( ( #332 ) );
#176 = ADVANCED_FACE( '', ( #333 ), #334, .F. );
#177 = PRESENTATION_STYLE_ASSIGNMENT( ( #335 ) );
#178 = ADVANCED_FACE( '', ( #336 ), #337, .F. );
#179 = PRESENTATION_STYLE_ASSIGNMENT( ( #338 ) );
#180 = ADVANCED_FACE( '', ( #339 ), #340, .T. );
#181 = PRESENTATION_STYLE_ASSIGNMENT( ( #341 ) );
#182 = ADVANCED_FACE( '', ( #342 ), #343, .F. );
#183 = PRESENTATION_STYLE_ASSIGNMENT( ( #344 ) );
#184 = ADVANCED_FACE( '', ( #345 ), #346, .F. );
#185 = PRODUCT( 'Assem1', 'Assem1', 'PART-Assem1-DESC', ( #347 ) );
#186 = PRODUCT_DEFINITION( 'NONE', 'NONE', #348, #2 );
#187 = MAPPED_ITEM( '', #349, #350 );
#188 = MAPPED_ITEM( '', #351, #352 );
#189 = MAPPED_ITEM( '', #353, #354 );
#190 = MAPPED_ITEM( '', #355, #356 );
#191 = AXIS2_PLACEMENT_3D( '', #357, #358, #359 );
#192 = PRODUCT( 'base', 'base', 'PART-base-DESC', ( #360 ) );
#193 = PRODUCT_DEFINITION( 'NONE', 'NONE', #361, #6 );
#194 = MANIFOLD_SOLID_BREP( 'base', #362 );
#195 = PRODUCT( 'top cover', 'top cover', 'PART-top cover-DESC', ( #363 ) );
#196 = PRODUCT_DEFINITION( 'NONE', 'NONE', #364, #10 );
#197 = MANIFOLD_SOLID_BREP( 'top cover', #365 );
#198 = PRODUCT( 'pins', 'pins', 'PART-pins-DESC', ( #366 ) );
#199 = PRODUCT_DEFINITION( 'NONE', 'NONE', #367, #14 );
#200 = MANIFOLD_SOLID_BREP( 'pins', #368 );
#201 = PRODUCT( 'pins', 'pins', 'PART-pins-DESC', ( #369 ) );
#202 = PRODUCT_DEFINITION( 'NONE', 'NONE', #370, #18 );
#203 = MANIFOLD_SOLID_BREP( 'pins', #371 );
#206 = LENGTH_MEASURE_WITH_UNIT( LENGTH_MEASURE( 1.00000000000000 ), #372 );
#209 = DIMENSIONAL_EXPONENTS( 1.00000000000000, 0.000000000000000, 0.000000000000000, 0.000000000000000, 0.000000000000000, 0.000000000000000, 0.000000000000000 );
#211 = DIMENSIONAL_EXPONENTS( 0.000000000000000, 0.000000000000000, 0.000000000000000, 0.000000000000000, 0.000000000000000, 0.000000000000000, 0.000000000000000 );
#217 = SURFACE_STYLE_USAGE( .BOTH., #373 );
#218 = FACE_OUTER_BOUND( '', #374, .T. );
#219 = PLANE( '', #375 );
#220 = SURFACE_STYLE_USAGE( .BOTH., #376 );
#221 = FACE_OUTER_BOUND( '', #377, .T. );
#222 = CYLINDRICAL_SURFACE( '', #378, 0.0500000000000000 );
#223 = SURFACE_STYLE_USAGE( .BOTH., #379 );
#224 = FACE_OUTER_BOUND( '', #380, .T. );
#225 = PLANE( '', #381 );
#226 = SURFACE_STYLE_USAGE( .BOTH., #382 );
#227 = FACE_OUTER_BOUND( '', #383, .T. );
#228 = PLANE( '', #384 );
#229 = SURFACE_STYLE_USAGE( .BOTH., #385 );
#230 = FACE_OUTER_BOUND( '', #386, .T. );
#231 = PLANE( '', #387 );
#232 = SURFACE_STYLE_USAGE( .BOTH., #388 );
#233 = FACE_OUTER_BOUND( '', #389, .T. );
#234 = PLANE( '', #390 );
#235 = SURFACE_STYLE_USAGE( .BOTH., #391 );
#236 = FACE_OUTER_BOUND( '', #392, .T. );
#237 = PLANE( '', #393 );
#238 = SURFACE_STYLE_USAGE( .BOTH., #394 );
#239 = FACE_OUTER_BOUND( '', #395, .T. );
#240 = PLANE( '', #396 );
#241 = SURFACE_STYLE_USAGE( .BOTH., #397 );
#242 = FACE_OUTER_BOUND( '', #398, .T. );
#243 = PLANE( '', #399 );
#244 = SURFACE_STYLE_USAGE( .BOTH., #400 );
#245 = FACE_OUTER_BOUND( '', #401, .T. );
#246 = PLANE( '', #402 );
#247 = SURFACE_STYLE_USAGE( .BOTH., #403 );
#248 = FACE_OUTER_BOUND( '', #404, .T. );
#249 = PLANE( '', #405 );
#250 = SURFACE_STYLE_USAGE( .BOTH., #406 );
#251 = FACE_OUTER_BOUND( '', #407, .T. );
#252 = PLANE( '', #408 );
#253 = SURFACE_STYLE_USAGE( .BOTH., #409 );
#254 = FACE_OUTER_BOUND( '', #410, .T. );
#255 = PLANE( '', #411 );
#256 = SURFACE_STYLE_USAGE( .BOTH., #412 );
#257 = FACE_OUTER_BOUND( '', #413, .T. );
#258 = PLANE( '', #414 );
#259 = SURFACE_STYLE_USAGE( .BOTH., #415 );
#260 = FACE_OUTER_BOUND( '', #416, .T. );
#261 = PLANE( '', #417 );
#262 = SURFACE_STYLE_USAGE( .BOTH., #418 );
#263 = FACE_OUTER_BOUND( '', #419, .T. );
#264 = PLANE( '', #420 );
#265 = SURFACE_STYLE_USAGE( .BOTH., #421 );
#266 = FACE_OUTER_BOUND( '', #422, .T. );
#267 = FACE_OUTER_BOUND( '', #423, .T. );
#268 = TOROIDAL_SURFACE( '', #424, 0.850000000000000, 0.100000000000000 );
#269 = SURFACE_STYLE_USAGE( .BOTH., #425 );
#270 = FACE_OUTER_BOUND( '', #426, .T. );
#271 = CYLINDRICAL_SURFACE( '', #427, 0.0500000000000000 );
#272 = SURFACE_STYLE_USAGE( .BOTH., #428 );
#273 = FACE_OUTER_BOUND( '', #429, .T. );
#274 = PLANE( '', #430 );
#275 = SURFACE_STYLE_USAGE( .BOTH., #431 );
#276 = FACE_OUTER_BOUND( '', #432, .T. );
#277 = FACE_OUTER_BOUND( '', #433, .T. );
#278 = CYLINDRICAL_SURFACE( '', #434, 0.950000000000000 );
#279 = SURFACE_STYLE_USAGE( .BOTH., #435 );
#280 = FACE_OUTER_BOUND( '', #436, .T. );
#281 = CYLINDRICAL_SURFACE( '', #437, 0.0500000000000000 );
#282 = SURFACE_STYLE_USAGE( .BOTH., #438 );
#283 = FACE_OUTER_BOUND( '', #439, .T. );
#284 = PLANE( '', #440 );
#285 = SURFACE_STYLE_USAGE( .BOTH., #441 );
#286 = FACE_OUTER_BOUND( '', #442, .T. );
#287 = PLANE( '', #443 );
#288 = SURFACE_STYLE_USAGE( .BOTH., #444 );
#289 = FACE_OUTER_BOUND( '', #445, .T. );
#290 = CYLINDRICAL_SURFACE( '', #446, 0.0500000000000000 );
#291 = SURFACE_STYLE_USAGE( .BOTH., #447 );
#292 = FACE_OUTER_BOUND( '', #448, .T. );
#293 = FACE_BOUND( '', #449, .T. );
#294 = PLANE( '', #450 );
#295 = SURFACE_STYLE_USAGE( .BOTH., #451 );
#296 = FACE_OUTER_BOUND( '', #452, .T. );
#297 = PLANE( '', #453 );
#298 = SURFACE_STYLE_USAGE( .BOTH., #454 );
#299 = FACE_OUTER_BOUND( '', #455, .T. );
#300 = CYLINDRICAL_SURFACE( '', #456, 0.0500000000000000 );
#301 = SURFACE_STYLE_USAGE( .BOTH., #457 );
#302 = FACE_OUTER_BOUND( '', #458, .T. );
#303 = PLANE( '', #459 );
#304 = SURFACE_STYLE_USAGE( .BOTH., #460 );
#305 = FACE_OUTER_BOUND( '', #461, .T. );
#306 = PLANE( '', #462 );
#307 = SURFACE_STYLE_USAGE( .BOTH., #463 );
#308 = FACE_OUTER_BOUND( '', #464, .T. );
#309 = FACE_BOUND( '', #465, .T. );
#310 = PLANE( '', #466 );
#311 = SURFACE_STYLE_USAGE( .BOTH., #467 );
#312 = FACE_OUTER_BOUND( '', #468, .T. );
#313 = PLANE( '', #469 );
#314 = SURFACE_STYLE_USAGE( .BOTH., #470 );
#315 = FACE_OUTER_BOUND( '', #471, .T. );
#316 = PLANE( '', #472 );
#317 = SURFACE_STYLE_USAGE( .BOTH., #473 );
#318 = FACE_OUTER_BOUND( '', #474, .T. );
#319 = PLANE( '', #475 );
#320 = SURFACE_STYLE_USAGE( .BOTH., #476 );
#321 = FACE_OUTER_BOUND( '', #477, .T. );
#322 = PLANE( '', #478 );
#323 = SURFACE_STYLE_USAGE( .BOTH., #479 );
#324 = FACE_OUTER_BOUND( '', #480, .T. );
#325 = PLANE( '', #481 );
#326 = SURFACE_STYLE_USAGE( .BOTH., #482 );
#327 = FACE_OUTER_BOUND( '', #483, .T. );
#328 = CYLINDRICAL_SURFACE( '', #484, 0.0500000000000000 );
#329 = SURFACE_STYLE_USAGE( .BOTH., #485 );
#330 = FACE_OUTER_BOUND( '', #486, .T. );
#331 = PLANE( '', #487 );
#332 = SURFACE_STYLE_USAGE( .BOTH., #488 );
#333 = FACE_OUTER_BOUND( '', #489, .T. );
#334 = PLANE( '', #490 );
#335 = SURFACE_STYLE_USAGE( .BOTH., #491 );
#336 = FACE_OUTER_BOUND( '', #492, .T. );
#337 = PLANE( '', #493 );
#338 = SURFACE_STYLE_USAGE( .BOTH., #494 );
#339 = FACE_OUTER_BOUND( '', #495, .T. );
#340 = PLANE( '', #496 );
#341 = SURFACE_STYLE_USAGE( .BOTH., #497 );
#342 = FACE_OUTER_BOUND( '', #498, .T. );
#343 = PLANE( '', #499 );
#344 = SURFACE_STYLE_USAGE( .BOTH., #500 );
#345 = FACE_OUTER_BOUND( '', #501, .T. );
#346 = PLANE( '', #502 );
#347 = PRODUCT_CONTEXT( '', #68, 'mechanical' );
#348 = PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE( ' ', 'NONE', #185, .NOT_KNOWN. );
#349 = REPRESENTATION_MAP( #191, #77 );
#350 = AXIS2_PLACEMENT_3D( '', #503, #504, #505 );
#351 = REPRESENTATION_MAP( #191, #82 );
#352 = AXIS2_PLACEMENT_3D( '', #506, #507, #508 );
#353 = REPRESENTATION_MAP( #191, #87 );
#354 = AXIS2_PLACEMENT_3D( '', #509, #510, #511 );
#355 = REPRESENTATION_MAP( #191, #92 );
#356 = AXIS2_PLACEMENT_3D( '', #512, #513, #514 );
#357 = CARTESIAN_POINT( '', ( 0.000000000000000, 0.000000000000000, 0.000000000000000 ) );
#358 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#359 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#360 = PRODUCT_CONTEXT( '', #73, 'mechanical' );
#361 = PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE( ' ', 'NONE', #192, .NOT_KNOWN. );
#362 = CLOSED_SHELL( '', ( #132, #134, #176, #104, #108, #142, #150, #152, #156, #178, #126, #154, #110, #166, #118, #122, #146, #144, #120, #112, #138, #136, #148, #160, #172, #174, #180, #162, #168, #128, #182, #184, #102, #164, #106, #114, #158, #140, #170, #130, #124, #116 ) );
#363 = PRODUCT_CONTEXT( '', #78, 'mechanical' );
#364 = PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE( ' ', 'NONE', #195, .NOT_KNOWN. );
#365 = CLOSED_SHELL( '', ( #515, #516, #517, #518, #519, #520, #521, #522, #523, #524, #525, #526, #527, #528, #529, #530, #531, #532, #533, #534, #535, #536, #537, #538, #539, #540, #541, #542, #543, #544, #545, #546, #547, #548, #549, #550, #551, #552, #553, #554, #555, #556, #557, #558, #559, #560, #561, #562, #563, #564, #565, #566, #567, #568, #569, #570, #571, #572, #573, #574, #575, #576, #577 ) );
#366 = PRODUCT_CONTEXT( '', #83, 'mechanical' );
#367 = PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE( ' ', 'NONE', #198, .NOT_KNOWN. );
#368 = CLOSED_SHELL( '', ( #578, #579, #580, #581, #582, #583 ) );
#369 = PRODUCT_CONTEXT( '', #88, 'mechanical' );
#370 = PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE( ' ', 'NONE', #201, .NOT_KNOWN. );
#371 = CLOSED_SHELL( '', ( #584, #585, #586, #587, #588, #589 ) );
#372 =  ( NAMED_UNIT( #209 )LENGTH_UNIT(  )SI_UNIT( .MILLI., .METRE. ) );
#373 = SURFACE_SIDE_STYLE( '', ( #591 ) );
#374 = EDGE_LOOP( '', ( #592, #593, #594, #595 ) );
#375 = AXIS2_PLACEMENT_3D( '', #596, #597, #598 );
#376 = SURFACE_SIDE_STYLE( '', ( #599 ) );
#377 = EDGE_LOOP( '', ( #600, #601, #602, #603, #604, #605 ) );
#378 = AXIS2_PLACEMENT_3D( '', #606, #607, #608 );
#379 = SURFACE_SIDE_STYLE( '', ( #609 ) );
#380 = EDGE_LOOP( '', ( #610, #611, #612, #613 ) );
#381 = AXIS2_PLACEMENT_3D( '', #614, #615, #616 );
#382 = SURFACE_SIDE_STYLE( '', ( #617 ) );
#383 = EDGE_LOOP( '', ( #618, #619, #620, #621 ) );
#384 = AXIS2_PLACEMENT_3D( '', #622, #623, #624 );
#385 = SURFACE_SIDE_STYLE( '', ( #625 ) );
#386 = EDGE_LOOP( '', ( #626, #627, #628, #629 ) );
#387 = AXIS2_PLACEMENT_3D( '', #630, #631, #632 );
#388 = SURFACE_SIDE_STYLE( '', ( #633 ) );
#389 = EDGE_LOOP( '', ( #634, #635, #636, #637 ) );
#390 = AXIS2_PLACEMENT_3D( '', #638, #639, #640 );
#391 = SURFACE_SIDE_STYLE( '', ( #641 ) );
#392 = EDGE_LOOP( '', ( #642, #643, #644, #645 ) );
#393 = AXIS2_PLACEMENT_3D( '', #646, #647, #648 );
#394 = SURFACE_SIDE_STYLE( '', ( #649 ) );
#395 = EDGE_LOOP( '', ( #650 ) );
#396 = AXIS2_PLACEMENT_3D( '', #651, #652, #653 );
#397 = SURFACE_SIDE_STYLE( '', ( #654 ) );
#398 = EDGE_LOOP( '', ( #655, #656, #657, #658, #659, #660 ) );
#399 = AXIS2_PLACEMENT_3D( '', #661, #662, #663 );
#400 = SURFACE_SIDE_STYLE( '', ( #664 ) );
#401 = EDGE_LOOP( '', ( #665, #666, #667, #668, #669, #670 ) );
#402 = AXIS2_PLACEMENT_3D( '', #671, #672, #673 );
#403 = SURFACE_SIDE_STYLE( '', ( #674 ) );
#404 = EDGE_LOOP( '', ( #675, #676, #677, #678 ) );
#405 = AXIS2_PLACEMENT_3D( '', #679, #680, #681 );
#406 = SURFACE_SIDE_STYLE( '', ( #682 ) );
#407 = EDGE_LOOP( '', ( #683, #684, #685, #686 ) );
#408 = AXIS2_PLACEMENT_3D( '', #687, #688, #689 );
#409 = SURFACE_SIDE_STYLE( '', ( #690 ) );
#410 = EDGE_LOOP( '', ( #691, #692, #693, #694, #695, #696, #697, #698, #699, #700 ) );
#411 = AXIS2_PLACEMENT_3D( '', #701, #702, #703 );
#412 = SURFACE_SIDE_STYLE( '', ( #704 ) );
#413 = EDGE_LOOP( '', ( #705, #706, #707, #708 ) );
#414 = AXIS2_PLACEMENT_3D( '', #709, #710, #711 );
#415 = SURFACE_SIDE_STYLE( '', ( #712 ) );
#416 = EDGE_LOOP( '', ( #713, #714, #715, #716 ) );
#417 = AXIS2_PLACEMENT_3D( '', #717, #718, #719 );
#418 = SURFACE_SIDE_STYLE( '', ( #720 ) );
#419 = EDGE_LOOP( '', ( #721, #722, #723, #724 ) );
#420 = AXIS2_PLACEMENT_3D( '', #725, #726, #727 );
#421 = SURFACE_SIDE_STYLE( '', ( #728 ) );
#422 = EDGE_LOOP( '', ( #729 ) );
#423 = EDGE_LOOP( '', ( #730 ) );
#424 = AXIS2_PLACEMENT_3D( '', #731, #732, #733 );
#425 = SURFACE_SIDE_STYLE( '', ( #734 ) );
#426 = EDGE_LOOP( '', ( #735, #736, #737, #738 ) );
#427 = AXIS2_PLACEMENT_3D( '', #739, #740, #741 );
#428 = SURFACE_SIDE_STYLE( '', ( #742 ) );
#429 = EDGE_LOOP( '', ( #743, #744, #745, #746, #747, #748, #749, #750 ) );
#430 = AXIS2_PLACEMENT_3D( '', #751, #752, #753 );
#431 = SURFACE_SIDE_STYLE( '', ( #754 ) );
#432 = EDGE_LOOP( '', ( #755 ) );
#433 = EDGE_LOOP( '', ( #756 ) );
#434 = AXIS2_PLACEMENT_3D( '', #757, #758, #759 );
#435 = SURFACE_SIDE_STYLE( '', ( #760 ) );
#436 = EDGE_LOOP( '', ( #761, #762, #763, #764 ) );
#437 = AXIS2_PLACEMENT_3D( '', #765, #766, #767 );
#438 = SURFACE_SIDE_STYLE( '', ( #768 ) );
#439 = EDGE_LOOP( '', ( #769, #770, #771, #772 ) );
#440 = AXIS2_PLACEMENT_3D( '', #773, #774, #775 );
#441 = SURFACE_SIDE_STYLE( '', ( #776 ) );
#442 = EDGE_LOOP( '', ( #777, #778, #779, #780 ) );
#443 = AXIS2_PLACEMENT_3D( '', #781, #782, #783 );
#444 = SURFACE_SIDE_STYLE( '', ( #784 ) );
#445 = EDGE_LOOP( '', ( #785, #786, #787, #788 ) );
#446 = AXIS2_PLACEMENT_3D( '', #789, #790, #791 );
#447 = SURFACE_SIDE_STYLE( '', ( #792 ) );
#448 = EDGE_LOOP( '', ( #793, #794, #795, #796, #797, #798, #799, #800, #801, #802, #803, #804 ) );
#449 = EDGE_LOOP( '', ( #805, #806, #807, #808 ) );
#450 = AXIS2_PLACEMENT_3D( '', #809, #810, #811 );
#451 = SURFACE_SIDE_STYLE( '', ( #812 ) );
#452 = EDGE_LOOP( '', ( #813, #814, #815, #816, #817, #818, #819, #820, #821, #822 ) );
#453 = AXIS2_PLACEMENT_3D( '', #823, #824, #825 );
#454 = SURFACE_SIDE_STYLE( '', ( #826 ) );
#455 = EDGE_LOOP( '', ( #827, #828, #829, #830 ) );
#456 = AXIS2_PLACEMENT_3D( '', #831, #832, #833 );
#457 = SURFACE_SIDE_STYLE( '', ( #834 ) );
#458 = EDGE_LOOP( '', ( #835, #836, #837, #838, #839, #840, #841, #842, #843, #844, #845, #846 ) );
#459 = AXIS2_PLACEMENT_3D( '', #847, #848, #849 );
#460 = SURFACE_SIDE_STYLE( '', ( #850 ) );
#461 = EDGE_LOOP( '', ( #851, #852, #853, #854 ) );
#462 = AXIS2_PLACEMENT_3D( '', #855, #856, #857 );
#463 = SURFACE_SIDE_STYLE( '', ( #858 ) );
#464 = EDGE_LOOP( '', ( #859, #860, #861, #862 ) );
#465 = EDGE_LOOP( '', ( #863 ) );
#466 = AXIS2_PLACEMENT_3D( '', #864, #865, #866 );
#467 = SURFACE_SIDE_STYLE( '', ( #867 ) );
#468 = EDGE_LOOP( '', ( #868, #869, #870, #871 ) );
#469 = AXIS2_PLACEMENT_3D( '', #872, #873, #874 );
#470 = SURFACE_SIDE_STYLE( '', ( #875 ) );
#471 = EDGE_LOOP( '', ( #876, #877, #878, #879 ) );
#472 = AXIS2_PLACEMENT_3D( '', #880, #881, #882 );
#473 = SURFACE_SIDE_STYLE( '', ( #883 ) );
#474 = EDGE_LOOP( '', ( #884, #885, #886, #887 ) );
#475 = AXIS2_PLACEMENT_3D( '', #888, #889, #890 );
#476 = SURFACE_SIDE_STYLE( '', ( #891 ) );
#477 = EDGE_LOOP( '', ( #892, #893, #894, #895 ) );
#478 = AXIS2_PLACEMENT_3D( '', #896, #897, #898 );
#479 = SURFACE_SIDE_STYLE( '', ( #899 ) );
#480 = EDGE_LOOP( '', ( #900, #901, #902, #903, #904, #905 ) );
#481 = AXIS2_PLACEMENT_3D( '', #906, #907, #908 );
#482 = SURFACE_SIDE_STYLE( '', ( #909 ) );
#483 = EDGE_LOOP( '', ( #910, #911, #912, #913, #914, #915 ) );
#484 = AXIS2_PLACEMENT_3D( '', #916, #917, #918 );
#485 = SURFACE_SIDE_STYLE( '', ( #919 ) );
#486 = EDGE_LOOP( '', ( #920, #921, #922, #923 ) );
#487 = AXIS2_PLACEMENT_3D( '', #924, #925, #926 );
#488 = SURFACE_SIDE_STYLE( '', ( #927 ) );
#489 = EDGE_LOOP( '', ( #928, #929, #930, #931, #932, #933 ) );
#490 = AXIS2_PLACEMENT_3D( '', #934, #935, #936 );
#491 = SURFACE_SIDE_STYLE( '', ( #937 ) );
#492 = EDGE_LOOP( '', ( #938, #939, #940, #941, #942, #943, #944, #945 ) );
#493 = AXIS2_PLACEMENT_3D( '', #946, #947, #948 );
#494 = SURFACE_SIDE_STYLE( '', ( #949 ) );
#495 = EDGE_LOOP( '', ( #950, #951, #952, #953 ) );
#496 = AXIS2_PLACEMENT_3D( '', #954, #955, #956 );
#497 = SURFACE_SIDE_STYLE( '', ( #957 ) );
#498 = EDGE_LOOP( '', ( #958, #959, #960, #961 ) );
#499 = AXIS2_PLACEMENT_3D( '', #962, #963, #964 );
#500 = SURFACE_SIDE_STYLE( '', ( #965 ) );
#501 = EDGE_LOOP( '', ( #966, #967, #968, #969 ) );
#502 = AXIS2_PLACEMENT_3D( '', #970, #971, #972 );
#503 = CARTESIAN_POINT( '', ( 0.000000000000000, 0.000000000000000, 0.000000000000000 ) );
#504 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#505 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#506 = CARTESIAN_POINT( '', ( 0.000000000000000, 0.000000000000000, 0.000000000000000 ) );
#507 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#508 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#509 = CARTESIAN_POINT( '', ( -2.60208521396521E-015, -8.34835672813838E-015, 0.00100000000000044 ) );
#510 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#511 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#512 = CARTESIAN_POINT( '', ( -2.60208521396521E-015, -8.34835672813838E-015, 0.00100000000000044 ) );
#513 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#514 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#515 = ADVANCED_FACE( '', ( #973 ), #974, .T. );
#516 = ADVANCED_FACE( '', ( #975 ), #976, .T. );
#517 = ADVANCED_FACE( '', ( #977, #978 ), #979, .F. );
#518 = ADVANCED_FACE( '', ( #980 ), #981, .T. );
#519 = ADVANCED_FACE( '', ( #982 ), #983, .F. );
#520 = ADVANCED_FACE( '', ( #984 ), #985, .F. );
#521 = ADVANCED_FACE( '', ( #986 ), #987, .T. );
#522 = ADVANCED_FACE( '', ( #988 ), #989, .F. );
#523 = ADVANCED_FACE( '', ( #990 ), #991, .F. );
#524 = ADVANCED_FACE( '', ( #992 ), #993, .F. );
#525 = ADVANCED_FACE( '', ( #994 ), #995, .T. );
#526 = ADVANCED_FACE( '', ( #996 ), #997, .F. );
#527 = ADVANCED_FACE( '', ( #998 ), #999, .F. );
#528 = ADVANCED_FACE( '', ( #1000 ), #1001, .F. );
#529 = ADVANCED_FACE( '', ( #1002 ), #1003, .F. );
#530 = ADVANCED_FACE( '', ( #1004 ), #1005, .T. );
#531 = ADVANCED_FACE( '', ( #1006 ), #1007, .F. );
#532 = ADVANCED_FACE( '', ( #1008, #1009 ), #1010, .T. );
#533 = ADVANCED_FACE( '', ( #1011 ), #1012, .F. );
#534 = ADVANCED_FACE( '', ( #1013 ), #1014, .F. );
#535 = ADVANCED_FACE( '', ( #1015 ), #1016, .T. );
#536 = ADVANCED_FACE( '', ( #1017 ), #1018, .F. );
#537 = ADVANCED_FACE( '', ( #1019 ), #1020, .T. );
#538 = ADVANCED_FACE( '', ( #1021 ), #1022, .T. );
#539 = ADVANCED_FACE( '', ( #1023 ), #1024, .F. );
#540 = ADVANCED_FACE( '', ( #1025 ), #1026, .F. );
#541 = ADVANCED_FACE( '', ( #1027 ), #1028, .F. );
#542 = ADVANCED_FACE( '', ( #1029 ), #1030, .T. );
#543 = ADVANCED_FACE( '', ( #1031 ), #1032, .T. );
#544 = ADVANCED_FACE( '', ( #1033 ), #1034, .T. );
#545 = ADVANCED_FACE( '', ( #1035 ), #1036, .F. );
#546 = ADVANCED_FACE( '', ( #1037 ), #1038, .T. );
#547 = ADVANCED_FACE( '', ( #1039 ), #1040, .T. );
#548 = ADVANCED_FACE( '', ( #1041 ), #1042, .F. );
#549 = ADVANCED_FACE( '', ( #1043 ), #1044, .F. );
#550 = ADVANCED_FACE( '', ( #1045 ), #1046, .F. );
#551 = ADVANCED_FACE( '', ( #1047 ), #1048, .F. );
#552 = ADVANCED_FACE( '', ( #1049 ), #1050, .T. );
#553 = ADVANCED_FACE( '', ( #1051 ), #1052, .F. );
#554 = ADVANCED_FACE( '', ( #1053 ), #1054, .T. );
#555 = ADVANCED_FACE( '', ( #1055 ), #1056, .F. );
#556 = ADVANCED_FACE( '', ( #1057 ), #1058, .F. );
#557 = ADVANCED_FACE( '', ( #1059 ), #1060, .T. );
#558 = ADVANCED_FACE( '', ( #1061 ), #1062, .T. );
#559 = ADVANCED_FACE( '', ( #1063 ), #1064, .F. );
#560 = ADVANCED_FACE( '', ( #1065 ), #1066, .F. );
#561 = ADVANCED_FACE( '', ( #1067 ), #1068, .F. );
#562 = ADVANCED_FACE( '', ( #1069 ), #1070, .T. );
#563 = ADVANCED_FACE( '', ( #1071 ), #1072, .F. );
#564 = ADVANCED_FACE( '', ( #1073 ), #1074, .T. );
#565 = ADVANCED_FACE( '', ( #1075 ), #1076, .T. );
#566 = ADVANCED_FACE( '', ( #1077 ), #1078, .T. );
#567 = ADVANCED_FACE( '', ( #1079 ), #1080, .F. );
#568 = ADVANCED_FACE( '', ( #1081 ), #1082, .F. );
#569 = ADVANCED_FACE( '', ( #1083 ), #1084, .F. );
#570 = ADVANCED_FACE( '', ( #1085 ), #1086, .F. );
#571 = ADVANCED_FACE( '', ( #1087 ), #1088, .T. );
#572 = ADVANCED_FACE( '', ( #1089 ), #1090, .F. );
#573 = ADVANCED_FACE( '', ( #1091 ), #1092, .F. );
#574 = ADVANCED_FACE( '', ( #1093, #1094 ), #1095, .F. );
#575 = ADVANCED_FACE( '', ( #1096 ), #1097, .F. );
#576 = ADVANCED_FACE( '', ( #1098 ), #1099, .T. );
#577 = ADVANCED_FACE( '', ( #1100 ), #1101, .F. );
#578 = ADVANCED_FACE( '', ( #1102 ), #1103, .T. );
#579 = ADVANCED_FACE( '', ( #1104 ), #1105, .T. );
#580 = ADVANCED_FACE( '', ( #1106 ), #1107, .F. );
#581 = ADVANCED_FACE( '', ( #1108 ), #1109, .T. );
#582 = ADVANCED_FACE( '', ( #1110 ), #1111, .T. );
#583 = ADVANCED_FACE( '', ( #1112 ), #1113, .T. );
#584 = ADVANCED_FACE( '', ( #1114 ), #1115, .F. );
#585 = ADVANCED_FACE( '', ( #1116 ), #1117, .F. );
#586 = ADVANCED_FACE( '', ( #1118 ), #1119, .F. );
#587 = ADVANCED_FACE( '', ( #1120 ), #1121, .T. );
#588 = ADVANCED_FACE( '', ( #1122 ), #1123, .F. );
#589 = ADVANCED_FACE( '', ( #1124 ), #1125, .F. );
#591 = SURFACE_STYLE_FILL_AREA( #1126 );
#592 = ORIENTED_EDGE( '', *, *, #1127, .T. );
#593 = ORIENTED_EDGE( '', *, *, #1128, .F. );
#594 = ORIENTED_EDGE( '', *, *, #1129, .F. );
#595 = ORIENTED_EDGE( '', *, *, #1130, .T. );
#596 = CARTESIAN_POINT( '', ( -3.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#597 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#598 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#599 = SURFACE_STYLE_FILL_AREA( #1131 );
#600 = ORIENTED_EDGE( '', *, *, #1132, .F. );
#601 = ORIENTED_EDGE( '', *, *, #1133, .F. );
#602 = ORIENTED_EDGE( '', *, *, #1134, .F. );
#603 = ORIENTED_EDGE( '', *, *, #1135, .F. );
#604 = ORIENTED_EDGE( '', *, *, #1136, .T. );
#605 = ORIENTED_EDGE( '', *, *, #1137, .F. );
#606 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.70686168865201, 1.50000000000000 ) );
#607 = DIRECTION( '', ( 1.00000000000000, -0.000000000000000, -0.000000000000000 ) );
#608 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#609 = SURFACE_STYLE_FILL_AREA( #1138 );
#610 = ORIENTED_EDGE( '', *, *, #1139, .F. );
#611 = ORIENTED_EDGE( '', *, *, #1140, .F. );
#612 = ORIENTED_EDGE( '', *, *, #1141, .T. );
#613 = ORIENTED_EDGE( '', *, *, #1142, .T. );
#614 = CARTESIAN_POINT( '', ( 2.00000000000000, -0.500000000000000, 0.151000000000000 ) );
#615 = DIRECTION( '', ( -5.42101086242752E-016, 1.00000000000000, 0.000000000000000 ) );
#616 = DIRECTION( '', ( -1.00000000000000, -5.42101086242752E-016, 0.000000000000000 ) );
#617 = SURFACE_STYLE_FILL_AREA( #1143 );
#618 = ORIENTED_EDGE( '', *, *, #1133, .T. );
#619 = ORIENTED_EDGE( '', *, *, #1144, .T. );
#620 = ORIENTED_EDGE( '', *, *, #1145, .T. );
#621 = ORIENTED_EDGE( '', *, *, #1146, .F. );
#622 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.75000000000000, 0.775000000000000 ) );
#623 = DIRECTION( '', ( 1.00000000000000, -6.12303176911189E-017, 0.000000000000000 ) );
#624 = DIRECTION( '', ( 6.12303176911189E-017, 1.00000000000000, 0.000000000000000 ) );
#625 = SURFACE_STYLE_FILL_AREA( #1147 );
#626 = ORIENTED_EDGE( '', *, *, #1148, .T. );
#627 = ORIENTED_EDGE( '', *, *, #1149, .T. );
#628 = ORIENTED_EDGE( '', *, *, #1150, .T. );
#629 = ORIENTED_EDGE( '', *, *, #1151, .F. );
#630 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 0.775000000000000 ) );
#631 = DIRECTION( '', ( -1.00000000000000, 6.12303176911189E-017, 0.000000000000000 ) );
#632 = DIRECTION( '', ( -6.12303176911189E-017, -1.00000000000000, 0.000000000000000 ) );
#633 = SURFACE_STYLE_FILL_AREA( #1152 );
#634 = ORIENTED_EDGE( '', *, *, #1153, .T. );
#635 = ORIENTED_EDGE( '', *, *, #1154, .F. );
#636 = ORIENTED_EDGE( '', *, *, #1135, .T. );
#637 = ORIENTED_EDGE( '', *, *, #1155, .F. );
#638 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75000000000000, 0.775000000000000 ) );
#639 = DIRECTION( '', ( 1.00000000000000, -6.12303176911189E-017, 0.000000000000000 ) );
#640 = DIRECTION( '', ( 6.12303176911189E-017, 1.00000000000000, 0.000000000000000 ) );
#641 = SURFACE_STYLE_FILL_AREA( #1156 );
#642 = ORIENTED_EDGE( '', *, *, #1157, .F. );
#643 = ORIENTED_EDGE( '', *, *, #1158, .F. );
#644 = ORIENTED_EDGE( '', *, *, #1159, .T. );
#645 = ORIENTED_EDGE( '', *, *, #1140, .T. );
#646 = CARTESIAN_POINT( '', ( 2.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#647 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#648 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#649 = SURFACE_STYLE_FILL_AREA( #1160 );
#650 = ORIENTED_EDGE( '', *, *, #1161, .T. );
#651 = CARTESIAN_POINT( '', ( 0.000000000000000, 0.000000000000000, 2.50000000000000 ) );
#652 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#653 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#654 = SURFACE_STYLE_FILL_AREA( #1162 );
#655 = ORIENTED_EDGE( '', *, *, #1163, .T. );
#656 = ORIENTED_EDGE( '', *, *, #1164, .F. );
#657 = ORIENTED_EDGE( '', *, *, #1165, .T. );
#658 = ORIENTED_EDGE( '', *, *, #1166, .F. );
#659 = ORIENTED_EDGE( '', *, *, #1167, .T. );
#660 = ORIENTED_EDGE( '', *, *, #1168, .F. );
#661 = CARTESIAN_POINT( '', ( -1.07500000000000, -1.85000000000000, 7.47930478052606 ) );
#662 = DIRECTION( '', ( -0.707106781186548, -0.707106781186548, 0.000000000000000 ) );
#663 = DIRECTION( '', ( 0.707106781186548, -0.707106781186548, 0.000000000000000 ) );
#664 = SURFACE_STYLE_FILL_AREA( #1169 );
#665 = ORIENTED_EDGE( '', *, *, #1170, .F. );
#666 = ORIENTED_EDGE( '', *, *, #1171, .F. );
#667 = ORIENTED_EDGE( '', *, *, #1136, .F. );
#668 = ORIENTED_EDGE( '', *, *, #1154, .T. );
#669 = ORIENTED_EDGE( '', *, *, #1172, .F. );
#670 = ORIENTED_EDGE( '', *, *, #1173, .F. );
#671 = CARTESIAN_POINT( '', ( 1.07500000000000, 1.85000000000000, 7.47930478052606 ) );
#672 = DIRECTION( '', ( 0.707106781186548, 0.707106781186548, 0.000000000000000 ) );
#673 = DIRECTION( '', ( -0.707106781186548, 0.707106781186548, 0.000000000000000 ) );
#674 = SURFACE_STYLE_FILL_AREA( #1174 );
#675 = ORIENTED_EDGE( '', *, *, #1175, .F. );
#676 = ORIENTED_EDGE( '', *, *, #1176, .T. );
#677 = ORIENTED_EDGE( '', *, *, #1177, .T. );
#678 = ORIENTED_EDGE( '', *, *, #1165, .F. );
#679 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 1.55000000000000 ) );
#680 = DIRECTION( '', ( 0.000000000000000, 0.989203462353870, -0.146548661089465 ) );
#681 = DIRECTION( '', ( 0.000000000000000, 0.146548661089465, 0.989203462353870 ) );
#682 = SURFACE_STYLE_FILL_AREA( #1178 );
#683 = ORIENTED_EDGE( '', *, *, #1179, .F. );
#684 = ORIENTED_EDGE( '', *, *, #1180, .T. );
#685 = ORIENTED_EDGE( '', *, *, #1181, .T. );
#686 = ORIENTED_EDGE( '', *, *, #1163, .F. );
#687 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.85000000000000, 0.775000000000000 ) );
#688 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#689 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#690 = SURFACE_STYLE_FILL_AREA( #1182 );
#691 = ORIENTED_EDGE( '', *, *, #1183, .F. );
#692 = ORIENTED_EDGE( '', *, *, #1184, .T. );
#693 = ORIENTED_EDGE( '', *, *, #1185, .T. );
#694 = ORIENTED_EDGE( '', *, *, #1186, .T. );
#695 = ORIENTED_EDGE( '', *, *, #1142, .F. );
#696 = ORIENTED_EDGE( '', *, *, #1187, .T. );
#697 = ORIENTED_EDGE( '', *, *, #1188, .T. );
#698 = ORIENTED_EDGE( '', *, *, #1189, .T. );
#699 = ORIENTED_EDGE( '', *, *, #1190, .F. );
#700 = ORIENTED_EDGE( '', *, *, #1191, .T. );
#701 = CARTESIAN_POINT( '', ( 3.00000000000000, -1.75000000000000, 1.55000000000000 ) );
#702 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#703 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#704 = SURFACE_STYLE_FILL_AREA( #1192 );
#705 = ORIENTED_EDGE( '', *, *, #1193, .T. );
#706 = ORIENTED_EDGE( '', *, *, #1129, .T. );
#707 = ORIENTED_EDGE( '', *, *, #1194, .T. );
#708 = ORIENTED_EDGE( '', *, *, #1195, .T. );
#709 = CARTESIAN_POINT( '', ( -3.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#710 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#711 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#712 = SURFACE_STYLE_FILL_AREA( #1196 );
#713 = ORIENTED_EDGE( '', *, *, #1197, .T. );
#714 = ORIENTED_EDGE( '', *, *, #1167, .F. );
#715 = ORIENTED_EDGE( '', *, *, #1198, .T. );
#716 = ORIENTED_EDGE( '', *, *, #1199, .F. );
#717 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.75000000000000, 0.775000000000000 ) );
#718 = DIRECTION( '', ( -1.00000000000000, 6.12303176911189E-017, 0.000000000000000 ) );
#719 = DIRECTION( '', ( -6.12303176911189E-017, -1.00000000000000, 0.000000000000000 ) );
#720 = SURFACE_STYLE_FILL_AREA( #1200 );
#721 = ORIENTED_EDGE( '', *, *, #1201, .F. );
#722 = ORIENTED_EDGE( '', *, *, #1137, .T. );
#723 = ORIENTED_EDGE( '', *, *, #1171, .T. );
#724 = ORIENTED_EDGE( '', *, *, #1202, .T. );
#725 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75000000000000, 1.55000000000000 ) );
#726 = DIRECTION( '', ( 0.000000000000000, 0.989203462353870, 0.146548661089465 ) );
#727 = DIRECTION( '', ( 0.000000000000000, -0.146548661089465, 0.989203462353870 ) );
#728 = SURFACE_STYLE_FILL_AREA( #1203 );
#729 = ORIENTED_EDGE( '', *, *, #1161, .F. );
#730 = ORIENTED_EDGE( '', *, *, #1204, .F. );
#731 = CARTESIAN_POINT( '', ( 0.000000000000000, 0.000000000000000, 2.40000000000000 ) );
#732 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#733 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#734 = SURFACE_STYLE_FILL_AREA( #1205 );
#735 = ORIENTED_EDGE( '', *, *, #1191, .F. );
#736 = ORIENTED_EDGE( '', *, *, #1206, .F. );
#737 = ORIENTED_EDGE( '', *, *, #1153, .F. );
#738 = ORIENTED_EDGE( '', *, *, #1207, .F. );
#739 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.70000000000000, 1.50000000000000 ) );
#740 = DIRECTION( '', ( -1.00000000000000, 7.22801448323670E-017, -0.000000000000000 ) );
#741 = DIRECTION( '', ( 7.22801448323670E-017, 1.00000000000000, 0.000000000000000 ) );
#742 = SURFACE_STYLE_FILL_AREA( #1208 );
#743 = ORIENTED_EDGE( '', *, *, #1209, .F. );
#744 = ORIENTED_EDGE( '', *, *, #1210, .F. );
#745 = ORIENTED_EDGE( '', *, *, #1172, .T. );
#746 = ORIENTED_EDGE( '', *, *, #1206, .T. );
#747 = ORIENTED_EDGE( '', *, *, #1190, .T. );
#748 = ORIENTED_EDGE( '', *, *, #1211, .T. );
#749 = ORIENTED_EDGE( '', *, *, #1212, .F. );
#750 = ORIENTED_EDGE( '', *, *, #1213, .T. );
#751 = CARTESIAN_POINT( '', ( 3.00000000000000, 1.75000000000000, 1.55000000000000 ) );
#752 = DIRECTION( '', ( -7.22801448323670E-017, -1.00000000000000, 0.000000000000000 ) );
#753 = DIRECTION( '', ( 1.00000000000000, -7.22801448323670E-017, 0.000000000000000 ) );
#754 = SURFACE_STYLE_FILL_AREA( #1214 );
#755 = ORIENTED_EDGE( '', *, *, #1204, .T. );
#756 = ORIENTED_EDGE( '', *, *, #1215, .F. );
#757 = CARTESIAN_POINT( '', ( 0.000000000000000, 0.000000000000000, -4.97930478052606 ) );
#758 = DIRECTION( '', ( -0.000000000000000, -0.000000000000000, -1.00000000000000 ) );
#759 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#760 = SURFACE_STYLE_FILL_AREA( #1216 );
#761 = ORIENTED_EDGE( '', *, *, #1145, .F. );
#762 = ORIENTED_EDGE( '', *, *, #1213, .F. );
#763 = ORIENTED_EDGE( '', *, *, #1217, .F. );
#764 = ORIENTED_EDGE( '', *, *, #1218, .F. );
#765 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.70000000000000, 1.50000000000000 ) );
#766 = DIRECTION( '', ( -1.00000000000000, 7.22801448323670E-017, -0.000000000000000 ) );
#767 = DIRECTION( '', ( 7.22801448323670E-017, 1.00000000000000, 0.000000000000000 ) );
#768 = SURFACE_STYLE_FILL_AREA( #1219 );
#769 = ORIENTED_EDGE( '', *, *, #1220, .F. );
#770 = ORIENTED_EDGE( '', *, *, #1202, .F. );
#771 = ORIENTED_EDGE( '', *, *, #1170, .T. );
#772 = ORIENTED_EDGE( '', *, *, #1221, .T. );
#773 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.85000000000000, 0.875000000000000 ) );
#774 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, -4.33680868994197E-015 ) );
#775 = DIRECTION( '', ( 0.000000000000000, 4.33680868994197E-015, 1.00000000000000 ) );
#776 = SURFACE_STYLE_FILL_AREA( #1222 );
#777 = ORIENTED_EDGE( '', *, *, #1223, .F. );
#778 = ORIENTED_EDGE( '', *, *, #1221, .F. );
#779 = ORIENTED_EDGE( '', *, *, #1173, .T. );
#780 = ORIENTED_EDGE( '', *, *, #1210, .T. );
#781 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.85000000000000, 0.775000000000000 ) );
#782 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#783 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#784 = SURFACE_STYLE_FILL_AREA( #1224 );
#785 = ORIENTED_EDGE( '', *, *, #1225, .F. );
#786 = ORIENTED_EDGE( '', *, *, #1226, .F. );
#787 = ORIENTED_EDGE( '', *, *, #1197, .F. );
#788 = ORIENTED_EDGE( '', *, *, #1227, .F. );
#789 = CARTESIAN_POINT( '', ( -3.00000000000000, -1.70000000000000, 1.50000000000000 ) );
#790 = DIRECTION( '', ( 1.00000000000000, 7.22801448323670E-017, -0.000000000000000 ) );
#791 = DIRECTION( '', ( 7.22801448323670E-017, -1.00000000000000, 0.000000000000000 ) );
#792 = SURFACE_STYLE_FILL_AREA( #1228 );
#793 = ORIENTED_EDGE( '', *, *, #1199, .T. );
#794 = ORIENTED_EDGE( '', *, *, #1229, .T. );
#795 = ORIENTED_EDGE( '', *, *, #1151, .T. );
#796 = ORIENTED_EDGE( '', *, *, #1230, .T. );
#797 = ORIENTED_EDGE( '', *, *, #1183, .T. );
#798 = ORIENTED_EDGE( '', *, *, #1207, .T. );
#799 = ORIENTED_EDGE( '', *, *, #1155, .T. );
#800 = ORIENTED_EDGE( '', *, *, #1134, .T. );
#801 = ORIENTED_EDGE( '', *, *, #1146, .T. );
#802 = ORIENTED_EDGE( '', *, *, #1218, .T. );
#803 = ORIENTED_EDGE( '', *, *, #1231, .T. );
#804 = ORIENTED_EDGE( '', *, *, #1227, .T. );
#805 = ORIENTED_EDGE( '', *, *, #1232, .T. );
#806 = ORIENTED_EDGE( '', *, *, #1233, .T. );
#807 = ORIENTED_EDGE( '', *, *, #1234, .T. );
#808 = ORIENTED_EDGE( '', *, *, #1235, .T. );
#809 = CARTESIAN_POINT( '', ( -3.00000000000000, -1.75000000000000, 1.55000000000000 ) );
#810 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#811 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#812 = SURFACE_STYLE_FILL_AREA( #1236 );
#813 = ORIENTED_EDGE( '', *, *, #1231, .F. );
#814 = ORIENTED_EDGE( '', *, *, #1217, .T. );
#815 = ORIENTED_EDGE( '', *, *, #1212, .T. );
#816 = ORIENTED_EDGE( '', *, *, #1237, .T. );
#817 = ORIENTED_EDGE( '', *, *, #1130, .F. );
#818 = ORIENTED_EDGE( '', *, *, #1193, .F. );
#819 = ORIENTED_EDGE( '', *, *, #1238, .T. );
#820 = ORIENTED_EDGE( '', *, *, #1239, .T. );
#821 = ORIENTED_EDGE( '', *, *, #1240, .F. );
#822 = ORIENTED_EDGE( '', *, *, #1225, .T. );
#823 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.75000000000000, 1.55000000000000 ) );
#824 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#825 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#826 = SURFACE_STYLE_FILL_AREA( #1241 );
#827 = ORIENTED_EDGE( '', *, *, #1150, .F. );
#828 = ORIENTED_EDGE( '', *, *, #1242, .F. );
#829 = ORIENTED_EDGE( '', *, *, #1184, .F. );
#830 = ORIENTED_EDGE( '', *, *, #1230, .F. );
#831 = CARTESIAN_POINT( '', ( -3.00000000000000, -1.70000000000000, 1.50000000000000 ) );
#832 = DIRECTION( '', ( 1.00000000000000, 7.22801448323670E-017, -0.000000000000000 ) );
#833 = DIRECTION( '', ( 7.22801448323670E-017, -1.00000000000000, 0.000000000000000 ) );
#834 = SURFACE_STYLE_FILL_AREA( #1243 );
#835 = ORIENTED_EDGE( '', *, *, #1244, .F. );
#836 = ORIENTED_EDGE( '', *, *, #1127, .F. );
#837 = ORIENTED_EDGE( '', *, *, #1237, .F. );
#838 = ORIENTED_EDGE( '', *, *, #1211, .F. );
#839 = ORIENTED_EDGE( '', *, *, #1189, .F. );
#840 = ORIENTED_EDGE( '', *, *, #1245, .T. );
#841 = ORIENTED_EDGE( '', *, *, #1157, .T. );
#842 = ORIENTED_EDGE( '', *, *, #1139, .T. );
#843 = ORIENTED_EDGE( '', *, *, #1186, .F. );
#844 = ORIENTED_EDGE( '', *, *, #1246, .F. );
#845 = ORIENTED_EDGE( '', *, *, #1239, .F. );
#846 = ORIENTED_EDGE( '', *, *, #1247, .F. );
#847 = CARTESIAN_POINT( '', ( -3.00000000000000, -1.75000000000000, 0.000000000000000 ) );
#848 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#849 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#850 = SURFACE_STYLE_FILL_AREA( #1248 );
#851 = ORIENTED_EDGE( '', *, *, #1245, .F. );
#852 = ORIENTED_EDGE( '', *, *, #1188, .F. );
#853 = ORIENTED_EDGE( '', *, *, #1249, .T. );
#854 = ORIENTED_EDGE( '', *, *, #1158, .T. );
#855 = CARTESIAN_POINT( '', ( 3.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#856 = DIRECTION( '', ( -5.42101086242752E-016, -1.00000000000000, 0.000000000000000 ) );
#857 = DIRECTION( '', ( 1.00000000000000, -5.42101086242752E-016, 0.000000000000000 ) );
#858 = SURFACE_STYLE_FILL_AREA( #1250 );
#859 = ORIENTED_EDGE( '', *, *, #1251, .T. );
#860 = ORIENTED_EDGE( '', *, *, #1252, .T. );
#861 = ORIENTED_EDGE( '', *, *, #1253, .T. );
#862 = ORIENTED_EDGE( '', *, *, #1254, .T. );
#863 = ORIENTED_EDGE( '', *, *, #1215, .T. );
#864 = CARTESIAN_POINT( '', ( -2.05000000000000, 1.50000000000000, 0.775000000000000 ) );
#865 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#866 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#867 = SURFACE_STYLE_FILL_AREA( #1255 );
#868 = ORIENTED_EDGE( '', *, *, #1256, .T. );
#869 = ORIENTED_EDGE( '', *, *, #1252, .F. );
#870 = ORIENTED_EDGE( '', *, *, #1257, .F. );
#871 = ORIENTED_EDGE( '', *, *, #1232, .F. );
#872 = CARTESIAN_POINT( '', ( -2.05000000000000, 1.50000000000000, 7.89205697602598 ) );
#873 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#874 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#875 = SURFACE_STYLE_FILL_AREA( #1258 );
#876 = ORIENTED_EDGE( '', *, *, #1187, .F. );
#877 = ORIENTED_EDGE( '', *, *, #1141, .F. );
#878 = ORIENTED_EDGE( '', *, *, #1159, .F. );
#879 = ORIENTED_EDGE( '', *, *, #1249, .F. );
#880 = CARTESIAN_POINT( '', ( 3.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#881 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#882 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#883 = SURFACE_STYLE_FILL_AREA( #1259 );
#884 = ORIENTED_EDGE( '', *, *, #1181, .F. );
#885 = ORIENTED_EDGE( '', *, *, #1260, .F. );
#886 = ORIENTED_EDGE( '', *, *, #1175, .T. );
#887 = ORIENTED_EDGE( '', *, *, #1164, .T. );
#888 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.85000000000000, 0.875000000000000 ) );
#889 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 4.33680868994197E-015 ) );
#890 = DIRECTION( '', ( 0.000000000000000, -4.33680868994197E-015, 1.00000000000000 ) );
#891 = SURFACE_STYLE_FILL_AREA( #1261 );
#892 = ORIENTED_EDGE( '', *, *, #1257, .T. );
#893 = ORIENTED_EDGE( '', *, *, #1251, .F. );
#894 = ORIENTED_EDGE( '', *, *, #1262, .F. );
#895 = ORIENTED_EDGE( '', *, *, #1233, .F. );
#896 = CARTESIAN_POINT( '', ( 2.05000000000000, 1.50000000000000, 7.89205697602598 ) );
#897 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#898 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#899 = SURFACE_STYLE_FILL_AREA( #1263 );
#900 = ORIENTED_EDGE( '', *, *, #1264, .T. );
#901 = ORIENTED_EDGE( '', *, *, #1149, .F. );
#902 = ORIENTED_EDGE( '', *, *, #1265, .T. );
#903 = ORIENTED_EDGE( '', *, *, #1176, .F. );
#904 = ORIENTED_EDGE( '', *, *, #1260, .T. );
#905 = ORIENTED_EDGE( '', *, *, #1180, .F. );
#906 = CARTESIAN_POINT( '', ( 1.07500000000000, -1.85000000000000, 7.47930478052606 ) );
#907 = DIRECTION( '', ( -0.707106781186548, 0.707106781186548, 0.000000000000000 ) );
#908 = DIRECTION( '', ( -0.707106781186548, -0.707106781186548, 0.000000000000000 ) );
#909 = SURFACE_STYLE_FILL_AREA( #1266 );
#910 = ORIENTED_EDGE( '', *, *, #1265, .F. );
#911 = ORIENTED_EDGE( '', *, *, #1148, .F. );
#912 = ORIENTED_EDGE( '', *, *, #1229, .F. );
#913 = ORIENTED_EDGE( '', *, *, #1198, .F. );
#914 = ORIENTED_EDGE( '', *, *, #1166, .T. );
#915 = ORIENTED_EDGE( '', *, *, #1177, .F. );
#916 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70686168865201, 1.50000000000000 ) );
#917 = DIRECTION( '', ( -1.00000000000000, -0.000000000000000, -0.000000000000000 ) );
#918 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#919 = SURFACE_STYLE_FILL_AREA( #1267 );
#920 = ORIENTED_EDGE( '', *, *, #1262, .T. );
#921 = ORIENTED_EDGE( '', *, *, #1254, .F. );
#922 = ORIENTED_EDGE( '', *, *, #1268, .F. );
#923 = ORIENTED_EDGE( '', *, *, #1234, .F. );
#924 = CARTESIAN_POINT( '', ( 2.05000000000000, -1.50000000000000, 7.89205697602598 ) );
#925 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#926 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#927 = SURFACE_STYLE_FILL_AREA( #1269 );
#928 = ORIENTED_EDGE( '', *, *, #1223, .T. );
#929 = ORIENTED_EDGE( '', *, *, #1209, .T. );
#930 = ORIENTED_EDGE( '', *, *, #1144, .F. );
#931 = ORIENTED_EDGE( '', *, *, #1132, .T. );
#932 = ORIENTED_EDGE( '', *, *, #1201, .T. );
#933 = ORIENTED_EDGE( '', *, *, #1220, .T. );
#934 = CARTESIAN_POINT( '', ( -1.07500000000000, 1.85000000000000, 7.47930478052606 ) );
#935 = DIRECTION( '', ( 0.707106781186548, -0.707106781186548, 0.000000000000000 ) );
#936 = DIRECTION( '', ( 0.707106781186548, 0.707106781186548, 0.000000000000000 ) );
#937 = SURFACE_STYLE_FILL_AREA( #1270 );
#938 = ORIENTED_EDGE( '', *, *, #1264, .F. );
#939 = ORIENTED_EDGE( '', *, *, #1179, .T. );
#940 = ORIENTED_EDGE( '', *, *, #1168, .T. );
#941 = ORIENTED_EDGE( '', *, *, #1226, .T. );
#942 = ORIENTED_EDGE( '', *, *, #1240, .T. );
#943 = ORIENTED_EDGE( '', *, *, #1246, .T. );
#944 = ORIENTED_EDGE( '', *, *, #1185, .F. );
#945 = ORIENTED_EDGE( '', *, *, #1242, .T. );
#946 = CARTESIAN_POINT( '', ( -3.00000000000000, -1.75000000000000, 1.55000000000000 ) );
#947 = DIRECTION( '', ( -7.22801448323670E-017, 1.00000000000000, 0.000000000000000 ) );
#948 = DIRECTION( '', ( -1.00000000000000, -7.22801448323670E-017, 0.000000000000000 ) );
#949 = SURFACE_STYLE_FILL_AREA( #1271 );
#950 = ORIENTED_EDGE( '', *, *, #1268, .T. );
#951 = ORIENTED_EDGE( '', *, *, #1253, .F. );
#952 = ORIENTED_EDGE( '', *, *, #1256, .F. );
#953 = ORIENTED_EDGE( '', *, *, #1235, .F. );
#954 = CARTESIAN_POINT( '', ( -2.05000000000000, -1.50000000000000, 7.89205697602598 ) );
#955 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#956 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#957 = SURFACE_STYLE_FILL_AREA( #1272 );
#958 = ORIENTED_EDGE( '', *, *, #1247, .T. );
#959 = ORIENTED_EDGE( '', *, *, #1238, .F. );
#960 = ORIENTED_EDGE( '', *, *, #1195, .F. );
#961 = ORIENTED_EDGE( '', *, *, #1273, .T. );
#962 = CARTESIAN_POINT( '', ( -2.00000000000000, -0.500000000000000, 0.151000000000000 ) );
#963 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#964 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#965 = SURFACE_STYLE_FILL_AREA( #1274 );
#966 = ORIENTED_EDGE( '', *, *, #1244, .T. );
#967 = ORIENTED_EDGE( '', *, *, #1273, .F. );
#968 = ORIENTED_EDGE( '', *, *, #1194, .F. );
#969 = ORIENTED_EDGE( '', *, *, #1128, .T. );
#970 = CARTESIAN_POINT( '', ( -2.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#971 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#972 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#973 = FACE_OUTER_BOUND( '', #1275, .T. );
#974 = PLANE( '', #1276 );
#975 = FACE_OUTER_BOUND( '', #1277, .T. );
#976 = PLANE( '', #1278 );
#977 = FACE_BOUND( '', #1279, .T. );
#978 = FACE_OUTER_BOUND( '', #1280, .T. );
#979 = PLANE( '', #1281 );
#980 = FACE_OUTER_BOUND( '', #1282, .T. );
#981 = CYLINDRICAL_SURFACE( '', #1283, 0.200000000000000 );
#982 = FACE_OUTER_BOUND( '', #1284, .T. );
#983 = PLANE( '', #1285 );
#984 = FACE_OUTER_BOUND( '', #1286, .T. );
#985 = PLANE( '', #1287 );
#986 = FACE_OUTER_BOUND( '', #1288, .T. );
#987 = PLANE( '', #1289 );
#988 = FACE_OUTER_BOUND( '', #1290, .T. );
#989 = PLANE( '', #1291 );
#990 = FACE_OUTER_BOUND( '', #1292, .T. );
#991 = B_SPLINE_SURFACE_WITH_KNOTS( '', 1, 3, ( ( #1293, #1294, #1295, #1296, #1297, #1298, #1299 ), ( #1300, #1301, #1302, #1303, #1304, #1305, #1306 ) ), .UNSPECIFIED., .F., .F., .F., ( 2, 2 ), ( 4, 3, 4 ), ( 0.000000000000000, 1.00000000000000 ), ( -0.0990735538218553, 0.000000000000000, 1.00000000000000 ), .UNSPECIFIED. );
#992 = FACE_OUTER_BOUND( '', #1307, .T. );
#993 = CYLINDRICAL_SURFACE( '', #1308, 0.100000000000000 );
#994 = FACE_OUTER_BOUND( '', #1309, .T. );
#995 = PLANE( '', #1310 );
#996 = FACE_OUTER_BOUND( '', #1311, .T. );
#997 = CYLINDRICAL_SURFACE( '', #1312, 0.0500000000000000 );
#998 = FACE_OUTER_BOUND( '', #1313, .T. );
#999 = CYLINDRICAL_SURFACE( '', #1314, 0.100000000000000 );
#1000 = FACE_OUTER_BOUND( '', #1315, .T. );
#1001 = PLANE( '', #1316 );
#1002 = FACE_OUTER_BOUND( '', #1317, .T. );
#1003 = PLANE( '', #1318 );
#1004 = FACE_OUTER_BOUND( '', #1319, .T. );
#1005 = PLANE( '', #1320 );
#1006 = FACE_OUTER_BOUND( '', #1321, .T. );
#1007 = CYLINDRICAL_SURFACE( '', #1322, 0.0500000000000000 );
#1008 = FACE_BOUND( '', #1323, .T. );
#1009 = FACE_OUTER_BOUND( '', #1324, .T. );
#1010 = PLANE( '', #1325 );
#1011 = FACE_OUTER_BOUND( '', #1326, .T. );
#1012 = CYLINDRICAL_SURFACE( '', #1327, 0.0500000000000000 );
#1013 = FACE_OUTER_BOUND( '', #1328, .T. );
#1014 = CYLINDRICAL_SURFACE( '', #1329, 0.100000000000000 );
#1015 = FACE_OUTER_BOUND( '', #1330, .T. );
#1016 = PLANE( '', #1331 );
#1017 = FACE_OUTER_BOUND( '', #1332, .T. );
#1018 = B_SPLINE_SURFACE_WITH_KNOTS( '', 1, 3, ( ( #1333, #1334, #1335, #1336, #1337, #1338, #1339 ), ( #1340, #1341, #1342, #1343, #1344, #1345, #1346 ) ), .UNSPECIFIED., .F., .F., .F., ( 2, 2 ), ( 4, 3, 4 ), ( 0.000000000000000, 1.00000000000000 ), ( -0.0990735538231188, 0.000000000000000, 1.00000000000000 ), .UNSPECIFIED. );
#1019 = FACE_OUTER_BOUND( '', #1347, .T. );
#1020 = CYLINDRICAL_SURFACE( '', #1348, 0.200000000000000 );
#1021 = FACE_OUTER_BOUND( '', #1349, .T. );
#1022 = CYLINDRICAL_SURFACE( '', #1350, 0.200000000000000 );
#1023 = FACE_OUTER_BOUND( '', #1351, .T. );
#1024 = CYLINDRICAL_SURFACE( '', #1352, 0.0500000000000000 );
#1025 = FACE_OUTER_BOUND( '', #1353, .T. );
#1026 = PLANE( '', #1354 );
#1027 = FACE_OUTER_BOUND( '', #1355, .T. );
#1028 = B_SPLINE_SURFACE_WITH_KNOTS( '', 1, 3, ( ( #1356, #1357, #1358, #1359, #1360, #1361, #1362 ), ( #1363, #1364, #1365, #1366, #1367, #1368, #1369 ) ), .UNSPECIFIED., .F., .F., .F., ( 2, 2 ), ( 4, 3, 4 ), ( 0.000000000000000, 1.00000000000000 ), ( -0.122338689074076, 0.000000000000000, 1.00000000000000 ), .UNSPECIFIED. );
#1029 = FACE_OUTER_BOUND( '', #1370, .T. );
#1030 = CYLINDRICAL_SURFACE( '', #1371, 0.200000000000000 );
#1031 = FACE_OUTER_BOUND( '', #1372, .T. );
#1032 = PLANE( '', #1373 );
#1033 = FACE_OUTER_BOUND( '', #1374, .T. );
#1034 = PLANE( '', #1375 );
#1035 = FACE_OUTER_BOUND( '', #1376, .T. );
#1036 = PLANE( '', #1377 );
#1037 = FACE_OUTER_BOUND( '', #1378, .T. );
#1038 = CYLINDRICAL_SURFACE( '', #1379, 0.200000000000000 );
#1039 = FACE_OUTER_BOUND( '', #1380, .T. );
#1040 = PLANE( '', #1381 );
#1041 = FACE_OUTER_BOUND( '', #1382, .T. );
#1042 = PLANE( '', #1383 );
#1043 = FACE_OUTER_BOUND( '', #1384, .T. );
#1044 = CYLINDRICAL_SURFACE( '', #1385, 0.100000000000000 );
#1045 = FACE_OUTER_BOUND( '', #1386, .T. );
#1046 = PLANE( '', #1387 );
#1047 = FACE_OUTER_BOUND( '', #1388, .T. );
#1048 = PLANE( '', #1389 );
#1049 = FACE_OUTER_BOUND( '', #1390, .T. );
#1050 = PLANE( '', #1391 );
#1051 = FACE_OUTER_BOUND( '', #1392, .T. );
#1052 = CYLINDRICAL_SURFACE( '', #1393, 0.100000000000000 );
#1053 = FACE_OUTER_BOUND( '', #1394, .T. );
#1054 = PLANE( '', #1395 );
#1055 = FACE_OUTER_BOUND( '', #1396, .T. );
#1056 = CYLINDRICAL_SURFACE( '', #1397, 0.0500000000000000 );
#1057 = FACE_OUTER_BOUND( '', #1398, .T. );
#1058 = CYLINDRICAL_SURFACE( '', #1399, 0.0500000000000000 );
#1059 = FACE_OUTER_BOUND( '', #1400, .T. );
#1060 = PLANE( '', #1401 );
#1061 = FACE_OUTER_BOUND( '', #1402, .T. );
#1062 = CYLINDRICAL_SURFACE( '', #1403, 0.200000000000000 );
#1063 = FACE_OUTER_BOUND( '', #1404, .T. );
#1064 = PLANE( '', #1405 );
#1065 = FACE_OUTER_BOUND( '', #1406, .T. );
#1066 = CYLINDRICAL_SURFACE( '', #1407, 0.0500000000000000 );
#1067 = FACE_OUTER_BOUND( '', #1408, .T. );
#1068 = B_SPLINE_SURFACE_WITH_KNOTS( '', 1, 3, ( ( #1409, #1410, #1411, #1412, #1413, #1414, #1415 ), ( #1416, #1417, #1418, #1419, #1420, #1421, #1422 ) ), .UNSPECIFIED., .F., .F., .F., ( 2, 2 ), ( 4, 3, 4 ), ( 0.000000000000000, 1.00000000000000 ), ( -0.122338689074621, 0.000000000000000, 1.00000000000000 ), .UNSPECIFIED. );
#1069 = FACE_OUTER_BOUND( '', #1423, .T. );
#1070 = CYLINDRICAL_SURFACE( '', #1424, 0.200000000000000 );
#1071 = FACE_OUTER_BOUND( '', #1425, .T. );
#1072 = CYLINDRICAL_SURFACE( '', #1426, 0.100000000000000 );
#1073 = FACE_OUTER_BOUND( '', #1427, .T. );
#1074 = PLANE( '', #1428 );
#1075 = FACE_OUTER_BOUND( '', #1429, .T. );
#1076 = PLANE( '', #1430 );
#1077 = FACE_OUTER_BOUND( '', #1431, .T. );
#1078 = PLANE( '', #1432 );
#1079 = FACE_OUTER_BOUND( '', #1433, .T. );
#1080 = CYLINDRICAL_SURFACE( '', #1434, 0.100000000000000 );
#1081 = FACE_OUTER_BOUND( '', #1435, .T. );
#1082 = PLANE( '', #1436 );
#1083 = FACE_OUTER_BOUND( '', #1437, .T. );
#1084 = CYLINDRICAL_SURFACE( '', #1438, 0.100000000000000 );
#1085 = FACE_OUTER_BOUND( '', #1439, .T. );
#1086 = PLANE( '', #1440 );
#1087 = FACE_OUTER_BOUND( '', #1441, .T. );
#1088 = CYLINDRICAL_SURFACE( '', #1442, 0.200000000000000 );
#1089 = FACE_OUTER_BOUND( '', #1443, .T. );
#1090 = PLANE( '', #1444 );
#1091 = FACE_OUTER_BOUND( '', #1445, .T. );
#1092 = PLANE( '', #1446 );
#1093 = FACE_OUTER_BOUND( '', #1447, .T. );
#1094 = FACE_OUTER_BOUND( '', #1448, .T. );
#1095 = CYLINDRICAL_SURFACE( '', #1449, 1.02500000000000 );
#1096 = FACE_OUTER_BOUND( '', #1450, .T. );
#1097 = PLANE( '', #1451 );
#1098 = FACE_OUTER_BOUND( '', #1452, .T. );
#1099 = PLANE( '', #1453 );
#1100 = FACE_OUTER_BOUND( '', #1454, .T. );
#1101 = CYLINDRICAL_SURFACE( '', #1455, 0.0500000000000000 );
#1102 = FACE_OUTER_BOUND( '', #1456, .T. );
#1103 = PLANE( '', #1457 );
#1104 = FACE_OUTER_BOUND( '', #1458, .T. );
#1105 = PLANE( '', #1459 );
#1106 = FACE_OUTER_BOUND( '', #1460, .T. );
#1107 = PLANE( '', #1461 );
#1108 = FACE_OUTER_BOUND( '', #1462, .T. );
#1109 = PLANE( '', #1463 );
#1110 = FACE_OUTER_BOUND( '', #1464, .T. );
#1111 = PLANE( '', #1465 );
#1112 = FACE_OUTER_BOUND( '', #1466, .T. );
#1113 = PLANE( '', #1467 );
#1114 = FACE_OUTER_BOUND( '', #1468, .T. );
#1115 = PLANE( '', #1469 );
#1116 = FACE_OUTER_BOUND( '', #1470, .T. );
#1117 = PLANE( '', #1471 );
#1118 = FACE_OUTER_BOUND( '', #1472, .T. );
#1119 = PLANE( '', #1473 );
#1120 = FACE_OUTER_BOUND( '', #1474, .T. );
#1121 = PLANE( '', #1475 );
#1122 = FACE_OUTER_BOUND( '', #1476, .T. );
#1123 = PLANE( '', #1477 );
#1124 = FACE_OUTER_BOUND( '', #1478, .T. );
#1125 = PLANE( '', #1479 );
#1126 = FILL_AREA_STYLE( '', ( #1480 ) );
#1127 = EDGE_CURVE( '', #1481, #1482, #1483, .T. );
#1128 = EDGE_CURVE( '', #1484, #1482, #1485, .T. );
#1129 = EDGE_CURVE( '', #1486, #1484, #1487, .T. );
#1130 = EDGE_CURVE( '', #1486, #1481, #1488, .T. );
#1131 = FILL_AREA_STYLE( '', ( #1489 ) );
#1132 = EDGE_CURVE( '', #1490, #1491, #1492, .F. );
#1133 = EDGE_CURVE( '', #1493, #1490, #1494, .F. );
#1134 = EDGE_CURVE( '', #1495, #1493, #1496, .T. );
#1135 = EDGE_CURVE( '', #1497, #1495, #1498, .T. );
#1136 = EDGE_CURVE( '', #1497, #1499, #1500, .F. );
#1137 = EDGE_CURVE( '', #1491, #1499, #1501, .F. );
#1138 = FILL_AREA_STYLE( '', ( #1502 ) );
#1139 = EDGE_CURVE( '', #1503, #1504, #1505, .T. );
#1140 = EDGE_CURVE( '', #1506, #1503, #1507, .T. );
#1141 = EDGE_CURVE( '', #1506, #1508, #1509, .T. );
#1142 = EDGE_CURVE( '', #1508, #1504, #1510, .T. );
#1143 = FILL_AREA_STYLE( '', ( #1511 ) );
#1144 = EDGE_CURVE( '', #1490, #1512, #1513, .T. );
#1145 = EDGE_CURVE( '', #1512, #1514, #1515, .T. );
#1146 = EDGE_CURVE( '', #1493, #1514, #1516, .T. );
#1147 = FILL_AREA_STYLE( '', ( #1517 ) );
#1148 = EDGE_CURVE( '', #1518, #1519, #1520, .F. );
#1149 = EDGE_CURVE( '', #1519, #1521, #1522, .T. );
#1150 = EDGE_CURVE( '', #1521, #1523, #1524, .T. );
#1151 = EDGE_CURVE( '', #1518, #1523, #1525, .T. );
#1152 = FILL_AREA_STYLE( '', ( #1526 ) );
#1153 = EDGE_CURVE( '', #1527, #1528, #1529, .F. );
#1154 = EDGE_CURVE( '', #1497, #1528, #1530, .T. );
#1155 = EDGE_CURVE( '', #1527, #1495, #1531, .T. );
#1156 = FILL_AREA_STYLE( '', ( #1532 ) );
#1157 = EDGE_CURVE( '', #1533, #1503, #1534, .T. );
#1158 = EDGE_CURVE( '', #1535, #1533, #1536, .T. );
#1159 = EDGE_CURVE( '', #1535, #1506, #1537, .T. );
#1160 = FILL_AREA_STYLE( '', ( #1538 ) );
#1161 = EDGE_CURVE( '', #1539, #1539, #1540, .F. );
#1162 = FILL_AREA_STYLE( '', ( #1541 ) );
#1163 = EDGE_CURVE( '', #1542, #1543, #1544, .T. );
#1164 = EDGE_CURVE( '', #1545, #1543, #1546, .T. );
#1165 = EDGE_CURVE( '', #1545, #1547, #1548, .T. );
#1166 = EDGE_CURVE( '', #1549, #1547, #1550, .F. );
#1167 = EDGE_CURVE( '', #1549, #1551, #1552, .T. );
#1168 = EDGE_CURVE( '', #1542, #1551, #1553, .T. );
#1169 = FILL_AREA_STYLE( '', ( #1554 ) );
#1170 = EDGE_CURVE( '', #1555, #1556, #1557, .T. );
#1171 = EDGE_CURVE( '', #1499, #1555, #1558, .T. );
#1172 = EDGE_CURVE( '', #1559, #1528, #1560, .T. );
#1173 = EDGE_CURVE( '', #1556, #1559, #1561, .T. );
#1174 = FILL_AREA_STYLE( '', ( #1562 ) );
#1175 = EDGE_CURVE( '', #1563, #1545, #1564, .T. );
#1176 = EDGE_CURVE( '', #1563, #1565, #1566, .T. );
#1177 = EDGE_CURVE( '', #1565, #1547, #1567, .T. );
#1178 = FILL_AREA_STYLE( '', ( #1568 ) );
#1179 = EDGE_CURVE( '', #1569, #1542, #1570, .T. );
#1180 = EDGE_CURVE( '', #1569, #1571, #1572, .T. );
#1181 = EDGE_CURVE( '', #1571, #1543, #1573, .T. );
#1182 = FILL_AREA_STYLE( '', ( #1574 ) );
#1183 = EDGE_CURVE( '', #1575, #1576, #1577, .T. );
#1184 = EDGE_CURVE( '', #1575, #1578, #1579, .F. );
#1185 = EDGE_CURVE( '', #1578, #1580, #1581, .T. );
#1186 = EDGE_CURVE( '', #1580, #1504, #1582, .T. );
#1187 = EDGE_CURVE( '', #1508, #1583, #1584, .T. );
#1188 = EDGE_CURVE( '', #1583, #1585, #1586, .T. );
#1189 = EDGE_CURVE( '', #1585, #1587, #1588, .T. );
#1190 = EDGE_CURVE( '', #1589, #1587, #1590, .T. );
#1191 = EDGE_CURVE( '', #1589, #1576, #1591, .F. );
#1192 = FILL_AREA_STYLE( '', ( #1592 ) );
#1193 = EDGE_CURVE( '', #1593, #1486, #1594, .T. );
#1194 = EDGE_CURVE( '', #1484, #1595, #1596, .T. );
#1195 = EDGE_CURVE( '', #1595, #1593, #1597, .T. );
#1196 = FILL_AREA_STYLE( '', ( #1598 ) );
#1197 = EDGE_CURVE( '', #1599, #1551, #1600, .F. );
#1198 = EDGE_CURVE( '', #1549, #1601, #1602, .T. );
#1199 = EDGE_CURVE( '', #1599, #1601, #1603, .T. );
#1200 = FILL_AREA_STYLE( '', ( #1604 ) );
#1201 = EDGE_CURVE( '', #1491, #1605, #1606, .T. );
#1202 = EDGE_CURVE( '', #1555, #1605, #1607, .T. );
#1203 = FILL_AREA_STYLE( '', ( #1608 ) );
#1204 = EDGE_CURVE( '', #1609, #1609, #1610, .T. );
#1205 = FILL_AREA_STYLE( '', ( #1611 ) );
#1206 = EDGE_CURVE( '', #1528, #1589, #1612, .F. );
#1207 = EDGE_CURVE( '', #1576, #1527, #1613, .T. );
#1208 = FILL_AREA_STYLE( '', ( #1614 ) );
#1209 = EDGE_CURVE( '', #1615, #1512, #1616, .T. );
#1210 = EDGE_CURVE( '', #1559, #1615, #1617, .T. );
#1211 = EDGE_CURVE( '', #1587, #1618, #1619, .T. );
#1212 = EDGE_CURVE( '', #1620, #1618, #1621, .T. );
#1213 = EDGE_CURVE( '', #1620, #1512, #1622, .F. );
#1214 = FILL_AREA_STYLE( '', ( #1623 ) );
#1215 = EDGE_CURVE( '', #1624, #1624, #1625, .F. );
#1216 = FILL_AREA_STYLE( '', ( #1626 ) );
#1217 = EDGE_CURVE( '', #1627, #1620, #1628, .F. );
#1218 = EDGE_CURVE( '', #1514, #1627, #1629, .T. );
#1219 = FILL_AREA_STYLE( '', ( #1630 ) );
#1220 = EDGE_CURVE( '', #1605, #1631, #1632, .T. );
#1221 = EDGE_CURVE( '', #1556, #1631, #1633, .T. );
#1222 = FILL_AREA_STYLE( '', ( #1634 ) );
#1223 = EDGE_CURVE( '', #1631, #1615, #1635, .T. );
#1224 = FILL_AREA_STYLE( '', ( #1636 ) );
#1225 = EDGE_CURVE( '', #1637, #1638, #1639, .F. );
#1226 = EDGE_CURVE( '', #1551, #1637, #1640, .F. );
#1227 = EDGE_CURVE( '', #1638, #1599, #1641, .T. );
#1228 = FILL_AREA_STYLE( '', ( #1642 ) );
#1229 = EDGE_CURVE( '', #1601, #1518, #1643, .F. );
#1230 = EDGE_CURVE( '', #1523, #1575, #1644, .T. );
#1231 = EDGE_CURVE( '', #1627, #1638, #1645, .T. );
#1232 = EDGE_CURVE( '', #1646, #1647, #1648, .T. );
#1233 = EDGE_CURVE( '', #1647, #1649, #1650, .T. );
#1234 = EDGE_CURVE( '', #1649, #1651, #1652, .T. );
#1235 = EDGE_CURVE( '', #1651, #1646, #1653, .T. );
#1236 = FILL_AREA_STYLE( '', ( #1654 ) );
#1237 = EDGE_CURVE( '', #1618, #1481, #1655, .T. );
#1238 = EDGE_CURVE( '', #1593, #1656, #1657, .T. );
#1239 = EDGE_CURVE( '', #1656, #1658, #1659, .T. );
#1240 = EDGE_CURVE( '', #1637, #1658, #1660, .T. );
#1241 = FILL_AREA_STYLE( '', ( #1661 ) );
#1242 = EDGE_CURVE( '', #1578, #1521, #1662, .F. );
#1243 = FILL_AREA_STYLE( '', ( #1663 ) );
#1244 = EDGE_CURVE( '', #1482, #1664, #1665, .T. );
#1245 = EDGE_CURVE( '', #1585, #1533, #1666, .T. );
#1246 = EDGE_CURVE( '', #1658, #1580, #1667, .T. );
#1247 = EDGE_CURVE( '', #1664, #1656, #1668, .T. );
#1248 = FILL_AREA_STYLE( '', ( #1669 ) );
#1249 = EDGE_CURVE( '', #1583, #1535, #1670, .T. );
#1250 = FILL_AREA_STYLE( '', ( #1671 ) );
#1251 = EDGE_CURVE( '', #1672, #1673, #1674, .T. );
#1252 = EDGE_CURVE( '', #1673, #1675, #1676, .T. );
#1253 = EDGE_CURVE( '', #1675, #1677, #1678, .T. );
#1254 = EDGE_CURVE( '', #1677, #1672, #1679, .T. );
#1255 = FILL_AREA_STYLE( '', ( #1680 ) );
#1256 = EDGE_CURVE( '', #1646, #1675, #1681, .T. );
#1257 = EDGE_CURVE( '', #1647, #1673, #1682, .T. );
#1258 = FILL_AREA_STYLE( '', ( #1683 ) );
#1259 = FILL_AREA_STYLE( '', ( #1684 ) );
#1260 = EDGE_CURVE( '', #1563, #1571, #1685, .T. );
#1261 = FILL_AREA_STYLE( '', ( #1686 ) );
#1262 = EDGE_CURVE( '', #1649, #1672, #1687, .T. );
#1263 = FILL_AREA_STYLE( '', ( #1688 ) );
#1264 = EDGE_CURVE( '', #1569, #1521, #1689, .T. );
#1265 = EDGE_CURVE( '', #1519, #1565, #1690, .F. );
#1266 = FILL_AREA_STYLE( '', ( #1691 ) );
#1267 = FILL_AREA_STYLE( '', ( #1692 ) );
#1268 = EDGE_CURVE( '', #1651, #1677, #1693, .T. );
#1269 = FILL_AREA_STYLE( '', ( #1694 ) );
#1270 = FILL_AREA_STYLE( '', ( #1695 ) );
#1271 = FILL_AREA_STYLE( '', ( #1696 ) );
#1272 = FILL_AREA_STYLE( '', ( #1697 ) );
#1273 = EDGE_CURVE( '', #1595, #1664, #1698, .T. );
#1274 = FILL_AREA_STYLE( '', ( #1699 ) );
#1275 = EDGE_LOOP( '', ( #1700, #1701, #1702, #1703 ) );
#1276 = AXIS2_PLACEMENT_3D( '', #1704, #1705, #1706 );
#1277 = EDGE_LOOP( '', ( #1707, #1708, #1709, #1710, #1711, #1712, #1713, #1714, #1715, #1716, #1717, #1718, #1719, #1720, #1721, #1722, #1723, #1724, #1725, #1726 ) );
#1278 = AXIS2_PLACEMENT_3D( '', #1727, #1728, #1729 );
#1279 = EDGE_LOOP( '', ( #1730 ) );
#1280 = EDGE_LOOP( '', ( #1731, #1732, #1733, #1734, #1735, #1736, #1737, #1738 ) );
#1281 = AXIS2_PLACEMENT_3D( '', #1739, #1740, #1741 );
#1282 = EDGE_LOOP( '', ( #1742, #1743, #1744, #1745 ) );
#1283 = AXIS2_PLACEMENT_3D( '', #1746, #1747, #1748 );
#1284 = EDGE_LOOP( '', ( #1749, #1750, #1751, #1752, #1753, #1754, #1755, #1756, #1757, #1758, #1759, #1760, #1761, #1762, #1763, #1764, #1765, #1766, #1767, #1768 ) );
#1285 = AXIS2_PLACEMENT_3D( '', #1769, #1770, #1771 );
#1286 = EDGE_LOOP( '', ( #1772, #1773, #1774, #1775 ) );
#1287 = AXIS2_PLACEMENT_3D( '', #1776, #1777, #1778 );
#1288 = EDGE_LOOP( '', ( #1779, #1780, #1781, #1782 ) );
#1289 = AXIS2_PLACEMENT_3D( '', #1783, #1784, #1785 );
#1290 = EDGE_LOOP( '', ( #1786, #1787, #1788, #1789, #1790, #1791, #1792, #1793 ) );
#1291 = AXIS2_PLACEMENT_3D( '', #1794, #1795, #1796 );
#1292 = EDGE_LOOP( '', ( #1797, #1798, #1799, #1800 ) );
#1293 = CARTESIAN_POINT( '', ( -1.27140397542541, 1.75000000000000, 0.776660964447032 ) );
#1294 = CARTESIAN_POINT( '', ( -1.27154417816867, 1.75000000000000, 0.776107309631355 ) );
#1295 = CARTESIAN_POINT( '', ( -1.27168438091193, 1.75000000000000, 0.775553654815677 ) );
#1296 = CARTESIAN_POINT( '', ( -1.27182458365518, 1.75000000000000, 0.775000000000000 ) );
#1297 = CARTESIAN_POINT( '', ( -1.27323972157899, 1.75000000000000, 0.769411679057433 ) );
#1298 = CARTESIAN_POINT( '', ( -1.27428778622344, 1.75023887789709, 0.763407248793691 ) );
#1299 = CARTESIAN_POINT( '', ( -1.27474080934666, 1.75079410346242, 0.757195203324373 ) );
#1300 = CARTESIAN_POINT( '', ( -1.27139760412356, 1.90000000000000, 0.775421531113411 ) );
#1301 = CARTESIAN_POINT( '', ( -1.27153993063410, 1.90000000000000, 0.775281020742273 ) );
#1302 = CARTESIAN_POINT( '', ( -1.27168225714464, 1.90000000000000, 0.775140510371136 ) );
#1303 = CARTESIAN_POINT( '', ( -1.27182458365518, 1.90000000000000, 0.775000000000000 ) );
#1304 = CARTESIAN_POINT( '', ( -1.27326115784729, 1.90000000000000, 0.773581757030838 ) );
#1305 = CARTESIAN_POINT( '', ( -1.27428437026337, 1.90005867282329, 0.772113522472251 ) );
#1306 = CARTESIAN_POINT( '', ( -1.27474080934666, 1.90019852586559, 0.770548800831091 ) );
#1307 = EDGE_LOOP( '', ( #1801, #1802, #1803, #1804 ) );
#1308 = AXIS2_PLACEMENT_3D( '', #1805, #1806, #1807 );
#1309 = EDGE_LOOP( '', ( #1808, #1809, #1810, #1811, #1812, #1813 ) );
#1310 = AXIS2_PLACEMENT_3D( '', #1814, #1815, #1816 );
#1311 = EDGE_LOOP( '', ( #1817, #1818, #1819, #1820 ) );
#1312 = AXIS2_PLACEMENT_3D( '', #1821, #1822, #1823 );
#1313 = EDGE_LOOP( '', ( #1824, #1825, #1826, #1827 ) );
#1314 = AXIS2_PLACEMENT_3D( '', #1828, #1829, #1830 );
#1315 = EDGE_LOOP( '', ( #1831, #1832, #1833, #1834, #1835 ) );
#1316 = AXIS2_PLACEMENT_3D( '', #1836, #1837, #1838 );
#1317 = EDGE_LOOP( '', ( #1839, #1840, #1841, #1842, #1843, #1844 ) );
#1318 = AXIS2_PLACEMENT_3D( '', #1845, #1846, #1847 );
#1319 = EDGE_LOOP( '', ( #1848, #1849, #1850, #1851, #1852 ) );
#1320 = AXIS2_PLACEMENT_3D( '', #1853, #1854, #1855 );
#1321 = EDGE_LOOP( '', ( #1856, #1857, #1858, #1859 ) );
#1322 = AXIS2_PLACEMENT_3D( '', #1860, #1861, #1862 );
#1323 = EDGE_LOOP( '', ( #1863 ) );
#1324 = EDGE_LOOP( '', ( #1864, #1865, #1866, #1867, #1868, #1869, #1870, #1871 ) );
#1325 = AXIS2_PLACEMENT_3D( '', #1872, #1873, #1874 );
#1326 = EDGE_LOOP( '', ( #1875, #1876, #1877, #1878 ) );
#1327 = AXIS2_PLACEMENT_3D( '', #1879, #1880, #1881 );
#1328 = EDGE_LOOP( '', ( #1882, #1883, #1884, #1885 ) );
#1329 = AXIS2_PLACEMENT_3D( '', #1886, #1887, #1888 );
#1330 = EDGE_LOOP( '', ( #1889, #1890, #1891, #1892 ) );
#1331 = AXIS2_PLACEMENT_3D( '', #1893, #1894, #1895 );
#1332 = EDGE_LOOP( '', ( #1896, #1897, #1898, #1899 ) );
#1333 = CARTESIAN_POINT( '', ( 1.27140397542540, -1.75000000000000, 0.776660964447047 ) );
#1334 = CARTESIAN_POINT( '', ( 1.27154417816866, -1.75000000000000, 0.776107309631364 ) );
#1335 = CARTESIAN_POINT( '', ( 1.27168438091192, -1.75000000000000, 0.775553654815682 ) );
#1336 = CARTESIAN_POINT( '', ( 1.27182458365519, -1.75000000000000, 0.775000000000000 ) );
#1337 = CARTESIAN_POINT( '', ( 1.27323972157899, -1.75000000000000, 0.769411679057450 ) );
#1338 = CARTESIAN_POINT( '', ( 1.27428778622348, -1.75023887789710, 0.763407248793677 ) );
#1339 = CARTESIAN_POINT( '', ( 1.27474080934666, -1.75079410346243, 0.757195203324363 ) );
#1340 = CARTESIAN_POINT( '', ( 1.27139760412356, -1.90000000000000, 0.775421531113411 ) );
#1341 = CARTESIAN_POINT( '', ( 1.27153993063410, -1.90000000000000, 0.775281020742274 ) );
#1342 = CARTESIAN_POINT( '', ( 1.27168225714464, -1.90000000000000, 0.775140510371137 ) );
#1343 = CARTESIAN_POINT( '', ( 1.27182458365519, -1.90000000000000, 0.775000000000000 ) );
#1344 = CARTESIAN_POINT( '', ( 1.27326115784728, -1.90000000000000, 0.773581757030859 ) );
#1345 = CARTESIAN_POINT( '', ( 1.27428437026341, -1.90005867282330, 0.772113522472244 ) );
#1346 = CARTESIAN_POINT( '', ( 1.27474080934666, -1.90019852586560, 0.770548800831090 ) );
#1347 = EDGE_LOOP( '', ( #1900, #1901, #1902, #1903 ) );
#1348 = AXIS2_PLACEMENT_3D( '', #1904, #1905, #1906 );
#1349 = EDGE_LOOP( '', ( #1907, #1908, #1909, #1910 ) );
#1350 = AXIS2_PLACEMENT_3D( '', #1911, #1912, #1913 );
#1351 = EDGE_LOOP( '', ( #1914, #1915, #1916, #1917 ) );
#1352 = AXIS2_PLACEMENT_3D( '', #1918, #1919, #1920 );
#1353 = EDGE_LOOP( '', ( #1921, #1922, #1923, #1924, #1925, #1926, #1927, #1928 ) );
#1354 = AXIS2_PLACEMENT_3D( '', #1929, #1930, #1931 );
#1355 = EDGE_LOOP( '', ( #1932, #1933, #1934, #1935 ) );
#1356 = CARTESIAN_POINT( '', ( -1.27491600611921, -1.75099778103083, 0.754916391981592 ) );
#1357 = CARTESIAN_POINT( '', ( -1.27485760719501, -1.75092988850803, 0.755675995762511 ) );
#1358 = CARTESIAN_POINT( '', ( -1.27479920827082, -1.75086199598523, 0.756435599543431 ) );
#1359 = CARTESIAN_POINT( '', ( -1.27474080934663, -1.75079410346243, 0.757195203324351 ) );
#1360 = CARTESIAN_POINT( '', ( -1.27426345484306, -1.75023914800788, 0.763404226705824 ) );
#1361 = CARTESIAN_POINT( '', ( -1.27326241976465, -1.75000000000000, 0.769414903876664 ) );
#1362 = CARTESIAN_POINT( '', ( -1.27182458365526, -1.75000000000000, 0.775000000000000 ) );
#1363 = CARTESIAN_POINT( '', ( -1.27491653211873, -1.90024959813482, 0.769977387556230 ) );
#1364 = CARTESIAN_POINT( '', ( -1.27485795786137, -1.90023257404508, 0.770167858647849 ) );
#1365 = CARTESIAN_POINT( '', ( -1.27479938360400, -1.90021554995535, 0.770358329739468 ) );
#1366 = CARTESIAN_POINT( '', ( -1.27474080934663, -1.90019852586560, 0.770548800831087 ) );
#1367 = CARTESIAN_POINT( '', ( -1.27426202166458, -1.90005937046132, 0.772105717069610 ) );
#1368 = CARTESIAN_POINT( '', ( -1.27327854559268, -1.90000000000000, 0.773588066274189 ) );
#1369 = CARTESIAN_POINT( '', ( -1.27182458365526, -1.90000000000000, 0.775000000000000 ) );
#1370 = EDGE_LOOP( '', ( #1936, #1937, #1938, #1939 ) );
#1371 = AXIS2_PLACEMENT_3D( '', #1940, #1941, #1942 );
#1372 = EDGE_LOOP( '', ( #1943, #1944, #1945, #1946, #1947 ) );
#1373 = AXIS2_PLACEMENT_3D( '', #1948, #1949, #1950 );
#1374 = EDGE_LOOP( '', ( #1951, #1952, #1953, #1954, #1955, #1956, #1957, #1958, #1959, #1960 ) );
#1375 = AXIS2_PLACEMENT_3D( '', #1961, #1962, #1963 );
#1376 = EDGE_LOOP( '', ( #1964, #1965, #1966, #1967 ) );
#1377 = AXIS2_PLACEMENT_3D( '', #1968, #1969, #1970 );
#1378 = EDGE_LOOP( '', ( #1971, #1972, #1973, #1974 ) );
#1379 = AXIS2_PLACEMENT_3D( '', #1975, #1976, #1977 );
#1380 = EDGE_LOOP( '', ( #1978, #1979, #1980, #1981, #1982, #1983 ) );
#1381 = AXIS2_PLACEMENT_3D( '', #1984, #1985, #1986 );
#1382 = EDGE_LOOP( '', ( #1987, #1988, #1989, #1990, #1991 ) );
#1383 = AXIS2_PLACEMENT_3D( '', #1992, #1993, #1994 );
#1384 = EDGE_LOOP( '', ( #1995, #1996, #1997, #1998 ) );
#1385 = AXIS2_PLACEMENT_3D( '', #1999, #2000, #2001 );
#1386 = EDGE_LOOP( '', ( #2002, #2003, #2004, #2005 ) );
#1387 = AXIS2_PLACEMENT_3D( '', #2006, #2007, #2008 );
#1388 = EDGE_LOOP( '', ( #2009, #2010, #2011, #2012 ) );
#1389 = AXIS2_PLACEMENT_3D( '', #2013, #2014, #2015 );
#1390 = EDGE_LOOP( '', ( #2016, #2017, #2018, #2019, #2020, #2021 ) );
#1391 = AXIS2_PLACEMENT_3D( '', #2022, #2023, #2024 );
#1392 = EDGE_LOOP( '', ( #2025, #2026, #2027, #2028 ) );
#1393 = AXIS2_PLACEMENT_3D( '', #2029, #2030, #2031 );
#1394 = EDGE_LOOP( '', ( #2032, #2033, #2034, #2035 ) );
#1395 = AXIS2_PLACEMENT_3D( '', #2036, #2037, #2038 );
#1396 = EDGE_LOOP( '', ( #2039, #2040, #2041, #2042 ) );
#1397 = AXIS2_PLACEMENT_3D( '', #2043, #2044, #2045 );
#1398 = EDGE_LOOP( '', ( #2046, #2047, #2048, #2049 ) );
#1399 = AXIS2_PLACEMENT_3D( '', #2050, #2051, #2052 );
#1400 = EDGE_LOOP( '', ( #2053, #2054, #2055, #2056, #2057, #2058 ) );
#1401 = AXIS2_PLACEMENT_3D( '', #2059, #2060, #2061 );
#1402 = EDGE_LOOP( '', ( #2062, #2063, #2064, #2065 ) );
#1403 = AXIS2_PLACEMENT_3D( '', #2066, #2067, #2068 );
#1404 = EDGE_LOOP( '', ( #2069, #2070, #2071, #2072, #2073 ) );
#1405 = AXIS2_PLACEMENT_3D( '', #2074, #2075, #2076 );
#1406 = EDGE_LOOP( '', ( #2077, #2078, #2079, #2080 ) );
#1407 = AXIS2_PLACEMENT_3D( '', #2081, #2082, #2083 );
#1408 = EDGE_LOOP( '', ( #2084, #2085, #2086, #2087 ) );
#1409 = CARTESIAN_POINT( '', ( 1.27491600611921, 1.75099778103083, 0.754916391981587 ) );
#1410 = CARTESIAN_POINT( '', ( 1.27485760719502, 1.75092988850802, 0.755675995762511 ) );
#1411 = CARTESIAN_POINT( '', ( 1.27479920827083, 1.75086199598522, 0.756435599543435 ) );
#1412 = CARTESIAN_POINT( '', ( 1.27474080934663, 1.75079410346242, 0.757195203324359 ) );
#1413 = CARTESIAN_POINT( '', ( 1.27426345484304, 1.75023914800787, 0.763404226705840 ) );
#1414 = CARTESIAN_POINT( '', ( 1.27326241976467, 1.75000000000000, 0.769414903876684 ) );
#1415 = CARTESIAN_POINT( '', ( 1.27182458365525, 1.75000000000000, 0.775000000000000 ) );
#1416 = CARTESIAN_POINT( '', ( 1.27491653211874, 1.90024959813481, 0.769977387556232 ) );
#1417 = CARTESIAN_POINT( '', ( 1.27485795786137, 1.90023257404507, 0.770167858647850 ) );
#1418 = CARTESIAN_POINT( '', ( 1.27479938360400, 1.90021554995534, 0.770358329739469 ) );
#1419 = CARTESIAN_POINT( '', ( 1.27474080934663, 1.90019852586559, 0.770548800831087 ) );
#1420 = CARTESIAN_POINT( '', ( 1.27426202166457, 1.90005937046131, 0.772105717069600 ) );
#1421 = CARTESIAN_POINT( '', ( 1.27327854559268, 1.90000000000000, 0.773588066274210 ) );
#1422 = CARTESIAN_POINT( '', ( 1.27182458365525, 1.90000000000000, 0.775000000000000 ) );
#1423 = EDGE_LOOP( '', ( #2088, #2089, #2090, #2091 ) );
#1424 = AXIS2_PLACEMENT_3D( '', #2092, #2093, #2094 );
#1425 = EDGE_LOOP( '', ( #2095, #2096, #2097, #2098 ) );
#1426 = AXIS2_PLACEMENT_3D( '', #2099, #2100, #2101 );
#1427 = EDGE_LOOP( '', ( #2102, #2103, #2104, #2105 ) );
#1428 = AXIS2_PLACEMENT_3D( '', #2106, #2107, #2108 );
#1429 = EDGE_LOOP( '', ( #2109, #2110, #2111, #2112, #2113 ) );
#1430 = AXIS2_PLACEMENT_3D( '', #2114, #2115, #2116 );
#1431 = EDGE_LOOP( '', ( #2117, #2118, #2119, #2120, #2121, #2122, #2123, #2124, #2125, #2126 ) );
#1432 = AXIS2_PLACEMENT_3D( '', #2127, #2128, #2129 );
#1433 = EDGE_LOOP( '', ( #2130, #2131, #2132, #2133 ) );
#1434 = AXIS2_PLACEMENT_3D( '', #2134, #2135, #2136 );
#1435 = EDGE_LOOP( '', ( #2137, #2138, #2139, #2140 ) );
#1436 = AXIS2_PLACEMENT_3D( '', #2141, #2142, #2143 );
#1437 = EDGE_LOOP( '', ( #2144, #2145, #2146, #2147 ) );
#1438 = AXIS2_PLACEMENT_3D( '', #2148, #2149, #2150 );
#1439 = EDGE_LOOP( '', ( #2151, #2152, #2153, #2154 ) );
#1440 = AXIS2_PLACEMENT_3D( '', #2155, #2156, #2157 );
#1441 = EDGE_LOOP( '', ( #2158, #2159, #2160, #2161 ) );
#1442 = AXIS2_PLACEMENT_3D( '', #2162, #2163, #2164 );
#1443 = EDGE_LOOP( '', ( #2165, #2166, #2167, #2168, #2169 ) );
#1444 = AXIS2_PLACEMENT_3D( '', #2170, #2171, #2172 );
#1445 = EDGE_LOOP( '', ( #2173, #2174, #2175, #2176, #2177, #2178 ) );
#1446 = AXIS2_PLACEMENT_3D( '', #2179, #2180, #2181 );
#1447 = EDGE_LOOP( '', ( #2182 ) );
#1448 = EDGE_LOOP( '', ( #2183 ) );
#1449 = AXIS2_PLACEMENT_3D( '', #2184, #2185, #2186 );
#1450 = EDGE_LOOP( '', ( #2187, #2188, #2189, #2190 ) );
#1451 = AXIS2_PLACEMENT_3D( '', #2191, #2192, #2193 );
#1452 = EDGE_LOOP( '', ( #2194, #2195, #2196, #2197, #2198 ) );
#1453 = AXIS2_PLACEMENT_3D( '', #2199, #2200, #2201 );
#1454 = EDGE_LOOP( '', ( #2202, #2203, #2204, #2205 ) );
#1455 = AXIS2_PLACEMENT_3D( '', #2206, #2207, #2208 );
#1456 = EDGE_LOOP( '', ( #2209, #2210, #2211, #2212 ) );
#1457 = AXIS2_PLACEMENT_3D( '', #2213, #2214, #2215 );
#1458 = EDGE_LOOP( '', ( #2216, #2217, #2218, #2219 ) );
#1459 = AXIS2_PLACEMENT_3D( '', #2220, #2221, #2222 );
#1460 = EDGE_LOOP( '', ( #2223, #2224, #2225, #2226 ) );
#1461 = AXIS2_PLACEMENT_3D( '', #2227, #2228, #2229 );
#1462 = EDGE_LOOP( '', ( #2230, #2231, #2232, #2233 ) );
#1463 = AXIS2_PLACEMENT_3D( '', #2234, #2235, #2236 );
#1464 = EDGE_LOOP( '', ( #2237, #2238, #2239, #2240 ) );
#1465 = AXIS2_PLACEMENT_3D( '', #2241, #2242, #2243 );
#1466 = EDGE_LOOP( '', ( #2244, #2245, #2246, #2247 ) );
#1467 = AXIS2_PLACEMENT_3D( '', #2248, #2249, #2250 );
#1468 = EDGE_LOOP( '', ( #2251, #2252, #2253, #2254 ) );
#1469 = AXIS2_PLACEMENT_3D( '', #2255, #2256, #2257 );
#1470 = EDGE_LOOP( '', ( #2258, #2259, #2260, #2261 ) );
#1471 = AXIS2_PLACEMENT_3D( '', #2262, #2263, #2264 );
#1472 = EDGE_LOOP( '', ( #2265, #2266, #2267, #2268 ) );
#1473 = AXIS2_PLACEMENT_3D( '', #2269, #2270, #2271 );
#1474 = EDGE_LOOP( '', ( #2272, #2273, #2274, #2275 ) );
#1475 = AXIS2_PLACEMENT_3D( '', #2276, #2277, #2278 );
#1476 = EDGE_LOOP( '', ( #2279, #2280, #2281, #2282 ) );
#1477 = AXIS2_PLACEMENT_3D( '', #2283, #2284, #2285 );
#1478 = EDGE_LOOP( '', ( #2286, #2287, #2288, #2289 ) );
#1479 = AXIS2_PLACEMENT_3D( '', #2290, #2291, #2292 );
#1480 = FILL_AREA_STYLE_COLOUR( '', #2293 );
#1481 = VERTEX_POINT( '', #2294 );
#1482 = VERTEX_POINT( '', #2295 );
#1483 = LINE( '', #2296, #2297 );
#1484 = VERTEX_POINT( '', #2298 );
#1485 = LINE( '', #2299, #2300 );
#1486 = VERTEX_POINT( '', #2301 );
#1487 = LINE( '', #2302, #2303 );
#1488 = LINE( '', #2304, #2305 );
#1489 = FILL_AREA_STYLE_COLOUR( '', #2306 );
#1490 = VERTEX_POINT( '', #2307 );
#1491 = VERTEX_POINT( '', #2308 );
#1492 = ELLIPSE( '', #2309, 0.0707106781186548, 0.0500000000000001 );
#1493 = VERTEX_POINT( '', #2310 );
#1494 = CIRCLE( '', #2311, 0.0500000000000000 );
#1495 = VERTEX_POINT( '', #2312 );
#1496 = LINE( '', #2313, #2314 );
#1497 = VERTEX_POINT( '', #2315 );
#1498 = CIRCLE( '', #2316, 0.0500000000000000 );
#1499 = VERTEX_POINT( '', #2317 );
#1500 = ELLIPSE( '', #2318, 0.0707106781186548, 0.0500000000000001 );
#1501 = LINE( '', #2319, #2320 );
#1502 = FILL_AREA_STYLE_COLOUR( '', #2321 );
#1503 = VERTEX_POINT( '', #2322 );
#1504 = VERTEX_POINT( '', #2323 );
#1505 = LINE( '', #2324, #2325 );
#1506 = VERTEX_POINT( '', #2326 );
#1507 = LINE( '', #2327, #2328 );
#1508 = VERTEX_POINT( '', #2329 );
#1509 = LINE( '', #2330, #2331 );
#1510 = LINE( '', #2332, #2333 );
#1511 = FILL_AREA_STYLE_COLOUR( '', #2334 );
#1512 = VERTEX_POINT( '', #2335 );
#1513 = LINE( '', #2336, #2337 );
#1514 = VERTEX_POINT( '', #2338 );
#1515 = CIRCLE( '', #2339, 0.0500000000000000 );
#1516 = LINE( '', #2340, #2341 );
#1517 = FILL_AREA_STYLE_COLOUR( '', #2342 );
#1518 = VERTEX_POINT( '', #2343 );
#1519 = VERTEX_POINT( '', #2344 );
#1520 = CIRCLE( '', #2345, 0.0500000000000000 );
#1521 = VERTEX_POINT( '', #2346 );
#1522 = LINE( '', #2347, #2348 );
#1523 = VERTEX_POINT( '', #2349 );
#1524 = CIRCLE( '', #2350, 0.0500000000000000 );
#1525 = LINE( '', #2351, #2352 );
#1526 = FILL_AREA_STYLE_COLOUR( '', #2353 );
#1527 = VERTEX_POINT( '', #2354 );
#1528 = VERTEX_POINT( '', #2355 );
#1529 = CIRCLE( '', #2356, 0.0500000000000000 );
#1530 = LINE( '', #2357, #2358 );
#1531 = LINE( '', #2359, #2360 );
#1532 = FILL_AREA_STYLE_COLOUR( '', #2361 );
#1533 = VERTEX_POINT( '', #2362 );
#1534 = LINE( '', #2363, #2364 );
#1535 = VERTEX_POINT( '', #2365 );
#1536 = LINE( '', #2366, #2367 );
#1537 = LINE( '', #2368, #2369 );
#1538 = FILL_AREA_STYLE_COLOUR( '', #2370 );
#1539 = VERTEX_POINT( '', #2371 );
#1540 = CIRCLE( '', #2372, 0.850000000000000 );
#1541 = FILL_AREA_STYLE_COLOUR( '', #2373 );
#1542 = VERTEX_POINT( '', #2374 );
#1543 = VERTEX_POINT( '', #2375 );
#1544 = LINE( '', #2376, #2377 );
#1545 = VERTEX_POINT( '', #2378 );
#1546 = LINE( '', #2379, #2380 );
#1547 = VERTEX_POINT( '', #2381 );
#1548 = LINE( '', #2382, #2383 );
#1549 = VERTEX_POINT( '', #2384 );
#1550 = ELLIPSE( '', #2385, 0.0707106781186548, 0.0500000000000001 );
#1551 = VERTEX_POINT( '', #2386 );
#1552 = LINE( '', #2387, #2388 );
#1553 = LINE( '', #2389, #2390 );
#1554 = FILL_AREA_STYLE_COLOUR( '', #2391 );
#1555 = VERTEX_POINT( '', #2392 );
#1556 = VERTEX_POINT( '', #2393 );
#1557 = LINE( '', #2394, #2395 );
#1558 = LINE( '', #2396, #2397 );
#1559 = VERTEX_POINT( '', #2398 );
#1560 = LINE( '', #2399, #2400 );
#1561 = LINE( '', #2401, #2402 );
#1562 = FILL_AREA_STYLE_COLOUR( '', #2403 );
#1563 = VERTEX_POINT( '', #2404 );
#1564 = LINE( '', #2405, #2406 );
#1565 = VERTEX_POINT( '', #2407 );
#1566 = LINE( '', #2408, #2409 );
#1567 = LINE( '', #2410, #2411 );
#1568 = FILL_AREA_STYLE_COLOUR( '', #2412 );
#1569 = VERTEX_POINT( '', #2413 );
#1570 = LINE( '', #2414, #2415 );
#1571 = VERTEX_POINT( '', #2416 );
#1572 = LINE( '', #2417, #2418 );
#1573 = LINE( '', #2419, #2420 );
#1574 = FILL_AREA_STYLE_COLOUR( '', #2421 );
#1575 = VERTEX_POINT( '', #2422 );
#1576 = VERTEX_POINT( '', #2423 );
#1577 = LINE( '', #2424, #2425 );
#1578 = VERTEX_POINT( '', #2426 );
#1579 = CIRCLE( '', #2427, 0.0500000000000000 );
#1580 = VERTEX_POINT( '', #2428 );
#1581 = LINE( '', #2429, #2430 );
#1582 = LINE( '', #2431, #2432 );
#1583 = VERTEX_POINT( '', #2433 );
#1584 = LINE( '', #2434, #2435 );
#1585 = VERTEX_POINT( '', #2436 );
#1586 = LINE( '', #2437, #2438 );
#1587 = VERTEX_POINT( '', #2439 );
#1588 = LINE( '', #2440, #2441 );
#1589 = VERTEX_POINT( '', #2442 );
#1590 = LINE( '', #2443, #2444 );
#1591 = CIRCLE( '', #2445, 0.0500000000000000 );
#1592 = FILL_AREA_STYLE_COLOUR( '', #2446 );
#1593 = VERTEX_POINT( '', #2447 );
#1594 = LINE( '', #2448, #2449 );
#1595 = VERTEX_POINT( '', #2450 );
#1596 = LINE( '', #2451, #2452 );
#1597 = LINE( '', #2453, #2454 );
#1598 = FILL_AREA_STYLE_COLOUR( '', #2455 );
#1599 = VERTEX_POINT( '', #2456 );
#1600 = CIRCLE( '', #2457, 0.0500000000000000 );
#1601 = VERTEX_POINT( '', #2458 );
#1602 = CIRCLE( '', #2459, 0.0500000000000000 );
#1603 = LINE( '', #2460, #2461 );
#1604 = FILL_AREA_STYLE_COLOUR( '', #2462 );
#1605 = VERTEX_POINT( '', #2463 );
#1606 = LINE( '', #2464, #2465 );
#1607 = LINE( '', #2466, #2467 );
#1608 = FILL_AREA_STYLE_COLOUR( '', #2468 );
#1609 = VERTEX_POINT( '', #2469 );
#1610 = CIRCLE( '', #2470, 0.950000000000000 );
#1611 = FILL_AREA_STYLE_COLOUR( '', #2471 );
#1612 = LINE( '', #2472, #2473 );
#1613 = LINE( '', #2474, #2475 );
#1614 = FILL_AREA_STYLE_COLOUR( '', #2476 );
#1615 = VERTEX_POINT( '', #2477 );
#1616 = LINE( '', #2478, #2479 );
#1617 = LINE( '', #2480, #2481 );
#1618 = VERTEX_POINT( '', #2482 );
#1619 = LINE( '', #2483, #2484 );
#1620 = VERTEX_POINT( '', #2485 );
#1621 = LINE( '', #2486, #2487 );
#1622 = LINE( '', #2488, #2489 );
#1623 = FILL_AREA_STYLE_COLOUR( '', #2490 );
#1624 = VERTEX_POINT( '', #2491 );
#1625 = CIRCLE( '', #2492, 0.950000000000000 );
#1626 = FILL_AREA_STYLE_COLOUR( '', #2493 );
#1627 = VERTEX_POINT( '', #2494 );
#1628 = CIRCLE( '', #2495, 0.0500000000000000 );
#1629 = LINE( '', #2496, #2497 );
#1630 = FILL_AREA_STYLE_COLOUR( '', #2498 );
#1631 = VERTEX_POINT( '', #2499 );
#1632 = LINE( '', #2500, #2501 );
#1633 = LINE( '', #2502, #2503 );
#1634 = FILL_AREA_STYLE_COLOUR( '', #2504 );
#1635 = LINE( '', #2505, #2506 );
#1636 = FILL_AREA_STYLE_COLOUR( '', #2507 );
#1637 = VERTEX_POINT( '', #2508 );
#1638 = VERTEX_POINT( '', #2509 );
#1639 = CIRCLE( '', #2510, 0.0500000000000000 );
#1640 = LINE( '', #2511, #2512 );
#1641 = LINE( '', #2513, #2514 );
#1642 = FILL_AREA_STYLE_COLOUR( '', #2515 );
#1643 = LINE( '', #2516, #2517 );
#1644 = LINE( '', #2518, #2519 );
#1645 = LINE( '', #2520, #2521 );
#1646 = VERTEX_POINT( '', #2522 );
#1647 = VERTEX_POINT( '', #2523 );
#1648 = LINE( '', #2524, #2525 );
#1649 = VERTEX_POINT( '', #2526 );
#1650 = LINE( '', #2527, #2528 );
#1651 = VERTEX_POINT( '', #2529 );
#1652 = LINE( '', #2530, #2531 );
#1653 = LINE( '', #2532, #2533 );
#1654 = FILL_AREA_STYLE_COLOUR( '', #2534 );
#1655 = LINE( '', #2535, #2536 );
#1656 = VERTEX_POINT( '', #2537 );
#1657 = LINE( '', #2538, #2539 );
#1658 = VERTEX_POINT( '', #2540 );
#1659 = LINE( '', #2541, #2542 );
#1660 = LINE( '', #2543, #2544 );
#1661 = FILL_AREA_STYLE_COLOUR( '', #2545 );
#1662 = LINE( '', #2546, #2547 );
#1663 = FILL_AREA_STYLE_COLOUR( '', #2548 );
#1664 = VERTEX_POINT( '', #2549 );
#1665 = LINE( '', #2550, #2551 );
#1666 = LINE( '', #2552, #2553 );
#1667 = LINE( '', #2554, #2555 );
#1668 = LINE( '', #2556, #2557 );
#1669 = FILL_AREA_STYLE_COLOUR( '', #2558 );
#1670 = LINE( '', #2559, #2560 );
#1671 = FILL_AREA_STYLE_COLOUR( '', #2561 );
#1672 = VERTEX_POINT( '', #2562 );
#1673 = VERTEX_POINT( '', #2563 );
#1674 = LINE( '', #2564, #2565 );
#1675 = VERTEX_POINT( '', #2566 );
#1676 = LINE( '', #2567, #2568 );
#1677 = VERTEX_POINT( '', #2569 );
#1678 = LINE( '', #2570, #2571 );
#1679 = LINE( '', #2572, #2573 );
#1680 = FILL_AREA_STYLE_COLOUR( '', #2574 );
#1681 = LINE( '', #2575, #2576 );
#1682 = LINE( '', #2577, #2578 );
#1683 = FILL_AREA_STYLE_COLOUR( '', #2579 );
#1684 = FILL_AREA_STYLE_COLOUR( '', #2580 );
#1685 = LINE( '', #2581, #2582 );
#1686 = FILL_AREA_STYLE_COLOUR( '', #2583 );
#1687 = LINE( '', #2584, #2585 );
#1688 = FILL_AREA_STYLE_COLOUR( '', #2586 );
#1689 = LINE( '', #2587, #2588 );
#1690 = ELLIPSE( '', #2589, 0.0707106781186548, 0.0500000000000001 );
#1691 = FILL_AREA_STYLE_COLOUR( '', #2590 );
#1692 = FILL_AREA_STYLE_COLOUR( '', #2591 );
#1693 = LINE( '', #2592, #2593 );
#1694 = FILL_AREA_STYLE_COLOUR( '', #2594 );
#1695 = FILL_AREA_STYLE_COLOUR( '', #2595 );
#1696 = FILL_AREA_STYLE_COLOUR( '', #2596 );
#1697 = FILL_AREA_STYLE_COLOUR( '', #2597 );
#1698 = LINE( '', #2598, #2599 );
#1699 = FILL_AREA_STYLE_COLOUR( '', #2600 );
#1700 = ORIENTED_EDGE( '', *, *, #2601, .F. );
#1701 = ORIENTED_EDGE( '', *, *, #2602, .F. );
#1702 = ORIENTED_EDGE( '', *, *, #2603, .T. );
#1703 = ORIENTED_EDGE( '', *, *, #2604, .T. );
#1704 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 0.750000000000000 ) );
#1705 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#1706 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#1707 = ORIENTED_EDGE( '', *, *, #2605, .T. );
#1708 = ORIENTED_EDGE( '', *, *, #2606, .F. );
#1709 = ORIENTED_EDGE( '', *, *, #2607, .T. );
#1710 = ORIENTED_EDGE( '', *, *, #2608, .F. );
#1711 = ORIENTED_EDGE( '', *, *, #2609, .T. );
#1712 = ORIENTED_EDGE( '', *, *, #2610, .T. );
#1713 = ORIENTED_EDGE( '', *, *, #2611, .F. );
#1714 = ORIENTED_EDGE( '', *, *, #2612, .T. );
#1715 = ORIENTED_EDGE( '', *, *, #2613, .F. );
#1716 = ORIENTED_EDGE( '', *, *, #2614, .T. );
#1717 = ORIENTED_EDGE( '', *, *, #2615, .F. );
#1718 = ORIENTED_EDGE( '', *, *, #2616, .T. );
#1719 = ORIENTED_EDGE( '', *, *, #2617, .F. );
#1720 = ORIENTED_EDGE( '', *, *, #2618, .T. );
#1721 = ORIENTED_EDGE( '', *, *, #2619, .F. );
#1722 = ORIENTED_EDGE( '', *, *, #2620, .F. );
#1723 = ORIENTED_EDGE( '', *, *, #2621, .T. );
#1724 = ORIENTED_EDGE( '', *, *, #2622, .F. );
#1725 = ORIENTED_EDGE( '', *, *, #2623, .T. );
#1726 = ORIENTED_EDGE( '', *, *, #2624, .F. );
#1727 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75079410346242, 0.757195203324359 ) );
#1728 = DIRECTION( '', ( -1.00000000000000, 4.85266776407460E-019, -5.42932362639114E-018 ) );
#1729 = DIRECTION( '', ( 5.45096678437632E-018, 0.0890239833782041, -0.996029482687876 ) );
#1730 = ORIENTED_EDGE( '', *, *, #2625, .T. );
#1731 = ORIENTED_EDGE( '', *, *, #2626, .T. );
#1732 = ORIENTED_EDGE( '', *, *, #2627, .T. );
#1733 = ORIENTED_EDGE( '', *, *, #2604, .F. );
#1734 = ORIENTED_EDGE( '', *, *, #2628, .F. );
#1735 = ORIENTED_EDGE( '', *, *, #2605, .F. );
#1736 = ORIENTED_EDGE( '', *, *, #2629, .T. );
#1737 = ORIENTED_EDGE( '', *, *, #2630, .T. );
#1738 = ORIENTED_EDGE( '', *, *, #2631, .T. );
#1739 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.70000000000000 ) );
#1740 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#1741 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#1742 = ORIENTED_EDGE( '', *, *, #2627, .F. );
#1743 = ORIENTED_EDGE( '', *, *, #2632, .F. );
#1744 = ORIENTED_EDGE( '', *, *, #2633, .F. );
#1745 = ORIENTED_EDGE( '', *, *, #2634, .F. );
#1746 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 1.50000000000000 ) );
#1747 = DIRECTION( '', ( 1.00000000000000, -0.000000000000000, -0.000000000000000 ) );
#1748 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#1749 = ORIENTED_EDGE( '', *, *, #2635, .T. );
#1750 = ORIENTED_EDGE( '', *, *, #2636, .F. );
#1751 = ORIENTED_EDGE( '', *, *, #2637, .T. );
#1752 = ORIENTED_EDGE( '', *, *, #2638, .T. );
#1753 = ORIENTED_EDGE( '', *, *, #2639, .T. );
#1754 = ORIENTED_EDGE( '', *, *, #2640, .F. );
#1755 = ORIENTED_EDGE( '', *, *, #2641, .F. );
#1756 = ORIENTED_EDGE( '', *, *, #2642, .F. );
#1757 = ORIENTED_EDGE( '', *, *, #2643, .F. );
#1758 = ORIENTED_EDGE( '', *, *, #2632, .T. );
#1759 = ORIENTED_EDGE( '', *, *, #2626, .F. );
#1760 = ORIENTED_EDGE( '', *, *, #2644, .T. );
#1761 = ORIENTED_EDGE( '', *, *, #2645, .F. );
#1762 = ORIENTED_EDGE( '', *, *, #2646, .F. );
#1763 = ORIENTED_EDGE( '', *, *, #2647, .F. );
#1764 = ORIENTED_EDGE( '', *, *, #2648, .T. );
#1765 = ORIENTED_EDGE( '', *, *, #2649, .T. );
#1766 = ORIENTED_EDGE( '', *, *, #2650, .T. );
#1767 = ORIENTED_EDGE( '', *, *, #2651, .T. );
#1768 = ORIENTED_EDGE( '', *, *, #2652, .F. );
#1769 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.80059557759684, 0.200000000000000 ) );
#1770 = DIRECTION( '', ( -1.00000000000000, 4.85266776407903E-019, 5.42932362639360E-018 ) );
#1771 = DIRECTION( '', ( 5.45096678437881E-018, 0.0890239833782447, 0.996029482687872 ) );
#1772 = ORIENTED_EDGE( '', *, *, #2653, .T. );
#1773 = ORIENTED_EDGE( '', *, *, #2654, .F. );
#1774 = ORIENTED_EDGE( '', *, *, #2655, .F. );
#1775 = ORIENTED_EDGE( '', *, *, #2640, .T. );
#1776 = CARTESIAN_POINT( '', ( 2.95000000000000, 8.97386843989081, 1.34313215005202 ) );
#1777 = DIRECTION( '', ( -0.707106781186548, -0.0629494623349667, 0.704299201470326 ) );
#1778 = DIRECTION( '', ( 0.707106781186547, -0.0629494623349667, 0.704299201470326 ) );
#1779 = ORIENTED_EDGE( '', *, *, #2656, .F. );
#1780 = ORIENTED_EDGE( '', *, *, #2657, .F. );
#1781 = ORIENTED_EDGE( '', *, *, #2658, .T. );
#1782 = ORIENTED_EDGE( '', *, *, #2654, .T. );
#1783 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.80059557759680, 0.200000000000008 ) );
#1784 = DIRECTION( '', ( 0.000000000000000, 0.0890239833782041, -0.996029482687876 ) );
#1785 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#1786 = ORIENTED_EDGE( '', *, *, #2659, .F. );
#1787 = ORIENTED_EDGE( '', *, *, #2609, .F. );
#1788 = ORIENTED_EDGE( '', *, *, #2660, .T. );
#1789 = ORIENTED_EDGE( '', *, *, #2661, .F. );
#1790 = ORIENTED_EDGE( '', *, *, #2662, .F. );
#1791 = ORIENTED_EDGE( '', *, *, #2641, .T. );
#1792 = ORIENTED_EDGE( '', *, *, #2655, .T. );
#1793 = ORIENTED_EDGE( '', *, *, #2658, .F. );
#1794 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.90019852586560, 0.770548800831091 ) );
#1795 = DIRECTION( '', ( 0.000000000000000, -0.996029482687876, -0.0890239833782041 ) );
#1796 = DIRECTION( '', ( 0.000000000000000, 0.0890239833782041, -0.996029482687876 ) );
#1797 = ORIENTED_EDGE( '', *, *, #2663, .F. );
#1798 = ORIENTED_EDGE( '', *, *, #2664, .F. );
#1799 = ORIENTED_EDGE( '', *, *, #2665, .T. );
#1800 = ORIENTED_EDGE( '', *, *, #2666, .T. );
#1801 = ORIENTED_EDGE( '', *, *, #2667, .T. );
#1802 = ORIENTED_EDGE( '', *, *, #2668, .T. );
#1803 = ORIENTED_EDGE( '', *, *, #2669, .T. );
#1804 = ORIENTED_EDGE( '', *, *, #2670, .T. );
#1805 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 0.750000000000000 ) );
#1806 = DIRECTION( '', ( -0.000000000000000, -1.00000000000000, -0.000000000000000 ) );
#1807 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#1808 = ORIENTED_EDGE( '', *, *, #2661, .T. );
#1809 = ORIENTED_EDGE( '', *, *, #2663, .T. );
#1810 = ORIENTED_EDGE( '', *, *, #2671, .T. );
#1811 = ORIENTED_EDGE( '', *, *, #2672, .T. );
#1812 = ORIENTED_EDGE( '', *, *, #2673, .T. );
#1813 = ORIENTED_EDGE( '', *, *, #2674, .F. );
#1814 = CARTESIAN_POINT( '', ( 0.000000000000000, 1.82549631466401, 0.763872002077725 ) );
#1815 = DIRECTION( '', ( 1.95357086939468E-017, -0.0890239833782007, 0.996029482687876 ) );
#1816 = DIRECTION( '', ( 0.000000000000000, -0.996029482687876, -0.0890239833782007 ) );
#1817 = ORIENTED_EDGE( '', *, *, #2608, .T. );
#1818 = ORIENTED_EDGE( '', *, *, #2675, .T. );
#1819 = ORIENTED_EDGE( '', *, *, #2664, .T. );
#1820 = ORIENTED_EDGE( '', *, *, #2660, .F. );
#1821 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.95000000000000, 0.775000000000000 ) );
#1822 = DIRECTION( '', ( -1.00000000000000, -1.38040321271469E-045, -1.04894090727380E-031 ) );
#1823 = DIRECTION( '', ( 4.66726145839586E-061, 1.00000000000000, -1.31599711970654E-014 ) );
#1824 = ORIENTED_EDGE( '', *, *, #2676, .T. );
#1825 = ORIENTED_EDGE( '', *, *, #2677, .F. );
#1826 = ORIENTED_EDGE( '', *, *, #2678, .T. );
#1827 = ORIENTED_EDGE( '', *, *, #2679, .F. );
#1828 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70000000000000, 0.750000000000000 ) );
#1829 = DIRECTION( '', ( -0.000000000000000, 1.00000000000000, -0.000000000000000 ) );
#1830 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#1831 = ORIENTED_EDGE( '', *, *, #2680, .F. );
#1832 = ORIENTED_EDGE( '', *, *, #2678, .F. );
#1833 = ORIENTED_EDGE( '', *, *, #2681, .T. );
#1834 = ORIENTED_EDGE( '', *, *, #2645, .T. );
#1835 = ORIENTED_EDGE( '', *, *, #2682, .F. );
#1836 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.90000000000000, 0.775000000000000 ) );
#1837 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#1838 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#1839 = ORIENTED_EDGE( '', *, *, #2680, .T. );
#1840 = ORIENTED_EDGE( '', *, *, #2683, .T. );
#1841 = ORIENTED_EDGE( '', *, *, #2684, .F. );
#1842 = ORIENTED_EDGE( '', *, *, #2685, .F. );
#1843 = ORIENTED_EDGE( '', *, *, #2686, .T. );
#1844 = ORIENTED_EDGE( '', *, *, #2679, .T. );
#1845 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70000000000000, 0.850000000000000 ) );
#1846 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#1847 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#1848 = ORIENTED_EDGE( '', *, *, #2676, .F. );
#1849 = ORIENTED_EDGE( '', *, *, #2686, .F. );
#1850 = ORIENTED_EDGE( '', *, *, #2687, .T. );
#1851 = ORIENTED_EDGE( '', *, *, #2651, .F. );
#1852 = ORIENTED_EDGE( '', *, *, #2688, .F. );
#1853 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.75000000000000, 0.775000000000000 ) );
#1854 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#1855 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#1856 = ORIENTED_EDGE( '', *, *, #2687, .F. );
#1857 = ORIENTED_EDGE( '', *, *, #2685, .T. );
#1858 = ORIENTED_EDGE( '', *, *, #2689, .T. );
#1859 = ORIENTED_EDGE( '', *, *, #2652, .T. );
#1860 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.50000000000000 ) );
#1861 = DIRECTION( '', ( -1.00000000000000, 2.99204702930428E-032, -0.000000000000000 ) );
#1862 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#1863 = ORIENTED_EDGE( '', *, *, #2690, .F. );
#1864 = ORIENTED_EDGE( '', *, *, #2635, .F. );
#1865 = ORIENTED_EDGE( '', *, *, #2689, .F. );
#1866 = ORIENTED_EDGE( '', *, *, #2691, .F. );
#1867 = ORIENTED_EDGE( '', *, *, #2692, .F. );
#1868 = ORIENTED_EDGE( '', *, *, #2615, .T. );
#1869 = ORIENTED_EDGE( '', *, *, #2693, .T. );
#1870 = ORIENTED_EDGE( '', *, *, #2602, .T. );
#1871 = ORIENTED_EDGE( '', *, *, #2694, .F. );
#1872 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.55000000000000 ) );
#1873 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#1874 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#1875 = ORIENTED_EDGE( '', *, *, #2646, .T. );
#1876 = ORIENTED_EDGE( '', *, *, #2681, .F. );
#1877 = ORIENTED_EDGE( '', *, *, #2695, .T. );
#1878 = ORIENTED_EDGE( '', *, *, #2696, .T. );
#1879 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.95000000000000, 0.775000000000000 ) );
#1880 = DIRECTION( '', ( 1.00000000000000, -0.000000000000000, -0.000000000000000 ) );
#1881 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#1882 = ORIENTED_EDGE( '', *, *, #2697, .T. );
#1883 = ORIENTED_EDGE( '', *, *, #2698, .T. );
#1884 = ORIENTED_EDGE( '', *, *, #2699, .T. );
#1885 = ORIENTED_EDGE( '', *, *, #2700, .T. );
#1886 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70000000000000, 0.750000000000000 ) );
#1887 = DIRECTION( '', ( -0.000000000000000, 1.00000000000000, -0.000000000000000 ) );
#1888 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#1889 = ORIENTED_EDGE( '', *, *, #2701, .T. );
#1890 = ORIENTED_EDGE( '', *, *, #2702, .T. );
#1891 = ORIENTED_EDGE( '', *, *, #2703, .F. );
#1892 = ORIENTED_EDGE( '', *, *, #2648, .F. );
#1893 = CARTESIAN_POINT( '', ( 2.95000000000000, -8.97386843989081, 1.34313215005231 ) );
#1894 = DIRECTION( '', ( 0.707106781186548, -0.0629494623349955, -0.704299201470323 ) );
#1895 = DIRECTION( '', ( 0.707106781186547, 0.0629494623349956, 0.704299201470324 ) );
#1896 = ORIENTED_EDGE( '', *, *, #2704, .F. );
#1897 = ORIENTED_EDGE( '', *, *, #2695, .F. );
#1898 = ORIENTED_EDGE( '', *, *, #2677, .T. );
#1899 = ORIENTED_EDGE( '', *, *, #2705, .T. );
#1900 = ORIENTED_EDGE( '', *, *, #2650, .F. );
#1901 = ORIENTED_EDGE( '', *, *, #2706, .F. );
#1902 = ORIENTED_EDGE( '', *, *, #2705, .F. );
#1903 = ORIENTED_EDGE( '', *, *, #2688, .T. );
#1904 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.95000000000000, 0.775000000000000 ) );
#1905 = DIRECTION( '', ( 1.00000000000000, -0.000000000000000, -0.000000000000000 ) );
#1906 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#1907 = ORIENTED_EDGE( '', *, *, #2629, .F. );
#1908 = ORIENTED_EDGE( '', *, *, #2624, .T. );
#1909 = ORIENTED_EDGE( '', *, *, #2707, .T. );
#1910 = ORIENTED_EDGE( '', *, *, #2708, .T. );
#1911 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.50000000000000 ) );
#1912 = DIRECTION( '', ( -1.00000000000000, 2.99204702930428E-032, -0.000000000000000 ) );
#1913 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#1914 = ORIENTED_EDGE( '', *, *, #2622, .T. );
#1915 = ORIENTED_EDGE( '', *, *, #2709, .T. );
#1916 = ORIENTED_EDGE( '', *, *, #2710, .T. );
#1917 = ORIENTED_EDGE( '', *, *, #2711, .F. );
#1918 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.95000000000000, 0.775000000000000 ) );
#1919 = DIRECTION( '', ( 1.00000000000000, -0.000000000000000, -0.000000000000000 ) );
#1920 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#1921 = ORIENTED_EDGE( '', *, *, #2621, .F. );
#1922 = ORIENTED_EDGE( '', *, *, #2712, .T. );
#1923 = ORIENTED_EDGE( '', *, *, #2713, .T. );
#1924 = ORIENTED_EDGE( '', *, *, #2701, .F. );
#1925 = ORIENTED_EDGE( '', *, *, #2647, .T. );
#1926 = ORIENTED_EDGE( '', *, *, #2696, .F. );
#1927 = ORIENTED_EDGE( '', *, *, #2714, .F. );
#1928 = ORIENTED_EDGE( '', *, *, #2709, .F. );
#1929 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.95000000000000, 0.213353597506737 ) );
#1930 = DIRECTION( '', ( 0.000000000000000, 0.996029482687872, -0.0890239833782447 ) );
#1931 = DIRECTION( '', ( 0.000000000000000, 0.0890239833782447, 0.996029482687872 ) );
#1932 = ORIENTED_EDGE( '', *, *, #2697, .F. );
#1933 = ORIENTED_EDGE( '', *, *, #2710, .F. );
#1934 = ORIENTED_EDGE( '', *, *, #2715, .T. );
#1935 = ORIENTED_EDGE( '', *, *, #2716, .T. );
#1936 = ORIENTED_EDGE( '', *, *, #2618, .F. );
#1937 = ORIENTED_EDGE( '', *, *, #2717, .T. );
#1938 = ORIENTED_EDGE( '', *, *, #2716, .F. );
#1939 = ORIENTED_EDGE( '', *, *, #2718, .F. );
#1940 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.95000000000000, 0.775000000000000 ) );
#1941 = DIRECTION( '', ( 1.00000000000000, -0.000000000000000, -0.000000000000000 ) );
#1942 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#1943 = ORIENTED_EDGE( '', *, *, #2719, .F. );
#1944 = ORIENTED_EDGE( '', *, *, #2698, .F. );
#1945 = ORIENTED_EDGE( '', *, *, #2717, .F. );
#1946 = ORIENTED_EDGE( '', *, *, #2617, .T. );
#1947 = ORIENTED_EDGE( '', *, *, #2720, .T. );
#1948 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.75000000000000, 0.775000000000000 ) );
#1949 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#1950 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#1951 = ORIENTED_EDGE( '', *, *, #2721, .F. );
#1952 = ORIENTED_EDGE( '', *, *, #2619, .T. );
#1953 = ORIENTED_EDGE( '', *, *, #2718, .T. );
#1954 = ORIENTED_EDGE( '', *, *, #2722, .F. );
#1955 = ORIENTED_EDGE( '', *, *, #2723, .F. );
#1956 = ORIENTED_EDGE( '', *, *, #2724, .F. );
#1957 = ORIENTED_EDGE( '', *, *, #2706, .T. );
#1958 = ORIENTED_EDGE( '', *, *, #2649, .F. );
#1959 = ORIENTED_EDGE( '', *, *, #2703, .T. );
#1960 = ORIENTED_EDGE( '', *, *, #2725, .F. );
#1961 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.80059557759684, 0.200000000000000 ) );
#1962 = DIRECTION( '', ( 0.000000000000000, 0.996029482687872, -0.0890239833782447 ) );
#1963 = DIRECTION( '', ( 0.000000000000000, 0.0890239833782447, 0.996029482687872 ) );
#1964 = ORIENTED_EDGE( '', *, *, #2620, .T. );
#1965 = ORIENTED_EDGE( '', *, *, #2721, .T. );
#1966 = ORIENTED_EDGE( '', *, *, #2726, .F. );
#1967 = ORIENTED_EDGE( '', *, *, #2712, .F. );
#1968 = CARTESIAN_POINT( '', ( -2.95000000000000, -8.97386843989081, 1.34313215005231 ) );
#1969 = DIRECTION( '', ( 0.707106781186548, 0.0629494623349955, 0.704299201470323 ) );
#1970 = DIRECTION( '', ( -0.707106781186547, 0.0629494623349956, 0.704299201470324 ) );
#1971 = ORIENTED_EDGE( '', *, *, #2682, .T. );
#1972 = ORIENTED_EDGE( '', *, *, #2644, .F. );
#1973 = ORIENTED_EDGE( '', *, *, #2631, .F. );
#1974 = ORIENTED_EDGE( '', *, *, #2683, .F. );
#1975 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.50000000000000 ) );
#1976 = DIRECTION( '', ( -1.00000000000000, 2.99204702930428E-032, -0.000000000000000 ) );
#1977 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#1978 = ORIENTED_EDGE( '', *, *, #2708, .F. );
#1979 = ORIENTED_EDGE( '', *, *, #2727, .T. );
#1980 = ORIENTED_EDGE( '', *, *, #2699, .F. );
#1981 = ORIENTED_EDGE( '', *, *, #2719, .T. );
#1982 = ORIENTED_EDGE( '', *, *, #2728, .T. );
#1983 = ORIENTED_EDGE( '', *, *, #2729, .T. );
#1984 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70000000000000, 1.50000000000000 ) );
#1985 = DIRECTION( '', ( 1.00000000000000, -2.99204702930428E-032, 3.18618382226490E-064 ) );
#1986 = DIRECTION( '', ( -2.99204702930428E-032, -1.00000000000000, -6.12303176911189E-017 ) );
#1987 = ORIENTED_EDGE( '', *, *, #2700, .F. );
#1988 = ORIENTED_EDGE( '', *, *, #2727, .F. );
#1989 = ORIENTED_EDGE( '', *, *, #2707, .F. );
#1990 = ORIENTED_EDGE( '', *, *, #2623, .F. );
#1991 = ORIENTED_EDGE( '', *, *, #2711, .T. );
#1992 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.90000000000000, 0.775000000000000 ) );
#1993 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#1994 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#1995 = ORIENTED_EDGE( '', *, *, #2730, .T. );
#1996 = ORIENTED_EDGE( '', *, *, #2731, .T. );
#1997 = ORIENTED_EDGE( '', *, *, #2724, .T. );
#1998 = ORIENTED_EDGE( '', *, *, #2732, .T. );
#1999 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70000000000000, 0.750000000000000 ) );
#2000 = DIRECTION( '', ( -0.000000000000000, 1.00000000000000, -0.000000000000000 ) );
#2001 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2002 = ORIENTED_EDGE( '', *, *, #2723, .T. );
#2003 = ORIENTED_EDGE( '', *, *, #2733, .T. );
#2004 = ORIENTED_EDGE( '', *, *, #2734, .T. );
#2005 = ORIENTED_EDGE( '', *, *, #2732, .F. );
#2006 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70000000000000, 0.650000000000000 ) );
#2007 = DIRECTION( '', ( 0.000000000000000, 1.38777878078145E-017, -1.00000000000000 ) );
#2008 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2009 = ORIENTED_EDGE( '', *, *, #2735, .F. );
#2010 = ORIENTED_EDGE( '', *, *, #2730, .F. );
#2011 = ORIENTED_EDGE( '', *, *, #2734, .F. );
#2012 = ORIENTED_EDGE( '', *, *, #2736, .F. );
#2013 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.90000000000000, 0.750000000000000 ) );
#2014 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#2015 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2016 = ORIENTED_EDGE( '', *, *, #2714, .T. );
#2017 = ORIENTED_EDGE( '', *, *, #2704, .T. );
#2018 = ORIENTED_EDGE( '', *, *, #2731, .F. );
#2019 = ORIENTED_EDGE( '', *, *, #2735, .T. );
#2020 = ORIENTED_EDGE( '', *, *, #2737, .F. );
#2021 = ORIENTED_EDGE( '', *, *, #2715, .F. );
#2022 = CARTESIAN_POINT( '', ( 0.000000000000000, -1.82549631466402, 0.763872002077720 ) );
#2023 = DIRECTION( '', ( -3.51833710430856E-017, 0.0890239833782441, 0.996029482687872 ) );
#2024 = DIRECTION( '', ( 0.000000000000000, -0.996029482687872, 0.0890239833782441 ) );
#2025 = ORIENTED_EDGE( '', *, *, #2722, .T. );
#2026 = ORIENTED_EDGE( '', *, *, #2737, .T. );
#2027 = ORIENTED_EDGE( '', *, *, #2736, .T. );
#2028 = ORIENTED_EDGE( '', *, *, #2733, .F. );
#2029 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70000000000000, 0.750000000000000 ) );
#2030 = DIRECTION( '', ( -0.000000000000000, 1.00000000000000, -0.000000000000000 ) );
#2031 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2032 = ORIENTED_EDGE( '', *, *, #2691, .T. );
#2033 = ORIENTED_EDGE( '', *, *, #2684, .T. );
#2034 = ORIENTED_EDGE( '', *, *, #2630, .F. );
#2035 = ORIENTED_EDGE( '', *, *, #2729, .F. );
#2036 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70000000000000, 0.750000000000000 ) );
#2037 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#2038 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2039 = ORIENTED_EDGE( '', *, *, #2692, .T. );
#2040 = ORIENTED_EDGE( '', *, *, #2728, .F. );
#2041 = ORIENTED_EDGE( '', *, *, #2720, .F. );
#2042 = ORIENTED_EDGE( '', *, *, #2616, .F. );
#2043 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.50000000000000 ) );
#2044 = DIRECTION( '', ( -1.00000000000000, 2.99204702930428E-032, -0.000000000000000 ) );
#2045 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2046 = ORIENTED_EDGE( '', *, *, #2738, .T. );
#2047 = ORIENTED_EDGE( '', *, *, #2739, .F. );
#2048 = ORIENTED_EDGE( '', *, *, #2693, .F. );
#2049 = ORIENTED_EDGE( '', *, *, #2614, .F. );
#2050 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 1.50000000000000 ) );
#2051 = DIRECTION( '', ( 1.00000000000000, -0.000000000000000, -0.000000000000000 ) );
#2052 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2053 = ORIENTED_EDGE( '', *, *, #2603, .F. );
#2054 = ORIENTED_EDGE( '', *, *, #2739, .T. );
#2055 = ORIENTED_EDGE( '', *, *, #2740, .T. );
#2056 = ORIENTED_EDGE( '', *, *, #2741, .T. );
#2057 = ORIENTED_EDGE( '', *, *, #2742, .T. );
#2058 = ORIENTED_EDGE( '', *, *, #2743, .F. );
#2059 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 1.50000000000000 ) );
#2060 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, -0.000000000000000 ) );
#2061 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 1.32614459778108E-014 ) );
#2062 = ORIENTED_EDGE( '', *, *, #2744, .F. );
#2063 = ORIENTED_EDGE( '', *, *, #2606, .T. );
#2064 = ORIENTED_EDGE( '', *, *, #2628, .T. );
#2065 = ORIENTED_EDGE( '', *, *, #2743, .T. );
#2066 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 1.50000000000000 ) );
#2067 = DIRECTION( '', ( 1.00000000000000, -0.000000000000000, -0.000000000000000 ) );
#2068 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2069 = ORIENTED_EDGE( '', *, *, #2670, .F. );
#2070 = ORIENTED_EDGE( '', *, *, #2745, .F. );
#2071 = ORIENTED_EDGE( '', *, *, #2633, .T. );
#2072 = ORIENTED_EDGE( '', *, *, #2643, .T. );
#2073 = ORIENTED_EDGE( '', *, *, #2746, .F. );
#2074 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.90000000000000, 1.50000000000000 ) );
#2075 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 1.31599711970654E-014 ) );
#2076 = DIRECTION( '', ( 0.000000000000000, -1.31599711970654E-014, -1.00000000000000 ) );
#2077 = ORIENTED_EDGE( '', *, *, #2642, .T. );
#2078 = ORIENTED_EDGE( '', *, *, #2662, .T. );
#2079 = ORIENTED_EDGE( '', *, *, #2747, .T. );
#2080 = ORIENTED_EDGE( '', *, *, #2746, .T. );
#2081 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.95000000000000, 0.775000000000000 ) );
#2082 = DIRECTION( '', ( -1.00000000000000, -1.38040321271469E-045, -1.04894090727380E-031 ) );
#2083 = DIRECTION( '', ( 4.66726145839586E-061, 1.00000000000000, -1.31599711970654E-014 ) );
#2084 = ORIENTED_EDGE( '', *, *, #2667, .F. );
#2085 = ORIENTED_EDGE( '', *, *, #2747, .F. );
#2086 = ORIENTED_EDGE( '', *, *, #2674, .T. );
#2087 = ORIENTED_EDGE( '', *, *, #2748, .T. );
#2088 = ORIENTED_EDGE( '', *, *, #2638, .F. );
#2089 = ORIENTED_EDGE( '', *, *, #2749, .F. );
#2090 = ORIENTED_EDGE( '', *, *, #2748, .F. );
#2091 = ORIENTED_EDGE( '', *, *, #2750, .T. );
#2092 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.95000000000000, 0.775000000000000 ) );
#2093 = DIRECTION( '', ( -1.00000000000000, -1.38040321271469E-045, -1.04894090727380E-031 ) );
#2094 = DIRECTION( '', ( 4.66726145839586E-061, 1.00000000000000, -1.31599711970654E-014 ) );
#2095 = ORIENTED_EDGE( '', *, *, #2751, .T. );
#2096 = ORIENTED_EDGE( '', *, *, #2665, .F. );
#2097 = ORIENTED_EDGE( '', *, *, #2752, .T. );
#2098 = ORIENTED_EDGE( '', *, *, #2741, .F. );
#2099 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 0.750000000000000 ) );
#2100 = DIRECTION( '', ( -0.000000000000000, -1.00000000000000, -0.000000000000000 ) );
#2101 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2102 = ORIENTED_EDGE( '', *, *, #2610, .F. );
#2103 = ORIENTED_EDGE( '', *, *, #2659, .T. );
#2104 = ORIENTED_EDGE( '', *, *, #2657, .T. );
#2105 = ORIENTED_EDGE( '', *, *, #2753, .F. );
#2106 = CARTESIAN_POINT( '', ( -2.95000000000000, 8.97386843989081, 1.34313215005202 ) );
#2107 = DIRECTION( '', ( -0.707106781186548, 0.0629494623349667, -0.704299201470326 ) );
#2108 = DIRECTION( '', ( -0.707106781186547, -0.0629494623349667, 0.704299201470326 ) );
#2109 = ORIENTED_EDGE( '', *, *, #2754, .F. );
#2110 = ORIENTED_EDGE( '', *, *, #2668, .F. );
#2111 = ORIENTED_EDGE( '', *, *, #2749, .T. );
#2112 = ORIENTED_EDGE( '', *, *, #2637, .F. );
#2113 = ORIENTED_EDGE( '', *, *, #2755, .F. );
#2114 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75000000000000, 1.50000000000000 ) );
#2115 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 1.31599711970654E-014 ) );
#2116 = DIRECTION( '', ( 0.000000000000000, -1.31599711970654E-014, -1.00000000000000 ) );
#2117 = ORIENTED_EDGE( '', *, *, #2611, .T. );
#2118 = ORIENTED_EDGE( '', *, *, #2753, .T. );
#2119 = ORIENTED_EDGE( '', *, *, #2656, .T. );
#2120 = ORIENTED_EDGE( '', *, *, #2653, .F. );
#2121 = ORIENTED_EDGE( '', *, *, #2639, .F. );
#2122 = ORIENTED_EDGE( '', *, *, #2750, .F. );
#2123 = ORIENTED_EDGE( '', *, *, #2756, .F. );
#2124 = ORIENTED_EDGE( '', *, *, #2757, .F. );
#2125 = ORIENTED_EDGE( '', *, *, #2758, .F. );
#2126 = ORIENTED_EDGE( '', *, *, #2759, .F. );
#2127 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75079410346242, 0.757195203324360 ) );
#2128 = DIRECTION( '', ( 0.000000000000000, -0.996029482687876, -0.0890239833782041 ) );
#2129 = DIRECTION( '', ( 0.000000000000000, 0.0890239833782041, -0.996029482687876 ) );
#2130 = ORIENTED_EDGE( '', *, *, #2673, .F. );
#2131 = ORIENTED_EDGE( '', *, *, #2760, .T. );
#2132 = ORIENTED_EDGE( '', *, *, #2761, .F. );
#2133 = ORIENTED_EDGE( '', *, *, #2756, .T. );
#2134 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 0.750000000000000 ) );
#2135 = DIRECTION( '', ( -0.000000000000000, -1.00000000000000, -0.000000000000000 ) );
#2136 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2137 = ORIENTED_EDGE( '', *, *, #2757, .T. );
#2138 = ORIENTED_EDGE( '', *, *, #2761, .T. );
#2139 = ORIENTED_EDGE( '', *, *, #2762, .T. );
#2140 = ORIENTED_EDGE( '', *, *, #2763, .F. );
#2141 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 0.650000000000000 ) );
#2142 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2143 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2144 = ORIENTED_EDGE( '', *, *, #2671, .F. );
#2145 = ORIENTED_EDGE( '', *, *, #2758, .T. );
#2146 = ORIENTED_EDGE( '', *, *, #2763, .T. );
#2147 = ORIENTED_EDGE( '', *, *, #2764, .T. );
#2148 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 0.750000000000000 ) );
#2149 = DIRECTION( '', ( -0.000000000000000, -1.00000000000000, -0.000000000000000 ) );
#2150 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2151 = ORIENTED_EDGE( '', *, *, #2672, .F. );
#2152 = ORIENTED_EDGE( '', *, *, #2764, .F. );
#2153 = ORIENTED_EDGE( '', *, *, #2762, .F. );
#2154 = ORIENTED_EDGE( '', *, *, #2760, .F. );
#2155 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.90000000000000, 0.750000000000000 ) );
#2156 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#2157 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2158 = ORIENTED_EDGE( '', *, *, #2612, .F. );
#2159 = ORIENTED_EDGE( '', *, *, #2759, .T. );
#2160 = ORIENTED_EDGE( '', *, *, #2666, .F. );
#2161 = ORIENTED_EDGE( '', *, *, #2765, .F. );
#2162 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.95000000000000, 0.775000000000000 ) );
#2163 = DIRECTION( '', ( -1.00000000000000, -1.38040321271469E-045, -1.04894090727380E-031 ) );
#2164 = DIRECTION( '', ( 4.66726145839586E-061, 1.00000000000000, -1.31599711970654E-014 ) );
#2165 = ORIENTED_EDGE( '', *, *, #2742, .F. );
#2166 = ORIENTED_EDGE( '', *, *, #2752, .F. );
#2167 = ORIENTED_EDGE( '', *, *, #2675, .F. );
#2168 = ORIENTED_EDGE( '', *, *, #2607, .F. );
#2169 = ORIENTED_EDGE( '', *, *, #2744, .T. );
#2170 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.90000000000000, 1.50000000000000 ) );
#2171 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 1.31599711970654E-014 ) );
#2172 = DIRECTION( '', ( 0.000000000000000, -1.31599711970654E-014, -1.00000000000000 ) );
#2173 = ORIENTED_EDGE( '', *, *, #2754, .T. );
#2174 = ORIENTED_EDGE( '', *, *, #2766, .F. );
#2175 = ORIENTED_EDGE( '', *, *, #2601, .T. );
#2176 = ORIENTED_EDGE( '', *, *, #2634, .T. );
#2177 = ORIENTED_EDGE( '', *, *, #2745, .T. );
#2178 = ORIENTED_EDGE( '', *, *, #2669, .F. );
#2179 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 1.70000000000000 ) );
#2180 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2181 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2182 = ORIENTED_EDGE( '', *, *, #2625, .F. );
#2183 = ORIENTED_EDGE( '', *, *, #2690, .T. );
#2184 = CARTESIAN_POINT( '', ( 0.000000000000000, -4.33680868994202E-016, 7.27392603756734 ) );
#2185 = DIRECTION( '', ( -0.000000000000000, -0.000000000000000, 1.00000000000000 ) );
#2186 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2187 = ORIENTED_EDGE( '', *, *, #2726, .T. );
#2188 = ORIENTED_EDGE( '', *, *, #2725, .T. );
#2189 = ORIENTED_EDGE( '', *, *, #2702, .F. );
#2190 = ORIENTED_EDGE( '', *, *, #2713, .F. );
#2191 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.80059557759684, 0.200000000000000 ) );
#2192 = DIRECTION( '', ( -0.000000000000000, 0.0890239833782447, 0.996029482687872 ) );
#2193 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2194 = ORIENTED_EDGE( '', *, *, #2751, .F. );
#2195 = ORIENTED_EDGE( '', *, *, #2740, .F. );
#2196 = ORIENTED_EDGE( '', *, *, #2738, .F. );
#2197 = ORIENTED_EDGE( '', *, *, #2613, .T. );
#2198 = ORIENTED_EDGE( '', *, *, #2765, .T. );
#2199 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75000000000000, 1.50000000000000 ) );
#2200 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 1.31599711970654E-014 ) );
#2201 = DIRECTION( '', ( 0.000000000000000, -1.31599711970654E-014, -1.00000000000000 ) );
#2202 = ORIENTED_EDGE( '', *, *, #2694, .T. );
#2203 = ORIENTED_EDGE( '', *, *, #2766, .T. );
#2204 = ORIENTED_EDGE( '', *, *, #2755, .T. );
#2205 = ORIENTED_EDGE( '', *, *, #2636, .T. );
#2206 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 1.50000000000000 ) );
#2207 = DIRECTION( '', ( 1.00000000000000, -0.000000000000000, -0.000000000000000 ) );
#2208 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2209 = ORIENTED_EDGE( '', *, *, #2767, .F. );
#2210 = ORIENTED_EDGE( '', *, *, #2768, .F. );
#2211 = ORIENTED_EDGE( '', *, *, #2769, .T. );
#2212 = ORIENTED_EDGE( '', *, *, #2770, .T. );
#2213 = CARTESIAN_POINT( '', ( 3.65000000000000, 0.500000000000008, 0.150000000000000 ) );
#2214 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2215 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2216 = ORIENTED_EDGE( '', *, *, #2767, .T. );
#2217 = ORIENTED_EDGE( '', *, *, #2771, .T. );
#2218 = ORIENTED_EDGE( '', *, *, #2772, .T. );
#2219 = ORIENTED_EDGE( '', *, *, #2773, .T. );
#2220 = CARTESIAN_POINT( '', ( 3.65000000000000, -0.500000000000008, 0.000000000000000 ) );
#2221 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2222 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2223 = ORIENTED_EDGE( '', *, *, #2769, .F. );
#2224 = ORIENTED_EDGE( '', *, *, #2774, .F. );
#2225 = ORIENTED_EDGE( '', *, *, #2775, .F. );
#2226 = ORIENTED_EDGE( '', *, *, #2776, .F. );
#2227 = CARTESIAN_POINT( '', ( 3.65000000000000, -0.500000000000008, 0.150000000000000 ) );
#2228 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2229 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2230 = ORIENTED_EDGE( '', *, *, #2773, .F. );
#2231 = ORIENTED_EDGE( '', *, *, #2777, .F. );
#2232 = ORIENTED_EDGE( '', *, *, #2774, .T. );
#2233 = ORIENTED_EDGE( '', *, *, #2768, .T. );
#2234 = CARTESIAN_POINT( '', ( 2.00000000000000, 0.500000000000008, 0.150000000000000 ) );
#2235 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#2236 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2237 = ORIENTED_EDGE( '', *, *, #2772, .F. );
#2238 = ORIENTED_EDGE( '', *, *, #2778, .F. );
#2239 = ORIENTED_EDGE( '', *, *, #2775, .T. );
#2240 = ORIENTED_EDGE( '', *, *, #2777, .T. );
#2241 = CARTESIAN_POINT( '', ( 2.00000000000000, -0.500000000000008, 0.150000000000000 ) );
#2242 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2243 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2244 = ORIENTED_EDGE( '', *, *, #2771, .F. );
#2245 = ORIENTED_EDGE( '', *, *, #2770, .F. );
#2246 = ORIENTED_EDGE( '', *, *, #2776, .T. );
#2247 = ORIENTED_EDGE( '', *, *, #2778, .T. );
#2248 = CARTESIAN_POINT( '', ( 3.65000000000000, -0.500000000000008, 0.150000000000000 ) );
#2249 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#2250 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2251 = ORIENTED_EDGE( '', *, *, #2779, .T. );
#2252 = ORIENTED_EDGE( '', *, *, #2780, .F. );
#2253 = ORIENTED_EDGE( '', *, *, #2781, .F. );
#2254 = ORIENTED_EDGE( '', *, *, #2782, .T. );
#2255 = CARTESIAN_POINT( '', ( -3.65000000000000, 0.500000000000008, 0.150000000000000 ) );
#2256 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2257 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2258 = ORIENTED_EDGE( '', *, *, #2783, .T. );
#2259 = ORIENTED_EDGE( '', *, *, #2782, .F. );
#2260 = ORIENTED_EDGE( '', *, *, #2784, .F. );
#2261 = ORIENTED_EDGE( '', *, *, #2785, .T. );
#2262 = CARTESIAN_POINT( '', ( -2.00000000000000, 0.500000000000008, 0.150000000000000 ) );
#2263 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#2264 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2265 = ORIENTED_EDGE( '', *, *, #2779, .F. );
#2266 = ORIENTED_EDGE( '', *, *, #2783, .F. );
#2267 = ORIENTED_EDGE( '', *, *, #2786, .F. );
#2268 = ORIENTED_EDGE( '', *, *, #2787, .F. );
#2269 = CARTESIAN_POINT( '', ( -3.65000000000000, -0.500000000000008, 0.000000000000000 ) );
#2270 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2271 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2272 = ORIENTED_EDGE( '', *, *, #2781, .T. );
#2273 = ORIENTED_EDGE( '', *, *, #2788, .T. );
#2274 = ORIENTED_EDGE( '', *, *, #2789, .T. );
#2275 = ORIENTED_EDGE( '', *, *, #2784, .T. );
#2276 = CARTESIAN_POINT( '', ( -3.65000000000000, -0.500000000000008, 0.150000000000000 ) );
#2277 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2278 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2279 = ORIENTED_EDGE( '', *, *, #2786, .T. );
#2280 = ORIENTED_EDGE( '', *, *, #2785, .F. );
#2281 = ORIENTED_EDGE( '', *, *, #2789, .F. );
#2282 = ORIENTED_EDGE( '', *, *, #2790, .T. );
#2283 = CARTESIAN_POINT( '', ( -2.00000000000000, -0.500000000000008, 0.150000000000000 ) );
#2284 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2285 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2286 = ORIENTED_EDGE( '', *, *, #2787, .T. );
#2287 = ORIENTED_EDGE( '', *, *, #2790, .F. );
#2288 = ORIENTED_EDGE( '', *, *, #2788, .F. );
#2289 = ORIENTED_EDGE( '', *, *, #2780, .T. );
#2290 = CARTESIAN_POINT( '', ( -3.65000000000000, -0.500000000000008, 0.150000000000000 ) );
#2291 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#2292 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2293 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2294 = CARTESIAN_POINT( '', ( -3.00000000000000, 0.500000000000000, 0.000000000000000 ) );
#2295 = CARTESIAN_POINT( '', ( -2.00000000000000, 0.500000000000000, 0.000000000000000 ) );
#2296 = CARTESIAN_POINT( '', ( -3.00000000000000, 0.500000000000000, 0.000000000000000 ) );
#2297 = VECTOR( '', #2791, 1000.00000000000 );
#2298 = CARTESIAN_POINT( '', ( -2.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#2299 = CARTESIAN_POINT( '', ( -2.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#2300 = VECTOR( '', #2792, 1000.00000000000 );
#2301 = CARTESIAN_POINT( '', ( -3.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#2302 = CARTESIAN_POINT( '', ( -3.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#2303 = VECTOR( '', #2793, 1000.00000000000 );
#2304 = CARTESIAN_POINT( '', ( -3.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#2305 = VECTOR( '', #2794, 1000.00000000000 );
#2306 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2307 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.75000000000000, 1.52528015217604 ) );
#2308 = CARTESIAN_POINT( '', ( -1.16867813823029, 1.75632186176971, 1.50732743305448 ) );
#2309 = AXIS2_PLACEMENT_3D( '', #2795, #2796, #2797 );
#2310 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70686168865201, 1.55000000000000 ) );
#2311 = AXIS2_PLACEMENT_3D( '', #2798, #2799, #2800 );
#2312 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70686168865201, 1.55000000000000 ) );
#2313 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.70686168865201, 1.55000000000000 ) );
#2314 = VECTOR( '', #2801, 1000.00000000000 );
#2315 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75000000000000, 1.52528015217605 ) );
#2316 = AXIS2_PLACEMENT_3D( '', #2802, #2803, #2804 );
#2317 = CARTESIAN_POINT( '', ( 1.16867813823029, 1.75632186176971, 1.50732743305448 ) );
#2318 = AXIS2_PLACEMENT_3D( '', #2805, #2806, #2807 );
#2319 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75632186176971, 1.50732743305448 ) );
#2320 = VECTOR( '', #2808, 1000.00000000000 );
#2321 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2322 = CARTESIAN_POINT( '', ( 2.00000000000000, -0.500000000000000, 0.000000000000000 ) );
#2323 = CARTESIAN_POINT( '', ( 3.00000000000000, -0.500000000000000, 0.000000000000000 ) );
#2324 = CARTESIAN_POINT( '', ( 2.00000000000000, -0.500000000000000, 0.000000000000000 ) );
#2325 = VECTOR( '', #2809, 1000.00000000000 );
#2326 = CARTESIAN_POINT( '', ( 2.00000000000000, -0.500000000000000, 0.151000000000000 ) );
#2327 = CARTESIAN_POINT( '', ( 2.00000000000000, -0.500000000000000, 0.151000000000000 ) );
#2328 = VECTOR( '', #2810, 1000.00000000000 );
#2329 = CARTESIAN_POINT( '', ( 3.00000000000000, -0.500000000000000, 0.151000000000000 ) );
#2330 = CARTESIAN_POINT( '', ( 2.00000000000000, -0.500000000000000, 0.151000000000000 ) );
#2331 = VECTOR( '', #2811, 1000.00000000000 );
#2332 = CARTESIAN_POINT( '', ( 3.00000000000000, -0.500000000000000, 0.151000000000000 ) );
#2333 = VECTOR( '', #2812, 1000.00000000000 );
#2334 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2335 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.75000000000000, 1.50000000000000 ) );
#2336 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.75000000000000, 7.47930478052606 ) );
#2337 = VECTOR( '', #2813, 1000.00000000000 );
#2338 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 1.55000000000000 ) );
#2339 = AXIS2_PLACEMENT_3D( '', #2814, #2815, #2816 );
#2340 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.75000000000000, 1.55000000000000 ) );
#2341 = VECTOR( '', #2817, 1000.00000000000 );
#2342 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2343 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70686168865201, 1.55000000000000 ) );
#2344 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 1.52528015217604 ) );
#2345 = AXIS2_PLACEMENT_3D( '', #2818, #2819, #2820 );
#2346 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 1.50000000000000 ) );
#2347 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 7.47930478052606 ) );
#2348 = VECTOR( '', #2821, 1000.00000000000 );
#2349 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70000000000000, 1.55000000000000 ) );
#2350 = AXIS2_PLACEMENT_3D( '', #2822, #2823, #2824 );
#2351 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 1.55000000000000 ) );
#2352 = VECTOR( '', #2825, 1000.00000000000 );
#2353 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2354 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 1.55000000000000 ) );
#2355 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75000000000000, 1.50000000000000 ) );
#2356 = AXIS2_PLACEMENT_3D( '', #2826, #2827, #2828 );
#2357 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75000000000000, 7.47930478052606 ) );
#2358 = VECTOR( '', #2829, 1000.00000000000 );
#2359 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 1.55000000000000 ) );
#2360 = VECTOR( '', #2830, 1000.00000000000 );
#2361 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2362 = CARTESIAN_POINT( '', ( 2.00000000000000, 0.500000000000000, 0.000000000000000 ) );
#2363 = CARTESIAN_POINT( '', ( 2.00000000000000, 0.500000000000000, 0.000000000000000 ) );
#2364 = VECTOR( '', #2831, 1000.00000000000 );
#2365 = CARTESIAN_POINT( '', ( 2.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#2366 = CARTESIAN_POINT( '', ( 2.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#2367 = VECTOR( '', #2832, 1000.00000000000 );
#2368 = CARTESIAN_POINT( '', ( 2.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#2369 = VECTOR( '', #2833, 1000.00000000000 );
#2370 = COLOUR_RGB( '', 1.00000000000000, 1.00000000000000, 1.00000000000000 );
#2371 = CARTESIAN_POINT( '', ( -0.850000000000000, 0.000000000000000, 2.50000000000000 ) );
#2372 = AXIS2_PLACEMENT_3D( '', #2834, #2835, #2836 );
#2373 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2374 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.75000000000000, 0.775000000000000 ) );
#2375 = CARTESIAN_POINT( '', ( -1.07500000000000, -1.85000000000000, 0.775000000000000 ) );
#2376 = CARTESIAN_POINT( '', ( 0.0499999999999991, -2.97500000000000, 0.775000000000000 ) );
#2377 = VECTOR( '', #2837, 1000.00000000000 );
#2378 = CARTESIAN_POINT( '', ( -1.07500000000000, -1.85000000000000, 0.875000000000000 ) );
#2379 = CARTESIAN_POINT( '', ( -1.07500000000000, -1.85000000000000, 7.47930478052606 ) );
#2380 = VECTOR( '', #2838, 1000.00000000000 );
#2381 = CARTESIAN_POINT( '', ( -1.16867813823029, -1.75632186176971, 1.50732743305448 ) );
#2382 = CARTESIAN_POINT( '', ( -1.12559132720105, -1.79940867279895, 1.21649145860709 ) );
#2383 = VECTOR( '', #2839, 1000.00000000000 );
#2384 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.75000000000000, 1.52528015217605 ) );
#2385 = AXIS2_PLACEMENT_3D( '', #2840, #2841, #2842 );
#2386 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.75000000000000, 1.50000000000000 ) );
#2387 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.75000000000000, 7.47930478052606 ) );
#2388 = VECTOR( '', #2843, 1000.00000000000 );
#2389 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.75000000000000, 0.775000000000000 ) );
#2390 = VECTOR( '', #2844, 1000.00000000000 );
#2391 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2392 = CARTESIAN_POINT( '', ( 1.07500000000000, 1.85000000000000, 0.875000000000000 ) );
#2393 = CARTESIAN_POINT( '', ( 1.07500000000000, 1.85000000000000, 0.775000000000000 ) );
#2394 = CARTESIAN_POINT( '', ( 1.07500000000000, 1.85000000000000, 7.47930478052606 ) );
#2395 = VECTOR( '', #2845, 1000.00000000000 );
#2396 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75000000000000, 1.55000000000000 ) );
#2397 = VECTOR( '', #2846, 1000.00000000000 );
#2398 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75000000000000, 0.775000000000000 ) );
#2399 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75000000000000, 0.775000000000000 ) );
#2400 = VECTOR( '', #2847, 1000.00000000000 );
#2401 = CARTESIAN_POINT( '', ( 1.12500000000000, 1.80000000000000, 0.775000000000000 ) );
#2402 = VECTOR( '', #2848, 1000.00000000000 );
#2403 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2404 = CARTESIAN_POINT( '', ( 1.07500000000000, -1.85000000000000, 0.875000000000000 ) );
#2405 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.85000000000000, 0.875000000000000 ) );
#2406 = VECTOR( '', #2849, 1000.00000000000 );
#2407 = CARTESIAN_POINT( '', ( 1.16867813823030, -1.75632186176971, 1.50732743305448 ) );
#2408 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 1.55000000000000 ) );
#2409 = VECTOR( '', #2850, 1000.00000000000 );
#2410 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.75632186176971, 1.50732743305448 ) );
#2411 = VECTOR( '', #2851, 1000.00000000000 );
#2412 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2413 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 0.775000000000000 ) );
#2414 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 0.775000000000000 ) );
#2415 = VECTOR( '', #2852, 1000.00000000000 );
#2416 = CARTESIAN_POINT( '', ( 1.07500000000000, -1.85000000000000, 0.775000000000000 ) );
#2417 = CARTESIAN_POINT( '', ( 1.12500000000000, -1.80000000000000, 0.775000000000000 ) );
#2418 = VECTOR( '', #2853, 1000.00000000000 );
#2419 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.85000000000000, 0.775000000000000 ) );
#2420 = VECTOR( '', #2854, 1000.00000000000 );
#2421 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2422 = CARTESIAN_POINT( '', ( 3.00000000000000, -1.70000000000000, 1.55000000000000 ) );
#2423 = CARTESIAN_POINT( '', ( 3.00000000000000, 1.70000000000000, 1.55000000000000 ) );
#2424 = CARTESIAN_POINT( '', ( 3.00000000000000, -1.75000000000000, 1.55000000000000 ) );
#2425 = VECTOR( '', #2855, 1000.00000000000 );
#2426 = CARTESIAN_POINT( '', ( 3.00000000000000, -1.75000000000000, 1.50000000000000 ) );
#2427 = AXIS2_PLACEMENT_3D( '', #2856, #2857, #2858 );
#2428 = CARTESIAN_POINT( '', ( 3.00000000000000, -1.75000000000000, 0.000000000000000 ) );
#2429 = CARTESIAN_POINT( '', ( 3.00000000000000, -1.75000000000000, 1.55000000000000 ) );
#2430 = VECTOR( '', #2859, 1000.00000000000 );
#2431 = CARTESIAN_POINT( '', ( 3.00000000000000, -1.75000000000000, 0.000000000000000 ) );
#2432 = VECTOR( '', #2860, 1000.00000000000 );
#2433 = CARTESIAN_POINT( '', ( 3.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#2434 = CARTESIAN_POINT( '', ( 3.00000000000000, -0.500000000000000, 0.151000000000000 ) );
#2435 = VECTOR( '', #2861, 1000.00000000000 );
#2436 = CARTESIAN_POINT( '', ( 3.00000000000000, 0.500000000000000, 0.000000000000000 ) );
#2437 = CARTESIAN_POINT( '', ( 3.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#2438 = VECTOR( '', #2862, 1000.00000000000 );
#2439 = CARTESIAN_POINT( '', ( 3.00000000000000, 1.75000000000000, 0.000000000000000 ) );
#2440 = CARTESIAN_POINT( '', ( 3.00000000000000, -1.75000000000000, 0.000000000000000 ) );
#2441 = VECTOR( '', #2863, 1000.00000000000 );
#2442 = CARTESIAN_POINT( '', ( 3.00000000000000, 1.75000000000000, 1.50000000000000 ) );
#2443 = CARTESIAN_POINT( '', ( 3.00000000000000, 1.75000000000000, 1.55000000000000 ) );
#2444 = VECTOR( '', #2864, 1000.00000000000 );
#2445 = AXIS2_PLACEMENT_3D( '', #2865, #2866, #2867 );
#2446 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2447 = CARTESIAN_POINT( '', ( -3.00000000000000, -0.500000000000000, 0.151000000000000 ) );
#2448 = CARTESIAN_POINT( '', ( -3.00000000000000, -0.500000000000000, 0.151000000000000 ) );
#2449 = VECTOR( '', #2868, 1000.00000000000 );
#2450 = CARTESIAN_POINT( '', ( -2.00000000000000, -0.500000000000000, 0.151000000000000 ) );
#2451 = CARTESIAN_POINT( '', ( -2.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#2452 = VECTOR( '', #2869, 1000.00000000000 );
#2453 = CARTESIAN_POINT( '', ( -2.00000000000000, -0.500000000000000, 0.151000000000000 ) );
#2454 = VECTOR( '', #2870, 1000.00000000000 );
#2455 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2456 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70000000000000, 1.55000000000000 ) );
#2457 = AXIS2_PLACEMENT_3D( '', #2871, #2872, #2873 );
#2458 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70686168865201, 1.55000000000000 ) );
#2459 = AXIS2_PLACEMENT_3D( '', #2874, #2875, #2876 );
#2460 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.75000000000000, 1.55000000000000 ) );
#2461 = VECTOR( '', #2877, 1000.00000000000 );
#2462 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2463 = CARTESIAN_POINT( '', ( -1.07500000000000, 1.85000000000000, 0.875000000000000 ) );
#2464 = CARTESIAN_POINT( '', ( -1.12559132720105, 1.79940867279895, 1.21649145860709 ) );
#2465 = VECTOR( '', #2878, 1000.00000000000 );
#2466 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.85000000000000, 0.875000000000000 ) );
#2467 = VECTOR( '', #2879, 1000.00000000000 );
#2468 = COLOUR_RGB( '', 1.00000000000000, 1.00000000000000, 1.00000000000000 );
#2469 = CARTESIAN_POINT( '', ( -0.950000000000000, 0.000000000000000, 2.40000000000000 ) );
#2470 = AXIS2_PLACEMENT_3D( '', #2880, #2881, #2882 );
#2471 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2472 = CARTESIAN_POINT( '', ( 3.00000000000000, 1.75000000000000, 1.50000000000000 ) );
#2473 = VECTOR( '', #2883, 1000.00000000000 );
#2474 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 1.55000000000000 ) );
#2475 = VECTOR( '', #2884, 1000.00000000000 );
#2476 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2477 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.75000000000000, 0.775000000000000 ) );
#2478 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.75000000000000, 0.775000000000000 ) );
#2479 = VECTOR( '', #2885, 1000.00000000000 );
#2480 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75000000000000, 0.775000000000000 ) );
#2481 = VECTOR( '', #2886, 1000.00000000000 );
#2482 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.75000000000000, 0.000000000000000 ) );
#2483 = CARTESIAN_POINT( '', ( 3.00000000000000, 1.75000000000000, 0.000000000000000 ) );
#2484 = VECTOR( '', #2887, 1000.00000000000 );
#2485 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.75000000000000, 1.50000000000000 ) );
#2486 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.75000000000000, 1.55000000000000 ) );
#2487 = VECTOR( '', #2888, 1000.00000000000 );
#2488 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.75000000000000, 1.50000000000000 ) );
#2489 = VECTOR( '', #2889, 1000.00000000000 );
#2490 = COLOUR_RGB( '', 1.00000000000000, 1.00000000000000, 1.00000000000000 );
#2491 = CARTESIAN_POINT( '', ( 0.950000000000000, 0.000000000000000, 0.775000000000000 ) );
#2492 = AXIS2_PLACEMENT_3D( '', #2890, #2891, #2892 );
#2493 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2494 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.70000000000000, 1.55000000000000 ) );
#2495 = AXIS2_PLACEMENT_3D( '', #2893, #2894, #2895 );
#2496 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.70000000000000, 1.55000000000000 ) );
#2497 = VECTOR( '', #2896, 1000.00000000000 );
#2498 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2499 = CARTESIAN_POINT( '', ( -1.07500000000000, 1.85000000000000, 0.775000000000000 ) );
#2500 = CARTESIAN_POINT( '', ( -1.07500000000000, 1.85000000000000, 7.47930478052606 ) );
#2501 = VECTOR( '', #2897, 1000.00000000000 );
#2502 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.85000000000000, 0.775000000000000 ) );
#2503 = VECTOR( '', #2898, 1000.00000000000 );
#2504 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2505 = CARTESIAN_POINT( '', ( 0.0499999999999991, 2.97500000000000, 0.775000000000000 ) );
#2506 = VECTOR( '', #2899, 1000.00000000000 );
#2507 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2508 = CARTESIAN_POINT( '', ( -3.00000000000000, -1.75000000000000, 1.50000000000000 ) );
#2509 = CARTESIAN_POINT( '', ( -3.00000000000000, -1.70000000000000, 1.55000000000000 ) );
#2510 = AXIS2_PLACEMENT_3D( '', #2900, #2901, #2902 );
#2511 = CARTESIAN_POINT( '', ( -3.00000000000000, -1.75000000000000, 1.50000000000000 ) );
#2512 = VECTOR( '', #2903, 1000.00000000000 );
#2513 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70000000000000, 1.55000000000000 ) );
#2514 = VECTOR( '', #2904, 1000.00000000000 );
#2515 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2516 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70686168865201, 1.55000000000000 ) );
#2517 = VECTOR( '', #2905, 1000.00000000000 );
#2518 = CARTESIAN_POINT( '', ( 3.00000000000000, -1.70000000000000, 1.55000000000000 ) );
#2519 = VECTOR( '', #2906, 1000.00000000000 );
#2520 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.75000000000000, 1.55000000000000 ) );
#2521 = VECTOR( '', #2907, 1000.00000000000 );
#2522 = CARTESIAN_POINT( '', ( -2.05000000000000, -1.50000000000000, 1.55000000000000 ) );
#2523 = CARTESIAN_POINT( '', ( -2.05000000000000, 1.50000000000000, 1.55000000000000 ) );
#2524 = CARTESIAN_POINT( '', ( -2.05000000000000, -1.75000000000000, 1.55000000000000 ) );
#2525 = VECTOR( '', #2908, 1000.00000000000 );
#2526 = CARTESIAN_POINT( '', ( 2.05000000000000, 1.50000000000000, 1.55000000000000 ) );
#2527 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.50000000000000, 1.55000000000000 ) );
#2528 = VECTOR( '', #2909, 1000.00000000000 );
#2529 = CARTESIAN_POINT( '', ( 2.05000000000000, -1.50000000000000, 1.55000000000000 ) );
#2530 = CARTESIAN_POINT( '', ( 2.05000000000000, -1.75000000000000, 1.55000000000000 ) );
#2531 = VECTOR( '', #2910, 1000.00000000000 );
#2532 = CARTESIAN_POINT( '', ( -3.00000000000000, -1.50000000000000, 1.55000000000000 ) );
#2533 = VECTOR( '', #2911, 1000.00000000000 );
#2534 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2535 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.75000000000000, 0.000000000000000 ) );
#2536 = VECTOR( '', #2912, 1000.00000000000 );
#2537 = CARTESIAN_POINT( '', ( -3.00000000000000, -0.500000000000000, 0.000000000000000 ) );
#2538 = CARTESIAN_POINT( '', ( -3.00000000000000, -0.500000000000000, 0.151000000000000 ) );
#2539 = VECTOR( '', #2913, 1000.00000000000 );
#2540 = CARTESIAN_POINT( '', ( -3.00000000000000, -1.75000000000000, 0.000000000000000 ) );
#2541 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.75000000000000, 0.000000000000000 ) );
#2542 = VECTOR( '', #2914, 1000.00000000000 );
#2543 = CARTESIAN_POINT( '', ( -3.00000000000000, -1.75000000000000, 1.55000000000000 ) );
#2544 = VECTOR( '', #2915, 1000.00000000000 );
#2545 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2546 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 1.50000000000000 ) );
#2547 = VECTOR( '', #2916, 1000.00000000000 );
#2548 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2549 = CARTESIAN_POINT( '', ( -2.00000000000000, -0.500000000000000, 0.000000000000000 ) );
#2550 = CARTESIAN_POINT( '', ( -2.00000000000000, 0.500000000000000, 0.000000000000000 ) );
#2551 = VECTOR( '', #2917, 1000.00000000000 );
#2552 = CARTESIAN_POINT( '', ( 3.00000000000000, 0.500000000000000, 0.000000000000000 ) );
#2553 = VECTOR( '', #2918, 1000.00000000000 );
#2554 = CARTESIAN_POINT( '', ( -3.00000000000000, -1.75000000000000, 0.000000000000000 ) );
#2555 = VECTOR( '', #2919, 1000.00000000000 );
#2556 = CARTESIAN_POINT( '', ( -2.00000000000000, -0.500000000000000, 0.000000000000000 ) );
#2557 = VECTOR( '', #2920, 1000.00000000000 );
#2558 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2559 = CARTESIAN_POINT( '', ( 3.00000000000000, 0.500000000000000, 0.151000000000000 ) );
#2560 = VECTOR( '', #2921, 1000.00000000000 );
#2561 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2562 = CARTESIAN_POINT( '', ( 2.05000000000000, 1.50000000000000, 0.775000000000000 ) );
#2563 = CARTESIAN_POINT( '', ( -2.05000000000000, 1.50000000000000, 0.775000000000000 ) );
#2564 = CARTESIAN_POINT( '', ( 2.05000000000000, 1.50000000000000, 0.775000000000000 ) );
#2565 = VECTOR( '', #2922, 1000.00000000000 );
#2566 = CARTESIAN_POINT( '', ( -2.05000000000000, -1.50000000000000, 0.775000000000000 ) );
#2567 = CARTESIAN_POINT( '', ( -2.05000000000000, 1.50000000000000, 0.775000000000000 ) );
#2568 = VECTOR( '', #2923, 1000.00000000000 );
#2569 = CARTESIAN_POINT( '', ( 2.05000000000000, -1.50000000000000, 0.775000000000000 ) );
#2570 = CARTESIAN_POINT( '', ( -2.05000000000000, -1.50000000000000, 0.775000000000000 ) );
#2571 = VECTOR( '', #2924, 1000.00000000000 );
#2572 = CARTESIAN_POINT( '', ( 2.05000000000000, -1.50000000000000, 0.775000000000000 ) );
#2573 = VECTOR( '', #2925, 1000.00000000000 );
#2574 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2575 = CARTESIAN_POINT( '', ( -2.05000000000000, -1.50000000000000, 7.89205697602598 ) );
#2576 = VECTOR( '', #2926, 1000.00000000000 );
#2577 = CARTESIAN_POINT( '', ( -2.05000000000000, 1.50000000000000, 7.89205697602598 ) );
#2578 = VECTOR( '', #2927, 1000.00000000000 );
#2579 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2580 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2581 = CARTESIAN_POINT( '', ( 1.07500000000000, -1.85000000000000, 7.47930478052606 ) );
#2582 = VECTOR( '', #2928, 1000.00000000000 );
#2583 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2584 = CARTESIAN_POINT( '', ( 2.05000000000000, 1.50000000000000, 7.89205697602598 ) );
#2585 = VECTOR( '', #2929, 1000.00000000000 );
#2586 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2587 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 0.775000000000000 ) );
#2588 = VECTOR( '', #2930, 1000.00000000000 );
#2589 = AXIS2_PLACEMENT_3D( '', #2931, #2932, #2933 );
#2590 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2591 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2592 = CARTESIAN_POINT( '', ( 2.05000000000000, -1.50000000000000, 7.89205697602598 ) );
#2593 = VECTOR( '', #2934, 1000.00000000000 );
#2594 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2595 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2596 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2597 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2598 = CARTESIAN_POINT( '', ( -2.00000000000000, -0.500000000000000, 0.151000000000000 ) );
#2599 = VECTOR( '', #2935, 1000.00000000000 );
#2600 = COLOUR_RGB( '', 0.200000002980232, 0.200000002980232, 0.200000002980232 );
#2601 = EDGE_CURVE( '', #2936, #2937, #2938, .T. );
#2602 = EDGE_CURVE( '', #2939, #2936, #2940, .T. );
#2603 = EDGE_CURVE( '', #2939, #2941, #2942, .T. );
#2604 = EDGE_CURVE( '', #2941, #2937, #2943, .F. );
#2605 = EDGE_CURVE( '', #2944, #2945, #2946, .T. );
#2606 = EDGE_CURVE( '', #2947, #2945, #2948, .T. );
#2607 = EDGE_CURVE( '', #2947, #2949, #2950, .T. );
#2608 = EDGE_CURVE( '', #2951, #2949, #2952, .F. );
#2609 = EDGE_CURVE( '', #2951, #2953, #2954, .T. );
#2610 = EDGE_CURVE( '', #2953, #2955, #2956, .T. );
#2611 = EDGE_CURVE( '', #2957, #2955, #2958, .T. );
#2612 = EDGE_CURVE( '', #2957, #2959, #2960, .T. );
#2613 = EDGE_CURVE( '', #2961, #2959, #2962, .T. );
#2614 = EDGE_CURVE( '', #2961, #2963, #2964, .F. );
#2615 = EDGE_CURVE( '', #2965, #2963, #2966, .T. );
#2616 = EDGE_CURVE( '', #2965, #2967, #2968, .T. );
#2617 = EDGE_CURVE( '', #2969, #2967, #2970, .T. );
#2618 = EDGE_CURVE( '', #2969, #2971, #2972, .T. );
#2619 = EDGE_CURVE( '', #2973, #2971, #2974, .T. );
#2620 = EDGE_CURVE( '', #2975, #2973, #2976, .T. );
#2621 = EDGE_CURVE( '', #2975, #2977, #2978, .T. );
#2622 = EDGE_CURVE( '', #2979, #2977, #2980, .T. );
#2623 = EDGE_CURVE( '', #2979, #2981, #2982, .T. );
#2624 = EDGE_CURVE( '', #2944, #2981, #2983, .T. );
#2625 = EDGE_CURVE( '', #2984, #2984, #2985, .T. );
#2626 = EDGE_CURVE( '', #2986, #2987, #2988, .T. );
#2627 = EDGE_CURVE( '', #2987, #2937, #2989, .T. );
#2628 = EDGE_CURVE( '', #2945, #2941, #2990, .F. );
#2629 = EDGE_CURVE( '', #2944, #2991, #2992, .T. );
#2630 = EDGE_CURVE( '', #2991, #2993, #2994, .T. );
#2631 = EDGE_CURVE( '', #2993, #2986, #2995, .T. );
#2632 = EDGE_CURVE( '', #2996, #2987, #2997, .T. );
#2633 = EDGE_CURVE( '', #2998, #2996, #2999, .T. );
#2634 = EDGE_CURVE( '', #2937, #2998, #3000, .T. );
#2635 = EDGE_CURVE( '', #3001, #3002, #3003, .T. );
#2636 = EDGE_CURVE( '', #3004, #3002, #3005, .F. );
#2637 = EDGE_CURVE( '', #3004, #3006, #3007, .T. );
#2638 = EDGE_CURVE( '', #3006, #3008, #3009, .T. );
#2639 = EDGE_CURVE( '', #3008, #3010, #3011, .T. );
#2640 = EDGE_CURVE( '', #3012, #3010, #3013, .T. );
#2641 = EDGE_CURVE( '', #3014, #3012, #3015, .T. );
#2642 = EDGE_CURVE( '', #3016, #3014, #3017, .T. );
#2643 = EDGE_CURVE( '', #2996, #3016, #3018, .T. );
#2644 = EDGE_CURVE( '', #2986, #3019, #3020, .T. );
#2645 = EDGE_CURVE( '', #3021, #3019, #3022, .T. );
#2646 = EDGE_CURVE( '', #3023, #3021, #3024, .F. );
#2647 = EDGE_CURVE( '', #3025, #3023, #3026, .T. );
#2648 = EDGE_CURVE( '', #3025, #3027, #3028, .F. );
#2649 = EDGE_CURVE( '', #3027, #3029, #3030, .T. );
#2650 = EDGE_CURVE( '', #3029, #3031, #3032, .T. );
#2651 = EDGE_CURVE( '', #3031, #3033, #3034, .T. );
#2652 = EDGE_CURVE( '', #3001, #3033, #3035, .T. );
#2653 = EDGE_CURVE( '', #3010, #3036, #3037, .T. );
#2654 = EDGE_CURVE( '', #3038, #3036, #3039, .T. );
#2655 = EDGE_CURVE( '', #3012, #3038, #3040, .T. );
#2656 = EDGE_CURVE( '', #3041, #3036, #3042, .T. );
#2657 = EDGE_CURVE( '', #3043, #3041, #3044, .T. );
#2658 = EDGE_CURVE( '', #3043, #3038, #3045, .T. );
#2659 = EDGE_CURVE( '', #2953, #3043, #3046, .T. );
#2660 = EDGE_CURVE( '', #2951, #3047, #3048, .T. );
#2661 = EDGE_CURVE( '', #3049, #3047, #3050, .T. );
#2662 = EDGE_CURVE( '', #3014, #3049, #3051, .T. );
#2663 = EDGE_CURVE( '', #3047, #3052, #3053, .T. );
#2664 = EDGE_CURVE( '', #3054, #3047, #3055, .T. );
#2665 = EDGE_CURVE( '', #3054, #3056, #3057, .T. );
#2666 = EDGE_CURVE( '', #3056, #3052, #3058, .T. );
#2667 = EDGE_CURVE( '', #3059, #3060, #3061, .T. );
#2668 = EDGE_CURVE( '', #3060, #3062, #3063, .T. );
#2669 = EDGE_CURVE( '', #3062, #3064, #3065, .T. );
#2670 = EDGE_CURVE( '', #3064, #3059, #3066, .T. );
#2671 = EDGE_CURVE( '', #3052, #3067, #3068, .F. );
#2672 = EDGE_CURVE( '', #3067, #3069, #3070, .T. );
#2673 = EDGE_CURVE( '', #3069, #3071, #3072, .F. );
#2674 = EDGE_CURVE( '', #3049, #3071, #3073, .T. );
#2675 = EDGE_CURVE( '', #2949, #3054, #3074, .T. );
#2676 = EDGE_CURVE( '', #3075, #3076, #3077, .T. );
#2677 = EDGE_CURVE( '', #3078, #3076, #3079, .T. );
#2678 = EDGE_CURVE( '', #3078, #3080, #3081, .T. );
#2679 = EDGE_CURVE( '', #3075, #3080, #3082, .T. );
#2680 = EDGE_CURVE( '', #3080, #3083, #3084, .T. );
#2681 = EDGE_CURVE( '', #3078, #3021, #3085, .T. );
#2682 = EDGE_CURVE( '', #3083, #3019, #3086, .T. );
#2683 = EDGE_CURVE( '', #3083, #2993, #3087, .T. );
#2684 = EDGE_CURVE( '', #3088, #2993, #3089, .T. );
#2685 = EDGE_CURVE( '', #3090, #3088, #3091, .F. );
#2686 = EDGE_CURVE( '', #3090, #3075, #3092, .T. );
#2687 = EDGE_CURVE( '', #3090, #3033, #3093, .T. );
#2688 = EDGE_CURVE( '', #3076, #3031, #3094, .T. );
#2689 = EDGE_CURVE( '', #3088, #3001, #3095, .T. );
#2690 = EDGE_CURVE( '', #3096, #3096, #3097, .T. );
#2691 = EDGE_CURVE( '', #3098, #3088, #3099, .T. );
#2692 = EDGE_CURVE( '', #2965, #3098, #3100, .T. );
#2693 = EDGE_CURVE( '', #2963, #2939, #3101, .T. );
#2694 = EDGE_CURVE( '', #3002, #2936, #3102, .T. );
#2695 = EDGE_CURVE( '', #3078, #3103, #3104, .T. );
#2696 = EDGE_CURVE( '', #3103, #3023, #3105, .F. );
#2697 = EDGE_CURVE( '', #3106, #3107, #3108, .T. );
#2698 = EDGE_CURVE( '', #3107, #3109, #3110, .T. );
#2699 = EDGE_CURVE( '', #3109, #3111, #3112, .T. );
#2700 = EDGE_CURVE( '', #3111, #3106, #3113, .T. );
#2701 = EDGE_CURVE( '', #3025, #3114, #3115, .T. );
#2702 = EDGE_CURVE( '', #3114, #3116, #3117, .T. );
#2703 = EDGE_CURVE( '', #3027, #3116, #3118, .T. );
#2704 = EDGE_CURVE( '', #3103, #3119, #3120, .T. );
#2705 = EDGE_CURVE( '', #3076, #3119, #3121, .T. );
#2706 = EDGE_CURVE( '', #3119, #3029, #3122, .T. );
#2707 = EDGE_CURVE( '', #2981, #3123, #3124, .T. );
#2708 = EDGE_CURVE( '', #3123, #2991, #3125, .T. );
#2709 = EDGE_CURVE( '', #2977, #3126, #3127, .T. );
#2710 = EDGE_CURVE( '', #3126, #3106, #3128, .T. );
#2711 = EDGE_CURVE( '', #2979, #3106, #3129, .T. );
#2712 = EDGE_CURVE( '', #2975, #3130, #3131, .T. );
#2713 = EDGE_CURVE( '', #3130, #3114, #3132, .T. );
#2714 = EDGE_CURVE( '', #3126, #3103, #3133, .T. );
#2715 = EDGE_CURVE( '', #3126, #3134, #3135, .T. );
#2716 = EDGE_CURVE( '', #3134, #3107, #3136, .T. );
#2717 = EDGE_CURVE( '', #2969, #3107, #3137, .T. );
#2718 = EDGE_CURVE( '', #2971, #3134, #3138, .T. );
#2719 = EDGE_CURVE( '', #3109, #3139, #3140, .T. );
#2720 = EDGE_CURVE( '', #2967, #3139, #3141, .T. );
#2721 = EDGE_CURVE( '', #2973, #3142, #3143, .T. );
#2722 = EDGE_CURVE( '', #3144, #3134, #3145, .T. );
#2723 = EDGE_CURVE( '', #3146, #3144, #3147, .T. );
#2724 = EDGE_CURVE( '', #3119, #3146, #3148, .T. );
#2725 = EDGE_CURVE( '', #3142, #3116, #3149, .T. );
#2726 = EDGE_CURVE( '', #3130, #3142, #3150, .T. );
#2727 = EDGE_CURVE( '', #3123, #3111, #3151, .T. );
#2728 = EDGE_CURVE( '', #3139, #3098, #3152, .F. );
#2729 = EDGE_CURVE( '', #3098, #2991, #3153, .T. );
#2730 = EDGE_CURVE( '', #3154, #3155, #3156, .T. );
#2731 = EDGE_CURVE( '', #3155, #3119, #3157, .F. );
#2732 = EDGE_CURVE( '', #3146, #3154, #3158, .T. );
#2733 = EDGE_CURVE( '', #3144, #3159, #3160, .T. );
#2734 = EDGE_CURVE( '', #3159, #3154, #3161, .T. );
#2735 = EDGE_CURVE( '', #3155, #3162, #3163, .T. );
#2736 = EDGE_CURVE( '', #3162, #3159, #3164, .T. );
#2737 = EDGE_CURVE( '', #3134, #3162, #3165, .F. );
#2738 = EDGE_CURVE( '', #2961, #3166, #3167, .T. );
#2739 = EDGE_CURVE( '', #2939, #3166, #3168, .T. );
#2740 = EDGE_CURVE( '', #3166, #3169, #3170, .T. );
#2741 = EDGE_CURVE( '', #3169, #3171, #3172, .T. );
#2742 = EDGE_CURVE( '', #3171, #3173, #3174, .T. );
#2743 = EDGE_CURVE( '', #2941, #3173, #3175, .T. );
#2744 = EDGE_CURVE( '', #2947, #3173, #3176, .T. );
#2745 = EDGE_CURVE( '', #2998, #3064, #3177, .T. );
#2746 = EDGE_CURVE( '', #3059, #3016, #3178, .T. );
#2747 = EDGE_CURVE( '', #3049, #3059, #3179, .T. );
#2748 = EDGE_CURVE( '', #3071, #3060, #3180, .T. );
#2749 = EDGE_CURVE( '', #3060, #3006, #3181, .T. );
#2750 = EDGE_CURVE( '', #3071, #3008, #3182, .T. );
#2751 = EDGE_CURVE( '', #3169, #3056, #3183, .T. );
#2752 = EDGE_CURVE( '', #3054, #3171, #3184, .T. );
#2753 = EDGE_CURVE( '', #2955, #3041, #3185, .T. );
#2754 = EDGE_CURVE( '', #3062, #3186, #3187, .T. );
#2755 = EDGE_CURVE( '', #3186, #3004, #3188, .T. );
#2756 = EDGE_CURVE( '', #3189, #3071, #3190, .T. );
#2757 = EDGE_CURVE( '', #3191, #3189, #3192, .T. );
#2758 = EDGE_CURVE( '', #3052, #3191, #3193, .T. );
#2759 = EDGE_CURVE( '', #2957, #3052, #3194, .T. );
#2760 = EDGE_CURVE( '', #3069, #3195, #3196, .T. );
#2761 = EDGE_CURVE( '', #3189, #3195, #3197, .T. );
#2762 = EDGE_CURVE( '', #3195, #3198, #3199, .T. );
#2763 = EDGE_CURVE( '', #3191, #3198, #3200, .T. );
#2764 = EDGE_CURVE( '', #3198, #3067, #3201, .T. );
#2765 = EDGE_CURVE( '', #2959, #3056, #3202, .T. );
#2766 = EDGE_CURVE( '', #2936, #3186, #3203, .T. );
#2767 = EDGE_CURVE( '', #3204, #3205, #3206, .T. );
#2768 = EDGE_CURVE( '', #3207, #3204, #3208, .T. );
#2769 = EDGE_CURVE( '', #3207, #3209, #3210, .T. );
#2770 = EDGE_CURVE( '', #3209, #3205, #3211, .T. );
#2771 = EDGE_CURVE( '', #3205, #3212, #3213, .T. );
#2772 = EDGE_CURVE( '', #3212, #3214, #3215, .T. );
#2773 = EDGE_CURVE( '', #3214, #3204, #3216, .T. );
#2774 = EDGE_CURVE( '', #3217, #3207, #3218, .T. );
#2775 = EDGE_CURVE( '', #3219, #3217, #3220, .T. );
#2776 = EDGE_CURVE( '', #3209, #3219, #3221, .T. );
#2777 = EDGE_CURVE( '', #3217, #3214, #3222, .T. );
#2778 = EDGE_CURVE( '', #3219, #3212, #3223, .T. );
#2779 = EDGE_CURVE( '', #3224, #3225, #3226, .T. );
#2780 = EDGE_CURVE( '', #3227, #3225, #3228, .T. );
#2781 = EDGE_CURVE( '', #3229, #3227, #3230, .T. );
#2782 = EDGE_CURVE( '', #3229, #3224, #3231, .T. );
#2783 = EDGE_CURVE( '', #3232, #3224, #3233, .T. );
#2784 = EDGE_CURVE( '', #3234, #3229, #3235, .T. );
#2785 = EDGE_CURVE( '', #3234, #3232, #3236, .T. );
#2786 = EDGE_CURVE( '', #3237, #3232, #3238, .T. );
#2787 = EDGE_CURVE( '', #3225, #3237, #3239, .T. );
#2788 = EDGE_CURVE( '', #3227, #3240, #3241, .T. );
#2789 = EDGE_CURVE( '', #3240, #3234, #3242, .T. );
#2790 = EDGE_CURVE( '', #3240, #3237, #3243, .T. );
#2791 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2792 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2793 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2794 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2795 = CARTESIAN_POINT( '', ( -1.21813831134799, 1.70686168865201, 1.50000000000000 ) );
#2796 = DIRECTION( '', ( 0.707106781186548, -0.707106781186548, 0.000000000000000 ) );
#2797 = DIRECTION( '', ( -0.707106781186547, -0.707106781186547, 0.000000000000000 ) );
#2798 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70686168865201, 1.50000000000000 ) );
#2799 = DIRECTION( '', ( 1.00000000000000, -6.12303176911189E-017, 0.000000000000000 ) );
#2800 = DIRECTION( '', ( 6.12303176911189E-017, 1.00000000000000, 0.000000000000000 ) );
#2801 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2802 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70686168865201, 1.50000000000000 ) );
#2803 = DIRECTION( '', ( 1.00000000000000, -6.12303176911189E-017, 0.000000000000000 ) );
#2804 = DIRECTION( '', ( 6.12303176911189E-017, 1.00000000000000, 0.000000000000000 ) );
#2805 = CARTESIAN_POINT( '', ( 1.21813831134799, 1.70686168865201, 1.50000000000000 ) );
#2806 = DIRECTION( '', ( 0.707106781186548, 0.707106781186548, 0.000000000000000 ) );
#2807 = DIRECTION( '', ( -0.707106781186547, 0.707106781186547, 0.000000000000000 ) );
#2808 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2809 = DIRECTION( '', ( 1.00000000000000, 5.42101086242752E-016, 0.000000000000000 ) );
#2810 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2811 = DIRECTION( '', ( 1.00000000000000, 5.42101086242752E-016, 0.000000000000000 ) );
#2812 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2813 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2814 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 1.50000000000000 ) );
#2815 = DIRECTION( '', ( 1.00000000000000, -6.12303176911189E-017, 0.000000000000000 ) );
#2816 = DIRECTION( '', ( 6.12303176911189E-017, 1.00000000000000, 0.000000000000000 ) );
#2817 = DIRECTION( '', ( -6.12303176911189E-017, -1.00000000000000, 0.000000000000000 ) );
#2818 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70686168865201, 1.50000000000000 ) );
#2819 = DIRECTION( '', ( -1.00000000000000, 6.12303176911189E-017, 0.000000000000000 ) );
#2820 = DIRECTION( '', ( -6.12303176911189E-017, -1.00000000000000, 0.000000000000000 ) );
#2821 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2822 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70000000000000, 1.50000000000000 ) );
#2823 = DIRECTION( '', ( -1.00000000000000, 6.12303176911189E-017, 0.000000000000000 ) );
#2824 = DIRECTION( '', ( -6.12303176911189E-017, -1.00000000000000, 0.000000000000000 ) );
#2825 = DIRECTION( '', ( 6.12303176911189E-017, 1.00000000000000, 0.000000000000000 ) );
#2826 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 1.50000000000000 ) );
#2827 = DIRECTION( '', ( 1.00000000000000, -6.12303176911189E-017, 0.000000000000000 ) );
#2828 = DIRECTION( '', ( 6.12303176911189E-017, 1.00000000000000, 0.000000000000000 ) );
#2829 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2830 = DIRECTION( '', ( 6.12303176911189E-017, 1.00000000000000, 0.000000000000000 ) );
#2831 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#2832 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2833 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#2834 = CARTESIAN_POINT( '', ( 0.000000000000000, 0.000000000000000, 2.50000000000000 ) );
#2835 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2836 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2837 = DIRECTION( '', ( 0.707106781186548, -0.707106781186548, 0.000000000000000 ) );
#2838 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2839 = DIRECTION( '', ( -0.144999886718885, 0.144999886718885, 0.978749235352458 ) );
#2840 = CARTESIAN_POINT( '', ( -1.21813831134799, -1.70686168865201, 1.50000000000000 ) );
#2841 = DIRECTION( '', ( -0.707106781186548, -0.707106781186548, 0.000000000000000 ) );
#2842 = DIRECTION( '', ( 0.707106781186547, -0.707106781186547, 0.000000000000000 ) );
#2843 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2844 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2845 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2846 = DIRECTION( '', ( -0.144999886718885, 0.144999886718885, -0.978749235352458 ) );
#2847 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2848 = DIRECTION( '', ( 0.707106781186548, -0.707106781186548, 0.000000000000000 ) );
#2849 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2850 = DIRECTION( '', ( 0.144999886718885, 0.144999886718885, 0.978749235352458 ) );
#2851 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2852 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2853 = DIRECTION( '', ( -0.707106781186548, -0.707106781186548, 0.000000000000000 ) );
#2854 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2855 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#2856 = CARTESIAN_POINT( '', ( 3.00000000000000, -1.70000000000000, 1.50000000000000 ) );
#2857 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2858 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2859 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2860 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#2861 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#2862 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2863 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#2864 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2865 = CARTESIAN_POINT( '', ( 3.00000000000000, 1.70000000000000, 1.50000000000000 ) );
#2866 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2867 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2868 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#2869 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#2870 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2871 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70000000000000, 1.50000000000000 ) );
#2872 = DIRECTION( '', ( -1.00000000000000, 6.12303176911189E-017, 0.000000000000000 ) );
#2873 = DIRECTION( '', ( -6.12303176911189E-017, -1.00000000000000, 0.000000000000000 ) );
#2874 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70686168865201, 1.50000000000000 ) );
#2875 = DIRECTION( '', ( -1.00000000000000, 6.12303176911189E-017, 0.000000000000000 ) );
#2876 = DIRECTION( '', ( -6.12303176911189E-017, -1.00000000000000, 0.000000000000000 ) );
#2877 = DIRECTION( '', ( -6.12303176911189E-017, -1.00000000000000, 0.000000000000000 ) );
#2878 = DIRECTION( '', ( 0.144999886718885, 0.144999886718885, -0.978749235352458 ) );
#2879 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2880 = CARTESIAN_POINT( '', ( 0.000000000000000, 0.000000000000000, 2.40000000000000 ) );
#2881 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2882 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2883 = DIRECTION( '', ( -1.00000000000000, 7.22801448323670E-017, 0.000000000000000 ) );
#2884 = DIRECTION( '', ( -1.00000000000000, 7.22801448323670E-017, 0.000000000000000 ) );
#2885 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2886 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2887 = DIRECTION( '', ( -1.00000000000000, 7.22801448323670E-017, 0.000000000000000 ) );
#2888 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2889 = DIRECTION( '', ( -1.00000000000000, 7.22801448323670E-017, 0.000000000000000 ) );
#2890 = CARTESIAN_POINT( '', ( 0.000000000000000, 0.000000000000000, 0.775000000000000 ) );
#2891 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2892 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2893 = CARTESIAN_POINT( '', ( -3.00000000000000, 1.70000000000000, 1.50000000000000 ) );
#2894 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2895 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2896 = DIRECTION( '', ( -1.00000000000000, 7.22801448323670E-017, 0.000000000000000 ) );
#2897 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2898 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2899 = DIRECTION( '', ( -0.707106781186548, -0.707106781186548, 0.000000000000000 ) );
#2900 = CARTESIAN_POINT( '', ( -3.00000000000000, -1.70000000000000, 1.50000000000000 ) );
#2901 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2902 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2903 = DIRECTION( '', ( 1.00000000000000, 7.22801448323670E-017, 0.000000000000000 ) );
#2904 = DIRECTION( '', ( 1.00000000000000, 7.22801448323670E-017, 0.000000000000000 ) );
#2905 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2906 = DIRECTION( '', ( 1.00000000000000, 7.22801448323670E-017, 0.000000000000000 ) );
#2907 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#2908 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#2909 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2910 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#2911 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2912 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#2913 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2914 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#2915 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2916 = DIRECTION( '', ( 1.00000000000000, 7.22801448323670E-017, 0.000000000000000 ) );
#2917 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#2918 = DIRECTION( '', ( -1.00000000000000, 5.42101086242752E-016, 0.000000000000000 ) );
#2919 = DIRECTION( '', ( 1.00000000000000, 7.22801448323670E-017, 0.000000000000000 ) );
#2920 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2921 = DIRECTION( '', ( -1.00000000000000, 5.42101086242752E-016, 0.000000000000000 ) );
#2922 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2923 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#2924 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#2925 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#2926 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2927 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2928 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2929 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2930 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#2931 = CARTESIAN_POINT( '', ( 1.21813831134799, -1.70686168865201, 1.50000000000000 ) );
#2932 = DIRECTION( '', ( -0.707106781186548, 0.707106781186548, 0.000000000000000 ) );
#2933 = DIRECTION( '', ( 0.707106781186547, 0.707106781186547, 0.000000000000000 ) );
#2934 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2935 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#2936 = VERTEX_POINT( '', #3244 );
#2937 = VERTEX_POINT( '', #3245 );
#2938 = LINE( '', #3246, #3247 );
#2939 = VERTEX_POINT( '', #3248 );
#2940 = LINE( '', #3249, #3250 );
#2941 = VERTEX_POINT( '', #3251 );
#2942 = LINE( '', #3252, #3253 );
#2943 = LINE( '', #3254, #3255 );
#2944 = VERTEX_POINT( '', #3256 );
#2945 = VERTEX_POINT( '', #3257 );
#2946 = LINE( '', #3258, #3259 );
#2947 = VERTEX_POINT( '', #3260 );
#2948 = CIRCLE( '', #3261, 0.200000000000000 );
#2949 = VERTEX_POINT( '', #3262 );
#2950 = LINE( '', #3263, #3264 );
#2951 = VERTEX_POINT( '', #3265 );
#2952 = CIRCLE( '', #3266, 0.0500000000000000 );
#2953 = VERTEX_POINT( '', #3267 );
#2954 = LINE( '', #3268, #3269 );
#2955 = VERTEX_POINT( '', #3270 );
#2956 = LINE( '', #3271, #3272 );
#2957 = VERTEX_POINT( '', #3273 );
#2958 = LINE( '', #3274, #3275 );
#2959 = VERTEX_POINT( '', #3276 );
#2960 = CIRCLE( '', #3277, 0.200000000000000 );
#2961 = VERTEX_POINT( '', #3278 );
#2962 = LINE( '', #3279, #3280 );
#2963 = VERTEX_POINT( '', #3281 );
#2964 = CIRCLE( '', #3282, 0.0500000000000000 );
#2965 = VERTEX_POINT( '', #3283 );
#2966 = LINE( '', #3284, #3285 );
#2967 = VERTEX_POINT( '', #3286 );
#2968 = CIRCLE( '', #3287, 0.0500000000000000 );
#2969 = VERTEX_POINT( '', #3288 );
#2970 = LINE( '', #3289, #3290 );
#2971 = VERTEX_POINT( '', #3291 );
#2972 = CIRCLE( '', #3292, 0.200000000000000 );
#2973 = VERTEX_POINT( '', #3293 );
#2974 = LINE( '', #3294, #3295 );
#2975 = VERTEX_POINT( '', #3296 );
#2976 = LINE( '', #3297, #3298 );
#2977 = VERTEX_POINT( '', #3299 );
#2978 = LINE( '', #3300, #3301 );
#2979 = VERTEX_POINT( '', #3302 );
#2980 = CIRCLE( '', #3303, 0.0500000000000000 );
#2981 = VERTEX_POINT( '', #3304 );
#2982 = LINE( '', #3305, #3306 );
#2983 = CIRCLE( '', #3307, 0.200000000000000 );
#2984 = VERTEX_POINT( '', #3308 );
#2985 = CIRCLE( '', #3309, 1.02500000000000 );
#2986 = VERTEX_POINT( '', #3310 );
#2987 = VERTEX_POINT( '', #3311 );
#2988 = LINE( '', #3312, #3313 );
#2989 = LINE( '', #3314, #3315 );
#2990 = LINE( '', #3316, #3317 );
#2991 = VERTEX_POINT( '', #3318 );
#2992 = LINE( '', #3319, #3320 );
#2993 = VERTEX_POINT( '', #3321 );
#2994 = LINE( '', #3322, #3323 );
#2995 = LINE( '', #3324, #3325 );
#2996 = VERTEX_POINT( '', #3326 );
#2997 = CIRCLE( '', #3327, 0.200000000000000 );
#2998 = VERTEX_POINT( '', #3328 );
#2999 = LINE( '', #3329, #3330 );
#3000 = CIRCLE( '', #3331, 0.200000000000000 );
#3001 = VERTEX_POINT( '', #3332 );
#3002 = VERTEX_POINT( '', #3333 );
#3003 = LINE( '', #3334, #3335 );
#3004 = VERTEX_POINT( '', #3336 );
#3005 = CIRCLE( '', #3337, 0.0500000000000000 );
#3006 = VERTEX_POINT( '', #3338 );
#3007 = LINE( '', #3339, #3340 );
#3008 = VERTEX_POINT( '', #3341 );
#3009 = CIRCLE( '', #3342, 0.200000000000000 );
#3010 = VERTEX_POINT( '', #3343 );
#3011 = LINE( '', #3344, #3345 );
#3012 = VERTEX_POINT( '', #3346 );
#3013 = LINE( '', #3347, #3348 );
#3014 = VERTEX_POINT( '', #3349 );
#3015 = LINE( '', #3350, #3351 );
#3016 = VERTEX_POINT( '', #3352 );
#3017 = CIRCLE( '', #3353, 0.0500000000000000 );
#3018 = LINE( '', #3354, #3355 );
#3019 = VERTEX_POINT( '', #3356 );
#3020 = CIRCLE( '', #3357, 0.200000000000000 );
#3021 = VERTEX_POINT( '', #3358 );
#3022 = LINE( '', #3359, #3360 );
#3023 = VERTEX_POINT( '', #3361 );
#3024 = CIRCLE( '', #3362, 0.0500000000000000 );
#3025 = VERTEX_POINT( '', #3363 );
#3026 = LINE( '', #3364, #3365 );
#3027 = VERTEX_POINT( '', #3366 );
#3028 = LINE( '', #3367, #3368 );
#3029 = VERTEX_POINT( '', #3369 );
#3030 = LINE( '', #3370, #3371 );
#3031 = VERTEX_POINT( '', #3372 );
#3032 = CIRCLE( '', #3373, 0.200000000000000 );
#3033 = VERTEX_POINT( '', #3374 );
#3034 = LINE( '', #3375, #3376 );
#3035 = CIRCLE( '', #3377, 0.0500000000000000 );
#3036 = VERTEX_POINT( '', #3378 );
#3037 = LINE( '', #3379, #3380 );
#3038 = VERTEX_POINT( '', #3381 );
#3039 = LINE( '', #3382, #3383 );
#3040 = LINE( '', #3384, #3385 );
#3041 = VERTEX_POINT( '', #3386 );
#3042 = LINE( '', #3387, #3388 );
#3043 = VERTEX_POINT( '', #3389 );
#3044 = LINE( '', #3390, #3391 );
#3045 = LINE( '', #3392, #3393 );
#3046 = LINE( '', #3394, #3395 );
#3047 = VERTEX_POINT( '', #3396 );
#3048 = LINE( '', #3397, #3398 );
#3049 = VERTEX_POINT( '', #3399 );
#3050 = LINE( '', #3400, #3401 );
#3051 = LINE( '', #3402, #3403 );
#3052 = VERTEX_POINT( '', #3404 );
#3053 = LINE( '', #3405, #3406 );
#3054 = VERTEX_POINT( '', #3407 );
#3055 = B_SPLINE_CURVE_WITH_KNOTS( '', 3, ( #3408, #3409, #3410, #3411, #3412, #3413, #3414, #3415, #3416, #3417, #3418, #3419, #3420, #3421 ), .UNSPECIFIED., .F., .F., ( 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4 ), ( 0.000000000000000, 0.137702904470055, 0.205087166454940, 0.337024080608930, 0.401570377409631, 0.527856600521148, 0.589647843891779, 0.710627514217791, 0.769907607649443, 0.886228648410926, 0.943410094907395, 1.00000000000000 ), .UNSPECIFIED. );
#3056 = VERTEX_POINT( '', #3422 );
#3057 = LINE( '', #3423, #3424 );
#3058 = B_SPLINE_CURVE_WITH_KNOTS( '', 3, ( #3425, #3426, #3427, #3428 ), .UNSPECIFIED., .F., .F., ( 4, 4 ), ( 0.000000000000000, 1.00000000000000 ), .UNSPECIFIED. );
#3059 = VERTEX_POINT( '', #3429 );
#3060 = VERTEX_POINT( '', #3430 );
#3061 = LINE( '', #3431, #3432 );
#3062 = VERTEX_POINT( '', #3433 );
#3063 = CIRCLE( '', #3434, 0.100000000000000 );
#3064 = VERTEX_POINT( '', #3435 );
#3065 = LINE( '', #3436, #3437 );
#3066 = CIRCLE( '', #3438, 0.100000000000000 );
#3067 = VERTEX_POINT( '', #3439 );
#3068 = ELLIPSE( '', #3440, 1.12329280498680, 0.100000000000000 );
#3069 = VERTEX_POINT( '', #3441 );
#3070 = LINE( '', #3442, #3443 );
#3071 = VERTEX_POINT( '', #3444 );
#3072 = ELLIPSE( '', #3445, 1.12329280498680, 0.100000000000000 );
#3073 = LINE( '', #3446, #3447 );
#3074 = LINE( '', #3448, #3449 );
#3075 = VERTEX_POINT( '', #3450 );
#3076 = VERTEX_POINT( '', #3451 );
#3077 = CIRCLE( '', #3452, 0.100000000000000 );
#3078 = VERTEX_POINT( '', #3453 );
#3079 = LINE( '', #3454, #3455 );
#3080 = VERTEX_POINT( '', #3456 );
#3081 = CIRCLE( '', #3457, 0.100000000000000 );
#3082 = LINE( '', #3458, #3459 );
#3083 = VERTEX_POINT( '', #3460 );
#3084 = LINE( '', #3461, #3462 );
#3085 = LINE( '', #3463, #3464 );
#3086 = LINE( '', #3465, #3466 );
#3087 = CIRCLE( '', #3467, 0.200000000000000 );
#3088 = VERTEX_POINT( '', #3468 );
#3089 = LINE( '', #3469, #3470 );
#3090 = VERTEX_POINT( '', #3471 );
#3091 = CIRCLE( '', #3472, 0.0500000000000000 );
#3092 = LINE( '', #3473, #3474 );
#3093 = LINE( '', #3475, #3476 );
#3094 = LINE( '', #3477, #3478 );
#3095 = LINE( '', #3479, #3480 );
#3096 = VERTEX_POINT( '', #3481 );
#3097 = CIRCLE( '', #3482, 1.02500000000000 );
#3098 = VERTEX_POINT( '', #3483 );
#3099 = LINE( '', #3484, #3485 );
#3100 = LINE( '', #3486, #3487 );
#3101 = LINE( '', #3488, #3489 );
#3102 = LINE( '', #3490, #3491 );
#3103 = VERTEX_POINT( '', #3492 );
#3104 = B_SPLINE_CURVE_WITH_KNOTS( '', 3, ( #3493, #3494, #3495, #3496, #3497, #3498, #3499, #3500, #3501, #3502, #3503, #3504, #3505, #3506 ), .UNSPECIFIED., .F., .F., ( 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4 ), ( 0.000000000000000, 0.137702904469989, 0.205087166454878, 0.337024080608819, 0.401570377409511, 0.527856600520964, 0.589647843891640, 0.710627514217731, 0.769907607649378, 0.886228648410874, 0.943410094907378, 1.00000000000000 ), .UNSPECIFIED. );
#3105 = LINE( '', #3507, #3508 );
#3106 = VERTEX_POINT( '', #3509 );
#3107 = VERTEX_POINT( '', #3510 );
#3108 = LINE( '', #3511, #3512 );
#3109 = VERTEX_POINT( '', #3513 );
#3110 = CIRCLE( '', #3514, 0.100000000000000 );
#3111 = VERTEX_POINT( '', #3515 );
#3112 = LINE( '', #3516, #3517 );
#3113 = CIRCLE( '', #3518, 0.100000000000000 );
#3114 = VERTEX_POINT( '', #3519 );
#3115 = LINE( '', #3520, #3521 );
#3116 = VERTEX_POINT( '', #3522 );
#3117 = LINE( '', #3523, #3524 );
#3118 = LINE( '', #3525, #3526 );
#3119 = VERTEX_POINT( '', #3527 );
#3120 = LINE( '', #3528, #3529 );
#3121 = B_SPLINE_CURVE_WITH_KNOTS( '', 3, ( #3530, #3531, #3532, #3533 ), .UNSPECIFIED., .F., .F., ( 4, 4 ), ( 0.000000000000000, 1.00000000000000 ), .UNSPECIFIED. );
#3122 = LINE( '', #3534, #3535 );
#3123 = VERTEX_POINT( '', #3536 );
#3124 = LINE( '', #3537, #3538 );
#3125 = CIRCLE( '', #3539, 0.200000000000000 );
#3126 = VERTEX_POINT( '', #3540 );
#3127 = LINE( '', #3541, #3542 );
#3128 = B_SPLINE_CURVE_WITH_KNOTS( '', 3, ( #3543, #3544, #3545, #3546, #3547, #3548, #3549, #3550, #3551, #3552, #3553, #3554, #3555, #3556 ), .UNSPECIFIED., .F., .F., ( 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4 ), ( 0.000000000000000, 0.113729803691331, 0.171574629991025, 0.289352800738379, 0.349435423537440, 0.472128510837612, 0.534819741721010, 0.662965618249610, 0.728461932924560, 0.862313816212096, 0.930674102475057, 1.00000000000000 ), .UNSPECIFIED. );
#3129 = LINE( '', #3557, #3558 );
#3130 = VERTEX_POINT( '', #3559 );
#3131 = LINE( '', #3560, #3561 );
#3132 = LINE( '', #3562, #3563 );
#3133 = LINE( '', #3564, #3565 );
#3134 = VERTEX_POINT( '', #3566 );
#3135 = LINE( '', #3567, #3568 );
#3136 = B_SPLINE_CURVE_WITH_KNOTS( '', 3, ( #3569, #3570, #3571, #3572 ), .UNSPECIFIED., .F., .F., ( 4, 4 ), ( 0.000000000000000, 1.00000000000000 ), .UNSPECIFIED. );
#3137 = LINE( '', #3573, #3574 );
#3138 = LINE( '', #3575, #3576 );
#3139 = VERTEX_POINT( '', #3577 );
#3140 = LINE( '', #3578, #3579 );
#3141 = LINE( '', #3580, #3581 );
#3142 = VERTEX_POINT( '', #3582 );
#3143 = LINE( '', #3583, #3584 );
#3144 = VERTEX_POINT( '', #3585 );
#3145 = ELLIPSE( '', #3586, 0.100398634516462, 0.100000000000000 );
#3146 = VERTEX_POINT( '', #3587 );
#3147 = LINE( '', #3588, #3589 );
#3148 = ELLIPSE( '', #3590, 0.100398634516462, 0.100000000000000 );
#3149 = LINE( '', #3591, #3592 );
#3150 = LINE( '', #3593, #3594 );
#3151 = LINE( '', #3595, #3596 );
#3152 = CIRCLE( '', #3597, 0.0500000000000000 );
#3153 = LINE( '', #3598, #3599 );
#3154 = VERTEX_POINT( '', #3600 );
#3155 = VERTEX_POINT( '', #3601 );
#3156 = CIRCLE( '', #3602, 0.100000000000000 );
#3157 = ELLIPSE( '', #3603, 1.12329280498628, 0.100000000000000 );
#3158 = LINE( '', #3604, #3605 );
#3159 = VERTEX_POINT( '', #3606 );
#3160 = LINE( '', #3607, #3608 );
#3161 = LINE( '', #3609, #3610 );
#3162 = VERTEX_POINT( '', #3611 );
#3163 = LINE( '', #3612, #3613 );
#3164 = CIRCLE( '', #3614, 0.100000000000000 );
#3165 = ELLIPSE( '', #3615, 1.12329280498628, 0.100000000000000 );
#3166 = VERTEX_POINT( '', #3616 );
#3167 = LINE( '', #3617, #3618 );
#3168 = CIRCLE( '', #3619, 0.0500000000000000 );
#3169 = VERTEX_POINT( '', #3620 );
#3170 = LINE( '', #3621, #3622 );
#3171 = VERTEX_POINT( '', #3623 );
#3172 = LINE( '', #3624, #3625 );
#3173 = VERTEX_POINT( '', #3626 );
#3174 = LINE( '', #3627, #3628 );
#3175 = CIRCLE( '', #3629, 0.200000000000000 );
#3176 = LINE( '', #3630, #3631 );
#3177 = LINE( '', #3632, #3633 );
#3178 = LINE( '', #3634, #3635 );
#3179 = B_SPLINE_CURVE_WITH_KNOTS( '', 3, ( #3636, #3637, #3638, #3639, #3640, #3641, #3642, #3643, #3644, #3645, #3646, #3647, #3648, #3649 ), .UNSPECIFIED., .F., .F., ( 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4 ), ( 0.000000000000000, 0.113729803691528, 0.171574629991580, 0.289352800739002, 0.349435423538086, 0.472128510838475, 0.534819741721556, 0.662965618250110, 0.728461932924962, 0.862313816213014, 0.930674102474428, 1.00000000000000 ), .UNSPECIFIED. );
#3180 = B_SPLINE_CURVE_WITH_KNOTS( '', 3, ( #3650, #3651, #3652, #3653 ), .UNSPECIFIED., .F., .F., ( 4, 4 ), ( 0.000000000000000, 1.00000000000000 ), .UNSPECIFIED. );
#3181 = LINE( '', #3654, #3655 );
#3182 = LINE( '', #3656, #3657 );
#3183 = CIRCLE( '', #3658, 0.100000000000000 );
#3184 = CIRCLE( '', #3659, 0.100000000000000 );
#3185 = LINE( '', #3660, #3661 );
#3186 = VERTEX_POINT( '', #3662 );
#3187 = LINE( '', #3663, #3664 );
#3188 = LINE( '', #3665, #3666 );
#3189 = VERTEX_POINT( '', #3667 );
#3190 = ELLIPSE( '', #3668, 0.100398634516461, 0.100000000000000 );
#3191 = VERTEX_POINT( '', #3669 );
#3192 = LINE( '', #3670, #3671 );
#3193 = ELLIPSE( '', #3672, 0.100398634516461, 0.100000000000000 );
#3194 = LINE( '', #3673, #3674 );
#3195 = VERTEX_POINT( '', #3675 );
#3196 = CIRCLE( '', #3676, 0.100000000000000 );
#3197 = LINE( '', #3677, #3678 );
#3198 = VERTEX_POINT( '', #3679 );
#3199 = LINE( '', #3680, #3681 );
#3200 = LINE( '', #3682, #3683 );
#3201 = CIRCLE( '', #3684, 0.100000000000000 );
#3202 = LINE( '', #3685, #3686 );
#3203 = CIRCLE( '', #3687, 0.0500000000000000 );
#3204 = VERTEX_POINT( '', #3688 );
#3205 = VERTEX_POINT( '', #3689 );
#3206 = LINE( '', #3690, #3691 );
#3207 = VERTEX_POINT( '', #3692 );
#3208 = LINE( '', #3693, #3694 );
#3209 = VERTEX_POINT( '', #3695 );
#3210 = LINE( '', #3696, #3697 );
#3211 = LINE( '', #3698, #3699 );
#3212 = VERTEX_POINT( '', #3700 );
#3213 = LINE( '', #3701, #3702 );
#3214 = VERTEX_POINT( '', #3703 );
#3215 = LINE( '', #3704, #3705 );
#3216 = LINE( '', #3706, #3707 );
#3217 = VERTEX_POINT( '', #3708 );
#3218 = LINE( '', #3709, #3710 );
#3219 = VERTEX_POINT( '', #3711 );
#3220 = LINE( '', #3712, #3713 );
#3221 = LINE( '', #3714, #3715 );
#3222 = LINE( '', #3716, #3717 );
#3223 = LINE( '', #3718, #3719 );
#3224 = VERTEX_POINT( '', #3720 );
#3225 = VERTEX_POINT( '', #3721 );
#3226 = LINE( '', #3722, #3723 );
#3227 = VERTEX_POINT( '', #3724 );
#3228 = LINE( '', #3725, #3726 );
#3229 = VERTEX_POINT( '', #3727 );
#3230 = LINE( '', #3728, #3729 );
#3231 = LINE( '', #3730, #3731 );
#3232 = VERTEX_POINT( '', #3732 );
#3233 = LINE( '', #3733, #3734 );
#3234 = VERTEX_POINT( '', #3735 );
#3235 = LINE( '', #3736, #3737 );
#3236 = LINE( '', #3738, #3739 );
#3237 = VERTEX_POINT( '', #3740 );
#3238 = LINE( '', #3741, #3742 );
#3239 = LINE( '', #3743, #3744 );
#3240 = VERTEX_POINT( '', #3745 );
#3241 = LINE( '', #3746, #3747 );
#3242 = LINE( '', #3748, #3749 );
#3243 = LINE( '', #3750, #3751 );
#3244 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 1.55000000000000 ) );
#3245 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 1.70000000000000 ) );
#3246 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 1.70000000000000 ) );
#3247 = VECTOR( '', #3752, 1000.00000000000 );
#3248 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 1.55000000000000 ) );
#3249 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.70000000000000, 1.55000000000000 ) );
#3250 = VECTOR( '', #3753, 1000.00000000000 );
#3251 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 1.70000000000000 ) );
#3252 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 1.70000000000000 ) );
#3253 = VECTOR( '', #3754, 1000.00000000000 );
#3254 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.70000000000000, 1.70000000000000 ) );
#3255 = VECTOR( '', #3755, 1000.00000000000 );
#3256 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.70000000000000 ) );
#3257 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.70000000000000, 1.70000000000000 ) );
#3258 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.70000000000000 ) );
#3259 = VECTOR( '', #3756, 1000.00000000000 );
#3260 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.90000000000000, 1.50000000000000 ) );
#3261 = AXIS2_PLACEMENT_3D( '', #3757, #3758, #3759 );
#3262 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.90000000000000, 0.775000000000000 ) );
#3263 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.90000000000000, 1.50000000000000 ) );
#3264 = VECTOR( '', #3760, 1000.00000000000 );
#3265 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.90019852586559, 0.770548800831089 ) );
#3266 = AXIS2_PLACEMENT_3D( '', #3761, #3762, #3763 );
#3267 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.90548800831088, 0.711368338850676 ) );
#3268 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.90019852586560, 0.770548800831091 ) );
#3269 = VECTOR( '', #3764, 1000.00000000000 );
#3270 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75608358590770, 0.698014741343945 ) );
#3271 = CARTESIAN_POINT( '', ( -2.95000000000000, 8.97386843989081, 1.34313215005202 ) );
#3272 = VECTOR( '', #3765, 1000.00000000000 );
#3273 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75079410346242, 0.757195203324359 ) );
#3274 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75079410346242, 0.757195203324360 ) );
#3275 = VECTOR( '', #3766, 1000.00000000000 );
#3276 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75000000000000, 0.775000000000000 ) );
#3277 = AXIS2_PLACEMENT_3D( '', #3767, #3768, #3769 );
#3278 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75000000000000, 1.50000000000000 ) );
#3279 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75000000000000, 1.50000000000000 ) );
#3280 = VECTOR( '', #3770, 1000.00000000000 );
#3281 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.70000000000000, 1.55000000000000 ) );
#3282 = AXIS2_PLACEMENT_3D( '', #3771, #3772, #3773 );
#3283 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.55000000000000 ) );
#3284 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.55000000000000 ) );
#3285 = VECTOR( '', #3774, 1000.00000000000 );
#3286 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.75000000000000, 1.50000000000000 ) );
#3287 = AXIS2_PLACEMENT_3D( '', #3775, #3776, #3777 );
#3288 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.75000000000000, 0.775000000000000 ) );
#3289 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.75000000000000, 0.775000000000000 ) );
#3290 = VECTOR( '', #3778, 1000.00000000000 );
#3291 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.75079410346243, 0.757195203324351 ) );
#3292 = AXIS2_PLACEMENT_3D( '', #3779, #3780, #3781 );
#3293 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.75608358590771, 0.698014741343942 ) );
#3294 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.80059557759684, 0.200000000000000 ) );
#3295 = VECTOR( '', #3782, 1000.00000000000 );
#3296 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.90548800831090, 0.711368338850679 ) );
#3297 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.71775359039120, 0.387527121833913 ) );
#3298 = VECTOR( '', #3783, 1000.00000000000 );
#3299 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.90019852586562, 0.770548800831088 ) );
#3300 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.95000000000000, 0.213353597506737 ) );
#3301 = VECTOR( '', #3784, 1000.00000000000 );
#3302 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.90000000000000, 0.775000000000000 ) );
#3303 = AXIS2_PLACEMENT_3D( '', #3785, #3786, #3787 );
#3304 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.90000000000000, 1.50000000000000 ) );
#3305 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.90000000000000, 0.775000000000000 ) );
#3306 = VECTOR( '', #3788, 1000.00000000000 );
#3307 = AXIS2_PLACEMENT_3D( '', #3789, #3790, #3791 );
#3308 = CARTESIAN_POINT( '', ( -1.02500000000000, 0.000000000000000, 1.70000000000000 ) );
#3309 = AXIS2_PLACEMENT_3D( '', #3792, #3793, #3794 );
#3310 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.70000000000000, 1.70000000000000 ) );
#3311 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.70000000000000, 1.70000000000000 ) );
#3312 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.70000000000000, 1.70000000000000 ) );
#3313 = VECTOR( '', #3795, 1000.00000000000 );
#3314 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.70000000000000, 1.70000000000000 ) );
#3315 = VECTOR( '', #3796, 1000.00000000000 );
#3316 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.70000000000000, 1.70000000000000 ) );
#3317 = VECTOR( '', #3797, 1000.00000000000 );
#3318 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70000000000000, 1.70000000000000 ) );
#3319 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.70000000000000 ) );
#3320 = VECTOR( '', #3798, 1000.00000000000 );
#3321 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70000000000000, 1.70000000000000 ) );
#3322 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.70000000000000 ) );
#3323 = VECTOR( '', #3799, 1000.00000000000 );
#3324 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70000000000000, 1.70000000000000 ) );
#3325 = VECTOR( '', #3800, 1000.00000000000 );
#3326 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.90000000000000, 1.50000000000000 ) );
#3327 = AXIS2_PLACEMENT_3D( '', #3801, #3802, #3803 );
#3328 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.90000000000000, 1.50000000000000 ) );
#3329 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.90000000000000, 1.50000000000000 ) );
#3330 = VECTOR( '', #3804, 1000.00000000000 );
#3331 = AXIS2_PLACEMENT_3D( '', #3805, #3806, #3807 );
#3332 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.70000000000000, 1.55000000000000 ) );
#3333 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.70000000000000, 1.55000000000000 ) );
#3334 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.70000000000000, 1.55000000000000 ) );
#3335 = VECTOR( '', #3808, 1000.00000000000 );
#3336 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.75000000000000, 1.50000000000000 ) );
#3337 = AXIS2_PLACEMENT_3D( '', #3809, #3810, #3811 );
#3338 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.75000000000000, 0.775000000000000 ) );
#3339 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.75000000000000, 1.50000000000000 ) );
#3340 = VECTOR( '', #3812, 1000.00000000000 );
#3341 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.75079410346242, 0.757195203324360 ) );
#3342 = AXIS2_PLACEMENT_3D( '', #3813, #3814, #3815 );
#3343 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.75608358590769, 0.698014741343944 ) );
#3344 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.75079410346242, 0.757195203324360 ) );
#3345 = VECTOR( '', #3816, 1000.00000000000 );
#3346 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.90548800831088, 0.711368338850675 ) );
#3347 = CARTESIAN_POINT( '', ( 2.95000000000000, 8.97386843989081, 1.34313215005202 ) );
#3348 = VECTOR( '', #3817, 1000.00000000000 );
#3349 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.90019852586559, 0.770548800831088 ) );
#3350 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.90019852586560, 0.770548800831091 ) );
#3351 = VECTOR( '', #3818, 1000.00000000000 );
#3352 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.90000000000000, 0.775000000000000 ) );
#3353 = AXIS2_PLACEMENT_3D( '', #3819, #3820, #3821 );
#3354 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.90000000000000, 1.50000000000000 ) );
#3355 = VECTOR( '', #3822, 1000.00000000000 );
#3356 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.90000000000000, 1.50000000000000 ) );
#3357 = AXIS2_PLACEMENT_3D( '', #3823, #3824, #3825 );
#3358 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.90000000000000, 0.775000000000000 ) );
#3359 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.90000000000000, 0.775000000000000 ) );
#3360 = VECTOR( '', #3826, 1000.00000000000 );
#3361 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.90019852586562, 0.770548800831088 ) );
#3362 = AXIS2_PLACEMENT_3D( '', #3827, #3828, #3829 );
#3363 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.90548800831090, 0.711368338850678 ) );
#3364 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.95000000000000, 0.213353597506737 ) );
#3365 = VECTOR( '', #3830, 1000.00000000000 );
#3366 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.75608358590771, 0.698014741343941 ) );
#3367 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.75608358590772, 0.698014741343942 ) );
#3368 = VECTOR( '', #3831, 1000.00000000000 );
#3369 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.75079410346243, 0.757195203324351 ) );
#3370 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.80059557759684, 0.200000000000000 ) );
#3371 = VECTOR( '', #3832, 1000.00000000000 );
#3372 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.75000000000000, 0.775000000000000 ) );
#3373 = AXIS2_PLACEMENT_3D( '', #3833, #3834, #3835 );
#3374 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.75000000000000, 1.50000000000000 ) );
#3375 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.75000000000000, 0.775000000000000 ) );
#3376 = VECTOR( '', #3836, 1000.00000000000 );
#3377 = AXIS2_PLACEMENT_3D( '', #3837, #3838, #3839 );
#3378 = CARTESIAN_POINT( '', ( 2.45000000000000, 1.80059557759680, 0.200000000000008 ) );
#3379 = CARTESIAN_POINT( '', ( 0.0297081878644360, 2.01605959565076, -2.21068200159509 ) );
#3380 = VECTOR( '', #3840, 1000.00000000000 );
#3381 = CARTESIAN_POINT( '', ( 2.45000000000000, 1.95000000000000, 0.213353597506739 ) );
#3382 = CARTESIAN_POINT( '', ( 2.45000000000000, 9.01838043157991, 0.845117408708082 ) );
#3383 = VECTOR( '', #3841, 1000.00000000000 );
#3384 = CARTESIAN_POINT( '', ( 0.0297081878644360, 2.16546401805394, -2.19732840408835 ) );
#3385 = VECTOR( '', #3842, 1000.00000000000 );
#3386 = CARTESIAN_POINT( '', ( -2.45000000000000, 1.80059557759680, 0.200000000000008 ) );
#3387 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.80059557759680, 0.200000000000008 ) );
#3388 = VECTOR( '', #3843, 1000.00000000000 );
#3389 = CARTESIAN_POINT( '', ( -2.45000000000000, 1.95000000000000, 0.213353597506739 ) );
#3390 = CARTESIAN_POINT( '', ( -2.45000000000000, 9.01838043157991, 0.845117408708083 ) );
#3391 = VECTOR( '', #3844, 1000.00000000000 );
#3392 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.95000000000000, 0.213353597506739 ) );
#3393 = VECTOR( '', #3845, 1000.00000000000 );
#3394 = CARTESIAN_POINT( '', ( -2.97970818786443, 1.90284326708824, 0.740958569840883 ) );
#3395 = VECTOR( '', #3846, 1000.00000000000 );
#3396 = CARTESIAN_POINT( '', ( -1.27474080934666, 1.90019852586559, 0.770548800831089 ) );
#3397 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.90019852586559, 0.770548800831088 ) );
#3398 = VECTOR( '', #3847, 1000.00000000000 );
#3399 = CARTESIAN_POINT( '', ( 1.27474080934663, 1.90019852586559, 0.770548800831088 ) );
#3400 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.90019852586559, 0.770548800831089 ) );
#3401 = VECTOR( '', #3848, 1000.00000000000 );
#3402 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.90019852586559, 0.770548800831089 ) );
#3403 = VECTOR( '', #3849, 1000.00000000000 );
#3404 = CARTESIAN_POINT( '', ( -1.27474080934663, 1.75079410346242, 0.757195203324360 ) );
#3405 = CARTESIAN_POINT( '', ( -1.27474080934666, 1.75079410346242, 0.757195203324362 ) );
#3406 = VECTOR( '', #3850, 1000.00000000000 );
#3407 = CARTESIAN_POINT( '', ( -1.27182458365518, 1.90000000000000, 0.775000000000000 ) );
#3408 = CARTESIAN_POINT( '', ( -1.27182458365518, 1.90000000000000, 0.775000000000000 ) );
#3409 = CARTESIAN_POINT( '', ( -1.27200027686110, 1.90000048683785, 0.774825973320280 ) );
#3410 = CARTESIAN_POINT( '', ( -1.27226194470251, 1.90000121190748, 0.774566787511754 ) );
#3411 = CARTESIAN_POINT( '', ( -1.27267000697809, 1.90000723540535, 0.774119906822643 ) );
#3412 = CARTESIAN_POINT( '', ( -1.27297732429479, 1.90001480086735, 0.773759101726778 ) );
#3413 = CARTESIAN_POINT( '', ( -1.27333324127023, 1.90002826819705, 0.773301910956486 ) );
#3414 = CARTESIAN_POINT( '', ( -1.27359607022306, 1.90004215917004, 0.772932074792143 ) );
#3415 = CARTESIAN_POINT( '', ( -1.27389479017964, 1.90006370456259, 0.772464894801597 ) );
#3416 = CARTESIAN_POINT( '', ( -1.27411013593858, 1.90008425788612, 0.772087461695433 ) );
#3417 = CARTESIAN_POINT( '', ( -1.27434782520134, 1.90011429320088, 0.771611826543436 ) );
#3418 = CARTESIAN_POINT( '', ( -1.27451235817796, 1.90014185659052, 0.771228101410258 ) );
#3419 = CARTESIAN_POINT( '', ( -1.27465273187006, 1.90017294267877, 0.770840648748363 ) );
#3420 = CARTESIAN_POINT( '', ( -1.27471155213091, 1.90019002774729, 0.770645745677953 ) );
#3421 = CARTESIAN_POINT( '', ( -1.27474080934666, 1.90019852586561, 0.770548800831094 ) );
#3422 = CARTESIAN_POINT( '', ( -1.27182458365518, 1.75000000000000, 0.775000000000000 ) );
#3423 = CARTESIAN_POINT( '', ( -1.27182458365518, 1.90000000000000, 0.775000000000000 ) );
#3424 = VECTOR( '', #3851, 1000.00000000000 );
#3425 = CARTESIAN_POINT( '', ( -1.27182458365518, 1.75000000000000, 0.774999999999998 ) );
#3426 = CARTESIAN_POINT( '', ( -1.27323972157899, 1.75000000000000, 0.769411679057431 ) );
#3427 = CARTESIAN_POINT( '', ( -1.27428778622344, 1.75023887789709, 0.763407248793689 ) );
#3428 = CARTESIAN_POINT( '', ( -1.27474080934666, 1.75079410346242, 0.757195203324373 ) );
#3429 = CARTESIAN_POINT( '', ( 1.27182458365519, 1.90000000000000, 0.775000000000000 ) );
#3430 = CARTESIAN_POINT( '', ( 1.27182458365519, 1.75000000000000, 0.775000000000000 ) );
#3431 = CARTESIAN_POINT( '', ( 1.27182458365519, 1.90000000000000, 0.775000000000000 ) );
#3432 = VECTOR( '', #3852, 1000.00000000000 );
#3433 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75000000000000, 0.850000000000000 ) );
#3434 = AXIS2_PLACEMENT_3D( '', #3853, #3854, #3855 );
#3435 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.90000000000000, 0.850000000000000 ) );
#3436 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 0.850000000000000 ) );
#3437 = VECTOR( '', #3856, 1000.00000000000 );
#3438 = AXIS2_PLACEMENT_3D( '', #3857, #3858, #3859 );
#3439 = CARTESIAN_POINT( '', ( -1.27286968737085, 1.90000000000000, 0.770531056814808 ) );
#3440 = AXIS2_PLACEMENT_3D( '', #3860, #3861, #3862 );
#3441 = CARTESIAN_POINT( '', ( 1.27286968737085, 1.90000000000000, 0.770531056814808 ) );
#3442 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.90000000000000, 0.770531056814808 ) );
#3443 = VECTOR( '', #3863, 1000.00000000000 );
#3444 = CARTESIAN_POINT( '', ( 1.27474080934663, 1.75079410346242, 0.757195203324362 ) );
#3445 = AXIS2_PLACEMENT_3D( '', #3864, #3865, #3866 );
#3446 = CARTESIAN_POINT( '', ( 1.27474080934663, 1.75079410346242, 0.757195203324362 ) );
#3447 = VECTOR( '', #3867, 1000.00000000000 );
#3448 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.90000000000000, 0.775000000000000 ) );
#3449 = VECTOR( '', #3868, 1000.00000000000 );
#3450 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 0.850000000000000 ) );
#3451 = CARTESIAN_POINT( '', ( 1.27182458365519, -1.75000000000000, 0.775000000000000 ) );
#3452 = AXIS2_PLACEMENT_3D( '', #3869, #3870, #3871 );
#3453 = CARTESIAN_POINT( '', ( 1.27182458365519, -1.90000000000000, 0.775000000000000 ) );
#3454 = CARTESIAN_POINT( '', ( 1.27182458365519, -1.90000000000000, 0.775000000000000 ) );
#3455 = VECTOR( '', #3872, 1000.00000000000 );
#3456 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.90000000000000, 0.850000000000000 ) );
#3457 = AXIS2_PLACEMENT_3D( '', #3873, #3874, #3875 );
#3458 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70000000000000, 0.850000000000000 ) );
#3459 = VECTOR( '', #3876, 1000.00000000000 );
#3460 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.90000000000000, 1.50000000000000 ) );
#3461 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.90000000000000, 0.850000000000000 ) );
#3462 = VECTOR( '', #3877, 1000.00000000000 );
#3463 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.90000000000000, 0.775000000000000 ) );
#3464 = VECTOR( '', #3878, 1000.00000000000 );
#3465 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.90000000000000, 1.50000000000000 ) );
#3466 = VECTOR( '', #3879, 1000.00000000000 );
#3467 = AXIS2_PLACEMENT_3D( '', #3880, #3881, #3882 );
#3468 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70000000000000, 1.55000000000000 ) );
#3469 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70000000000000, 1.70000000000000 ) );
#3470 = VECTOR( '', #3883, 1000.00000000000 );
#3471 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 1.50000000000000 ) );
#3472 = AXIS2_PLACEMENT_3D( '', #3884, #3885, #3886 );
#3473 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 0.775000000000000 ) );
#3474 = VECTOR( '', #3887, 1000.00000000000 );
#3475 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.75000000000000, 1.50000000000000 ) );
#3476 = VECTOR( '', #3888, 1000.00000000000 );
#3477 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.75000000000000, 0.775000000000000 ) );
#3478 = VECTOR( '', #3889, 1000.00000000000 );
#3479 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.55000000000000 ) );
#3480 = VECTOR( '', #3890, 1000.00000000000 );
#3481 = CARTESIAN_POINT( '', ( -1.02500000000000, -4.33680868994202E-016, 1.55000000000000 ) );
#3482 = AXIS2_PLACEMENT_3D( '', #3891, #3892, #3893 );
#3483 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70000000000000, 1.55000000000000 ) );
#3484 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.55000000000000 ) );
#3485 = VECTOR( '', #3894, 1000.00000000000 );
#3486 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.55000000000000 ) );
#3487 = VECTOR( '', #3895, 1000.00000000000 );
#3488 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.70000000000000, 1.55000000000000 ) );
#3489 = VECTOR( '', #3896, 1000.00000000000 );
#3490 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 1.55000000000000 ) );
#3491 = VECTOR( '', #3897, 1000.00000000000 );
#3492 = CARTESIAN_POINT( '', ( 1.27474080934666, -1.90019852586562, 0.770548800831088 ) );
#3493 = CARTESIAN_POINT( '', ( 1.27182458365518, -1.90000000000000, 0.775000000000000 ) );
#3494 = CARTESIAN_POINT( '', ( 1.27200027686110, -1.90000048683785, 0.774825973320279 ) );
#3495 = CARTESIAN_POINT( '', ( 1.27226194470251, -1.90000121190748, 0.774566787511754 ) );
#3496 = CARTESIAN_POINT( '', ( 1.27267000697809, -1.90000723540535, 0.774119906822642 ) );
#3497 = CARTESIAN_POINT( '', ( 1.27297732429479, -1.90001480086735, 0.773759101726777 ) );
#3498 = CARTESIAN_POINT( '', ( 1.27333324127023, -1.90002826819705, 0.773301910956485 ) );
#3499 = CARTESIAN_POINT( '', ( 1.27359607022306, -1.90004215917004, 0.772932074792143 ) );
#3500 = CARTESIAN_POINT( '', ( 1.27389479017964, -1.90006370456259, 0.772464894801595 ) );
#3501 = CARTESIAN_POINT( '', ( 1.27411013593858, -1.90008425788613, 0.772087461695432 ) );
#3502 = CARTESIAN_POINT( '', ( 1.27434782520134, -1.90011429320088, 0.771611826543433 ) );
#3503 = CARTESIAN_POINT( '', ( 1.27451235817796, -1.90014185659052, 0.771228101410255 ) );
#3504 = CARTESIAN_POINT( '', ( 1.27465273187006, -1.90017294267877, 0.770840648748361 ) );
#3505 = CARTESIAN_POINT( '', ( 1.27471155213091, -1.90019002774729, 0.770645745677951 ) );
#3506 = CARTESIAN_POINT( '', ( 1.27474080934666, -1.90019852586561, 0.770548800831091 ) );
#3507 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.90019852586560, 0.770548800831087 ) );
#3508 = VECTOR( '', #3898, 1000.00000000000 );
#3509 = CARTESIAN_POINT( '', ( -1.27182458365518, -1.90000000000000, 0.775000000000000 ) );
#3510 = CARTESIAN_POINT( '', ( -1.27182458365519, -1.75000000000000, 0.775000000000000 ) );
#3511 = CARTESIAN_POINT( '', ( -1.27182458365518, -1.90000000000000, 0.775000000000000 ) );
#3512 = VECTOR( '', #3899, 1000.00000000000 );
#3513 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.75000000000000, 0.850000000000000 ) );
#3514 = AXIS2_PLACEMENT_3D( '', #3900, #3901, #3902 );
#3515 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.90000000000000, 0.850000000000000 ) );
#3516 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70000000000000, 0.850000000000000 ) );
#3517 = VECTOR( '', #3903, 1000.00000000000 );
#3518 = AXIS2_PLACEMENT_3D( '', #3904, #3905, #3906 );
#3519 = CARTESIAN_POINT( '', ( 2.45000000000000, -1.95000000000000, 0.213353597506737 ) );
#3520 = CARTESIAN_POINT( '', ( -0.250000000000000, -2.19036475512128, -2.47592600575052 ) );
#3521 = VECTOR( '', #3907, 1000.00000000000 );
#3522 = CARTESIAN_POINT( '', ( 2.45000000000000, -1.80059557759684, 0.200000000000006 ) );
#3523 = CARTESIAN_POINT( '', ( 2.45000000000000, -9.01838043157993, 0.845117408708376 ) );
#3524 = VECTOR( '', #3908, 1000.00000000000 );
#3525 = CARTESIAN_POINT( '', ( -0.250000000000000, -2.04096033271810, -2.48927960325726 ) );
#3526 = VECTOR( '', #3909, 1000.00000000000 );
#3527 = CARTESIAN_POINT( '', ( 1.27474080934663, -1.75079410346244, 0.757195203324351 ) );
#3528 = CARTESIAN_POINT( '', ( 1.27474080934666, -1.75079410346243, 0.757195203324351 ) );
#3529 = VECTOR( '', #3910, 1000.00000000000 );
#3530 = CARTESIAN_POINT( '', ( 1.27182458365519, -1.75000000000000, 0.775000000000004 ) );
#3531 = CARTESIAN_POINT( '', ( 1.27323972157899, -1.75000000000000, 0.769411679057454 ) );
#3532 = CARTESIAN_POINT( '', ( 1.27428778622348, -1.75023887789710, 0.763407248793681 ) );
#3533 = CARTESIAN_POINT( '', ( 1.27474080934666, -1.75079410346243, 0.757195203324363 ) );
#3534 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.75079410346244, 0.757195203324351 ) );
#3535 = VECTOR( '', #3911, 1000.00000000000 );
#3536 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.90000000000000, 1.50000000000000 ) );
#3537 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.90000000000000, 1.50000000000000 ) );
#3538 = VECTOR( '', #3912, 1000.00000000000 );
#3539 = AXIS2_PLACEMENT_3D( '', #3913, #3914, #3915 );
#3540 = CARTESIAN_POINT( '', ( -1.27474080934663, -1.90019852586560, 0.770548800831087 ) );
#3541 = CARTESIAN_POINT( '', ( 1.27474080934663, -1.90019852586562, 0.770548800831088 ) );
#3542 = VECTOR( '', #3916, 1000.00000000000 );
#3543 = CARTESIAN_POINT( '', ( -1.27474080934663, -1.90019852586561, 0.770548800831088 ) );
#3544 = CARTESIAN_POINT( '', ( -1.27467987679759, -1.90018180183440, 0.770743118080213 ) );
#3545 = CARTESIAN_POINT( '', ( -1.27458795296393, -1.90015657168915, 0.771036268239954 ) );
#3546 = CARTESIAN_POINT( '', ( -1.27439057613909, -1.90012095079824, 0.771515773278905 ) );
#3547 = CARTESIAN_POINT( '', ( -1.27420938586645, -1.90009569669592, 0.771897823166140 ) );
#3548 = CARTESIAN_POINT( '', ( -1.27395079539604, -1.90006854263343, 0.772370763994750 ) );
#3549 = CARTESIAN_POINT( '', ( -1.27371935225406, -1.90005024196479, 0.772745848286413 ) );
#3550 = CARTESIAN_POINT( '', ( -1.27340106538909, -1.90003144477255, 0.773209813272835 ) );
#3551 = CARTESIAN_POINT( '', ( -1.27312316996822, -1.90001969016441, 0.773576896674021 ) );
#3552 = CARTESIAN_POINT( '', ( -1.27274900920375, -1.90000881337399, 0.774030388870400 ) );
#3553 = CARTESIAN_POINT( '', ( -1.27242787305958, -1.90000323381497, 0.774388294833207 ) );
#3554 = CARTESIAN_POINT( '', ( -1.27209001596949, -1.90000038797861, 0.774739813552179 ) );
#3555 = CARTESIAN_POINT( '', ( -1.27191347379780, -1.90000012992945, 0.774912866636070 ) );
#3556 = CARTESIAN_POINT( '', ( -1.27182458365526, -1.90000000000000, 0.775000000000000 ) );
#3557 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.90000000000000, 0.775000000000000 ) );
#3558 = VECTOR( '', #3917, 1000.00000000000 );
#3559 = CARTESIAN_POINT( '', ( -2.45000000000000, -1.95000000000000, 0.213353597506743 ) );
#3560 = CARTESIAN_POINT( '', ( -2.70000000000000, -1.92774400415546, 0.462360968178709 ) );
#3561 = VECTOR( '', #3918, 1000.00000000000 );
#3562 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.95000000000000, 0.213353597506737 ) );
#3563 = VECTOR( '', #3919, 1000.00000000000 );
#3564 = CARTESIAN_POINT( '', ( 1.27474080934663, -1.90019852586562, 0.770548800831088 ) );
#3565 = VECTOR( '', #3920, 1000.00000000000 );
#3566 = CARTESIAN_POINT( '', ( -1.27474080934663, -1.75079410346243, 0.757195203324351 ) );
#3567 = CARTESIAN_POINT( '', ( -1.27474080934663, -1.75079410346243, 0.757195203324351 ) );
#3568 = VECTOR( '', #3921, 1000.00000000000 );
#3569 = CARTESIAN_POINT( '', ( -1.27474080934663, -1.75079410346242, 0.757195203324354 ) );
#3570 = CARTESIAN_POINT( '', ( -1.27426345484306, -1.75023914800788, 0.763404226705827 ) );
#3571 = CARTESIAN_POINT( '', ( -1.27326241976465, -1.75000000000000, 0.769414903876667 ) );
#3572 = CARTESIAN_POINT( '', ( -1.27182458365526, -1.75000000000000, 0.775000000000000 ) );
#3573 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.75000000000000, 0.775000000000000 ) );
#3574 = VECTOR( '', #3922, 1000.00000000000 );
#3575 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.75079410346244, 0.757195203324351 ) );
#3576 = VECTOR( '', #3923, 1000.00000000000 );
#3577 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.75000000000000, 1.50000000000000 ) );
#3578 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.75000000000000, 0.775000000000000 ) );
#3579 = VECTOR( '', #3924, 1000.00000000000 );
#3580 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.75000000000000, 1.50000000000000 ) );
#3581 = VECTOR( '', #3925, 1000.00000000000 );
#3582 = CARTESIAN_POINT( '', ( -2.45000000000000, -1.80059557759684, 0.200000000000000 ) );
#3583 = CARTESIAN_POINT( '', ( -2.70000000000000, -1.77833958175227, 0.449007370671972 ) );
#3584 = VECTOR( '', #3926, 1000.00000000000 );
#3585 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.76037508893008, 0.650000000000000 ) );
#3586 = AXIS2_PLACEMENT_3D( '', #3927, #3928, #3929 );
#3587 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.76037508893008, 0.650000000000000 ) );
#3588 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.76037508893008, 0.650000000000000 ) );
#3589 = VECTOR( '', #3930, 1000.00000000000 );
#3590 = AXIS2_PLACEMENT_3D( '', #3931, #3932, #3933 );
#3591 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.80059557759684, 0.200000000000000 ) );
#3592 = VECTOR( '', #3934, 1000.00000000000 );
#3593 = CARTESIAN_POINT( '', ( -2.45000000000000, -9.01838043157993, 0.845117408708376 ) );
#3594 = VECTOR( '', #3935, 1000.00000000000 );
#3595 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.90000000000000, 1.70000000000000 ) );
#3596 = VECTOR( '', #3936, 1000.00000000000 );
#3597 = AXIS2_PLACEMENT_3D( '', #3937, #3938, #3939 );
#3598 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70000000000000, 1.70000000000000 ) );
#3599 = VECTOR( '', #3940, 1000.00000000000 );
#3600 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.90000000000000, 0.650000000000000 ) );
#3601 = CARTESIAN_POINT( '', ( 1.27286968737085, -1.90000000000000, 0.770531056814804 ) );
#3602 = AXIS2_PLACEMENT_3D( '', #3941, #3942, #3943 );
#3603 = AXIS2_PLACEMENT_3D( '', #3944, #3945, #3946 );
#3604 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70000000000000, 0.650000000000000 ) );
#3605 = VECTOR( '', #3947, 1000.00000000000 );
#3606 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.90000000000000, 0.650000000000000 ) );
#3607 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70000000000000, 0.650000000000000 ) );
#3608 = VECTOR( '', #3948, 1000.00000000000 );
#3609 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.90000000000000, 0.650000000000000 ) );
#3610 = VECTOR( '', #3949, 1000.00000000000 );
#3611 = CARTESIAN_POINT( '', ( -1.27286968737085, -1.90000000000000, 0.770531056814804 ) );
#3612 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.90000000000000, 0.770531056814804 ) );
#3613 = VECTOR( '', #3950, 1000.00000000000 );
#3614 = AXIS2_PLACEMENT_3D( '', #3951, #3952, #3953 );
#3615 = AXIS2_PLACEMENT_3D( '', #3954, #3955, #3956 );
#3616 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.75000000000000, 1.50000000000000 ) );
#3617 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75000000000000, 1.50000000000000 ) );
#3618 = VECTOR( '', #3957, 1000.00000000000 );
#3619 = AXIS2_PLACEMENT_3D( '', #3958, #3959, #3960 );
#3620 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.75000000000000, 0.850000000000000 ) );
#3621 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.75000000000000, 1.50000000000000 ) );
#3622 = VECTOR( '', #3961, 1000.00000000000 );
#3623 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.90000000000000, 0.850000000000000 ) );
#3624 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 0.850000000000000 ) );
#3625 = VECTOR( '', #3962, 1000.00000000000 );
#3626 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.90000000000000, 1.50000000000000 ) );
#3627 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.90000000000000, 0.850000000000000 ) );
#3628 = VECTOR( '', #3963, 1000.00000000000 );
#3629 = AXIS2_PLACEMENT_3D( '', #3964, #3965, #3966 );
#3630 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.90000000000000, 1.50000000000000 ) );
#3631 = VECTOR( '', #3967, 1000.00000000000 );
#3632 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.90000000000000, 1.70000000000000 ) );
#3633 = VECTOR( '', #3968, 1000.00000000000 );
#3634 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.90000000000000, 0.775000000000000 ) );
#3635 = VECTOR( '', #3969, 1000.00000000000 );
#3636 = CARTESIAN_POINT( '', ( 1.27474080934663, 1.90019852586561, 0.770548800831090 ) );
#3637 = CARTESIAN_POINT( '', ( 1.27467987679759, 1.90018180183440, 0.770743118080216 ) );
#3638 = CARTESIAN_POINT( '', ( 1.27458795296393, 1.90015657168915, 0.771036268239958 ) );
#3639 = CARTESIAN_POINT( '', ( 1.27439057613908, 1.90012095079824, 0.771515773278907 ) );
#3640 = CARTESIAN_POINT( '', ( 1.27420938586645, 1.90009569669592, 0.771897823166145 ) );
#3641 = CARTESIAN_POINT( '', ( 1.27395079539604, 1.90006854263343, 0.772370763994755 ) );
#3642 = CARTESIAN_POINT( '', ( 1.27371935225406, 1.90005024196479, 0.772745848286416 ) );
#3643 = CARTESIAN_POINT( '', ( 1.27340106538909, 1.90003144477255, 0.773209813272840 ) );
#3644 = CARTESIAN_POINT( '', ( 1.27312316996822, 1.90001969016441, 0.773576896674023 ) );
#3645 = CARTESIAN_POINT( '', ( 1.27274900920374, 1.90000881337399, 0.774030388870404 ) );
#3646 = CARTESIAN_POINT( '', ( 1.27242787305957, 1.90000323381497, 0.774388294833199 ) );
#3647 = CARTESIAN_POINT( '', ( 1.27209001596950, 1.90000038797861, 0.774739813552187 ) );
#3648 = CARTESIAN_POINT( '', ( 1.27191347379780, 1.90000012992945, 0.774912866636072 ) );
#3649 = CARTESIAN_POINT( '', ( 1.27182458365526, 1.90000000000000, 0.775000000000000 ) );
#3650 = CARTESIAN_POINT( '', ( 1.27474080934663, 1.75079410346241, 0.757195203324359 ) );
#3651 = CARTESIAN_POINT( '', ( 1.27426345484304, 1.75023914800787, 0.763404226705840 ) );
#3652 = CARTESIAN_POINT( '', ( 1.27326241976467, 1.75000000000000, 0.769414903876684 ) );
#3653 = CARTESIAN_POINT( '', ( 1.27182458365525, 1.75000000000000, 0.775000000000000 ) );
#3654 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75000000000000, 0.775000000000000 ) );
#3655 = VECTOR( '', #3970, 1000.00000000000 );
#3656 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75079410346242, 0.757195203324360 ) );
#3657 = VECTOR( '', #3971, 1000.00000000000 );
#3658 = AXIS2_PLACEMENT_3D( '', #3972, #3973, #3974 );
#3659 = AXIS2_PLACEMENT_3D( '', #3975, #3976, #3977 );
#3660 = CARTESIAN_POINT( '', ( -2.97970818786443, 1.75343884468506, 0.727604972334153 ) );
#3661 = VECTOR( '', #3978, 1000.00000000000 );
#3662 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75000000000000, 1.50000000000000 ) );
#3663 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75000000000000, 1.50000000000000 ) );
#3664 = VECTOR( '', #3979, 1000.00000000000 );
#3665 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75000000000000, 1.50000000000000 ) );
#3666 = VECTOR( '', #3980, 1000.00000000000 );
#3667 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.76037508893006, 0.650000000000000 ) );
#3668 = AXIS2_PLACEMENT_3D( '', #3981, #3982, #3983 );
#3669 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.76037508893006, 0.650000000000000 ) );
#3670 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.76037508893006, 0.650000000000000 ) );
#3671 = VECTOR( '', #3984, 1000.00000000000 );
#3672 = AXIS2_PLACEMENT_3D( '', #3985, #3986, #3987 );
#3673 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75079410346242, 0.757195203324360 ) );
#3674 = VECTOR( '', #3988, 1000.00000000000 );
#3675 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.90000000000000, 0.650000000000000 ) );
#3676 = AXIS2_PLACEMENT_3D( '', #3989, #3990, #3991 );
#3677 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 0.650000000000000 ) );
#3678 = VECTOR( '', #3992, 1000.00000000000 );
#3679 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.90000000000000, 0.650000000000000 ) );
#3680 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.90000000000000, 0.650000000000000 ) );
#3681 = VECTOR( '', #3993, 1000.00000000000 );
#3682 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 0.650000000000000 ) );
#3683 = VECTOR( '', #3994, 1000.00000000000 );
#3684 = AXIS2_PLACEMENT_3D( '', #3995, #3996, #3997 );
#3685 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.75000000000000, 0.775000000000000 ) );
#3686 = VECTOR( '', #3998, 1000.00000000000 );
#3687 = AXIS2_PLACEMENT_3D( '', #3999, #4000, #4001 );
#3688 = CARTESIAN_POINT( '', ( 3.65000000000000, 0.500000000000008, 0.000000000000000 ) );
#3689 = CARTESIAN_POINT( '', ( 3.65000000000000, -0.500000000000008, 0.000000000000000 ) );
#3690 = CARTESIAN_POINT( '', ( 3.65000000000000, 0.500000000000008, 0.000000000000000 ) );
#3691 = VECTOR( '', #4002, 1000.00000000000 );
#3692 = CARTESIAN_POINT( '', ( 3.65000000000000, 0.500000000000008, 0.150000000000000 ) );
#3693 = CARTESIAN_POINT( '', ( 3.65000000000000, 0.500000000000008, 0.150000000000000 ) );
#3694 = VECTOR( '', #4003, 1000.00000000000 );
#3695 = CARTESIAN_POINT( '', ( 3.65000000000000, -0.500000000000008, 0.150000000000000 ) );
#3696 = CARTESIAN_POINT( '', ( 3.65000000000000, 0.500000000000008, 0.150000000000000 ) );
#3697 = VECTOR( '', #4004, 1000.00000000000 );
#3698 = CARTESIAN_POINT( '', ( 3.65000000000000, -0.500000000000008, 0.150000000000000 ) );
#3699 = VECTOR( '', #4005, 1000.00000000000 );
#3700 = CARTESIAN_POINT( '', ( 2.00000000000000, -0.500000000000008, 0.000000000000000 ) );
#3701 = CARTESIAN_POINT( '', ( 3.65000000000000, -0.500000000000008, 0.000000000000000 ) );
#3702 = VECTOR( '', #4006, 1000.00000000000 );
#3703 = CARTESIAN_POINT( '', ( 2.00000000000000, 0.500000000000008, 0.000000000000000 ) );
#3704 = CARTESIAN_POINT( '', ( 2.00000000000000, -0.500000000000008, 0.000000000000000 ) );
#3705 = VECTOR( '', #4007, 1000.00000000000 );
#3706 = CARTESIAN_POINT( '', ( 2.00000000000000, 0.500000000000008, 0.000000000000000 ) );
#3707 = VECTOR( '', #4008, 1000.00000000000 );
#3708 = CARTESIAN_POINT( '', ( 2.00000000000000, 0.500000000000008, 0.150000000000000 ) );
#3709 = CARTESIAN_POINT( '', ( 2.00000000000000, 0.500000000000008, 0.150000000000000 ) );
#3710 = VECTOR( '', #4009, 1000.00000000000 );
#3711 = CARTESIAN_POINT( '', ( 2.00000000000000, -0.500000000000008, 0.150000000000000 ) );
#3712 = CARTESIAN_POINT( '', ( 2.00000000000000, -0.500000000000008, 0.150000000000000 ) );
#3713 = VECTOR( '', #4010, 1000.00000000000 );
#3714 = CARTESIAN_POINT( '', ( 3.65000000000000, -0.500000000000008, 0.150000000000000 ) );
#3715 = VECTOR( '', #4011, 1000.00000000000 );
#3716 = CARTESIAN_POINT( '', ( 2.00000000000000, 0.500000000000008, 0.150000000000000 ) );
#3717 = VECTOR( '', #4012, 1000.00000000000 );
#3718 = CARTESIAN_POINT( '', ( 2.00000000000000, -0.500000000000008, 0.150000000000000 ) );
#3719 = VECTOR( '', #4013, 1000.00000000000 );
#3720 = CARTESIAN_POINT( '', ( -3.65000000000000, 0.500000000000008, 0.000000000000000 ) );
#3721 = CARTESIAN_POINT( '', ( -3.65000000000000, -0.500000000000008, 0.000000000000000 ) );
#3722 = CARTESIAN_POINT( '', ( -3.65000000000000, 0.500000000000008, 0.000000000000000 ) );
#3723 = VECTOR( '', #4014, 1000.00000000000 );
#3724 = CARTESIAN_POINT( '', ( -3.65000000000000, -0.500000000000008, 0.150000000000000 ) );
#3725 = CARTESIAN_POINT( '', ( -3.65000000000000, -0.500000000000008, 0.150000000000000 ) );
#3726 = VECTOR( '', #4015, 1000.00000000000 );
#3727 = CARTESIAN_POINT( '', ( -3.65000000000000, 0.500000000000008, 0.150000000000000 ) );
#3728 = CARTESIAN_POINT( '', ( -3.65000000000000, 0.500000000000008, 0.150000000000000 ) );
#3729 = VECTOR( '', #4016, 1000.00000000000 );
#3730 = CARTESIAN_POINT( '', ( -3.65000000000000, 0.500000000000008, 0.150000000000000 ) );
#3731 = VECTOR( '', #4017, 1000.00000000000 );
#3732 = CARTESIAN_POINT( '', ( -2.00000000000000, 0.500000000000008, 0.000000000000000 ) );
#3733 = CARTESIAN_POINT( '', ( -2.00000000000000, 0.500000000000008, 0.000000000000000 ) );
#3734 = VECTOR( '', #4018, 1000.00000000000 );
#3735 = CARTESIAN_POINT( '', ( -2.00000000000000, 0.500000000000008, 0.150000000000000 ) );
#3736 = CARTESIAN_POINT( '', ( -2.00000000000000, 0.500000000000008, 0.150000000000000 ) );
#3737 = VECTOR( '', #4019, 1000.00000000000 );
#3738 = CARTESIAN_POINT( '', ( -2.00000000000000, 0.500000000000008, 0.150000000000000 ) );
#3739 = VECTOR( '', #4020, 1000.00000000000 );
#3740 = CARTESIAN_POINT( '', ( -2.00000000000000, -0.500000000000008, 0.000000000000000 ) );
#3741 = CARTESIAN_POINT( '', ( -2.00000000000000, -0.500000000000008, 0.000000000000000 ) );
#3742 = VECTOR( '', #4021, 1000.00000000000 );
#3743 = CARTESIAN_POINT( '', ( -3.65000000000000, -0.500000000000008, 0.000000000000000 ) );
#3744 = VECTOR( '', #4022, 1000.00000000000 );
#3745 = CARTESIAN_POINT( '', ( -2.00000000000000, -0.500000000000008, 0.150000000000000 ) );
#3746 = CARTESIAN_POINT( '', ( -3.65000000000000, -0.500000000000008, 0.150000000000000 ) );
#3747 = VECTOR( '', #4023, 1000.00000000000 );
#3748 = CARTESIAN_POINT( '', ( -2.00000000000000, -0.500000000000008, 0.150000000000000 ) );
#3749 = VECTOR( '', #4024, 1000.00000000000 );
#3750 = CARTESIAN_POINT( '', ( -2.00000000000000, -0.500000000000008, 0.150000000000000 ) );
#3751 = VECTOR( '', #4025, 1000.00000000000 );
#3752 = DIRECTION( '', ( 0.000000000000000, -2.51019907984711E-016, 1.00000000000000 ) );
#3753 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3754 = DIRECTION( '', ( 0.000000000000000, -2.51019907984711E-016, 1.00000000000000 ) );
#3755 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3756 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#3757 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.70000000000000, 1.50000000000000 ) );
#3758 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3759 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#3760 = DIRECTION( '', ( 0.000000000000000, -1.31599711970654E-014, -1.00000000000000 ) );
#3761 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.95000000000000, 0.775000000000000 ) );
#3762 = DIRECTION( '', ( 1.00000000000000, 1.38040321271469E-045, 1.04894090727380E-031 ) );
#3763 = DIRECTION( '', ( 1.04894090727380E-031, -1.32614459778108E-014, -1.00000000000000 ) );
#3764 = DIRECTION( '', ( 0.000000000000000, 0.0890239833782041, -0.996029482687876 ) );
#3765 = DIRECTION( '', ( 0.000000000000000, -0.996029482687876, -0.0890239833782042 ) );
#3766 = DIRECTION( '', ( 5.45096678437632E-018, 0.0890239833782041, -0.996029482687876 ) );
#3767 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.95000000000000, 0.775000000000000 ) );
#3768 = DIRECTION( '', ( -1.00000000000000, -1.38040321271469E-045, -1.04894090727380E-031 ) );
#3769 = DIRECTION( '', ( 4.66726145839586E-061, 1.00000000000000, -1.31599711970654E-014 ) );
#3770 = DIRECTION( '', ( -8.05789217202290E-031, -1.31599711970654E-014, -1.00000000000000 ) );
#3771 = CARTESIAN_POINT( '', ( -2.95000000000000, 1.70000000000000, 1.50000000000000 ) );
#3772 = DIRECTION( '', ( -1.00000000000000, -0.000000000000000, 0.000000000000000 ) );
#3773 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, -1.32614459778108E-014 ) );
#3774 = DIRECTION( '', ( 6.12303176911189E-017, 1.00000000000000, 0.000000000000000 ) );
#3775 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.50000000000000 ) );
#3776 = DIRECTION( '', ( 1.00000000000000, -2.99204702930428E-032, -2.37389193643995E-064 ) );
#3777 = DIRECTION( '', ( -2.99204702930428E-032, -1.00000000000000, -6.12303176911189E-017 ) );
#3778 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#3779 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.95000000000000, 0.775000000000000 ) );
#3780 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3781 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#3782 = DIRECTION( '', ( 5.45096678437881E-018, 0.0890239833782447, 0.996029482687872 ) );
#3783 = DIRECTION( '', ( 9.66680032541690E-019, 0.996029482687872, -0.0890239833782449 ) );
#3784 = DIRECTION( '', ( 0.000000000000000, 0.0890239833782447, 0.996029482687872 ) );
#3785 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.95000000000000, 0.775000000000000 ) );
#3786 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3787 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 6.12303176911189E-017 ) );
#3788 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#3789 = CARTESIAN_POINT( '', ( -2.95000000000000, -1.70000000000000, 1.50000000000000 ) );
#3790 = DIRECTION( '', ( 1.00000000000000, -2.99204702930428E-032, 0.000000000000000 ) );
#3791 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#3792 = CARTESIAN_POINT( '', ( 0.000000000000000, 0.000000000000000, 1.70000000000000 ) );
#3793 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#3794 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3795 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#3796 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3797 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3798 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3799 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3800 = DIRECTION( '', ( 1.00000000000000, -2.99204702930428E-032, 0.000000000000000 ) );
#3801 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.70000000000000, 1.50000000000000 ) );
#3802 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3803 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#3804 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3805 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 1.50000000000000 ) );
#3806 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3807 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#3808 = DIRECTION( '', ( 6.12303176911189E-017, 1.00000000000000, 0.000000000000000 ) );
#3809 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.70000000000000, 1.50000000000000 ) );
#3810 = DIRECTION( '', ( -1.00000000000000, -0.000000000000000, 0.000000000000000 ) );
#3811 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, -1.32614459778108E-014 ) );
#3812 = DIRECTION( '', ( -8.05789217202290E-031, -1.31599711970654E-014, -1.00000000000000 ) );
#3813 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.95000000000000, 0.775000000000000 ) );
#3814 = DIRECTION( '', ( 1.00000000000000, 1.38040321271469E-045, 1.04894090727380E-031 ) );
#3815 = DIRECTION( '', ( 4.66726145839586E-061, 1.00000000000000, -1.31599711970654E-014 ) );
#3816 = DIRECTION( '', ( 5.45096678437632E-018, 0.0890239833782041, -0.996029482687876 ) );
#3817 = DIRECTION( '', ( 0.000000000000000, -0.996029482687876, -0.0890239833782042 ) );
#3818 = DIRECTION( '', ( 0.000000000000000, 0.0890239833782041, -0.996029482687876 ) );
#3819 = CARTESIAN_POINT( '', ( 2.95000000000000, 1.95000000000000, 0.775000000000000 ) );
#3820 = DIRECTION( '', ( 1.00000000000000, 1.38040321271469E-045, 1.04894090727380E-031 ) );
#3821 = DIRECTION( '', ( 1.04894090727380E-031, -1.32614459778108E-014, -1.00000000000000 ) );
#3822 = DIRECTION( '', ( 0.000000000000000, -1.31599711970654E-014, -1.00000000000000 ) );
#3823 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.70000000000000, 1.50000000000000 ) );
#3824 = DIRECTION( '', ( 1.00000000000000, -2.99204702930428E-032, 0.000000000000000 ) );
#3825 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#3826 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#3827 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.95000000000000, 0.775000000000000 ) );
#3828 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3829 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 6.12303176911189E-017 ) );
#3830 = DIRECTION( '', ( 0.000000000000000, 0.0890239833782447, 0.996029482687872 ) );
#3831 = DIRECTION( '', ( 7.70371977754894E-034, -0.996029482687872, 0.0890239833782449 ) );
#3832 = DIRECTION( '', ( 5.45096678437881E-018, 0.0890239833782447, 0.996029482687872 ) );
#3833 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.95000000000000, 0.775000000000000 ) );
#3834 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3835 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#3836 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#3837 = CARTESIAN_POINT( '', ( 2.95000000000000, -1.70000000000000, 1.50000000000000 ) );
#3838 = DIRECTION( '', ( 1.00000000000000, -2.99204702930428E-032, -2.37389193643995E-064 ) );
#3839 = DIRECTION( '', ( -2.99204702930428E-032, -1.00000000000000, -6.12303176911189E-017 ) );
#3840 = DIRECTION( '', ( -0.707106781186548, 0.0629494623349666, -0.704299201470326 ) );
#3841 = DIRECTION( '', ( 0.000000000000000, -0.996029482687876, -0.0890239833782042 ) );
#3842 = DIRECTION( '', ( -0.707106781186548, 0.0629494623349666, -0.704299201470326 ) );
#3843 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3844 = DIRECTION( '', ( 0.000000000000000, -0.996029482687876, -0.0890239833782042 ) );
#3845 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3846 = DIRECTION( '', ( 0.707106781186548, 0.0629494623349666, -0.704299201470326 ) );
#3847 = DIRECTION( '', ( 1.00000000000000, 1.38040321271469E-045, 1.04894090727380E-031 ) );
#3848 = DIRECTION( '', ( -1.00000000000000, -1.21102989098680E-030, 1.35493990503064E-029 ) );
#3849 = DIRECTION( '', ( -1.00000000000000, -1.21102989098680E-030, 1.35493990503064E-029 ) );
#3850 = DIRECTION( '', ( -8.83659436653564E-018, -0.996029482687877, -0.0890239833781851 ) );
#3851 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 1.31599711970654E-014 ) );
#3852 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 1.31599711970654E-014 ) );
#3853 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75000000000000, 0.750000000000000 ) );
#3854 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 1.31599711970654E-014 ) );
#3855 = DIRECTION( '', ( 0.000000000000000, -1.31599711970654E-014, -1.00000000000000 ) );
#3856 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#3857 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.90000000000000, 0.750000000000000 ) );
#3858 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#3859 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3860 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.67029181213549, 0.750000000000000 ) );
#3861 = DIRECTION( '', ( 0.000000000000000, -0.0890239833782033, 0.996029482687876 ) );
#3862 = DIRECTION( '', ( 0.000000000000000, 0.996029482687876, 0.0890239833782033 ) );
#3863 = DIRECTION( '', ( 1.00000000000000, 3.85185988877447E-034, -1.96135847718362E-017 ) );
#3864 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.67029181213549, 0.750000000000000 ) );
#3865 = DIRECTION( '', ( 0.000000000000000, -0.0890239833782033, 0.996029482687876 ) );
#3866 = DIRECTION( '', ( 0.000000000000000, 0.996029482687876, 0.0890239833782033 ) );
#3867 = DIRECTION( '', ( 9.33808978938461E-033, -0.996029482687878, -0.0890239833781765 ) );
#3868 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3869 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75000000000000, 0.750000000000000 ) );
#3870 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#3871 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#3872 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#3873 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.90000000000000, 0.750000000000000 ) );
#3874 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#3875 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3876 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#3877 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#3878 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3879 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3880 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70000000000000, 1.50000000000000 ) );
#3881 = DIRECTION( '', ( -1.00000000000000, 2.99204702930428E-032, 0.000000000000000 ) );
#3882 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#3883 = DIRECTION( '', ( 0.000000000000000, -6.12303176911189E-017, 1.00000000000000 ) );
#3884 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.70000000000000, 1.50000000000000 ) );
#3885 = DIRECTION( '', ( 1.00000000000000, -2.99204702930428E-032, -2.37389193643995E-064 ) );
#3886 = DIRECTION( '', ( -2.99204702930428E-032, -1.00000000000000, -6.12303176911189E-017 ) );
#3887 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#3888 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3889 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3890 = DIRECTION( '', ( 1.00000000000000, -2.99204702930428E-032, 0.000000000000000 ) );
#3891 = CARTESIAN_POINT( '', ( 0.000000000000000, -4.33680868994202E-016, 1.55000000000000 ) );
#3892 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#3893 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3894 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3895 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3896 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3897 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3898 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3899 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#3900 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.75000000000000, 0.750000000000000 ) );
#3901 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#3902 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#3903 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#3904 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.90000000000000, 0.750000000000000 ) );
#3905 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#3906 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3907 = DIRECTION( '', ( -0.707106781186548, -0.0629494623349954, -0.704299201470323 ) );
#3908 = DIRECTION( '', ( 0.000000000000000, 0.996029482687872, -0.0890239833782449 ) );
#3909 = DIRECTION( '', ( -0.707106781186548, -0.0629494623349954, -0.704299201470323 ) );
#3910 = DIRECTION( '', ( 0.000000000000000, 0.996029482687872, -0.0890239833782418 ) );
#3911 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3912 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3913 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70000000000000, 1.50000000000000 ) );
#3914 = DIRECTION( '', ( -1.00000000000000, 2.99204702930428E-032, 0.000000000000000 ) );
#3915 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#3916 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3917 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3918 = DIRECTION( '', ( 0.707106781186548, -0.0629494623349954, -0.704299201470323 ) );
#3919 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3920 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3921 = DIRECTION( '', ( 0.000000000000000, 0.996029482687872, -0.0890239833782418 ) );
#3922 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3923 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3924 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#3925 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3926 = DIRECTION( '', ( 0.707106781186548, -0.0629494623349954, -0.704299201470323 ) );
#3927 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.75143720255968, 0.750000000000000 ) );
#3928 = DIRECTION( '', ( 0.000000000000000, 0.996029482687872, -0.0890239833782447 ) );
#3929 = DIRECTION( '', ( 0.000000000000000, -0.0890239833782447, -0.996029482687872 ) );
#3930 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3931 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.75143720255968, 0.750000000000000 ) );
#3932 = DIRECTION( '', ( 0.000000000000000, 0.996029482687872, -0.0890239833782447 ) );
#3933 = DIRECTION( '', ( 0.000000000000000, -0.0890239833782447, -0.996029482687872 ) );
#3934 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3935 = DIRECTION( '', ( 0.000000000000000, 0.996029482687872, -0.0890239833782449 ) );
#3936 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#3937 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.70000000000000, 1.50000000000000 ) );
#3938 = DIRECTION( '', ( 1.00000000000000, -2.99204702930428E-032, -2.37389193643995E-064 ) );
#3939 = DIRECTION( '', ( -2.99204702930428E-032, -1.00000000000000, -6.12303176911189E-017 ) );
#3940 = DIRECTION( '', ( 0.000000000000000, -6.12303176911189E-017, 1.00000000000000 ) );
#3941 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.90000000000000, 0.750000000000000 ) );
#3942 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#3943 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3944 = CARTESIAN_POINT( '', ( 1.17500000000000, -1.67029181213563, 0.750000000000000 ) );
#3945 = DIRECTION( '', ( 0.000000000000000, -0.0890239833782440, -0.996029482687872 ) );
#3946 = DIRECTION( '', ( 0.000000000000000, -0.996029482687872, 0.0890239833782440 ) );
#3947 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#3948 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#3949 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3950 = DIRECTION( '', ( -1.00000000000000, 7.70371977754894E-034, -3.53236241041180E-017 ) );
#3951 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.90000000000000, 0.750000000000000 ) );
#3952 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#3953 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3954 = CARTESIAN_POINT( '', ( -1.17500000000000, -1.67029181213563, 0.750000000000000 ) );
#3955 = DIRECTION( '', ( 0.000000000000000, -0.0890239833782440, -0.996029482687872 ) );
#3956 = DIRECTION( '', ( 0.000000000000000, -0.996029482687872, 0.0890239833782440 ) );
#3957 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3958 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 1.50000000000000 ) );
#3959 = DIRECTION( '', ( -1.00000000000000, -0.000000000000000, 0.000000000000000 ) );
#3960 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, -1.32614459778108E-014 ) );
#3961 = DIRECTION( '', ( 0.000000000000000, -1.31599711970654E-014, -1.00000000000000 ) );
#3962 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#3963 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, 1.00000000000000 ) );
#3964 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.70000000000000, 1.50000000000000 ) );
#3965 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3966 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#3967 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3968 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#3969 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3970 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3971 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3972 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.75000000000000, 0.750000000000000 ) );
#3973 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 1.31599711970654E-014 ) );
#3974 = DIRECTION( '', ( 0.000000000000000, -1.31599711970654E-014, -1.00000000000000 ) );
#3975 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.90000000000000, 0.750000000000000 ) );
#3976 = DIRECTION( '', ( -0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#3977 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3978 = DIRECTION( '', ( 0.707106781186548, 0.0629494623349666, -0.704299201470326 ) );
#3979 = DIRECTION( '', ( 0.000000000000000, 1.31599711970654E-014, 1.00000000000000 ) );
#3980 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3981 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.75143720255967, 0.750000000000000 ) );
#3982 = DIRECTION( '', ( 0.000000000000000, -0.996029482687876, -0.0890239833782041 ) );
#3983 = DIRECTION( '', ( 0.000000000000000, 0.0890239833782041, -0.996029482687876 ) );
#3984 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3985 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.75143720255967, 0.750000000000000 ) );
#3986 = DIRECTION( '', ( 0.000000000000000, -0.996029482687876, -0.0890239833782041 ) );
#3987 = DIRECTION( '', ( 0.000000000000000, 0.0890239833782041, -0.996029482687876 ) );
#3988 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3989 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.90000000000000, 0.750000000000000 ) );
#3990 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#3991 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3992 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#3993 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3994 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#3995 = CARTESIAN_POINT( '', ( -1.17500000000000, 1.90000000000000, 0.750000000000000 ) );
#3996 = DIRECTION( '', ( -0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#3997 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3998 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#3999 = CARTESIAN_POINT( '', ( 1.17500000000000, 1.70000000000000, 1.50000000000000 ) );
#4000 = DIRECTION( '', ( -1.00000000000000, -0.000000000000000, 0.000000000000000 ) );
#4001 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, -1.32614459778108E-014 ) );
#4002 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#4003 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#4004 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#4005 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#4006 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#4007 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#4008 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#4009 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#4010 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#4011 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#4012 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#4013 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#4014 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#4015 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#4016 = DIRECTION( '', ( 0.000000000000000, -1.00000000000000, 0.000000000000000 ) );
#4017 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#4018 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#4019 = DIRECTION( '', ( -1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#4020 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
#4021 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#4022 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#4023 = DIRECTION( '', ( 1.00000000000000, 0.000000000000000, 0.000000000000000 ) );
#4024 = DIRECTION( '', ( 0.000000000000000, 1.00000000000000, 0.000000000000000 ) );
#4025 = DIRECTION( '', ( 0.000000000000000, 0.000000000000000, -1.00000000000000 ) );
ENDSEC;
END-ISO-10303-21;
