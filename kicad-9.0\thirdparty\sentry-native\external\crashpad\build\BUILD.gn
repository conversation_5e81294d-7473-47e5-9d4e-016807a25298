# Copyright 2017 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# When building in Chromium, these configs is used to set #defines that indicate
# whether code is being built standalone, or in Chromium, or potentially in some
# other configutation.

import("crashpad_buildconfig.gni")

config("crashpad_is_in_chromium") {
  if (crashpad_is_in_chromium) {
    defines = [ "CRASHPAD_IS_IN_CHROMIUM" ]
  }
}

config("crashpad_is_in_fuchsia") {
  if (crashpad_is_in_fuchsia) {
    defines = [ "CRASHPAD_IS_IN_FUCHSIA" ]
  }
}

config("flock_always_supported_defines") {
  defines =
      [ "CRASHPAD_FLOCK_ALWAYS_SUPPORTED=$crashpad_flock_always_supported" ]
}

group("default_exe_manifest_win") {
  if (crashpad_is_in_chromium) {
    deps = [ "//build/win:default_exe_manifest" ]
  }
}

config("crashpad_fuzzer_flags") {
  cflags = [
    "-fsanitize=address",
    "-fsanitize-address-use-after-scope",
    "-fsanitize=fuzzer",
  ]

  ldflags = [ "-fsanitize=address" ]
}

if (crashpad_is_apple) {
  group("apple_enable_arc") {
    # If `crashpad_is_in_chromium`, then because Chromium enables ARC
    # compilation by default, no special configuration is needed.

    if (crashpad_is_standalone) {
      public_configs = [ "//third_party/mini_chromium/mini_chromium/build/config:apple_enable_arc" ]
    }
  }
}

if (crashpad_is_ios) {
  group("ios_xctest") {
    if (crashpad_is_in_chromium) {
      public_configs = [ "//build/config/ios:xctest_config" ]
    } else if (crashpad_is_standalone) {
      public_configs = [
        "//third_party/mini_chromium/mini_chromium/build/ios:xctest_config",
      ]
    }
  }
}
