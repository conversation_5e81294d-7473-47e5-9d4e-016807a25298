///////////////////////////////////////////////////////////////////////////
// C++ code generated with wxFormBuilder (version 4.2.1-0-g80c4cb6)
// http://www.wxformbuilder.org/
//
// PLEASE DO *NOT* EDIT THIS FILE!
///////////////////////////////////////////////////////////////////////////

#pragma once

#include <wx/artprov.h>
#include <wx/xrc/xmlres.h>
#include <wx/intl.h>
#include "dialog_shim.h"
#include <wx/bitmap.h>
#include <wx/image.h>
#include <wx/icon.h>
#include <wx/statbmp.h>
#include <wx/gdicmn.h>
#include <wx/font.h>
#include <wx/colour.h>
#include <wx/settings.h>
#include <wx/string.h>
#include <wx/stattext.h>
#include <wx/sizer.h>
#include <wx/checkbox.h>
#include <wx/button.h>
#include <wx/dialog.h>

///////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
/// Class DIALOG_LOCKED_ITEMS_QUERY_BASE
///////////////////////////////////////////////////////////////////////////////
class DIALOG_LOCKED_ITEMS_QUERY_BASE : public DIALOG_SHIM
{
	private:

	protected:
		wxStaticBitmap* m_icon;
		wxStaticText* m_messageLine1;
		wxStaticText* m_messageLine2;
		wxCheckBox* m_doNotShowBtn;
		wxButton* m_overrideBtn;
		wxStdDialogButtonSizer* m_sdbSizer;
		wxButton* m_sdbSizerOK;
		wxButton* m_sdbSizerCancel;

		// Virtual event handlers, override them in your derived class
		virtual void OnInitDlg( wxInitDialogEvent& event ) { event.Skip(); }
		virtual void onOverrideLocks( wxCommandEvent& event ) { event.Skip(); }


	public:

		DIALOG_LOCKED_ITEMS_QUERY_BASE( wxWindow* parent, wxWindowID id = wxID_ANY, const wxString& title = _("Locked Items"), const wxPoint& pos = wxDefaultPosition, const wxSize& size = wxSize( -1,-1 ), long style = wxDEFAULT_DIALOG_STYLE|wxRESIZE_BORDER );

		~DIALOG_LOCKED_ITEMS_QUERY_BASE();

};

