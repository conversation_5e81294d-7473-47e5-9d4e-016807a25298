(footprint "DSUB-25_Female_Horizontal_P2.77x2.84mm_EdgePinOffset9.90mm_Housed_MountingHolesOffset11.32mm"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(locked yes)
	(layer "F.Cu")
	(descr "25-pin D-Sub connector, horizontal/angled (90 deg), THT-mount, female, pitch 2.77x2.84mm, pin-PCB-offset 9.9mm, distance of mounting holes 47.1mm, distance of mounting holes to PCB edge 11.32mm, see https://disti-assets.s3.amazonaws.com/tonar/files/datasheets/16730.pdf")
	(tags "25-pin D-Sub connector horizontal angled 90deg THT female pitch 2.77x2.84mm pin-PCB-offset 9.9mm mounting-holes-distance 47.1mm mounting-hole-offset 11.32mm")
	(property "Reference" "REF**"
		(at -16.62 -2.8 0)
		(layer "F.SilkS")
		(uuid "580cce0d-506f-4835-95e2-1eaf6e38ca97")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "DB25FEMELLE"
		(at -16.62 20.81 0)
		(layer "F.Fab")
		(uuid "05ebae5a-76ef-479d-94a4-7d703cee64f2")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "3a803808-2d18-47a9-9c13-51a55317aa2c")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" "25-pin female D-SUB connector"
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "dd988f4e-6d8b-4aa9-8539-867e7f4347d9")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -43.23 -1.86)
		(end 9.99 -1.86)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "1e5627ee-e197-4fc5-84fc-6748d2461bda")
	)
	(fp_line
		(start -43.23 12.68)
		(end -43.23 -1.86)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "c989639c-e742-4b91-ac19-0d019fb6b9f5")
	)
	(fp_line
		(start -0.25 -2.754338)
		(end 0.25 -2.754338)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "64563497-37a6-48e5-8c33-59acc8f7ef99")
	)
	(fp_line
		(start 0 -2.321325)
		(end -0.25 -2.754338)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f41e3dd7-1cac-446c-a252-db97b13a973b")
	)
	(fp_line
		(start 0.25 -2.754338)
		(end 0 -2.321325)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "0bb21478-bad9-4d61-8b8e-1338c7208632")
	)
	(fp_line
		(start 9.99 -1.86)
		(end 9.99 12.68)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "b21543f2-4d51-4cc2-aa5b-c01ea8f06dc5")
	)
	(fp_line
		(start -43.67 -2.31)
		(end -43.67 19.81)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "326c378f-07be-455a-831d-75d25819a290")
	)
	(fp_line
		(start -43.67 19.81)
		(end 10.43 19.81)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "c6879694-4d16-43f1-8ce2-f63108d5b90b")
	)
	(fp_line
		(start 10.43 -2.31)
		(end -43.67 -2.31)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "e1e90af5-f7b2-412c-879d-896748c9a188")
	)
	(fp_line
		(start 10.43 19.81)
		(end 10.43 -2.31)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "8800ed24-a0c1-4c28-aa12-7bdc8389d9af")
	)
	(fp_line
		(start -43.17 -1.8)
		(end -43.17 12.74)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "95cfc498-927a-4f3a-9026-5d09813df31f")
	)
	(fp_line
		(start -43.17 12.74)
		(end -43.17 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "7e10b955-3c0b-4440-b2ff-78ba8e78e260")
	)
	(fp_line
		(start -43.17 12.74)
		(end 9.93 12.74)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "23b045e8-ba53-419f-9c51-5a2dd2d7d66b")
	)
	(fp_line
		(start -43.17 13.14)
		(end 9.93 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "620b0346-7f4a-4b58-87d5-04a6c0f27a86")
	)
	(fp_line
		(start -42.67 13.14)
		(end -42.67 18.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "296d819c-4b89-4c2b-8a2b-2211458f3a55")
	)
	(fp_line
		(start -42.67 18.14)
		(end -37.67 18.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "d079091f-f270-45c4-a5cf-901e592ffc93")
	)
	(fp_line
		(start -41.77 12.74)
		(end -41.77 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "be8d4b42-e204-4a73-be66-0ea1c6895b7a")
	)
	(fp_line
		(start -38.57 12.74)
		(end -38.57 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "26fa392b-7131-4003-8a85-55bbddc4c822")
	)
	(fp_line
		(start -37.67 13.14)
		(end -42.67 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "dedd3a64-06db-40db-b3ce-5ba8e271b761")
	)
	(fp_line
		(start -37.67 18.14)
		(end -37.67 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "5173eb8e-f832-46da-9577-887feb9c52ac")
	)
	(fp_line
		(start -35.77 13.14)
		(end -35.77 19.31)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "caace370-18fd-4879-98df-13f55185bdbb")
	)
	(fp_line
		(start -35.77 19.31)
		(end 2.53 19.31)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "6a4bd799-401b-4613-a5ca-880cab7a526e")
	)
	(fp_line
		(start 2.53 13.14)
		(end -35.77 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "21643740-f6b5-4769-a2ff-78bd5b44f104")
	)
	(fp_line
		(start 2.53 19.31)
		(end 2.53 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "e8b5f4c5-73c2-4a7d-97e1-f9ba6594dd8a")
	)
	(fp_line
		(start 4.43 13.14)
		(end 4.43 18.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "c249fa4e-4dce-44a8-9e6e-68c33c2e4141")
	)
	(fp_line
		(start 4.43 18.14)
		(end 9.43 18.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "9c239f58-dd1a-4203-a8c4-538595a406d4")
	)
	(fp_line
		(start 5.33 12.74)
		(end 5.33 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "280c5e33-84ed-4b46-9d24-b61fa4fe5bae")
	)
	(fp_line
		(start 8.53 12.74)
		(end 8.53 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "64dd92f7-67e5-4fb3-9f6e-5639db04dabd")
	)
	(fp_line
		(start 9.43 13.14)
		(end 4.43 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "54e1fea2-5fe6-4cfd-8ba9-3dac7ca396a6")
	)
	(fp_line
		(start 9.43 18.14)
		(end 9.43 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "45674e21-4c91-4215-a403-fbb6e9616ff1")
	)
	(fp_line
		(start 9.93 -1.8)
		(end -43.17 -1.8)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "3dab517c-b0fe-4763-8753-e031ddc69bc9")
	)
	(fp_line
		(start 9.93 12.74)
		(end -43.17 12.74)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "3a6b9398-a34c-4237-82d9-855f9f825dad")
	)
	(fp_line
		(start 9.93 12.74)
		(end 9.93 -1.8)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "7fd6c35a-5ae9-4648-8610-ace41723296a")
	)
	(fp_line
		(start 9.93 13.14)
		(end 9.93 12.74)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "a568c026-9722-47b3-bf0f-fb1d00cf6ce8")
	)
	(fp_arc
		(start -41.77 1.42)
		(mid -40.17 -0.18)
		(end -38.57 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "4bd85b23-64b5-430e-992a-9e15dbb094aa")
	)
	(fp_arc
		(start 5.33 1.42)
		(mid 6.93 -0.18)
		(end 8.53 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "3bdf036b-36c5-482e-99e2-ef747151657d")
	)
	(fp_text user "${REFERENCE}"
		(at -16.62 16.225 0)
		(layer "F.Fab")
		(uuid "32d77869-7fdd-45c3-9014-3a4efad6d653")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "0" thru_hole circle
		(at -40.17 1.42)
		(size 4 4)
		(drill 3.2)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "38b45d59-1fba-4129-83ad-c990e3b71912")
	)
	(pad "0" thru_hole circle
		(at 6.93 1.42)
		(size 4 4)
		(drill 3.2)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6ce7e24e-9562-45a9-9965-d75a87070569")
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7240c0d8-616d-4735-ae3f-6bc60a8d24f1")
	)
	(pad "2" thru_hole circle
		(at -2.77 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0c9f0bcb-becb-4628-8a68-63257b45e885")
	)
	(pad "3" thru_hole circle
		(at -5.54 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "b69d970c-b676-4558-97c5-35a381f547c4")
	)
	(pad "4" thru_hole circle
		(at -8.31 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "74205d2b-681e-415a-92e1-5d5b9400eb35")
	)
	(pad "5" thru_hole circle
		(at -11.08 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "d186a4f6-0900-4bc6-b274-e8463da0ffb2")
	)
	(pad "6" thru_hole circle
		(at -13.85 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "8484eba5-28a8-4874-9275-47ade6ea93d9")
	)
	(pad "7" thru_hole circle
		(at -16.62 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0fcd225c-21b5-4489-a253-89885028a49e")
	)
	(pad "8" thru_hole circle
		(at -19.39 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "2ce695a5-2b54-4cba-a68c-c81c5bd268bd")
	)
	(pad "9" thru_hole circle
		(at -22.16 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6da8aa3e-de1f-4cd9-bde4-d1165ce57a13")
	)
	(pad "10" thru_hole circle
		(at -24.93 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "5263e066-080b-48f5-a9c5-c3869140ad38")
	)
	(pad "11" thru_hole circle
		(at -27.7 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c722637a-95fb-4229-8fa0-50583da8087c")
	)
	(pad "12" thru_hole circle
		(at -30.47 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "282ae0bc-b271-46b4-95b2-a1cbde7f2514")
	)
	(pad "13" thru_hole circle
		(at -33.24 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7cdb27b2-dea9-4830-ba46-3ad5ccb6f582")
	)
	(pad "14" thru_hole circle
		(at -1.385 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "faf40ebe-ace7-4a7f-847e-6e0fce6096bc")
	)
	(pad "15" thru_hole circle
		(at -4.155 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "57da5e88-f066-4d2d-9e72-80b91654f2e5")
	)
	(pad "16" thru_hole circle
		(at -6.925 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "32ae68c6-978a-4695-ae73-e521456483b9")
	)
	(pad "17" thru_hole circle
		(at -9.695 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "bf305226-3efd-4778-9bef-54c3f074b7ed")
	)
	(pad "18" thru_hole circle
		(at -12.465 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "4f5ff7d5-52a8-4278-903f-2223948a913b")
	)
	(pad "19" thru_hole circle
		(at -15.235 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "73d51cb5-b65d-4ab0-894d-2afa9a41bed4")
	)
	(pad "20" thru_hole circle
		(at -18.005 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "735f3642-a919-425c-83a6-d632eef5ebe7")
	)
	(pad "21" thru_hole circle
		(at -20.775 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "ade072f4-82c1-4172-a9b6-f066e242cca9")
	)
	(pad "22" thru_hole circle
		(at -23.545 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f80ec9fd-0017-4c37-9d94-d7f5b9427a6d")
	)
	(pad "23" thru_hole circle
		(at -26.315 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "cfe30bd7-c117-41f6-af3a-9f14fb2002e0")
	)
	(pad "24" thru_hole circle
		(at -29.085 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "345ff03d-b9bd-45a0-8049-4e584ef48823")
	)
	(pad "25" thru_hole circle
		(at -31.855 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "16536fb1-5846-40dc-a308-49aa905edd26")
	)
	(embedded_fonts no)
	(model "${KICAD8_3DMODEL_DIR}/Connector_Dsub.3dshapes/DSUB-25_Female_Horizontal_P2.77x2.84mm_EdgePinOffset9.90mm_Housed_MountingHolesOffset11.32mm.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
