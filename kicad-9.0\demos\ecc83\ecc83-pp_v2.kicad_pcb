(kicad_pcb
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(layers
		(0 "F.Cu" signal "Dessus")
		(2 "B.Cu" signal "Dessous")
		(9 "<PERSON>.<PERSON>" user "F.Adhesive")
		(11 "<PERSON><PERSON>" user "B.Adhesive")
		(13 "F.Paste" user)
		(15 "B.Paste" user)
		(5 "F.SilkS" user "F.Silkscreen")
		(7 "B.SilkS" user "B.Silkscreen")
		(1 "F.Mask" user)
		(3 "B.Mask" user)
		(17 "Dwgs.User" user "User.Drawings")
		(19 "Cmts.User" user "User.Comments")
		(21 "Eco1.User" user "User.Eco1")
		(23 "Eco2.User" user "User.Eco2")
		(25 "Edge.Cuts" user)
		(27 "Margin" user)
		(31 "F.CrtYd" user "F.Courtyard")
		(29 "B.CrtYd" user "B.Courtyard")
		(35 "F.Fab" user)
		(33 "B.Fab" user)
	)
	(setup
		(stackup
			(layer "F.SilkS"
				(type "Top Silk Screen")
				(color "White")
			)
			(layer "F.Paste"
				(type "Top Solder Paste")
			)
			(layer "F.Mask"
				(type "Top Solder Mask")
				(color "Green")
				(thickness 0.01)
			)
			(layer "F.Cu"
				(type "copper")
				(thickness 0.035)
			)
			(layer "dielectric 1"
				(type "core")
				(thickness 1.51)
				(material "FR4")
				(epsilon_r 4.5)
				(loss_tangent 0.02)
			)
			(layer "B.Cu"
				(type "copper")
				(thickness 0.035)
			)
			(layer "B.Mask"
				(type "Bottom Solder Mask")
				(color "Green")
				(thickness 0.01)
			)
			(layer "B.Paste"
				(type "Bottom Solder Paste")
			)
			(layer "B.SilkS"
				(type "Bottom Silk Screen")
				(color "White")
			)
			(copper_finish "None")
			(dielectric_constraints no)
		)
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(tenting front back)
		(pcbplotparams
			(layerselection 0x00000000_00000000_00000000_000000a5)
			(plot_on_all_layers_selection 0x00000000_00000000_00000000_00000000)
			(disableapertmacros no)
			(usegerberextensions yes)
			(usegerberattributes no)
			(usegerberadvancedattributes no)
			(creategerberjobfile no)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 6)
			(plotframeref no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(pdf_metadata yes)
			(pdf_single_document no)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plot_black_and_white yes)
			(plotinvisibletext no)
			(sketchpadsonfab no)
			(plotpadnumbers no)
			(hidednponfab no)
			(sketchdnponfab yes)
			(crossoutdnponfab yes)
			(subtractmaskfromsilk no)
			(outputformat 1)
			(mirror no)
			(drillshape 1)
			(scaleselection 1)
			(outputdirectory "")
		)
	)
	(net 0 "")
	(net 1 "GND")
	(net 2 "Net-(P3-P1)")
	(net 3 "Net-(P2-P1)")
	(net 4 "Net-(U1A-K)")
	(net 5 "Net-(P1-PM)")
	(net 6 "Net-(P4-P1)")
	(net 7 "Net-(P4-PM)")
	(net 8 "Net-(U1A-G)")
	(net 9 "Net-(U1B-K)")
	(net 10 "unconnected-(P5-Pad1)")
	(net 11 "unconnected-(P6-Pad1)")
	(net 12 "unconnected-(P7-Pad1)")
	(net 13 "unconnected-(P8-Pad1)")
	(footprint "Footprints:CP_Radial_D12.5mm_P7.50mm"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a58357")
		(at 133.1 102.9 90)
		(descr "CP, Radial series, Radial, pin pitch=7.50mm, , diameter=12.5mm, Electrolytic Capacitor")
		(tags "CP Radial series Radial pin pitch 7.50mm  diameter 12.5mm Electrolytic Capacitor")
		(property "Reference" "C1"
			(at 3.75 -7.62 90)
			(layer "F.SilkS")
			(uuid "7d7bcc49-4f4b-4151-a5cb-f2f7b481ac46")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "10uF"
			(at 3.75 7.62 90)
			(layer "F.Fab")
			(uuid "5e28f9f7-ba51-4c50-ae64-50317fe3e807")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 90)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "6dbbc28d-f254-4b8d-aea6-b423ca401d91")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 90)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "850a506d-a49e-453e-baab-1b64365d6a31")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "CP_*")
		(path "/00000000-0000-0000-0000-00004549f4be")
		(sheetname "Racine")
		(sheetfile "ecc83-pp_v2.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 3.83 -6.33)
			(end 3.83 6.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b38e6cd5-62c8-419b-bb0e-c87c2de9f4eb")
		)
		(fp_line
			(start 3.79 -6.33)
			(end 3.79 6.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "40cdf30c-58c9-410a-b3e1-471ce4257fd7")
		)
		(fp_line
			(start 3.75 -6.33)
			(end 3.75 6.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c6ba61b1-bf2c-4e97-af62-d9d124f2c21f")
		)
		(fp_line
			(start 3.87 -6.329)
			(end 3.87 6.329)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "de523612-d518-4db6-a589-a4b84a4a8681")
		)
		(fp_line
			(start 3.91 -6.328)
			(end 3.91 6.328)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "898a437f-65dc-49f4-a71d-b58e887c9e5d")
		)
		(fp_line
			(start 3.95 -6.327)
			(end 3.95 6.327)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "616ed491-f7d0-46c5-98ad-3ebcbaef3758")
		)
		(fp_line
			(start 3.99 -6.326)
			(end 3.99 6.326)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5ec35acf-5e68-4b84-95fc-6b76efae8908")
		)
		(fp_line
			(start 4.03 -6.324)
			(end 4.03 6.324)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bc5f6e1e-bbbe-4d4d-81ac-9b8fb1e21fec")
		)
		(fp_line
			(start 4.07 -6.322)
			(end 4.07 6.322)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3f857e82-e3d9-4c5e-b3c2-c45e782325b9")
		)
		(fp_line
			(start 4.11 -6.32)
			(end 4.11 6.32)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a7649820-331c-414b-83ae-cbd90b21c277")
		)
		(fp_line
			(start 4.15 -6.318)
			(end 4.15 6.318)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9d5d33d0-9fd9-4975-bdcf-8c21e46a7238")
		)
		(fp_line
			(start 4.19 -6.315)
			(end 4.19 6.315)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "59465f36-a232-44e8-b4c8-f3b16466f01d")
		)
		(fp_line
			(start 4.23 -6.312)
			(end 4.23 6.312)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f8edf161-08fe-42d2-a3f5-dfb1071e1529")
		)
		(fp_line
			(start 4.27 -6.309)
			(end 4.27 6.309)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "694b5eb1-315a-4935-b8f6-7f0fbb9a3216")
		)
		(fp_line
			(start 4.31 -6.306)
			(end 4.31 6.306)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2adfcff8-ad63-483d-b30b-aed992a179cc")
		)
		(fp_line
			(start 4.35 -6.302)
			(end 4.35 6.302)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "27ab11d2-52f8-4d72-9390-99200ce13719")
		)
		(fp_line
			(start 4.39 -6.298)
			(end 4.39 6.298)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "554737c6-91e0-4f96-a312-a6713bb8a069")
		)
		(fp_line
			(start 4.43 -6.294)
			(end 4.43 6.294)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1db8124c-39c2-4ce2-873e-4071b585782d")
		)
		(fp_line
			(start 4.471 -6.29)
			(end 4.471 6.29)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "81b818a8-2ded-4f2b-9b79-d55071149c18")
		)
		(fp_line
			(start 4.511 -6.285)
			(end 4.511 6.285)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3e6c85c4-af2d-4b6e-97e8-3eab966b40fb")
		)
		(fp_line
			(start 4.551 -6.28)
			(end 4.551 6.28)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5ecfed81-9d16-4ae6-8bbc-c0ba00d63ad2")
		)
		(fp_line
			(start 4.591 -6.275)
			(end 4.591 6.275)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0522f5d9-1bd4-4223-8390-56239f31107b")
		)
		(fp_line
			(start 4.631 -6.269)
			(end 4.631 6.269)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "64b70010-dcc6-4300-a2c2-bf2fddf6334d")
		)
		(fp_line
			(start 4.671 -6.264)
			(end 4.671 6.264)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "92cd753b-356c-478f-991c-f8b3badc20f5")
		)
		(fp_line
			(start 4.711 -6.258)
			(end 4.711 6.258)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "568a2fb7-8417-4335-a7a5-bb9ebd19eb2d")
		)
		(fp_line
			(start 4.751 -6.252)
			(end 4.751 6.252)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a502cbc0-86a0-4462-9245-8f3ff603c8d0")
		)
		(fp_line
			(start 4.791 -6.245)
			(end 4.791 6.245)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cc5c6bdc-382a-42dd-b6ea-e5969d5587c3")
		)
		(fp_line
			(start 4.831 -6.238)
			(end 4.831 6.238)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7b688567-d18e-4b57-bc43-70fe484326f5")
		)
		(fp_line
			(start 4.871 -6.231)
			(end 4.871 6.231)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6137893d-0f6b-4cc7-ae7f-ec3e8c2790cb")
		)
		(fp_line
			(start 4.911 -6.224)
			(end 4.911 6.224)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b8b4c17f-50b2-45ee-bef6-4eb16db230a4")
		)
		(fp_line
			(start 4.951 -6.216)
			(end 4.951 6.216)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "780239b4-4862-4678-bf1b-23a37856dff9")
		)
		(fp_line
			(start 4.991 -6.209)
			(end 4.991 6.209)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ff64fe53-3971-4951-9bdd-404f47e34093")
		)
		(fp_line
			(start 5.031 -6.201)
			(end 5.031 6.201)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6098116c-d9f3-4ee8-981a-d2c313c5c794")
		)
		(fp_line
			(start 5.071 -6.192)
			(end 5.071 6.192)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9f6b99e7-cc29-4788-aff7-893ecf432993")
		)
		(fp_line
			(start 5.111 -6.184)
			(end 5.111 6.184)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7025353d-dd91-49c7-9810-883a191187ad")
		)
		(fp_line
			(start 5.151 -6.175)
			(end 5.151 6.175)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f3ee8b97-4b70-45d7-a734-03a94896e87a")
		)
		(fp_line
			(start 5.191 -6.166)
			(end 5.191 6.166)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "116c25f3-0593-4b33-8bef-cf01571a4ee9")
		)
		(fp_line
			(start 5.231 -6.156)
			(end 5.231 6.156)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f3efec49-0cf1-49e1-96f1-4eea182b0dc1")
		)
		(fp_line
			(start 5.271 -6.146)
			(end 5.271 6.146)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cfd0ce4b-a44d-45ff-9eb8-ed266ab7bd5e")
		)
		(fp_line
			(start 5.311 -6.137)
			(end 5.311 6.137)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "18bf8ab4-4aa5-45db-a4f0-a55a822ae39e")
		)
		(fp_line
			(start 5.351 -6.126)
			(end 5.351 6.126)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "baaf3f93-8850-4f9b-8eff-7240834b878b")
		)
		(fp_line
			(start 5.391 -6.116)
			(end 5.391 6.116)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a7ef23a3-d718-4e74-be71-5a1d307c1294")
		)
		(fp_line
			(start 5.431 -6.105)
			(end 5.431 6.105)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "29ea6a87-b889-45dc-87b2-9c925b10da64")
		)
		(fp_line
			(start 5.471 -6.094)
			(end 5.471 6.094)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d9122d52-7a2d-4c0d-bc21-3c5190b1e5ab")
		)
		(fp_line
			(start 5.511 -6.083)
			(end 5.511 6.083)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6fcbb876-832e-4d0b-9a72-95cabfeb7cf5")
		)
		(fp_line
			(start 5.551 -6.071)
			(end 5.551 6.071)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a3ac186e-622a-465c-8e54-5cf1f0199d30")
		)
		(fp_line
			(start 5.591 -6.059)
			(end 5.591 6.059)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cbed5007-a3d4-4cb1-8ccf-01ee566adef8")
		)
		(fp_line
			(start 5.631 -6.047)
			(end 5.631 6.047)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1b9e92de-17bc-4ea8-aab9-481ffb7fbe28")
		)
		(fp_line
			(start 5.671 -6.034)
			(end 5.671 6.034)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b999a946-1386-4f07-bfe0-a5faf41df158")
		)
		(fp_line
			(start 5.711 -6.021)
			(end 5.711 6.021)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0cc41f2d-8717-4e09-a484-701dd569f504")
		)
		(fp_line
			(start 5.751 -6.008)
			(end 5.751 6.008)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "796d4d6f-9645-48d8-8ef3-cecb6a028aae")
		)
		(fp_line
			(start 5.791 -5.995)
			(end 5.791 5.995)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7b5c60e4-0a21-4d73-9995-b845e4df0f88")
		)
		(fp_line
			(start 5.831 -5.981)
			(end 5.831 5.981)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "553c7530-27c0-4fb7-9634-8d9ea707b377")
		)
		(fp_line
			(start 5.871 -5.967)
			(end 5.871 5.967)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f7674b55-eb39-4f95-99de-db5afacce8ab")
		)
		(fp_line
			(start 5.911 -5.953)
			(end 5.911 5.953)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e9fce4dc-7080-45f2-8fc4-cbb16a9c1319")
		)
		(fp_line
			(start 5.951 -5.939)
			(end 5.951 5.939)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1ac4894a-34b8-4716-a464-b32092f74809")
		)
		(fp_line
			(start 5.991 -5.924)
			(end 5.991 5.924)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "aeebf330-a013-4dee-8137-9758091dc3fa")
		)
		(fp_line
			(start 6.031 -5.908)
			(end 6.031 5.908)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "64d09571-64ee-44ed-bef5-f6e112557a81")
		)
		(fp_line
			(start 6.071 -5.893)
			(end 6.071 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6a4a4b75-14cb-4197-95bc-2a56e2a686b5")
		)
		(fp_line
			(start 6.111 -5.877)
			(end 6.111 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b71e78e8-8b89-4149-bfe2-954ac1515618")
		)
		(fp_line
			(start 6.151 -5.861)
			(end 6.151 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f6011056-9d4f-4561-919f-989b3dd58bcb")
		)
		(fp_line
			(start 6.191 -5.845)
			(end 6.191 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "37820ca9-64ec-4939-80d8-68b4f94269b3")
		)
		(fp_line
			(start 6.231 -5.828)
			(end 6.231 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "be7157d8-38b4-4961-b45a-5efc35a9a01a")
		)
		(fp_line
			(start 6.271 -5.811)
			(end 6.271 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7a73c5f6-dd43-4ec2-a986-0b70d1e030a2")
		)
		(fp_line
			(start 6.311 -5.793)
			(end 6.311 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bba4b33d-07ad-4a6d-baa0-5e7e747bb7fe")
		)
		(fp_line
			(start 6.351 -5.776)
			(end 6.351 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9541380c-8606-4998-91cd-7d65e4d5a6d3")
		)
		(fp_line
			(start 6.391 -5.758)
			(end 6.391 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a71bb59c-fe2d-4204-b846-ae2f52e8100a")
		)
		(fp_line
			(start 6.431 -5.739)
			(end 6.431 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ee503a25-3cce-4538-8daa-c3072fe7dbaa")
		)
		(fp_line
			(start 6.471 -5.721)
			(end 6.471 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0beb5473-4397-4c5c-8eac-7ad2c45b5ba1")
		)
		(fp_line
			(start 6.511 -5.702)
			(end 6.511 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ae87c59c-71e3-4b7a-9baf-eb989710ad5b")
		)
		(fp_line
			(start 6.551 -5.682)
			(end 6.551 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3a65eb74-c76e-4d41-9e17-8e997e115e19")
		)
		(fp_line
			(start 6.591 -5.662)
			(end 6.591 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9540efbc-8d65-4156-a292-e79c827aaa1c")
		)
		(fp_line
			(start 6.631 -5.642)
			(end 6.631 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "08860f49-dcd9-4ea7-b3d5-9ede56d8819b")
		)
		(fp_line
			(start 6.671 -5.622)
			(end 6.671 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9d255b7a-780b-4d2d-a0dc-f2eacda2ff00")
		)
		(fp_line
			(start 6.711 -5.601)
			(end 6.711 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4876aea2-0b81-49c9-8337-327474a8c082")
		)
		(fp_line
			(start 6.751 -5.58)
			(end 6.751 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0a52e2ec-f5e3-4819-a2fe-83767c4c226b")
		)
		(fp_line
			(start 6.791 -5.558)
			(end 6.791 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9d3610ac-05a1-45e8-a347-4c3146274847")
		)
		(fp_line
			(start 6.831 -5.536)
			(end 6.831 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e16e383b-c33e-413f-8ccb-7b0bca7a5469")
		)
		(fp_line
			(start 6.871 -5.514)
			(end 6.871 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6ea6113b-495a-464d-9de4-178ef1e89d96")
		)
		(fp_line
			(start 6.911 -5.491)
			(end 6.911 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b1dcd5f1-274b-4f7a-8612-1a0f6cf31895")
		)
		(fp_line
			(start 6.951 -5.468)
			(end 6.951 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "77e52323-4443-46da-8838-96b6416a9bd1")
		)
		(fp_line
			(start 6.991 -5.445)
			(end 6.991 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d6822e23-ffaa-4370-ac1a-db641fff75c7")
		)
		(fp_line
			(start 7.031 -5.421)
			(end 7.031 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "64de9820-9eaa-443b-914a-a655e2e36eb1")
		)
		(fp_line
			(start 7.071 -5.397)
			(end 7.071 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "579af085-df9a-4b9e-8308-34b867007c87")
		)
		(fp_line
			(start 7.111 -5.372)
			(end 7.111 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4d51dacb-0447-45fd-b94c-ceda56fef2eb")
		)
		(fp_line
			(start 7.151 -5.347)
			(end 7.151 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1b3ada8e-0a30-4c58-b390-fd4f0a118191")
		)
		(fp_line
			(start 7.191 -5.322)
			(end 7.191 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9b038b5c-4d7c-4885-aae1-ed1b0ed6c3ad")
		)
		(fp_line
			(start 7.231 -5.296)
			(end 7.231 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "029cb283-1383-45a9-aa5d-980b31d07f0e")
		)
		(fp_line
			(start 7.271 -5.27)
			(end 7.271 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9ec1dfe5-feb8-4d93-9986-aafefa85acf3")
		)
		(fp_line
			(start 7.311 -5.243)
			(end 7.311 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "41853282-f601-4a9c-8061-dfb75d5befff")
		)
		(fp_line
			(start 7.351 -5.216)
			(end 7.351 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "926af1b6-8e15-4811-b1c1-ad67fc8586cd")
		)
		(fp_line
			(start 7.391 -5.188)
			(end 7.391 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0d0e2816-7b19-4873-aa88-147cba3ff7e9")
		)
		(fp_line
			(start 7.431 -5.16)
			(end 7.431 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "22eaabd3-2aaf-4d94-a264-7740d057b09e")
		)
		(fp_line
			(start 7.471 -5.131)
			(end 7.471 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "beca83a0-2c57-46d1-bdb9-9abbd79c33df")
		)
		(fp_line
			(start 7.511 -5.102)
			(end 7.511 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bf546f57-55b0-4916-9353-cc1e01abe0d3")
		)
		(fp_line
			(start 7.551 -5.073)
			(end 7.551 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "593bf0d8-27f3-4077-bdfe-cdbe34fc7fe6")
		)
		(fp_line
			(start 7.591 -5.043)
			(end 7.591 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9f562bbb-e895-4c9f-8a3b-e795aa669e12")
		)
		(fp_line
			(start 7.631 -5.012)
			(end 7.631 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a0fc2164-1f13-4618-b1f6-0a37783c0f93")
		)
		(fp_line
			(start 7.671 -4.982)
			(end 7.671 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b6bd1469-b9b2-44e1-9b30-5555b1bec973")
		)
		(fp_line
			(start 7.711 -4.95)
			(end 7.711 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "471a358a-ec50-4de1-a164-73b00d1f3d93")
		)
		(fp_line
			(start 7.751 -4.918)
			(end 7.751 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f20fa46e-57f0-4992-b7ce-3bd405a1a6f6")
		)
		(fp_line
			(start 7.791 -4.885)
			(end 7.791 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fe8d7800-e2d8-4be5-a642-a8e893e21b21")
		)
		(fp_line
			(start 7.831 -4.852)
			(end 7.831 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c2262784-da47-431c-9c51-03350d18feee")
		)
		(fp_line
			(start 7.871 -4.819)
			(end 7.871 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "27405bde-949d-4119-98b7-f9aaedf9b4a5")
		)
		(fp_line
			(start 7.911 -4.785)
			(end 7.911 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e2668ec6-9eab-48a3-9a3a-92f67593f54a")
		)
		(fp_line
			(start 7.951 -4.75)
			(end 7.951 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b52360fb-5082-4e73-8664-33195ed7a6c9")
		)
		(fp_line
			(start 7.991 -4.714)
			(end 7.991 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7e97b9b3-99dd-4032-bcfe-01b764d9fb39")
		)
		(fp_line
			(start 8.031 -4.678)
			(end 8.031 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "17ce5583-2b41-4859-b400-691e7d8abe95")
		)
		(fp_line
			(start 8.071 -4.642)
			(end 8.071 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1c47add7-c9d7-44a6-af5b-e7bf1e0428bf")
		)
		(fp_line
			(start 8.111 -4.605)
			(end 8.111 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2730832a-13c1-4c7b-a2c7-bf750377cc8a")
		)
		(fp_line
			(start 8.151 -4.567)
			(end 8.151 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ada5d12c-70f6-43a4-b67e-0661759c7c4b")
		)
		(fp_line
			(start 8.191 -4.528)
			(end 8.191 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1bb6e7af-99d3-4e63-8f4f-634405d65c4b")
		)
		(fp_line
			(start 8.231 -4.489)
			(end 8.231 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "eb043a4c-019a-41c6-90de-5c778c9ba878")
		)
		(fp_line
			(start 8.271 -4.449)
			(end 8.271 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "769a8149-c82d-41c5-bba9-103a56a423b6")
		)
		(fp_line
			(start 8.311 -4.408)
			(end 8.311 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c6a74ad2-660a-4124-a796-7ca886f9d4a4")
		)
		(fp_line
			(start 8.351 -4.367)
			(end 8.351 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1592d1a9-e09f-402a-9cd0-f0a6561e4080")
		)
		(fp_line
			(start 8.391 -4.325)
			(end 8.391 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "be8075e4-5426-4541-9f09-3a2eb0b7e241")
		)
		(fp_line
			(start 8.431 -4.282)
			(end 8.431 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b85260d2-1795-45fa-b5d2-28ab95a5eb19")
		)
		(fp_line
			(start 8.471 -4.238)
			(end 8.471 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e77ee8e3-386f-4db3-8dcf-cb561197ef2e")
		)
		(fp_line
			(start -2.442082 -4.2)
			(end -2.442082 -2.95)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3f885e8f-86ac-49ee-9872-853731e65d8f")
		)
		(fp_line
			(start 8.511 -4.194)
			(end 8.511 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "df0e4c0b-e518-4e2c-86fc-3d7247b2267e")
		)
		(fp_line
			(start 8.551 -4.148)
			(end 8.551 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "938ebbf5-ecb4-4115-8f61-c34fc412a2eb")
		)
		(fp_line
			(start 8.591 -4.102)
			(end 8.591 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7bc40b78-31c0-4c92-907a-68e2f4dca559")
		)
		(fp_line
			(start 8.631 -4.055)
			(end 8.631 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "04afd6bf-ddca-43c6-b06c-4dbdcf46ae41")
		)
		(fp_line
			(start 8.671 -4.007)
			(end 8.671 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6f49145c-4931-4e6d-9787-ca3304208ace")
		)
		(fp_line
			(start 8.711 -3.957)
			(end 8.711 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "882659b4-d7ea-4f57-acd7-7116af65b8f5")
		)
		(fp_line
			(start 8.751 -3.907)
			(end 8.751 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ee79780d-59b0-4420-9d17-a74c8d760f4e")
		)
		(fp_line
			(start 8.791 -3.856)
			(end 8.791 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9a5cf295-27b9-42a8-a489-802366baeb94")
		)
		(fp_line
			(start 8.831 -3.804)
			(end 8.831 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bb2d19b2-1169-43db-a787-feb72373403b")
		)
		(fp_line
			(start 8.871 -3.75)
			(end 8.871 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "17e1bd51-2f3b-4553-a988-a3a70982d72f")
		)
		(fp_line
			(start 8.911 -3.696)
			(end 8.911 -1.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3cabba31-e095-4540-a15e-1bbc75aabc37")
		)
		(fp_line
			(start 8.951 -3.64)
			(end 8.951 3.64)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6e3a9602-4e0f-4ced-a9e5-e17591d5ac00")
		)
		(fp_line
			(start 8.991 -3.583)
			(end 8.991 3.583)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "249d84f9-d55f-49d1-9136-1f04bed23948")
		)
		(fp_line
			(start -3.067082 -3.575)
			(end -1.817082 -3.575)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "50b5d1dc-9225-4982-9aaf-13259da4eedf")
		)
		(fp_line
			(start 9.031 -3.524)
			(end 9.031 3.524)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1d1acefe-53dc-4af7-a7c2-bc04456af6f3")
		)
		(fp_line
			(start 9.071 -3.464)
			(end 9.071 3.464)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d83fcc49-4e74-4039-959e-edf1857c3e19")
		)
		(fp_line
			(start 9.111 -3.402)
			(end 9.111 3.402)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4ba09ab2-6788-41a5-894f-12e2c2c48336")
		)
		(fp_line
			(start 9.151 -3.339)
			(end 9.151 3.339)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3da92c24-c397-40dd-a81a-7c3974939d1b")
		)
		(fp_line
			(start 9.191 -3.275)
			(end 9.191 3.275)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "06199d3c-1d0e-40e3-9389-ce3c1fea7bbe")
		)
		(fp_line
			(start 9.231 -3.208)
			(end 9.231 3.208)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "51ab9c9f-92c0-4799-aaac-02a6ff096e43")
		)
		(fp_line
			(start 9.271 -3.14)
			(end 9.271 3.14)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cec241d1-04c7-4501-8812-854e808f65c4")
		)
		(fp_line
			(start 9.311 -3.069)
			(end 9.311 3.069)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e2498729-c7e6-4634-b094-09878bca2af6")
		)
		(fp_line
			(start 9.351 -2.996)
			(end 9.351 2.996)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "02a870eb-af42-4db9-8982-6da01d86b0d3")
		)
		(fp_line
			(start 9.391 -2.921)
			(end 9.391 2.921)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ad7db419-da6c-4ac2-8647-c4c46aa122c3")
		)
		(fp_line
			(start 9.431 -2.844)
			(end 9.431 2.844)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0b6345dc-3ae7-4118-a2cd-b9f061f3b15a")
		)
		(fp_line
			(start 9.471 -2.764)
			(end 9.471 2.764)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f69c15b1-6cf1-4ade-8b6d-396757e99349")
		)
		(fp_line
			(start 9.511 -2.681)
			(end 9.511 2.681)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ac9ec529-8e75-47a4-b3f0-82829f353061")
		)
		(fp_line
			(start 9.551 -2.594)
			(end 9.551 2.594)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1150a6e4-a97b-4463-bfa7-068c69911a4d")
		)
		(fp_line
			(start 9.591 -2.504)
			(end 9.591 2.504)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "17d93579-7462-4c62-acf5-6ccccd8a2683")
		)
		(fp_line
			(start 9.631 -2.41)
			(end 9.631 2.41)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ab2b50e1-f9d8-4bbb-ac98-32150e7bc8bb")
		)
		(fp_line
			(start 9.671 -2.312)
			(end 9.671 2.312)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "94a709ad-a61e-4e9e-86ea-e08ffeed6f96")
		)
		(fp_line
			(start 9.711 -2.209)
			(end 9.711 2.209)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0890d7f4-bc9f-4b26-8534-c4df9209869d")
		)
		(fp_line
			(start 9.751 -2.1)
			(end 9.751 2.1)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "59729295-bb7d-4227-9b44-2b3e0e61060a")
		)
		(fp_line
			(start 9.791 -1.984)
			(end 9.791 1.984)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "07fd133e-c716-4fa1-ac0d-b70652541058")
		)
		(fp_line
			(start 9.831 -1.861)
			(end 9.831 1.861)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "984b7488-ed87-4407-a428-1638bfa6d94e")
		)
		(fp_line
			(start 9.871 -1.728)
			(end 9.871 1.728)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "02db6af1-3d9e-4300-a46f-049dccf464f4")
		)
		(fp_line
			(start 9.911 -1.583)
			(end 9.911 1.583)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cc466312-8a3e-47ec-9d37-931e9c6775b8")
		)
		(fp_line
			(start 9.951 -1.422)
			(end 9.951 1.422)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2bc38e9d-248c-413d-bf18-93aefa7b5fd6")
		)
		(fp_line
			(start 9.991 -1.241)
			(end 9.991 1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9f84589a-2685-4e70-a31c-c34e341ef466")
		)
		(fp_line
			(start 10.031 -1.028)
			(end 10.031 1.028)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f8f5b5b2-ebd0-47f0-adf8-c0aac7de445a")
		)
		(fp_line
			(start 10.071 -0.757)
			(end 10.071 0.757)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5fe7e1c1-db01-4bf8-b66a-dd0696aecaab")
		)
		(fp_line
			(start 10.111 -0.317)
			(end 10.111 0.317)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "63cd5f78-2dca-49b2-9283-57aedc3f89fe")
		)
		(fp_line
			(start 8.911 1.44)
			(end 8.911 3.696)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "af43e6b1-6781-4226-afa9-f99739aface3")
		)
		(fp_line
			(start 8.871 1.44)
			(end 8.871 3.75)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "62658ec7-a2e3-46d9-bda0-f144836b19c4")
		)
		(fp_line
			(start 8.831 1.44)
			(end 8.831 3.804)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9693a223-c036-43e6-935a-f939665a2a68")
		)
		(fp_line
			(start 8.791 1.44)
			(end 8.791 3.856)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "809b4bbb-2d5d-4532-8a10-df8fc582528f")
		)
		(fp_line
			(start 8.751 1.44)
			(end 8.751 3.907)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b4c1bf54-3579-4fb6-95d2-587e09fc4dc5")
		)
		(fp_line
			(start 8.711 1.44)
			(end 8.711 3.957)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bc25dbd9-47f6-4117-816d-1a31ed8cfabe")
		)
		(fp_line
			(start 8.671 1.44)
			(end 8.671 4.007)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7d3d89e6-36fc-43e5-9081-67a056843fd8")
		)
		(fp_line
			(start 8.631 1.44)
			(end 8.631 4.055)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3e18f53f-2b1f-4158-a71c-9df5389f6779")
		)
		(fp_line
			(start 8.591 1.44)
			(end 8.591 4.102)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c24a5758-732b-4055-8dd3-639014fd757c")
		)
		(fp_line
			(start 8.551 1.44)
			(end 8.551 4.148)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4ae2edc4-4933-43ca-8427-cf32fff0a0fd")
		)
		(fp_line
			(start 8.511 1.44)
			(end 8.511 4.194)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "636b748c-047e-4b82-aca3-63e021ec6728")
		)
		(fp_line
			(start 8.471 1.44)
			(end 8.471 4.238)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b68ba60a-def0-477d-b0a6-785f90ed2e58")
		)
		(fp_line
			(start 8.431 1.44)
			(end 8.431 4.282)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7095ebf9-b683-49e5-97e5-72abb9141830")
		)
		(fp_line
			(start 8.391 1.44)
			(end 8.391 4.325)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0c6765cc-b4e8-4d74-997c-8de48a32b7d5")
		)
		(fp_line
			(start 8.351 1.44)
			(end 8.351 4.367)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0bc05007-b2f6-4566-9335-a2599206834b")
		)
		(fp_line
			(start 8.311 1.44)
			(end 8.311 4.408)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ccde7c5a-180f-490b-afea-fab01a485ac8")
		)
		(fp_line
			(start 8.271 1.44)
			(end 8.271 4.449)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "39b4475e-4fef-4c7f-ae7d-ccfc0ee39c90")
		)
		(fp_line
			(start 8.231 1.44)
			(end 8.231 4.489)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9feea0e4-0ad7-479a-917d-b95ea1211ac7")
		)
		(fp_line
			(start 8.191 1.44)
			(end 8.191 4.528)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "66327098-402f-4df4-b059-5134d1b9366d")
		)
		(fp_line
			(start 8.151 1.44)
			(end 8.151 4.567)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "becf1471-f184-44e9-82bd-59c557fc30ce")
		)
		(fp_line
			(start 8.111 1.44)
			(end 8.111 4.605)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1b101b00-bd29-455c-b57e-4f6042991004")
		)
		(fp_line
			(start 8.071 1.44)
			(end 8.071 4.642)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f12c933e-4290-40c0-8833-4fa4e6b2921d")
		)
		(fp_line
			(start 8.031 1.44)
			(end 8.031 4.678)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d8b7a937-9cfb-4ccc-aa5c-f6b7bb3d5343")
		)
		(fp_line
			(start 7.991 1.44)
			(end 7.991 4.714)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9070d1b3-b2ad-43eb-870a-88c50610fed1")
		)
		(fp_line
			(start 7.951 1.44)
			(end 7.951 4.75)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a347b264-c7e1-4d9c-b349-39a544446e08")
		)
		(fp_line
			(start 7.911 1.44)
			(end 7.911 4.785)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f524c4a7-ecc3-4b98-93b0-36a56b28bf9e")
		)
		(fp_line
			(start 7.871 1.44)
			(end 7.871 4.819)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "06f200b4-42bf-468d-820b-cf0254958721")
		)
		(fp_line
			(start 7.831 1.44)
			(end 7.831 4.852)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c057a303-c93c-49d7-806d-efe65a41e3f1")
		)
		(fp_line
			(start 7.791 1.44)
			(end 7.791 4.885)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6124d4f1-8cd2-4829-8cc0-9faa9a9d86d3")
		)
		(fp_line
			(start 7.751 1.44)
			(end 7.751 4.918)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ba8daea6-81ab-4e8f-9478-1553c3943caa")
		)
		(fp_line
			(start 7.711 1.44)
			(end 7.711 4.95)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8b1a18f5-35a2-4843-8c2e-55f04d3a3622")
		)
		(fp_line
			(start 7.671 1.44)
			(end 7.671 4.982)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0220161f-9460-4e1f-ae3f-f4a6ec36bd62")
		)
		(fp_line
			(start 7.631 1.44)
			(end 7.631 5.012)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a33b3954-a0c6-4edd-bee7-21310c4ad28d")
		)
		(fp_line
			(start 7.591 1.44)
			(end 7.591 5.043)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "23ac6a97-bd0c-4cd2-9ceb-cebb8240fdd8")
		)
		(fp_line
			(start 7.551 1.44)
			(end 7.551 5.073)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a69e8e0d-ae3d-4ccd-86ed-82f289b61a62")
		)
		(fp_line
			(start 7.511 1.44)
			(end 7.511 5.102)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "78ed7b4c-18c2-4eac-a805-6e5386fe1b27")
		)
		(fp_line
			(start 7.471 1.44)
			(end 7.471 5.131)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ba0ecaff-f2e0-462e-b5c8-45daae60c833")
		)
		(fp_line
			(start 7.431 1.44)
			(end 7.431 5.16)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7bf0cf46-8e6e-42e5-843f-cd764e30264b")
		)
		(fp_line
			(start 7.391 1.44)
			(end 7.391 5.188)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b8a60f88-fa39-42bc-a142-7f6828b45e86")
		)
		(fp_line
			(start 7.351 1.44)
			(end 7.351 5.216)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "494ebd84-f05a-4570-a122-086d2f2ccae1")
		)
		(fp_line
			(start 7.311 1.44)
			(end 7.311 5.243)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1b284faf-1a2a-438f-bc64-1cca40a47ea2")
		)
		(fp_line
			(start 7.271 1.44)
			(end 7.271 5.27)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4a534456-ec3d-496a-a20e-1b13cf6c7eb5")
		)
		(fp_line
			(start 7.231 1.44)
			(end 7.231 5.296)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2f0975e9-ed43-4a7d-8301-03df4f1bcd30")
		)
		(fp_line
			(start 7.191 1.44)
			(end 7.191 5.322)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "13d555ea-086b-4e3b-8ccc-f0aca376c367")
		)
		(fp_line
			(start 7.151 1.44)
			(end 7.151 5.347)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3d8141ed-8986-42e1-aee3-6658e815a3c2")
		)
		(fp_line
			(start 7.111 1.44)
			(end 7.111 5.372)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4d43daf9-12fa-4633-af0a-0a57bfe50331")
		)
		(fp_line
			(start 7.071 1.44)
			(end 7.071 5.397)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b8fa6ccf-3f8f-47ad-9639-d0d4855f38d7")
		)
		(fp_line
			(start 7.031 1.44)
			(end 7.031 5.421)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bde5953f-3b2d-49a5-bce0-ae825107ad29")
		)
		(fp_line
			(start 6.991 1.44)
			(end 6.991 5.445)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c8f5de97-afcd-404d-945c-b68ea13e473d")
		)
		(fp_line
			(start 6.951 1.44)
			(end 6.951 5.468)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "19da9466-749d-45ed-b046-3e13c828ddff")
		)
		(fp_line
			(start 6.911 1.44)
			(end 6.911 5.491)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "743b01f4-c007-4535-9a27-7669421c04da")
		)
		(fp_line
			(start 6.871 1.44)
			(end 6.871 5.514)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d2f1c692-c0b4-497f-a496-014b4857e81e")
		)
		(fp_line
			(start 6.831 1.44)
			(end 6.831 5.536)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2f0c5923-5222-4aff-8cb3-430640483a21")
		)
		(fp_line
			(start 6.791 1.44)
			(end 6.791 5.558)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "13fa1c4f-00e9-4abe-89a2-71684baf1fb5")
		)
		(fp_line
			(start 6.751 1.44)
			(end 6.751 5.58)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "32d63a10-d769-4db5-a192-7d675c05ad24")
		)
		(fp_line
			(start 6.711 1.44)
			(end 6.711 5.601)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6153f7fb-d84f-42d1-9f7a-ad3dce3629d2")
		)
		(fp_line
			(start 6.671 1.44)
			(end 6.671 5.622)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "01d319b6-ae8d-44cf-852f-85659fb74a69")
		)
		(fp_line
			(start 6.631 1.44)
			(end 6.631 5.642)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c9fa2946-2586-4ae4-b090-c8ebfb54f48c")
		)
		(fp_line
			(start 6.591 1.44)
			(end 6.591 5.662)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3a9d8cb0-9c43-4224-8ef6-83cd0843155e")
		)
		(fp_line
			(start 6.551 1.44)
			(end 6.551 5.682)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b2ef917a-4ce8-4ba7-99d0-1b58a0a0d63a")
		)
		(fp_line
			(start 6.511 1.44)
			(end 6.511 5.702)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6aaa2ee6-b9b7-4866-940d-535e9507d7fb")
		)
		(fp_line
			(start 6.471 1.44)
			(end 6.471 5.721)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "234195da-e3ba-43e8-8c84-7aca6bbd682d")
		)
		(fp_line
			(start 6.431 1.44)
			(end 6.431 5.739)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "943bb3da-848e-4a4c-8b0c-b040937901fd")
		)
		(fp_line
			(start 6.391 1.44)
			(end 6.391 5.758)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1adcc2a4-aafc-4e79-b381-b648f869f73b")
		)
		(fp_line
			(start 6.351 1.44)
			(end 6.351 5.776)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f7533bbe-72de-47c5-9ed7-b1c4ea444761")
		)
		(fp_line
			(start 6.311 1.44)
			(end 6.311 5.793)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "824081d6-01fd-4ec3-8b00-2e969cf1ca50")
		)
		(fp_line
			(start 6.271 1.44)
			(end 6.271 5.811)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4977b7e2-43f4-4067-b15c-9c5d6f558de2")
		)
		(fp_line
			(start 6.231 1.44)
			(end 6.231 5.828)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "22ce3560-0cdc-42c6-8579-5dbcf74cc079")
		)
		(fp_line
			(start 6.191 1.44)
			(end 6.191 5.845)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0446bd4a-1438-402d-a002-8f922406e6fa")
		)
		(fp_line
			(start 6.151 1.44)
			(end 6.151 5.861)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bd5fe5d8-e124-4b83-98de-f4ae98c69a72")
		)
		(fp_line
			(start 6.111 1.44)
			(end 6.111 5.877)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e4fbaf72-697c-4515-9a30-05d0cab22c51")
		)
		(fp_line
			(start 6.071 1.44)
			(end 6.071 5.893)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5868f69a-a7ff-42a8-8dc0-c6e462705596")
		)
		(fp_circle
			(center 3.75 0)
			(end 10.12 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(fill no)
			(layer "F.SilkS")
			(uuid "e9e2165f-3ef6-4063-9acb-ca11d867f123")
		)
		(fp_circle
			(center 3.75 0)
			(end 10.25 0)
			(stroke
				(width 0.05)
				(type solid)
			)
			(fill no)
			(layer "F.CrtYd")
			(uuid "50b445d7-f419-41f7-bf7f-199fec8b9e9f")
		)
		(fp_line
			(start -0.991489 -3.3625)
			(end -0.991489 -2.1125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f2dcf5f2-86e8-41d1-a674-c4bde417449d")
		)
		(fp_line
			(start -1.616489 -2.7375)
			(end -0.366489 -2.7375)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "497dbfde-313d-4b71-9c8f-dde6571d345a")
		)
		(fp_circle
			(center 3.75 0)
			(end 10 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(fill no)
			(layer "F.Fab")
			(uuid "b597ff9d-da4e-4c00-9b79-cdf3a1cf1e01")
		)
		(fp_text user "${REFERENCE}"
			(at 3.75 0 90)
			(layer "F.Fab")
			(uuid "667d3d91-d4ce-4f68-a317-2b380cb74278")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0 90)
			(size 2.4 2.4)
			(drill 1.2)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 2 "Net-(P3-P1)")
			(pintype "passive")
			(uuid "f194fb04-aacf-4ad7-a4b8-9bc113a374a3")
		)
		(pad "2" thru_hole circle
			(at 7.5 0 90)
			(size 2.4 2.4)
			(drill 1.2)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "GND")
			(pintype "passive")
			(uuid "cbfb8a2e-bf7a-4a42-bd86-43b1d51269e9")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Capacitor_THT.3dshapes/CP_Radial_D12.5mm_P7.50mm.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a58389")
		(at 141 121.3 -90)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=7.62mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 7.62mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R1"
			(at 3.81 -2.37 270)
			(layer "F.SilkS")
			(uuid "769457be-7aa0-4a0a-834d-df3b923c0113")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "1.5K"
			(at 3.81 2.37 270)
			(layer "F.Fab")
			(uuid "4294d7df-fa69-4bff-ac52-262eacb16793")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "908fc449-328d-4a56-a281-8727f0dba151")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "065c376a-7a12-4a26-b75a-805190ad0f27")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R_* Resistor_*")
		(path "/00000000-0000-0000-0000-00004549f38a")
		(sheetname "Racine")
		(sheetfile "ecc83-pp_v2.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 0.54 1.37)
			(end 7.08 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ac4cac51-c841-42d4-b2fe-511e07e80437")
		)
		(fp_line
			(start 7.08 1.37)
			(end 7.08 1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3684ac98-0371-4696-85b9-9835a0d88e5b")
		)
		(fp_line
			(start 0.54 1.04)
			(end 0.54 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0040412e-c628-4bf5-b069-3d09fea642bf")
		)
		(fp_line
			(start 0.54 -1.04)
			(end 0.54 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4e650eed-2df9-4ae7-bd08-2f1590d34847")
		)
		(fp_line
			(start 0.54 -1.37)
			(end 7.08 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "dd88fd24-ba4d-4fdc-830d-f6eabfbe24a0")
		)
		(fp_line
			(start 7.08 -1.37)
			(end 7.08 -1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0b872d60-ea11-4b33-aabd-45a9407a0377")
		)
		(fp_line
			(start -1.05 1.5)
			(end 8.67 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "af63755c-10f2-4402-94b3-e0f6c837e42f")
		)
		(fp_line
			(start 8.67 1.5)
			(end 8.67 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "16063b50-b672-46e7-b971-898b9cfd690c")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "23321106-9a67-4f08-8667-cdab90198b7d")
		)
		(fp_line
			(start 8.67 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "92157757-a063-40ad-95e6-5556cc5e02c1")
		)
		(fp_line
			(start 0.66 1.25)
			(end 6.96 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0099047d-502d-4726-9fc0-bb0138c69456")
		)
		(fp_line
			(start 6.96 1.25)
			(end 6.96 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "55f9a832-d074-4dc7-8073-8f49b1c7688a")
		)
		(fp_line
			(start 0 0)
			(end 0.66 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f1dbcdcc-dc2d-4fa7-86cf-36ac4e396eab")
		)
		(fp_line
			(start 7.62 0)
			(end 6.96 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "1d5942a1-516e-42ed-b4b3-af8608e406fb")
		)
		(fp_line
			(start 0.66 -1.25)
			(end 0.66 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ea853abf-ab5b-4787-a5e0-1017be9eee33")
		)
		(fp_line
			(start 6.96 -1.25)
			(end 0.66 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "72b0a567-1fa8-4abd-bea5-20ba9a72b47e")
		)
		(fp_text user "${REFERENCE}"
			(at 3.81 0 270)
			(layer "F.Fab")
			(uuid "22e2e332-7896-4164-a44c-59cef8c61a3a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 8 "Net-(U1A-G)")
			(pintype "passive")
			(uuid "3c696ac4-cb66-4f73-9e6f-d83b6d1cf19a")
		)
		(pad "2" thru_hole oval
			(at 7.62 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 4 "Net-(U1A-K)")
			(pintype "passive")
			(uuid "7b142f9d-4153-4b29-aa31-a3a7fb4d8ec8")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a5838e")
		(at 165.2 107.3 90)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=7.62mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 7.62mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R2"
			(at 3.81 -2.37 90)
			(layer "F.SilkS")
			(uuid "31b9d9ef-0f0a-46ae-a58d-c5379d70adf1")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "1.5K"
			(at 3.81 2.37 90)
			(layer "F.Fab")
			(uuid "11c6d2f7-6f3b-46ba-adf4-0436fef40da5")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 90)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "8a6f0741-ad22-4232-97e3-7bbbd9d833fe")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 90)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "f3ba155c-19fe-443d-836f-79a27d72fe9d")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R_* Resistor_*")
		(path "/00000000-0000-0000-0000-00004549f39d")
		(sheetname "Racine")
		(sheetfile "ecc83-pp_v2.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 7.08 -1.37)
			(end 7.08 -1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d56399ad-de96-4440-a8f3-ad419e02bcc2")
		)
		(fp_line
			(start 0.54 -1.37)
			(end 7.08 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9f120e65-1b03-4692-b988-18c8c4f4bb38")
		)
		(fp_line
			(start 0.54 -1.04)
			(end 0.54 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "469937b9-4da6-4d00-842e-f6e9deafeb14")
		)
		(fp_line
			(start 0.54 1.04)
			(end 0.54 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "974ab6a9-2aaa-4f1a-91a6-d9c1572d7446")
		)
		(fp_line
			(start 7.08 1.37)
			(end 7.08 1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fddc150c-311e-425a-9fe4-ee8f3b4bdc3b")
		)
		(fp_line
			(start 0.54 1.37)
			(end 7.08 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9f64bba3-1995-4dfe-8ab9-1e0ae653eac9")
		)
		(fp_line
			(start 8.67 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "e21344ba-11c4-41d7-be05-25d509b64744")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "7e448c11-015f-4ded-adfb-6ec7733b3e2f")
		)
		(fp_line
			(start 8.67 1.5)
			(end 8.67 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "2c7eb351-6704-46c8-985d-7774ed0f10b9")
		)
		(fp_line
			(start -1.05 1.5)
			(end 8.67 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "ee84ceb3-e4a1-425c-940f-e860d25f0527")
		)
		(fp_line
			(start 6.96 -1.25)
			(end 0.66 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f2b9d0a0-3181-4452-a134-9978c3d48ac3")
		)
		(fp_line
			(start 0.66 -1.25)
			(end 0.66 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2f68a899-5ae2-4e11-9500-44ba48b020ac")
		)
		(fp_line
			(start 7.62 0)
			(end 6.96 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6d4ef06a-410e-4f8d-a088-943b6de57839")
		)
		(fp_line
			(start 0 0)
			(end 0.66 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "319edd6c-33ff-47ad-9b3d-b8166b0ccc52")
		)
		(fp_line
			(start 6.96 1.25)
			(end 6.96 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "81af3a16-e7af-4f19-b0a0-8cc8f82e0cd5")
		)
		(fp_line
			(start 0.66 1.25)
			(end 6.96 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "11a22096-38d5-4ad5-8926-c064f455d437")
		)
		(fp_text user "${REFERENCE}"
			(at 3.81 0 90)
			(layer "F.Fab")
			(uuid "841c6426-709b-4518-b013-c43d547b7607")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 90)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 9 "Net-(U1B-K)")
			(pintype "passive")
			(uuid "9c6fb000-f8dd-437e-ac64-6f7f6fb8ab0e")
		)
		(pad "2" thru_hole oval
			(at 7.62 0 90)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "GND")
			(pintype "passive")
			(uuid "ff2e0c06-6de8-48b4-9404-3be0fa7564d0")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a58393")
		(at 124.46 115 -90)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=7.62mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 7.62mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R3"
			(at 3.81 -2.37 270)
			(layer "F.SilkS")
			(uuid "578c73db-cbe3-4872-9666-b1d38d337063")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100K"
			(at 3.81 2.37 270)
			(layer "F.Fab")
			(uuid "d2b7be05-d40f-4196-8d4e-1c1cda234620")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "84d306da-b03b-4ec0-8596-1dbe4e69d29e")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "0ca99ad2-9e01-40fc-9e50-5ed060b6c4f1")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R_* Resistor_*")
		(path "/00000000-0000-0000-0000-00004549f3ad")
		(sheetname "Racine")
		(sheetfile "ecc83-pp_v2.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 0.54 1.37)
			(end 7.08 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8f474633-4637-42c2-90be-c289ab0aa515")
		)
		(fp_line
			(start 7.08 1.37)
			(end 7.08 1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "108ec64c-aa96-4923-bb51-77125ad2f8f9")
		)
		(fp_line
			(start 0.54 1.04)
			(end 0.54 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "62f02fe3-cbfd-459e-b001-ac88c0b6aef1")
		)
		(fp_line
			(start 0.54 -1.04)
			(end 0.54 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "87c1963a-f21b-4c7e-aa50-69f420d26012")
		)
		(fp_line
			(start 0.54 -1.37)
			(end 7.08 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a0b48dc7-7c34-4348-8736-9f02fc958d6c")
		)
		(fp_line
			(start 7.08 -1.37)
			(end 7.08 -1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a420ac7f-cd82-4d7a-b663-fd5814eed7fa")
		)
		(fp_line
			(start -1.05 1.5)
			(end 8.67 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3c5c8960-2dae-450c-8ac2-5d54bc883166")
		)
		(fp_line
			(start 8.67 1.5)
			(end 8.67 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "bb699cc3-5537-455e-a1da-d77961c6dfb4")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "fb801c67-b5f0-4904-b07b-8a5da58ca09a")
		)
		(fp_line
			(start 8.67 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "8bf9f989-14fb-426e-b786-ca4367571947")
		)
		(fp_line
			(start 0.66 1.25)
			(end 6.96 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6f9eba76-bd00-4304-a759-f556f665e4cc")
		)
		(fp_line
			(start 6.96 1.25)
			(end 6.96 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "37be6d8e-767f-4fa4-954c-9cfa54567bd0")
		)
		(fp_line
			(start 0 0)
			(end 0.66 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2870a8f1-7eb2-452c-ba12-9f5cc3a6209c")
		)
		(fp_line
			(start 7.62 0)
			(end 6.96 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a12c5dff-b4e1-44a2-acff-e77104fb18a8")
		)
		(fp_line
			(start 0.66 -1.25)
			(end 0.66 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f55614b0-18d9-4bcd-b4a2-2f9d6663eb1e")
		)
		(fp_line
			(start 6.96 -1.25)
			(end 0.66 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "7f63406e-e697-47a7-bb0d-406049d69ee0")
		)
		(fp_text user "${REFERENCE}"
			(at 3.81 0 270)
			(layer "F.Fab")
			(uuid "*************-4f38-9ae2-0546b847e25b")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 3 "Net-(P2-P1)")
			(pintype "passive")
			(uuid "b11bbb3a-5c93-45a2-b1ea-4e5f7337622b")
		)
		(pad "2" thru_hole oval
			(at 7.62 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "GND")
			(pintype "passive")
			(uuid "94b65cde-1f7c-4d6d-8605-c2946d5c993c")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a58398")
		(at 165.1 116.2 -90)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=7.62mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 7.62mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R4"
			(at 3.81 -2.37 270)
			(layer "F.SilkS")
			(uuid "42720b50-f013-430c-bde6-d33822d17a32")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "47K"
			(at 3.81 2.37 270)
			(layer "F.Fab")
			(uuid "f7e56de3-ec4d-4bff-a9c2-9eb9f1a4bfd4")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "51257287-063d-4ac0-ac29-efb246f9ec4d")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "6ffb58ef-aa69-48a9-9b8e-55aea029af4f")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R_* Resistor_*")
		(path "/00000000-0000-0000-0000-00004549f3a2")
		(sheetname "Racine")
		(sheetfile "ecc83-pp_v2.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 0.54 1.37)
			(end 7.08 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cef87710-c458-4860-9e9f-76205a9cf878")
		)
		(fp_line
			(start 7.08 1.37)
			(end 7.08 1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c149c1d2-e60c-43f5-b3af-7d6c5587efb1")
		)
		(fp_line
			(start 0.54 1.04)
			(end 0.54 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5acb4c4e-8027-4214-862c-f8c5b99485a2")
		)
		(fp_line
			(start 0.54 -1.04)
			(end 0.54 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f6b2d109-2c69-4b47-a437-06ecb5030bca")
		)
		(fp_line
			(start 0.54 -1.37)
			(end 7.08 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "11214f58-c8b0-47cd-9879-fb665511f496")
		)
		(fp_line
			(start 7.08 -1.37)
			(end 7.08 -1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e540b849-7851-4b97-9b00-2b0d658af390")
		)
		(fp_line
			(start -1.05 1.5)
			(end 8.67 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "7a0dbbe6-90a8-42c2-8834-257c9d25bc9a")
		)
		(fp_line
			(start 8.67 1.5)
			(end 8.67 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "6c093b16-6a64-4afd-9f11-1842d49f96ce")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "942fea91-fc27-40c6-ba85-8f62068c50bc")
		)
		(fp_line
			(start 8.67 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "407b6154-ab2c-48b9-9d23-d08b3f24a602")
		)
		(fp_line
			(start 0.66 1.25)
			(end 6.96 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "3ef76720-8d7b-4be5-97d2-46ad03ede464")
		)
		(fp_line
			(start 6.96 1.25)
			(end 6.96 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "e37d6f09-169f-4e7e-8f1a-c007f5af4a7a")
		)
		(fp_line
			(start 0 0)
			(end 0.66 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "3f2a6845-ce0a-411e-a939-e991f643a4d5")
		)
		(fp_line
			(start 7.62 0)
			(end 6.96 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b28a6436-6577-4d4a-84e4-810b1722c17d")
		)
		(fp_line
			(start 0.66 -1.25)
			(end 0.66 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c251c2d3-9012-4eac-a974-beef9ddd6527")
		)
		(fp_line
			(start 6.96 -1.25)
			(end 0.66 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b1a98a2a-a788-4608-ab11-6fe6781a6fab")
		)
		(fp_text user "${REFERENCE}"
			(at 3.81 0 270)
			(layer "F.Fab")
			(uuid "724ac06d-f0d9-419d-875c-0fac4c5d5eb3")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 5 "Net-(P1-PM)")
			(pintype "passive")
			(uuid "c2c7b538-8a90-4070-840d-a6ef3d92e68b")
		)
		(pad "2" thru_hole oval
			(at 7.62 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "GND")
			(pintype "passive")
			(uuid "40108a1e-d4fc-4e9f-933c-43691bfb28d0")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:MountingHole_3.2mm_M3_DIN965_Pad"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a583d2")
		(at 123.19 93.98)
		(descr "Mounting Hole 3.2mm, M3, DIN965")
		(tags "mounting hole 3.2mm m3 din965")
		(property "Reference" "P5"
			(at 4.31 -0.68 0)
			(layer "F.SilkS")
			(uuid "f622554e-e37f-4617-bc85-40506f16fc12")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "CONN_1"
			(at 6.11 -2.08 0)
			(layer "F.Fab")
			(uuid "31df529e-2abc-4796-b1d0-0dabad673c11")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "022f64ad-4e54-41d8-a365-b89b7f760c5d")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "9d8dfa8b-5e0b-40c5-b0d2-d97487b77646")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054a5830a")
		(attr through_hole exclude_from_pos_files exclude_from_bom)
		(fp_circle
			(center 0 0)
			(end 2.8 0)
			(stroke
				(width 0.15)
				(type solid)
			)
			(fill no)
			(layer "Cmts.User")
			(uuid "188b6e7f-d1c2-41bd-b014-d028eb009683")
		)
		(fp_circle
			(center 0 0)
			(end 3.05 0)
			(stroke
				(width 0.05)
				(type solid)
			)
			(fill no)
			(layer "F.CrtYd")
			(uuid "c02c0bb4-9889-462f-9c0b-2ff9e8205eef")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "86b49023-93c7-43af-bcf4-5075a8739408")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0)
			(size 5.6 5.6)
			(drill 3.2)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 10 "unconnected-(P5-Pad1)")
			(pinfunction "1")
			(pintype "passive+no_connect")
			(uuid "328ee246-1187-4edd-9910-fd11bffb6614")
		)
		(embedded_fonts no)
	)
	(footprint "Footprints:MountingHole_3.2mm_M3_DIN965_Pad"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a583d6")
		(at 165.1 93.98)
		(descr "Mounting Hole 3.2mm, M3, DIN965")
		(tags "mounting hole 3.2mm m3 din965")
		(property "Reference" "P6"
			(at -4.4 -0.18 0)
			(layer "F.SilkS")
			(uuid "104ef721-2162-4e8c-bc3f-bbbebc0311aa")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "CONN_1"
			(at 0 3.8 0)
			(layer "F.Fab")
			(uuid "8b63349e-c8e4-4954-989f-e572e317c069")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "d13585ac-3e12-40cb-926e-5ecb52fabe71")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "40393c2a-731a-464c-b38f-71b0f4bf3e73")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054a58363")
		(attr through_hole exclude_from_pos_files exclude_from_bom)
		(fp_circle
			(center 0 0)
			(end 2.8 0)
			(stroke
				(width 0.15)
				(type solid)
			)
			(fill no)
			(layer "Cmts.User")
			(uuid "51ff3d02-b1ad-445e-af19-0a7bc4070ab8")
		)
		(fp_circle
			(center 0 0)
			(end 3.05 0)
			(stroke
				(width 0.05)
				(type solid)
			)
			(fill no)
			(layer "F.CrtYd")
			(uuid "*************-4dd3-8d60-397c27ce07eb")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "e883d4f3-6098-409d-b5d9-e637a401ff55")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0)
			(size 5.6 5.6)
			(drill 3.2)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 11 "unconnected-(P6-Pad1)")
			(pinfunction "1")
			(pintype "passive+no_connect")
			(uuid "caed44f9-0751-40a4-ab47-a1d96dcacd04")
		)
		(embedded_fonts no)
	)
	(footprint "Footprints:MountingHole_3.2mm_M3_DIN965_Pad"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a583da")
		(at 165.1 129.54)
		(descr "Mounting Hole 3.2mm, M3, DIN965")
		(tags "mounting hole 3.2mm m3 din965")
		(property "Reference" "P7"
			(at 0 -3.8 0)
			(layer "F.SilkS")
			(uuid "a9dc950a-7618-45a1-9a63-b79ad1d7fbbc")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "CONN_1"
			(at -3.9 -3.74 0)
			(layer "F.Fab")
			(uuid "ee1f4b65-d853-4f7d-9d03-0616dd11d371")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "0587bd41-6601-4b5f-9f6a-8ac52ea59d5d")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "81ff98c9-3cf9-429d-a6d3-acd3a357cc8e")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054a5837a")
		(attr through_hole exclude_from_pos_files exclude_from_bom)
		(fp_circle
			(center 0 0)
			(end 2.8 0)
			(stroke
				(width 0.15)
				(type solid)
			)
			(fill no)
			(layer "Cmts.User")
			(uuid "6c7692e6-3cbd-45b8-9490-24b36682fcfe")
		)
		(fp_circle
			(center 0 0)
			(end 3.05 0)
			(stroke
				(width 0.05)
				(type solid)
			)
			(fill no)
			(layer "F.CrtYd")
			(uuid "d00b958e-cc1b-4f11-b0cc-afce424a3c7b")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "102b8a12-1baf-4c4b-a04f-bb9f8a7480f4")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0)
			(size 5.6 5.6)
			(drill 3.2)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 12 "unconnected-(P7-Pad1)")
			(pinfunction "1")
			(pintype "passive+no_connect")
			(uuid "f3d63e94-5de4-4728-9147-78ee5f0e5eb7")
		)
		(embedded_fonts no)
	)
	(footprint "Footprints:MountingHole_3.2mm_M3_DIN965_Pad"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a583de")
		(at 123.19 129.54)
		(descr "Mounting Hole 3.2mm, M3, DIN965")
		(tags "mounting hole 3.2mm m3 din965")
		(property "Reference" "P8"
			(at 0 -3.8 0)
			(layer "F.SilkS")
			(uuid "a2973ef5-760d-44f9-86f0-6b8d783cb7e1")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "CONN_1"
			(at 4.81 -3.34 0)
			(layer "F.Fab")
			(uuid "7782bf3b-40e4-4e53-9110-a4b962031571")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "5b4f28f9-0fb6-40f8-a020-8a33d92fae4a")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "6cd8ee5f-fc34-434e-aaa0-d22277103f7c")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054a58391")
		(attr through_hole exclude_from_pos_files exclude_from_bom)
		(fp_circle
			(center 0 0)
			(end 2.8 0)
			(stroke
				(width 0.15)
				(type solid)
			)
			(fill no)
			(layer "Cmts.User")
			(uuid "98eb28a9-1e8e-4094-9517-f4ab56720883")
		)
		(fp_circle
			(center 0 0)
			(end 3.05 0)
			(stroke
				(width 0.05)
				(type solid)
			)
			(fill no)
			(layer "F.CrtYd")
			(uuid "acf836f6-1982-4daf-92cd-45af195f65d0")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "ff0bfdf8-4509-4032-9fd3-598db966dd35")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0)
			(size 5.6 5.6)
			(drill 3.2)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "unconnected-(P8-Pad1)")
			(pinfunction "1")
			(pintype "passive+no_connect")
			(uuid "d1bbbdbc-d2bc-4da0-a727-438ad509bb8a")
		)
		(embedded_fonts no)
	)
	(footprint "Footprints:Valve_ECC-83-2"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a5a6b2")
		(at 149.28 109.23)
		(descr "Valve ECC-83-2 flat pins")
		(tags "Valve ECC-83-2 flat pins")
		(property "Reference" "U1"
			(at 1.596 -11.694 0)
			(layer "F.SilkS")
			(uuid "294b776c-6a71-46e4-a3cb-b2887456a440")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "ECC83"
			(at -3.45 6.68 0)
			(layer "F.Fab")
			(uuid "e562a9f3-387c-4800-ba56-0bd302cbd4a8")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "a57e3e02-6b51-446a-9736-7e29bded3445")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "d5584514-4a28-4e12-b310-ab939a18b52f")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000048b4f266")
		(sheetname "Racine")
		(sheetfile "ecc83-pp_v2.kicad_sch")
		(attr through_hole)
		(fp_circle
			(center 0 0)
			(end 10.16 1.27)
			(stroke
				(width 0.12)
				(type solid)
			)
			(fill no)
			(layer "F.SilkS")
			(uuid "e1264354-eb6d-41a6-a6b1-056ea2cdc006")
		)
		(fp_circle
			(center 0 0)
			(end 0 -10.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(fill no)
			(layer "F.CrtYd")
			(uuid "c9c49181-6916-472c-9718-cd6075a7db5d")
		)
		(fp_circle
			(center 0 0)
			(end 0 -10.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(fill no)
			(layer "F.Fab")
			(uuid "79e6b785-0d91-4f8a-add3-e3c57a06002b")
		)
		(fp_text user "${REFERENCE}"
			(at 0 -8.5 0)
			(layer "F.Fab")
			(uuid "1e54329f-a0cd-410c-b8f6-689feaac2618")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "" thru_hole circle
			(at 0 0)
			(size 4.5 4.5)
			(drill 3.1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(uuid "9990deae-ccc1-4abb-b6a6-872a0217973e")
		)
		(pad "1" thru_hole oval
			(at 3.45 4.75 306)
			(size 2.03 3.05)
			(drill oval 1.02 2.03)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 8 "Net-(U1A-G)")
			(pinfunction "A")
			(pintype "passive")
			(uuid "ae34eac2-2e21-4f35-9b13-c657e05f38de")
		)
		(pad "2" thru_hole oval
			(at 5.6 1.82 342)
			(size 2.03 3.05)
			(drill oval 1.02 2.03)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 5 "Net-(P1-PM)")
			(pinfunction "G")
			(pintype "input")
			(uuid "42dad25e-f342-4842-85bc-8ca00b095151")
		)
		(pad "3" thru_hole oval
			(at 5.6 -1.83 18)
			(size 2.03 3.05)
			(drill oval 1.02 2.03)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 9 "Net-(U1B-K)")
			(pinfunction "K")
			(pintype "passive")
			(uuid "fb3124ce-5bed-45a7-8cff-83d7e1040899")
		)
		(pad "4" thru_hole oval
			(at 3.45 -4.76 54)
			(size 2.03 3.05)
			(drill oval 1.02 2.03)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 6 "Net-(P4-P1)")
			(pinfunction "F1")
			(pintype "input")
			(uuid "af72b75e-738e-4a16-ba78-e6a02f62fa82")
		)
		(pad "5" thru_hole oval
			(at 0 -5.9 90)
			(size 2.03 3.05)
			(drill oval 1.02 2.03)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 6 "Net-(P4-P1)")
			(pinfunction "F1")
			(pintype "input")
			(uuid "e5f1c76c-6212-45bd-847c-c12a1e88be74")
		)
		(pad "6" thru_hole oval
			(at -3.46 -4.76 306)
			(size 2.03 3.05)
			(drill oval 1.02 2.03)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 2 "Net-(P3-P1)")
			(pinfunction "A")
			(pintype "passive")
			(uuid "79f8aa3e-bb98-4bd3-a876-4ebb07375a30")
		)
		(pad "7" thru_hole oval
			(at -5.61 -1.83 342)
			(size 2.03 3.05)
			(drill oval 1.02 2.03)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 8 "Net-(U1A-G)")
			(pinfunction "G")
			(pintype "input")
			(uuid "f06cd7b6-50cd-4020-8276-073d44e18879")
		)
		(pad "8" thru_hole oval
			(at -5.61 1.78 18)
			(size 2.03 3.05)
			(drill oval 1.02 2.03)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 4 "Net-(U1A-K)")
			(pinfunction "K")
			(pintype "passive")
			(uuid "2f9754f2-150a-443f-ae97-6b8c4c9157c7")
		)
		(pad "9" thru_hole oval
			(at -3.46 4.75 54)
			(size 2.03 3.05)
			(drill oval 1.02 2.03)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 7 "Net-(P4-PM)")
			(pinfunction "F2")
			(pintype "input")
			(uuid "afcb7de4-7365-449b-84c5-dcf5986c8c5b")
		)
		(embedded_fonts no)
		(model "${KIPRJMOD}/3d_shapes/ecc83.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:C_Axial_L12.0mm_D6.5mm_P20.00mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a34ccf9")
		(at 131.445 108.585 -90)
		(descr "C, Axial series, Axial, Horizontal, pin pitch=20mm, , length*diameter=12*6.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B300/STYROFLEX.pdf")
		(tags "C Axial series Axial Horizontal pin pitch 20mm  length 12mm diameter 6.5mm")
		(property "Reference" "C2"
			(at 10 -4.37 270)
			(layer "F.SilkS")
			(uuid "63cb4e90-c433-4b8c-8eb6-c019cfea1101")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "680nF"
			(at 10 4.37 270)
			(layer "F.Fab")
			(uuid "1581ec72-89c4-4212-9a8b-d6d6df9097c3")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "8cdd467a-82f0-4b2f-9037-a810126327b8")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "1eb4946d-08f7-4766-9d6c-6d169cbf83e5")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "C? C_????_* C_???? SMD*_c Capacitor*")
		(path "/00000000-0000-0000-0000-00004549f3be")
		(sheetname "Racine")
		(sheetfile "ecc83-pp_v2.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 3.88 3.37)
			(end 16.12 3.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "02b20db7-66f2-4fa4-908d-8f56ec0e66e7")
		)
		(fp_line
			(start 16.12 3.37)
			(end 16.12 -3.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "720aa702-2e00-4101-b4c4-ea93c3167d08")
		)
		(fp_line
			(start 1.04 0)
			(end 3.88 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "dd0a0ea1-70fd-4f16-8024-e9f6e526a985")
		)
		(fp_line
			(start 18.96 0)
			(end 16.12 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "87b12c2a-1b79-4256-ac58-a23f27a3ba85")
		)
		(fp_line
			(start 3.88 -3.37)
			(end 3.88 3.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b1736daa-91cf-4b43-b7f2-ac1dfa1d995c")
		)
		(fp_line
			(start 16.12 -3.37)
			(end 3.88 -3.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f5710f80-4074-452c-bb5d-50dd7cbbccdf")
		)
		(fp_line
			(start -1.05 3.5)
			(end 21.05 3.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3e1e59a9-77a8-437b-86e3-1df5e5bc3110")
		)
		(fp_line
			(start 21.05 3.5)
			(end 21.05 -3.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "6cc42d41-2eab-4966-ac32-3becb24e853c")
		)
		(fp_line
			(start -1.05 -3.5)
			(end -1.05 3.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "edd45708-17e5-4bde-937c-986ac1474275")
		)
		(fp_line
			(start 21.05 -3.5)
			(end -1.05 -3.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "14e8b2a2-58b1-453d-a2d7-8743dba32788")
		)
		(fp_line
			(start 4 3.25)
			(end 16 3.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "01d94359-92ab-4bd5-a8ed-ee1cce7bc492")
		)
		(fp_line
			(start 16 3.25)
			(end 16 -3.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5ae51722-b451-4305-bbea-0e50457b68e3")
		)
		(fp_line
			(start 0 0)
			(end 4 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "871405a2-e98b-4ca3-979e-ab58565b9675")
		)
		(fp_line
			(start 20 0)
			(end 16 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "230ce8d5-42ad-4ad4-9b50-87bb9890dec3")
		)
		(fp_line
			(start 4 -3.25)
			(end 4 3.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2836a378-a025-4969-9391-6e5b2913744c")
		)
		(fp_line
			(start 16 -3.25)
			(end 4 -3.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "e2ea2f37-5175-4fdd-ac55-98e664a11aca")
		)
		(fp_text user "${REFERENCE}"
			(at 10 0 270)
			(layer "F.Fab")
			(uuid "3cc5453c-ae39-4989-898b-02a860b87a0b")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 3 "Net-(P2-P1)")
			(pintype "passive")
			(uuid "6cca0032-93d9-4472-89e3-018667c5033a")
		)
		(pad "2" thru_hole oval
			(at 20 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 4 "Net-(U1A-K)")
			(pintype "passive")
			(uuid "bf9750af-18a3-4f2b-939f-dd3064519e33")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Capacitor_THT.3dshapes/C_Axial_L12.0mm_D6.5mm_P20.00mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:PinHeader_1x02_P2.54mm_Vertical"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a3805d9")
		(at 123.19 111.125 180)
		(descr "Through hole straight pin header, 1x02, 2.54mm pitch, single row")
		(tags "Through hole pin header THT 1x02 2.54mm single row")
		(property "Reference" "P2"
			(at 0 -2.33 180)
			(layer "F.SilkS")
			(uuid "632049e1-edc3-470a-93e8-426d69ebeb65")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "OUT"
			(at 0 4.87 180)
			(layer "F.Fab")
			(uuid "be206817-964e-4f3d-a5a9-4b8df382004b")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "46d52be9-3cca-40ce-92b1-68364df304ec")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "b6184194-f7f8-46ac-9261-b81837e4247a")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-00004549f46c")
		(sheetname "Racine")
		(sheetfile "ecc83-pp_v2.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 1.33 1.27)
			(end 1.33 3.87)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "93a99a67-3230-4b36-b95b-15d6fc848331")
		)
		(fp_line
			(start -1.33 3.87)
			(end 1.33 3.87)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "de240efd-0ade-4352-8a7a-fbf0e84c1bbf")
		)
		(fp_line
			(start -1.33 1.27)
			(end 1.33 1.27)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e51d1c89-fb46-43b4-85f4-0e123140d6fe")
		)
		(fp_line
			(start -1.33 1.27)
			(end -1.33 3.87)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ca785ed2-2350-4005-8b9f-96cb286c0de3")
		)
		(fp_line
			(start -1.33 0)
			(end -1.33 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cacc2bd3-4be0-49a2-9d4d-64cebcd21425")
		)
		(fp_line
			(start -1.33 -1.33)
			(end 0 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3be27f18-c7ff-471f-be52-9f62a376810c")
		)
		(fp_line
			(start 1.8 4.35)
			(end 1.8 -1.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "e7a1446b-be0d-4a84-a9be-d20bba16b2c0")
		)
		(fp_line
			(start 1.8 -1.8)
			(end -1.8 -1.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "a7bf042d-ab67-4f64-ace9-b83f631ed09c")
		)
		(fp_line
			(start -1.8 4.35)
			(end 1.8 4.35)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "e4911879-d606-486e-ab7c-6d19003d3730")
		)
		(fp_line
			(start -1.8 -1.8)
			(end -1.8 4.35)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "71e2b4de-532d-4ff3-b447-f598d66b942b")
		)
		(fp_line
			(start 1.27 3.81)
			(end -1.27 3.81)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "65366511-d0bb-41d2-9791-1f9467d48776")
		)
		(fp_line
			(start 1.27 -1.27)
			(end 1.27 3.81)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b4cd2fc5-4d6f-463a-9ee1-b16bec676672")
		)
		(fp_line
			(start -0.635 -1.27)
			(end 1.27 -1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a176cb4e-0000-41d9-bd57-523795ce4a53")
		)
		(fp_line
			(start -1.27 3.81)
			(end -1.27 -0.635)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "40cd2915-78f2-47ba-85c9-e612e261759d")
		)
		(fp_line
			(start -1.27 -0.635)
			(end -0.635 -1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ca9bd0ed-ccad-4593-9819-70d448bedd81")
		)
		(fp_text user "${REFERENCE}"
			(at 0 1.27 270)
			(layer "F.Fab")
			(uuid "9dd150a9-47c2-42f6-afe2-907d43b30908")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0 180)
			(size 1.7 1.7)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 3 "Net-(P2-P1)")
			(pinfunction "P1")
			(pintype "passive")
			(uuid "e683ae31-6af3-424c-a55a-a5a6b9f7c9af")
		)
		(pad "2" thru_hole oval
			(at 0 2.54 180)
			(size 1.7 1.7)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "GND")
			(pinfunction "PM")
			(pintype "passive")
			(uuid "9b073810-2889-46ca-b4cb-a3f9829bfcba")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x02_P2.54mm_Vertical.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:PinHeader_1x02_P2.54mm_Vertical"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a3805ee")
		(at 123.19 102.87 180)
		(descr "Through hole straight pin header, 1x02, 2.54mm pitch, single row")
		(tags "Through hole pin header THT 1x02 2.54mm single row")
		(property "Reference" "P3"
			(at 0 -2.33 180)
			(layer "F.SilkS")
			(uuid "dd244a6f-0d5f-450d-b80e-6a95b222cbfa")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "POWER"
			(at 0 4.87 180)
			(layer "F.Fab")
			(uuid "71670ce8-89f3-482d-bc30-ce14bdd3443b")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "b39880bf-d5c3-4d45-8b96-590af409ea15")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "0afbfd1a-9621-4fbe-a24f-3cf94e2a9827")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-00004549f4a5")
		(sheetname "Racine")
		(sheetfile "ecc83-pp_v2.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 1.33 1.27)
			(end 1.33 3.87)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0e0d25c3-1424-42d9-a3b8-bcd4e51866be")
		)
		(fp_line
			(start -1.33 3.87)
			(end 1.33 3.87)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "33830b78-8002-41cb-b50a-51d5cad97e4b")
		)
		(fp_line
			(start -1.33 1.27)
			(end 1.33 1.27)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f062d429-c838-49ee-b6ee-0b5291e01d8d")
		)
		(fp_line
			(start -1.33 1.27)
			(end -1.33 3.87)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ba78f032-529f-4fde-a38e-0522b06481be")
		)
		(fp_line
			(start -1.33 0)
			(end -1.33 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3dc3ddc5-3637-4c3c-8a17-de1502f6c20e")
		)
		(fp_line
			(start -1.33 -1.33)
			(end 0 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "30e248b2-b9d1-43a9-9e2d-79b87d426a1b")
		)
		(fp_line
			(start 1.8 4.35)
			(end 1.8 -1.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "1f600baf-580d-4747-8107-a218eaf41d2b")
		)
		(fp_line
			(start 1.8 -1.8)
			(end -1.8 -1.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "2de04b80-812a-4e2c-a6c9-716601ee6546")
		)
		(fp_line
			(start -1.8 4.35)
			(end 1.8 4.35)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "59dd32d2-309d-42f7-b41f-4006ce1d0f1a")
		)
		(fp_line
			(start -1.8 -1.8)
			(end -1.8 4.35)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "c60d8fa0-5dcd-4335-9d05-0c937f369d3b")
		)
		(fp_line
			(start 1.27 3.81)
			(end -1.27 3.81)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "08b8dd9e-7111-440b-bbc8-eb60804dbe08")
		)
		(fp_line
			(start 1.27 -1.27)
			(end 1.27 3.81)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0ccfa9fa-babd-41d0-89c4-0b577dd3f467")
		)
		(fp_line
			(start -0.635 -1.27)
			(end 1.27 -1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5783b2ae-ad8f-4310-90af-54355def2d0f")
		)
		(fp_line
			(start -1.27 3.81)
			(end -1.27 -0.635)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "aededc3a-6d02-4ff0-871f-73f6bc157828")
		)
		(fp_line
			(start -1.27 -0.635)
			(end -0.635 -1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "708702a9-563d-4a35-b58c-2aa8e6fdc058")
		)
		(fp_text user "${REFERENCE}"
			(at 0 1.27 270)
			(layer "F.Fab")
			(uuid "88dfc8e8-5f1a-4e8d-b0b9-ec79b5a9fb2a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0 180)
			(size 1.7 1.7)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 2 "Net-(P3-P1)")
			(pinfunction "P1")
			(pintype "passive")
			(uuid "ac77d90f-cff2-459a-bb97-8f5e1cced5b9")
		)
		(pad "2" thru_hole oval
			(at 0 2.54 180)
			(size 1.7 1.7)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "GND")
			(pinfunction "PM")
			(pintype "passive")
			(uuid "459dddf8-2cc2-44c0-af8a-ea6b905c3dd1")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x02_P2.54mm_Vertical.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:PinHeader_1x02_P2.54mm_Vertical"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a38c77f")
		(at 165.1 110.49)
		(descr "Through hole straight pin header, 1x02, 2.54mm pitch, single row")
		(tags "Through hole pin header THT 1x02 2.54mm single row")
		(property "Reference" "P1"
			(at -2.794 -0.254 0)
			(layer "F.SilkS")
			(uuid "677bdd8e-ea64-4949-9147-ef8ec9c59ddf")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "IN"
			(at 0 4.87 0)
			(layer "F.Fab")
			(uuid "801f8613-4e33-416d-9e88-af68d696eb3f")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "1fe6eb66-197b-4c84-b0f6-b96cd3e63cb3")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "68b2a21a-a1e5-47fd-adcb-48b1334c155c")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-00004549f464")
		(sheetname "Racine")
		(sheetfile "ecc83-pp_v2.kicad_sch")
		(attr through_hole)
		(fp_line
			(start -1.33 -1.33)
			(end 0 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3f5dd17b-66bc-4f5d-ba56-2fbe816f2da4")
		)
		(fp_line
			(start -1.33 0)
			(end -1.33 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "26f9b90f-9680-4c7d-aa38-ba61811226b8")
		)
		(fp_line
			(start -1.33 1.27)
			(end -1.33 3.87)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3eeed008-c04d-43c7-bc27-1decf578037d")
		)
		(fp_line
			(start -1.33 1.27)
			(end 1.33 1.27)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "56f97847-8392-415b-9b55-79d1e90eba8f")
		)
		(fp_line
			(start -1.33 3.87)
			(end 1.33 3.87)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "24deb571-48a1-48ed-bcf3-c806e44ac1b3")
		)
		(fp_line
			(start 1.33 1.27)
			(end 1.33 3.87)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f5e1191a-3e8d-495b-9a36-03677acf88a3")
		)
		(fp_line
			(start -1.8 -1.8)
			(end -1.8 4.35)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "18ec1a22-e727-4b40-bf96-d80008069bf1")
		)
		(fp_line
			(start -1.8 4.35)
			(end 1.8 4.35)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "5415e725-eade-46eb-9e29-d9364e29047b")
		)
		(fp_line
			(start 1.8 -1.8)
			(end -1.8 -1.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "64e1b52f-325c-4299-9137-e96819b05e1a")
		)
		(fp_line
			(start 1.8 4.35)
			(end 1.8 -1.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "df073cca-53f0-41af-97ae-14a0f09cbc6c")
		)
		(fp_line
			(start -1.27 -0.635)
			(end -0.635 -1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "bd2b7bcb-51b4-455a-abec-ab2ceda6af94")
		)
		(fp_line
			(start -1.27 3.81)
			(end -1.27 -0.635)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "28b3aa0e-f7f0-40e4-b49e-1b494b33feb8")
		)
		(fp_line
			(start -0.635 -1.27)
			(end 1.27 -1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "1bed8b06-eae6-4bc4-8c00-bf1528806f5e")
		)
		(fp_line
			(start 1.27 -1.27)
			(end 1.27 3.81)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "cca83b7b-71ff-4ed0-b4d3-14e172e23a8f")
		)
		(fp_line
			(start 1.27 3.81)
			(end -1.27 3.81)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "3d9d8d78-92a5-43a6-887c-f628ad680943")
		)
		(fp_text user "${REFERENCE}"
			(at 0 1.27 90)
			(layer "F.Fab")
			(uuid "cbfc14dd-a7e6-4170-b258-71c01d72f890")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0)
			(size 1.7 1.7)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "GND")
			(pinfunction "P1")
			(pintype "passive")
			(uuid "d79aa92f-b959-43f9-9f3b-4e7a77b4ad37")
		)
		(pad "2" thru_hole oval
			(at 0 2.54)
			(size 1.7 1.7)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 5 "Net-(P1-PM)")
			(pinfunction "PM")
			(pintype "passive")
			(uuid "aeb94703-7bc4-4268-a31e-4cc35fc4c0e9")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x02_P2.54mm_Vertical.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:PinHeader_1x02_P2.54mm_Vertical"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a38c89b")
		(at 150.495 128.905 -90)
		(descr "Through hole straight pin header, 1x02, 2.54mm pitch, single row")
		(tags "Through hole pin header THT 1x02 2.54mm single row")
		(property "Reference" "P4"
			(at 0 -2.33 270)
			(layer "F.SilkS")
			(uuid "42af4b62-e5b1-42ea-9ee5-5be34fddb52d")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "CONN_2"
			(at 0 4.87 270)
			(layer "F.Fab")
			(uuid "c3c867f4-d1ce-4fe4-8711-b453d35b6a09")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "5952cfef-82f0-45b0-923b-c2ef886f69a7")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "9d902652-9ae0-43df-8e4c-1f15451ef3ee")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-0000456a8acc")
		(sheetname "Racine")
		(sheetfile "ecc83-pp_v2.kicad_sch")
		(attr through_hole)
		(fp_line
			(start -1.33 3.87)
			(end 1.33 3.87)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ee99cf37-f417-488d-9ac0-e67b817a0d40")
		)
		(fp_line
			(start -1.33 1.27)
			(end -1.33 3.87)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5b8b3deb-10f5-49e6-832a-9d131888155f")
		)
		(fp_line
			(start -1.33 1.27)
			(end 1.33 1.27)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a64240cf-7f78-4cc9-aa3a-6263133bd98a")
		)
		(fp_line
			(start 1.33 1.27)
			(end 1.33 3.87)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5edf101f-ae1b-438a-bcc7-1e2ab1c1721c")
		)
		(fp_line
			(start -1.33 0)
			(end -1.33 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "56480a24-cd88-439e-b86c-f23f4b12168d")
		)
		(fp_line
			(start -1.33 -1.33)
			(end 0 -1.33)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "72e2d872-cca0-401b-8889-a42697c4d872")
		)
		(fp_line
			(start -1.8 4.35)
			(end 1.8 4.35)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "67180090-2f20-4e7d-8d06-add14c8a748a")
		)
		(fp_line
			(start 1.8 4.35)
			(end 1.8 -1.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "c82b2981-1a35-4321-8f62-aa4a603e869d")
		)
		(fp_line
			(start -1.8 -1.8)
			(end -1.8 4.35)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3f187549-74dd-431e-849b-3e0adb8de074")
		)
		(fp_line
			(start 1.8 -1.8)
			(end -1.8 -1.8)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "54ffc580-7596-4307-bbb2-96e645315673")
		)
		(fp_line
			(start -1.27 3.81)
			(end -1.27 -0.635)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2a8ccdc4-f7f8-437f-bb51-60378897e3c1")
		)
		(fp_line
			(start 1.27 3.81)
			(end -1.27 3.81)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "648c48d7-e2ab-46f9-90c7-64164e6b2219")
		)
		(fp_line
			(start -1.27 -0.635)
			(end -0.635 -1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b694854e-7646-4c43-8be9-4f4227c24ea7")
		)
		(fp_line
			(start -0.635 -1.27)
			(end 1.27 -1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "81c8cd40-33e5-49a2-837a-4d63f551c852")
		)
		(fp_line
			(start 1.27 -1.27)
			(end 1.27 3.81)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "200f402b-14f3-42ad-a65f-af330b26f73f")
		)
		(fp_text user "${REFERENCE}"
			(at 0 1.27 0)
			(layer "F.Fab")
			(uuid "df94a4b3-79f6-4123-bd6a-154df7c8ef11")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0 270)
			(size 1.7 1.7)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 6 "Net-(P4-P1)")
			(pinfunction "P1")
			(pintype "passive")
			(uuid "dc6778f5-9e75-4962-b82f-05bc7f8fd796")
		)
		(pad "2" thru_hole oval
			(at 0 2.54 270)
			(size 1.7 1.7)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 7 "Net-(P4-PM)")
			(pinfunction "PM")
			(pintype "passive")
			(uuid "8e05bb2b-7b13-43a2-a0db-8f4efa2b4a5d")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x02_P2.54mm_Vertical.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(gr_line
		(start 168.275 90.805)
		(end 168.275 132.715)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "10427b83-629f-4b51-9d69-00c8aed1c764")
	)
	(gr_line
		(start 120.015 90.805)
		(end 120.015 132.715)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "34316d74-64d8-45b4-8cb7-31cf6a4e8427")
	)
	(gr_line
		(start 168.275 90.805)
		(end 120.015 90.805)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "9d903977-89b9-400d-a957-c285b8f528cd")
	)
	(gr_line
		(start 168.275 132.715)
		(end 120.015 132.715)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "e346801c-20fd-4959-91c0-3d3bf6dbcd08")
	)
	(segment
		(start 165.1 123.825)
		(end 165.354 123.825)
		(width 0.8636)
		(layer "B.Cu")
		(net 1)
		(uuid "ca2dfff9-02dc-4d1f-8653-c83bf83998d0")
	)
	(segment
		(start 142.7 101.35)
		(end 137.6 101.35)
		(width 0.8636)
		(layer "B.Cu")
		(net 2)
		(uuid "3b469e6c-e6df-48c1-9c51-a3141276528b")
	)
	(segment
		(start 123.19 102.87)
		(end 124.46 102.87)
		(width 0.8636)
		(layer "B.Cu")
		(net 2)
		(uuid "48a33d03-c05b-46de-a2e5-b5837057841b")
	)
	(segment
		(start 129.9 102.9)
		(end 133.1 102.9)
		(width 0.8636)
		(layer "B.Cu")
		(net 2)
		(uuid "562b8c98-3a67-4916-ad20-797c642e4cc9")
	)
	(segment
		(start 133.1 102.9)
		(end 136.05 102.9)
		(width 0.8636)
		(layer "B.Cu")
		(net 2)
		(uuid "5f54253d-edd9-4431-b60c-474db336e0f3")
	)
	(segment
		(start 124.46 102.87)
		(end 125.73 101.6)
		(width 0.8636)
		(layer "B.Cu")
		(net 2)
		(uuid "8478986a-447b-4150-bf9f-c8fc4c4aa880")
	)
	(segment
		(start 125.73 101.6)
		(end 128.6 101.6)
		(width 0.8636)
		(layer "B.Cu")
		(net 2)
		(uuid "a66cce80-77ce-424f-964a-09f3ffa97285")
	)
	(segment
		(start 145.82 104.47)
		(end 142.7 101.35)
		(width 0.8636)
		(layer "B.Cu")
		(net 2)
		(uuid "d74252fb-e2a8-4cb5-b4ea-adabb9d6dd14")
	)
	(segment
		(start 128.6 101.6)
		(end 129.9 102.9)
		(width 0.8636)
		(layer "B.Cu")
		(net 2)
		(uuid "d92e2007-6121-4920-ae81-f5c4971cd7d4")
	)
	(segment
		(start 136.05 102.9)
		(end 137.6 101.35)
		(width 0.8636)
		(layer "B.Cu")
		(net 2)
		(uuid "fffbed7d-2a39-4e7b-a2bd-9249ac91f4af")
	)
	(segment
		(start 128.905 111.125)
		(end 123.19 111.125)
		(width 0.8636)
		(layer "B.Cu")
		(net 3)
		(uuid "00000000-0000-0000-0000-00005a380672")
	)
	(segment
		(start 131.445 108.585)
		(end 128.905 111.125)
		(width 0.8636)
		(layer "B.Cu")
		(net 3)
		(uuid "0e488523-9c76-4849-8e3b-ad18a3c05242")
	)
	(segment
		(start 123.825 114.935)
		(end 123.19 114.3)
		(width 0.8636)
		(layer "B.Cu")
		(net 3)
		(uuid "99061c02-f381-4cc1-9360-52901267677f")
	)
	(segment
		(start 124.46 114.935)
		(end 123.825 114.935)
		(width 0.8636)
		(layer "B.Cu")
		(net 3)
		(uuid "a2024f9b-c68e-4579-a987-3844956c4d7f")
	)
	(segment
		(start 123.19 114.3)
		(end 123.19 111.125)
		(width 0.8636)
		(layer "B.Cu")
		(net 3)
		(uuid "ef39514b-5cf3-49d0-8567-759c338fec5b")
	)
	(segment
		(start 143.61668 111.65332)
		(end 142.875 112.395)
		(width 0.8636)
		(layer "B.Cu")
		(net 4)
		(uuid "0b9d1cf2-f76f-4dee-84d1-e9a29a970249")
	)
	(segment
		(start 140.97 128.905)
		(end 142.24 128.905)
		(width 0.8636)
		(layer "B.Cu")
		(net 4)
		(uuid "34845745-ccd7-48a1-8413-1577af10e5fe")
	)
	(segment
		(start 143.51 127.635)
		(end 143.51 116.84)
		(width 0.8636)
		(layer "B.Cu")
		(net 4)
		(uuid "463829d1-52a4-46f2-81fc-dbe49806f462")
	)
	(segment
		(start 142.24 128.905)
		(end 143.51 127.635)
		(width 0.8636)
		(layer "B.Cu")
		(net 4)
		(uuid "47ad2996-a667-487b-85de-fb3534ab1d26")
	)
	(segment
		(start 142.875 116.205)
		(end 143.51 116.84)
		(width 0.8636)
		(layer "B.Cu")
		(net 4)
		(uuid "6aac9bf3-7817-4613-a552-31af632c7436")
	)
	(segment
		(start 143.61668 111.00816)
		(end 143.61668 111.65332)
		(width 0.8636)
		(layer "B.Cu")
		(net 4)
		(uuid "8e2f7006-a4b1-4388-ac54-4b575936a9f0")
	)
	(segment
		(start 131.445 128.905)
		(end 140.97 128.905)
		(width 0.8636)
		(layer "B.Cu")
		(net 4)
		(uuid "9e3f32f6-f44d-4ff1-b66b-456122dbd23b")
	)
	(segment
		(start 142.875 112.395)
		(end 142.875 116.205)
		(width 0.8636)
		(layer "B.Cu")
		(net 4)
		(uuid "f31fee50-a2b3-479f-b13d-2438c21b47d1")
	)
	(segment
		(start 161.925 113.03)
		(end 165.1 113.03)
		(width 0.8636)
		(layer "B.Cu")
		(net 5)
		(uuid "0b4f50e3-7922-4158-9f55-4690ebf0ac05")
	)
	(segment
		(start 154.83332 111.0488)
		(end 159.9438 111.0488)
		(width 0.8636)
		(layer "B.Cu")
		(net 5)
		(uuid "621657c6-0b24-4203-9747-796b861ea117")
	)
	(segment
		(start 159.9438 111.0488)
		(end 161.925 113.03)
		(width 0.8636)
		(layer "B.Cu")
		(net 5)
		(uuid "a110024f-bd17-4f39-920c-027492aab67b")
	)
	(segment
		(start 165.1 113.03)
		(end 165.1 116.205)
		(width 0.8636)
		(layer "B.Cu")
		(net 5)
		(uuid "d9331417-1553-4654-bdb1-97534e310285")
	)
	(segment
		(start 152.7544 105.5456)
		(end 152.5 105.8)
		(width 0.8636)
		(layer "B.Cu")
		(net 6)
		(uuid "00000000-0000-0000-0000-000054a5a703")
	)
	(segment
		(start 152.5 105.8)
		(end 152.5 110.7)
		(width 0.8636)
		(layer "B.Cu")
		(net 6)
		(uuid "00000000-0000-0000-0000-000054a5a705")
	)
	(segment
		(start 152.5 110.7)
		(end 149.86 113.34)
		(width 0.8636)
		(layer "B.Cu")
		(net 6)
		(uuid "00000000-0000-0000-0000-000054a5a707")
	)
	(segment
		(start 149.86 113.34)
		(end 149.86 115.697)
		(width 0.8636)
		(layer "B.Cu")
		(net 6)
		(uuid "00000000-0000-0000-0000-000054a5a709")
	)
	(segment
		(start 151.54148 103.3272)
		(end 152.6794 104.46512)
		(width 0.8636)
		(layer "B.Cu")
		(net 6)
		(uuid "32dddb8d-2b18-4e42-a268-243a7068c0b3")
	)
	(segment
		(start 150.495 128.905)
		(end 150.495 116.332)
		(width 0.8636)
		(layer "B.Cu")
		(net 6)
		(uuid "43a0aacb-1be6-4bce-b033-0b5de39c8977")
	)
	(segment
		(start 152.7544 104.44512)
		(end 152.7544 105.5456)
		(width 0.8636)
		(layer "B.Cu")
		(net 6)
		(uuid "4e0de2c6-8cf4-4c7f-ab08-23afd1d09e4f")
	)
	(segment
		(start 149.225 103.3272)
		(end 151.54148 103.3272)
		(width 0.8636)
		(layer "B.Cu")
		(net 6)
		(uuid "954b8dfd-04f3-4af0-9f3b-5e39e6c61b70")
	)
	(segment
		(start 149.86 115.697)
		(end 150.495 116.332)
		(width 0.8636)
		(layer "B.Cu")
		(net 6)
		(uuid "d2d1228b-aa06-4264-9d00-1f725467781c")
	)
	(segment
		(start 147.955 116.15928)
		(end 145.7706 113.97488)
		(width 0.8636)
		(layer "B.Cu")
		(net 7)
		(uuid "75d83ffa-ca0d-43f8-b0ce-de2fc9158449")
	)
	(segment
		(start 147.955 128.905)
		(end 147.955 116.15928)
		(width 0.8636)
		(layer "B.Cu")
		(net 7)
		(uuid "f234c2ce-9b86-4ee2-a191-d9362243fb18")
	)
	(segment
		(start 143.61668 107.3912)
		(end 142.1638 107.3912)
		(width 0.8636)
		(layer "B.Cu")
		(net 8)
		(uuid "0dcb7e69-df3f-4797-b155-d283533adf24")
	)
	(segment
		(start 153.67 131.445)
		(end 154.94 130.175)
		(width 0.8636)
		(layer "B.Cu")
		(net 8)
		(uuid "22eaf701-e391-4f13-9207-b4beb5f12464")
	)
	(segment
		(start 139.065 125.73)
		(end 130.175 125.73)
		(width 0.8636)
		(layer "B.Cu")
		(net 8)
		(uuid "3480db20-0791-43b6-95fa-d8e8adf02de1")
	)
	(segment
		(start 154.94 130.175)
		(end 154.94 116.23548)
		(width 0.8636)
		(layer "B.Cu")
		(net 8)
		(uuid "3b4f6f5d-5050-43ea-b7aa-896d24fc56eb")
	)
	(segment
		(start 142.1638 107.3912)
		(end 140.97 108.585)
		(width 0.8636)
		(layer "B.Cu")
		(net 8)
		(uuid "5052372b-ada0-4137-b8a6-f6a2cbc2ac85")
	)
	(segment
		(start 140.97 108.585)
		(end 140.97 121.285)
		(width 0.8636)
		(layer "B.Cu")
		(net 8)
		(uuid "5156048f-c7c7-42db-86f3-8a61310f9386")
	)
	(segment
		(start 154.94 116.23548)
		(end 152.6794 113.97488)
		(width 0.8636)
		(layer "B.Cu")
		(net 8)
		(uuid "5addcb19-8df1-4300-9ede-e84b56d0d3a5")
	)
	(segment
		(start 130.175 131.445)
		(end 153.67 131.445)
		(width 0.8636)
		(layer "B.Cu")
		(net 8)
		(uuid "6edc481b-3da8-4a49-a84b-680ccf23a8aa")
	)
	(segment
		(start 130.175 125.73)
		(end 128.905 127)
		(width 0.8636)
		(layer "B.Cu")
		(net 8)
		(uuid "7e68272c-7ac3-450f-8310-31cd66fe23fb")
	)
	(segment
		(start 128.905 130.175)
		(end 130.175 131.445)
		(width 0.8636)
		(layer "B.Cu")
		(net 8)
		(uuid "91f0fc64-b271-463c-9db5-bfb15e87204e")
	)
	(segment
		(start 128.905 127)
		(end 128.905 130.175)
		(width 0.8636)
		(layer "B.Cu")
		(net 8)
		(uuid "980b1101-9545-4fe4-8c0b-2ceb346ae3f6")
	)
	(segment
		(start 140.97 123.825)
		(end 139.065 125.73)
		(width 0.8636)
		(layer "B.Cu")
		(net 8)
		(uuid "aeaabc1d-5637-48a6-b956-225d988dffc2")
	)
	(segment
		(start 140.97 121.285)
		(end 140.97 123.825)
		(width 0.8636)
		(layer "B.Cu")
		(net 8)
		(uuid "f7990ab2-14ff-42df-9b20-69d6b83f8bab")
	)
	(segment
		(start 154.83332 107.3912)
		(end 165.0238 107.3912)
		(width 0.8636)
		(layer "B.Cu")
		(net 9)
		(uuid "8af3f7c8-a645-42fb-9056-2c10c84d9999")
	)
	(segment
		(start 165.0238 107.3912)
		(end 165.1 107.315)
		(width 0.8636)
		(layer "B.Cu")
		(net 9)
		(uuid "db0366f3-a0ba-4c55-9ab4-43542e0ac49a")
	)
	(zone
		(net 1)
		(net_name "GND")
		(layer "B.Cu")
		(uuid "00000000-0000-0000-0000-00004eed96a1")
		(hatch edge 0.508)
		(connect_pads
			(clearance 0.635)
		)
		(min_thickness 0.254)
		(filled_areas_thickness no)
		(fill yes
			(thermal_gap 0.254)
			(thermal_bridge_width 0.50038)
		)
		(polygon
			(pts
				(xy 167.64 132.08) (xy 167.64 91.44) (xy 120.65 91.44) (xy 120.65 132.08)
			)
		)
		(filled_polygon
			(layer "B.Cu")
			(pts
				(xy 162.5457 91.460002) (xy 162.592193 91.513658) (xy 162.602297 91.583932) (xy 162.573611 91.647571)
				(xy 162.361005 91.89787) (xy 162.359095 91.900688) (xy 162.359089 91.900695) (xy 162.153854 92.203394)
				(xy 162.151943 92.206213) (xy 161.977445 92.535352) (xy 161.839556 92.881428) (xy 161.739892 93.240384)
				(xy 161.679623 93.608012) (xy 161.659454 93.98) (xy 161.659639 93.983412) (xy 161.671878 94.209136)
				(xy 161.679623 94.351988) (xy 161.739892 94.719616) (xy 161.740805 94.722904) (xy 161.829119 95.04098)
				(xy 161.839556 95.078572) (xy 161.840819 95.081741) (xy 161.966443 95.397034) (xy 161.977445 95.424648)
				(xy 162.151943 95.753787) (xy 162.153851 95.756602) (xy 162.153854 95.756606) (xy 162.359089 96.059305)
				(xy 162.359095 96.059312) (xy 162.361005 96.06213) (xy 162.602179 96.346062) (xy 162.872638 96.602254)
				(xy 163.16921 96.827702) (xy 163.172139 96.829465) (xy 163.172143 96.829467) (xy 163.259885 96.88226)
				(xy 163.488419 97.019764) (xy 163.826523 97.176187) (xy 163.829755 97.177276) (xy 163.829765 97.17728)
				(xy 164.17632 97.294048) (xy 164.176326 97.29405) (xy 164.179556 97.295138) (xy 164.182893 97.295872)
				(xy 164.182891 97.295872) (xy 164.540051 97.374489) (xy 164.540055 97.37449) (xy 164.543382 97.375222)
				(xy 164.546772 97.375591) (xy 164.546774 97.375591) (xy 164.61262 97.382752) (xy 164.913733 97.4155)
				(xy 165.286267 97.4155) (xy 165.58738 97.382752) (xy 165.653226 97.375591) (xy 165.653228 97.375591)
				(xy 165.656618 97.375222) (xy 165.659945 97.37449) (xy 165.659949 97.374489) (xy 166.017109 97.295872)
				(xy 166.017107 97.295872) (xy 166.020444 97.295138) (xy 166.023674 97.29405) (xy 166.02368 97.294048)
				(xy 166.370235 97.17728) (xy 166.370245 97.177276) (xy 166.373477 97.176187) (xy 166.711581 97.019764)
				(xy 166.940115 96.88226) (xy 167.027857 96.829467) (xy 167.027861 96.829465) (xy 167.03079 96.827702)
				(xy 167.327362 96.602254) (xy 167.427351 96.507539) (xy 167.49056 96.475214) (xy 167.561213 96.482195)
				(xy 167.616876 96.526265) (xy 167.64 96.599015) (xy 167.64 126.920985) (xy 167.619998 126.989106)
				(xy 167.566342 127.035599) (xy 167.496068 127.045703) (xy 167.42735 127.01246) (xy 167.420703 127.006163)
				(xy 167.327362 126.917746) (xy 167.03079 126.692298) (xy 166.711581 126.500236) (xy 166.373477 126.343813)
				(xy 166.370245 126.342724) (xy 166.370235 126.34272) (xy 166.02368 126.225952) (xy 166.023674 126.22595)
				(xy 166.020444 126.224862) (xy 165.910976 126.200766) (xy 165.659949 126.145511) (xy 165.659945 126.14551)
				(xy 165.656618 126.144778) (xy 165.653228 126.144409) (xy 165.653226 126.144409) (xy 165.58738 126.137248)
				(xy 165.286267 126.1045) (xy 164.913733 126.1045) (xy 164.61262 126.137248) (xy 164.546774 126.144409)
				(xy 164.546772 126.144409) (xy 164.543382 126.144778) (xy 164.540055 126.14551) (xy 164.540051 126.145511)
				(xy 164.289024 126.200766) (xy 164.179556 126.224862) (xy 164.176326 126.22595) (xy 164.17632 126.225952)
				(xy 163.829765 126.34272) (xy 163.829755 126.342724) (xy 163.826523 126.343813) (xy 163.488419 126.500236)
				(xy 163.16921 126.692298) (xy 162.872638 126.917746) (xy 162.602179 127.173938) (xy 162.361005 127.45787)
				(xy 162.359095 127.460688) (xy 162.359089 127.460695) (xy 162.185088 127.717328) (xy 162.151943 127.766213)
				(xy 161.977445 128.095352) (xy 161.839556 128.441428) (xy 161.739892 128.800384) (xy 161.679623 129.168012)
				(xy 161.679439 129.171408) (xy 161.679438 129.171416) (xy 161.671895 129.310535) (xy 161.659454 129.54)
				(xy 161.659639 129.543412) (xy 161.679061 129.901617) (xy 161.679623 129.911988) (xy 161.739892 130.279616)
				(xy 161.740805 130.282904) (xy 161.826312 130.59087) (xy 161.839556 130.638572) (xy 161.892252 130.770829)
				(xy 161.958568 130.937269) (xy 161.977445 130.984648) (xy 162.151943 131.313787) (xy 162.153851 131.316602)
				(xy 162.153854 131.316606) (xy 162.359089 131.619305) (xy 162.359095 131.619312) (xy 162.361005 131.62213)
				(xy 162.363218 131.624735) (xy 162.573611 131.872429) (xy 162.602467 131.937297) (xy 162.591672 132.007468)
				(xy 162.544653 132.060664) (xy 162.477579 132.08) (xy 154.84858 132.08) (xy 154.780459 132.059998)
				(xy 154.733966 132.006342) (xy 154.723862 131.936068) (xy 154.753356 131.871488) (xy 154.759485 131.864905)
				(xy 155.653234 130.971156) (xy 155.662396 130.962851) (xy 155.693568 130.937269) (xy 155.698347 130.933347)
				(xy 155.777596 130.836781) (xy 155.785896 130.826668) (xy 155.827798 130.775611) (xy 155.827801 130.775606)
				(xy 155.831721 130.77083) (xy 155.930828 130.585415) (xy 155.945948 130.535569) (xy 155.99006 130.390153)
				(xy 155.990062 130.390144) (xy 155.991857 130.384227) (xy 156.012464 130.175) (xy 156.007907 130.128732)
				(xy 156.0073 130.116382) (xy 156.0073 124.08172) (xy 164.077965 124.08172) (xy 164.119722 124.219377)
				(xy 164.124438 124.230761) (xy 164.216475 124.402948) (xy 164.223315 124.413185) (xy 164.347181 124.564118)
				(xy 164.355882 124.572819) (xy 164.506815 124.696685) (xy 164.517052 124.703525) (xy 164.689239 124.795562)
				(xy 164.700623 124.800278) (xy 164.832475 124.840275) (xy 164.846576 124.8404) (xy 164.84981 124.833483)
				(xy 164.84981 124.827418) (xy 165.35019 124.827418) (xy 165.354163 124.840949) (xy 165.36172 124.842035)
				(xy 165.499377 124.800278) (xy 165.510761 124.795562) (xy 165.682948 124.703525) (xy 165.693185 124.696685)
				(xy 165.844118 124.572819) (xy 165.852819 124.564118) (xy 165.976685 124.413185) (xy 165.983525 124.402948)
				(xy 166.075562 124.230761) (xy 166.080278 124.219377) (xy 166.120275 124.087525) (xy 166.1204 124.073424)
				(xy 166.113483 124.07019) (xy 165.368305 124.07019) (xy 165.353066 124.074665) (xy 165.351861 124.076055)
				(xy 165.35019 124.083738) (xy 165.35019 124.827418) (xy 164.84981 124.827418) (xy 164.84981 124.088305)
				(xy 164.845335 124.073066) (xy 164.843945 124.071861) (xy 164.836262 124.07019) (xy 164.092582 124.07019)
				(xy 164.079051 124.074163) (xy 164.077965 124.08172) (xy 156.0073 124.08172) (xy 156.0073 123.566576)
				(xy 164.0796 123.566576) (xy 164.086517 123.56981) (xy 164.831695 123.56981) (xy 164.846934 123.565335)
				(xy 164.848139 123.563945) (xy 164.84981 123.556262) (xy 164.84981 123.551695) (xy 165.35019 123.551695)
				(xy 165.354665 123.566934) (xy 165.356055 123.568139) (xy 165.363738 123.56981) (xy 166.107418 123.56981)
				(xy 166.120949 123.565837) (xy 166.122035 123.55828) (xy 166.080278 123.420623) (xy 166.075562 123.409239)
				(xy 165.983525 123.237052) (xy 165.976685 123.226815) (xy 165.852819 123.075882) (xy 165.844118 123.067181)
				(xy 165.693185 122.943315) (xy 165.682948 122.936475) (xy 165.510761 122.844438) (xy 165.499377 122.839722)
				(xy 165.367525 122.799725) (xy 165.353424 122.7996) (xy 165.35019 122.806517) (xy 165.35019 123.551695)
				(xy 164.84981 123.551695) (xy 164.84981 122.812582) (xy 164.845837 122.799051) (xy 164.83828 122.797965)
				(xy 164.700623 122.839722) (xy 164.689239 122.844438) (xy 164.517052 122.936475) (xy 164.506815 122.943315)
				(xy 164.355882 123.067181) (xy 164.347181 123.075882) (xy 164.223315 123.226815) (xy 164.216475 123.237052)
				(xy 164.124438 123.409239) (xy 164.119722 123.420623) (xy 164.079725 123.552475) (xy 164.0796 123.566576)
				(xy 156.0073 123.566576) (xy 156.0073 116.294098) (xy 156.007907 116.281748) (xy 156.011857 116.241643)
				(xy 156.012464 116.23548) (xy 155.991857 116.026253) (xy 155.990061 116.020333) (xy 155.99006 116.020327)
				(xy 155.940059 115.855496) (xy 155.930828 115.825065) (xy 155.853711 115.68079) (xy 155.834639 115.645109)
				(xy 155.834639 115.645108) (xy 155.831721 115.63965) (xy 155.769186 115.563451) (xy 155.698347 115.477133)
				(xy 155.662396 115.447629) (xy 155.653234 115.439324) (xy 154.685491 114.471581) (xy 154.651465 114.409269)
				(xy 154.658177 114.334267) (xy 154.715282 114.196405) (xy 154.715282 114.196404) (xy 154.717172 114.191842)
				(xy 154.734507 114.119638) (xy 154.776665 113.944038) (xy 154.776666 113.944032) (xy 154.77782 113.939225)
				(xy 154.798203 113.680232) (xy 154.77782 113.421238) (xy 154.769427 113.386276) (xy 154.756898 113.334088)
				(xy 154.760446 113.26318) (xy 154.801766 113.205447) (xy 154.869531 113.179064) (xy 154.981393 113.17026)
				(xy 154.9862 113.169106) (xy 154.986206 113.169105) (xy 155.170517 113.124856) (xy 155.23401 113.109613)
				(xy 155.413686 113.035189) (xy 155.469455 113.012089) (xy 155.469459 113.012087) (xy 155.474029 113.010194)
				(xy 155.69554 112.874452) (xy 155.699295 112.871245) (xy 155.699299 112.871242) (xy 155.889333 112.708936)
				(xy 155.893089 112.705728) (xy 155.896297 112.701972) (xy 156.058599 112.511942) (xy 156.058599 112.511941)
				(xy 156.061812 112.50818) (xy 156.197555 112.286669) (xy 156.235989 112.193881) (xy 156.280538 112.138601)
				(xy 156.352398 112.1161) (xy 159.44952 112.1161) (xy 159.517641 112.136102) (xy 159.538615 112.153005)
				(xy 161.128844 113.743234) (xy 161.137149 113.752396) (xy 161.166653 113.788347) (xy 161.290013 113.889585)
				(xy 161.329171 113.921721) (xy 161.514585 114.020828) (xy 161.715773 114.081857) (xy 161.925 114.102465)
				(xy 161.971277 114.097907) (xy 161.983627 114.0973) (xy 163.9067 114.0973) (xy 163.974821 114.117302)
				(xy 164.021314 114.170958) (xy 164.0327 114.2233) (xy 164.0327 115.190729) (xy 164.012698 115.25885)
				(xy 163.999405 115.276062) (xy 163.963306 115.315276) (xy 163.882261 115.439324) (xy 163.844171 115.497626)
				(xy 163.833188 115.514436) (xy 163.831094 115.51921) (xy 163.754711 115.693347) (xy 163.737626 115.732296)
				(xy 163.736348 115.737345) (xy 163.736346 115.737349) (xy 163.680506 115.957856) (xy 163.679225 115.962915)
				(xy 163.678795 115.968106) (xy 163.678794 115.968111) (xy 163.673466 116.032418) (xy 163.65958 116.2)
				(xy 163.660272 116.208351) (xy 163.678604 116.429586) (xy 163.679225 116.437085) (xy 163.680506 116.442143)
				(xy 163.680506 116.442144) (xy 163.730779 116.640665) (xy 163.737626 116.667704) (xy 163.739718 116.672474)
				(xy 163.739719 116.672476) (xy 163.753098 116.702977) (xy 163.833188 116.885564) (xy 163.836036 116.889923)
				(xy 163.836037 116.889925) (xy 163.944645 117.056162) (xy 163.963306 117.084724) (xy 164.12443 117.259751)
				(xy 164.312165 117.405871) (xy 164.316748 117.408351) (xy 164.516805 117.516617) (xy 164.516808 117.516618)
				(xy 164.52139 117.519098) (xy 164.746398 117.596344) (xy 164.751532 117.597201) (xy 164.751537 117.597202)
				(xy 164.975914 117.634643) (xy 164.975916 117.634643) (xy 164.981051 117.6355) (xy 165.218949 117.6355)
				(xy 165.224084 117.634643) (xy 165.224086 117.634643) (xy 165.448463 117.597202) (xy 165.448468 117.597201)
				(xy 165.453602 117.596344) (xy 165.67861 117.519098) (xy 165.683192 117.516618) (xy 165.683195 117.516617)
				(xy 165.883252 117.408351) (xy 165.887835 117.405871) (xy 166.07557 117.259751) (xy 166.236694 117.084724)
				(xy 166.255355 117.056162) (xy 166.363963 116.889925) (xy 166.363964 116.889923) (xy 166.366812 116.885564)
				(xy 166.446902 116.702977) (xy 166.460281 116.672476) (xy 166.460282 116.672474) (xy 166.462374 116.667704)
				(xy 166.469222 116.640665) (xy 166.519494 116.442144) (xy 166.519494 116.442143) (xy 166.520775 116.437085)
				(xy 166.521397 116.429586) (xy 166.539728 116.208351) (xy 166.54042 116.2) (xy 166.526534 116.032418)
				(xy 166.521206 115.968111) (xy 166.521205 115.968106) (xy 166.520775 115.962915) (xy 166.519494 115.957856)
				(xy 166.463654 115.737349) (xy 166.463652 115.737345) (xy 166.462374 115.732296) (xy 166.44529 115.693347)
				(xy 166.368906 115.51921) (xy 166.366812 115.514436) (xy 166.35583 115.497626) (xy 166.317739 115.439324)
				(xy 166.236694 115.315276) (xy 166.200597 115.276064) (xy 166.169178 115.212401) (xy 166.1673 115.190729)
				(xy 166.1673 114.113096) (xy 166.187302 114.044975) (xy 166.200599 114.027759) (xy 166.272752 113.94938)
				(xy 166.272757 113.949373) (xy 166.276286 113.94554) (xy 166.280412 113.939225) (xy 166.408087 113.743804)
				(xy 166.408088 113.743802) (xy 166.410936 113.739443) (xy 166.503622 113.52814) (xy 166.507735 113.518764)
				(xy 166.507736 113.518761) (xy 166.509827 113.513994) (xy 166.528945 113.4385) (xy 166.568982 113.280393)
				(xy 166.570261 113.275343) (xy 166.590591 113.03) (xy 166.570261 112.784657) (xy 166.517861 112.577731)
				(xy 166.511105 112.551051) (xy 166.511103 112.551046) (xy 166.509827 112.546006) (xy 166.483142 112.485169)
				(xy 166.41303 112.325331) (xy 166.410936 112.320557) (xy 166.385812 112.282101) (xy 166.279136 112.118822)
				(xy 166.279135 112.118821) (xy 166.276286 112.11446) (xy 166.268182 112.105656) (xy 166.186311 112.016721)
				(xy 166.10955 111.933337) (xy 165.952382 111.811008) (xy 165.910911 111.753383) (xy 165.907177 111.682484)
				(xy 165.942367 111.620822) (xy 166.005193 111.587997) (xy 166.036932 111.581684) (xy 166.059427 111.572367)
				(xy 166.122808 111.530017) (xy 166.140017 111.512808) (xy 166.182368 111.449425) (xy 166.191684 111.426934)
				(xy 166.202793 111.371085) (xy 166.204 111.35883) (xy 166.204 110.758305) (xy 166.199525 110.743066)
				(xy 166.198135 110.741861) (xy 166.190452 110.74019) (xy 164.014116 110.74019) (xy 163.998877 110.744665)
				(xy 163.997672 110.746055) (xy 163.996001 110.753738) (xy 163.996001 111.358828) (xy 163.997209 111.371088)
				(xy 164.008315 111.426931) (xy 164.017633 111.449427) (xy 164.059983 111.512808) (xy 164.077192 111.530017)
				(xy 164.140575 111.572368) (xy 164.163066 111.581684) (xy 164.194808 111.587998) (xy 164.257717 111.620906)
				(xy 164.292849 111.682601) (xy 164.289049 111.753496) (xy 164.247618 111.811008) (xy 164.09045 111.933337)
				(xy 164.089599 111.932244) (xy 164.031699 111.960822) (xy 164.010023 111.9627) (xy 162.41928 111.9627)
				(xy 162.351159 111.942698) (xy 162.330185 111.925795) (xy 160.739956 110.335566) (xy 160.731651 110.326404)
				(xy 160.706069 110.295232) (xy 160.702147 110.290453) (xy 160.638402 110.238139) (xy 160.618365 110.221695)
				(xy 163.996 110.221695) (xy 164.000475 110.236934) (xy 164.001865 110.238139) (xy 164.009548 110.23981)
				(xy 164.831695 110.23981) (xy 164.846934 110.235335) (xy 164.848139 110.233945) (xy 164.84981 110.226262)
				(xy 164.84981 110.221695) (xy 165.35019 110.221695) (xy 165.354665 110.236934) (xy 165.356055 110.238139)
				(xy 165.363738 110.23981) (xy 166.185884 110.23981) (xy 166.201123 110.235335) (xy 166.202328 110.233945)
				(xy 166.203999 110.226262) (xy 166.203999 109.621172) (xy 166.202791 109.608912) (xy 166.191685 109.553069)
				(xy 166.182367 109.530573) (xy 166.140017 109.467192) (xy 166.122808 109.449983) (xy 166.059425 109.407632)
				(xy 166.036934 109.398316) (xy 165.981085 109.387207) (xy 165.96883 109.386) (xy 165.368305 109.386)
				(xy 165.353066 109.390475) (xy 165.351861 109.391865) (xy 165.35019 109.399548) (xy 165.35019 110.221695)
				(xy 164.84981 110.221695) (xy 164.84981 109.404116) (xy 164.845335 109.388877) (xy 164.843945 109.387672)
				(xy 164.836262 109.386001) (xy 164.231172 109.386001) (xy 164.218912 109.387209) (xy 164.163069 109.398315)
				(xy 164.140573 109.407633) (xy 164.077192 109.449983) (xy 164.059983 109.467192) (xy 164.017632 109.530575)
				(xy 164.008316 109.553066) (xy 163.997207 109.608915) (xy 163.996 109.62117) (xy 163.996 110.221695)
				(xy 160.618365 110.221695) (xy 160.544416 110.161007) (xy 160.544412 110.161005) (xy 160.539629 110.157079)
				(xy 160.50462 110.138366) (xy 160.354215 110.057972) (xy 160.153027 109.996943) (xy 159.9438 109.976335)
				(xy 159.910134 109.979651) (xy 159.897524 109.980893) (xy 159.885173 109.9815) (xy 156.666602 109.9815)
				(xy 156.598481 109.961498) (xy 156.550193 109.903718) (xy 156.514649 109.817908) (xy 156.512754 109.813333)
				(xy 156.377012 109.591822) (xy 156.208288 109.394272) (xy 156.200916 109.387976) (xy 156.122277 109.320811)
				(xy 156.083468 109.26136) (xy 156.082962 109.190365) (xy 156.122277 109.129189) (xy 156.204532 109.058936)
				(xy 156.208288 109.055728) (xy 156.377012 108.858178) (xy 156.512754 108.636667) (xy 156.520032 108.619098)
				(xy 156.554335 108.536282) (xy 156.598883 108.481001) (xy 156.670744 108.4585) (xy 164.308046 108.4585)
				(xy 164.376167 108.478502) (xy 164.385436 108.485068) (xy 164.408048 108.502668) (xy 164.408059 108.502675)
				(xy 164.412165 108.505871) (xy 164.499527 108.553149) (xy 164.616805 108.616617) (xy 164.616808 108.616618)
				(xy 164.62139 108.619098) (xy 164.846398 108.696344) (xy 164.851532 108.697201) (xy 164.851537 108.697202)
				(xy 165.075914 108.734643) (xy 165.075916 108.734643) (xy 165.081051 108.7355) (xy 165.318949 108.7355)
				(xy 165.324084 108.734643) (xy 165.324086 108.734643) (xy 165.548463 108.697202) (xy 165.548468 108.697201)
				(xy 165.553602 108.696344) (xy 165.77861 108.619098) (xy 165.783192 108.616618) (xy 165.783195 108.616617)
				(xy 165.900473 108.553149) (xy 165.987835 108.505871) (xy 166.17557 108.359751) (xy 166.336694 108.184724)
				(xy 166.343318 108.174585) (xy 166.463963 107.989925) (xy 166.463964 107.989923) (xy 166.466812 107.985564)
				(xy 166.562374 107.767704) (xy 166.578471 107.704141) (xy 166.619494 107.542144) (xy 166.619494 107.542143)
				(xy 166.620775 107.537085) (xy 166.621439 107.529083) (xy 166.63999 107.305189) (xy 166.64042 107.3)
				(xy 166.631123 107.187798) (xy 166.621206 107.068111) (xy 166.621205 107.068106) (xy 166.620775 107.062915)
				(xy 166.583042 106.913912) (xy 166.563654 106.837349) (xy 166.563652 106.837345) (xy 166.562374 106.832296)
				(xy 166.466812 106.614436) (xy 166.441699 106.575997) (xy 166.339544 106.419638) (xy 166.339543 106.419637)
				(xy 166.336694 106.415276) (xy 166.327584 106.405379) (xy 166.181619 106.24682) (xy 166.17557 106.240249)
				(xy 165.987835 106.094129) (xy 165.900473 106.046851) (xy 165.783195 105.983383) (xy 165.783192 105.983382)
				(xy 165.77861 105.980902) (xy 165.553602 105.903656) (xy 165.548468 105.902799) (xy 165.548463 105.902798)
				(xy 165.324086 105.865357) (xy 165.324084 105.865357) (xy 165.318949 105.8645) (xy 165.081051 105.8645)
				(xy 165.075916 105.865357) (xy 165.075914 105.865357) (xy 164.851537 105.902798) (xy 164.851532 105.902799)
				(xy 164.846398 105.903656) (xy 164.62139 105.980902) (xy 164.616808 105.983382) (xy 164.616805 105.983383)
				(xy 164.499527 106.046851) (xy 164.412165 106.094129) (xy 164.22443 106.240249) (xy 164.219027 106.246118)
				(xy 164.184857 106.283237) (xy 164.124005 106.319808) (xy 164.092156 106.3239) (xy 156.348255 106.3239)
				(xy 156.280134 106.303898) (xy 156.231846 106.246118) (xy 156.199449 106.167903) (xy 156.199448 106.1679)
				(xy 156.197555 106.163331) (xy 156.061812 105.94182) (xy 155.893089 105.744272) (xy 155.889333 105.741064)
				(xy 155.699299 105.578758) (xy 155.699295 105.578755) (xy 155.69554 105.575548) (xy 155.474029 105.439806)
				(xy 155.469459 105.437913) (xy 155.469455 105.437911) (xy 155.238583 105.342281) (xy 155.238581 105.34228)
				(xy 155.23401 105.340387) (xy 155.148164 105.319778) (xy 154.986206 105.280895) (xy 154.9862 105.280894)
				(xy 154.981393 105.27974) (xy 154.869531 105.270936) (xy 154.80319 105.245652) (xy 154.76105 105.188514)
				(xy 154.756898 105.115912) (xy 154.776664 105.033578) (xy 154.776664 105.033576) (xy 154.77782 105.028762)
				(xy 154.798203 104.769768) (xy 154.77782 104.510775) (xy 154.773457 104.492598) (xy 154.718325 104.262961)
				(xy 154.717172 104.258158) (xy 154.617752 104.01814) (xy 154.48201 103.796629) (xy 154.341634 103.63227)
				(xy 154.316502 103.602844) (xy 154.3165 103.602842) (xy 154.313287 103.59908) (xy 154.309525 103.595867)
				(xy 154.30952 103.595862) (xy 154.225406 103.524023) (xy 154.165204 103.472606) (xy 153.272082 102.823716)
				(xy 153.237078 102.798284) (xy 153.23707 102.798279) (xy 153.235072 102.796827) (xy 153.069028 102.695075)
				(xy 153.064458 102.693182) (xy 153.064454 102.69318) (xy 152.833582 102.59755) (xy 152.83358 102.597549)
				(xy 152.829009 102.595656) (xy 152.739437 102.574152) (xy 152.581206 102.536164) (xy 152.5812 102.536163)
				(xy 152.576393 102.535009) (xy 152.317399 102.514626) (xy 152.312469 102.515014) (xy 152.312459 102.515014)
				(xy 152.291662 102.516651) (xy 152.222182 102.502057) (xy 152.201841 102.488439) (xy 152.142091 102.439403)
				(xy 152.142088 102.439401) (xy 152.137309 102.435479) (xy 152.074407 102.401857) (xy 151.951895 102.336372)
				(xy 151.750707 102.275343) (xy 151.54148 102.254735) (xy 151.535317 102.255342) (xy 151.495204 102.259293)
				(xy 151.482853 102.2599) (xy 151.104686 102.2599) (xy 151.036565 102.239898) (xy 151.008875 102.21573)
				(xy 150.963907 102.163078) (xy 150.963902 102.163073) (xy 150.960689 102.159311) (xy 150.76314 101.990588)
				(xy 150.541628 101.854846) (xy 150.537058 101.852953) (xy 150.537054 101.852951) (xy 150.306183 101.757321)
				(xy 150.306181 101.75732) (xy 150.30161 101.755427) (xy 150.213276 101.73422) (xy 150.053807 101.695934)
				(xy 150.053801 101.695933) (xy 150.048994 101.694779) (xy 149.958809 101.687681) (xy 149.85731 101.679693)
				(xy 149.857301 101.679693) (xy 149.854853 101.6795) (xy 148.705147 101.6795) (xy 148.702699 101.679693)
				(xy 148.70269 101.679693) (xy 148.601191 101.687681) (xy 148.511006 101.694779) (xy 148.506199 101.695933)
				(xy 148.506193 101.695934) (xy 148.346724 101.73422) (xy 148.25839 101.755427) (xy 148.253819 101.75732)
				(xy 148.253817 101.757321) (xy 148.022946 101.852951) (xy 148.022942 101.852953) (xy 148.018372 101.854846)
				(xy 147.79686 101.990588) (xy 147.599311 102.159311) (xy 147.430588 102.35686) (xy 147.294846 102.578372)
				(xy 147.292953 102.582942) (xy 147.292951 102.582946) (xy 147.249865 102.686965) (xy 147.205316 102.742246)
				(xy 147.137953 102.764667) (xy 147.067622 102.74618) (xy 146.98423 102.695077) (xy 146.979658 102.693183)
				(xy 146.748784 102.597551) (xy 146.748782 102.59755) (xy 146.744211 102.595657) (xy 146.654636 102.574152)
				(xy 146.496407 102.536164) (xy 146.496401 102.536163) (xy 146.491594 102.535009) (xy 146.232601 102.514626)
				(xy 145.973607 102.535009) (xy 145.9688 102.536163) (xy 145.968794 102.536164) (xy 145.810563 102.574152)
				(xy 145.720991 102.595656) (xy 145.716416 102.597551) (xy 145.716413 102.597552) (xy 145.610719 102.641331)
				(xy 145.54013 102.64892) (xy 145.473407 102.614017) (xy 143.496156 100.636766) (xy 143.487851 100.627604)
				(xy 143.462269 100.596432) (xy 143.458347 100.591653) (xy 143.407517 100.549938) (xy 143.300616 100.462207)
				(xy 143.300612 100.462205) (xy 143.295829 100.458279) (xy 143.110415 100.359172) (xy 142.909227 100.298143)
				(xy 142.7 100.277535) (xy 142.693837 100.278142) (xy 142.653724 100.282093) (xy 142.641373 100.2827)
				(xy 137.658627 100.2827) (xy 137.646276 100.282093) (xy 137.606163 100.278142) (xy 137.6 100.277535)
				(xy 137.390773 100.298143) (xy 137.189585 100.359172) (xy 137.004171 100.458279) (xy 136.999388 100.462205)
				(xy 136.999384 100.462207) (xy 136.892483 100.549938) (xy 136.841653 100.591653) (xy 136.837731 100.596432)
				(xy 136.812149 100.627604) (xy 136.803844 100.636766) (xy 135.644815 101.795795) (xy 135.582503 101.829821)
				(xy 135.55572 101.8327) (xy 135.061499 101.8327) (xy 134.993378 101.812698) (xy 134.946885 101.759042)
				(xy 134.935499 101.7067) (xy 134.935499 101.63582) (xy 134.932665 101.599796) (xy 134.909329 101.519471)
				(xy 134.890081 101.45322) (xy 134.89008 101.453218) (xy 134.887869 101.445607) (xy 134.853109 101.38683)
				(xy 134.810169 101.314223) (xy 134.806135 101.307402) (xy 134.692598 101.193865) (xy 134.554393 101.112131)
				(xy 134.546782 101.10992) (xy 134.54678 101.109919) (xy 134.406381 101.069129) (xy 134.406376 101.069128)
				(xy 134.400204 101.067335) (xy 134.378581 101.065633) (xy 134.366638 101.064693) (xy 134.366629 101.064693)
				(xy 134.364181 101.0645) (xy 133.101024 101.0645) (xy 131.83582 101.064501) (xy 131.816577 101.066015)
				(xy 131.806214 101.06683) (xy 131.806213 101.06683) (xy 131.799796 101.067335) (xy 131.793616 101.06913)
				(xy 131.793613 101.069131) (xy 131.65322 101.109919) (xy 131.653218 101.10992) (xy 131.645607 101.112131)
				(xy 131.507402 101.193865) (xy 131.393865 101.307402) (xy 131.389831 101.314223) (xy 131.346892 101.38683)
				(xy 131.312131 101.445607) (xy 131.30992 101.453218) (xy 131.309919 101.45322) (xy 131.269129 101.593619)
				(xy 131.269128 101.593624) (xy 131.267335 101.599796) (xy 131.266831 101.606206) (xy 131.264694 101.633356)
				(xy 131.2645 101.635819) (xy 131.2645 101.7067) (xy 131.244498 101.774821) (xy 131.190842 101.821314)
				(xy 131.1385 101.8327) (xy 130.39428 101.8327) (xy 130.326159 101.812698) (xy 130.305185 101.795795)
				(xy 129.396156 100.886766) (xy 129.387851 100.877604) (xy 129.362269 100.846432) (xy 129.358347 100.841653)
				(xy 129.251748 100.75417) (xy 129.200616 100.712207) (xy 129.200612 100.712205) (xy 129.195829 100.708279)
				(xy 129.053839 100.632383) (xy 129.015869 100.612087) (xy 129.015867 100.612086) (xy 129.010415 100.609172)
				(xy 128.809227 100.548143) (xy 128.6 100.527535) (xy 128.593837 100.528142) (xy 128.553724 100.532093)
				(xy 128.541373 100.5327) (xy 125.788618 100.5327) (xy 125.776268 100.532093) (xy 125.736163 100.528143)
				(xy 125.73 100.527536) (xy 125.520773 100.548143) (xy 125.514853 100.549939) (xy 125.514847 100.54994)
				(xy 125.390267 100.587731) (xy 125.319585 100.609172) (xy 125.276161 100.632383) (xy 125.145852 100.702035)
				(xy 125.13417 100.708279) (xy 125.129385 100.712206) (xy 125.113896 100.724918) (xy 125.078251 100.754171)
				(xy 124.971653 100.841653) (xy 124.967731 100.846432) (xy 124.942149 100.877604) (xy 124.933844 100.886766)
				(xy 124.419855 101.400755) (xy 124.357543 101.434781) (xy 124.295386 101.428711) (xy 124.294393 101.432131)
				(xy 124.146381 101.389129) (xy 124.146376 101.389128) (xy 124.140204 101.387335) (xy 124.118581 101.385633)
				(xy 124.106638 101.384693) (xy 124.106629 101.384693) (xy 124.104181 101.3845) (xy 123.997235 101.3845)
				(xy 123.929114 101.364498) (xy 123.882621 101.310842) (xy 123.872517 101.240568) (xy 123.902011 101.175988)
				(xy 123.912349 101.165385) (xy 124.005055 101.080872) (xy 124.012873 101.072296) (xy 124.129149 100.918321)
				(xy 124.135257 100.908457) (xy 124.221265 100.73573) (xy 124.225454 100.724918) (xy 124.261675 100.597616)
				(xy 124.261557 100.583513) (xy 124.254116 100.58019) (xy 122.131482 100.58019) (xy 122.117951 100.584163)
				(xy 122.116792 100.592227) (xy 122.154546 100.724918) (xy 122.158735 100.73573) (xy 122.244743 100.908457)
				(xy 122.250851 100.918321) (xy 122.367127 101.072296) (xy 122.374945 101.080872) (xy 122.467652 101.165386)
				(xy 122.504518 101.22606) (xy 122.502729 101.297034) (xy 122.462853 101.355774) (xy 122.397549 101.383631)
				(xy 122.382767 101.384501) (xy 122.27582 101.384501) (xy 122.256577 101.386015) (xy 122.246214 101.38683)
				(xy 122.246213 101.38683) (xy 122.239796 101.387335) (xy 122.233616 101.38913) (xy 122.233613 101.389131)
				(xy 122.09322 101.429919) (xy 122.093218 101.42992) (xy 122.085607 101.432131) (xy 121.947402 101.513865)
				(xy 121.833865 101.627402) (xy 121.752131 101.765607) (xy 121.74992 101.773218) (xy 121.749919 101.77322)
				(xy 121.709129 101.913619) (xy 121.709128 101.913624) (xy 121.707335 101.919796) (xy 121.7045 101.955819)
				(xy 121.704501 103.78418) (xy 121.707335 103.820204) (xy 121.70913 103.826384) (xy 121.709131 103.826387)
				(xy 121.74576 103.952463) (xy 121.752131 103.974393) (xy 121.833865 104.112598) (xy 121.947402 104.226135)
				(xy 121.954223 104.230169) (xy 122.009672 104.262961) (xy 122.085607 104.307869) (xy 122.093218 104.31008)
				(xy 122.09322 104.310081) (xy 122.233619 104.350871) (xy 122.233624 104.350872) (xy 122.239796 104.352665)
				(xy 122.261419 104.354367) (xy 122.273362 104.355307) (xy 122.273371 104.355307) (xy 122.275819 104.3555)
				(xy 123.18926 104.3555) (xy 124.10418 104.355499) (xy 124.123423 104.353985) (xy 124.133786 104.35317)
				(xy 124.133787 104.35317) (xy 124.140204 104.352665) (xy 124.146384 104.35087) (xy 124.146387 104.350869)
				(xy 124.28678 104.310081) (xy 124.286782 104.31008) (xy 124.294393 104.307869) (xy 124.370329 104.262961)
				(xy 124.425777 104.230169) (xy 124.432598 104.226135) (xy 124.546135 104.112598) (xy 124.627869 103.974393)
				(xy 124.629266 103.975219) (xy 124.668764 103.92775) (xy 124.702417 103.911789) (xy 124.870415 103.860828)
				(xy 125.055829 103.761721) (xy 125.085202 103.737615) (xy 125.097426 103.727583) (xy 125.213567 103.63227)
				(xy 125.213568 103.632269) (xy 125.218347 103.628347) (xy 125.247851 103.592396) (xy 125.256156 103.583234)
				(xy 126.135185 102.704205) (xy 126.197497 102.670179) (xy 126.22428 102.6673) (xy 128.10572 102.6673)
				(xy 128.173841 102.687302) (xy 128.194815 102.704205) (xy 129.103844 103.613234) (xy 129.112149 103.622396)
				(xy 129.141653 103.658347) (xy 129.226018 103.727583) (xy 129.304171 103.791721) (xy 129.489586 103.890828)
				(xy 129.495506 103.892624) (xy 129.495509 103.892625) (xy 129.600598 103.924503) (xy 129.690773 103.951857)
				(xy 129.9 103.972465) (xy 129.946277 103.967907) (xy 129.958627 103.9673) (xy 131.138501 103.9673)
				(xy 131.206622 103.987302) (xy 131.253115 104.040958) (xy 131.264501 104.0933) (xy 131.264501 104.16418)
				(xy 131.267335 104.200204) (xy 131.26913 104.206381) (xy 131.269131 104.206387) (xy 131.299257 104.310081)
				(xy 131.312131 104.354393) (xy 131.393865 104.492598) (xy 131.507402 104.606135) (xy 131.514223 104.610169)
				(xy 131.63544 104.681856) (xy 131.645607 104.687869) (xy 131.653218 104.69008) (xy 131.65322 104.690081)
				(xy 131.793619 104.730871) (xy 131.793624 104.730872) (xy 131.799796 104.732665) (xy 131.821419 104.734367)
				(xy 131.833362 104.735307) (xy 131.833371 104.735307) (xy 131.835819 104.7355) (xy 133.098976 104.7355)
				(xy 134.36418 104.735499) (xy 134.383423 104.733985) (xy 134.393786 104.73317) (xy 134.393787 104.73317)
				(xy 134.400204 104.732665) (xy 134.406384 104.73087) (xy 134.406387 104.730869) (xy 134.54678 104.690081)
				(xy 134.546782 104.69008) (xy 134.554393 104.687869) (xy 134.564561 104.681856) (xy 134.685777 104.610169)
				(xy 134.692598 104.606135) (xy 134.806135 104.492598) (xy 134.887869 104.354393) (xy 134.900743 104.310081)
				(xy 134.930871 104.206381) (xy 134.930872 104.206376) (xy 134.932665 104.200204) (xy 134.934367 104.178581)
				(xy 134.935307 104.166638) (xy 134.935307 104.166629) (xy 134.9355 104.164181) (xy 134.9355 104.0933)
				(xy 134.955502 104.025179) (xy 135.009158 103.978686) (xy 135.0615 103.9673) (xy 135.991373 103.9673)
				(xy 136.003723 103.967907) (xy 136.05 103.972465) (xy 136.259227 103.951857) (xy 136.460415 103.890828)
				(xy 136.516541 103.860828) (xy 136.580974 103.826387) (xy 136.645829 103.791721) (xy 136.682384 103.761721)
				(xy 136.808347 103.658347) (xy 136.837851 103.622396) (xy 136.846156 103.613234) (xy 138.005185 102.454205)
				(xy 138.067497 102.420179) (xy 138.09428 102.4173) (xy 142.20572 102.4173) (xy 142.273841 102.437302)
				(xy 142.294815 102.454205) (xy 143.851188 104.010578) (xy 143.885214 104.07289) (xy 143.878502 104.147891)
				(xy 143.83472 104.25359) (xy 143.832828 104.258158) (xy 143.831675 104.262961) (xy 143.776544 104.492598)
				(xy 143.77218 104.510775) (xy 143.751797 104.769768) (xy 143.77218 105.028762) (xy 143.773334 105.033569)
				(xy 143.773335 105.033575) (xy 143.793102 105.11591) (xy 143.789555 105.186818) (xy 143.748235 105.244552)
				(xy 143.680469 105.270936) (xy 143.660465 105.27251) (xy 143.568607 105.27974) (xy 143.5638 105.280894)
				(xy 143.563794 105.280895) (xy 143.401836 105.319778) (xy 143.31599 105.340387) (xy 143.311419 105.34228)
				(xy 143.311417 105.342281) (xy 143.080545 105.437911) (xy 143.080541 105.437913) (xy 143.075971 105.439806)
				(xy 142.85446 105.575548) (xy 142.850705 105.578755) (xy 142.850701 105.578758) (xy 142.660667 105.741064)
				(xy 142.656911 105.744272) (xy 142.488188 105.94182) (xy 142.352445 106.163331) (xy 142.350552 106.167901)
				(xy 142.350551 106.167903) (xy 142.318964 106.244161) (xy 142.274416 106.299442) (xy 142.207052 106.321863)
				(xy 142.190209 106.321336) (xy 142.1638 106.318735) (xy 141.954573 106.339343) (xy 141.753385 106.400372)
				(xy 141.63989 106.461037) (xy 141.567971 106.499479) (xy 141.405453 106.632853) (xy 141.401531 106.637632)
				(xy 141.375949 106.668804) (xy 141.367644 106.677966) (xy 140.256766 107.788844) (xy 140.247604 107.797149)
				(xy 140.211653 107.826653) (xy 140.081239 107.985564) (xy 140.078279 107.989171) (xy 139.979172 108.174585)
				(xy 139.918143 108.375773) (xy 139.897535 108.585) (xy 139.900894 108.619098) (xy 139.902093 108.631276)
				(xy 139.9027 108.643627) (xy 139.9027 120.323317) (xy 139.882698 120.391438) (xy 139.869402 120.408654)
				(xy 139.863306 120.415276) (xy 139.733188 120.614436) (xy 139.637626 120.832296) (xy 139.579225 121.062915)
				(xy 139.55958 121.3) (xy 139.579225 121.537085) (xy 139.580506 121.542143) (xy 139.580506 121.542144)
				(xy 139.63145 121.743315) (xy 139.637626 121.767704) (xy 139.639718 121.772474) (xy 139.639719 121.772476)
				(xy 139.685077 121.875882) (xy 139.733188 121.985564) (xy 139.736036 121.989923) (xy 139.736037 121.989925)
				(xy 139.766827 122.037052) (xy 139.863306 122.184724) (xy 139.866832 122.188555) (xy 139.866835 122.188558)
				(xy 139.869402 122.191346) (xy 139.900822 122.255011) (xy 139.9027 122.276683) (xy 139.9027 123.33072)
				(xy 139.882698 123.398841) (xy 139.865795 123.419815) (xy 138.659815 124.625795) (xy 138.597503 124.659821)
				(xy 138.57072 124.6627) (xy 130.233618 124.6627) (xy 130.221268 124.662093) (xy 130.181163 124.658143)
				(xy 130.175 124.657536) (xy 129.965773 124.678143) (xy 129.959853 124.679939) (xy 129.959847 124.67994)
				(xy 129.814431 124.724052) (xy 129.764585 124.739172) (xy 129.75913 124.742088) (xy 129.588143 124.833483)
				(xy 129.57917 124.838279) (xy 129.532163 124.876857) (xy 129.416653 124.971653) (xy 129.412731 124.976432)
				(xy 129.387149 125.007604) (xy 129.378844 125.016766) (xy 128.191766 126.203844) (xy 128.182604 126.212149)
				(xy 128.146653 126.241653) (xy 128.142731 126.246432) (xy 128.062813 126.343813) (xy 128.017207 126.399384)
				(xy 128.017205 126.399388) (xy 128.013279 126.404171) (xy 127.914172 126.589585) (xy 127.853143 126.790773)
				(xy 127.832535 127) (xy 127.833142 127.006163) (xy 127.837093 127.046276) (xy 127.8377 127.058627)
				(xy 127.8377 130.116373) (xy 127.837093 130.128723) (xy 127.832535 130.175) (xy 127.853143 130.384227)
				(xy 127.914172 130.585415) (xy 127.917088 130.59087) (xy 127.990054 130.727379) (xy 128.010362 130.765373)
				(xy 128.010363 130.765374) (xy 128.013279 130.770829) (xy 128.146653 130.933347) (xy 128.151432 130.937269)
				(xy 128.182604 130.962851) (xy 128.191766 130.971156) (xy 129.085515 131.864905) (xy 129.119541 131.927217)
				(xy 129.114476 131.998032) (xy 129.071929 132.054868) (xy 129.005409 132.079679) (xy 128.99642 132.08)
				(xy 125.812421 132.08) (xy 125.7443 132.059998) (xy 125.697807 132.006342) (xy 125.687703 131.936068)
				(xy 125.716389 131.872429) (xy 125.926782 131.624735) (xy 125.928995 131.62213) (xy 125.930905 131.619312)
				(xy 125.930911 131.619305) (xy 126.136146 131.316606) (xy 126.136149 131.316602) (xy 126.138057 131.313787)
				(xy 126.312555 130.984648) (xy 126.331433 130.937269) (xy 126.397748 130.770829) (xy 126.450444 130.638572)
				(xy 126.463689 130.59087) (xy 126.549195 130.282904) (xy 126.550108 130.279616) (xy 126.610377 129.911988)
				(xy 126.61094 129.901617) (xy 126.630361 129.543412) (xy 126.630546 129.54) (xy 126.618105 129.310535)
				(xy 126.610562 129.171416) (xy 126.610561 129.171408) (xy 126.610377 129.168012) (xy 126.550108 128.800384)
				(xy 126.450444 128.441428) (xy 126.312555 128.095352) (xy 126.138057 127.766213) (xy 126.104912 127.717328)
				(xy 125.930911 127.460695) (xy 125.930905 127.460688) (xy 125.928995 127.45787) (xy 125.687821 127.173938)
				(xy 125.417362 126.917746) (xy 125.12079 126.692298) (xy 124.801581 126.500236) (xy 124.463477 126.343813)
				(xy 124.460245 126.342724) (xy 124.460235 126.34272) (xy 124.11368 126.225952) (xy 124.113674 126.22595)
				(xy 124.110444 126.224862) (xy 124.000976 126.200766) (xy 123.749949 126.145511) (xy 123.749945 126.14551)
				(xy 123.746618 126.144778) (xy 123.743228 126.144409) (xy 123.743226 126.144409) (xy 123.67738 126.137248)
				(xy 123.376267 126.1045) (xy 123.003733 126.1045) (xy 122.70262 126.137248) (xy 122.636774 126.144409)
				(xy 122.636772 126.144409) (xy 122.633382 126.144778) (xy 122.630055 126.14551) (xy 122.630051 126.145511)
				(xy 122.379024 126.200766) (xy 122.269556 126.224862) (xy 122.266326 126.22595) (xy 122.26632 126.225952)
				(xy 121.919765 126.34272) (xy 121.919755 126.342724) (xy 121.916523 126.343813) (xy 121.578419 126.500236)
				(xy 121.25921 126.692298) (xy 120.962638 126.917746) (xy 120.869298 127.006163) (xy 120.86265 127.01246)
				(xy 120.79944 127.044786) (xy 120.728787 127.037805) (xy 120.673124 126.993735) (xy 120.65 126.920985)
				(xy 120.65 122.88172) (xy 123.437965 122.88172) (xy 123.479722 123.019377) (xy 123.484438 123.030761)
				(xy 123.576475 123.202948) (xy 123.583315 123.213185) (xy 123.707181 123.364118) (xy 123.715882 123.372819)
				(xy 123.866815 123.496685) (xy 123.877052 123.503525) (xy 124.049239 123.595562) (xy 124.060623 123.600278)
				(xy 124.192475 123.640275) (xy 124.206576 123.6404) (xy 124.20981 123.633483) (xy 124.20981 123.627418)
				(xy 124.71019 123.627418) (xy 124.714163 123.640949) (xy 124.72172 123.642035) (xy 124.859377 123.600278)
				(xy 124.870761 123.595562) (xy 125.042948 123.503525) (xy 125.053185 123.496685) (xy 125.204118 123.372819)
				(xy 125.212819 123.364118) (xy 125.336685 123.213185) (xy 125.343525 123.202948) (xy 125.435562 123.030761)
				(xy 125.440278 123.019377) (xy 125.480275 122.887525) (xy 125.4804 122.873424) (xy 125.473483 122.87019)
				(xy 124.728305 122.87019) (xy 124.713066 122.874665) (xy 124.711861 122.876055) (xy 124.71019 122.883738)
				(xy 124.71019 123.627418) (xy 124.20981 123.627418) (xy 124.20981 122.888305) (xy 124.205335 122.873066)
				(xy 124.203945 122.871861) (xy 124.196262 122.87019) (xy 123.452582 122.87019) (xy 123.439051 122.874163)
				(xy 123.437965 122.88172) (xy 120.65 122.88172) (xy 120.65 122.366576) (xy 123.4396 122.366576)
				(xy 123.446517 122.36981) (xy 124.191695 122.36981) (xy 124.206934 122.365335) (xy 124.208139 122.363945)
				(xy 124.20981 122.356262) (xy 124.20981 122.351695) (xy 124.71019 122.351695) (xy 124.714665 122.366934)
				(xy 124.716055 122.368139) (xy 124.723738 122.36981) (xy 125.467418 122.36981) (xy 125.480949 122.365837)
				(xy 125.482035 122.35828) (xy 125.440278 122.220623) (xy 125.435562 122.209239) (xy 125.343525 122.037052)
				(xy 125.336685 122.026815) (xy 125.212819 121.875882) (xy 125.204118 121.867181) (xy 125.053185 121.743315)
				(xy 125.042948 121.736475) (xy 124.870761 121.644438) (xy 124.859377 121.639722) (xy 124.727525 121.599725)
				(xy 124.713424 121.5996) (xy 124.71019 121.606517) (xy 124.71019 122.351695) (xy 124.20981 122.351695)
				(xy 124.20981 121.612582) (xy 124.205837 121.599051) (xy 124.19828 121.597965) (xy 124.060623 121.639722)
				(xy 124.049239 121.644438) (xy 123.877052 121.736475) (xy 123.866815 121.743315) (xy 123.715882 121.867181)
				(xy 123.707181 121.875882) (xy 123.583315 122.026815) (xy 123.576475 122.037052) (xy 123.484438 122.209239)
				(xy 123.479722 122.220623) (xy 123.439725 122.352475) (xy 123.4396 122.366576) (xy 120.65 122.366576)
				(xy 120.65 110.210819) (xy 121.7045 110.210819) (xy 121.704501 112.03918) (xy 121.707335 112.075204)
				(xy 121.70913 112.081384) (xy 121.709131 112.081387) (xy 121.742855 112.197465) (xy 121.752131 112.229393)
				(xy 121.756166 112.236215) (xy 121.756166 112.236216) (xy 121.829831 112.360777) (xy 121.833865 112.367598)
				(xy 121.947402 112.481135) (xy 122.057094 112.546006) (xy 122.060839 112.548221) (xy 122.109292 112.600114)
				(xy 122.1227 112.656675) (xy 122.1227 114.241373) (xy 122.122093 114.253723) (xy 122.117535 114.3)
				(xy 122.138143 114.509227) (xy 122.199172 114.710415) (xy 122.274274 114.85092) (xy 122.298279 114.895829)
				(xy 122.431653 115.058347) (xy 122.436432 115.062269) (xy 122.467604 115.087851) (xy 122.476766 115.096156)
				(xy 123.028844 115.648234) (xy 123.037149 115.657396) (xy 123.066653 115.693347) (xy 123.071432 115.697269)
				(xy 123.182163 115.788143) (xy 123.224384 115.822793) (xy 123.224388 115.822795) (xy 123.229171 115.826721)
				(xy 123.234632 115.82964) (xy 123.234635 115.829642) (xy 123.290538 115.859523) (xy 123.323843 115.885308)
				(xy 123.480895 116.055912) (xy 123.480901 116.055918) (xy 123.48443 116.059751) (xy 123.488539 116.062949)
				(xy 123.488541 116.062951) (xy 123.565401 116.122773) (xy 123.672165 116.205871) (xy 123.726878 116.23548)
				(xy 123.876805 116.316617) (xy 123.876808 116.316618) (xy 123.88139 116.319098) (xy 124.106398 116.396344)
				(xy 124.111532 116.397201) (xy 124.111537 116.397202) (xy 124.335914 116.434643) (xy 124.335916 116.434643)
				(xy 124.341051 116.4355) (xy 124.578949 116.4355) (xy 124.584084 116.434643) (xy 124.584086 116.434643)
				(xy 124.808463 116.397202) (xy 124.808468 116.397201) (xy 124.813602 116.396344) (xy 125.03861 116.319098)
				(xy 125.043192 116.316618) (xy 125.043195 116.316617) (xy 125.193122 116.23548) (xy 125.247835 116.205871)
				(xy 125.354599 116.122773) (xy 125.431459 116.062951) (xy 125.431461 116.062949) (xy 125.43557 116.059751)
				(xy 125.538412 115.948035) (xy 125.593165 115.888558) (xy 125.593167 115.888555) (xy 125.596694 115.884724)
				(xy 125.681496 115.754925) (xy 125.723963 115.689925) (xy 125.723964 115.689923) (xy 125.726812 115.685564)
				(xy 125.790563 115.540227) (xy 125.820281 115.472476) (xy 125.820282 115.472474) (xy 125.822374 115.467704)
				(xy 125.826742 115.450458) (xy 125.879494 115.242144) (xy 125.879494 115.242143) (xy 125.880775 115.237085)
				(xy 125.882821 115.212401) (xy 125.89999 115.005189) (xy 125.90042 115) (xy 125.892185 114.900613)
				(xy 125.881206 114.768111) (xy 125.881205 114.768106) (xy 125.880775 114.762915) (xy 125.86748 114.710415)
				(xy 125.823654 114.537349) (xy 125.823652 114.537345) (xy 125.822374 114.532296) (xy 125.814851 114.515144)
				(xy 125.728906 114.31921) (xy 125.726812 114.314436) (xy 125.717381 114.3) (xy 125.599544 114.119638)
				(xy 125.599543 114.119637) (xy 125.596694 114.115276) (xy 125.566488 114.082463) (xy 125.505269 114.015962)
				(xy 125.43557 113.940249) (xy 125.427916 113.934291) (xy 125.251945 113.797328) (xy 125.247835 113.794129)
				(xy 125.046482 113.685162) (xy 125.043195 113.683383) (xy 125.043192 113.683382) (xy 125.03861 113.680902)
				(xy 124.813602 113.603656) (xy 124.808468 113.602799) (xy 124.808463 113.602798) (xy 124.584086 113.565357)
				(xy 124.584084 113.565357) (xy 124.578949 113.5645) (xy 124.3833 113.5645) (xy 124.315179 113.544498)
				(xy 124.268686 113.490842) (xy 124.2573 113.4385) (xy 124.2573 112.656675) (xy 124.277302 112.588554)
				(xy 124.319161 112.548221) (xy 124.322907 112.546006) (xy 124.432598 112.481135) (xy 124.546135 112.367598)
				(xy 124.613222 112.25416) (xy 124.665114 112.205708) (xy 124.721675 112.1923) (xy 128.846373 112.1923)
				(xy 128.858723 112.192907) (xy 128.905 112.197465) (xy 129.114227 112.176857) (xy 129.315415 112.115828)
				(xy 129.325148 112.110626) (xy 129.408122 112.066275) (xy 129.500829 112.016721) (xy 129.539987 111.984585)
				(xy 129.663347 111.883347) (xy 129.692851 111.847396) (xy 129.701156 111.838234) (xy 131.482629 110.056761)
				(xy 131.544941 110.022735) (xy 131.55989 110.0205) (xy 131.563949 110.0205) (xy 131.569083 110.019643)
				(xy 131.569087 110.019643) (xy 131.793463 109.982202) (xy 131.793468 109.982201) (xy 131.798602 109.981344)
				(xy 132.02361 109.904098) (xy 132.028192 109.901618) (xy 132.028195 109.901617) (xy 132.228252 109.793351)
				(xy 132.232835 109.790871) (xy 132.42057 109.644751) (xy 132.525677 109.530575) (xy 132.578165 109.473558)
				(xy 132.578167 109.473555) (xy 132.581694 109.469724) (xy 132.593936 109.450986) (xy 132.708963 109.274925)
				(xy 132.708964 109.274923) (xy 132.711812 109.270564) (xy 132.773825 109.129189) (xy 132.805281 109.057476)
				(xy 132.805282 109.057474) (xy 132.807374 109.052704) (xy 132.808954 109.046467) (xy 132.864494 108.827144)
				(xy 132.864494 108.827143) (xy 132.865775 108.822085) (xy 132.87295 108.7355) (xy 132.88499 108.590189)
				(xy 132.88542 108.585) (xy 132.874599 108.454407) (xy 132.866206 108.353111) (xy 132.866205 108.353106)
				(xy 132.865775 108.347915) (xy 132.859026 108.321262) (xy 132.808654 108.122349) (xy 132.808652 108.122345)
				(xy 132.807374 108.117296) (xy 132.711812 107.899436) (xy 132.664261 107.826653) (xy 132.584544 107.704638)
				(xy 132.584543 107.704637) (xy 132.581694 107.700276) (xy 132.527776 107.641705) (xy 132.436123 107.542144)
				(xy 132.42057 107.525249) (xy 132.414726 107.5207) (xy 132.236945 107.382328) (xy 132.232835 107.379129)
				(xy 132.077029 107.294811) (xy 132.028195 107.268383) (xy 132.028192 107.268382) (xy 132.02361 107.265902)
				(xy 131.798602 107.188656) (xy 131.793468 107.187799) (xy 131.793463 107.187798) (xy 131.569086 107.150357)
				(xy 131.569084 107.150357) (xy 131.563949 107.1495) (xy 131.326051 107.1495) (xy 131.320916 107.150357)
				(xy 131.320914 107.150357) (xy 131.096537 107.187798) (xy 131.096532 107.187799) (xy 131.091398 107.188656)
				(xy 130.86639 107.265902) (xy 130.861808 107.268382) (xy 130.861805 107.268383) (xy 130.812971 107.294811)
				(xy 130.657165 107.379129) (xy 130.653055 107.382328) (xy 130.475275 107.5207) (xy 130.46943 107.525249)
				(xy 130.453877 107.542144) (xy 130.362225 107.641705) (xy 130.308306 107.700276) (xy 130.305457 107.704637)
				(xy 130.305456 107.704638) (xy 130.22574 107.826653) (xy 130.178188 107.899436) (xy 130.082626 108.117296)
				(xy 130.081348 108.122345) (xy 130.081346 108.122349) (xy 130.030974 108.321262) (xy 130.024225 108.347915)
				(xy 130.023795 108.353106) (xy 130.023794 108.353111) (xy 130.014625 108.46377) (xy 129.989066 108.530006)
				(xy 129.97815 108.54246) (xy 128.499815 110.020795) (xy 128.437503 110.054821) (xy 128.41072 110.0577)
				(xy 124.721675 110.0577) (xy 124.653554 110.037698) (xy 124.613221 109.995839) (xy 124.602046 109.976942)
				(xy 124.546135 109.882402) (xy 124.432598 109.768865) (xy 124.294393 109.687131) (xy 124.286782 109.68492)
				(xy 124.28678 109.684919) (xy 124.146381 109.644129) (xy 124.146376 109.644128) (xy 124.140204 109.642335)
				(xy 124.118581 109.640633) (xy 124.106638 109.639693) (xy 124.106629 109.639693) (xy 124.104181 109.6395)
				(xy 123.997235 109.6395) (xy 123.929114 109.619498) (xy 123.882621 109.565842) (xy 123.872517 109.495568)
				(xy 123.902011 109.430988) (xy 123.912349 109.420385) (xy 124.005055 109.335872) (xy 124.012873 109.327296)
				(xy 124.129149 109.173321) (xy 124.135257 109.163457) (xy 124.221265 108.99073) (xy 124.225454 108.979918)
				(xy 124.261675 108.852616) (xy 124.261557 108.838513) (xy 124.254116 108.83519) (xy 122.131482 108.83519)
				(xy 122.117951 108.839163) (xy 122.116792 108.847227) (xy 122.154546 108.979918) (xy 122.158735 108.99073)
				(xy 122.244743 109.163457) (xy 122.250851 109.173321) (xy 122.367127 109.327296) (xy 122.374945 109.335872)
				(xy 122.467652 109.420386) (xy 122.504518 109.48106) (xy 122.502729 109.552034) (xy 122.462853 109.610774)
				(xy 122.397549 109.638631) (xy 122.382767 109.639501) (xy 122.27582 109.639501) (xy 122.257821 109.640917)
				(xy 122.246214 109.64183) (xy 122.246213 109.64183) (xy 122.239796 109.642335) (xy 122.233616 109.64413)
				(xy 122.233613 109.644131) (xy 122.09322 109.684919) (xy 122.093218 109.68492) (xy 122.085607 109.687131)
				(xy 121.947402 109.768865) (xy 121.833865 109.882402) (xy 121.829831 109.889223) (xy 121.774844 109.982202)
				(xy 121.752131 110.020607) (xy 121.74992 110.028218) (xy 121.749919 110.02822) (xy 121.709129 110.168619)
				(xy 121.709128 110.168624) (xy 121.707335 110.174796) (xy 121.7045 110.210819) (xy 120.65 110.210819)
				(xy 120.65 108.317384) (xy 122.118325 108.317384) (xy 122.118443 108.331487) (xy 122.125884 108.33481)
				(xy 122.921695 108.33481) (xy 122.936934 108.330335) (xy 122.938139 108.328945) (xy 122.93981 108.321262)
				(xy 122.93981 108.316695) (xy 123.44019 108.316695) (xy 123.444665 108.331934) (xy 123.446055 108.333139)
				(xy 123.453738 108.33481) (xy 124.248518 108.33481) (xy 124.262049 108.330837) (xy 124.263208 108.322773)
				(xy 124.225454 108.190082) (xy 124.221265 108.17927) (xy 124.135257 108.006543) (xy 124.129149 107.996679)
				(xy 124.012873 107.842704) (xy 124.005055 107.834128) (xy 123.862467 107.704141) (xy 123.853204 107.697146)
				(xy 123.689153 107.595571) (xy 123.678771 107.590402) (xy 123.498851 107.5207) (xy 123.487694 107.517525)
				(xy 123.457996 107.511973) (xy 123.44488 107.5133) (xy 123.44019 107.528115) (xy 123.44019 108.316695)
				(xy 122.93981 108.316695) (xy 122.93981 107.52676) (xy 122.936096 107.51411) (xy 122.920673 107.512222)
				(xy 122.892306 107.517525) (xy 122.881149 107.5207) (xy 122.701229 107.590402) (xy 122.690847 107.595571)
				(xy 122.526796 107.697146) (xy 122.517533 107.704141) (xy 122.374945 107.834128) (xy 122.367127 107.842704)
				(xy 122.250851 107.996679) (xy 122.244743 108.006543) (xy 122.158735 108.17927) (xy 122.154546 108.190082)
				(xy 122.118325 108.317384) (xy 120.65 108.317384) (xy 120.65 100.062384) (xy 122.118325 100.062384)
				(xy 122.118443 100.076487) (xy 122.125884 100.07981) (xy 122.921695 100.07981) (xy 122.936934 100.075335)
				(xy 122.938139 100.073945) (xy 122.93981 100.066262) (xy 122.93981 100.061695) (xy 123.44019 100.061695)
				(xy 123.444665 100.076934) (xy 123.446055 100.078139) (xy 123.453738 100.07981) (xy 124.248518 100.07981)
				(xy 124.262049 100.075837) (xy 124.263208 100.067773) (xy 124.227343 99.94172) (xy 164.177965 99.94172)
				(xy 164.219722 100.079377) (xy 164.224438 100.090761) (xy 164.316475 100.262948) (xy 164.323315 100.273185)
				(xy 164.447181 100.424118) (xy 164.455882 100.432819) (xy 164.606815 100.556685) (xy 164.617052 100.563525)
				(xy 164.789239 100.655562) (xy 164.800623 100.660278) (xy 164.932475 100.700275) (xy 164.946576 100.7004)
				(xy 164.94981 100.693483) (xy 164.94981 100.687418) (xy 165.45019 100.687418) (xy 165.454163 100.700949)
				(xy 165.46172 100.702035) (xy 165.599377 100.660278) (xy 165.610761 100.655562) (xy 165.782948 100.563525)
				(xy 165.793185 100.556685) (xy 165.944118 100.432819) (xy 165.952819 100.424118) (xy 166.076685 100.273185)
				(xy 166.083525 100.262948) (xy 166.175562 100.090761) (xy 166.180278 100.079377) (xy 166.220275 99.947525)
				(xy 166.2204 99.933424) (xy 166.213483 99.93019) (xy 165.468305 99.93019) (xy 165.453066 99.934665)
				(xy 165.451861 99.936055) (xy 165.45019 99.943738) (xy 165.45019 100.687418) (xy 164.94981 100.687418)
				(xy 164.94981 99.948305) (xy 164.945335 99.933066) (xy 164.943945 99.931861) (xy 164.936262 99.93019)
				(xy 164.192582 99.93019) (xy 164.179051 99.934163) (xy 164.177965 99.94172) (xy 124.227343 99.94172)
				(xy 124.225454 99.935082) (xy 124.221265 99.92427) (xy 124.135257 99.751543) (xy 124.129149 99.741679)
				(xy 124.012873 99.587704) (xy 124.005055 99.579128) (xy 123.862467 99.449141) (xy 123.853204 99.442146)
				(xy 123.828057 99.426576) (xy 164.1796 99.426576) (xy 164.186517 99.42981) (xy 164.931695 99.42981)
				(xy 164.946934 99.425335) (xy 164.948139 99.423945) (xy 164.94981 99.416262) (xy 164.94981 99.411695)
				(xy 165.45019 99.411695) (xy 165.454665 99.426934) (xy 165.456055 99.428139) (xy 165.463738 99.42981)
				(xy 166.207418 99.42981) (xy 166.220949 99.425837) (xy 166.222035 99.41828) (xy 166.180278 99.280623)
				(xy 166.175562 99.269239) (xy 166.083525 99.097052) (xy 166.076685 99.086815) (xy 165.952819 98.935882)
				(xy 165.944118 98.927181) (xy 165.793185 98.803315) (xy 165.782948 98.796475) (xy 165.610761 98.704438)
				(xy 165.599377 98.699722) (xy 165.467525 98.659725) (xy 165.453424 98.6596) (xy 165.45019 98.666517)
				(xy 165.45019 99.411695) (xy 164.94981 99.411695) (xy 164.94981 98.672582) (xy 164.945837 98.659051)
				(xy 164.93828 98.657965) (xy 164.800623 98.699722) (xy 164.789239 98.704438) (xy 164.617052 98.796475)
				(xy 164.606815 98.803315) (xy 164.455882 98.927181) (xy 164.447181 98.935882) (xy 164.323315 99.086815)
				(xy 164.316475 99.097052) (xy 164.224438 99.269239) (xy 164.219722 99.280623) (xy 164.179725 99.412475)
				(xy 164.1796 99.426576) (xy 123.828057 99.426576) (xy 123.689153 99.340571) (xy 123.678771 99.335402)
				(xy 123.498851 99.2657) (xy 123.487694 99.262525) (xy 123.457996 99.256973) (xy 123.44488 99.2583)
				(xy 123.44019 99.273115) (xy 123.44019 100.061695) (xy 122.93981 100.061695) (xy 122.93981 99.27176)
				(xy 122.936096 99.25911) (xy 122.920673 99.257222) (xy 122.892306 99.262525) (xy 122.881149 99.2657)
				(xy 122.701229 99.335402) (xy 122.690847 99.340571) (xy 122.526796 99.442146) (xy 122.517533 99.449141)
				(xy 122.374945 99.579128) (xy 122.367127 99.587704) (xy 122.250851 99.741679) (xy 122.244743 99.751543)
				(xy 122.158735 99.92427) (xy 122.154546 99.935082) (xy 122.118325 100.062384) (xy 120.65 100.062384)
				(xy 120.65 96.599015) (xy 120.670002 96.530894) (xy 120.723658 96.484401) (xy 120.793932 96.474297)
				(xy 120.862649 96.50754) (xy 120.962638 96.602254) (xy 121.25921 96.827702) (xy 121.262139 96.829465)
				(xy 121.262143 96.829467) (xy 121.349885 96.88226) (xy 121.578419 97.019764) (xy 121.916523 97.176187)
				(xy 121.919755 97.177276) (xy 121.919765 97.17728) (xy 122.26632 97.294048) (xy 122.266326 97.29405)
				(xy 122.269556 97.295138) (xy 122.272893 97.295872) (xy 122.272891 97.295872) (xy 122.630051 97.374489)
				(xy 122.630055 97.37449) (xy 122.633382 97.375222) (xy 122.636772 97.375591) (xy 122.636774 97.375591)
				(xy 122.70262 97.382752) (xy 123.003733 97.4155) (xy 123.376267 97.4155) (xy 123.67738 97.382752)
				(xy 123.743226 97.375591) (xy 123.743228 97.375591) (xy 123.746618 97.375222) (xy 123.749945 97.37449)
				(xy 123.749949 97.374489) (xy 124.107109 97.295872) (xy 124.107107 97.295872) (xy 124.110444 97.295138)
				(xy 124.113674 97.29405) (xy 124.11368 97.294048) (xy 124.460235 97.17728) (xy 124.460245 97.177276)
				(xy 124.463477 97.176187) (xy 124.801581 97.019764) (xy 125.030115 96.88226) (xy 125.117857 96.829467)
				(xy 125.117861 96.829465) (xy 125.12079 96.827702) (xy 125.417362 96.602254) (xy 125.429386 96.590864)
				(xy 132.268735 96.590864) (xy 132.276692 96.601704) (xy 132.297903 96.618214) (xy 132.306592 96.623891)
				(xy 132.509348 96.733617) (xy 132.518865 96.737791) (xy 132.73691 96.812646) (xy 132.74698 96.815197)
				(xy 132.974381 96.853143) (xy 132.984729 96.854) (xy 133.215271 96.854) (xy 133.225619 96.853143)
				(xy 133.45302 96.815197) (xy 133.46309 96.812646) (xy 133.681135 96.737791) (xy 133.690652 96.733617)
				(xy 133.893408 96.623891) (xy 133.902097 96.618214) (xy 133.922831 96.602076) (xy 133.931299 96.590308)
				(xy 133.924745 96.578568) (xy 133.112811 95.766633) (xy 133.098868 95.75902) (xy 133.097034 95.759151)
				(xy 133.09042 95.763402) (xy 132.275682 96.578141) (xy 132.268735 96.590864) (xy 125.429386 96.590864)
				(xy 125.687821 96.346062) (xy 125.928995 96.06213) (xy 125.930905 96.059312) (xy 125.930911 96.059305)
				(xy 126.136146 95.756606) (xy 126.136149 95.756602) (xy 126.138057 95.753787) (xy 126.312555 95.424648)
				(xy 126.320309 95.405189) (xy 131.641447 95.405189) (xy 131.660485 95.634945) (xy 131.662197 95.6452)
				(xy 131.718789 95.868678) (xy 131.722162 95.878503) (xy 131.814768 96.089623) (xy 131.819715 96.098765)
				(xy 131.899783 96.221318) (xy 131.91051 96.230473) (xy 131.920232 96.225945) (xy 132.733367 95.412811)
				(xy 132.739744 95.401132) (xy 133.45902 95.401132) (xy 133.459151 95.402966) (xy 133.463402 95.40958)
				(xy 134.2775 96.223677) (xy 134.289875 96.230434) (xy 134.29846 96.224008) (xy 134.380285 96.098765)
				(xy 134.385232 96.089623) (xy 134.477838 95.878503) (xy 134.481211 95.868678) (xy 134.537803 95.6452)
				(xy 134.539515 95.634945) (xy 134.558553 95.405189) (xy 134.558553 95.394811) (xy 134.539515 95.165055)
				(xy 134.537803 95.1548) (xy 134.481211 94.931322) (xy 134.477838 94.921497) (xy 134.385232 94.710377)
				(xy 134.380285 94.701235) (xy 134.300217 94.578682) (xy 134.28949 94.569527) (xy 134.279768 94.574055)
				(xy 133.466633 95.387189) (xy 133.45902 95.401132) (xy 132.739744 95.401132) (xy 132.74098 95.398868)
				(xy 132.740849 95.397034) (xy 132.736598 95.39042) (xy 131.9225 94.576323) (xy 131.910125 94.569566)
				(xy 131.90154 94.575992) (xy 131.819715 94.701235) (xy 131.814768 94.710377) (xy 131.722162 94.921497)
				(xy 131.718789 94.931322) (xy 131.662197 95.1548) (xy 131.660485 95.165055) (xy 131.641447 95.394811)
				(xy 131.641447 95.405189) (xy 126.320309 95.405189) (xy 126.323558 95.397034) (xy 126.449181 95.081741)
				(xy 126.450444 95.078572) (xy 126.460882 95.04098) (xy 126.549195 94.722904) (xy 126.550108 94.719616)
				(xy 126.610377 94.351988) (xy 126.618093 94.209692) (xy 132.268701 94.209692) (xy 132.275255 94.221432)
				(xy 133.087189 95.033367) (xy 133.101132 95.04098) (xy 133.102966 95.040849) (xy 133.10958 95.036598)
				(xy 133.924318 94.221859) (xy 133.931265 94.209136) (xy 133.923308 94.198296) (xy 133.902097 94.181786)
				(xy 133.893408 94.176109) (xy 133.690652 94.066383) (xy 133.681135 94.062209) (xy 133.46309 93.987354)
				(xy 133.45302 93.984803) (xy 133.225619 93.946857) (xy 133.215271 93.946) (xy 132.984729 93.946)
				(xy 132.974381 93.946857) (xy 132.74698 93.984803) (xy 132.73691 93.987354) (xy 132.518865 94.062209)
				(xy 132.509348 94.066383) (xy 132.306592 94.176109) (xy 132.297903 94.181786) (xy 132.277169 94.197924)
				(xy 132.268701 94.209692) (xy 126.618093 94.209692) (xy 126.618123 94.209136) (xy 126.630361 93.983412)
				(xy 126.630546 93.98) (xy 126.610377 93.608012) (xy 126.550108 93.240384) (xy 126.450444 92.881428)
				(xy 126.312555 92.535352) (xy 126.138057 92.206213) (xy 126.136146 92.203394) (xy 125.930911 91.900695)
				(xy 125.930905 91.900688) (xy 125.928995 91.89787) (xy 125.716389 91.647571) (xy 125.687533 91.582703)
				(xy 125.698328 91.512532) (xy 125.745347 91.459336) (xy 125.812421 91.44) (xy 162.477579 91.44)
			)
		)
	)
	(embedded_fonts no)
)
