<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<wxFormBuilder_Project>
    <FileVersion major="1" minor="15" />
    <object class="Project" expanded="1">
        <property name="class_decoration"></property>
        <property name="code_generation">C++</property>
        <property name="disconnect_events">1</property>
        <property name="disconnect_mode">source_name</property>
        <property name="disconnect_php_events">0</property>
        <property name="disconnect_python_events">0</property>
        <property name="embedded_files_path">res</property>
        <property name="encoding">UTF-8</property>
        <property name="event_generation">connect</property>
        <property name="file">dialog_HTML_reporter_base</property>
        <property name="first_id">2240</property>
        <property name="help_provider">none</property>
        <property name="indent_with_spaces"></property>
        <property name="internationalize">1</property>
        <property name="name">DialogHTMLReporterBase</property>
        <property name="namespace"></property>
        <property name="path">.</property>
        <property name="precompiled_header"></property>
        <property name="relative_path">1</property>
        <property name="skip_lua_events">1</property>
        <property name="skip_php_events">1</property>
        <property name="skip_python_events">1</property>
        <property name="ui_table">UI</property>
        <property name="use_enum">0</property>
        <property name="use_microsoft_bom">0</property>
        <object class="Dialog" expanded="1">
            <property name="aui_managed">0</property>
            <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
            <property name="bg"></property>
            <property name="center">wxBOTH</property>
            <property name="context_help"></property>
            <property name="context_menu">1</property>
            <property name="enabled">1</property>
            <property name="event_handler">impl_virtual</property>
            <property name="extra_style"></property>
            <property name="fg"></property>
            <property name="font"></property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="maximum_size"></property>
            <property name="minimum_size"></property>
            <property name="name">DIALOG_HTML_REPORTER</property>
            <property name="pos"></property>
            <property name="size"></property>
            <property name="style">wxDEFAULT_DIALOG_STYLE|wxRESIZE_BORDER</property>
            <property name="subclass">DIALOG_SHIM; dialog_shim.h; forward_declare</property>
            <property name="title">Report</property>
            <property name="tooltip"></property>
            <property name="window_extra_style"></property>
            <property name="window_name"></property>
            <property name="window_style"></property>
            <object class="wxBoxSizer" expanded="1">
                <property name="minimum_size"></property>
                <property name="name">bMainSizer</property>
                <property name="orient">wxVERTICAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="1">
                    <property name="border">10</property>
                    <property name="flag">wxEXPAND|wxTOP|wxRIGHT|wxLEFT</property>
                    <property name="proportion">1</property>
                    <object class="wxHtmlWindow" expanded="1">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size">640,360</property>
                        <property name="moveable">1</property>
                        <property name="name">m_Reporter</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">public</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style">wxHW_SCROLLBAR_AUTO</property>
                        <property name="subclass">WX_HTML_REPORT_BOX; wx_html_report_box.h; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style">wxBORDER_SIMPLE</property>
                        <event name="OnHtmlLinkClicked">OnErrorLinkClicked</event>
                    </object>
                </object>
                <object class="sizeritem" expanded="1">
                    <property name="border">5</property>
                    <property name="flag">wxEXPAND|wxALL</property>
                    <property name="proportion">0</property>
                    <object class="wxStdDialogButtonSizer" expanded="1">
                        <property name="Apply">0</property>
                        <property name="Cancel">0</property>
                        <property name="ContextHelp">0</property>
                        <property name="Help">0</property>
                        <property name="No">0</property>
                        <property name="OK">1</property>
                        <property name="Save">0</property>
                        <property name="Yes">0</property>
                        <property name="minimum_size"></property>
                        <property name="name">m_sdbSizer</property>
                        <property name="permission">protected</property>
                        <event name="OnOKButtonClick">OnOK</event>
                    </object>
                </object>
            </object>
        </object>
    </object>
</wxFormBuilder_Project>
