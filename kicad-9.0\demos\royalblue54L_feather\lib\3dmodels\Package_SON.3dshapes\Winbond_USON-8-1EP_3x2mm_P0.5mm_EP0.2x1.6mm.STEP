ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */
/* OPTION: using custom renumber hook */

FILE_DESCRIPTION(
/* description */ ('STEP AP242',
'CAx-IF Rec.Pracs.---Representation and Presentation of Product Manufa
cturing Information (PMI)---4.0---2014-10-13',
'CAx-IF Rec.Pracs.---3D Tessellated Geometry---0.4---2014-09-14','2;1'),

/* implementation_level */ '2;1');

FILE_NAME(
/* name */ '673c0be6a43bb33766d6bfa9',
/* time_stamp */ '2024-11-19T03:54:14Z',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v20',
/* originating_system */ 'ONSHAPE BY PTC INC, 1.189',
/* authorisation */ '  ');

FILE_SCHEMA (('AP242_MANAGED_MODEL_BASED_3D_ENGINEERING_MIM_LF { 1 0 10303 442 1 1 4 }'));
ENDSEC;

DATA;
#10=SHAPE_REPRESENTATION_RELATIONSHIP('','',#1011,#11);
#11=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#1009),#1571);
#12=CYLINDRICAL_SURFACE('',#1066,0.000125);
#13=CIRCLE('',#1054,0.000125);
#14=CIRCLE('',#1067,0.000125);
#15=OVER_RIDING_STYLED_ITEM('',(#966),#944,#964);
#16=OVER_RIDING_STYLED_ITEM('',(#967),#945,#964);
#17=OVER_RIDING_STYLED_ITEM('',(#968),#946,#964);
#18=OVER_RIDING_STYLED_ITEM('',(#969),#947,#964);
#19=OVER_RIDING_STYLED_ITEM('',(#970),#948,#964);
#20=OVER_RIDING_STYLED_ITEM('',(#971),#949,#964);
#21=ORIENTED_EDGE('',*,*,#271,.T.);
#22=ORIENTED_EDGE('',*,*,#272,.F.);
#23=ORIENTED_EDGE('',*,*,#273,.F.);
#24=ORIENTED_EDGE('',*,*,#274,.T.);
#25=ORIENTED_EDGE('',*,*,#275,.F.);
#26=ORIENTED_EDGE('',*,*,#274,.F.);
#27=ORIENTED_EDGE('',*,*,#276,.T.);
#28=ORIENTED_EDGE('',*,*,#277,.T.);
#29=ORIENTED_EDGE('',*,*,#278,.F.);
#30=ORIENTED_EDGE('',*,*,#277,.F.);
#31=ORIENTED_EDGE('',*,*,#279,.T.);
#32=ORIENTED_EDGE('',*,*,#280,.T.);
#33=ORIENTED_EDGE('',*,*,#281,.T.);
#34=ORIENTED_EDGE('',*,*,#280,.F.);
#35=ORIENTED_EDGE('',*,*,#282,.F.);
#36=ORIENTED_EDGE('',*,*,#272,.T.);
#37=ORIENTED_EDGE('',*,*,#273,.T.);
#38=ORIENTED_EDGE('',*,*,#282,.T.);
#39=ORIENTED_EDGE('',*,*,#279,.F.);
#40=ORIENTED_EDGE('',*,*,#276,.F.);
#41=ORIENTED_EDGE('',*,*,#283,.T.);
#42=ORIENTED_EDGE('',*,*,#284,.F.);
#43=ORIENTED_EDGE('',*,*,#285,.F.);
#44=ORIENTED_EDGE('',*,*,#286,.T.);
#45=ORIENTED_EDGE('',*,*,#287,.F.);
#46=ORIENTED_EDGE('',*,*,#286,.F.);
#47=ORIENTED_EDGE('',*,*,#288,.T.);
#48=ORIENTED_EDGE('',*,*,#289,.T.);
#49=ORIENTED_EDGE('',*,*,#290,.F.);
#50=ORIENTED_EDGE('',*,*,#289,.F.);
#51=ORIENTED_EDGE('',*,*,#291,.T.);
#52=ORIENTED_EDGE('',*,*,#292,.T.);
#53=ORIENTED_EDGE('',*,*,#293,.T.);
#54=ORIENTED_EDGE('',*,*,#292,.F.);
#55=ORIENTED_EDGE('',*,*,#294,.F.);
#56=ORIENTED_EDGE('',*,*,#284,.T.);
#57=ORIENTED_EDGE('',*,*,#285,.T.);
#58=ORIENTED_EDGE('',*,*,#294,.T.);
#59=ORIENTED_EDGE('',*,*,#291,.F.);
#60=ORIENTED_EDGE('',*,*,#288,.F.);
#61=ORIENTED_EDGE('',*,*,#295,.T.);
#62=ORIENTED_EDGE('',*,*,#296,.F.);
#63=ORIENTED_EDGE('',*,*,#297,.F.);
#64=ORIENTED_EDGE('',*,*,#298,.T.);
#65=ORIENTED_EDGE('',*,*,#299,.F.);
#66=ORIENTED_EDGE('',*,*,#298,.F.);
#67=ORIENTED_EDGE('',*,*,#300,.T.);
#68=ORIENTED_EDGE('',*,*,#301,.T.);
#69=ORIENTED_EDGE('',*,*,#302,.F.);
#70=ORIENTED_EDGE('',*,*,#301,.F.);
#71=ORIENTED_EDGE('',*,*,#303,.T.);
#72=ORIENTED_EDGE('',*,*,#304,.T.);
#73=ORIENTED_EDGE('',*,*,#305,.T.);
#74=ORIENTED_EDGE('',*,*,#304,.F.);
#75=ORIENTED_EDGE('',*,*,#306,.F.);
#76=ORIENTED_EDGE('',*,*,#296,.T.);
#77=ORIENTED_EDGE('',*,*,#297,.T.);
#78=ORIENTED_EDGE('',*,*,#306,.T.);
#79=ORIENTED_EDGE('',*,*,#303,.F.);
#80=ORIENTED_EDGE('',*,*,#300,.F.);
#81=ORIENTED_EDGE('',*,*,#307,.T.);
#82=ORIENTED_EDGE('',*,*,#308,.F.);
#83=ORIENTED_EDGE('',*,*,#309,.F.);
#84=ORIENTED_EDGE('',*,*,#310,.T.);
#85=ORIENTED_EDGE('',*,*,#311,.F.);
#86=ORIENTED_EDGE('',*,*,#310,.F.);
#87=ORIENTED_EDGE('',*,*,#312,.T.);
#88=ORIENTED_EDGE('',*,*,#313,.T.);
#89=ORIENTED_EDGE('',*,*,#314,.F.);
#90=ORIENTED_EDGE('',*,*,#313,.F.);
#91=ORIENTED_EDGE('',*,*,#315,.T.);
#92=ORIENTED_EDGE('',*,*,#316,.T.);
#93=ORIENTED_EDGE('',*,*,#317,.T.);
#94=ORIENTED_EDGE('',*,*,#316,.F.);
#95=ORIENTED_EDGE('',*,*,#318,.F.);
#96=ORIENTED_EDGE('',*,*,#308,.T.);
#97=ORIENTED_EDGE('',*,*,#309,.T.);
#98=ORIENTED_EDGE('',*,*,#318,.T.);
#99=ORIENTED_EDGE('',*,*,#315,.F.);
#100=ORIENTED_EDGE('',*,*,#312,.F.);
#101=ORIENTED_EDGE('',*,*,#319,.F.);
#102=ORIENTED_EDGE('',*,*,#320,.F.);
#103=ORIENTED_EDGE('',*,*,#321,.T.);
#104=ORIENTED_EDGE('',*,*,#322,.T.);
#105=ORIENTED_EDGE('',*,*,#323,.T.);
#106=ORIENTED_EDGE('',*,*,#324,.F.);
#107=ORIENTED_EDGE('',*,*,#325,.F.);
#108=ORIENTED_EDGE('',*,*,#320,.T.);
#109=ORIENTED_EDGE('',*,*,#326,.T.);
#110=ORIENTED_EDGE('',*,*,#327,.F.);
#111=ORIENTED_EDGE('',*,*,#328,.F.);
#112=ORIENTED_EDGE('',*,*,#324,.T.);
#113=ORIENTED_EDGE('',*,*,#329,.F.);
#114=ORIENTED_EDGE('',*,*,#322,.F.);
#115=ORIENTED_EDGE('',*,*,#330,.T.);
#116=ORIENTED_EDGE('',*,*,#327,.T.);
#117=ORIENTED_EDGE('',*,*,#321,.F.);
#118=ORIENTED_EDGE('',*,*,#325,.T.);
#119=ORIENTED_EDGE('',*,*,#328,.T.);
#120=ORIENTED_EDGE('',*,*,#330,.F.);
#121=ORIENTED_EDGE('',*,*,#331,.F.);
#122=ORIENTED_EDGE('',*,*,#332,.F.);
#123=ORIENTED_EDGE('',*,*,#333,.T.);
#124=ORIENTED_EDGE('',*,*,#334,.T.);
#125=ORIENTED_EDGE('',*,*,#335,.T.);
#126=ORIENTED_EDGE('',*,*,#336,.F.);
#127=ORIENTED_EDGE('',*,*,#337,.F.);
#128=ORIENTED_EDGE('',*,*,#332,.T.);
#129=ORIENTED_EDGE('',*,*,#338,.T.);
#130=ORIENTED_EDGE('',*,*,#339,.F.);
#131=ORIENTED_EDGE('',*,*,#340,.F.);
#132=ORIENTED_EDGE('',*,*,#336,.T.);
#133=ORIENTED_EDGE('',*,*,#341,.F.);
#134=ORIENTED_EDGE('',*,*,#334,.F.);
#135=ORIENTED_EDGE('',*,*,#342,.T.);
#136=ORIENTED_EDGE('',*,*,#339,.T.);
#137=ORIENTED_EDGE('',*,*,#333,.F.);
#138=ORIENTED_EDGE('',*,*,#337,.T.);
#139=ORIENTED_EDGE('',*,*,#340,.T.);
#140=ORIENTED_EDGE('',*,*,#342,.F.);
#141=ORIENTED_EDGE('',*,*,#343,.F.);
#142=ORIENTED_EDGE('',*,*,#344,.F.);
#143=ORIENTED_EDGE('',*,*,#345,.T.);
#144=ORIENTED_EDGE('',*,*,#346,.T.);
#145=ORIENTED_EDGE('',*,*,#347,.T.);
#146=ORIENTED_EDGE('',*,*,#348,.F.);
#147=ORIENTED_EDGE('',*,*,#349,.F.);
#148=ORIENTED_EDGE('',*,*,#344,.T.);
#149=ORIENTED_EDGE('',*,*,#350,.T.);
#150=ORIENTED_EDGE('',*,*,#351,.F.);
#151=ORIENTED_EDGE('',*,*,#352,.F.);
#152=ORIENTED_EDGE('',*,*,#348,.T.);
#153=ORIENTED_EDGE('',*,*,#353,.F.);
#154=ORIENTED_EDGE('',*,*,#346,.F.);
#155=ORIENTED_EDGE('',*,*,#354,.T.);
#156=ORIENTED_EDGE('',*,*,#351,.T.);
#157=ORIENTED_EDGE('',*,*,#345,.F.);
#158=ORIENTED_EDGE('',*,*,#349,.T.);
#159=ORIENTED_EDGE('',*,*,#352,.T.);
#160=ORIENTED_EDGE('',*,*,#354,.F.);
#161=ORIENTED_EDGE('',*,*,#355,.F.);
#162=ORIENTED_EDGE('',*,*,#356,.F.);
#163=ORIENTED_EDGE('',*,*,#357,.T.);
#164=ORIENTED_EDGE('',*,*,#358,.T.);
#165=ORIENTED_EDGE('',*,*,#356,.T.);
#166=ORIENTED_EDGE('',*,*,#359,.T.);
#167=ORIENTED_EDGE('',*,*,#360,.F.);
#168=ORIENTED_EDGE('',*,*,#361,.F.);
#169=ORIENTED_EDGE('',*,*,#360,.T.);
#170=ORIENTED_EDGE('',*,*,#362,.T.);
#171=ORIENTED_EDGE('',*,*,#363,.F.);
#172=ORIENTED_EDGE('',*,*,#364,.F.);
#173=ORIENTED_EDGE('',*,*,#365,.F.);
#174=ORIENTED_EDGE('',*,*,#358,.F.);
#175=ORIENTED_EDGE('',*,*,#366,.T.);
#176=ORIENTED_EDGE('',*,*,#363,.T.);
#177=ORIENTED_EDGE('',*,*,#367,.F.);
#178=ORIENTED_EDGE('',*,*,#368,.T.);
#179=ORIENTED_EDGE('',*,*,#369,.T.);
#180=ORIENTED_EDGE('',*,*,#370,.T.);
#181=ORIENTED_EDGE('',*,*,#371,.F.);
#182=ORIENTED_EDGE('',*,*,#311,.T.);
#183=ORIENTED_EDGE('',*,*,#314,.T.);
#184=ORIENTED_EDGE('',*,*,#317,.F.);
#185=ORIENTED_EDGE('',*,*,#307,.F.);
#186=ORIENTED_EDGE('',*,*,#299,.T.);
#187=ORIENTED_EDGE('',*,*,#302,.T.);
#188=ORIENTED_EDGE('',*,*,#305,.F.);
#189=ORIENTED_EDGE('',*,*,#295,.F.);
#190=ORIENTED_EDGE('',*,*,#287,.T.);
#191=ORIENTED_EDGE('',*,*,#290,.T.);
#192=ORIENTED_EDGE('',*,*,#293,.F.);
#193=ORIENTED_EDGE('',*,*,#283,.F.);
#194=ORIENTED_EDGE('',*,*,#275,.T.);
#195=ORIENTED_EDGE('',*,*,#278,.T.);
#196=ORIENTED_EDGE('',*,*,#281,.F.);
#197=ORIENTED_EDGE('',*,*,#271,.F.);
#198=ORIENTED_EDGE('',*,*,#323,.F.);
#199=ORIENTED_EDGE('',*,*,#319,.T.);
#200=ORIENTED_EDGE('',*,*,#329,.T.);
#201=ORIENTED_EDGE('',*,*,#326,.F.);
#202=ORIENTED_EDGE('',*,*,#335,.F.);
#203=ORIENTED_EDGE('',*,*,#331,.T.);
#204=ORIENTED_EDGE('',*,*,#341,.T.);
#205=ORIENTED_EDGE('',*,*,#338,.F.);
#206=ORIENTED_EDGE('',*,*,#347,.F.);
#207=ORIENTED_EDGE('',*,*,#343,.T.);
#208=ORIENTED_EDGE('',*,*,#353,.T.);
#209=ORIENTED_EDGE('',*,*,#350,.F.);
#210=ORIENTED_EDGE('',*,*,#372,.F.);
#211=ORIENTED_EDGE('',*,*,#373,.T.);
#212=ORIENTED_EDGE('',*,*,#374,.T.);
#213=ORIENTED_EDGE('',*,*,#375,.F.);
#214=ORIENTED_EDGE('',*,*,#355,.T.);
#215=ORIENTED_EDGE('',*,*,#365,.T.);
#216=ORIENTED_EDGE('',*,*,#362,.F.);
#217=ORIENTED_EDGE('',*,*,#359,.F.);
#218=ORIENTED_EDGE('',*,*,#376,.F.);
#219=ORIENTED_EDGE('',*,*,#357,.F.);
#220=ORIENTED_EDGE('',*,*,#361,.T.);
#221=ORIENTED_EDGE('',*,*,#364,.T.);
#222=ORIENTED_EDGE('',*,*,#366,.F.);
#223=ORIENTED_EDGE('',*,*,#373,.F.);
#224=ORIENTED_EDGE('',*,*,#377,.F.);
#225=ORIENTED_EDGE('',*,*,#378,.T.);
#226=ORIENTED_EDGE('',*,*,#379,.T.);
#227=ORIENTED_EDGE('',*,*,#372,.T.);
#228=ORIENTED_EDGE('',*,*,#380,.F.);
#229=ORIENTED_EDGE('',*,*,#381,.F.);
#230=ORIENTED_EDGE('',*,*,#377,.T.);
#231=ORIENTED_EDGE('',*,*,#375,.T.);
#232=ORIENTED_EDGE('',*,*,#382,.F.);
#233=ORIENTED_EDGE('',*,*,#383,.F.);
#234=ORIENTED_EDGE('',*,*,#380,.T.);
#235=ORIENTED_EDGE('',*,*,#374,.F.);
#236=ORIENTED_EDGE('',*,*,#379,.F.);
#237=ORIENTED_EDGE('',*,*,#384,.T.);
#238=ORIENTED_EDGE('',*,*,#382,.T.);
#239=ORIENTED_EDGE('',*,*,#378,.F.);
#240=ORIENTED_EDGE('',*,*,#381,.T.);
#241=ORIENTED_EDGE('',*,*,#383,.T.);
#242=ORIENTED_EDGE('',*,*,#384,.F.);
#243=ORIENTED_EDGE('',*,*,#368,.F.);
#244=ORIENTED_EDGE('',*,*,#385,.F.);
#245=ORIENTED_EDGE('',*,*,#386,.T.);
#246=ORIENTED_EDGE('',*,*,#387,.T.);
#247=ORIENTED_EDGE('',*,*,#367,.T.);
#248=ORIENTED_EDGE('',*,*,#388,.F.);
#249=ORIENTED_EDGE('',*,*,#389,.F.);
#250=ORIENTED_EDGE('',*,*,#385,.T.);
#251=ORIENTED_EDGE('',*,*,#371,.T.);
#252=ORIENTED_EDGE('',*,*,#390,.T.);
#253=ORIENTED_EDGE('',*,*,#391,.F.);
#254=ORIENTED_EDGE('',*,*,#388,.T.);
#255=ORIENTED_EDGE('',*,*,#392,.F.);
#256=ORIENTED_EDGE('',*,*,#369,.F.);
#257=ORIENTED_EDGE('',*,*,#387,.F.);
#258=ORIENTED_EDGE('',*,*,#393,.T.);
#259=ORIENTED_EDGE('',*,*,#394,.F.);
#260=ORIENTED_EDGE('',*,*,#393,.F.);
#261=ORIENTED_EDGE('',*,*,#386,.F.);
#262=ORIENTED_EDGE('',*,*,#389,.T.);
#263=ORIENTED_EDGE('',*,*,#391,.T.);
#264=ORIENTED_EDGE('',*,*,#392,.T.);
#265=ORIENTED_EDGE('',*,*,#394,.T.);
#266=ORIENTED_EDGE('',*,*,#390,.F.);
#267=ORIENTED_EDGE('',*,*,#370,.F.);
#268=ORIENTED_EDGE('',*,*,#395,.F.);
#269=ORIENTED_EDGE('',*,*,#376,.T.);
#270=ORIENTED_EDGE('',*,*,#395,.T.);
#271=EDGE_CURVE('',#396,#397,#480,.T.);
#272=EDGE_CURVE('',#398,#397,#481,.T.);
#273=EDGE_CURVE('',#399,#398,#482,.T.);
#274=EDGE_CURVE('',#399,#396,#483,.T.);
#275=EDGE_CURVE('',#396,#400,#484,.T.);
#276=EDGE_CURVE('',#399,#401,#485,.T.);
#277=EDGE_CURVE('',#401,#400,#486,.T.);
#278=EDGE_CURVE('',#400,#402,#487,.T.);
#279=EDGE_CURVE('',#401,#403,#488,.T.);
#280=EDGE_CURVE('',#403,#402,#489,.T.);
#281=EDGE_CURVE('',#397,#402,#490,.T.);
#282=EDGE_CURVE('',#398,#403,#491,.T.);
#283=EDGE_CURVE('',#404,#405,#492,.T.);
#284=EDGE_CURVE('',#406,#405,#493,.T.);
#285=EDGE_CURVE('',#407,#406,#494,.T.);
#286=EDGE_CURVE('',#407,#404,#495,.T.);
#287=EDGE_CURVE('',#404,#408,#496,.T.);
#288=EDGE_CURVE('',#407,#409,#497,.T.);
#289=EDGE_CURVE('',#409,#408,#498,.T.);
#290=EDGE_CURVE('',#408,#410,#499,.T.);
#291=EDGE_CURVE('',#409,#411,#500,.T.);
#292=EDGE_CURVE('',#411,#410,#501,.T.);
#293=EDGE_CURVE('',#405,#410,#502,.T.);
#294=EDGE_CURVE('',#406,#411,#503,.T.);
#295=EDGE_CURVE('',#412,#413,#504,.T.);
#296=EDGE_CURVE('',#414,#413,#505,.T.);
#297=EDGE_CURVE('',#415,#414,#506,.T.);
#298=EDGE_CURVE('',#415,#412,#507,.T.);
#299=EDGE_CURVE('',#412,#416,#508,.T.);
#300=EDGE_CURVE('',#415,#417,#509,.T.);
#301=EDGE_CURVE('',#417,#416,#510,.T.);
#302=EDGE_CURVE('',#416,#418,#511,.T.);
#303=EDGE_CURVE('',#417,#419,#512,.T.);
#304=EDGE_CURVE('',#419,#418,#513,.T.);
#305=EDGE_CURVE('',#413,#418,#514,.T.);
#306=EDGE_CURVE('',#414,#419,#515,.T.);
#307=EDGE_CURVE('',#420,#421,#516,.T.);
#308=EDGE_CURVE('',#422,#421,#517,.T.);
#309=EDGE_CURVE('',#423,#422,#518,.T.);
#310=EDGE_CURVE('',#423,#420,#519,.T.);
#311=EDGE_CURVE('',#420,#424,#520,.T.);
#312=EDGE_CURVE('',#423,#425,#521,.T.);
#313=EDGE_CURVE('',#425,#424,#522,.T.);
#314=EDGE_CURVE('',#424,#426,#523,.T.);
#315=EDGE_CURVE('',#425,#427,#524,.T.);
#316=EDGE_CURVE('',#427,#426,#525,.T.);
#317=EDGE_CURVE('',#421,#426,#526,.T.);
#318=EDGE_CURVE('',#422,#427,#527,.T.);
#319=EDGE_CURVE('',#428,#429,#528,.T.);
#320=EDGE_CURVE('',#430,#428,#529,.T.);
#321=EDGE_CURVE('',#430,#431,#530,.T.);
#322=EDGE_CURVE('',#431,#429,#531,.T.);
#323=EDGE_CURVE('',#428,#432,#532,.T.);
#324=EDGE_CURVE('',#433,#432,#533,.T.);
#325=EDGE_CURVE('',#430,#433,#534,.T.);
#326=EDGE_CURVE('',#432,#434,#535,.T.);
#327=EDGE_CURVE('',#435,#434,#536,.T.);
#328=EDGE_CURVE('',#433,#435,#537,.T.);
#329=EDGE_CURVE('',#429,#434,#538,.T.);
#330=EDGE_CURVE('',#431,#435,#539,.T.);
#331=EDGE_CURVE('',#436,#437,#540,.T.);
#332=EDGE_CURVE('',#438,#436,#541,.T.);
#333=EDGE_CURVE('',#438,#439,#542,.T.);
#334=EDGE_CURVE('',#439,#437,#543,.T.);
#335=EDGE_CURVE('',#436,#440,#544,.T.);
#336=EDGE_CURVE('',#441,#440,#545,.T.);
#337=EDGE_CURVE('',#438,#441,#546,.T.);
#338=EDGE_CURVE('',#440,#442,#547,.T.);
#339=EDGE_CURVE('',#443,#442,#548,.T.);
#340=EDGE_CURVE('',#441,#443,#549,.T.);
#341=EDGE_CURVE('',#437,#442,#550,.T.);
#342=EDGE_CURVE('',#439,#443,#551,.T.);
#343=EDGE_CURVE('',#444,#445,#552,.T.);
#344=EDGE_CURVE('',#446,#444,#553,.T.);
#345=EDGE_CURVE('',#446,#447,#554,.T.);
#346=EDGE_CURVE('',#447,#445,#555,.T.);
#347=EDGE_CURVE('',#444,#448,#556,.T.);
#348=EDGE_CURVE('',#449,#448,#557,.T.);
#349=EDGE_CURVE('',#446,#449,#558,.T.);
#350=EDGE_CURVE('',#448,#450,#559,.T.);
#351=EDGE_CURVE('',#451,#450,#560,.T.);
#352=EDGE_CURVE('',#449,#451,#561,.T.);
#353=EDGE_CURVE('',#445,#450,#562,.T.);
#354=EDGE_CURVE('',#447,#451,#563,.T.);
#355=EDGE_CURVE('',#452,#453,#564,.T.);
#356=EDGE_CURVE('',#454,#452,#565,.T.);
#357=EDGE_CURVE('',#454,#455,#566,.T.);
#358=EDGE_CURVE('',#455,#453,#567,.T.);
#359=EDGE_CURVE('',#452,#456,#568,.T.);
#360=EDGE_CURVE('',#457,#456,#569,.T.);
#361=EDGE_CURVE('',#454,#457,#570,.T.);
#362=EDGE_CURVE('',#456,#458,#571,.T.);
#363=EDGE_CURVE('',#459,#458,#572,.T.);
#364=EDGE_CURVE('',#457,#459,#573,.T.);
#365=EDGE_CURVE('',#453,#458,#574,.T.);
#366=EDGE_CURVE('',#455,#459,#575,.T.);
#367=EDGE_CURVE('',#460,#461,#576,.T.);
#368=EDGE_CURVE('',#460,#462,#577,.T.);
#369=EDGE_CURVE('',#462,#463,#578,.T.);
#370=EDGE_CURVE('',#463,#464,#579,.T.);
#371=EDGE_CURVE('',#461,#464,#580,.T.);
#372=EDGE_CURVE('',#465,#466,#581,.T.);
#373=EDGE_CURVE('',#465,#467,#582,.T.);
#374=EDGE_CURVE('',#467,#468,#583,.T.);
#375=EDGE_CURVE('',#466,#468,#584,.T.);
#376=EDGE_CURVE('',#469,#469,#13,.T.);
#377=EDGE_CURVE('',#470,#465,#585,.T.);
#378=EDGE_CURVE('',#470,#471,#586,.T.);
#379=EDGE_CURVE('',#471,#467,#587,.T.);
#380=EDGE_CURVE('',#472,#466,#588,.T.);
#381=EDGE_CURVE('',#470,#472,#589,.T.);
#382=EDGE_CURVE('',#473,#468,#590,.T.);
#383=EDGE_CURVE('',#472,#473,#591,.T.);
#384=EDGE_CURVE('',#471,#473,#592,.T.);
#385=EDGE_CURVE('',#474,#460,#593,.T.);
#386=EDGE_CURVE('',#474,#475,#594,.T.);
#387=EDGE_CURVE('',#475,#462,#595,.T.);
#388=EDGE_CURVE('',#476,#461,#596,.T.);
#389=EDGE_CURVE('',#474,#476,#597,.T.);
#390=EDGE_CURVE('',#464,#477,#598,.T.);
#391=EDGE_CURVE('',#476,#477,#599,.T.);
#392=EDGE_CURVE('',#463,#478,#600,.T.);
#393=EDGE_CURVE('',#475,#478,#601,.T.);
#394=EDGE_CURVE('',#478,#477,#602,.T.);
#395=EDGE_CURVE('',#479,#479,#14,.T.);
#396=VERTEX_POINT('',#1309);
#397=VERTEX_POINT('',#1310);
#398=VERTEX_POINT('',#1312);
#399=VERTEX_POINT('',#1314);
#400=VERTEX_POINT('',#1318);
#401=VERTEX_POINT('',#1320);
#402=VERTEX_POINT('',#1324);
#403=VERTEX_POINT('',#1326);
#404=VERTEX_POINT('',#1334);
#405=VERTEX_POINT('',#1335);
#406=VERTEX_POINT('',#1337);
#407=VERTEX_POINT('',#1339);
#408=VERTEX_POINT('',#1343);
#409=VERTEX_POINT('',#1345);
#410=VERTEX_POINT('',#1349);
#411=VERTEX_POINT('',#1351);
#412=VERTEX_POINT('',#1359);
#413=VERTEX_POINT('',#1360);
#414=VERTEX_POINT('',#1362);
#415=VERTEX_POINT('',#1364);
#416=VERTEX_POINT('',#1368);
#417=VERTEX_POINT('',#1370);
#418=VERTEX_POINT('',#1374);
#419=VERTEX_POINT('',#1376);
#420=VERTEX_POINT('',#1384);
#421=VERTEX_POINT('',#1385);
#422=VERTEX_POINT('',#1387);
#423=VERTEX_POINT('',#1389);
#424=VERTEX_POINT('',#1393);
#425=VERTEX_POINT('',#1395);
#426=VERTEX_POINT('',#1399);
#427=VERTEX_POINT('',#1401);
#428=VERTEX_POINT('',#1409);
#429=VERTEX_POINT('',#1410);
#430=VERTEX_POINT('',#1412);
#431=VERTEX_POINT('',#1414);
#432=VERTEX_POINT('',#1418);
#433=VERTEX_POINT('',#1420);
#434=VERTEX_POINT('',#1424);
#435=VERTEX_POINT('',#1426);
#436=VERTEX_POINT('',#1434);
#437=VERTEX_POINT('',#1435);
#438=VERTEX_POINT('',#1437);
#439=VERTEX_POINT('',#1439);
#440=VERTEX_POINT('',#1443);
#441=VERTEX_POINT('',#1445);
#442=VERTEX_POINT('',#1449);
#443=VERTEX_POINT('',#1451);
#444=VERTEX_POINT('',#1459);
#445=VERTEX_POINT('',#1460);
#446=VERTEX_POINT('',#1462);
#447=VERTEX_POINT('',#1464);
#448=VERTEX_POINT('',#1468);
#449=VERTEX_POINT('',#1470);
#450=VERTEX_POINT('',#1474);
#451=VERTEX_POINT('',#1476);
#452=VERTEX_POINT('',#1484);
#453=VERTEX_POINT('',#1485);
#454=VERTEX_POINT('',#1487);
#455=VERTEX_POINT('',#1489);
#456=VERTEX_POINT('',#1493);
#457=VERTEX_POINT('',#1495);
#458=VERTEX_POINT('',#1499);
#459=VERTEX_POINT('',#1501);
#460=VERTEX_POINT('',#1508);
#461=VERTEX_POINT('',#1509);
#462=VERTEX_POINT('',#1511);
#463=VERTEX_POINT('',#1513);
#464=VERTEX_POINT('',#1515);
#465=VERTEX_POINT('',#1518);
#466=VERTEX_POINT('',#1519);
#467=VERTEX_POINT('',#1521);
#468=VERTEX_POINT('',#1523);
#469=VERTEX_POINT('',#1527);
#470=VERTEX_POINT('',#1530);
#471=VERTEX_POINT('',#1532);
#472=VERTEX_POINT('',#1536);
#473=VERTEX_POINT('',#1540);
#474=VERTEX_POINT('',#1547);
#475=VERTEX_POINT('',#1549);
#476=VERTEX_POINT('',#1553);
#477=VERTEX_POINT('',#1557);
#478=VERTEX_POINT('',#1561);
#479=VERTEX_POINT('',#1568);
#480=LINE('',#1308,#603);
#481=LINE('',#1311,#604);
#482=LINE('',#1313,#605);
#483=LINE('',#1315,#606);
#484=LINE('',#1317,#607);
#485=LINE('',#1319,#608);
#486=LINE('',#1321,#609);
#487=LINE('',#1323,#610);
#488=LINE('',#1325,#611);
#489=LINE('',#1327,#612);
#490=LINE('',#1329,#613);
#491=LINE('',#1330,#614);
#492=LINE('',#1333,#615);
#493=LINE('',#1336,#616);
#494=LINE('',#1338,#617);
#495=LINE('',#1340,#618);
#496=LINE('',#1342,#619);
#497=LINE('',#1344,#620);
#498=LINE('',#1346,#621);
#499=LINE('',#1348,#622);
#500=LINE('',#1350,#623);
#501=LINE('',#1352,#624);
#502=LINE('',#1354,#625);
#503=LINE('',#1355,#626);
#504=LINE('',#1358,#627);
#505=LINE('',#1361,#628);
#506=LINE('',#1363,#629);
#507=LINE('',#1365,#630);
#508=LINE('',#1367,#631);
#509=LINE('',#1369,#632);
#510=LINE('',#1371,#633);
#511=LINE('',#1373,#634);
#512=LINE('',#1375,#635);
#513=LINE('',#1377,#636);
#514=LINE('',#1379,#637);
#515=LINE('',#1380,#638);
#516=LINE('',#1383,#639);
#517=LINE('',#1386,#640);
#518=LINE('',#1388,#641);
#519=LINE('',#1390,#642);
#520=LINE('',#1392,#643);
#521=LINE('',#1394,#644);
#522=LINE('',#1396,#645);
#523=LINE('',#1398,#646);
#524=LINE('',#1400,#647);
#525=LINE('',#1402,#648);
#526=LINE('',#1404,#649);
#527=LINE('',#1405,#650);
#528=LINE('',#1408,#651);
#529=LINE('',#1411,#652);
#530=LINE('',#1413,#653);
#531=LINE('',#1415,#654);
#532=LINE('',#1417,#655);
#533=LINE('',#1419,#656);
#534=LINE('',#1421,#657);
#535=LINE('',#1423,#658);
#536=LINE('',#1425,#659);
#537=LINE('',#1427,#660);
#538=LINE('',#1429,#661);
#539=LINE('',#1430,#662);
#540=LINE('',#1433,#663);
#541=LINE('',#1436,#664);
#542=LINE('',#1438,#665);
#543=LINE('',#1440,#666);
#544=LINE('',#1442,#667);
#545=LINE('',#1444,#668);
#546=LINE('',#1446,#669);
#547=LINE('',#1448,#670);
#548=LINE('',#1450,#671);
#549=LINE('',#1452,#672);
#550=LINE('',#1454,#673);
#551=LINE('',#1455,#674);
#552=LINE('',#1458,#675);
#553=LINE('',#1461,#676);
#554=LINE('',#1463,#677);
#555=LINE('',#1465,#678);
#556=LINE('',#1467,#679);
#557=LINE('',#1469,#680);
#558=LINE('',#1471,#681);
#559=LINE('',#1473,#682);
#560=LINE('',#1475,#683);
#561=LINE('',#1477,#684);
#562=LINE('',#1479,#685);
#563=LINE('',#1480,#686);
#564=LINE('',#1483,#687);
#565=LINE('',#1486,#688);
#566=LINE('',#1488,#689);
#567=LINE('',#1490,#690);
#568=LINE('',#1492,#691);
#569=LINE('',#1494,#692);
#570=LINE('',#1496,#693);
#571=LINE('',#1498,#694);
#572=LINE('',#1500,#695);
#573=LINE('',#1502,#696);
#574=LINE('',#1504,#697);
#575=LINE('',#1505,#698);
#576=LINE('',#1507,#699);
#577=LINE('',#1510,#700);
#578=LINE('',#1512,#701);
#579=LINE('',#1514,#702);
#580=LINE('',#1516,#703);
#581=LINE('',#1517,#704);
#582=LINE('',#1520,#705);
#583=LINE('',#1522,#706);
#584=LINE('',#1524,#707);
#585=LINE('',#1529,#708);
#586=LINE('',#1531,#709);
#587=LINE('',#1533,#710);
#588=LINE('',#1535,#711);
#589=LINE('',#1537,#712);
#590=LINE('',#1539,#713);
#591=LINE('',#1541,#714);
#592=LINE('',#1543,#715);
#593=LINE('',#1546,#716);
#594=LINE('',#1548,#717);
#595=LINE('',#1550,#718);
#596=LINE('',#1552,#719);
#597=LINE('',#1554,#720);
#598=LINE('',#1556,#721);
#599=LINE('',#1558,#722);
#600=LINE('',#1560,#723);
#601=LINE('',#1562,#724);
#602=LINE('',#1564,#725);
#603=VECTOR('',#1073,1.);
#604=VECTOR('',#1074,1.);
#605=VECTOR('',#1075,1.);
#606=VECTOR('',#1076,1.);
#607=VECTOR('',#1079,1.);
#608=VECTOR('',#1080,1.);
#609=VECTOR('',#1081,1.);
#610=VECTOR('',#1084,1.);
#611=VECTOR('',#1085,1.);
#612=VECTOR('',#1086,1.);
#613=VECTOR('',#1089,1.);
#614=VECTOR('',#1090,1.);
#615=VECTOR('',#1095,1.);
#616=VECTOR('',#1096,1.);
#617=VECTOR('',#1097,1.);
#618=VECTOR('',#1098,1.);
#619=VECTOR('',#1101,1.);
#620=VECTOR('',#1102,1.);
#621=VECTOR('',#1103,1.);
#622=VECTOR('',#1106,1.);
#623=VECTOR('',#1107,1.);
#624=VECTOR('',#1108,1.);
#625=VECTOR('',#1111,1.);
#626=VECTOR('',#1112,1.);
#627=VECTOR('',#1117,1.);
#628=VECTOR('',#1118,1.);
#629=VECTOR('',#1119,1.);
#630=VECTOR('',#1120,1.);
#631=VECTOR('',#1123,1.);
#632=VECTOR('',#1124,1.);
#633=VECTOR('',#1125,1.);
#634=VECTOR('',#1128,1.);
#635=VECTOR('',#1129,1.);
#636=VECTOR('',#1130,1.);
#637=VECTOR('',#1133,1.);
#638=VECTOR('',#1134,1.);
#639=VECTOR('',#1139,1.);
#640=VECTOR('',#1140,1.);
#641=VECTOR('',#1141,1.);
#642=VECTOR('',#1142,1.);
#643=VECTOR('',#1145,1.);
#644=VECTOR('',#1146,1.);
#645=VECTOR('',#1147,1.);
#646=VECTOR('',#1150,1.);
#647=VECTOR('',#1151,1.);
#648=VECTOR('',#1152,1.);
#649=VECTOR('',#1155,1.);
#650=VECTOR('',#1156,1.);
#651=VECTOR('',#1161,1.);
#652=VECTOR('',#1162,1.);
#653=VECTOR('',#1163,1.);
#654=VECTOR('',#1164,1.);
#655=VECTOR('',#1167,1.);
#656=VECTOR('',#1168,1.);
#657=VECTOR('',#1169,1.);
#658=VECTOR('',#1172,1.);
#659=VECTOR('',#1173,1.);
#660=VECTOR('',#1174,1.);
#661=VECTOR('',#1177,1.);
#662=VECTOR('',#1178,1.);
#663=VECTOR('',#1183,1.);
#664=VECTOR('',#1184,1.);
#665=VECTOR('',#1185,1.);
#666=VECTOR('',#1186,1.);
#667=VECTOR('',#1189,1.);
#668=VECTOR('',#1190,1.);
#669=VECTOR('',#1191,1.);
#670=VECTOR('',#1194,1.);
#671=VECTOR('',#1195,1.);
#672=VECTOR('',#1196,1.);
#673=VECTOR('',#1199,1.);
#674=VECTOR('',#1200,1.);
#675=VECTOR('',#1205,1.);
#676=VECTOR('',#1206,1.);
#677=VECTOR('',#1207,1.);
#678=VECTOR('',#1208,1.);
#679=VECTOR('',#1211,1.);
#680=VECTOR('',#1212,1.);
#681=VECTOR('',#1213,1.);
#682=VECTOR('',#1216,1.);
#683=VECTOR('',#1217,1.);
#684=VECTOR('',#1218,1.);
#685=VECTOR('',#1221,1.);
#686=VECTOR('',#1222,1.);
#687=VECTOR('',#1227,1.);
#688=VECTOR('',#1228,1.);
#689=VECTOR('',#1229,1.);
#690=VECTOR('',#1230,1.);
#691=VECTOR('',#1233,1.);
#692=VECTOR('',#1234,1.);
#693=VECTOR('',#1235,1.);
#694=VECTOR('',#1238,1.);
#695=VECTOR('',#1239,1.);
#696=VECTOR('',#1240,1.);
#697=VECTOR('',#1243,1.);
#698=VECTOR('',#1244,1.);
#699=VECTOR('',#1247,1.);
#700=VECTOR('',#1248,1.);
#701=VECTOR('',#1249,1.);
#702=VECTOR('',#1250,1.);
#703=VECTOR('',#1251,1.);
#704=VECTOR('',#1252,1.);
#705=VECTOR('',#1253,1.);
#706=VECTOR('',#1254,1.);
#707=VECTOR('',#1255,1.);
#708=VECTOR('',#1262,1.);
#709=VECTOR('',#1263,1.);
#710=VECTOR('',#1264,1.);
#711=VECTOR('',#1267,1.);
#712=VECTOR('',#1268,1.);
#713=VECTOR('',#1271,1.);
#714=VECTOR('',#1272,1.);
#715=VECTOR('',#1275,1.);
#716=VECTOR('',#1280,1.);
#717=VECTOR('',#1281,1.);
#718=VECTOR('',#1282,1.);
#719=VECTOR('',#1285,1.);
#720=VECTOR('',#1286,1.);
#721=VECTOR('',#1289,1.);
#722=VECTOR('',#1290,1.);
#723=VECTOR('',#1293,1.);
#724=VECTOR('',#1294,1.);
#725=VECTOR('',#1297,1.);
#726=EDGE_LOOP('',(#21,#22,#23,#24));
#727=EDGE_LOOP('',(#25,#26,#27,#28));
#728=EDGE_LOOP('',(#29,#30,#31,#32));
#729=EDGE_LOOP('',(#33,#34,#35,#36));
#730=EDGE_LOOP('',(#37,#38,#39,#40));
#731=EDGE_LOOP('',(#41,#42,#43,#44));
#732=EDGE_LOOP('',(#45,#46,#47,#48));
#733=EDGE_LOOP('',(#49,#50,#51,#52));
#734=EDGE_LOOP('',(#53,#54,#55,#56));
#735=EDGE_LOOP('',(#57,#58,#59,#60));
#736=EDGE_LOOP('',(#61,#62,#63,#64));
#737=EDGE_LOOP('',(#65,#66,#67,#68));
#738=EDGE_LOOP('',(#69,#70,#71,#72));
#739=EDGE_LOOP('',(#73,#74,#75,#76));
#740=EDGE_LOOP('',(#77,#78,#79,#80));
#741=EDGE_LOOP('',(#81,#82,#83,#84));
#742=EDGE_LOOP('',(#85,#86,#87,#88));
#743=EDGE_LOOP('',(#89,#90,#91,#92));
#744=EDGE_LOOP('',(#93,#94,#95,#96));
#745=EDGE_LOOP('',(#97,#98,#99,#100));
#746=EDGE_LOOP('',(#101,#102,#103,#104));
#747=EDGE_LOOP('',(#105,#106,#107,#108));
#748=EDGE_LOOP('',(#109,#110,#111,#112));
#749=EDGE_LOOP('',(#113,#114,#115,#116));
#750=EDGE_LOOP('',(#117,#118,#119,#120));
#751=EDGE_LOOP('',(#121,#122,#123,#124));
#752=EDGE_LOOP('',(#125,#126,#127,#128));
#753=EDGE_LOOP('',(#129,#130,#131,#132));
#754=EDGE_LOOP('',(#133,#134,#135,#136));
#755=EDGE_LOOP('',(#137,#138,#139,#140));
#756=EDGE_LOOP('',(#141,#142,#143,#144));
#757=EDGE_LOOP('',(#145,#146,#147,#148));
#758=EDGE_LOOP('',(#149,#150,#151,#152));
#759=EDGE_LOOP('',(#153,#154,#155,#156));
#760=EDGE_LOOP('',(#157,#158,#159,#160));
#761=EDGE_LOOP('',(#161,#162,#163,#164));
#762=EDGE_LOOP('',(#165,#166,#167,#168));
#763=EDGE_LOOP('',(#169,#170,#171,#172));
#764=EDGE_LOOP('',(#173,#174,#175,#176));
#765=EDGE_LOOP('',(#177,#178,#179,#180,#181));
#766=EDGE_LOOP('',(#182,#183,#184,#185));
#767=EDGE_LOOP('',(#186,#187,#188,#189));
#768=EDGE_LOOP('',(#190,#191,#192,#193));
#769=EDGE_LOOP('',(#194,#195,#196,#197));
#770=EDGE_LOOP('',(#198,#199,#200,#201));
#771=EDGE_LOOP('',(#202,#203,#204,#205));
#772=EDGE_LOOP('',(#206,#207,#208,#209));
#773=EDGE_LOOP('',(#210,#211,#212,#213));
#774=EDGE_LOOP('',(#214,#215,#216,#217));
#775=EDGE_LOOP('',(#218));
#776=EDGE_LOOP('',(#219,#220,#221,#222));
#777=EDGE_LOOP('',(#223,#224,#225,#226));
#778=EDGE_LOOP('',(#227,#228,#229,#230));
#779=EDGE_LOOP('',(#231,#232,#233,#234));
#780=EDGE_LOOP('',(#235,#236,#237,#238));
#781=EDGE_LOOP('',(#239,#240,#241,#242));
#782=EDGE_LOOP('',(#243,#244,#245,#246));
#783=EDGE_LOOP('',(#247,#248,#249,#250));
#784=EDGE_LOOP('',(#251,#252,#253,#254));
#785=EDGE_LOOP('',(#255,#256,#257,#258));
#786=EDGE_LOOP('',(#259,#260,#261,#262,#263));
#787=EDGE_LOOP('',(#264,#265,#266,#267));
#788=EDGE_LOOP('',(#268));
#789=EDGE_LOOP('',(#269));
#790=EDGE_LOOP('',(#270));
#791=FACE_BOUND('',#726,.T.);
#792=FACE_BOUND('',#727,.T.);
#793=FACE_BOUND('',#728,.T.);
#794=FACE_BOUND('',#729,.T.);
#795=FACE_BOUND('',#730,.T.);
#796=FACE_BOUND('',#731,.T.);
#797=FACE_BOUND('',#732,.T.);
#798=FACE_BOUND('',#733,.T.);
#799=FACE_BOUND('',#734,.T.);
#800=FACE_BOUND('',#735,.T.);
#801=FACE_BOUND('',#736,.T.);
#802=FACE_BOUND('',#737,.T.);
#803=FACE_BOUND('',#738,.T.);
#804=FACE_BOUND('',#739,.T.);
#805=FACE_BOUND('',#740,.T.);
#806=FACE_BOUND('',#741,.T.);
#807=FACE_BOUND('',#742,.T.);
#808=FACE_BOUND('',#743,.T.);
#809=FACE_BOUND('',#744,.T.);
#810=FACE_BOUND('',#745,.T.);
#811=FACE_BOUND('',#746,.T.);
#812=FACE_BOUND('',#747,.T.);
#813=FACE_BOUND('',#748,.T.);
#814=FACE_BOUND('',#749,.T.);
#815=FACE_BOUND('',#750,.T.);
#816=FACE_BOUND('',#751,.T.);
#817=FACE_BOUND('',#752,.T.);
#818=FACE_BOUND('',#753,.T.);
#819=FACE_BOUND('',#754,.T.);
#820=FACE_BOUND('',#755,.T.);
#821=FACE_BOUND('',#756,.T.);
#822=FACE_BOUND('',#757,.T.);
#823=FACE_BOUND('',#758,.T.);
#824=FACE_BOUND('',#759,.T.);
#825=FACE_BOUND('',#760,.T.);
#826=FACE_BOUND('',#761,.T.);
#827=FACE_BOUND('',#762,.T.);
#828=FACE_BOUND('',#763,.T.);
#829=FACE_BOUND('',#764,.T.);
#830=FACE_BOUND('',#765,.T.);
#831=FACE_BOUND('',#766,.T.);
#832=FACE_BOUND('',#767,.T.);
#833=FACE_BOUND('',#768,.T.);
#834=FACE_BOUND('',#769,.T.);
#835=FACE_BOUND('',#770,.T.);
#836=FACE_BOUND('',#771,.T.);
#837=FACE_BOUND('',#772,.T.);
#838=FACE_BOUND('',#773,.T.);
#839=FACE_BOUND('',#774,.T.);
#840=FACE_BOUND('',#775,.T.);
#841=FACE_BOUND('',#776,.T.);
#842=FACE_BOUND('',#777,.T.);
#843=FACE_BOUND('',#778,.T.);
#844=FACE_BOUND('',#779,.T.);
#845=FACE_BOUND('',#780,.T.);
#846=FACE_BOUND('',#781,.T.);
#847=FACE_BOUND('',#782,.T.);
#848=FACE_BOUND('',#783,.T.);
#849=FACE_BOUND('',#784,.T.);
#850=FACE_BOUND('',#785,.T.);
#851=FACE_BOUND('',#786,.T.);
#852=FACE_BOUND('',#787,.T.);
#853=FACE_BOUND('',#788,.T.);
#854=FACE_BOUND('',#789,.T.);
#855=FACE_BOUND('',#790,.T.);
#856=PLANE('',#1013);
#857=PLANE('',#1014);
#858=PLANE('',#1015);
#859=PLANE('',#1016);
#860=PLANE('',#1017);
#861=PLANE('',#1018);
#862=PLANE('',#1019);
#863=PLANE('',#1020);
#864=PLANE('',#1021);
#865=PLANE('',#1022);
#866=PLANE('',#1023);
#867=PLANE('',#1024);
#868=PLANE('',#1025);
#869=PLANE('',#1026);
#870=PLANE('',#1027);
#871=PLANE('',#1028);
#872=PLANE('',#1029);
#873=PLANE('',#1030);
#874=PLANE('',#1031);
#875=PLANE('',#1032);
#876=PLANE('',#1033);
#877=PLANE('',#1034);
#878=PLANE('',#1035);
#879=PLANE('',#1036);
#880=PLANE('',#1037);
#881=PLANE('',#1038);
#882=PLANE('',#1039);
#883=PLANE('',#1040);
#884=PLANE('',#1041);
#885=PLANE('',#1042);
#886=PLANE('',#1043);
#887=PLANE('',#1044);
#888=PLANE('',#1045);
#889=PLANE('',#1046);
#890=PLANE('',#1047);
#891=PLANE('',#1048);
#892=PLANE('',#1049);
#893=PLANE('',#1050);
#894=PLANE('',#1051);
#895=PLANE('',#1052);
#896=PLANE('',#1053);
#897=PLANE('',#1055);
#898=PLANE('',#1056);
#899=PLANE('',#1057);
#900=PLANE('',#1058);
#901=PLANE('',#1059);
#902=PLANE('',#1060);
#903=PLANE('',#1061);
#904=PLANE('',#1062);
#905=PLANE('',#1063);
#906=PLANE('',#1064);
#907=PLANE('',#1065);
#908=PLANE('',#1068);
#909=ADVANCED_FACE('',(#791),#856,.T.);
#910=ADVANCED_FACE('',(#792),#857,.F.);
#911=ADVANCED_FACE('',(#793),#858,.F.);
#912=ADVANCED_FACE('',(#794),#859,.T.);
#913=ADVANCED_FACE('',(#795),#860,.T.);
#914=ADVANCED_FACE('',(#796),#861,.T.);
#915=ADVANCED_FACE('',(#797),#862,.F.);
#916=ADVANCED_FACE('',(#798),#863,.F.);
#917=ADVANCED_FACE('',(#799),#864,.T.);
#918=ADVANCED_FACE('',(#800),#865,.T.);
#919=ADVANCED_FACE('',(#801),#866,.T.);
#920=ADVANCED_FACE('',(#802),#867,.F.);
#921=ADVANCED_FACE('',(#803),#868,.F.);
#922=ADVANCED_FACE('',(#804),#869,.T.);
#923=ADVANCED_FACE('',(#805),#870,.T.);
#924=ADVANCED_FACE('',(#806),#871,.T.);
#925=ADVANCED_FACE('',(#807),#872,.F.);
#926=ADVANCED_FACE('',(#808),#873,.F.);
#927=ADVANCED_FACE('',(#809),#874,.T.);
#928=ADVANCED_FACE('',(#810),#875,.T.);
#929=ADVANCED_FACE('',(#811),#876,.T.);
#930=ADVANCED_FACE('',(#812),#877,.F.);
#931=ADVANCED_FACE('',(#813),#878,.F.);
#932=ADVANCED_FACE('',(#814),#879,.T.);
#933=ADVANCED_FACE('',(#815),#880,.T.);
#934=ADVANCED_FACE('',(#816),#881,.T.);
#935=ADVANCED_FACE('',(#817),#882,.F.);
#936=ADVANCED_FACE('',(#818),#883,.F.);
#937=ADVANCED_FACE('',(#819),#884,.T.);
#938=ADVANCED_FACE('',(#820),#885,.T.);
#939=ADVANCED_FACE('',(#821),#886,.T.);
#940=ADVANCED_FACE('',(#822),#887,.F.);
#941=ADVANCED_FACE('',(#823),#888,.F.);
#942=ADVANCED_FACE('',(#824),#889,.T.);
#943=ADVANCED_FACE('',(#825),#890,.T.);
#944=ADVANCED_FACE('',(#826),#891,.T.);
#945=ADVANCED_FACE('',(#827),#892,.F.);
#946=ADVANCED_FACE('',(#828),#893,.F.);
#947=ADVANCED_FACE('',(#829),#894,.T.);
#948=ADVANCED_FACE('',(#830,#831,#832,#833,#834,#835,#836,#837,#838,#839),
#895,.F.);
#949=ADVANCED_FACE('',(#840,#841),#896,.T.);
#950=ADVANCED_FACE('',(#842),#897,.T.);
#951=ADVANCED_FACE('',(#843),#898,.F.);
#952=ADVANCED_FACE('',(#844),#899,.F.);
#953=ADVANCED_FACE('',(#845),#900,.T.);
#954=ADVANCED_FACE('',(#846),#901,.T.);
#955=ADVANCED_FACE('',(#847),#902,.T.);
#956=ADVANCED_FACE('',(#848),#903,.F.);
#957=ADVANCED_FACE('',(#849),#904,.F.);
#958=ADVANCED_FACE('',(#850),#905,.T.);
#959=ADVANCED_FACE('',(#851),#906,.T.);
#960=ADVANCED_FACE('',(#852),#907,.F.);
#961=ADVANCED_FACE('',(#853,#854),#12,.F.);
#962=ADVANCED_FACE('',(#855),#908,.T.);
#963=CLOSED_SHELL('',(#909,#910,#911,#912,#913,#914,#915,#916,#917,#918,
#919,#920,#921,#922,#923,#924,#925,#926,#927,#928,#929,#930,#931,#932,#933,
#934,#935,#936,#937,#938,#939,#940,#941,#942,#943,#944,#945,#946,#947,#948,
#949,#950,#951,#952,#953,#954,#955,#956,#957,#958,#959,#960,#961,#962));
#964=STYLED_ITEM('',(#965),#1009);
#965=PRESENTATION_STYLE_ASSIGNMENT((#972));
#966=PRESENTATION_STYLE_ASSIGNMENT((#973));
#967=PRESENTATION_STYLE_ASSIGNMENT((#974));
#968=PRESENTATION_STYLE_ASSIGNMENT((#975));
#969=PRESENTATION_STYLE_ASSIGNMENT((#976));
#970=PRESENTATION_STYLE_ASSIGNMENT((#977));
#971=PRESENTATION_STYLE_ASSIGNMENT((#978));
#972=SURFACE_STYLE_USAGE(.BOTH.,#979);
#973=SURFACE_STYLE_USAGE(.BOTH.,#980);
#974=SURFACE_STYLE_USAGE(.BOTH.,#981);
#975=SURFACE_STYLE_USAGE(.BOTH.,#982);
#976=SURFACE_STYLE_USAGE(.BOTH.,#983);
#977=SURFACE_STYLE_USAGE(.BOTH.,#984);
#978=SURFACE_STYLE_USAGE(.BOTH.,#985);
#979=SURFACE_SIDE_STYLE('',(#986));
#980=SURFACE_SIDE_STYLE('',(#987));
#981=SURFACE_SIDE_STYLE('',(#988));
#982=SURFACE_SIDE_STYLE('',(#989));
#983=SURFACE_SIDE_STYLE('',(#990));
#984=SURFACE_SIDE_STYLE('',(#991));
#985=SURFACE_SIDE_STYLE('',(#992));
#986=SURFACE_STYLE_FILL_AREA(#993);
#987=SURFACE_STYLE_FILL_AREA(#994);
#988=SURFACE_STYLE_FILL_AREA(#995);
#989=SURFACE_STYLE_FILL_AREA(#996);
#990=SURFACE_STYLE_FILL_AREA(#997);
#991=SURFACE_STYLE_FILL_AREA(#998);
#992=SURFACE_STYLE_FILL_AREA(#999);
#993=FILL_AREA_STYLE('',(#1000));
#994=FILL_AREA_STYLE('',(#1001));
#995=FILL_AREA_STYLE('',(#1002));
#996=FILL_AREA_STYLE('',(#1003));
#997=FILL_AREA_STYLE('',(#1004));
#998=FILL_AREA_STYLE('',(#1005));
#999=FILL_AREA_STYLE('',(#1006));
#1000=FILL_AREA_STYLE_COLOUR('',#1007);
#1001=FILL_AREA_STYLE_COLOUR('',#1008);
#1002=FILL_AREA_STYLE_COLOUR('',#1008);
#1003=FILL_AREA_STYLE_COLOUR('',#1008);
#1004=FILL_AREA_STYLE_COLOUR('',#1008);
#1005=FILL_AREA_STYLE_COLOUR('',#1008);
#1006=FILL_AREA_STYLE_COLOUR('',#1008);
#1007=COLOUR_RGB('',0.823529411764706,0.819607843137255,0.780392156862745);
#1008=COLOUR_RGB('',0.149019607843137,0.145098039215686,0.145098039215686);
#1009=MANIFOLD_SOLID_BREP('Part 1',#963);
#1010=SHAPE_DEFINITION_REPRESENTATION(#1576,#1011);
#1011=SHAPE_REPRESENTATION('Part 1',(#1012),#1571);
#1012=AXIS2_PLACEMENT_3D('',#1306,#1069,#1070);
#1013=AXIS2_PLACEMENT_3D('',#1307,#1071,#1072);
#1014=AXIS2_PLACEMENT_3D('',#1316,#1077,#1078);
#1015=AXIS2_PLACEMENT_3D('',#1322,#1082,#1083);
#1016=AXIS2_PLACEMENT_3D('',#1328,#1087,#1088);
#1017=AXIS2_PLACEMENT_3D('',#1331,#1091,#1092);
#1018=AXIS2_PLACEMENT_3D('',#1332,#1093,#1094);
#1019=AXIS2_PLACEMENT_3D('',#1341,#1099,#1100);
#1020=AXIS2_PLACEMENT_3D('',#1347,#1104,#1105);
#1021=AXIS2_PLACEMENT_3D('',#1353,#1109,#1110);
#1022=AXIS2_PLACEMENT_3D('',#1356,#1113,#1114);
#1023=AXIS2_PLACEMENT_3D('',#1357,#1115,#1116);
#1024=AXIS2_PLACEMENT_3D('',#1366,#1121,#1122);
#1025=AXIS2_PLACEMENT_3D('',#1372,#1126,#1127);
#1026=AXIS2_PLACEMENT_3D('',#1378,#1131,#1132);
#1027=AXIS2_PLACEMENT_3D('',#1381,#1135,#1136);
#1028=AXIS2_PLACEMENT_3D('',#1382,#1137,#1138);
#1029=AXIS2_PLACEMENT_3D('',#1391,#1143,#1144);
#1030=AXIS2_PLACEMENT_3D('',#1397,#1148,#1149);
#1031=AXIS2_PLACEMENT_3D('',#1403,#1153,#1154);
#1032=AXIS2_PLACEMENT_3D('',#1406,#1157,#1158);
#1033=AXIS2_PLACEMENT_3D('',#1407,#1159,#1160);
#1034=AXIS2_PLACEMENT_3D('',#1416,#1165,#1166);
#1035=AXIS2_PLACEMENT_3D('',#1422,#1170,#1171);
#1036=AXIS2_PLACEMENT_3D('',#1428,#1175,#1176);
#1037=AXIS2_PLACEMENT_3D('',#1431,#1179,#1180);
#1038=AXIS2_PLACEMENT_3D('',#1432,#1181,#1182);
#1039=AXIS2_PLACEMENT_3D('',#1441,#1187,#1188);
#1040=AXIS2_PLACEMENT_3D('',#1447,#1192,#1193);
#1041=AXIS2_PLACEMENT_3D('',#1453,#1197,#1198);
#1042=AXIS2_PLACEMENT_3D('',#1456,#1201,#1202);
#1043=AXIS2_PLACEMENT_3D('',#1457,#1203,#1204);
#1044=AXIS2_PLACEMENT_3D('',#1466,#1209,#1210);
#1045=AXIS2_PLACEMENT_3D('',#1472,#1214,#1215);
#1046=AXIS2_PLACEMENT_3D('',#1478,#1219,#1220);
#1047=AXIS2_PLACEMENT_3D('',#1481,#1223,#1224);
#1048=AXIS2_PLACEMENT_3D('',#1482,#1225,#1226);
#1049=AXIS2_PLACEMENT_3D('',#1491,#1231,#1232);
#1050=AXIS2_PLACEMENT_3D('',#1497,#1236,#1237);
#1051=AXIS2_PLACEMENT_3D('',#1503,#1241,#1242);
#1052=AXIS2_PLACEMENT_3D('',#1506,#1245,#1246);
#1053=AXIS2_PLACEMENT_3D('',#1525,#1256,#1257);
#1054=AXIS2_PLACEMENT_3D('',#1526,#1258,#1259);
#1055=AXIS2_PLACEMENT_3D('',#1528,#1260,#1261);
#1056=AXIS2_PLACEMENT_3D('',#1534,#1265,#1266);
#1057=AXIS2_PLACEMENT_3D('',#1538,#1269,#1270);
#1058=AXIS2_PLACEMENT_3D('',#1542,#1273,#1274);
#1059=AXIS2_PLACEMENT_3D('',#1544,#1276,#1277);
#1060=AXIS2_PLACEMENT_3D('',#1545,#1278,#1279);
#1061=AXIS2_PLACEMENT_3D('',#1551,#1283,#1284);
#1062=AXIS2_PLACEMENT_3D('',#1555,#1287,#1288);
#1063=AXIS2_PLACEMENT_3D('',#1559,#1291,#1292);
#1064=AXIS2_PLACEMENT_3D('',#1563,#1295,#1296);
#1065=AXIS2_PLACEMENT_3D('',#1565,#1298,#1299);
#1066=AXIS2_PLACEMENT_3D('',#1566,#1300,#1301);
#1067=AXIS2_PLACEMENT_3D('',#1567,#1302,#1303);
#1068=AXIS2_PLACEMENT_3D('',#1569,#1304,#1305);
#1069=DIRECTION('',(0.,0.,1.));
#1070=DIRECTION('',(1.,0.,0.));
#1071=DIRECTION('',(0.,1.,0.));
#1072=DIRECTION('',(0.,0.,1.));
#1073=DIRECTION('',(1.,0.,0.));
#1074=DIRECTION('',(0.,0.,1.));
#1075=DIRECTION('',(1.,0.,0.));
#1076=DIRECTION('',(0.,0.,1.));
#1077=DIRECTION('',(1.,0.,0.));
#1078=DIRECTION('',(0.,0.,1.));
#1079=DIRECTION('',(0.,-1.,0.));
#1080=DIRECTION('',(0.,-1.,0.));
#1081=DIRECTION('',(0.,0.,1.));
#1082=DIRECTION('',(0.,1.,0.));
#1083=DIRECTION('',(0.,0.,1.));
#1084=DIRECTION('',(1.,0.,0.));
#1085=DIRECTION('',(1.,0.,0.));
#1086=DIRECTION('',(0.,0.,1.));
#1087=DIRECTION('',(1.,0.,0.));
#1088=DIRECTION('',(0.,0.,1.));
#1089=DIRECTION('',(0.,-1.,0.));
#1090=DIRECTION('',(0.,-1.,0.));
#1091=DIRECTION('',(0.,0.,-1.));
#1092=DIRECTION('',(-1.,0.,0.));
#1093=DIRECTION('',(0.,1.,0.));
#1094=DIRECTION('',(0.,0.,1.));
#1095=DIRECTION('',(1.,0.,0.));
#1096=DIRECTION('',(0.,0.,1.));
#1097=DIRECTION('',(1.,0.,0.));
#1098=DIRECTION('',(0.,0.,1.));
#1099=DIRECTION('',(1.,0.,0.));
#1100=DIRECTION('',(0.,0.,1.));
#1101=DIRECTION('',(0.,-1.,0.));
#1102=DIRECTION('',(0.,-1.,0.));
#1103=DIRECTION('',(0.,0.,1.));
#1104=DIRECTION('',(0.,1.,0.));
#1105=DIRECTION('',(0.,0.,1.));
#1106=DIRECTION('',(1.,0.,0.));
#1107=DIRECTION('',(1.,0.,0.));
#1108=DIRECTION('',(0.,0.,1.));
#1109=DIRECTION('',(1.,0.,0.));
#1110=DIRECTION('',(0.,0.,1.));
#1111=DIRECTION('',(0.,-1.,0.));
#1112=DIRECTION('',(0.,-1.,0.));
#1113=DIRECTION('',(0.,0.,-1.));
#1114=DIRECTION('',(-1.,0.,0.));
#1115=DIRECTION('',(0.,1.,0.));
#1116=DIRECTION('',(0.,0.,1.));
#1117=DIRECTION('',(1.,0.,0.));
#1118=DIRECTION('',(0.,0.,1.));
#1119=DIRECTION('',(1.,0.,0.));
#1120=DIRECTION('',(0.,0.,1.));
#1121=DIRECTION('',(1.,0.,0.));
#1122=DIRECTION('',(0.,0.,1.));
#1123=DIRECTION('',(0.,-1.,0.));
#1124=DIRECTION('',(0.,-1.,0.));
#1125=DIRECTION('',(0.,0.,1.));
#1126=DIRECTION('',(0.,1.,0.));
#1127=DIRECTION('',(0.,0.,1.));
#1128=DIRECTION('',(1.,0.,0.));
#1129=DIRECTION('',(1.,0.,0.));
#1130=DIRECTION('',(0.,0.,1.));
#1131=DIRECTION('',(1.,0.,0.));
#1132=DIRECTION('',(0.,0.,1.));
#1133=DIRECTION('',(0.,-1.,0.));
#1134=DIRECTION('',(0.,-1.,0.));
#1135=DIRECTION('',(0.,0.,-1.));
#1136=DIRECTION('',(-1.,0.,0.));
#1137=DIRECTION('',(0.,1.,0.));
#1138=DIRECTION('',(0.,0.,1.));
#1139=DIRECTION('',(1.,0.,0.));
#1140=DIRECTION('',(0.,0.,1.));
#1141=DIRECTION('',(1.,0.,0.));
#1142=DIRECTION('',(0.,0.,1.));
#1143=DIRECTION('',(1.,0.,0.));
#1144=DIRECTION('',(0.,0.,1.));
#1145=DIRECTION('',(0.,-1.,0.));
#1146=DIRECTION('',(0.,-1.,0.));
#1147=DIRECTION('',(0.,0.,1.));
#1148=DIRECTION('',(0.,1.,0.));
#1149=DIRECTION('',(0.,0.,1.));
#1150=DIRECTION('',(1.,0.,0.));
#1151=DIRECTION('',(1.,0.,0.));
#1152=DIRECTION('',(0.,0.,1.));
#1153=DIRECTION('',(1.,0.,0.));
#1154=DIRECTION('',(0.,0.,1.));
#1155=DIRECTION('',(0.,-1.,0.));
#1156=DIRECTION('',(0.,-1.,0.));
#1157=DIRECTION('',(0.,0.,-1.));
#1158=DIRECTION('',(-1.,0.,0.));
#1159=DIRECTION('',(0.,1.,0.));
#1160=DIRECTION('',(0.,0.,1.));
#1161=DIRECTION('',(-1.,0.,0.));
#1162=DIRECTION('',(0.,0.,1.));
#1163=DIRECTION('',(-1.,0.,0.));
#1164=DIRECTION('',(0.,0.,1.));
#1165=DIRECTION('',(-1.,0.,0.));
#1166=DIRECTION('',(0.,0.,1.));
#1167=DIRECTION('',(0.,-1.,0.));
#1168=DIRECTION('',(0.,0.,1.));
#1169=DIRECTION('',(0.,-1.,0.));
#1170=DIRECTION('',(0.,1.,0.));
#1171=DIRECTION('',(0.,0.,1.));
#1172=DIRECTION('',(-1.,0.,0.));
#1173=DIRECTION('',(0.,0.,1.));
#1174=DIRECTION('',(-1.,0.,0.));
#1175=DIRECTION('',(-1.,0.,0.));
#1176=DIRECTION('',(0.,0.,1.));
#1177=DIRECTION('',(0.,-1.,0.));
#1178=DIRECTION('',(0.,-1.,0.));
#1179=DIRECTION('',(0.,0.,-1.));
#1180=DIRECTION('',(1.,0.,0.));
#1181=DIRECTION('',(0.,1.,0.));
#1182=DIRECTION('',(0.,0.,1.));
#1183=DIRECTION('',(-1.,0.,0.));
#1184=DIRECTION('',(0.,0.,1.));
#1185=DIRECTION('',(-1.,0.,0.));
#1186=DIRECTION('',(0.,0.,1.));
#1187=DIRECTION('',(-1.,0.,0.));
#1188=DIRECTION('',(0.,0.,1.));
#1189=DIRECTION('',(0.,-1.,0.));
#1190=DIRECTION('',(0.,0.,1.));
#1191=DIRECTION('',(0.,-1.,0.));
#1192=DIRECTION('',(0.,1.,0.));
#1193=DIRECTION('',(0.,0.,1.));
#1194=DIRECTION('',(-1.,0.,0.));
#1195=DIRECTION('',(0.,0.,1.));
#1196=DIRECTION('',(-1.,0.,0.));
#1197=DIRECTION('',(-1.,0.,0.));
#1198=DIRECTION('',(0.,0.,1.));
#1199=DIRECTION('',(0.,-1.,0.));
#1200=DIRECTION('',(0.,-1.,0.));
#1201=DIRECTION('',(0.,0.,-1.));
#1202=DIRECTION('',(1.,0.,0.));
#1203=DIRECTION('',(0.,1.,0.));
#1204=DIRECTION('',(0.,0.,1.));
#1205=DIRECTION('',(-1.,0.,0.));
#1206=DIRECTION('',(0.,0.,1.));
#1207=DIRECTION('',(-1.,0.,0.));
#1208=DIRECTION('',(0.,0.,1.));
#1209=DIRECTION('',(-1.,0.,0.));
#1210=DIRECTION('',(0.,0.,1.));
#1211=DIRECTION('',(0.,-1.,0.));
#1212=DIRECTION('',(0.,0.,1.));
#1213=DIRECTION('',(0.,-1.,0.));
#1214=DIRECTION('',(0.,1.,0.));
#1215=DIRECTION('',(0.,0.,1.));
#1216=DIRECTION('',(-1.,0.,0.));
#1217=DIRECTION('',(0.,0.,1.));
#1218=DIRECTION('',(-1.,0.,0.));
#1219=DIRECTION('',(-1.,0.,0.));
#1220=DIRECTION('',(0.,0.,1.));
#1221=DIRECTION('',(0.,-1.,0.));
#1222=DIRECTION('',(0.,-1.,0.));
#1223=DIRECTION('',(0.,0.,-1.));
#1224=DIRECTION('',(1.,0.,0.));
#1225=DIRECTION('',(1.,0.,0.));
#1226=DIRECTION('',(0.,0.,-1.));
#1227=DIRECTION('',(0.,-1.,0.));
#1228=DIRECTION('',(0.,0.,-1.));
#1229=DIRECTION('',(0.,-1.,0.));
#1230=DIRECTION('',(0.,0.,-1.));
#1231=DIRECTION('',(0.,-1.,0.));
#1232=DIRECTION('',(0.,0.,-1.));
#1233=DIRECTION('',(-1.,0.,0.));
#1234=DIRECTION('',(0.,0.,-1.));
#1235=DIRECTION('',(-1.,0.,0.));
#1236=DIRECTION('',(1.,0.,0.));
#1237=DIRECTION('',(0.,0.,-1.));
#1238=DIRECTION('',(0.,-1.,0.));
#1239=DIRECTION('',(0.,0.,-1.));
#1240=DIRECTION('',(0.,-1.,0.));
#1241=DIRECTION('',(0.,-1.,0.));
#1242=DIRECTION('',(0.,0.,-1.));
#1243=DIRECTION('',(-1.,0.,0.));
#1244=DIRECTION('',(-1.,0.,0.));
#1245=DIRECTION('',(0.,0.,1.));
#1246=DIRECTION('',(1.,0.,0.));
#1247=DIRECTION('',(-1.,0.,0.));
#1248=DIRECTION('',(-5.9292306307801E-17,1.,0.));
#1249=DIRECTION('',(-1.,0.,0.));
#1250=DIRECTION('',(-0.707106781186548,-0.707106781186548,0.));
#1251=DIRECTION('',(-5.9292306307801E-17,1.,0.));
#1252=DIRECTION('',(0.,-1.,0.));
#1253=DIRECTION('',(-1.,0.,0.));
#1254=DIRECTION('',(0.,-1.,0.));
#1255=DIRECTION('',(-1.,0.,0.));
#1256=DIRECTION('',(0.,0.,1.));
#1257=DIRECTION('',(1.,0.,0.));
#1258=DIRECTION('',(0.,0.,1.));
#1259=DIRECTION('',(1.,0.,0.));
#1260=DIRECTION('',(0.,1.,0.));
#1261=DIRECTION('',(0.,0.,1.));
#1262=DIRECTION('',(0.,0.,1.));
#1263=DIRECTION('',(-1.,0.,0.));
#1264=DIRECTION('',(0.,0.,1.));
#1265=DIRECTION('',(-1.,0.,0.));
#1266=DIRECTION('',(0.,0.,1.));
#1267=DIRECTION('',(0.,0.,1.));
#1268=DIRECTION('',(0.,-1.,0.));
#1269=DIRECTION('',(0.,1.,0.));
#1270=DIRECTION('',(0.,0.,1.));
#1271=DIRECTION('',(0.,0.,1.));
#1272=DIRECTION('',(-1.,0.,0.));
#1273=DIRECTION('',(-1.,0.,0.));
#1274=DIRECTION('',(0.,0.,1.));
#1275=DIRECTION('',(0.,-1.,0.));
#1276=DIRECTION('',(0.,0.,-1.));
#1277=DIRECTION('',(1.,0.,0.));
#1278=DIRECTION('',(1.,5.9292306307801E-17,0.));
#1279=DIRECTION('',(-5.9292306307801E-17,1.,0.));
#1280=DIRECTION('',(0.,0.,1.));
#1281=DIRECTION('',(-5.9292306307801E-17,1.,0.));
#1282=DIRECTION('',(0.,0.,1.));
#1283=DIRECTION('',(0.,1.,0.));
#1284=DIRECTION('',(0.,0.,1.));
#1285=DIRECTION('',(0.,0.,1.));
#1286=DIRECTION('',(-1.,0.,0.));
#1287=DIRECTION('',(1.,5.9292306307801E-17,0.));
#1288=DIRECTION('',(-5.9292306307801E-17,1.,0.));
#1289=DIRECTION('',(0.,0.,-1.));
#1290=DIRECTION('',(-5.9292306307801E-17,1.,0.));
#1291=DIRECTION('',(0.,1.,0.));
#1292=DIRECTION('',(0.,0.,1.));
#1293=DIRECTION('',(0.,0.,-1.));
#1294=DIRECTION('',(-1.,0.,0.));
#1295=DIRECTION('',(0.,0.,-1.));
#1296=DIRECTION('',(1.,0.,0.));
#1297=DIRECTION('',(-0.707106781186548,-0.707106781186548,0.));
#1298=DIRECTION('',(0.707106781186548,-0.707106781186548,0.));
#1299=DIRECTION('',(0.707106781186548,0.707106781186548,0.));
#1300=DIRECTION('',(0.,0.,1.));
#1301=DIRECTION('',(1.,0.,0.));
#1302=DIRECTION('',(0.,0.,1.));
#1303=DIRECTION('',(1.,0.,0.));
#1304=DIRECTION('',(0.,0.,1.));
#1305=DIRECTION('',(1.,0.,0.));
#1306=CARTESIAN_POINT('',(0.,0.,0.));
#1307=CARTESIAN_POINT('',(-0.001225,-0.000625,-2.5410988417629E-21));
#1308=CARTESIAN_POINT('',(-0.001225,-0.000625,2.E-5));
#1309=CARTESIAN_POINT('',(-0.0014,-0.000625,2.E-5));
#1310=CARTESIAN_POINT('',(-0.00105,-0.000625,2.E-5));
#1311=CARTESIAN_POINT('',(-0.00105,-0.000625,-2.5410988417629E-21));
#1312=CARTESIAN_POINT('',(-0.00105,-0.000625,-2.5410988417629E-21));
#1313=CARTESIAN_POINT('',(-0.001225,-0.000625,-2.5410988417629E-21));
#1314=CARTESIAN_POINT('',(-0.0014,-0.000625,-2.5410988417629E-21));
#1315=CARTESIAN_POINT('',(-0.0014,-0.000625,-2.5410988417629E-21));
#1316=CARTESIAN_POINT('',(-0.0014,-0.00075,-2.5410988417629E-21));
#1317=CARTESIAN_POINT('',(-0.0014,-0.00075,2.E-5));
#1318=CARTESIAN_POINT('',(-0.0014,-0.000875,2.E-5));
#1319=CARTESIAN_POINT('',(-0.0014,-0.00075,-2.5410988417629E-21));
#1320=CARTESIAN_POINT('',(-0.0014,-0.000875,-2.5410988417629E-21));
#1321=CARTESIAN_POINT('',(-0.0014,-0.000875,-2.5410988417629E-21));
#1322=CARTESIAN_POINT('',(-0.001225,-0.000875,-2.5410988417629E-21));
#1323=CARTESIAN_POINT('',(-0.001225,-0.000875,2.E-5));
#1324=CARTESIAN_POINT('',(-0.00105,-0.000875,2.E-5));
#1325=CARTESIAN_POINT('',(-0.001225,-0.000875,-2.5410988417629E-21));
#1326=CARTESIAN_POINT('',(-0.00105,-0.000875,-2.5410988417629E-21));
#1327=CARTESIAN_POINT('',(-0.00105,-0.000875,-2.5410988417629E-21));
#1328=CARTESIAN_POINT('',(-0.00105,-0.00075,-2.5410988417629E-21));
#1329=CARTESIAN_POINT('',(-0.00105,-0.00075,2.E-5));
#1330=CARTESIAN_POINT('',(-0.00105,-0.00075,-2.5410988417629E-21));
#1331=CARTESIAN_POINT('',(0.,-0.0015,-2.5410988417629E-21));
#1332=CARTESIAN_POINT('',(-0.001225,-0.000125,-1.6940658945086E-21));
#1333=CARTESIAN_POINT('',(-0.001225,-0.000125,2.E-5));
#1334=CARTESIAN_POINT('',(-0.0014,-0.000125,2.E-5));
#1335=CARTESIAN_POINT('',(-0.00105,-0.000125,2.E-5));
#1336=CARTESIAN_POINT('',(-0.00105,-0.000125,-1.6940658945086E-21));
#1337=CARTESIAN_POINT('',(-0.00105,-0.000125,-1.6940658945086E-21));
#1338=CARTESIAN_POINT('',(-0.001225,-0.000125,-1.6940658945086E-21));
#1339=CARTESIAN_POINT('',(-0.0014,-0.000125,-1.6940658945086E-21));
#1340=CARTESIAN_POINT('',(-0.0014,-0.000125,-1.6940658945086E-21));
#1341=CARTESIAN_POINT('',(-0.0014,-0.00025,-1.6940658945086E-21));
#1342=CARTESIAN_POINT('',(-0.0014,-0.00025,2.E-5));
#1343=CARTESIAN_POINT('',(-0.0014,-0.000375,2.E-5));
#1344=CARTESIAN_POINT('',(-0.0014,-0.00025,-1.6940658945086E-21));
#1345=CARTESIAN_POINT('',(-0.0014,-0.000375,-1.6940658945086E-21));
#1346=CARTESIAN_POINT('',(-0.0014,-0.000375,-1.6940658945086E-21));
#1347=CARTESIAN_POINT('',(-0.001225,-0.000375,-1.6940658945086E-21));
#1348=CARTESIAN_POINT('',(-0.001225,-0.000375,2.E-5));
#1349=CARTESIAN_POINT('',(-0.00105,-0.000375,2.E-5));
#1350=CARTESIAN_POINT('',(-0.001225,-0.000375,-1.6940658945086E-21));
#1351=CARTESIAN_POINT('',(-0.00105,-0.000375,-1.6940658945086E-21));
#1352=CARTESIAN_POINT('',(-0.00105,-0.000375,-1.6940658945086E-21));
#1353=CARTESIAN_POINT('',(-0.00105,-0.00025,-1.6940658945086E-21));
#1354=CARTESIAN_POINT('',(-0.00105,-0.00025,2.E-5));
#1355=CARTESIAN_POINT('',(-0.00105,-0.00025,-1.6940658945086E-21));
#1356=CARTESIAN_POINT('',(0.,-0.001,-1.6940658945086E-21));
#1357=CARTESIAN_POINT('',(-0.001225,0.000375,-8.470329472543E-22));
#1358=CARTESIAN_POINT('',(-0.001225,0.000375,2.E-5));
#1359=CARTESIAN_POINT('',(-0.0014,0.000375,2.E-5));
#1360=CARTESIAN_POINT('',(-0.00105,0.000375,2.E-5));
#1361=CARTESIAN_POINT('',(-0.00105,0.000375,-8.470329472543E-22));
#1362=CARTESIAN_POINT('',(-0.00105,0.000375,-8.470329472543E-22));
#1363=CARTESIAN_POINT('',(-0.001225,0.000375,-8.470329472543E-22));
#1364=CARTESIAN_POINT('',(-0.0014,0.000375,-8.470329472543E-22));
#1365=CARTESIAN_POINT('',(-0.0014,0.000375,-8.470329472543E-22));
#1366=CARTESIAN_POINT('',(-0.0014,0.00025,-8.470329472543E-22));
#1367=CARTESIAN_POINT('',(-0.0014,0.00025,2.E-5));
#1368=CARTESIAN_POINT('',(-0.0014,0.000125,2.E-5));
#1369=CARTESIAN_POINT('',(-0.0014,0.00025,-8.470329472543E-22));
#1370=CARTESIAN_POINT('',(-0.0014,0.000125,-8.470329472543E-22));
#1371=CARTESIAN_POINT('',(-0.0014,0.000125,-8.470329472543E-22));
#1372=CARTESIAN_POINT('',(-0.001225,0.000125,-8.470329472543E-22));
#1373=CARTESIAN_POINT('',(-0.001225,0.000125,2.E-5));
#1374=CARTESIAN_POINT('',(-0.00105,0.000125,2.E-5));
#1375=CARTESIAN_POINT('',(-0.001225,0.000125,-8.470329472543E-22));
#1376=CARTESIAN_POINT('',(-0.00105,0.000125,-8.470329472543E-22));
#1377=CARTESIAN_POINT('',(-0.00105,0.000125,-8.470329472543E-22));
#1378=CARTESIAN_POINT('',(-0.00105,0.00025,-8.470329472543E-22));
#1379=CARTESIAN_POINT('',(-0.00105,0.00025,2.E-5));
#1380=CARTESIAN_POINT('',(-0.00105,0.00025,-8.470329472543E-22));
#1381=CARTESIAN_POINT('',(0.,-0.0005,-8.470329472543E-22));
#1382=CARTESIAN_POINT('',(-0.001225,0.000875,0.));
#1383=CARTESIAN_POINT('',(-0.001225,0.000875,2.E-5));
#1384=CARTESIAN_POINT('',(-0.0014,0.000875,2.E-5));
#1385=CARTESIAN_POINT('',(-0.00105,0.000875,2.E-5));
#1386=CARTESIAN_POINT('',(-0.00105,0.000875,0.));
#1387=CARTESIAN_POINT('',(-0.00105,0.000875,0.));
#1388=CARTESIAN_POINT('',(-0.001225,0.000875,0.));
#1389=CARTESIAN_POINT('',(-0.0014,0.000875,0.));
#1390=CARTESIAN_POINT('',(-0.0014,0.000875,0.));
#1391=CARTESIAN_POINT('',(-0.0014,0.00075,0.));
#1392=CARTESIAN_POINT('',(-0.0014,0.00075,2.E-5));
#1393=CARTESIAN_POINT('',(-0.0014,0.000625,2.E-5));
#1394=CARTESIAN_POINT('',(-0.0014,0.00075,0.));
#1395=CARTESIAN_POINT('',(-0.0014,0.000625,0.));
#1396=CARTESIAN_POINT('',(-0.0014,0.000625,0.));
#1397=CARTESIAN_POINT('',(-0.001225,0.000625,0.));
#1398=CARTESIAN_POINT('',(-0.001225,0.000625,2.E-5));
#1399=CARTESIAN_POINT('',(-0.00105,0.000625,2.E-5));
#1400=CARTESIAN_POINT('',(-0.001225,0.000625,0.));
#1401=CARTESIAN_POINT('',(-0.00105,0.000625,0.));
#1402=CARTESIAN_POINT('',(-0.00105,0.000625,0.));
#1403=CARTESIAN_POINT('',(-0.00105,0.00075,0.));
#1404=CARTESIAN_POINT('',(-0.00105,0.00075,2.E-5));
#1405=CARTESIAN_POINT('',(-0.00105,0.00075,0.));
#1406=CARTESIAN_POINT('',(0.,0.,0.));
#1407=CARTESIAN_POINT('',(0.001225,-0.000625,-2.5410988417629E-21));
#1408=CARTESIAN_POINT('',(0.001225,-0.000625,2.E-5));
#1409=CARTESIAN_POINT('',(0.0014,-0.000625,2.E-5));
#1410=CARTESIAN_POINT('',(0.00105,-0.000625,2.E-5));
#1411=CARTESIAN_POINT('',(0.0014,-0.000625,-2.5410988417629E-21));
#1412=CARTESIAN_POINT('',(0.0014,-0.000625,-2.5410988417629E-21));
#1413=CARTESIAN_POINT('',(0.001225,-0.000625,-2.5410988417629E-21));
#1414=CARTESIAN_POINT('',(0.00105,-0.000625,-2.5410988417629E-21));
#1415=CARTESIAN_POINT('',(0.00105,-0.000625,-2.5410988417629E-21));
#1416=CARTESIAN_POINT('',(0.0014,-0.00075,-2.5410988417629E-21));
#1417=CARTESIAN_POINT('',(0.0014,-0.00075,2.E-5));
#1418=CARTESIAN_POINT('',(0.0014,-0.000875,2.E-5));
#1419=CARTESIAN_POINT('',(0.0014,-0.000875,-2.5410988417629E-21));
#1420=CARTESIAN_POINT('',(0.0014,-0.000875,-2.5410988417629E-21));
#1421=CARTESIAN_POINT('',(0.0014,-0.00075,-2.5410988417629E-21));
#1422=CARTESIAN_POINT('',(0.001225,-0.000875,-2.5410988417629E-21));
#1423=CARTESIAN_POINT('',(0.001225,-0.000875,2.E-5));
#1424=CARTESIAN_POINT('',(0.00105,-0.000875,2.E-5));
#1425=CARTESIAN_POINT('',(0.00105,-0.000875,-2.5410988417629E-21));
#1426=CARTESIAN_POINT('',(0.00105,-0.000875,-2.5410988417629E-21));
#1427=CARTESIAN_POINT('',(0.001225,-0.000875,-2.5410988417629E-21));
#1428=CARTESIAN_POINT('',(0.00105,-0.00075,-2.5410988417629E-21));
#1429=CARTESIAN_POINT('',(0.00105,-0.00075,2.E-5));
#1430=CARTESIAN_POINT('',(0.00105,-0.00075,-2.5410988417629E-21));
#1431=CARTESIAN_POINT('',(0.,-0.0015,-2.5410988417629E-21));
#1432=CARTESIAN_POINT('',(0.001225,-0.000125,-1.6940658945086E-21));
#1433=CARTESIAN_POINT('',(0.001225,-0.000125,2.E-5));
#1434=CARTESIAN_POINT('',(0.0014,-0.000125,2.E-5));
#1435=CARTESIAN_POINT('',(0.00105,-0.000125,2.E-5));
#1436=CARTESIAN_POINT('',(0.0014,-0.000125,-1.6940658945086E-21));
#1437=CARTESIAN_POINT('',(0.0014,-0.000125,-1.6940658945086E-21));
#1438=CARTESIAN_POINT('',(0.001225,-0.000125,-1.6940658945086E-21));
#1439=CARTESIAN_POINT('',(0.00105,-0.000125,-1.6940658945086E-21));
#1440=CARTESIAN_POINT('',(0.00105,-0.000125,-1.6940658945086E-21));
#1441=CARTESIAN_POINT('',(0.0014,-0.00025,-1.6940658945086E-21));
#1442=CARTESIAN_POINT('',(0.0014,-0.00025,2.E-5));
#1443=CARTESIAN_POINT('',(0.0014,-0.000375,2.E-5));
#1444=CARTESIAN_POINT('',(0.0014,-0.000375,-1.6940658945086E-21));
#1445=CARTESIAN_POINT('',(0.0014,-0.000375,-1.6940658945086E-21));
#1446=CARTESIAN_POINT('',(0.0014,-0.00025,-1.6940658945086E-21));
#1447=CARTESIAN_POINT('',(0.001225,-0.000375,-1.6940658945086E-21));
#1448=CARTESIAN_POINT('',(0.001225,-0.000375,2.E-5));
#1449=CARTESIAN_POINT('',(0.00105,-0.000375,2.E-5));
#1450=CARTESIAN_POINT('',(0.00105,-0.000375,-1.6940658945086E-21));
#1451=CARTESIAN_POINT('',(0.00105,-0.000375,-1.6940658945086E-21));
#1452=CARTESIAN_POINT('',(0.001225,-0.000375,-1.6940658945086E-21));
#1453=CARTESIAN_POINT('',(0.00105,-0.00025,-1.6940658945086E-21));
#1454=CARTESIAN_POINT('',(0.00105,-0.00025,2.E-5));
#1455=CARTESIAN_POINT('',(0.00105,-0.00025,-1.6940658945086E-21));
#1456=CARTESIAN_POINT('',(0.,-0.001,-1.6940658945086E-21));
#1457=CARTESIAN_POINT('',(0.001225,0.000375,-8.470329472543E-22));
#1458=CARTESIAN_POINT('',(0.001225,0.000375,2.E-5));
#1459=CARTESIAN_POINT('',(0.0014,0.000375,2.E-5));
#1460=CARTESIAN_POINT('',(0.00105,0.000375,2.E-5));
#1461=CARTESIAN_POINT('',(0.0014,0.000375,-8.470329472543E-22));
#1462=CARTESIAN_POINT('',(0.0014,0.000375,-8.470329472543E-22));
#1463=CARTESIAN_POINT('',(0.001225,0.000375,-8.470329472543E-22));
#1464=CARTESIAN_POINT('',(0.00105,0.000375,-8.470329472543E-22));
#1465=CARTESIAN_POINT('',(0.00105,0.000375,-8.470329472543E-22));
#1466=CARTESIAN_POINT('',(0.0014,0.00025,-8.470329472543E-22));
#1467=CARTESIAN_POINT('',(0.0014,0.00025,2.E-5));
#1468=CARTESIAN_POINT('',(0.0014,0.000125,2.E-5));
#1469=CARTESIAN_POINT('',(0.0014,0.000125,-8.470329472543E-22));
#1470=CARTESIAN_POINT('',(0.0014,0.000125,-8.470329472543E-22));
#1471=CARTESIAN_POINT('',(0.0014,0.00025,-8.470329472543E-22));
#1472=CARTESIAN_POINT('',(0.001225,0.000125,-8.470329472543E-22));
#1473=CARTESIAN_POINT('',(0.001225,0.000125,2.E-5));
#1474=CARTESIAN_POINT('',(0.00105,0.000125,2.E-5));
#1475=CARTESIAN_POINT('',(0.00105,0.000125,-8.470329472543E-22));
#1476=CARTESIAN_POINT('',(0.00105,0.000125,-8.470329472543E-22));
#1477=CARTESIAN_POINT('',(0.001225,0.000125,-8.470329472543E-22));
#1478=CARTESIAN_POINT('',(0.00105,0.00025,-8.470329472543E-22));
#1479=CARTESIAN_POINT('',(0.00105,0.00025,2.E-5));
#1480=CARTESIAN_POINT('',(0.00105,0.00025,-8.470329472543E-22));
#1481=CARTESIAN_POINT('',(0.,-0.0005,-8.470329472543E-22));
#1482=CARTESIAN_POINT('',(0.0015,2.16840434497101E-19,0.00045));
#1483=CARTESIAN_POINT('',(0.0015,2.16840434497101E-19,2.E-5));
#1484=CARTESIAN_POINT('',(0.0015,0.001,2.E-5));
#1485=CARTESIAN_POINT('',(0.0015,-0.001,2.E-5));
#1486=CARTESIAN_POINT('',(0.0015,0.001,0.00045));
#1487=CARTESIAN_POINT('',(0.0015,0.001,0.00047));
#1488=CARTESIAN_POINT('',(0.0015,2.16840434497101E-19,0.00047));
#1489=CARTESIAN_POINT('',(0.0015,-0.001,0.00047));
#1490=CARTESIAN_POINT('',(0.0015,-0.001,0.00045));
#1491=CARTESIAN_POINT('',(4.33680868994202E-19,0.001,0.00045));
#1492=CARTESIAN_POINT('',(4.33680868994202E-19,0.001,2.E-5));
#1493=CARTESIAN_POINT('',(-0.0015,0.001,2.E-5));
#1494=CARTESIAN_POINT('',(-0.0015,0.001,0.00045));
#1495=CARTESIAN_POINT('',(-0.0015,0.001,0.00047));
#1496=CARTESIAN_POINT('',(4.33680868994202E-19,0.001,0.00047));
#1497=CARTESIAN_POINT('',(-0.0015,0.,0.00045));
#1498=CARTESIAN_POINT('',(-0.0015,0.,2.E-5));
#1499=CARTESIAN_POINT('',(-0.0015,-0.001,2.E-5));
#1500=CARTESIAN_POINT('',(-0.0015,-0.001,0.00045));
#1501=CARTESIAN_POINT('',(-0.0015,-0.001,0.00047));
#1502=CARTESIAN_POINT('',(-0.0015,0.,0.00047));
#1503=CARTESIAN_POINT('',(4.33680868994202E-19,-0.001,0.00045));
#1504=CARTESIAN_POINT('',(4.33680868994202E-19,-0.001,2.E-5));
#1505=CARTESIAN_POINT('',(4.33680868994202E-19,-0.001,0.00047));
#1506=CARTESIAN_POINT('',(0.,0.,2.E-5));
#1507=CARTESIAN_POINT('',(6.7762635780344E-20,-0.0008,2.E-5));
#1508=CARTESIAN_POINT('',(0.0001,-0.0008,2.E-5));
#1509=CARTESIAN_POINT('',(-0.0001,-0.0008,2.E-5));
#1510=CARTESIAN_POINT('',(0.0001,1.0842021724855E-19,2.E-5));
#1511=CARTESIAN_POINT('',(0.0001,0.0008,2.E-5));
#1512=CARTESIAN_POINT('',(-4.06575814682064E-20,0.0008,2.E-5));
#1513=CARTESIAN_POINT('',(-4.06575814682064E-20,0.0008,2.E-5));
#1514=CARTESIAN_POINT('',(-5.E-5,0.00075,2.E-5));
#1515=CARTESIAN_POINT('',(-0.0001,0.0007,2.E-5));
#1516=CARTESIAN_POINT('',(-0.0001,1.0842021724855E-19,2.E-5));
#1517=CARTESIAN_POINT('',(0.0014,0.00075,2.E-5));
#1518=CARTESIAN_POINT('',(0.0014,0.000875,2.E-5));
#1519=CARTESIAN_POINT('',(0.0014,0.000625,2.E-5));
#1520=CARTESIAN_POINT('',(0.001225,0.000875,2.E-5));
#1521=CARTESIAN_POINT('',(0.00105,0.000875,2.E-5));
#1522=CARTESIAN_POINT('',(0.00105,0.00075,2.E-5));
#1523=CARTESIAN_POINT('',(0.00105,0.000625,2.E-5));
#1524=CARTESIAN_POINT('',(0.001225,0.000625,2.E-5));
#1525=CARTESIAN_POINT('',(0.,0.,0.00047));
#1526=CARTESIAN_POINT('',(-0.00125,0.00075,0.00047));
#1527=CARTESIAN_POINT('',(-0.001125,0.00075,0.00047));
#1528=CARTESIAN_POINT('',(0.001225,0.000875,0.));
#1529=CARTESIAN_POINT('',(0.0014,0.000875,0.));
#1530=CARTESIAN_POINT('',(0.0014,0.000875,0.));
#1531=CARTESIAN_POINT('',(0.001225,0.000875,0.));
#1532=CARTESIAN_POINT('',(0.00105,0.000875,0.));
#1533=CARTESIAN_POINT('',(0.00105,0.000875,0.));
#1534=CARTESIAN_POINT('',(0.0014,0.00075,0.));
#1535=CARTESIAN_POINT('',(0.0014,0.000625,0.));
#1536=CARTESIAN_POINT('',(0.0014,0.000625,0.));
#1537=CARTESIAN_POINT('',(0.0014,0.00075,0.));
#1538=CARTESIAN_POINT('',(0.001225,0.000625,0.));
#1539=CARTESIAN_POINT('',(0.00105,0.000625,0.));
#1540=CARTESIAN_POINT('',(0.00105,0.000625,0.));
#1541=CARTESIAN_POINT('',(0.001225,0.000625,0.));
#1542=CARTESIAN_POINT('',(0.00105,0.00075,0.));
#1543=CARTESIAN_POINT('',(0.00105,0.00075,0.));
#1544=CARTESIAN_POINT('',(0.,0.,0.));
#1545=CARTESIAN_POINT('',(0.0001,1.0842021724855E-19,0.));
#1546=CARTESIAN_POINT('',(0.0001,-0.0008,0.));
#1547=CARTESIAN_POINT('',(0.0001,-0.0008,0.));
#1548=CARTESIAN_POINT('',(0.0001,1.0842021724855E-19,0.));
#1549=CARTESIAN_POINT('',(0.0001,0.0008,0.));
#1550=CARTESIAN_POINT('',(0.0001,0.0008,0.));
#1551=CARTESIAN_POINT('',(6.7762635780344E-20,-0.0008,0.));
#1552=CARTESIAN_POINT('',(-0.0001,-0.0008,0.));
#1553=CARTESIAN_POINT('',(-0.0001,-0.0008,0.));
#1554=CARTESIAN_POINT('',(6.7762635780344E-20,-0.0008,0.));
#1555=CARTESIAN_POINT('',(-0.0001,1.0842021724855E-19,0.));
#1556=CARTESIAN_POINT('',(-0.0001,0.0007,2.2E-5));
#1557=CARTESIAN_POINT('',(-0.0001,0.0007,0.));
#1558=CARTESIAN_POINT('',(-0.0001,1.0842021724855E-19,0.));
#1559=CARTESIAN_POINT('',(-4.06575814682064E-20,0.0008,0.));
#1560=CARTESIAN_POINT('',(-4.06575814682064E-20,0.0008,2.2E-5));
#1561=CARTESIAN_POINT('',(-1.35525271560688E-20,0.0008,0.));
#1562=CARTESIAN_POINT('',(-4.06575814682064E-20,0.0008,0.));
#1563=CARTESIAN_POINT('',(0.,0.,0.));
#1564=CARTESIAN_POINT('',(-5.E-5,0.00075,0.));
#1565=CARTESIAN_POINT('',(-5.E-5,0.00075,2.2E-5));
#1566=CARTESIAN_POINT('',(-0.00125,0.00075,0.00046));
#1567=CARTESIAN_POINT('',(-0.00125,0.00075,0.00046));
#1568=CARTESIAN_POINT('',(-0.001125,0.00075,0.00046));
#1569=CARTESIAN_POINT('',(0.,0.,0.00046));
#1570=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#964,
#15,#16,#17,#18,#19,#20),#1571);
#1571=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1572))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1575,#1574,#1573))
REPRESENTATION_CONTEXT('Part 1','TOP_LEVEL_ASSEMBLY_PART')
);
#1572=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(5.E-6),#1575,
'DISTANCE_ACCURACY_VALUE','Maximum Tolerance applied to model');
#1573=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#1574=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#1575=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#1576=PRODUCT_DEFINITION_SHAPE('','',#1577);
#1577=PRODUCT_DEFINITION('','',#1579,#1578);
#1578=PRODUCT_DEFINITION_CONTEXT('',#1585,'design');
#1579=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('','',#1581,
 .NOT_KNOWN.);
#1580=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#1581));
#1581=PRODUCT('Part 1','Part 1','Part 1',(#1583));
#1582=PRODUCT_CATEGORY('','');
#1583=PRODUCT_CONTEXT('',#1585,'mechanical');
#1584=APPLICATION_PROTOCOL_DEFINITION('international standard',
'ap242_managed_model_based_3d_engineering',2011,#1585);
#1585=APPLICATION_CONTEXT('managed model based 3d engineering');
ENDSEC;
END-ISO-10303-21;
