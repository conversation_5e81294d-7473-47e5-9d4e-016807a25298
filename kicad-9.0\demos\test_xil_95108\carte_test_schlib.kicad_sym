(kicad_symbol_lib
	(version 20241209)
	(generator "kicad_symbol_editor")
	(generator_version "9.0")
	(symbol "+12V"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+12V"
			(at 0 3.556 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "power-flag symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "Power Flag Symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "+12V_0_1"
			(polyline
				(pts
					(xy -0.762 1.27) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 2.54) (xy 0.762 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "+12V_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "+12V"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "-12V"
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "-12V"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "power-flag symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "Power Flag Symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "-12V_0_0"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "-12V"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "-12V_0_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 1.27) (xy 0.762 1.27) (xy 0 2.54) (xy -0.762 1.27) (xy 0 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "74HC04"
		(pin_names
			(offset 0.762)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 3.81 2.54 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "74HC04"
			(at 5.08 -2.54 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "74HC04_0_0"
			(polyline
				(pts
					(xy -3.81 3.81) (xy -3.81 -3.81) (xy 3.81 0) (xy -3.81 3.81)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin power_in line
				(at -1.27 2.54 270)
				(length 0)
				(hide yes)
				(name "VCC"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
			)
			(pin power_in line
				(at -1.27 -2.54 90)
				(length 0)
				(hide yes)
				(name "GND"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
			)
		)
		(symbol "74HC04_1_1"
			(pin input line
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output inverted
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HC04_1_2"
			(pin input inverted
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HC04_2_1"
			(pin input line
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output inverted
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HC04_2_2"
			(pin input inverted
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HC04_3_1"
			(pin input line
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output inverted
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HC04_3_2"
			(pin input inverted
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HC04_4_1"
			(pin input line
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output inverted
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HC04_4_2"
			(pin input inverted
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HC04_5_1"
			(pin input line
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output inverted
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HC04_5_2"
			(pin input inverted
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HC04_6_1"
			(pin input line
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output inverted
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HC04_6_2"
			(pin input inverted
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "74HCT04"
		(pin_names
			(offset 0.762)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 3.81 2.54 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "74HCT04"
			(at 5.08 -2.54 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "74HCT04_0_0"
			(polyline
				(pts
					(xy -3.81 3.81) (xy -3.81 -3.81) (xy 3.81 0) (xy -3.81 3.81)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin power_in line
				(at -1.27 2.54 270)
				(length 0)
				(hide yes)
				(name "VCC"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
			)
			(pin power_in line
				(at -1.27 -2.54 90)
				(length 0)
				(hide yes)
				(name "GND"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
			)
		)
		(symbol "74HCT04_1_1"
			(pin input line
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output inverted
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HCT04_1_2"
			(pin input inverted
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HCT04_2_1"
			(pin input line
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output inverted
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HCT04_2_2"
			(pin input inverted
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HCT04_3_1"
			(pin input line
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output inverted
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HCT04_3_2"
			(pin input inverted
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HCT04_4_1"
			(pin input line
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output inverted
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HCT04_4_2"
			(pin input inverted
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HCT04_5_1"
			(pin input line
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output inverted
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HCT04_5_2"
			(pin input inverted
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HCT04_6_1"
			(pin input line
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output inverted
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "74HCT04_6_2"
			(pin input inverted
				(at -11.43 0 0)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 11.43 0 180)
				(length 7.62)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "7805"
		(pin_names
			(offset 0.762)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 3.81 -4.9784 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "7805"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "7805_0_1"
			(rectangle
				(start -5.08 -3.81)
				(end 5.08 3.81)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "7805_1_1"
			(pin input line
				(at -10.16 1.27 0)
				(length 5.08)
				(name "VI"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin input line
				(at 0 -6.35 90)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin power_out line
				(at 10.16 1.27 180)
				(length 5.08)
				(name "VO"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "C"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.254)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "C"
			(at 0.635 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "C"
			(at 0.635 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0.9652 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "cap capacitor"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "C_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "C_0_1"
			(polyline
				(pts
					(xy -2.032 0.762) (xy 2.032 0.762)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.032 -0.762) (xy 2.032 -0.762)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "C_1_1"
			(pin passive line
				(at 0 3.81 270)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 0 -3.81 90)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CAPAPOL"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.254)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "C"
			(at 1.27 2.54 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(justify left)
			)
		)
		(property "Value" "CAPAPOL"
			(at 1.27 -2.54 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 2.54 -3.81 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Datasheet" ""
			(at 1.27 2.54 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "CP* SM*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "CAPAPOL_0_1"
			(polyline
				(pts
					(xy -2.032 1.27) (xy -2.032 -1.27) (xy 2.032 -1.27) (xy 2.032 1.27)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 1.27) (xy -1.27 -0.508) (xy 1.27 -0.508) (xy 1.27 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "CAPAPOL_1_1"
			(pin passive line
				(at 0 5.08 270)
				(length 3.81)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 0 -5.08 90)
				(length 3.81)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CONN_2"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "P"
			(at -1.27 0 90)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "CONN_2"
			(at 1.27 0 90)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "CONN_2_0_1"
			(rectangle
				(start -2.54 3.81)
				(end 2.54 -3.81)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "CONN_2_1_1"
			(pin passive inverted
				(at -8.89 2.54 0)
				(length 6.35)
				(name "P1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive inverted
				(at -8.89 -2.54 0)
				(length 6.35)
				(name "PM"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CONN_2X2"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "P"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "CONN_2X2"
			(at 0.254 -3.302 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "CONN_2X2_0_1"
			(rectangle
				(start -2.54 2.54)
				(end 2.54 -2.54)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "CONN_2X2_1_1"
			(pin passive inverted
				(at -10.16 1.27 0)
				(length 7.62)
				(name "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive inverted
				(at -10.16 -1.27 0)
				(length 7.62)
				(name "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive inverted
				(at 10.16 1.27 180)
				(length 7.62)
				(name "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive inverted
				(at 10.16 -1.27 180)
				(length 7.62)
				(name "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CONN_3"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "K"
			(at -1.27 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "CONN_3"
			(at 1.27 0 90)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "CONN_3_0_1"
			(rectangle
				(start -2.54 3.81)
				(end 2.54 -3.81)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "CONN_3_1_1"
			(pin passive inverted
				(at -8.89 2.54 0)
				(length 6.35)
				(name "P1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive inverted
				(at -8.89 0 0)
				(length 6.35)
				(name "PM"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive inverted
				(at -8.89 -2.54 0)
				(length 6.35)
				(name "P3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CP"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.254)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "C"
			(at 0.635 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "CP"
			(at 0.635 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0.9652 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Polarised capacitor"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "cap capacitor"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "CP_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "CP_0_1"
			(rectangle
				(start -2.286 0.508)
				(end -2.286 1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -2.286 0.508)
				(end 2.286 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.778 2.286) (xy -0.762 2.286)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 2.794) (xy -1.27 1.778)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start 2.286 1.016)
				(end -2.286 1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start 2.286 1.016)
				(end 2.286 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start 2.286 -0.508)
				(end -2.286 -1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "CP_1_1"
			(pin passive line
				(at 0 3.81 270)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 0 -3.81 90)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CRYSTAL"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "X"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "CRYSTAL"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "CRYSTAL_0_1"
			(polyline
				(pts
					(xy -2.54 2.54) (xy -2.54 -2.54)
				)
				(stroke
					(width 0.4064)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 1.27) (xy 1.27 1.27) (xy 1.27 -1.27) (xy -1.27 -1.27) (xy -1.27 1.27)
				)
				(stroke
					(width 0.3048)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(polyline
				(pts
					(xy 2.54 2.54) (xy 2.54 -2.54)
				)
				(stroke
					(width 0.4064)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "CRYSTAL_1_1"
			(pin passive line
				(at -7.62 0 0)
				(length 5.08)
				(name "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 0 180)
				(length 5.08)
				(name "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "DB25"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at 1.27 34.29 0)
			(effects
				(font
					(size 1.778 1.778)
				)
			)
		)
		(property "Value" "DB25"
			(at -1.27 -34.29 0)
			(effects
				(font
					(size 1.778 1.778)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "DB25_0_1"
			(polyline
				(pts
					(xy -3.81 30.48) (xy -2.54 30.48)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 27.94) (xy 0.508 27.94)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 25.4) (xy -2.54 25.4)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 22.86) (xy 0.508 22.86)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 20.32) (xy -2.54 20.32)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 17.78) (xy 0.508 17.78)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 15.24) (xy -2.54 15.24)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 12.7) (xy 0.508 12.7)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 10.16) (xy -2.54 10.16)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 7.62) (xy 0.508 7.62)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 5.08) (xy -2.54 5.08)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 2.54) (xy 0.508 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 0) (xy -2.54 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -2.54) (xy 0.508 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -5.08) (xy -2.54 -5.08)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -7.62) (xy 0.508 -7.62)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -10.16) (xy -2.54 -10.16)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -12.7) (xy 0.508 -12.7)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -15.24) (xy -2.54 -15.24)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -17.78) (xy 0.508 -17.78)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -20.32) (xy -2.54 -20.32)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -22.86) (xy 0.508 -22.86)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -25.4) (xy -2.54 -25.4)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -27.94) (xy 0.508 -27.94)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -30.48) (xy -2.54 -30.48)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -32.004) (xy -3.81 32.258)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -3.81 32.258)
				(mid -3.4191 33.0712)
				(end -2.54 33.274)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -2.54 -33.02)
				(mid -3.4129 -32.8094)
				(end -3.81 -32.004)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.54 -33.02) (xy 3.302 -30.48)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 30.48)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 25.4)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 20.32)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 15.24)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 10.16)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 5.08)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 0)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 -5.08)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 -10.16)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 -15.24)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 -20.32)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 -25.4)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 -30.48)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 27.94)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 22.86)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 17.78)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 12.7)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 7.62)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 2.54)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 -2.54)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 -7.62)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 -12.7)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 -17.78)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 -22.86)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 -27.94)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start 3.81 -29.718)
				(mid 3.6655 -30.1721)
				(end 3.302 -30.48)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start 3.302 30.4546)
				(mid 3.6541 30.159)
				(end 3.7846 29.718)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.302 30.48) (xy -2.54 33.274)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.81 -29.718) (xy 3.81 29.718)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "DB25_1_1"
			(pin passive line
				(at -11.43 30.48 0)
				(length 7.62)
				(name "13"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 27.94 0)
				(length 7.62)
				(name "P25"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "25"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 25.4 0)
				(length 7.62)
				(name "12"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 22.86 0)
				(length 7.62)
				(name "P24"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "24"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 20.32 0)
				(length 7.62)
				(name "11"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 17.78 0)
				(length 7.62)
				(name "P23"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "23"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 15.24 0)
				(length 7.62)
				(name "10"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 12.7 0)
				(length 7.62)
				(name "P22"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "22"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 10.16 0)
				(length 7.62)
				(name "9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 7.62 0)
				(length 7.62)
				(name "P21"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "21"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 5.08 0)
				(length 7.62)
				(name "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 2.54 0)
				(length 7.62)
				(name "P20"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "20"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 0 0)
				(length 7.62)
				(name "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -2.54 0)
				(length 7.62)
				(name "P19"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "19"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -5.08 0)
				(length 7.62)
				(name "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -7.62 0)
				(length 7.62)
				(name "P18"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "18"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -10.16 0)
				(length 7.62)
				(name "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -12.7 0)
				(length 7.62)
				(name "P17"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "17"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -15.24 0)
				(length 7.62)
				(name "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -17.78 0)
				(length 7.62)
				(name "P16"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -20.32 0)
				(length 7.62)
				(name "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -22.86 0)
				(length 7.62)
				(name "P15"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -25.4 0)
				(length 7.62)
				(name "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -27.94 0)
				(length 7.62)
				(name "P14"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -30.48 0)
				(length 7.62)
				(name "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "DB9"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at 0 13.97 0)
			(effects
				(font
					(size 1.778 1.778)
				)
			)
		)
		(property "Value" "DB9"
			(at 0 -13.97 0)
			(effects
				(font
					(size 1.778 1.778)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "DB9_0_1"
			(polyline
				(pts
					(xy -3.81 10.16) (xy -2.54 10.16)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 7.62) (xy 0.508 7.62)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 5.08) (xy -2.54 5.08)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 2.54) (xy 0.508 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 0) (xy -2.54 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -2.54) (xy 0.508 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -5.08) (xy -2.54 -5.08)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -7.62) (xy 0.508 -7.62)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -10.16) (xy -2.54 -10.16)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -11.6586) (xy -3.556 -11.938)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -11.684) (xy -3.81 11.684)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.556 11.938) (xy -3.81 11.684)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.556 11.938) (xy -2.54 12.446)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.556 -11.938) (xy -2.794 -12.446)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.794 -12.446) (xy -1.27 -12.446)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.54 12.446) (xy -1.778 12.446)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 10.16)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 5.08)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 0)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 -5.08)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 -10.16)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 7.62)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 2.54)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 -2.54)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 -7.62)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.2766 9.906) (xy -1.778 12.446)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.2766 9.906) (xy 3.81 9.398)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.556 -10.3886) (xy -1.27 -12.446)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.81 9.398) (xy 3.81 -9.906)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.81 -9.906) (xy 3.556 -10.3886)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "DB9_1_1"
			(pin passive line
				(at -11.43 10.16 0)
				(length 7.62)
				(name "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 7.62 0)
				(length 7.62)
				(name "P9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 5.08 0)
				(length 7.62)
				(name "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 2.54 0)
				(length 7.62)
				(name "P8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 0 0)
				(length 7.62)
				(name "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -2.54 0)
				(length 7.62)
				(name "P7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -5.08 0)
				(length 7.62)
				(name "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -7.62 0)
				(length 7.62)
				(name "P6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -10.16 0)
				(length 7.62)
				(name "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "DIODE"
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "D"
			(at 0 2.54 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "DIODE"
			(at 0 -2.54 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "D? S*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "DIODE_0_1"
			(polyline
				(pts
					(xy -1.27 1.27) (xy 1.27 0) (xy -1.27 -1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy 1.27 1.27) (xy 1.27 -1.27)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "DIODE_1_1"
			(pin passive line
				(at -5.08 0 0)
				(length 3.81)
				(name "A"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
			(pin passive line
				(at 5.08 0 180)
				(length 3.81)
				(name "K"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "GND"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -6.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "GROUND power-flag symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "Power Flag Symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "GND_0_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "GND_1_1"
			(pin power_in line
				(at 0 0 270)
				(length 0)
				(hide yes)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "LED"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "D"
			(at 0 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "LED"
			(at 0 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "LED-3MM LED-5MM LED-10MM LED-0603 LED-0805 LED-1206 LEDV"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "LED_0_1"
			(polyline
				(pts
					(xy -2.032 -0.635) (xy -3.175 -1.651) (xy -3.048 -1.016)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.651 -1.016) (xy -2.794 -2.032) (xy -2.667 -1.397)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 1.27) (xy -1.27 -1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.27 1.27) (xy -1.27 0) (xy 1.27 -1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "LED_1_1"
			(pin passive line
				(at -5.08 0 0)
				(length 3.81)
				(name "K"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 5.08 0 180)
				(length 3.81)
				(name "A"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "LM318N"
		(pin_names
			(offset 0.762)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 2.54 7.62 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "LM318N"
			(at 5.334 -6.35 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "LM318N_0_1"
			(polyline
				(pts
					(xy 5.08 5.08) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 2.54) (xy 2.54 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 5.08 0) (xy -5.08 5.08) (xy -5.08 -5.08) (xy 5.08 0)
				)
				(stroke
					(width 0.3048)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(polyline
				(pts
					(xy 5.08 -2.54) (xy 2.54 -1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "LM318N_1_1"
			(pin input line
				(at -10.16 2.54 0)
				(length 5.08)
				(name "+"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -10.16 -2.54 0)
				(length 5.08)
				(name "-"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at -2.54 7.62 270)
				(length 3.81)
				(name "V+"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at -2.54 -7.62 90)
				(length 3.81)
				(name "V-"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 10.16 5.08 180)
				(length 5.08)
				(name "FIN2"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 10.16 2.54 180)
				(length 5.08)
				(name "FIN1"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at 10.16 0 180)
				(length 5.08)
				(name "OUT"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 10.16 -2.54 180)
				(length 5.08)
				(name "FOUT"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "LM7805"
		(pin_names
			(offset 0.762)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 3.81 -4.9784 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "LM7805"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "LM7805_0_1"
			(rectangle
				(start -5.08 -3.81)
				(end 5.08 3.81)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "LM7805_1_1"
			(pin input line
				(at -10.16 1.27 0)
				(length 5.08)
				(name "VI"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin input line
				(at 0 -6.35 90)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin power_out line
				(at 10.16 1.27 180)
				(length 5.08)
				(name "VO"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "PWR_FLAG"
		(power)
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#FLG"
			(at 0 2.413 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "PWR_FLAG"
			(at 0 4.572 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "PWR_FLAG_0_0"
			(pin power_out line
				(at 0 0 90)
				(length 0)
				(name "pwr"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
		)
		(symbol "PWR_FLAG_0_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 1.27) (xy -1.905 2.54) (xy 0 3.81) (xy 1.905 2.54) (xy 0 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "R"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "R"
			(at 2.032 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "R"
			(at 0 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at -1.778 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "r res resistor"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "R_* R_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "R_0_1"
			(rectangle
				(start -1.016 -2.54)
				(end 1.016 2.54)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "R_1_1"
			(pin passive line
				(at 0 3.81 270)
				(length 1.27)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 0 -3.81 90)
				(length 1.27)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "RAM_32KO"
		(pin_names
			(offset 1.016)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 3.81 22.86 0)
			(effects
				(font
					(size 1.778 1.778)
				)
			)
		)
		(property "Value" "RAM_32KO"
			(at 7.62 -27.94 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "RAM_32KO_0_0"
			(pin power_in line
				(at 0 26.67 270)
				(length 5.08)
				(name "VCC"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "28"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at 0 -31.75 90)
				(length 5.08)
				(name "GND"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "RAM_32KO_0_1"
			(rectangle
				(start -10.16 21.59)
				(end 10.16 -26.67)
				(stroke
					(width 0.3048)
					(type default)
				)
				(fill
					(type background)
				)
			)
		)
		(symbol "RAM_32KO_1_1"
			(pin input line
				(at -17.78 20.32 0)
				(length 7.62)
				(name "A0"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 17.78 0)
				(length 7.62)
				(name "A1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 15.24 0)
				(length 7.62)
				(name "A2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 12.7 0)
				(length 7.62)
				(name "A3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 10.16 0)
				(length 7.62)
				(name "A4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 7.62 0)
				(length 7.62)
				(name "A5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 5.08 0)
				(length 7.62)
				(name "A6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 2.54 0)
				(length 7.62)
				(name "A7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 0 0)
				(length 7.62)
				(name "A8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "25"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 -2.54 0)
				(length 7.62)
				(name "A9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "24"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 -5.08 0)
				(length 7.62)
				(name "A10"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "21"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 -7.62 0)
				(length 7.62)
				(name "A11"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "23"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 -10.16 0)
				(length 7.62)
				(name "A12"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 -12.7 0)
				(length 7.62)
				(name "A13"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "26"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 -15.24 0)
				(length 7.62)
				(name "A14"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input inverted
				(at -17.78 -20.32 0)
				(length 7.62)
				(name "CS"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "20"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input inverted
				(at -17.78 -22.86 0)
				(length 7.62)
				(name "OE"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "22"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input inverted
				(at -17.78 -25.4 0)
				(length 7.62)
				(name "WE"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "27"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin tri_state line
				(at 17.78 20.32 180)
				(length 7.62)
				(name "D0"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin tri_state line
				(at 17.78 17.78 180)
				(length 7.62)
				(name "D1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin tri_state line
				(at 17.78 15.24 180)
				(length 7.62)
				(name "D2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin tri_state line
				(at 17.78 12.7 180)
				(length 7.62)
				(name "D3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin tri_state line
				(at 17.78 10.16 180)
				(length 7.62)
				(name "D4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin tri_state line
				(at 17.78 7.62 180)
				(length 7.62)
				(name "D5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "17"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin tri_state line
				(at 17.78 5.08 180)
				(length 7.62)
				(name "D6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "18"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin tri_state line
				(at 17.78 2.54 180)
				(length 7.62)
				(name "D7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "19"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "SW_PUSH"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "SW"
			(at 3.81 2.794 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "SW_PUSH"
			(at 0 -2.032 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "SW_PUSH_0_1"
			(rectangle
				(start -4.318 1.27)
				(end 4.318 1.524)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.016 1.524) (xy -0.762 2.286) (xy 0.762 2.286) (xy 1.016 1.524)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive inverted
				(at -7.62 0 0)
				(length 5.08)
				(name "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive inverted
				(at 7.62 0 180)
				(length 5.08)
				(name "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "TDA8702"
		(pin_names
			(offset 1.016)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 5.08 20.32 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(property "Value" "TDA8702"
			(at 5.08 -20.32 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "TDA8702_0_1"
			(rectangle
				(start -12.7 17.78)
				(end 12.7 -17.78)
				(stroke
					(width 0.3048)
					(type default)
				)
				(fill
					(type background)
				)
			)
		)
		(symbol "TDA8702_1_1"
			(pin input line
				(at -20.32 15.24 0)
				(length 7.62)
				(name "DB0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 12.7 0)
				(length 7.62)
				(name "DB1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 10.16 0)
				(length 7.62)
				(name "DB2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 7.62 0)
				(length 7.62)
				(name "DB3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 5.08 0)
				(length 7.62)
				(name "DB4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 2.54 0)
				(length 7.62)
				(name "DB5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 0 0)
				(length 7.62)
				(name "DB6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 -2.54 0)
				(length 7.62)
				(name "DB7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 -10.16 0)
				(length 7.62)
				(name "VREF"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input clock
				(at -20.32 -15.24 0)
				(length 7.62)
				(name "Clock"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -2.54 22.86 270)
				(length 5.08)
				(name "VCCD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -2.54 -22.86 90)
				(length 5.08)
				(name "DGND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 2.54 22.86 270)
				(length 5.08)
				(name "VCCA"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 2.54 -22.86 90)
				(length 5.08)
				(name "AGND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output inverted
				(at 20.32 5.08 180)
				(length 7.62)
				(name "VOUT"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 20.32 -3.81 180)
				(length 7.62)
				(name "VOUT"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "VCC"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "power-flag symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "Power Flag Symbol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "VCC_0_1"
			(circle
				(center 0 1.905)
				(radius 0.635)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "VCC_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "VCC"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "XC95108PC84"
		(pin_names
			(offset 0.762)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 13.97 64.77 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(property "Value" "XC95108PC84"
			(at 10.16 -49.53 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "XC95108PC84_0_1"
			(rectangle
				(start -21.59 62.23)
				(end 21.59 -46.99)
				(stroke
					(width 0.381)
					(type default)
				)
				(fill
					(type background)
				)
			)
		)
		(symbol "XC95108PC84_1_1"
			(pin input line
				(at -29.21 59.69 0)
				(length 7.62)
				(name "TDI"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "28"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -29.21 57.15 0)
				(length 7.62)
				(name "TMS"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "29"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -29.21 54.61 0)
				(length 7.62)
				(name "TCK"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "30"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin output line
				(at -29.21 52.07 0)
				(length 7.62)
				(name "TDO"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "59"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 46.99 0)
				(length 7.62)
				(name "P1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 44.45 0)
				(length 7.62)
				(name "P2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 41.91 0)
				(length 7.62)
				(name "P3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 39.37 0)
				(length 7.62)
				(name "P4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 36.83 0)
				(length 7.62)
				(name "P5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 34.29 0)
				(length 7.62)
				(name "P6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 31.75 0)
				(length 7.62)
				(name "P7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 29.21 0)
				(length 7.62)
				(name "I/O/GCK1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 26.67 0)
				(length 7.62)
				(name "I/O/GCK2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 24.13 0)
				(length 7.62)
				(name "P11"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 21.59 0)
				(length 7.62)
				(name "I/O/GCK3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 19.05 0)
				(length 7.62)
				(name "P13"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 13.97 0)
				(length 7.62)
				(name "P14"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 11.43 0)
				(length 7.62)
				(name "P15"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 8.89 0)
				(length 7.62)
				(name "P17"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "17"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 6.35 0)
				(length 7.62)
				(name "P18"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "18"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 3.81 0)
				(length 7.62)
				(name "P19"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "19"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 1.27 0)
				(length 7.62)
				(name "P20"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "20"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -1.27 0)
				(length 7.62)
				(name "P21"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "21"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -3.81 0)
				(length 7.62)
				(name "P23"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "23"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -6.35 0)
				(length 7.62)
				(name "P24"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "24"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -8.89 0)
				(length 7.62)
				(name "P25"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "25"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -11.43 0)
				(length 7.62)
				(name "P26"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "26"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -13.97 0)
				(length 7.62)
				(name "P31"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "31"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -19.05 0)
				(length 7.62)
				(name "P32"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "32"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -21.59 0)
				(length 7.62)
				(name "P33"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "33"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -24.13 0)
				(length 7.62)
				(name "P34"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "34"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -26.67 0)
				(length 7.62)
				(name "P35"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "35"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -29.21 0)
				(length 7.62)
				(name "P36"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "36"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -31.75 0)
				(length 7.62)
				(name "P37"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "37"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -34.29 0)
				(length 7.62)
				(name "P39"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "39"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -36.83 0)
				(length 7.62)
				(name "P40"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "40"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -39.37 0)
				(length 7.62)
				(name "P41"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "41"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -41.91 0)
				(length 7.62)
				(name "P43"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "43"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at -29.21 -44.45 0)
				(length 7.62)
				(name "P44"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "44"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at -7.62 69.85 270)
				(length 7.62)
				(name "VCC"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "38"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at -6.35 -54.61 90)
				(length 7.62)
				(name "GND"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at -5.08 69.85 270)
				(length 7.62)
				(name "VCC"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "73"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at -3.81 -54.61 90)
				(length 7.62)
				(name "GND"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at -2.54 69.85 270)
				(length 7.62)
				(name "VCC"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "78"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at -1.27 -54.61 90)
				(length 7.62)
				(name "GND"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "27"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at 1.27 -54.61 90)
				(length 7.62)
				(name "GND"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "42"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at 2.54 69.85 270)
				(length 7.62)
				(name "VCCIO"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "22"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at 3.81 -54.61 90)
				(length 7.62)
				(name "GND"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "49"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at 5.08 69.85 270)
				(length 7.62)
				(name "VCCIO"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "64"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at 6.35 -54.61 90)
				(length 7.62)
				(name "GND"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "60"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 46.99 180)
				(length 7.62)
				(name "P84"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "84"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 44.45 180)
				(length 7.62)
				(name "P83"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "83"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 41.91 180)
				(length 7.62)
				(name "P82"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "82"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 39.37 180)
				(length 7.62)
				(name "P81"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "81"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 36.83 180)
				(length 7.62)
				(name "P80"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "80"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 34.29 180)
				(length 7.62)
				(name "P79"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "79"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 31.75 180)
				(length 7.62)
				(name "I/O/GTS2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "77"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 29.21 180)
				(length 7.62)
				(name "I/O/GTS1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "76"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 26.67 180)
				(length 7.62)
				(name "P75"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "75"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 24.13 180)
				(length 7.62)
				(name "I/O/GSR"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "74"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 21.59 180)
				(length 7.62)
				(name "P72"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "72"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 19.05 180)
				(length 7.62)
				(name "P71"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "71"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 13.97 180)
				(length 7.62)
				(name "P70"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "70"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 11.43 180)
				(length 7.62)
				(name "P69"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "69"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 8.89 180)
				(length 7.62)
				(name "P68"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "68"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 6.35 180)
				(length 7.62)
				(name "P67"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "67"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 3.81 180)
				(length 7.62)
				(name "P66"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "66"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 1.27 180)
				(length 7.62)
				(name "P65"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "65"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -1.27 180)
				(length 7.62)
				(name "P63"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "63"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -3.81 180)
				(length 7.62)
				(name "P62"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "62"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -6.35 180)
				(length 7.62)
				(name "P61"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "61"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -8.89 180)
				(length 7.62)
				(name "P58"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "58"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -11.43 180)
				(length 7.62)
				(name "P57"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "57"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -19.05 180)
				(length 7.62)
				(name "P56"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "56"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -21.59 180)
				(length 7.62)
				(name "P55"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "55"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -24.13 180)
				(length 7.62)
				(name "P54"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "54"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -26.67 180)
				(length 7.62)
				(name "P53"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "53"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -29.21 180)
				(length 7.62)
				(name "P52"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "52"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -31.75 180)
				(length 7.62)
				(name "P51"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "51"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -34.29 180)
				(length 7.62)
				(name "P50"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "50"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -36.83 180)
				(length 7.62)
				(name "P48"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "48"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -39.37 180)
				(length 7.62)
				(name "P47"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "47"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -41.91 180)
				(length 7.62)
				(name "P46"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "46"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 29.21 -44.45 180)
				(length 7.62)
				(name "P45"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "45"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "74HC14"
		(extends "74HCT04")
		(property "Reference" "U"
			(at 3.81 2.54 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "74HC14"
			(at 5.08 -2.54 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
	)
	(symbol "74LS14"
		(extends "74HCT04")
		(property "Reference" "U"
			(at 3.81 2.54 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "74LS14"
			(at 5.08 -2.54 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
	)
	(symbol "78L05"
		(extends "7805")
		(property "Reference" "U"
			(at 3.81 -4.9784 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "78L05"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
	)
	(symbol "LM7812"
		(extends "7805")
		(property "Reference" "U"
			(at 3.81 -4.9784 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "LM7812"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
	)
)
