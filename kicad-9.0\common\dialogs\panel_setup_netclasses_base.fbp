<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<wxFormBuilder_Project>
  <FileVersion major="1" minor="18"/>
  <object class="Project" expanded="true">
    <property name="code_generation">C++</property>
    <property name="cpp_class_decoration"></property>
    <property name="cpp_disconnect_events">1</property>
    <property name="cpp_event_generation">connect</property>
    <property name="cpp_help_provider">none</property>
    <property name="cpp_namespace"></property>
    <property name="cpp_precompiled_header"></property>
    <property name="cpp_use_array_enum">0</property>
    <property name="cpp_use_enum">1</property>
    <property name="embedded_files_path">res</property>
    <property name="encoding">UTF-8</property>
    <property name="file">panel_setup_netclasses_base</property>
    <property name="first_id">1000</property>
    <property name="internationalize">1</property>
    <property name="lua_skip_events">1</property>
    <property name="lua_ui_table">UI</property>
    <property name="name">panel_setup_netclasses_base</property>
    <property name="path">.</property>
    <property name="php_disconnect_events">0</property>
    <property name="php_disconnect_mode">source_name</property>
    <property name="php_skip_events">1</property>
    <property name="python_disconnect_events">0</property>
    <property name="python_disconnect_mode">source_name</property>
    <property name="python_image_path_wrapper_function_name"></property>
    <property name="python_indent_with_spaces"></property>
    <property name="python_skip_events">1</property>
    <property name="relative_path">1</property>
    <property name="use_microsoft_bom">0</property>
    <property name="use_native_eol">0</property>
    <object class="Panel" expanded="true">
      <property name="aui_managed">0</property>
      <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
      <property name="bg"></property>
      <property name="context_help"></property>
      <property name="context_menu">1</property>
      <property name="drag_accept_files">0</property>
      <property name="enabled">1</property>
      <property name="event_handler">impl_virtual</property>
      <property name="fg"></property>
      <property name="font"></property>
      <property name="hidden">0</property>
      <property name="id">wxID_ANY</property>
      <property name="maximum_size"></property>
      <property name="minimum_size">-1,-1</property>
      <property name="name">PANEL_SETUP_NETCLASSES_BASE</property>
      <property name="pos"></property>
      <property name="size">-1,-1</property>
      <property name="subclass">; forward_declare</property>
      <property name="tooltip"></property>
      <property name="two_step_creation">0</property>
      <property name="window_extra_style"></property>
      <property name="window_name"></property>
      <property name="window_style">wxTAB_TRAVERSAL</property>
      <event name="OnUpdateUI">OnUpdateUI</event>
      <object class="wxBoxSizer" expanded="true">
        <property name="minimum_size"></property>
        <property name="name">bpanelNetClassesSizer</property>
        <property name="orient">wxVERTICAL</property>
        <property name="permission">none</property>
        <object class="sizeritem" expanded="true">
          <property name="border">2</property>
          <property name="flag">wxEXPAND|wxTOP</property>
          <property name="proportion">1</property>
          <object class="wxBoxSizer" expanded="true">
            <property name="minimum_size"></property>
            <property name="name">bMargins</property>
            <property name="orient">wxVERTICAL</property>
            <property name="permission">none</property>
            <object class="sizeritem" expanded="true">
              <property name="border">10</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">1</property>
              <object class="wxSplitterWindow" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_pane_size">160</property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_splitter</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="sashgravity">0.0</property>
                <property name="sashpos">-1</property>
                <property name="sashsize">-1</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="splitmode">wxSPLIT_HORIZONTAL</property>
                <property name="style">wxSP_3DSASH|wxSP_LIVE_UPDATE|wxSP_NO_XP_THEME</property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <object class="splitteritem" expanded="true">
                  <object class="wxPanel" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_netclassesPane</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="subclass">WX_PANEL; widgets/wx_panel.h; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style">wxTAB_TRAVERSAL</property>
                    <object class="wxBoxSizer" expanded="true">
                      <property name="minimum_size"></property>
                      <property name="name">bUpperSizer</property>
                      <property name="orient">wxVERTICAL</property>
                      <property name="permission">none</property>
                      <object class="sizeritem" expanded="true">
                        <property name="border">8</property>
                        <property name="flag">wxTOP|wxLEFT|wxEXPAND</property>
                        <property name="proportion">0</property>
                        <object class="wxStaticText" expanded="true">
                          <property name="BottomDockable">1</property>
                          <property name="LeftDockable">1</property>
                          <property name="RightDockable">1</property>
                          <property name="TopDockable">1</property>
                          <property name="aui_layer">0</property>
                          <property name="aui_name"></property>
                          <property name="aui_position">0</property>
                          <property name="aui_row">0</property>
                          <property name="best_size"></property>
                          <property name="bg"></property>
                          <property name="caption"></property>
                          <property name="caption_visible">1</property>
                          <property name="center_pane">0</property>
                          <property name="close_button">1</property>
                          <property name="context_help"></property>
                          <property name="context_menu">1</property>
                          <property name="default_pane">0</property>
                          <property name="dock">Dock</property>
                          <property name="dock_fixed">0</property>
                          <property name="docking">Left</property>
                          <property name="drag_accept_files">0</property>
                          <property name="enabled">1</property>
                          <property name="fg"></property>
                          <property name="floatable">1</property>
                          <property name="font"></property>
                          <property name="gripper">0</property>
                          <property name="hidden">0</property>
                          <property name="id">wxID_ANY</property>
                          <property name="label">Netclasses</property>
                          <property name="markup">0</property>
                          <property name="max_size"></property>
                          <property name="maximize_button">0</property>
                          <property name="maximum_size"></property>
                          <property name="min_size"></property>
                          <property name="minimize_button">0</property>
                          <property name="minimum_size"></property>
                          <property name="moveable">1</property>
                          <property name="name">m_staticText3</property>
                          <property name="pane_border">1</property>
                          <property name="pane_position"></property>
                          <property name="pane_size"></property>
                          <property name="permission">protected</property>
                          <property name="pin_button">1</property>
                          <property name="pos"></property>
                          <property name="resize">Resizable</property>
                          <property name="show">1</property>
                          <property name="size"></property>
                          <property name="style"></property>
                          <property name="subclass">; ; forward_declare</property>
                          <property name="toolbar_pane">0</property>
                          <property name="tooltip"></property>
                          <property name="window_extra_style"></property>
                          <property name="window_name"></property>
                          <property name="window_style"></property>
                          <property name="wrap">-1</property>
                        </object>
                      </object>
                      <object class="sizeritem" expanded="true">
                        <property name="border">5</property>
                        <property name="flag">wxEXPAND</property>
                        <property name="proportion">0</property>
                        <object class="spacer" expanded="true">
                          <property name="height">3</property>
                          <property name="permission">protected</property>
                          <property name="width">0</property>
                        </object>
                      </object>
                      <object class="sizeritem" expanded="false">
                        <property name="border">5</property>
                        <property name="flag">wxEXPAND|wxFIXED_MINSIZE|wxLEFT|wxRIGHT|wxTOP</property>
                        <property name="proportion">1</property>
                        <object class="wxGrid" expanded="false">
                          <property name="BottomDockable">1</property>
                          <property name="LeftDockable">1</property>
                          <property name="RightDockable">1</property>
                          <property name="TopDockable">1</property>
                          <property name="aui_layer">0</property>
                          <property name="aui_name"></property>
                          <property name="aui_position">0</property>
                          <property name="aui_row">0</property>
                          <property name="autosize_cols">0</property>
                          <property name="autosize_rows">0</property>
                          <property name="best_size"></property>
                          <property name="bg"></property>
                          <property name="caption"></property>
                          <property name="caption_visible">1</property>
                          <property name="cell_bg"></property>
                          <property name="cell_font"></property>
                          <property name="cell_horiz_alignment">wxALIGN_LEFT</property>
                          <property name="cell_text"></property>
                          <property name="cell_vert_alignment">wxALIGN_CENTER</property>
                          <property name="center_pane">0</property>
                          <property name="close_button">1</property>
                          <property name="col_label_horiz_alignment">wxALIGN_CENTER</property>
                          <property name="col_label_size">wxGRID_AUTOSIZE</property>
                          <property name="col_label_values">&quot;Name&quot; &quot;Clearance&quot; &quot;Track Width&quot; &quot;Via Size&quot; &quot;Via Hole&quot; &quot;uVia Size&quot; &quot;uVia Hole&quot; &quot;DP Width&quot; &quot;DP Gap&quot; &quot;PCB Color&quot; &quot;Wire Thickness&quot; &quot;Bus Thickness&quot; &quot;Color&quot; &quot;Line Style&quot;</property>
                          <property name="col_label_vert_alignment">wxALIGN_CENTER</property>
                          <property name="cols">14</property>
                          <property name="column_sizes"></property>
                          <property name="context_help"></property>
                          <property name="context_menu">1</property>
                          <property name="default_pane">0</property>
                          <property name="dock">Dock</property>
                          <property name="dock_fixed">0</property>
                          <property name="docking">Left</property>
                          <property name="drag_accept_files">0</property>
                          <property name="drag_col_move">0</property>
                          <property name="drag_col_size">1</property>
                          <property name="drag_grid_size">0</property>
                          <property name="drag_row_size">1</property>
                          <property name="editing">1</property>
                          <property name="enabled">1</property>
                          <property name="fg"></property>
                          <property name="floatable">1</property>
                          <property name="font"></property>
                          <property name="grid_line_color"></property>
                          <property name="grid_lines">1</property>
                          <property name="gripper">0</property>
                          <property name="hidden">0</property>
                          <property name="id">wxID_ANY</property>
                          <property name="label_bg"></property>
                          <property name="label_font"></property>
                          <property name="label_text"></property>
                          <property name="margin_height">0</property>
                          <property name="margin_width">0</property>
                          <property name="max_size"></property>
                          <property name="maximize_button">0</property>
                          <property name="maximum_size"></property>
                          <property name="min_size"></property>
                          <property name="minimize_button">0</property>
                          <property name="minimum_size">-1,-1</property>
                          <property name="moveable">1</property>
                          <property name="name">m_netclassGrid</property>
                          <property name="pane_border">1</property>
                          <property name="pane_position"></property>
                          <property name="pane_size"></property>
                          <property name="permission">protected</property>
                          <property name="pin_button">1</property>
                          <property name="pos"></property>
                          <property name="resize">Resizable</property>
                          <property name="row_label_horiz_alignment">wxALIGN_LEFT</property>
                          <property name="row_label_size">0</property>
                          <property name="row_label_values">&quot;Default&quot;</property>
                          <property name="row_label_vert_alignment">wxALIGN_CENTER</property>
                          <property name="row_sizes"></property>
                          <property name="rows">3</property>
                          <property name="show">1</property>
                          <property name="size"></property>
                          <property name="subclass">WX_GRID; widgets/wx_grid.h; forward_declare</property>
                          <property name="toolbar_pane">0</property>
                          <property name="tooltip"></property>
                          <property name="window_extra_style"></property>
                          <property name="window_name"></property>
                          <property name="window_style">wxHSCROLL|wxTAB_TRAVERSAL|wxVSCROLL</property>
                          <event name="OnSize">OnSizeNetclassGrid</event>
                        </object>
                      </object>
                      <object class="sizeritem" expanded="true">
                        <property name="border">5</property>
                        <property name="flag">wxEXPAND|wxLEFT|wxTOP</property>
                        <property name="proportion">0</property>
                        <object class="wxBoxSizer" expanded="true">
                          <property name="minimum_size"></property>
                          <property name="name">buttonBoxSizer</property>
                          <property name="orient">wxHORIZONTAL</property>
                          <property name="permission">none</property>
                          <object class="sizeritem" expanded="false">
                            <property name="border">2</property>
                            <property name="flag">wxLEFT</property>
                            <property name="proportion">0</property>
                            <object class="wxBitmapButton" expanded="false">
                              <property name="BottomDockable">1</property>
                              <property name="LeftDockable">1</property>
                              <property name="RightDockable">1</property>
                              <property name="TopDockable">1</property>
                              <property name="aui_layer">0</property>
                              <property name="aui_name"></property>
                              <property name="aui_position">0</property>
                              <property name="aui_row">0</property>
                              <property name="auth_needed">0</property>
                              <property name="best_size"></property>
                              <property name="bg"></property>
                              <property name="bitmap"></property>
                              <property name="caption"></property>
                              <property name="caption_visible">1</property>
                              <property name="center_pane">0</property>
                              <property name="close_button">1</property>
                              <property name="context_help"></property>
                              <property name="context_menu">1</property>
                              <property name="current"></property>
                              <property name="default">0</property>
                              <property name="default_pane">0</property>
                              <property name="disabled"></property>
                              <property name="dock">Dock</property>
                              <property name="dock_fixed">0</property>
                              <property name="docking">Left</property>
                              <property name="drag_accept_files">0</property>
                              <property name="enabled">1</property>
                              <property name="fg"></property>
                              <property name="floatable">1</property>
                              <property name="focus"></property>
                              <property name="font"></property>
                              <property name="gripper">0</property>
                              <property name="hidden">0</property>
                              <property name="id">wxID_ANY</property>
                              <property name="label">Add Net Class</property>
                              <property name="margins"></property>
                              <property name="markup">0</property>
                              <property name="max_size"></property>
                              <property name="maximize_button">0</property>
                              <property name="maximum_size"></property>
                              <property name="min_size"></property>
                              <property name="minimize_button">0</property>
                              <property name="minimum_size">-1,-1</property>
                              <property name="moveable">1</property>
                              <property name="name">m_addButton</property>
                              <property name="pane_border">1</property>
                              <property name="pane_position"></property>
                              <property name="pane_size"></property>
                              <property name="permission">protected</property>
                              <property name="pin_button">1</property>
                              <property name="pos"></property>
                              <property name="position"></property>
                              <property name="pressed"></property>
                              <property name="resize">Resizable</property>
                              <property name="show">1</property>
                              <property name="size">-1,-1</property>
                              <property name="style"></property>
                              <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                              <property name="toolbar_pane">0</property>
                              <property name="tooltip"></property>
                              <property name="validator_data_type"></property>
                              <property name="validator_style">wxFILTER_NONE</property>
                              <property name="validator_type">wxDefaultValidator</property>
                              <property name="validator_variable"></property>
                              <property name="window_extra_style"></property>
                              <property name="window_name"></property>
                              <property name="window_style"></property>
                              <event name="OnButtonClick">OnAddNetclassClick</event>
                            </object>
                          </object>
                          <object class="sizeritem" expanded="true">
                            <property name="border">5</property>
                            <property name="flag">wxEXPAND</property>
                            <property name="proportion">0</property>
                            <object class="spacer" expanded="true">
                              <property name="height">0</property>
                              <property name="permission">protected</property>
                              <property name="width">20</property>
                            </object>
                          </object>
                          <object class="sizeritem" expanded="false">
                            <property name="border">5</property>
                            <property name="flag">wxBOTTOM|wxLEFT</property>
                            <property name="proportion">0</property>
                            <object class="wxBitmapButton" expanded="false">
                              <property name="BottomDockable">1</property>
                              <property name="LeftDockable">1</property>
                              <property name="RightDockable">1</property>
                              <property name="TopDockable">1</property>
                              <property name="aui_layer">0</property>
                              <property name="aui_name"></property>
                              <property name="aui_position">0</property>
                              <property name="aui_row">0</property>
                              <property name="auth_needed">0</property>
                              <property name="best_size"></property>
                              <property name="bg"></property>
                              <property name="bitmap"></property>
                              <property name="caption"></property>
                              <property name="caption_visible">1</property>
                              <property name="center_pane">0</property>
                              <property name="close_button">1</property>
                              <property name="context_help"></property>
                              <property name="context_menu">1</property>
                              <property name="current"></property>
                              <property name="default">0</property>
                              <property name="default_pane">0</property>
                              <property name="disabled"></property>
                              <property name="dock">Dock</property>
                              <property name="dock_fixed">0</property>
                              <property name="docking">Left</property>
                              <property name="drag_accept_files">0</property>
                              <property name="enabled">1</property>
                              <property name="fg"></property>
                              <property name="floatable">1</property>
                              <property name="focus"></property>
                              <property name="font"></property>
                              <property name="gripper">0</property>
                              <property name="hidden">0</property>
                              <property name="id">wxID_ANY</property>
                              <property name="label">Delete Net Class</property>
                              <property name="margins"></property>
                              <property name="markup">0</property>
                              <property name="max_size"></property>
                              <property name="maximize_button">0</property>
                              <property name="maximum_size"></property>
                              <property name="min_size"></property>
                              <property name="minimize_button">0</property>
                              <property name="minimum_size">-1,-1</property>
                              <property name="moveable">1</property>
                              <property name="name">m_removeButton</property>
                              <property name="pane_border">1</property>
                              <property name="pane_position"></property>
                              <property name="pane_size"></property>
                              <property name="permission">protected</property>
                              <property name="pin_button">1</property>
                              <property name="pos"></property>
                              <property name="position"></property>
                              <property name="pressed"></property>
                              <property name="resize">Resizable</property>
                              <property name="show">1</property>
                              <property name="size">-1,-1</property>
                              <property name="style"></property>
                              <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                              <property name="toolbar_pane">0</property>
                              <property name="tooltip"></property>
                              <property name="validator_data_type"></property>
                              <property name="validator_style">wxFILTER_NONE</property>
                              <property name="validator_type">wxDefaultValidator</property>
                              <property name="validator_variable"></property>
                              <property name="window_extra_style"></property>
                              <property name="window_name"></property>
                              <property name="window_style"></property>
                              <event name="OnButtonClick">OnRemoveNetclassClick</event>
                            </object>
                          </object>
                          <object class="sizeritem" expanded="true">
                            <property name="border">5</property>
                            <property name="flag">wxEXPAND</property>
                            <property name="proportion">0</property>
                            <object class="spacer" expanded="true">
                              <property name="height">0</property>
                              <property name="permission">protected</property>
                              <property name="width">20</property>
                            </object>
                          </object>
                          <object class="sizeritem" expanded="true">
                            <property name="border">5</property>
                            <property name="flag">wxBOTTOM|wxLEFT</property>
                            <property name="proportion">0</property>
                            <object class="wxBitmapButton" expanded="true">
                              <property name="BottomDockable">1</property>
                              <property name="LeftDockable">1</property>
                              <property name="RightDockable">1</property>
                              <property name="TopDockable">1</property>
                              <property name="aui_layer">0</property>
                              <property name="aui_name"></property>
                              <property name="aui_position">0</property>
                              <property name="aui_row">0</property>
                              <property name="auth_needed">0</property>
                              <property name="best_size"></property>
                              <property name="bg"></property>
                              <property name="bitmap"></property>
                              <property name="caption"></property>
                              <property name="caption_visible">1</property>
                              <property name="center_pane">0</property>
                              <property name="close_button">1</property>
                              <property name="context_help"></property>
                              <property name="context_menu">1</property>
                              <property name="current"></property>
                              <property name="default">0</property>
                              <property name="default_pane">0</property>
                              <property name="disabled"></property>
                              <property name="dock">Dock</property>
                              <property name="dock_fixed">0</property>
                              <property name="docking">Left</property>
                              <property name="drag_accept_files">0</property>
                              <property name="enabled">1</property>
                              <property name="fg"></property>
                              <property name="floatable">1</property>
                              <property name="focus"></property>
                              <property name="font"></property>
                              <property name="gripper">0</property>
                              <property name="hidden">0</property>
                              <property name="id">wxID_ANY</property>
                              <property name="label">Delete Net Class</property>
                              <property name="margins"></property>
                              <property name="markup">0</property>
                              <property name="max_size"></property>
                              <property name="maximize_button">0</property>
                              <property name="maximum_size"></property>
                              <property name="min_size"></property>
                              <property name="minimize_button">0</property>
                              <property name="minimum_size">-1,-1</property>
                              <property name="moveable">1</property>
                              <property name="name">m_moveUpButton</property>
                              <property name="pane_border">1</property>
                              <property name="pane_position"></property>
                              <property name="pane_size"></property>
                              <property name="permission">protected</property>
                              <property name="pin_button">1</property>
                              <property name="pos"></property>
                              <property name="position"></property>
                              <property name="pressed"></property>
                              <property name="resize">Resizable</property>
                              <property name="show">1</property>
                              <property name="size">-1,-1</property>
                              <property name="style"></property>
                              <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                              <property name="toolbar_pane">0</property>
                              <property name="tooltip"></property>
                              <property name="validator_data_type"></property>
                              <property name="validator_style">wxFILTER_NONE</property>
                              <property name="validator_type">wxDefaultValidator</property>
                              <property name="validator_variable"></property>
                              <property name="window_extra_style"></property>
                              <property name="window_name"></property>
                              <property name="window_style"></property>
                              <event name="OnButtonClick">OnMoveNetclassUpClick</event>
                            </object>
                          </object>
                          <object class="sizeritem" expanded="true">
                            <property name="border">5</property>
                            <property name="flag">wxEXPAND</property>
                            <property name="proportion">0</property>
                            <object class="spacer" expanded="true">
                              <property name="height">0</property>
                              <property name="permission">protected</property>
                              <property name="width">20</property>
                            </object>
                          </object>
                          <object class="sizeritem" expanded="true">
                            <property name="border">5</property>
                            <property name="flag">wxBOTTOM|wxLEFT|wxRIGHT</property>
                            <property name="proportion">0</property>
                            <object class="wxBitmapButton" expanded="true">
                              <property name="BottomDockable">1</property>
                              <property name="LeftDockable">1</property>
                              <property name="RightDockable">1</property>
                              <property name="TopDockable">1</property>
                              <property name="aui_layer">0</property>
                              <property name="aui_name"></property>
                              <property name="aui_position">0</property>
                              <property name="aui_row">0</property>
                              <property name="auth_needed">0</property>
                              <property name="best_size"></property>
                              <property name="bg"></property>
                              <property name="bitmap"></property>
                              <property name="caption"></property>
                              <property name="caption_visible">1</property>
                              <property name="center_pane">0</property>
                              <property name="close_button">1</property>
                              <property name="context_help"></property>
                              <property name="context_menu">1</property>
                              <property name="current"></property>
                              <property name="default">0</property>
                              <property name="default_pane">0</property>
                              <property name="disabled"></property>
                              <property name="dock">Dock</property>
                              <property name="dock_fixed">0</property>
                              <property name="docking">Left</property>
                              <property name="drag_accept_files">0</property>
                              <property name="enabled">1</property>
                              <property name="fg"></property>
                              <property name="floatable">1</property>
                              <property name="focus"></property>
                              <property name="font"></property>
                              <property name="gripper">0</property>
                              <property name="hidden">0</property>
                              <property name="id">wxID_ANY</property>
                              <property name="label">Delete Net Class</property>
                              <property name="margins"></property>
                              <property name="markup">0</property>
                              <property name="max_size"></property>
                              <property name="maximize_button">0</property>
                              <property name="maximum_size"></property>
                              <property name="min_size"></property>
                              <property name="minimize_button">0</property>
                              <property name="minimum_size">-1,-1</property>
                              <property name="moveable">1</property>
                              <property name="name">m_moveDownButton</property>
                              <property name="pane_border">1</property>
                              <property name="pane_position"></property>
                              <property name="pane_size"></property>
                              <property name="permission">protected</property>
                              <property name="pin_button">1</property>
                              <property name="pos"></property>
                              <property name="position"></property>
                              <property name="pressed"></property>
                              <property name="resize">Resizable</property>
                              <property name="show">1</property>
                              <property name="size">-1,-1</property>
                              <property name="style"></property>
                              <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                              <property name="toolbar_pane">0</property>
                              <property name="tooltip"></property>
                              <property name="validator_data_type"></property>
                              <property name="validator_style">wxFILTER_NONE</property>
                              <property name="validator_type">wxDefaultValidator</property>
                              <property name="validator_variable"></property>
                              <property name="window_extra_style"></property>
                              <property name="window_name"></property>
                              <property name="window_style"></property>
                              <event name="OnButtonClick">OnMoveNetclassDownClick</event>
                            </object>
                          </object>
                          <object class="sizeritem" expanded="true">
                            <property name="border">5</property>
                            <property name="flag">wxEXPAND</property>
                            <property name="proportion">1</property>
                            <object class="spacer" expanded="true">
                              <property name="height">0</property>
                              <property name="permission">protected</property>
                              <property name="width">60</property>
                            </object>
                          </object>
                          <object class="sizeritem" expanded="true">
                            <property name="border">10</property>
                            <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT</property>
                            <property name="proportion">0</property>
                            <object class="wxStaticText" expanded="true">
                              <property name="BottomDockable">1</property>
                              <property name="LeftDockable">1</property>
                              <property name="RightDockable">1</property>
                              <property name="TopDockable">1</property>
                              <property name="aui_layer">0</property>
                              <property name="aui_name"></property>
                              <property name="aui_position">0</property>
                              <property name="aui_row">0</property>
                              <property name="best_size"></property>
                              <property name="bg"></property>
                              <property name="caption"></property>
                              <property name="caption_visible">1</property>
                              <property name="center_pane">0</property>
                              <property name="close_button">1</property>
                              <property name="context_help"></property>
                              <property name="context_menu">1</property>
                              <property name="default_pane">0</property>
                              <property name="dock">Dock</property>
                              <property name="dock_fixed">0</property>
                              <property name="docking">Left</property>
                              <property name="drag_accept_files">0</property>
                              <property name="enabled">1</property>
                              <property name="fg"></property>
                              <property name="floatable">1</property>
                              <property name="font"></property>
                              <property name="gripper">0</property>
                              <property name="hidden">0</property>
                              <property name="id">wxID_ANY</property>
                              <property name="label">Set color to transparent to use KiCad default color.</property>
                              <property name="markup">0</property>
                              <property name="max_size"></property>
                              <property name="maximize_button">0</property>
                              <property name="maximum_size"></property>
                              <property name="min_size"></property>
                              <property name="minimize_button">0</property>
                              <property name="minimum_size"></property>
                              <property name="moveable">1</property>
                              <property name="name">m_colorDefaultHelpText</property>
                              <property name="pane_border">1</property>
                              <property name="pane_position"></property>
                              <property name="pane_size"></property>
                              <property name="permission">protected</property>
                              <property name="pin_button">1</property>
                              <property name="pos"></property>
                              <property name="resize">Resizable</property>
                              <property name="show">1</property>
                              <property name="size"></property>
                              <property name="style"></property>
                              <property name="subclass">; ; forward_declare</property>
                              <property name="toolbar_pane">0</property>
                              <property name="tooltip"></property>
                              <property name="window_extra_style"></property>
                              <property name="window_name"></property>
                              <property name="window_style"></property>
                              <property name="wrap">-1</property>
                            </object>
                          </object>
                          <object class="sizeritem" expanded="true">
                            <property name="border">5</property>
                            <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxRIGHT</property>
                            <property name="proportion">0</property>
                            <object class="wxButton" expanded="true">
                              <property name="BottomDockable">1</property>
                              <property name="LeftDockable">1</property>
                              <property name="RightDockable">1</property>
                              <property name="TopDockable">1</property>
                              <property name="aui_layer">0</property>
                              <property name="aui_name"></property>
                              <property name="aui_position">0</property>
                              <property name="aui_row">0</property>
                              <property name="auth_needed">0</property>
                              <property name="best_size"></property>
                              <property name="bg"></property>
                              <property name="bitmap"></property>
                              <property name="caption"></property>
                              <property name="caption_visible">1</property>
                              <property name="center_pane">0</property>
                              <property name="close_button">1</property>
                              <property name="context_help"></property>
                              <property name="context_menu">1</property>
                              <property name="current"></property>
                              <property name="default">0</property>
                              <property name="default_pane">0</property>
                              <property name="disabled"></property>
                              <property name="dock">Dock</property>
                              <property name="dock_fixed">0</property>
                              <property name="docking">Left</property>
                              <property name="drag_accept_files">0</property>
                              <property name="enabled">1</property>
                              <property name="fg"></property>
                              <property name="floatable">1</property>
                              <property name="focus"></property>
                              <property name="font"></property>
                              <property name="gripper">0</property>
                              <property name="hidden">0</property>
                              <property name="id">wxID_ANY</property>
                              <property name="label">Import colors from schematic</property>
                              <property name="margins"></property>
                              <property name="markup">0</property>
                              <property name="max_size"></property>
                              <property name="maximize_button">0</property>
                              <property name="maximum_size"></property>
                              <property name="min_size"></property>
                              <property name="minimize_button">0</property>
                              <property name="minimum_size"></property>
                              <property name="moveable">1</property>
                              <property name="name">m_importColorsButton</property>
                              <property name="pane_border">1</property>
                              <property name="pane_position"></property>
                              <property name="pane_size"></property>
                              <property name="permission">protected</property>
                              <property name="pin_button">1</property>
                              <property name="pos"></property>
                              <property name="position"></property>
                              <property name="pressed"></property>
                              <property name="resize">Resizable</property>
                              <property name="show">1</property>
                              <property name="size"></property>
                              <property name="style"></property>
                              <property name="subclass">; ; forward_declare</property>
                              <property name="toolbar_pane">0</property>
                              <property name="tooltip"></property>
                              <property name="validator_data_type"></property>
                              <property name="validator_style">wxFILTER_NONE</property>
                              <property name="validator_type">wxDefaultValidator</property>
                              <property name="validator_variable"></property>
                              <property name="window_extra_style"></property>
                              <property name="window_name"></property>
                              <property name="window_style"></property>
                              <event name="OnButtonClick">OnImportColorsClick</event>
                            </object>
                          </object>
                        </object>
                      </object>
                    </object>
                  </object>
                </object>
                <object class="splitteritem" expanded="true">
                  <object class="wxPanel" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_membershipPane</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="subclass">WX_PANEL; widgets/wx_panel.h; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style">wxTAB_TRAVERSAL</property>
                    <event name="OnSize">onmembershipPanelSize</event>
                    <object class="wxBoxSizer" expanded="true">
                      <property name="minimum_size"></property>
                      <property name="name">bLowerSizer</property>
                      <property name="orient">wxVERTICAL</property>
                      <property name="permission">none</property>
                      <object class="sizeritem" expanded="true">
                        <property name="border">5</property>
                        <property name="flag">wxEXPAND</property>
                        <property name="proportion">0</property>
                        <object class="spacer" expanded="true">
                          <property name="height">5</property>
                          <property name="permission">protected</property>
                          <property name="width">0</property>
                        </object>
                      </object>
                      <object class="sizeritem" expanded="true">
                        <property name="border">8</property>
                        <property name="flag">wxEXPAND|wxTOP|wxLEFT</property>
                        <property name="proportion">0</property>
                        <object class="wxStaticText" expanded="true">
                          <property name="BottomDockable">1</property>
                          <property name="LeftDockable">1</property>
                          <property name="RightDockable">1</property>
                          <property name="TopDockable">1</property>
                          <property name="aui_layer">0</property>
                          <property name="aui_name"></property>
                          <property name="aui_position">0</property>
                          <property name="aui_row">0</property>
                          <property name="best_size"></property>
                          <property name="bg"></property>
                          <property name="caption"></property>
                          <property name="caption_visible">1</property>
                          <property name="center_pane">0</property>
                          <property name="close_button">1</property>
                          <property name="context_help"></property>
                          <property name="context_menu">1</property>
                          <property name="default_pane">0</property>
                          <property name="dock">Dock</property>
                          <property name="dock_fixed">0</property>
                          <property name="docking">Left</property>
                          <property name="drag_accept_files">0</property>
                          <property name="enabled">1</property>
                          <property name="fg"></property>
                          <property name="floatable">1</property>
                          <property name="font"></property>
                          <property name="gripper">0</property>
                          <property name="hidden">0</property>
                          <property name="id">wxID_ANY</property>
                          <property name="label">Netclass Assignments</property>
                          <property name="markup">0</property>
                          <property name="max_size"></property>
                          <property name="maximize_button">0</property>
                          <property name="maximum_size"></property>
                          <property name="min_size"></property>
                          <property name="minimize_button">0</property>
                          <property name="minimum_size"></property>
                          <property name="moveable">1</property>
                          <property name="name">m_staticText5</property>
                          <property name="pane_border">1</property>
                          <property name="pane_position"></property>
                          <property name="pane_size"></property>
                          <property name="permission">protected</property>
                          <property name="pin_button">1</property>
                          <property name="pos"></property>
                          <property name="resize">Resizable</property>
                          <property name="show">1</property>
                          <property name="size"></property>
                          <property name="style"></property>
                          <property name="subclass">; ; forward_declare</property>
                          <property name="toolbar_pane">0</property>
                          <property name="tooltip"></property>
                          <property name="window_extra_style"></property>
                          <property name="window_name"></property>
                          <property name="window_style"></property>
                          <property name="wrap">-1</property>
                        </object>
                      </object>
                      <object class="sizeritem" expanded="true">
                        <property name="border">3</property>
                        <property name="flag">wxEXPAND|wxTOP</property>
                        <property name="proportion">1</property>
                        <object class="wxBoxSizer" expanded="true">
                          <property name="minimum_size"></property>
                          <property name="name">bColumns</property>
                          <property name="orient">wxHORIZONTAL</property>
                          <property name="permission">none</property>
                          <object class="sizeritem" expanded="true">
                            <property name="border">5</property>
                            <property name="flag">wxEXPAND</property>
                            <property name="proportion">3</property>
                            <object class="wxBoxSizer" expanded="true">
                              <property name="minimum_size"></property>
                              <property name="name">bSizer14</property>
                              <property name="orient">wxVERTICAL</property>
                              <property name="permission">none</property>
                              <object class="sizeritem" expanded="false">
                                <property name="border">5</property>
                                <property name="flag">wxEXPAND|wxFIXED_MINSIZE|wxLEFT</property>
                                <property name="proportion">2</property>
                                <object class="wxGrid" expanded="false">
                                  <property name="BottomDockable">1</property>
                                  <property name="LeftDockable">1</property>
                                  <property name="RightDockable">1</property>
                                  <property name="TopDockable">1</property>
                                  <property name="aui_layer">0</property>
                                  <property name="aui_name"></property>
                                  <property name="aui_position">0</property>
                                  <property name="aui_row">0</property>
                                  <property name="autosize_cols">0</property>
                                  <property name="autosize_rows">0</property>
                                  <property name="best_size"></property>
                                  <property name="bg"></property>
                                  <property name="caption"></property>
                                  <property name="caption_visible">1</property>
                                  <property name="cell_bg"></property>
                                  <property name="cell_font"></property>
                                  <property name="cell_horiz_alignment">wxALIGN_LEFT</property>
                                  <property name="cell_text"></property>
                                  <property name="cell_vert_alignment">wxALIGN_CENTER</property>
                                  <property name="center_pane">0</property>
                                  <property name="close_button">1</property>
                                  <property name="col_label_horiz_alignment">wxALIGN_CENTER</property>
                                  <property name="col_label_size">wxGRID_AUTOSIZE</property>
                                  <property name="col_label_values">&quot;Pattern&quot; &quot;Net Class&quot;</property>
                                  <property name="col_label_vert_alignment">wxALIGN_CENTER</property>
                                  <property name="cols">2</property>
                                  <property name="column_sizes">270,160</property>
                                  <property name="context_help"></property>
                                  <property name="context_menu">1</property>
                                  <property name="default_pane">0</property>
                                  <property name="dock">Dock</property>
                                  <property name="dock_fixed">0</property>
                                  <property name="docking">Left</property>
                                  <property name="drag_accept_files">0</property>
                                  <property name="drag_col_move">0</property>
                                  <property name="drag_col_size">1</property>
                                  <property name="drag_grid_size">0</property>
                                  <property name="drag_row_size">1</property>
                                  <property name="editing">1</property>
                                  <property name="enabled">1</property>
                                  <property name="fg"></property>
                                  <property name="floatable">1</property>
                                  <property name="font"></property>
                                  <property name="grid_line_color"></property>
                                  <property name="grid_lines">1</property>
                                  <property name="gripper">0</property>
                                  <property name="hidden">0</property>
                                  <property name="id">wxID_ANY</property>
                                  <property name="label_bg"></property>
                                  <property name="label_font"></property>
                                  <property name="label_text"></property>
                                  <property name="margin_height">0</property>
                                  <property name="margin_width">0</property>
                                  <property name="max_size"></property>
                                  <property name="maximize_button">0</property>
                                  <property name="maximum_size"></property>
                                  <property name="min_size"></property>
                                  <property name="minimize_button">0</property>
                                  <property name="minimum_size">-1,-1</property>
                                  <property name="moveable">1</property>
                                  <property name="name">m_assignmentGrid</property>
                                  <property name="pane_border">1</property>
                                  <property name="pane_position"></property>
                                  <property name="pane_size"></property>
                                  <property name="permission">protected</property>
                                  <property name="pin_button">1</property>
                                  <property name="pos"></property>
                                  <property name="resize">Resizable</property>
                                  <property name="row_label_horiz_alignment">wxALIGN_CENTER</property>
                                  <property name="row_label_size">0</property>
                                  <property name="row_label_values"></property>
                                  <property name="row_label_vert_alignment">wxALIGN_CENTER</property>
                                  <property name="row_sizes"></property>
                                  <property name="rows">5</property>
                                  <property name="show">1</property>
                                  <property name="size"></property>
                                  <property name="subclass">WX_GRID; widgets/wx_grid.h; forward_declare</property>
                                  <property name="toolbar_pane">0</property>
                                  <property name="tooltip"></property>
                                  <property name="window_extra_style"></property>
                                  <property name="window_name"></property>
                                  <property name="window_style">wxBORDER_DEFAULT</property>
                                  <event name="OnSize">OnSizeAssignmentGrid</event>
                                  <event name="OnUpdateUI">OnUpdateUI</event>
                                </object>
                              </object>
                            </object>
                          </object>
                          <object class="sizeritem" expanded="true">
                            <property name="border">5</property>
                            <property name="flag">wxEXPAND|wxLEFT|wxRIGHT</property>
                            <property name="proportion">2</property>
                            <object class="wxHtmlWindow" expanded="true">
                              <property name="BottomDockable">1</property>
                              <property name="LeftDockable">1</property>
                              <property name="RightDockable">1</property>
                              <property name="TopDockable">1</property>
                              <property name="aui_layer">0</property>
                              <property name="aui_name"></property>
                              <property name="aui_position">0</property>
                              <property name="aui_row">0</property>
                              <property name="best_size"></property>
                              <property name="bg"></property>
                              <property name="caption"></property>
                              <property name="caption_visible">1</property>
                              <property name="center_pane">0</property>
                              <property name="close_button">1</property>
                              <property name="context_help"></property>
                              <property name="context_menu">1</property>
                              <property name="default_pane">0</property>
                              <property name="dock">Dock</property>
                              <property name="dock_fixed">0</property>
                              <property name="docking">Left</property>
                              <property name="drag_accept_files">0</property>
                              <property name="enabled">1</property>
                              <property name="fg"></property>
                              <property name="floatable">1</property>
                              <property name="font"></property>
                              <property name="gripper">0</property>
                              <property name="hidden">0</property>
                              <property name="id">wxID_ANY</property>
                              <property name="max_size"></property>
                              <property name="maximize_button">0</property>
                              <property name="maximum_size"></property>
                              <property name="min_size"></property>
                              <property name="minimize_button">0</property>
                              <property name="minimum_size"></property>
                              <property name="moveable">1</property>
                              <property name="name">m_matchingNets</property>
                              <property name="pane_border">1</property>
                              <property name="pane_position"></property>
                              <property name="pane_size"></property>
                              <property name="permission">protected</property>
                              <property name="pin_button">1</property>
                              <property name="pos"></property>
                              <property name="resize">Resizable</property>
                              <property name="show">1</property>
                              <property name="size"></property>
                              <property name="style">wxHW_SCROLLBAR_AUTO</property>
                              <property name="subclass">WX_HTML_REPORT_BOX; widgets/wx_html_report_box.h; forward_declare</property>
                              <property name="toolbar_pane">0</property>
                              <property name="tooltip"></property>
                              <property name="window_extra_style"></property>
                              <property name="window_name"></property>
                              <property name="window_style"></property>
                            </object>
                          </object>
                        </object>
                      </object>
                      <object class="sizeritem" expanded="true">
                        <property name="border">5</property>
                        <property name="flag">wxEXPAND|wxTOP|wxBOTTOM|wxLEFT</property>
                        <property name="proportion">0</property>
                        <object class="wxBoxSizer" expanded="true">
                          <property name="minimum_size"></property>
                          <property name="name">buttonBoxSizer1</property>
                          <property name="orient">wxHORIZONTAL</property>
                          <property name="permission">none</property>
                          <object class="sizeritem" expanded="true">
                            <property name="border">2</property>
                            <property name="flag">wxLEFT</property>
                            <property name="proportion">0</property>
                            <object class="wxBitmapButton" expanded="true">
                              <property name="BottomDockable">1</property>
                              <property name="LeftDockable">1</property>
                              <property name="RightDockable">1</property>
                              <property name="TopDockable">1</property>
                              <property name="aui_layer">0</property>
                              <property name="aui_name"></property>
                              <property name="aui_position">0</property>
                              <property name="aui_row">0</property>
                              <property name="auth_needed">0</property>
                              <property name="best_size"></property>
                              <property name="bg"></property>
                              <property name="bitmap"></property>
                              <property name="caption"></property>
                              <property name="caption_visible">1</property>
                              <property name="center_pane">0</property>
                              <property name="close_button">1</property>
                              <property name="context_help"></property>
                              <property name="context_menu">1</property>
                              <property name="current"></property>
                              <property name="default">0</property>
                              <property name="default_pane">0</property>
                              <property name="disabled"></property>
                              <property name="dock">Dock</property>
                              <property name="dock_fixed">0</property>
                              <property name="docking">Left</property>
                              <property name="drag_accept_files">0</property>
                              <property name="enabled">1</property>
                              <property name="fg"></property>
                              <property name="floatable">1</property>
                              <property name="focus"></property>
                              <property name="font"></property>
                              <property name="gripper">0</property>
                              <property name="hidden">0</property>
                              <property name="id">wxID_ANY</property>
                              <property name="label">Add Net Class</property>
                              <property name="margins"></property>
                              <property name="markup">0</property>
                              <property name="max_size"></property>
                              <property name="maximize_button">0</property>
                              <property name="maximum_size"></property>
                              <property name="min_size"></property>
                              <property name="minimize_button">0</property>
                              <property name="minimum_size">-1,-1</property>
                              <property name="moveable">1</property>
                              <property name="name">m_addAssignmentButton</property>
                              <property name="pane_border">1</property>
                              <property name="pane_position"></property>
                              <property name="pane_size"></property>
                              <property name="permission">protected</property>
                              <property name="pin_button">1</property>
                              <property name="pos"></property>
                              <property name="position"></property>
                              <property name="pressed"></property>
                              <property name="resize">Resizable</property>
                              <property name="show">1</property>
                              <property name="size">-1,-1</property>
                              <property name="style"></property>
                              <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                              <property name="toolbar_pane">0</property>
                              <property name="tooltip"></property>
                              <property name="validator_data_type"></property>
                              <property name="validator_style">wxFILTER_NONE</property>
                              <property name="validator_type">wxDefaultValidator</property>
                              <property name="validator_variable"></property>
                              <property name="window_extra_style"></property>
                              <property name="window_name"></property>
                              <property name="window_style"></property>
                              <event name="OnButtonClick">OnAddAssignmentClick</event>
                            </object>
                          </object>
                          <object class="sizeritem" expanded="true">
                            <property name="border">5</property>
                            <property name="flag">wxEXPAND</property>
                            <property name="proportion">0</property>
                            <object class="spacer" expanded="true">
                              <property name="height">0</property>
                              <property name="permission">protected</property>
                              <property name="width">20</property>
                            </object>
                          </object>
                          <object class="sizeritem" expanded="true">
                            <property name="border">5</property>
                            <property name="flag">wxRIGHT|wxLEFT</property>
                            <property name="proportion">0</property>
                            <object class="wxBitmapButton" expanded="true">
                              <property name="BottomDockable">1</property>
                              <property name="LeftDockable">1</property>
                              <property name="RightDockable">1</property>
                              <property name="TopDockable">1</property>
                              <property name="aui_layer">0</property>
                              <property name="aui_name"></property>
                              <property name="aui_position">0</property>
                              <property name="aui_row">0</property>
                              <property name="auth_needed">0</property>
                              <property name="best_size"></property>
                              <property name="bg"></property>
                              <property name="bitmap"></property>
                              <property name="caption"></property>
                              <property name="caption_visible">1</property>
                              <property name="center_pane">0</property>
                              <property name="close_button">1</property>
                              <property name="context_help"></property>
                              <property name="context_menu">1</property>
                              <property name="current"></property>
                              <property name="default">0</property>
                              <property name="default_pane">0</property>
                              <property name="disabled"></property>
                              <property name="dock">Dock</property>
                              <property name="dock_fixed">0</property>
                              <property name="docking">Left</property>
                              <property name="drag_accept_files">0</property>
                              <property name="enabled">1</property>
                              <property name="fg"></property>
                              <property name="floatable">1</property>
                              <property name="focus"></property>
                              <property name="font"></property>
                              <property name="gripper">0</property>
                              <property name="hidden">0</property>
                              <property name="id">wxID_ANY</property>
                              <property name="label">Delete Net Class</property>
                              <property name="margins"></property>
                              <property name="markup">0</property>
                              <property name="max_size"></property>
                              <property name="maximize_button">0</property>
                              <property name="maximum_size"></property>
                              <property name="min_size"></property>
                              <property name="minimize_button">0</property>
                              <property name="minimum_size">-1,-1</property>
                              <property name="moveable">1</property>
                              <property name="name">m_removeAssignmentButton</property>
                              <property name="pane_border">1</property>
                              <property name="pane_position"></property>
                              <property name="pane_size"></property>
                              <property name="permission">protected</property>
                              <property name="pin_button">1</property>
                              <property name="pos"></property>
                              <property name="position"></property>
                              <property name="pressed"></property>
                              <property name="resize">Resizable</property>
                              <property name="show">1</property>
                              <property name="size">-1,-1</property>
                              <property name="style"></property>
                              <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                              <property name="toolbar_pane">0</property>
                              <property name="tooltip"></property>
                              <property name="validator_data_type"></property>
                              <property name="validator_style">wxFILTER_NONE</property>
                              <property name="validator_type">wxDefaultValidator</property>
                              <property name="validator_variable"></property>
                              <property name="window_extra_style"></property>
                              <property name="window_name"></property>
                              <property name="window_style"></property>
                              <event name="OnButtonClick">OnRemoveAssignmentClick</event>
                            </object>
                          </object>
                        </object>
                      </object>
                    </object>
                  </object>
                </object>
              </object>
            </object>
          </object>
        </object>
      </object>
    </object>
  </object>
</wxFormBuilder_Project>
