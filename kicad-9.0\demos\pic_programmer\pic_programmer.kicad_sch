(kicad_sch
	(version 20250114)
	(generator "eeschema")
	(generator_version "9.0")
	(uuid "2e45d1d2-c73f-46e5-98d6-3a9dc360bff5")
	(paper "A4")
	(title_block
		(title "JDM - COM84 PIC Programmer with 13V DC/DC converter")
		(date "05 jan 2014")
		(rev "2")
		(company "KiCad")
	)
	(lib_symbols
		(symbol "pic_programmer:74LS125"
			(pin_names
				(offset 0.254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at 5.08 3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "74LS125"
				(at 7.62 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 2.54 1.27 0)
				(effects
					(font
						(size 0.254 0.254)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "DIP?14*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "74LS125_0_0"
				(pin power_in line
					(at -1.27 5.08 270)
					(length 2.54)
					(name "VCC"
						(effects
							(font
								(size 0.508 0.508)
							)
						)
					)
					(number "14"
						(effects
							(font
								(size 0.762 0.762)
							)
						)
					)
				)
				(pin power_in line
					(at -1.27 -5.08 90)
					(length 2.54)
					(name "GND"
						(effects
							(font
								(size 0.508 0.508)
							)
						)
					)
					(number "7"
						(effects
							(font
								(size 0.508 0.508)
							)
						)
					)
				)
			)
			(symbol "74LS125_1_0"
				(polyline
					(pts
						(xy -3.81 3.81) (xy -3.81 -3.81) (xy 3.81 0) (xy -3.81 3.81)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(pin input line
					(at -7.62 0 0)
					(length 3.81)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input inverted
					(at 2.54 -5.08 90)
					(length 4.445)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin tri_state line
					(at 7.62 0 180)
					(length 3.81)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "74LS125_2_0"
				(polyline
					(pts
						(xy -3.81 3.81) (xy -3.81 -3.81) (xy 3.81 0) (xy -3.81 3.81)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(pin input line
					(at -7.62 0 0)
					(length 3.81)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input inverted
					(at 2.54 -5.08 90)
					(length 4.445)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin tri_state line
					(at 7.62 0 180)
					(length 3.81)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "74LS125_3_0"
				(polyline
					(pts
						(xy -3.81 3.81) (xy -3.81 -3.81) (xy 3.81 0) (xy -3.81 3.81)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(pin input line
					(at -7.62 0 0)
					(length 3.81)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input inverted
					(at 2.54 -5.08 90)
					(length 4.445)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "10"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin tri_state line
					(at 7.62 0 180)
					(length 3.81)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "74LS125_4_0"
				(polyline
					(pts
						(xy -3.81 3.81) (xy -3.81 -3.81) (xy 3.81 0) (xy -3.81 3.81)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(pin input line
					(at -7.62 0 0)
					(length 3.81)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "12"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input inverted
					(at 2.54 -5.08 90)
					(length 4.445)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "13"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin tri_state line
					(at 7.62 0 180)
					(length 3.81)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "11"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:7805"
			(pin_names
				(offset 0.762)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at 3.81 -4.9784 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Value" "7805"
				(at 0 5.08 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Footprint" ""
				(at 1.27 -7.5184 0)
				(effects
					(font
						(size 0.381 0.381)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "7805_0_1"
				(rectangle
					(start -5.08 -3.81)
					(end 5.08 3.81)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "7805_1_1"
				(pin input line
					(at -10.16 1.27 0)
					(length 5.08)
					(name "VI"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
				(pin input line
					(at 0 -6.35 90)
					(length 2.54)
					(name "GND"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 0.762 0.762)
							)
						)
					)
				)
				(pin power_out line
					(at 10.16 1.27 180)
					(length 5.08)
					(name "VO"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:BC237"
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "Q"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify right)
				)
			)
			(property "Value" "BC237"
				(at 0 3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify right)
				)
			)
			(property "Footprint" "Package_TO_SOT_THT:TO-92"
				(at -2.54 -5.08 0)
				(effects
					(font
						(size 0.381 0.381)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "BC237_0_1"
				(polyline
					(pts
						(xy 0 1.905) (xy 0 -1.905) (xy 0 -1.905)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 2.54 2.54)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 1.27 0)
					(radius 2.8194)
					(stroke
						(width 0.3048)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -1.27) (xy 0 0) (xy 0 0)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 2.286 -2.286) (xy 2.54 -2.54) (xy 2.54 -2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 2.286 -2.286) (xy 1.778 -0.762) (xy 0.762 -1.778) (xy 2.286 -2.286) (xy 2.286 -2.286)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
			)
			(symbol "BC237_1_1"
				(pin input line
					(at -5.08 0 0)
					(length 5.08)
					(name "B"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 5.08 270)
					(length 2.54)
					(name "C"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 -5.08 90)
					(length 2.54)
					(name "E"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:BC307"
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "Q"
				(at -1.27 -3.81 0)
				(effects
					(font
						(size 1.524 1.524)
					)
					(justify right)
				)
			)
			(property "Value" "BC307"
				(at 0 3.81 0)
				(effects
					(font
						(size 1.524 1.524)
					)
					(justify right)
				)
			)
			(property "Footprint" "Package_TO_SOT_THT:TO-92"
				(at -2.54 -5.08 0)
				(effects
					(font
						(size 0.381 0.381)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "BC307_0_1"
				(polyline
					(pts
						(xy 0 1.905) (xy 0 -1.905) (xy 0 -1.905)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 2.54 2.54)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.635 -0.635) (xy 0 0) (xy 0 0)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.635 -0.635) (xy 1.27 -1.905) (xy 1.905 -1.27) (xy 0.635 -0.635) (xy 0.635 -0.635)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(circle
					(center 1.27 0)
					(radius 2.8194)
					(stroke
						(width 0.3048)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 2.54 -2.54) (xy 1.651 -1.651) (xy 1.651 -1.651)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "BC307_1_1"
				(pin input line
					(at -5.08 0 0)
					(length 5.08)
					(name "B"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 5.08 270)
					(length 2.54)
					(name "C"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 -5.08 90)
					(length 2.54)
					(name "E"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:C"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "C"
				(at 0.635 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "C"
				(at 0.635 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0.9652 -3.81 0)
				(effects
					(font
						(size 0.762 0.762)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "C? C_????_* C_???? SMD*_c Capacitor*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "C_0_1"
				(polyline
					(pts
						(xy -2.032 0.762) (xy 2.032 0.762)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -2.032 -0.762) (xy 2.032 -0.762)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "C_1_1"
				(pin passive line
					(at 0 3.81 270)
					(length 2.794)
					(name "~"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
				(pin passive line
					(at 0 -3.81 90)
					(length 2.794)
					(name "~"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:CONN_2"
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "P"
				(at -1.27 0 90)
				(effects
					(font
						(size 1.016 1.016)
					)
				)
			)
			(property "Value" "CONN_2"
				(at 1.27 0 90)
				(effects
					(font
						(size 1.016 1.016)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "CONN_2_0_1"
				(rectangle
					(start -2.54 3.81)
					(end 2.54 -3.81)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "CONN_2_1_1"
				(pin passive inverted
					(at -8.89 2.54 0)
					(length 6.35)
					(name "P1"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin passive inverted
					(at -8.89 -2.54 0)
					(length 6.35)
					(name "PM"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:CP"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "C"
				(at 0.635 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "CP"
				(at 0.635 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0.9652 -3.81 0)
				(effects
					(font
						(size 0.762 0.762)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "CP* Elko* TantalC* C*elec c_elec* SMD*_Pol"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "CP_0_1"
				(rectangle
					(start -2.286 1.016)
					(end 2.286 0.508)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.778 2.286)
					(end -0.762 2.286)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 1.778)
					(end -1.27 2.794)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 2.286 -0.508)
					(end -2.286 -1.016)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
			)
			(symbol "CP_1_1"
				(pin passive line
					(at 0 3.81 270)
					(length 2.794)
					(name "~"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
				(pin passive line
					(at 0 -3.81 90)
					(length 2.794)
					(name "~"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:D"
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "D"
				(at 0 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "D"
				(at 0 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Diode_* D-Pak_TO252AA *SingleDiode *_Diode_* *SingleDiode*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "D_0_1"
				(polyline
					(pts
						(xy -1.27 1.27) (xy -1.27 -1.27)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 0) (xy 1.27 1.27) (xy 1.27 -1.27) (xy -1.27 0)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
			)
			(symbol "D_1_1"
				(pin passive line
					(at -3.81 0 0)
					(length 2.54)
					(name "K"
						(effects
							(font
								(size 0.762 0.762)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 0.762 0.762)
							)
						)
					)
				)
				(pin passive line
					(at 3.81 0 180)
					(length 2.54)
					(name "A"
						(effects
							(font
								(size 0.762 0.762)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 0.762 0.762)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:DB9"
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "J"
				(at 0 13.97 0)
				(effects
					(font
						(size 1.778 1.778)
					)
				)
			)
			(property "Value" "DB9"
				(at 0 -13.97 0)
				(effects
					(font
						(size 1.778 1.778)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "DB9*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "DB9_0_1"
				(polyline
					(pts
						(xy -3.81 10.16) (xy -2.54 10.16)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -3.81 7.62) (xy 0.508 7.62)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -3.81 5.08) (xy -2.54 5.08)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -3.81 2.54) (xy 0.508 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -3.81 0) (xy -2.54 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -3.81 -2.54) (xy 0.508 -2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -3.81 -5.08) (xy -2.54 -5.08)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -3.81 -7.62) (xy 0.508 -7.62)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -3.81 -10.16) (xy -2.54 -10.16)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -3.81 -11.6586) (xy -3.556 -11.938)
					)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -3.81 -11.684) (xy -3.81 11.684)
					)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -3.556 11.938) (xy -3.81 11.684)
					)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -3.556 11.938) (xy -2.54 12.446)
					)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -3.556 -11.938) (xy -2.794 -12.446)
					)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -2.794 -12.446) (xy -1.27 -12.446)
					)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -2.54 12.446) (xy -1.778 12.446)
					)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center -1.778 10.16)
					(radius 0.762)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center -1.778 5.08)
					(radius 0.762)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center -1.778 0)
					(radius 0.762)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center -1.778 -5.08)
					(radius 0.762)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center -1.778 -10.16)
					(radius 0.762)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 1.27 7.62)
					(radius 0.762)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 1.27 2.54)
					(radius 0.762)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 1.27 -2.54)
					(radius 0.762)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 1.27 -7.62)
					(radius 0.762)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 3.2766 9.906) (xy -1.778 12.446)
					)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 3.2766 9.906) (xy 3.81 9.398)
					)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 3.556 -10.3886) (xy -1.27 -12.446)
					)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 3.81 9.398) (xy 3.81 -9.906)
					)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 3.81 -9.906) (xy 3.556 -10.3886)
					)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "DB9_1_1"
				(pin passive line
					(at -11.43 10.16 0)
					(length 7.62)
					(name "5"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin passive line
					(at -11.43 7.62 0)
					(length 7.62)
					(name "P9"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "9"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin passive line
					(at -11.43 5.08 0)
					(length 7.62)
					(name "4"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin passive line
					(at -11.43 2.54 0)
					(length 7.62)
					(name "P8"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "8"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin passive line
					(at -11.43 0 0)
					(length 7.62)
					(name "3"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin passive line
					(at -11.43 -2.54 0)
					(length 7.62)
					(name "P7"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "7"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin passive line
					(at -11.43 -5.08 0)
					(length 7.62)
					(name "2"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin passive line
					(at -11.43 -7.62 0)
					(length 7.62)
					(name "P6"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin passive line
					(at -11.43 -10.16 0)
					(length 7.62)
					(name "1"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:D_Schottky"
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "D"
				(at 0 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "D_Schottky"
				(at 0 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at -2.54 0 0)
				(effects
					(font
						(size 0.254 0.254)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "D-Pak_TO252AA Diode_* *SingleDiode *SingleDiode* *_Diode_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "D_Schottky_0_1"
				(polyline
					(pts
						(xy -1.905 0.635) (xy -1.905 1.27) (xy -1.27 1.27) (xy -1.27 -1.27) (xy -0.635 -1.27) (xy -0.635 -0.635)
					)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 0) (xy 1.27 1.27) (xy 1.27 -1.27) (xy -1.27 0)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
			)
			(symbol "D_Schottky_1_1"
				(pin passive line
					(at -3.81 0 0)
					(length 2.54)
					(name "K"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 3.81 0 180)
					(length 2.54)
					(name "A"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:GND"
			(power)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 0 0)
				(effects
					(font
						(size 0.762 0.762)
					)
					(hide yes)
				)
			)
			(property "Value" "GND"
				(at 0 -1.778 0)
				(effects
					(font
						(size 0.762 0.762)
					)
					(hide yes)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "GND_0_1"
				(polyline
					(pts
						(xy -1.27 0) (xy 0 -1.27) (xy 1.27 0) (xy -1.27 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "GND_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(hide yes)
					(name "GND"
						(effects
							(font
								(size 0.762 0.762)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 0.762 0.762)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:INDUCTOR"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "L"
				(at -1.27 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "INDUCTOR"
				(at 2.54 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "INDUCTOR_0_1"
				(arc
					(start 0.0254 4.9784)
					(mid 1.1942 3.7719)
					(end 0.0254 2.5654)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 0.0254 2.5908)
					(mid 1.2704 1.3081)
					(end 0.0254 0.0254)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 0.0254 0.0508)
					(mid 1.2704 -1.2319)
					(end 0.0254 -2.5146)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 0.0254 -2.54)
					(mid 1.245 -3.7973)
					(end 0.0254 -5.0546)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "INDUCTOR_1_1"
				(pin passive line
					(at 0 7.62 270)
					(length 2.54)
					(name "1"
						(effects
							(font
								(size 1.778 1.778)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.778 1.778)
							)
						)
					)
				)
				(pin passive line
					(at 0 -7.62 90)
					(length 2.54)
					(name "2"
						(effects
							(font
								(size 1.778 1.778)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.778 1.778)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:JUMPER"
			(pin_names
				(offset 0.762)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "JP"
				(at 0 3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "JUMPER"
				(at 0 -2.032 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "JUMPER_0_1"
				(circle
					(center -2.54 0)
					(radius 0.889)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -2.4892 1.27)
					(mid 0.0127 2.5097)
					(end 2.5146 1.27)
					(stroke
						(width 0.3048)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 2.54 0)
					(radius 0.889)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(pin passive line
					(at -7.62 0 0)
					(length 4.191)
					(name "1"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin passive line
					(at 7.62 0 180)
					(length 4.191)
					(name "2"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:LED"
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "D"
				(at 0 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "LED"
				(at 0 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "LED-3MM LED-5MM LED-10MM LED-0603 LED-0805 LED-1206 LEDV"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "LED_0_1"
				(polyline
					(pts
						(xy -2.032 -0.635) (xy -3.175 -1.651) (xy -3.048 -1.016)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.651 -1.016) (xy -2.794 -2.032) (xy -2.667 -1.397)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 1.27) (xy -1.27 -1.27)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 0) (xy 1.27 1.27) (xy 1.27 -1.27) (xy -1.27 0)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
			)
			(symbol "LED_1_1"
				(pin passive line
					(at -5.08 0 0)
					(length 3.81)
					(name "K"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 0 180)
					(length 3.81)
					(name "A"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:LT1373"
			(pin_names
				(offset 0.762)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at 15.24 12.7 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Value" "LT1373"
				(at -12.7 12.7 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "LT1373_0_1"
				(rectangle
					(start -17.78 -10.16)
					(end 17.78 10.16)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "LT1373_1_1"
				(pin passive line
					(at -25.4 6.35 0)
					(length 7.62)
					(name "FB-"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin passive line
					(at -25.4 -6.35 0)
					(length 7.62)
					(name "S/S"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin input line
					(at -7.62 -17.78 90)
					(length 7.62)
					(name "GND"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "7"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin input line
					(at -3.81 -17.78 90)
					(length 7.62)
					(name "GND_S"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin power_in line
					(at 0 17.78 270)
					(length 7.62)
					(name "Vin"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin input line
					(at 6.35 -17.78 90)
					(length 7.62)
					(name "Vc"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin input line
					(at 25.4 6.35 180)
					(length 7.62)
					(name "Vsw"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "8"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin input line
					(at 25.4 -6.35 180)
					(length 7.62)
					(name "FB+"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:MOUNTING_HOLE"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.762)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "P"
				(at 2.032 0 0)
				(effects
					(font
						(size 1.016 1.016)
					)
					(justify left)
				)
			)
			(property "Value" "MOUNTING_HOLE"
				(at 0 1.397 0)
				(effects
					(font
						(size 0.762 0.762)
					)
					(hide yes)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "*Hole*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "MOUNTING_HOLE_0_1"
				(circle
					(center 0 0)
					(radius 0.7874)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:POT"
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "RV"
				(at 0 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "POT"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "POT_0_1"
				(rectangle
					(start -3.81 1.27)
					(end 3.81 -1.27)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 1.27) (xy -0.508 1.778) (xy 0.508 1.778)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
			)
			(symbol "POT_1_1"
				(pin passive line
					(at -6.35 0 0)
					(length 2.54)
					(name "1"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
				(pin passive line
					(at 0 3.81 270)
					(length 2.032)
					(name "2"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
				(pin passive line
					(at 6.35 0 180)
					(length 2.54)
					(name "3"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:PWR_FLAG"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#FLG"
				(at 0 2.413 0)
				(effects
					(font
						(size 0.762 0.762)
					)
					(hide yes)
				)
			)
			(property "Value" "PWR_FLAG"
				(at 0 4.572 0)
				(effects
					(font
						(size 0.762 0.762)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "PWR_FLAG_0_0"
				(pin power_out line
					(at 0 0 90)
					(length 0)
					(name "pwr"
						(effects
							(font
								(size 0.508 0.508)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 0.508 0.508)
							)
						)
					)
				)
			)
			(symbol "PWR_FLAG_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 1.27) (xy -1.905 2.54) (xy 0 3.81) (xy 1.905 2.54) (xy 0 1.27)
					)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:R"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "R"
				(at 2.032 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "R"
				(at 0 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at -1.778 0 90)
				(effects
					(font
						(size 0.762 0.762)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 0.762 0.762)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "R_* Resistor_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "R_0_1"
				(rectangle
					(start -1.016 -2.54)
					(end 1.016 2.54)
					(stroke
						(width 0.2032)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "R_1_1"
				(pin passive line
					(at 0 3.81 270)
					(length 1.27)
					(name "~"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
				(pin passive line
					(at 0 -3.81 90)
					(length 1.27)
					(name "~"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.524 1.524)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:VCC"
			(power)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 2.54 0)
				(effects
					(font
						(size 0.762 0.762)
					)
					(hide yes)
				)
			)
			(property "Value" "VCC"
				(at 0 2.54 0)
				(effects
					(font
						(size 0.762 0.762)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VCC_0_0"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(hide yes)
					(name "VCC"
						(effects
							(font
								(size 0.508 0.508)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 0.508 0.508)
							)
						)
					)
				)
			)
			(symbol "VCC_0_1"
				(circle
					(center 0 1.27)
					(radius 0.508)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 0.762) (xy 0 0.762)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "pic_programmer:VPP"
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 5.08 0)
				(effects
					(font
						(size 1.016 1.016)
					)
					(hide yes)
				)
			)
			(property "Value" "VPP"
				(at 0 3.81 0)
				(effects
					(font
						(size 1.016 1.016)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VPP_0_0"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(hide yes)
					(name "VPP"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.016 1.016)
							)
						)
					)
				)
			)
			(symbol "VPP_0_1"
				(circle
					(center 0 2.032)
					(radius 0.508)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 1.524) (xy 0 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(embedded_fonts no)
		)
	)
	(text "ADJUST for VPP = 13V"
		(exclude_from_sim no)
		(at 248.92 149.86 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "461a506c-6391-4cb6-9c12-e0b1e955c0da")
	)
	(text "8 to 15V"
		(exclude_from_sim no)
		(at 21.59 165.1 0)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify left bottom)
		)
		(uuid "629b714c-59fb-4aa8-80b3-0b67e75a9de9")
	)
	(text "VPP (13V) power"
		(exclude_from_sim no)
		(at 205.74 119.38 0)
		(effects
			(font
				(size 2.54 2.54)
				(thickness 0.508)
				(bold yes)
				(italic yes)
			)
			(justify left bottom)
		)
		(uuid "6309b1ea-35ff-4629-8b7d-248af0b86a3c")
	)
	(text "RTS"
		(exclude_from_sim no)
		(at 46.99 88.9 0)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify left bottom)
		)
		(uuid "7168a71a-38f6-4193-9c07-ca3ee9431fb2")
	)
	(text "TXD"
		(exclude_from_sim no)
		(at 46.99 91.44 0)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify left bottom)
		)
		(uuid "80ac3a88-4e36-46a3-8d7f-6b130147e5a3")
	)
	(text "CTS"
		(exclude_from_sim no)
		(at 46.99 93.98 0)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify left bottom)
		)
		(uuid "921da4bf-54c6-4a86-afaa-f2d1952e45b3")
	)
	(text "DTR"
		(exclude_from_sim no)
		(at 46.99 96.52 0)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify left bottom)
		)
		(uuid "b14dca8c-7914-4eeb-84ee-a3d1a6885a28")
	)
	(text "Mounting holes"
		(exclude_from_sim no)
		(at 153.67 195.58 0)
		(effects
			(font
				(size 1.016 1.016)
				(italic yes)
			)
			(justify left bottom)
		)
		(uuid "c8fc1660-2e43-4c80-80ea-e463d574734e")
	)
	(junction
		(at 81.28 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "01e9b6e7-adf9-4ee7-9447-a588630ee4a2")
	)
	(junction
		(at 97.79 129.54)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "0c3dceba-7c95-4b3d-b590-0eb581444beb")
	)
	(junction
		(at 179.07 40.64)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "16a9ae8c-3ad2-439b-8efe-377c994670c7")
	)
	(junction
		(at 35.56 173.99)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "16bd6381-8ac0-4bf2-9dce-ecc20c724b8d")
	)
	(junction
		(at 255.27 137.16)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "182b2d54-931d-49d6-9f39-60a752623e36")
	)
	(junction
		(at 71.12 43.18)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "4f66b314-0f62-4fb6-8c3c-f9c6a75cd3ec")
	)
	(junction
		(at 260.35 137.16)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "5114c7bf-b955-49f3-a0a8-4b954c81bde0")
	)
	(junction
		(at 154.94 31.75)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "6595b9c7-02ee-4647-bde5-6b566e35163e")
	)
	(junction
		(at 87.63 43.18)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "730b670c-9bcf-4dcd-9a8d-fcaa61fb0955")
	)
	(junction
		(at 166.37 40.64)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "770ad51a-7219-4633-b24a-bd20feb0a6c5")
	)
	(junction
		(at 203.2 154.94)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "789ca812-3e0c-4a3f-97bc-a916dd9bce80")
	)
	(junction
		(at 81.28 168.91)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "7d928d56-093a-4ca8-aed1-414b7e703b45")
	)
	(junction
		(at 86.36 168.91)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "8a650ebf-3f78-4ca4-a26b-a5028693e36d")
	)
	(junction
		(at 137.16 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "965308c8-e014-459a-b9db-b8493a601c62")
	)
	(junction
		(at 223.52 130.81)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "a17904b9-135e-4dae-ae20-401c7787de72")
	)
	(junction
		(at 49.53 168.91)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "a5cd8da1-8f7f-4f80-bb23-0317de562222")
	)
	(junction
		(at 97.79 80.01)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "abe07c9a-17c3-43b5-b7a6-ae867ac27ea7")
	)
	(junction
		(at 138.43 38.1)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "b1c649b1-f44d-46c7-9dea-818e75a1b87e")
	)
	(junction
		(at 154.94 35.56)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "b7199d9b-bebb-4100-9ad3-c2bd31e21d65")
	)
	(junction
		(at 81.28 129.54)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "ca87f11b-5f48-4b57-8535-68d3ec2fe5a9")
	)
	(junction
		(at 219.71 81.28)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "cdfb07af-801b-44ba-8c30-d021a6ad3039")
	)
	(junction
		(at 189.23 154.94)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "db36f6e3-e72a-487f-bda9-88cc84536f62")
	)
	(junction
		(at 200.66 73.66)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "e4c6fdbb-fdc7-4ad4-a516-240d84cdc120")
	)
	(junction
		(at 210.82 81.28)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "e6b860cc-cb76-4220-acfb-68f1eb348bfa")
	)
	(junction
		(at 233.68 152.4)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "f202141e-c20d-4cac-b016-06a44f2ecce8")
	)
	(junction
		(at 154.94 19.05)
		(diameter 1.016)
		(color 0 0 0 0)
		(uuid "f3628265-0155-43e2-a467-c40ff783e265")
	)
	(no_connect
		(at 171.45 130.81)
		(uuid "4a7043ea-6111-4547-80ff-c868f3173ef4")
	)
	(no_connect
		(at 43.18 81.28)
		(uuid "6e017d37-4c3e-4baf-bdac-affb8e62c379")
	)
	(no_connect
		(at 43.18 86.36)
		(uuid "7a3bd905-9009-46de-b05c-f07b31cacf67")
	)
	(no_connect
		(at 43.18 99.06)
		(uuid "8cbd52e7-5bc6-4f19-a7e5-ceccbda86f47")
	)
	(no_connect
		(at 43.18 83.82)
		(uuid "d3e4c573-c0d4-4e9e-a4a3-c81b005dc9bd")
	)
	(no_connect
		(at 171.45 143.51)
		(uuid "d4608387-9919-4315-81a5-c0c3cd57f062")
	)
	(wire
		(pts
			(xy 87.63 43.18) (xy 87.63 46.99)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "01d29356-3ea8-4f7d-8fdc-be547e2149b0")
	)
	(polyline
		(pts
			(xy 172.72 196.85) (xy 148.59 196.85)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "088f800b-89af-4049-b7bd-bc3ee6bc7fd7")
	)
	(wire
		(pts
			(xy 97.79 129.54) (xy 102.87 129.54)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "08aed6e5-6c4d-4c36-a051-b28cb81634ca")
	)
	(wire
		(pts
			(xy 102.87 106.68) (xy 57.15 106.68)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "094b5d86-7e03-4999-ad88-b83f2a3bdc59")
	)
	(wire
		(pts
			(xy 250.19 156.21) (xy 243.84 156.21)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "0bc0d85d-45d4-4d25-9108-8b3702667e35")
	)
	(wire
		(pts
			(xy 105.41 168.91) (xy 101.6 168.91)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "0d3d6ca5-c8e7-4f28-88d6-7ffceb687e4b")
	)
	(wire
		(pts
			(xy 57.15 106.68) (xy 57.15 93.98)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "0dfc3d91-b199-420e-96c7-ece03bcc8247")
	)
	(wire
		(pts
			(xy 250.19 143.51) (xy 243.84 143.51)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "0ebaae7b-4e65-4d46-adf4-6e8d1b68c883")
	)
	(wire
		(pts
			(xy 38.1 168.91) (xy 34.29 168.91)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "10d6f13f-11e8-40e0-97b8-bbbd1a5ba2fc")
	)
	(wire
		(pts
			(xy 234.95 137.16) (xy 223.52 137.16)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "1197f7e0-e4a9-43a8-90bf-76fa5c2a0413")
	)
	(wire
		(pts
			(xy 166.37 66.04) (xy 166.37 67.31)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "135e1c1e-07f3-42ff-8909-eee7c8079e30")
	)
	(wire
		(pts
			(xy 133.35 80.01) (xy 137.16 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "143b808a-a211-4485-878b-afdb1749de47")
	)
	(wire
		(pts
			(xy 260.35 137.16) (xy 260.35 143.51)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "163d8d87-fd89-4117-bfd0-027edda460ee")
	)
	(wire
		(pts
			(xy 208.28 154.94) (xy 203.2 154.94)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "19c6a95d-896f-4d9c-b89b-ee0b7be71c07")
	)
	(wire
		(pts
			(xy 138.43 35.56) (xy 142.24 35.56)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "1b27bcfc-68df-48b1-9a80-a0097ef4f057")
	)
	(wire
		(pts
			(xy 255.27 137.16) (xy 260.35 137.16)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "1c049b42-0c6e-4778-865d-e1b0bd8e5d1b")
	)
	(wire
		(pts
			(xy 260.35 156.21) (xy 260.35 157.48)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "1e4da90a-d330-4c8a-b48f-fa4001ba7542")
	)
	(wire
		(pts
			(xy 200.66 73.66) (xy 200.66 67.31)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "219f18dd-4ec5-41e3-9c56-370ad579ffff")
	)
	(wire
		(pts
			(xy 92.71 129.54) (xy 97.79 129.54)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "221721cf-0f50-4426-aef4-63aae29a18bd")
	)
	(wire
		(pts
			(xy 200.66 67.31) (xy 198.12 67.31)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "2adaa11f-3575-4b72-ad65-b69a2ab5e079")
	)
	(wire
		(pts
			(xy 130.81 43.18) (xy 123.19 43.18)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "2b951ebe-52a1-4065-b2a5-64aa43169002")
	)
	(wire
		(pts
			(xy 97.79 68.58) (xy 97.79 69.85)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "2bd4e260-cb8f-4cef-8d03-7b037255b014")
	)
	(wire
		(pts
			(xy 86.36 168.91) (xy 86.36 167.64)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "2daaae79-65ee-4a4c-96a3-************")
	)
	(wire
		(pts
			(xy 35.56 173.99) (xy 40.64 173.99)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "2f72c78c-3f47-44b4-bc9d-911cae4a10d9")
	)
	(wire
		(pts
			(xy 274.32 137.16) (xy 274.32 138.43)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "2fbea2a5-d6a6-411b-90f5-d630fdeed93c")
	)
	(wire
		(pts
			(xy 179.07 46.99) (xy 179.07 40.64)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "30256295-dacd-4071-b937-476c9e0e58f4")
	)
	(wire
		(pts
			(xy 232.41 152.4) (xy 233.68 152.4)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "309f572c-ee3a-425b-8525-b44da44051e5")
	)
	(wire
		(pts
			(xy 271.78 137.16) (xy 274.32 137.16)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "32ae0df1-5542-4fb6-ab85-1ab87ce57f4f")
	)
	(wire
		(pts
			(xy 189.23 154.94) (xy 193.04 154.94)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "35167783-3cd5-437e-97df-7fba0d36bcc2")
	)
	(wire
		(pts
			(xy 198.12 73.66) (xy 200.66 73.66)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "3ab6852c-8838-4520-94e5-c621c246e3d0")
	)
	(wire
		(pts
			(xy 43.18 93.98) (xy 57.15 93.98)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "3b26ab58-99ad-47ee-8b8b-3a709dba4092")
	)
	(wire
		(pts
			(xy 138.43 35.56) (xy 138.43 38.1)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "3bf4a168-f0bf-429a-aee8-4621b9e232f0")
	)
	(wire
		(pts
			(xy 144.78 19.05) (xy 154.94 19.05)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "3d3e513e-0e4e-488a-8e0b-1b7efdfc0e1b")
	)
	(wire
		(pts
			(xy 57.15 91.44) (xy 43.18 91.44)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "3f215395-bb66-4258-a542-73b868e54353")
	)
	(wire
		(pts
			(xy 233.68 152.4) (xy 234.95 152.4)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "41c0f5a5-48ed-454a-8b94-a803cd01067a")
	)
	(wire
		(pts
			(xy 81.28 80.01) (xy 85.09 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "476d3e68-31e2-4971-a853-66b9d3539ead")
	)
	(wire
		(pts
			(xy 138.43 38.1) (xy 148.59 38.1)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "47a6b79c-9dbf-4c4b-8e93-7f590d005e8a")
	)
	(wire
		(pts
			(xy 148.59 73.66) (xy 177.8 73.66)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "482372f8-c547-45a9-bcbf-67458e79dd51")
	)
	(wire
		(pts
			(xy 35.56 175.26) (xy 35.56 173.99)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "488c14c9-11ed-42f9-9936-8f34a5dec6f6")
	)
	(wire
		(pts
			(xy 137.16 80.01) (xy 152.4 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "49968d81-31c8-4898-a5c9-4e4606ddfaed")
	)
	(wire
		(pts
			(xy 81.28 168.91) (xy 86.36 168.91)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "4b26ff60-fd35-4b2a-b0dd-1079945d4d49")
	)
	(wire
		(pts
			(xy 233.68 105.41) (xy 213.36 105.41)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "4b850692-8c5a-4b58-a1ee-2b4a9eb2b95e")
	)
	(wire
		(pts
			(xy 238.76 149.86) (xy 238.76 143.51)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "4d8e718e-0f64-4dc5-a78e-70dd8e834181")
	)
	(wire
		(pts
			(xy 118.11 80.01) (xy 125.73 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "4da37657-6775-4e62-8cb2-8bbe05919085")
	)
	(wire
		(pts
			(xy 204.47 81.28) (xy 210.82 81.28)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "4e6b9059-3acd-4802-b07b-7763b0979fe9")
	)
	(wire
		(pts
			(xy 49.53 171.45) (xy 49.53 168.91)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "4fd33926-3aff-42ff-bc20-8d886e441ac1")
	)
	(wire
		(pts
			(xy 210.82 81.28) (xy 210.82 78.74)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "50db2e93-513b-4f11-b118-59fde90aa626")
	)
	(wire
		(pts
			(xy 53.34 129.54) (xy 81.28 129.54)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "51cde52e-77c9-4099-a36b-b965d9abca62")
	)
	(wire
		(pts
			(xy 78.74 168.91) (xy 81.28 168.91)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "520ee8ee-e661-40ee-b909-08bac9a18666")
	)
	(wire
		(pts
			(xy 190.5 40.64) (xy 209.55 40.64)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "5a25b45b-514f-4252-b152-c2998ba8c3f3")
	)
	(wire
		(pts
			(xy 154.94 29.21) (xy 154.94 31.75)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "5ac45803-32da-4781-91d6-5a5b8965d4e2")
	)
	(wire
		(pts
			(xy 148.59 38.1) (xy 148.59 73.66)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "5d69f094-ae35-42a3-ac03-ae68a62d92e2")
	)
	(wire
		(pts
			(xy 210.82 81.28) (xy 219.71 81.28)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "5ee06cb8-0e41-497f-9945-1a0342fe1fc9")
	)
	(wire
		(pts
			(xy 87.63 54.61) (xy 87.63 55.88)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "61959be2-8588-4690-931c-cb902e4b1128")
	)
	(wire
		(pts
			(xy 187.96 67.31) (xy 190.5 67.31)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "67d4de3c-fccb-4b57-ae93-6c8f26752391")
	)
	(wire
		(pts
			(xy 240.03 149.86) (xy 238.76 149.86)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "688fef45-ac7c-47e5-a5f3-3f80b4c6f937")
	)
	(wire
		(pts
			(xy 194.31 83.82) (xy 194.31 81.28)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "696cde2e-450e-4866-84f4-92fb1d08e037")
	)
	(wire
		(pts
			(xy 189.23 156.21) (xy 189.23 154.94)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "6ae090ba-d8ea-4edb-a886-ddde87008bf5")
	)
	(wire
		(pts
			(xy 115.57 43.18) (xy 107.95 43.18)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "6b1da020-31a2-4b15-b783-314283d7e8f6")
	)
	(wire
		(pts
			(xy 238.76 128.27) (xy 238.76 130.81)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "6b6ce1be-b545-4775-9f14-793e18521457")
	)
	(wire
		(pts
			(xy 34.29 173.99) (xy 35.56 173.99)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "6b768ddd-1f41-45e7-ac4a-001029b162b9")
	)
	(wire
		(pts
			(xy 228.6 160.02) (xy 233.68 160.02)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "6eba9a65-3218-42b4-a4ca-aa48e9d3eb53")
	)
	(polyline
		(pts
			(xy 283.21 114.3) (xy 283.21 163.83)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "72013c4c-4611-4b21-8072-7ca044aa6fc8")
	)
	(wire
		(pts
			(xy 71.12 43.18) (xy 74.93 43.18)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "72c44737-aeea-488c-a989-c2b7279a4437")
	)
	(wire
		(pts
			(xy 260.35 137.16) (xy 264.16 137.16)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "7439b8f9-443a-4676-9d5c-501900e51493")
	)
	(wire
		(pts
			(xy 203.2 160.02) (xy 220.98 160.02)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "76b79871-a769-4bf8-a39a-d75513a8e759")
	)
	(wire
		(pts
			(xy 166.37 30.48) (xy 166.37 27.94)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "7c108e0e-f351-4da9-98c5-168c3a209b2f")
	)
	(wire
		(pts
			(xy 86.36 168.91) (xy 93.98 168.91)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "7e537fed-27cb-4867-8ddd-1eecb2fe2eb9")
	)
	(wire
		(pts
			(xy 219.71 81.28) (xy 233.68 81.28)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "7e55fe25-f30b-4ae3-bcbd-4ffa6b2f5575")
	)
	(wire
		(pts
			(xy 97.79 77.47) (xy 97.79 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "8709bc30-726b-4f5b-a997-a9a52ffb6c2b")
	)
	(wire
		(pts
			(xy 57.15 43.18) (xy 71.12 43.18)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "8b9839b3-ac45-4501-8e4b-754d77d7d7af")
	)
	(wire
		(pts
			(xy 260.35 156.21) (xy 257.81 156.21)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "8d2c52e1-61c4-430c-932e-ea12c38508ec")
	)
	(wire
		(pts
			(xy 233.68 160.02) (xy 233.68 152.4)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "948296ea-2539-462e-9c66-0a0419b3f24c")
	)
	(wire
		(pts
			(xy 62.23 96.52) (xy 62.23 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "94c35674-3014-4fd7-a806-d448849a7af7")
	)
	(wire
		(pts
			(xy 53.34 88.9) (xy 43.18 88.9)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "9814f848-46a3-4782-81bf-71aea6231d58")
	)
	(wire
		(pts
			(xy 62.23 96.52) (xy 43.18 96.52)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "9929fd14-b27e-4e43-ba7b-c23953384986")
	)
	(wire
		(pts
			(xy 213.36 88.9) (xy 233.68 88.9)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "9c544d0e-2851-4889-aa5f-26d4a647aefe")
	)
	(wire
		(pts
			(xy 212.09 97.79) (xy 233.68 97.79)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "9cbc39f3-b987-4666-b795-48734aad6b61")
	)
	(wire
		(pts
			(xy 154.94 31.75) (xy 154.94 35.56)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "9dd2adad-f828-4a26-aa68-5ccf446988e8")
	)
	(polyline
		(pts
			(xy 148.59 196.85) (xy 148.59 170.18)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "9e1184c9-2f66-4019-ba23-bfc2d6556f60")
	)
	(wire
		(pts
			(xy 149.86 35.56) (xy 154.94 35.56)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "9fb4713e-4835-4cc0-9ec2-68033d5b6a91")
	)
	(wire
		(pts
			(xy 87.63 33.02) (xy 87.63 31.75)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "9fe2b510-ca73-43cd-9105-dfc1a50bf74e")
	)
	(wire
		(pts
			(xy 137.16 106.68) (xy 137.16 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "a1f4295c-894f-405d-903c-c764b6ef1b7a")
	)
	(wire
		(pts
			(xy 218.44 152.4) (xy 218.44 154.94)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "a21cd6a7-6d17-4218-9032-97330fe69ef9")
	)
	(wire
		(pts
			(xy 144.78 19.05) (xy 144.78 21.59)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "a365791e-f9f7-4f50-a5cb-b7d4e6dc4ecb")
	)
	(wire
		(pts
			(xy 57.15 43.18) (xy 57.15 91.44)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "a41b7975-76c2-4abe-a857-27cf2f309ec8")
	)
	(wire
		(pts
			(xy 200.66 73.66) (xy 203.2 73.66)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "ab0bc500-f5dc-4a73-9969-163155c7e11b")
	)
	(wire
		(pts
			(xy 144.78 31.75) (xy 154.94 31.75)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "ac90a1a6-baec-4477-89c9-8509ffce5ac0")
	)
	(wire
		(pts
			(xy 45.72 168.91) (xy 49.53 168.91)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "ad04a185-1182-43ac-828d-352a75b885c7")
	)
	(polyline
		(pts
			(xy 172.72 170.18) (xy 172.72 196.85)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "ae3e63a0-f7db-4ed5-bca3-bfc19e6c4e23")
	)
	(wire
		(pts
			(xy 219.71 81.28) (xy 219.71 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "af462d56-38dd-466b-bb1b-c9e40e504f64")
	)
	(wire
		(pts
			(xy 223.52 137.16) (xy 223.52 130.81)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "b01101ee-03d3-40ae-a8c3-1c8b6ffc8966")
	)
	(wire
		(pts
			(xy 81.28 129.54) (xy 85.09 129.54)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "b023b60f-9765-4914-9a3b-0834fa7ed88c")
	)
	(wire
		(pts
			(xy 148.59 129.54) (xy 133.35 129.54)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "b2af012f-0b60-4544-933b-************")
	)
	(wire
		(pts
			(xy 97.79 80.01) (xy 97.79 83.82)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "b4712c01-340e-4c6e-9e0f-1b7daae2c649")
	)
	(wire
		(pts
			(xy 97.79 80.01) (xy 102.87 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "b4ce1e1d-f588-47a2-b677-3444905ce05a")
	)
	(wire
		(pts
			(xy 154.94 17.78) (xy 154.94 19.05)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "b5e85a0a-5586-4784-a4c5-78749b02c275")
	)
	(wire
		(pts
			(xy 166.37 40.64) (xy 179.07 40.64)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "b7c61ea3-5e57-46da-8fda-a32008e68f40")
	)
	(wire
		(pts
			(xy 203.2 154.94) (xy 203.2 160.02)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "b9b4f953-cc3b-42a2-bd37-3d8583e4e0c5")
	)
	(wire
		(pts
			(xy 87.63 43.18) (xy 92.71 43.18)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "bd230f3b-a580-499b-938a-da6b4c354299")
	)
	(wire
		(pts
			(xy 260.35 143.51) (xy 257.81 143.51)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "beb5c864-057d-4a17-b820-f1c028638a67")
	)
	(wire
		(pts
			(xy 194.31 81.28) (xy 196.85 81.28)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "c2c0027f-11b1-47b2-ba49-8d834a535be1")
	)
	(wire
		(pts
			(xy 166.37 40.64) (xy 166.37 44.45)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "c4dbbabd-02e2-4f78-aa83-6ce622576cb9")
	)
	(wire
		(pts
			(xy 179.07 40.64) (xy 182.88 40.64)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "c53abf42-0137-45f9-95a2-ae8afef658f7")
	)
	(wire
		(pts
			(xy 105.41 171.45) (xy 105.41 168.91)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "c6687a16-b381-40bc-9ddf-99117ece00cd")
	)
	(wire
		(pts
			(xy 81.28 133.35) (xy 81.28 129.54)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "c7e24785-30c0-4eac-9506-4415066313c4")
	)
	(polyline
		(pts
			(xy 162.56 163.83) (xy 283.21 163.83)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "c8930cd8-b140-440c-b5bc-9ae4a62fd2ec")
	)
	(wire
		(pts
			(xy 260.35 135.89) (xy 260.35 137.16)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "cbabd890-728a-4a3b-bd30-42f88f6058d2")
	)
	(wire
		(pts
			(xy 53.34 129.54) (xy 53.34 88.9)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "cd070d93-147d-4386-bea5-dd3f8b9f4789")
	)
	(wire
		(pts
			(xy 81.28 83.82) (xy 81.28 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "cde20c2b-1742-47b6-9477-d054ec9d762a")
	)
	(wire
		(pts
			(xy 154.94 35.56) (xy 158.75 35.56)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "cfc23a7d-0a72-4f2c-ae7e-c326725d11a9")
	)
	(wire
		(pts
			(xy 62.23 80.01) (xy 81.28 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "d64b52b5-c0a0-4f6d-bd8b-58576b3a2504")
	)
	(wire
		(pts
			(xy 87.63 40.64) (xy 87.63 43.18)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "d83ea72f-790b-4641-a947-4c88072aec27")
	)
	(wire
		(pts
			(xy 190.5 73.66) (xy 185.42 73.66)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "d93c18f1-a321-4861-8e66-74657aefe865")
	)
	(wire
		(pts
			(xy 81.28 168.91) (xy 81.28 171.45)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "d9fb9595-b7d6-44c2-9286-6563a6dcdf37")
	)
	(wire
		(pts
			(xy 224.79 152.4) (xy 218.44 152.4)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "ddc278da-5a12-4c5e-9239-a1396aa001ce")
	)
	(wire
		(pts
			(xy 255.27 135.89) (xy 255.27 137.16)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "ddfb4f62-0743-4f95-ac86-05be79e05364")
	)
	(polyline
		(pts
			(xy 162.56 114.3) (xy 162.56 163.83)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "def06814-a49a-4054-861a-de83d45281c5")
	)
	(wire
		(pts
			(xy 97.79 142.24) (xy 97.79 140.97)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "df1b9937-db22-4142-80b8-901979a4eb43")
	)
	(wire
		(pts
			(xy 144.78 29.21) (xy 144.78 31.75)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e285e18b-77eb-4622-886c-325eb52fe8bd")
	)
	(wire
		(pts
			(xy 118.11 106.68) (xy 137.16 106.68)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e326ee0a-6ceb-4fb8-b256-e08b33cedacc")
	)
	(wire
		(pts
			(xy 92.71 80.01) (xy 97.79 80.01)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e413159d-13ff-4bf5-8286-a41ea89e486c")
	)
	(wire
		(pts
			(xy 82.55 43.18) (xy 87.63 43.18)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e424287a-b134-43ef-bc27-10b2f64ecdc3")
	)
	(wire
		(pts
			(xy 218.44 154.94) (xy 215.9 154.94)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e45465ce-c3d9-413c-a475-94e91a7bc5d4")
	)
	(wire
		(pts
			(xy 166.37 52.07) (xy 166.37 55.88)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e47f13f7-e811-4139-b760-50b3009c2b42")
	)
	(wire
		(pts
			(xy 40.64 173.99) (xy 40.64 175.26)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e7d34404-31f1-4795-a44e-eb35e3d1f241")
	)
	(wire
		(pts
			(xy 125.73 129.54) (xy 118.11 129.54)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e884834e-9284-4e84-8978-01cb388abc97")
	)
	(wire
		(pts
			(xy 97.79 119.38) (xy 97.79 118.11)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "e9075320-e9a1-472b-b795-809636f3cb51")
	)
	(polyline
		(pts
			(xy 148.59 170.18) (xy 172.72 170.18)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "eac60a6c-3bd2-4e06-a1f5-4485a6021830")
	)
	(wire
		(pts
			(xy 97.79 129.54) (xy 97.79 133.35)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "f1015aca-7920-46f0-8a11-a4bf1aee68b2")
	)
	(wire
		(pts
			(xy 97.79 127) (xy 97.79 129.54)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "f2359e4d-d917-4576-8697-058b0f9070f6")
	)
	(wire
		(pts
			(xy 196.85 119.38) (xy 196.85 118.11)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "f31d41e0-c52a-4c06-a62e-b4b3d2147d5f")
	)
	(wire
		(pts
			(xy 242.57 137.16) (xy 255.27 137.16)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "f56da68f-e2cd-4232-8519-05fcc827f38c")
	)
	(wire
		(pts
			(xy 154.94 19.05) (xy 154.94 21.59)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "f612fd7e-56f0-4f5e-8eb3-7fb208ee3fc3")
	)
	(wire
		(pts
			(xy 223.52 130.81) (xy 222.25 130.81)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "f99390ed-8d57-43df-a8de-e5835365ccfe")
	)
	(wire
		(pts
			(xy 49.53 168.91) (xy 58.42 168.91)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "f9bdf406-be12-4ef0-8bc0-5f56b1741d01")
	)
	(wire
		(pts
			(xy 71.12 46.99) (xy 71.12 43.18)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "fa74c927-c02f-41b3-aa9e-9581ddab9866")
	)
	(wire
		(pts
			(xy 238.76 143.51) (xy 222.25 143.51)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(uuid "fd13eb6b-0b53-4b11-8517-2e82ffe03746")
	)
	(polyline
		(pts
			(xy 283.21 114.3) (xy 162.56 114.3)
		)
		(stroke
			(width 0)
			(type dash)
		)
		(uuid "ff04f9ee-d9e9-4327-926b-4dff073464b6")
	)
	(label "DATA-RB7"
		(at 152.4 80.01 180)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify right bottom)
		)
		(uuid "306c3b00-22ad-4950-8b7c-ee9e3dfa00d3")
	)
	(label "CLOCK-RB6"
		(at 148.59 129.54 180)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify right bottom)
		)
		(uuid "3133b56a-7536-41c7-b617-84faafc1c31b")
	)
	(label "PC-DATA-IN"
		(at 68.58 106.68 0)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify left bottom)
		)
		(uuid "44829eff-e304-4f27-8e6b-996eac419f13")
	)
	(label "CLOCK-RB6"
		(at 213.36 105.41 0)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify left bottom)
		)
		(uuid "4ff39452-92ac-45f6-83d9-0e4976ce3343")
	)
	(label "PC-DATA-OUT"
		(at 63.5 80.01 0)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify left bottom)
		)
		(uuid "809e6c93-a079-4f2a-9ce2-2926a243f521")
	)
	(label "VPP{slash}MCLR"
		(at 209.55 40.64 180)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify right bottom)
		)
		(uuid "9c77600e-5b1a-4acd-8067-b8a9094cd765")
	)
	(label "VPP_ON"
		(at 58.42 43.18 0)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify left bottom)
		)
		(uuid "9efc0275-667a-479c-a957-365c53e7be8d")
	)
	(label "PC-CLOCK-OUT"
		(at 59.69 129.54 0)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify left bottom)
		)
		(uuid "c101fd7e-81b3-4e3e-b7c8-a5e87156b0d3")
	)
	(label "VPP/MCLR"
		(at 213.36 88.9 0)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify left bottom)
		)
		(uuid "d01fa610-99f4-4205-bdf3-b942139a4706")
	)
	(label "DATA-RB7"
		(at 212.09 97.79 0)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify left bottom)
		)
		(uuid "dd3b7494-aebc-4607-b02a-df441adf890f")
	)
	(symbol
		(lib_id "pic_programmer:DB9")
		(at 31.75 91.44 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4c93")
		(property "Reference" "J1"
			(at 31.75 105.41 0)
			(effects
				(font
					(size 1.778 1.778)
				)
			)
		)
		(property "Value" "DB9-FEMAL"
			(at 31.75 77.47 0)
			(effects
				(font
					(size 1.778 1.778)
				)
			)
		)
		(property "Footprint" "Connector_Dsub:DSUB-9_Female_Horizontal_P2.77x2.84mm_EdgePinOffset7.70mm_Housed_MountingHolesOffset9.12mm"
			(at 27.3812 91.5416 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 31.75 91.44 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 31.75 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "71f18f80-08e1-4f0e-add6-2dcfc2ed3dfe")
		)
		(pin "2"
			(uuid "f5f950f1-88cd-42bd-872f-e9186699fd8e")
		)
		(pin "3"
			(uuid "c79db9f7-4d02-4c5a-ac56-a0c905364e96")
		)
		(pin "4"
			(uuid "9007116d-5ce9-4dcf-ad23-1123cb09661a")
		)
		(pin "5"
			(uuid "09e4d3ce-0a0c-40f3-8cc2-ea7787eb7b24")
		)
		(pin "6"
			(uuid "e6097017-9bfa-421a-bd78-e1de12a26782")
		)
		(pin "7"
			(uuid "6fa27541-8a6e-44cb-9133-c2d1d973d82f")
		)
		(pin "8"
			(uuid "e7ef4409-3fac-4c70-8f6b-74931f5eafad")
		)
		(pin "9"
			(uuid "adaf933d-9694-4bde-acb5-fa6141c9e6ea")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "J1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:74LS125")
		(at 100.33 43.18 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4cc8")
		(property "Reference" "U2"
			(at 100.33 40.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left bottom)
			)
		)
		(property "Value" "74HC125"
			(at 104.14 45.72 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(justify left top)
			)
		)
		(property "Footprint" "Package_DIP:DIP-14_W7.62mm_LongPads"
			(at 107.95 48.26 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 100.33 43.18 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 100.33 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "14"
			(uuid "ffb6697c-7504-48ea-a4de-bbfe251668e7")
		)
		(pin "7"
			(uuid "88840377-39db-432d-8494-4a6e77005a96")
		)
		(pin "1"
			(uuid "cdee1101-ad94-4531-a8d5-f18afd80a130")
		)
		(pin "2"
			(uuid "a483a076-9094-4dbd-b041-566d623eca0b")
		)
		(pin "3"
			(uuid "7fd93780-9232-4c16-aea1-4c46ba56e07e")
		)
		(pin "4"
			(uuid "e79fcf2f-1a7c-426f-a1c6-5572db604fc1")
		)
		(pin "5"
			(uuid "2f9d1615-f8b7-4882-b99d-c0bf7120fb42")
		)
		(pin "6"
			(uuid "2e6daba8-53fc-496c-9632-23b0e87e5c6e")
		)
		(pin "10"
			(uuid "a0bb3c4c-d82b-4285-a66b-9fdfde642cae")
		)
		(pin "8"
			(uuid "14b6eab4-428d-4042-a3b2-483920a86cf8")
		)
		(pin "9"
			(uuid "d6815f01-4dcb-46d8-8498-8e34cd475ffe")
		)
		(pin "11"
			(uuid "7799c290-5e74-4dae-a4db-12e6fe31ed62")
		)
		(pin "12"
			(uuid "3e152fad-9859-4cbb-8408-7fb772d11e59")
		)
		(pin "13"
			(uuid "94525efa-b30b-4186-9248-3a900ec7d802")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "U2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 78.74 43.18 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4cf4")
		(property "Reference" "R1"
			(at 78.74 41.148 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "10K"
			(at 78.74 43.18 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 76.2 39.37 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 78.74 43.18 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 78.74 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "cf9c4f95-7f3f-4fbc-ba4d-cd9bd7a274b3")
		)
		(pin "2"
			(uuid "17592329-e031-49c8-b4e9-1f33425af9d0")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 71.12 50.8 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4cfb")
		(property "Reference" "R2"
			(at 73.152 50.8 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "10K"
			(at 71.12 50.8 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 74.93 50.8 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 71.12 50.8 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 71.12 50.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "81465fb3-f7ec-4362-8787-9533750585c7")
		)
		(pin "2"
			(uuid "855f1644-922c-428c-9d08-b89e2f707e94")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:D_Schottky")
		(at 87.63 36.83 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d1b")
		(property "Reference" "D2"
			(at 90.17 36.83 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "BAT43"
			(at 85.09 36.83 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" "Diode_THT:D_DO-35_SOD27_P7.62mm_Horizontal"
			(at 83.82 38.1 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 87.63 36.83 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 87.63 36.83 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "d2f961db-2f98-4f5c-a21e-d9906d69dcbe")
		)
		(pin "2"
			(uuid "9572fb05-e38d-48d2-b31a-ca7e733295c5")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "D2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:D_Schottky")
		(at 87.63 50.8 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d25")
		(property "Reference" "D3"
			(at 90.17 50.8 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "BAT43"
			(at 85.09 50.8 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" "Diode_THT:D_DO-35_SOD27_P7.62mm_Horizontal"
			(at 91.44 50.8 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 87.63 50.8 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 87.63 50.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "bd792c3b-c341-47fb-8b1c-2eb669f99775")
		)
		(pin "2"
			(uuid "0af391d2-75d5-4d57-af7c-46fafd5482b2")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "D3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 102.87 48.26 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d38")
		(property "Reference" "#PWR035"
			(at 102.87 48.26 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 102.87 50.038 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 102.87 48.26 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 102.87 48.26 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 102.87 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "403cab6e-1893-4e4a-97ac-fc49abd9b1b1")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR035")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 87.63 55.88 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d3b")
		(property "Reference" "#PWR034"
			(at 87.63 55.88 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 87.63 57.658 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 87.63 55.88 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 87.63 55.88 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 87.63 55.88 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "780c0434-2a35-47e8-b57d-d3021b7b12b2")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR034")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VCC")
		(at 87.63 31.75 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d41")
		(property "Reference" "#PWR033"
			(at 87.63 29.21 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 87.63 29.21 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 87.63 31.75 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 87.63 31.75 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 87.63 31.75 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "278a2343-3bee-4ece-9c08-ce3ad057e9c7")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR033")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:74LS125")
		(at 110.49 80.01 0)
		(unit 2)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d59")
		(property "Reference" "U2"
			(at 110.49 77.47 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left bottom)
			)
		)
		(property "Value" "74HC125"
			(at 114.3 82.55 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(justify left top)
			)
		)
		(property "Footprint" "Package_DIP:DIP-14_W7.62mm_LongPads"
			(at 118.11 87.63 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 110.49 80.01 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 110.49 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "14"
			(uuid "86f40b2e-4b36-40a5-a6b5-a19d09a6bef1")
		)
		(pin "7"
			(uuid "df0bd3e2-6330-432f-99e9-bfc22b1d379f")
		)
		(pin "1"
			(uuid "5519625b-b789-403b-b3b7-9dbd9f94f2c4")
		)
		(pin "2"
			(uuid "4c27ac53-4628-4c0a-8a45-df483f78c5ae")
		)
		(pin "3"
			(uuid "4604e145-1d21-4c93-91c2-e18079cc8d94")
		)
		(pin "4"
			(uuid "19fa2510-**************-dade95c0e7ed")
		)
		(pin "5"
			(uuid "9d021d1d-0979-4f51-a5db-1864a3c93742")
		)
		(pin "6"
			(uuid "be83b187-57b6-465a-954c-503b0538f1c4")
		)
		(pin "10"
			(uuid "60b24805-673a-4db9-8d07-a81b6eb4cda9")
		)
		(pin "8"
			(uuid "c4859b96-5a07-4600-bf0a-8e103ab31c50")
		)
		(pin "9"
			(uuid "1754ecfa-c3f5-4c08-9f2c-2cbeb87b888e")
		)
		(pin "11"
			(uuid "88da5b55-af1a-4848-8e3d-68ab77e3a7f7")
		)
		(pin "12"
			(uuid "ad59cf74-4af7-418b-8cd8-8d55b9fc4e4e")
		)
		(pin "13"
			(uuid "62ef7dc9-dc8a-4857-ab4a-17b27899d137")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "U2")
					(unit 2)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 88.9 80.01 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d5a")
		(property "Reference" "R3"
			(at 88.9 77.978 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "10K"
			(at 88.9 80.01 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 88.9 76.2 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 88.9 80.01 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 88.9 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "196f698c-a6e3-4e76-b2d0-a59bb1d2ebb8")
		)
		(pin "2"
			(uuid "144bb25d-4d06-47c0-a60f-bd4db7b9c7a3")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 81.28 87.63 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d5b")
		(property "Reference" "R4"
			(at 83.312 87.63 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "10K"
			(at 81.28 87.63 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 78.74 87.63 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 81.28 87.63 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 81.28 87.63 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "521de44c-9c0f-4696-989e-a10d7c498eb7")
		)
		(pin "2"
			(uuid "0c4e2b5a-ce2f-46a4-9fe6-cc9bf245865b")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:D_Schottky")
		(at 97.79 73.66 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d5c")
		(property "Reference" "D4"
			(at 100.33 73.66 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "BAT43"
			(at 95.25 73.66 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" "Diode_THT:D_DO-35_SOD27_P7.62mm_Horizontal"
			(at 101.6 73.66 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 97.79 73.66 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 97.79 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "f0ebb5af-7ca5-4783-9987-23532bfa6d4a")
		)
		(pin "2"
			(uuid "11bff5eb-8560-48b5-916f-81f5250d164c")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "D4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:D_Schottky")
		(at 97.79 87.63 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d5d")
		(property "Reference" "D5"
			(at 100.33 87.63 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "BAT43"
			(at 95.25 87.63 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" "Diode_THT:D_DO-35_SOD27_P7.62mm_Horizontal"
			(at 101.6 88.9 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 97.79 87.63 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 97.79 87.63 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "39a1b28c-5c5f-4ae2-b09e-a12bb082ab78")
		)
		(pin "2"
			(uuid "fc373566-00cd-4d1c-bb88-2925ff67f43d")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "D5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 113.03 85.09 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d5e")
		(property "Reference" "#PWR032"
			(at 113.03 85.09 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 113.03 86.868 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 113.03 85.09 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 113.03 85.09 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 113.03 85.09 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "7491c4e2-4e1e-4ca7-86dd-2a0b7512fdab")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR032")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 97.79 91.44 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d5f")
		(property "Reference" "#PWR031"
			(at 97.79 91.44 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 97.79 93.218 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 97.79 91.44 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 97.79 91.44 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 97.79 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "36e689ea-c191-42f9-938b-192f43f53b6d")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR031")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VCC")
		(at 97.79 68.58 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d60")
		(property "Reference" "#PWR030"
			(at 97.79 66.04 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 97.79 66.04 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 97.79 68.58 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 97.79 68.58 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 97.79 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "d4cd7aed-ce54-4e4a-899f-0517e29080c3")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR030")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:74LS125")
		(at 110.49 129.54 0)
		(unit 3)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d61")
		(property "Reference" "U2"
			(at 110.49 127 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left bottom)
			)
		)
		(property "Value" "74HC125"
			(at 114.3 130.81 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(justify left top)
			)
		)
		(property "Footprint" "Package_DIP:DIP-14_W7.62mm_LongPads"
			(at 121.92 133.35 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 110.49 129.54 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 110.49 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "14"
			(uuid "17bc559f-8beb-4ddc-9225-9d499ae2fe97")
		)
		(pin "7"
			(uuid "d35b02b7-3dc8-4650-bd2e-245203d56031")
		)
		(pin "1"
			(uuid "163c7c0c-11dd-4097-9ef8-c6b5f67fc50e")
		)
		(pin "2"
			(uuid "316f7ff4-9366-4a42-80be-9e29cf11448c")
		)
		(pin "3"
			(uuid "223a8fe8-73ac-4521-a8de-dd0bee42e994")
		)
		(pin "4"
			(uuid "d3f65777-95e7-46b0-9f6c-a33a62467912")
		)
		(pin "5"
			(uuid "1804b3ed-d330-4973-9a79-85f19eb8a628")
		)
		(pin "6"
			(uuid "b4b7f0ad-363b-4709-83a6-534fbe25f4d4")
		)
		(pin "10"
			(uuid "7193d50d-0994-4895-aebb-3a72b77d2068")
		)
		(pin "8"
			(uuid "b9e3c1cd-0e31-49d8-9f48-97732d86cac1")
		)
		(pin "9"
			(uuid "ee359660-91ea-4916-a50c-02399277f34c")
		)
		(pin "11"
			(uuid "7eeaf12d-ab28-43f5-858c-d3b9ef64b401")
		)
		(pin "12"
			(uuid "48b489c3-9ede-4e51-831a-8f17f0fe14a7")
		)
		(pin "13"
			(uuid "6d728bca-e422-4c95-9ad2-d4626bc070c2")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "U2")
					(unit 3)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 88.9 129.54 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d62")
		(property "Reference" "R5"
			(at 88.9 127.508 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "10K"
			(at 88.9 129.54 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 89.2048 131.572 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 88.9 129.54 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 88.9 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "e4067aa1-2607-41ed-8c34-a6b0ce21ae30")
		)
		(pin "2"
			(uuid "51158373-577b-4e58-8216-e47e86d33874")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 81.28 137.16 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d63")
		(property "Reference" "R6"
			(at 83.312 137.16 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "10K"
			(at 81.28 137.16 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 78.74 137.16 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 81.28 137.16 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 81.28 137.16 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "c762b24a-ceea-407b-ba22-a0d5a04ed921")
		)
		(pin "2"
			(uuid "0c0d9d43-67d5-4d69-ad11-0af3a7c45bb6")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:D_Schottky")
		(at 97.79 123.19 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d64")
		(property "Reference" "D6"
			(at 100.33 123.19 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "BAT43"
			(at 95.25 123.19 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" "Diode_THT:D_DO-35_SOD27_P7.62mm_Horizontal"
			(at 93.98 123.19 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 97.79 123.19 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 97.79 123.19 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "080bcc46-c2b2-4c24-8375-4ec8e70b195b")
		)
		(pin "2"
			(uuid "66b99f82-4bd2-408c-8793-a45035ffccc5")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "D6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:D_Schottky")
		(at 97.79 137.16 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d65")
		(property "Reference" "D7"
			(at 100.33 137.16 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "BAT43"
			(at 95.25 137.16 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" "Diode_THT:D_DO-35_SOD27_P7.62mm_Horizontal"
			(at 101.6 137.16 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 97.79 137.16 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 97.79 137.16 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "ad5bfd8e-b4ae-4115-8c5b-13b44b2e83bd")
		)
		(pin "2"
			(uuid "10855504-4c6b-4d63-9aa2-9b5c20913c9f")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "D7")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 113.03 134.62 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d66")
		(property "Reference" "#PWR029"
			(at 113.03 134.62 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 113.03 136.398 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 113.03 134.62 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 113.03 134.62 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 113.03 134.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "6ca4ea04-8846-4ba6-9345-9f5651e0571f")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR029")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 97.79 142.24 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d67")
		(property "Reference" "#PWR028"
			(at 97.79 142.24 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 97.79 144.018 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 97.79 142.24 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 97.79 142.24 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 97.79 142.24 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "c5efa5b3-42d9-4498-84b0-1a226e2eaeaa")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR028")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VCC")
		(at 97.79 118.11 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d68")
		(property "Reference" "#PWR027"
			(at 97.79 115.57 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 97.79 115.57 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 97.79 118.11 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 97.79 118.11 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 97.79 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "167896a0-2580-4e14-b24b-715a38247601")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR027")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:74LS125")
		(at 110.49 106.68 0)
		(mirror y)
		(unit 4)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d6b")
		(property "Reference" "U2"
			(at 110.49 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left bottom)
			)
		)
		(property "Value" "74HC125"
			(at 105.41 110.49 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(justify left top)
			)
		)
		(property "Footprint" "Package_DIP:DIP-14_W7.62mm_LongPads"
			(at 101.6 109.22 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 110.49 106.68 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 110.49 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "14"
			(uuid "4f12f531-7959-4c5d-a84f-d2024f52bb37")
		)
		(pin "7"
			(uuid "f0901900-012c-41fb-a4eb-ce8b1633ac5a")
		)
		(pin "1"
			(uuid "6a75ee13-9474-41b4-bfd3-2799a276daf0")
		)
		(pin "2"
			(uuid "14d0c46e-35e1-4bf5-a841-bafb170a09ca")
		)
		(pin "3"
			(uuid "72ebe2a7-7459-4022-a2fb-6a23cbf37d8b")
		)
		(pin "4"
			(uuid "52e90ed4-3456-480b-850f-a4df500cd377")
		)
		(pin "5"
			(uuid "3d84eabf-3dfa-4792-8011-9e9020b5abb4")
		)
		(pin "6"
			(uuid "1f1acfe5-d4db-43be-9b2c-e933d987ac06")
		)
		(pin "10"
			(uuid "7a6a5824-a5f4-4f20-b8de-b48fafad33ec")
		)
		(pin "8"
			(uuid "ef359bcc-3815-4b18-96c7-c30cd18fb55c")
		)
		(pin "9"
			(uuid "9b96cd39-398f-4670-ac91-2fe3e452cd9a")
		)
		(pin "11"
			(uuid "878cd0a7-0544-4b81-bb57-a17ceb70fda8")
		)
		(pin "12"
			(uuid "51ec8b1a-12c2-40cc-877c-2d2c424cf64d")
		)
		(pin "13"
			(uuid "e4fe4362-4fba-4f77-8230-a9054c74ac85")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "U2")
					(unit 4)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 107.95 111.76 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d75")
		(property "Reference" "#PWR026"
			(at 107.95 111.76 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 107.95 113.538 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 107.95 111.76 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 107.95 111.76 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 107.95 111.76 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "32ed33c4-a203-4c04-8f87-5155b2c48b20")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR026")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 129.54 80.01 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d85")
		(property "Reference" "R12"
			(at 129.54 77.978 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "470"
			(at 129.54 80.01 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 129.54 76.2 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 129.54 80.01 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 129.54 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "3172f001-bd85-487e-8d80-06196d65f6d3")
		)
		(pin "2"
			(uuid "f8ad9be5-4269-4740-be36-cdada26ba7c1")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R12")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 129.54 129.54 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d8d")
		(property "Reference" "R13"
			(at 129.54 127.508 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "470"
			(at 129.54 129.54 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 129.54 125.73 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 129.54 129.54 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 129.54 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "95e8ac70-9b6c-4102-ba45-a96d1f945443")
		)
		(pin "2"
			(uuid "8f251078-bd77-4b03-9ff2-0e7cd2963834")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R13")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 119.38 43.18 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4d92")
		(property "Reference" "R8"
			(at 119.38 41.148 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "1K"
			(at 119.38 43.18 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 119.38 39.37 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 119.38 43.18 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 119.38 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "429ce0e2-bd1c-44ed-a2a7-595b7d3581c7")
		)
		(pin "2"
			(uuid "f16741be-3a80-4eed-aa68-86a88f779c1e")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R8")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 81.28 91.44 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4dab")
		(property "Reference" "#PWR025"
			(at 81.28 91.44 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 81.28 93.218 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 81.28 91.44 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 81.28 91.44 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 81.28 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "f3a6761d-4dcd-4a58-b1ed-d6e9c5e8d019")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR025")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 81.28 140.97 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4dae")
		(property "Reference" "#PWR024"
			(at 81.28 140.97 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 81.28 142.748 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 81.28 140.97 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 81.28 140.97 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 81.28 140.97 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "62bb9be4-32f4-4086-87a5-b00aa75ffa97")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR024")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 71.12 54.61 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4db3")
		(property "Reference" "#PWR023"
			(at 71.12 54.61 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 71.12 56.388 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 71.12 54.61 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 71.12 54.61 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 71.12 54.61 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "336e0910-0504-472d-9dec-a83e755e2197")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR023")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 43.18 101.6 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4e06")
		(property "Reference" "#PWR022"
			(at 43.18 101.6 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 44.958 101.6 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 43.18 101.6 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 43.18 101.6 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 43.18 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "048b7644-1633-4221-a008-26de9eec7958")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR022")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:BC237")
		(at 135.89 43.18 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "00000000-0000-0000-0000-0000442a4eb9")
		(property "Reference" "Q1"
			(at 140.7668 41.1642 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "BC237"
			(at 140.7668 43.5885 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Package_TO_SOT_THT:TO-92"
			(at 140.7668 45.6043 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(justify left)
			)
		)
		(property "Datasheet" ""
			(at 135.89 43.18 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 135.89 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "2004f10c-4cee-4319-959a-c1cda40152e0")
		)
		(pin "2"
			(uuid "08ec5f0f-5eba-46ce-befa-8ffca02d4295")
		)
		(pin "3"
			(uuid "14c3e497-2f72-4cfb-8c21-e0ca6e7189d4")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "Q1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 138.43 48.26 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4f1c")
		(property "Reference" "#PWR021"
			(at 138.43 48.26 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 138.43 50.038 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 138.43 48.26 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 138.43 48.26 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 138.43 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "24ffa9da-ff6d-4df9-a2cc-df75754aea56")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR021")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 146.05 35.56 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4f23")
		(property "Reference" "R11"
			(at 146.05 33.528 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "22K"
			(at 146.05 35.56 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 141.1986 33.7566 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 146.05 35.56 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 146.05 35.56 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "c905d805-6066-43eb-8bda-27db76693a77")
		)
		(pin "2"
			(uuid "324acb47-2f7e-4445-8e7d-cdee221cea6e")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R11")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 154.94 25.4 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4f2a")
		(property "Reference" "R7"
			(at 156.972 25.4 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "10K"
			(at 154.94 25.4 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 158.75 25.4 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 154.94 25.4 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 154.94 25.4 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "41790005-f63d-47f1-9b1d-f27cb0c1ebb9")
		)
		(pin "2"
			(uuid "fbf261d1-ce63-47a1-97a1-03b247e8df39")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R7")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:BC307")
		(at 163.83 35.56 0)
		(mirror x)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "00000000-0000-0000-0000-0000442a4f30")
		(property "Reference" "Q2"
			(at 168.7068 33.3399 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(property "Value" "BC307"
			(at 168.7068 36.1728 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(property "Footprint" "Package_TO_SOT_THT:TO-92"
			(at 168.7068 38.3929 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(justify left)
			)
		)
		(property "Datasheet" ""
			(at 163.83 35.56 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 163.83 35.56 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "c5cd52a2-a99a-4913-9089-20047df91f1f")
		)
		(pin "2"
			(uuid "640d145a-29ac-4fa5-a5d8-72092db785cf")
		)
		(pin "3"
			(uuid "9973093c-a2fd-4eec-9078-4e971b53798b")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "Q2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VPP")
		(at 154.94 17.78 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4f44")
		(property "Reference" "#PWR122"
			(at 154.94 12.7 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(hide yes)
			)
		)
		(property "Value" "VPP"
			(at 154.94 13.97 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 154.94 17.78 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 154.94 17.78 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 154.94 17.78 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "83b19043-4b7c-457b-91a8-704712eeac34")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR122")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VPP")
		(at 166.37 27.94 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4f48")
		(property "Reference" "#PWR123"
			(at 166.37 22.86 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(hide yes)
			)
		)
		(property "Value" "VPP"
			(at 166.37 24.13 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 166.37 27.94 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 166.37 27.94 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 166.37 27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "610d0f34-56bf-41c2-b512-484dff469097")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR123")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 166.37 48.26 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4f52")
		(property "Reference" "R9"
			(at 168.402 48.26 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "2.2K"
			(at 166.37 48.26 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 170.18 48.26 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 166.37 48.26 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 166.37 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "bf0618aa-ab51-458e-8f2a-cb1f5145a744")
		)
		(pin "2"
			(uuid "eb3d7705-d8ac-4f07-8f69-3e9a983717d0")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R9")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:LED")
		(at 166.37 60.96 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4f5d")
		(property "Reference" "D8"
			(at 163.83 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "RED-LED"
			(at 168.91 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "LED_THT:LED_D5.0mm"
			(at 170.18 60.96 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Datasheet" ""
			(at 166.37 60.96 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 166.37 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Champ4" "Low Current Led"
			(at 162.56 59.69 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(pin "1"
			(uuid "26ee89e6-0f42-4ef4-a12f-0053e83e2d65")
		)
		(pin "2"
			(uuid "3e28d33b-e400-4567-bf5a-890328a27697")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "D8")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:CONN_2")
		(at 25.4 171.45 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a4fe7")
		(property "Reference" "P1"
			(at 26.67 171.45 90)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "CONN_2"
			(at 24.13 171.45 90)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" "TerminalBlock_Altech:Altech_AK300_1x02_P5.00mm_45-Degree"
			(at 25.4 176.53 0)
			(effects
				(font
					(size 0.508 0.508)
				)
			)
		)
		(property "Datasheet" ""
			(at 25.4 171.45 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 25.4 171.45 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "98659442-0eb2-480d-9090-f51d33105c0a")
		)
		(pin "2"
			(uuid "1a04c1af-1a0a-4a6b-bab6-162b7ae253a2")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "P1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:D")
		(at 41.91 168.91 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a500b")
		(property "Reference" "D1"
			(at 41.91 171.45 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "1N4004"
			(at 41.91 166.37 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" "Diode_THT:D_DO-35_SOD27_P12.70mm_Horizontal"
			(at 42.0116 172.72 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 41.91 168.91 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 41.91 168.91 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "de7bb502-516e-4ab1-a006-2a767ca8a863")
		)
		(pin "2"
			(uuid "cd612996-ea0c-47e5-b55a-5b775d132516")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "D1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 35.56 175.26 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a500f")
		(property "Reference" "#PWR020"
			(at 35.56 175.26 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 35.56 177.038 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 35.56 175.26 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 35.56 175.26 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 35.56 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "a74d1cb7-8321-4100-9e4b-d86faa4b5bd8")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR020")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:CP")
		(at 49.53 175.26 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a501d")
		(property "Reference" "C2"
			(at 50.8 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "220uF"
			(at 50.8 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_THT:CP_Axial_L18.0mm_D6.5mm_P25.00mm_Horizontal"
			(at 53.34 179.07 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 49.53 175.26 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 49.53 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "455f2b8a-c109-4533-a268-00d8add033a4")
		)
		(pin "2"
			(uuid "bea98637-b041-4d44-98e0-22a803e4cdf4")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "C2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 49.53 179.07 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a5023")
		(property "Reference" "#PWR019"
			(at 49.53 179.07 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 49.53 180.848 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 49.53 179.07 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 49.53 179.07 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 49.53 179.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "402b4f82-f147-42ff-914f-efdfa84d285f")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR019")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 68.58 176.53 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a5050")
		(property "Reference" "#PWR018"
			(at 68.58 176.53 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 68.58 178.308 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 68.58 176.53 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 68.58 176.53 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 68.58 176.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "72109ad3-edf9-4be2-bb15-0a13596f651b")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR018")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:CP")
		(at 81.28 175.26 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a5056")
		(property "Reference" "C1"
			(at 82.55 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "100µF"
			(at 82.55 177.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_THT:CP_Axial_L18.0mm_D6.5mm_P25.00mm_Horizontal"
			(at 88.9 179.07 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 81.28 175.26 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 81.28 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "99df22c4-b121-4c08-b42c-42a42d75007a")
		)
		(pin "2"
			(uuid "9cd53b79-1f16-42c5-b1ac-035592fab7ff")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "C1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 81.28 179.07 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a5057")
		(property "Reference" "#PWR017"
			(at 81.28 179.07 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 81.28 180.848 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 81.28 179.07 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 81.28 179.07 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 81.28 179.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "4e1e505e-8639-412b-af93-c0426a8dba78")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR017")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 97.79 168.91 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a5083")
		(property "Reference" "R14"
			(at 97.79 166.878 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "470"
			(at 97.79 168.91 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 97.917 171.069 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 97.79 168.91 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 97.79 168.91 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "aa2c0a1b-9753-411f-a252-3db9390fc823")
		)
		(pin "2"
			(uuid "b5e7643f-d437-4b3c-9703-c26299dc97fd")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R14")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:LED")
		(at 105.41 176.53 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a5084")
		(property "Reference" "D9"
			(at 102.87 176.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "GREEN-LED"
			(at 107.95 176.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "LED_THT:LED_D5.0mm"
			(at 109.22 177.8 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Datasheet" ""
			(at 105.41 176.53 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 105.41 176.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Champ4" "GREEN LED"
			(at 101.6 177.8 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(pin "1"
			(uuid "319800fe-bd3e-49fd-92c4-ff292d7443fa")
		)
		(pin "2"
			(uuid "071fde49-1ae2-4eb1-ad01-ba19e0bb598b")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "D9")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 105.41 181.61 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a5095")
		(property "Reference" "#PWR016"
			(at 105.41 181.61 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 105.41 183.388 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 105.41 181.61 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 105.41 181.61 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 105.41 181.61 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "41790cd6-42a1-4cca-87fc-9359ac864dd6")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR016")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VCC")
		(at 86.36 167.64 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a50b3")
		(property "Reference" "#PWR015"
			(at 86.36 165.1 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 86.36 165.1 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 86.36 167.64 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 86.36 167.64 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 86.36 167.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "af0be450-1eaa-4d03-84aa-80b0f4af243f")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR015")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 179.07 50.8 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a50bf")
		(property "Reference" "R17"
			(at 181.102 50.8 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "22K"
			(at 179.07 50.8 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 182.88 50.8 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 179.07 50.8 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 179.07 50.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "eca7a12d-48f8-45cc-9043-a6195a3563fb")
		)
		(pin "2"
			(uuid "a6995c3c-f9d5-4ab3-b022-046a41fcb6c4")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R17")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 179.07 54.61 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a50c2")
		(property "Reference" "#PWR014"
			(at 179.07 54.61 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 179.07 56.388 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 179.07 54.61 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 179.07 54.61 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 179.07 54.61 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "be333d2e-ce3d-4b82-9e95-2bd2e78309b2")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR014")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:INDUCTOR")
		(at 231.14 130.81 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a57be")
		(property "Reference" "L1"
			(at 231.14 132.08 90)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "22uH"
			(at 231.14 128.27 90)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" "Inductor_THT:L_Radial_D7.8mm_P5.00mm_Fastron_07HCP"
			(at 231.14 127 90)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Datasheet" ""
			(at 231.14 130.81 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 231.14 130.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "8c703c6b-1533-4d52-b453-b8ad2931def0")
		)
		(pin "2"
			(uuid "0b84be7f-ff97-47b2-948d-dedc667529eb")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "L1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VCC")
		(at 196.85 118.11 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a57cb")
		(property "Reference" "#PWR013"
			(at 196.85 115.57 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 196.85 115.57 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 196.85 118.11 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 196.85 118.11 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 196.85 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "fbd21129-b7fb-4647-bf4f-064a21f9be0d")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR013")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 189.23 156.21 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a580b")
		(property "Reference" "#PWR012"
			(at 189.23 156.21 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 189.23 157.988 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 189.23 156.21 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 189.23 156.21 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 189.23 156.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "7eb1aa9f-e27a-4763-9b2e-f9d84300a4df")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR012")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VPP")
		(at 260.35 135.89 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a5846")
		(property "Reference" "#PWR134"
			(at 260.35 130.81 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(hide yes)
			)
		)
		(property "Value" "VPP"
			(at 260.35 132.08 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 260.35 135.89 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 260.35 135.89 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 260.35 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "93f2f6eb-a886-4042-89c6-19727abf1c0b")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR134")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:CP")
		(at 267.97 137.16 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a584c")
		(property "Reference" "C3"
			(at 265.43 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "22uF/25V"
			(at 276.86 133.35 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_THT:C_Axial_L12.0mm_D6.5mm_P20.00mm_Horizontal"
			(at 267.97 139.7 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 267.97 137.16 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 267.97 137.16 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "133a11b8-fb5e-46c0-90dc-f932fae6b5b5")
		)
		(pin "2"
			(uuid "ecbd1a5e-fab9-40f4-89bc-b0b9953acc03")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "C3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 274.32 138.43 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a5852")
		(property "Reference" "#PWR011"
			(at 274.32 138.43 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 274.32 140.208 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 274.32 138.43 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 274.32 138.43 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 274.32 138.43 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "2ac007e0-8723-41ad-a17d-6b05795c2090")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR011")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VCC")
		(at 238.76 128.27 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a5882")
		(property "Reference" "#PWR010"
			(at 238.76 125.73 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 238.76 125.73 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 238.76 128.27 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 238.76 128.27 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 238.76 128.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "681d0bff-259d-4a4d-aee7-d226c228d0e7")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR010")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:PWR_FLAG")
		(at 255.27 135.89 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a5893")
		(property "Reference" "#FLG09"
			(at 255.27 129.032 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "PWR_FLAG"
			(at 255.27 130.048 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 255.27 135.89 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 255.27 135.89 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 255.27 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "ca821bc6-9ff0-4e70-9809-47b3549a7545")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#FLG09")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:C")
		(at 228.6 152.4 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a58b1")
		(property "Reference" "C5"
			(at 226.06 151.13 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10nF"
			(at 232.41 148.59 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_THT:C_Disc_D5.1mm_W3.2mm_P5.00mm"
			(at 228.6 154.94 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 228.6 152.4 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 228.6 152.4 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "033d940c-ed69-4fab-9fbc-bb78c68a1926")
		)
		(pin "2"
			(uuid "c709b076-a593-4753-9cf5-29cdb36bfe76")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "C5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 234.95 152.4 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a58b8")
		(property "Reference" "#PWR08"
			(at 234.95 152.4 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 236.728 152.4 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 234.95 152.4 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 234.95 152.4 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 234.95 152.4 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "82c2898d-362e-43ab-9634-dc862124c561")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR08")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 254 156.21 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a58d7")
		(property "Reference" "R15"
			(at 254 154.178 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "6.2K"
			(at 254 156.21 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 254.2286 158.1912 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 254 156.21 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 254 156.21 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "15cb22d1-1839-4dd4-97c0-19c7eac83620")
		)
		(pin "2"
			(uuid "e8aaa04c-f19e-44a9-88c4-0b4c5bdeb2e5")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R15")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 254 143.51 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a58dc")
		(property "Reference" "R16"
			(at 254 141.478 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "62K"
			(at 254 143.51 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 254.0762 145.3134 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 254 143.51 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 254 143.51 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "40274855-4e1a-4729-9eee-fe43bc56765e")
		)
		(pin "2"
			(uuid "c3180b3a-8368-4223-8c91-f737551d52c8")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R16")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 260.35 157.48 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a58df")
		(property "Reference" "#PWR07"
			(at 260.35 157.48 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 260.35 159.258 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 260.35 157.48 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 260.35 157.48 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 260.35 157.48 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "5c8765ad-2627-49bb-8db7-63808c0ee007")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR07")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:LT1373")
		(at 196.85 137.16 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a5e20")
		(property "Reference" "U4"
			(at 212.09 124.46 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "LT1373"
			(at 184.15 124.46 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" "Package_DIP:DIP-8_W7.62mm_LongPads"
			(at 186.69 125.73 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 196.85 137.16 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 196.85 137.16 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "0c82cc7b-9424-4094-8e4f-3b82afa2a080")
		)
		(pin "2"
			(uuid "fd1e30f3-68d5-4783-b473-989caaad43a0")
		)
		(pin "3"
			(uuid "11184146-965a-4fb2-a14d-ae0b1e50c87b")
		)
		(pin "4"
			(uuid "29f99ad4-6e83-4a34-a8e1-f7542e843ec7")
		)
		(pin "5"
			(uuid "18470784-5a25-4574-b290-48d10e318872")
		)
		(pin "6"
			(uuid "9f95b6a7-4b29-43cf-a99b-7672b4e83aea")
		)
		(pin "7"
			(uuid "aaafbcd3-c2bb-44b8-9fea-952d5997e528")
		)
		(pin "8"
			(uuid "beb3a188-2da7-4cbc-b362-f6c06abdae3f")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "U4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:C")
		(at 224.79 160.02 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a5f61")
		(property "Reference" "C4"
			(at 223.52 158.75 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "0"
			(at 227.33 158.75 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_THT:C_Disc_D5.1mm_W3.2mm_P5.00mm"
			(at 229.87 161.29 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 224.79 160.02 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 224.79 160.02 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b64ca10a-39f7-419f-b191-d107bbc31768")
		)
		(pin "2"
			(uuid "65c2300c-af2f-4ac8-a546-b86ee8cb3724")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "C4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 212.09 154.94 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a5f83")
		(property "Reference" "R10"
			(at 212.09 152.908 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "5,1K"
			(at 212.09 154.94 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 212.09 157.48 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 212.09 154.94 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 212.09 154.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "3cc3ddbe-d34b-414e-b352-1bc5e46ff876")
		)
		(pin "2"
			(uuid "794df13b-0bf2-484e-970f-1ec899e91cd9")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R10")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:D_Schottky")
		(at 238.76 137.16 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a6026")
		(property "Reference" "D10"
			(at 238.76 139.7 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "SCHOTTKY"
			(at 238.76 134.62 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" "Diode_THT:D_DO-35_SOD27_P12.70mm_Horizontal"
			(at 238.76 133.35 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 238.76 137.16 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 238.76 137.16 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "7bbc6dd2-7d07-48d1-b13d-b6c5e1d58b7c")
		)
		(pin "2"
			(uuid "1fb46d8e-f38c-446b-8ce7-308e46e25b81")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "D10")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:PWR_FLAG")
		(at 40.64 175.26 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442a8330")
		(property "Reference" "#FLG06"
			(at 40.64 182.118 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "PWR_FLAG"
			(at 40.64 181.102 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 40.64 175.26 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 40.64 175.26 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 40.64 175.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "54225e78-0dd5-4ff4-aae3-5773fe3067ac")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#FLG06")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 166.37 67.31 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000442aabc2")
		(property "Reference" "#PWR05"
			(at 166.37 67.31 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 166.37 69.088 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 166.37 67.31 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 166.37 67.31 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 166.37 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "80ffe8c9-1ff8-446b-8220-6165ef1d3104")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR05")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 186.69 40.64 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-000044369638")
		(property "Reference" "R18"
			(at 186.69 38.608 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "220"
			(at 186.69 40.64 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 186.69 36.83 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 186.69 40.64 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 186.69 40.64 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "333dc535-e300-48ba-b4d7-d353bb7744fb")
		)
		(pin "2"
			(uuid "28b191eb-54a5-421e-aed4-f2edd2d0a838")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R18")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:POT")
		(at 243.84 149.86 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000443d0101")
		(property "Reference" "RV1"
			(at 246.38 149.86 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "1K"
			(at 243.84 149.86 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "footprints:RV2X4"
			(at 247.65 149.86 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 243.84 149.86 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 243.84 149.86 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "3630c910-0a14-4b7b-89d9-86241fe30ae0")
		)
		(pin "2"
			(uuid "3d1c3154-15eb-40cd-b642-4ca158fabf19")
		)
		(pin "3"
			(uuid "74c6809d-0aeb-48b7-97fc-9bfe3dcd07ed")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "RV1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VCC")
		(at 187.96 67.31 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00004638ab33")
		(property "Reference" "#PWR04"
			(at 187.96 64.77 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 187.96 64.77 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 187.96 67.31 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 187.96 67.31 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 187.96 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b26ff2ed-c348-4e32-80ac-3eff55f2b706")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR04")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:BC307")
		(at 208.28 73.66 0)
		(mirror x)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00004639b996")
		(property "Reference" "Q3"
			(at 212.09 73.66 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "BC307"
			(at 205.8416 77.47 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" "Package_TO_SOT_THT:TO-92"
			(at 205.74 78.74 0)
			(effects
				(font
					(size 0.508 0.508)
				)
			)
		)
		(property "Datasheet" ""
			(at 208.28 73.66 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 208.28 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "4e6b4659-d07b-4c8f-abb8-981b667254f2")
		)
		(pin "2"
			(uuid "1a38e88c-4eb4-460e-be9f-10672f565780")
		)
		(pin "3"
			(uuid "34eb98f4-455f-4bc3-ab31-f77b558b7054")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "Q3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 194.31 73.66 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00004639b9b0")
		(property "Reference" "R19"
			(at 194.31 71.628 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "2.2K"
			(at 194.31 73.66 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 194.31 75.4888 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 194.31 73.66 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 194.31 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "a406e06b-76a6-4125-92f7-e3b81fd2d9a4")
		)
		(pin "2"
			(uuid "11e9d1fc-056e-4211-9f64-5544e1b6c71e")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R19")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 194.31 67.31 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00004639b9b3")
		(property "Reference" "R20"
			(at 194.31 65.278 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "2.2K"
			(at 194.31 67.31 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 194.31 64.0588 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 194.31 67.31 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 194.31 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "2ea2ac2b-4fef-4af1-b85f-457c6f1db886")
		)
		(pin "2"
			(uuid "480edfb8-5022-4fae-bf20-76078c689b58")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R20")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:R")
		(at 200.66 81.28 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00004639b9e9")
		(property "Reference" "R21"
			(at 200.66 79.248 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "470"
			(at 200.66 81.28 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P10.16mm_Horizontal"
			(at 200.9394 83.2104 90)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 200.66 81.28 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 200.66 81.28 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "2db1249c-ec93-4436-b9c3-27fbf6f10b45")
		)
		(pin "2"
			(uuid "cb32e0bf-dfe6-459b-b88d-dcec200ad7b4")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "R21")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:LED")
		(at 194.31 88.9 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00004639b9ea")
		(property "Reference" "D12"
			(at 191.77 88.9 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Value" "YELLOW-LED"
			(at 196.85 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "LED_THT:LED_D5.0mm"
			(at 190.5 88.9 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Datasheet" ""
			(at 194.31 88.9 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 194.31 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b8b7cfbe-f10a-4829-abd3-95d35ba69c1e")
		)
		(pin "2"
			(uuid "af45d6ee-2cf5-4555-b353-b5ad29965742")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "D12")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 194.31 93.98 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00004639b9eb")
		(property "Reference" "#PWR03"
			(at 194.31 93.98 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 194.31 95.758 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 194.31 93.98 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 194.31 93.98 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 194.31 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "4d647c5d-b0cb-49f7-a846-af9ec8286783")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR03")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VCC")
		(at 210.82 68.58 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00004639ba17")
		(property "Reference" "#PWR02"
			(at 210.82 66.04 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 210.82 66.04 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 210.82 68.58 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 210.82 68.58 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 210.82 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "5b88810e-3e3b-48d0-9115-75161b6f0737")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR02")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:D_Schottky")
		(at 181.61 73.66 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00004639ba28")
		(property "Reference" "D11"
			(at 181.61 71.12 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "BAT43"
			(at 181.61 76.2 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" "Diode_THT:D_DO-35_SOD27_P7.62mm_Horizontal"
			(at 181.61 69.85 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 181.61 73.66 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 181.61 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b5dff809-**************-0917c34c5190")
		)
		(pin "2"
			(uuid "cb482d32-871d-4d88-ba36-6c285d171e45")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "D11")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:JUMPER")
		(at 219.71 72.39 270)
		(unit 1)
		(exclude_from_sim yes)
		(in_bom no)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00004639baf8")
		(property "Reference" "JP1"
			(at 223.52 72.39 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "JUMPER"
			(at 217.678 72.39 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" "Jumper:SolderJumper-2_P1.3mm_Open_TrianglePad1.0x1.5mm"
			(at 224.79 72.39 0)
			(effects
				(font
					(size 0.381 0.381)
				)
			)
		)
		(property "Datasheet" ""
			(at 219.71 72.39 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 219.71 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "82a8fdde-d248-439d-804e-0cacae046daa")
		)
		(pin "2"
			(uuid "98fad3e5-c885-4b8b-be84-3aefb7dcee26")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "JP1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VCC")
		(at 219.71 64.77 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00004639bb04")
		(property "Reference" "#PWR01"
			(at 219.71 62.23 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 219.71 62.23 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 219.71 64.77 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 219.71 64.77 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 219.71 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "3e9a0e6f-59b1-4b12-ba49-948bc958ce8d")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR01")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:C")
		(at 144.78 25.4 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-0000464ad280")
		(property "Reference" "C9"
			(at 146.05 22.86 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "220nF"
			(at 146.05 27.94 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_THT:C_Disc_D5.1mm_W3.2mm_P5.00mm"
			(at 148.59 29.21 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 144.78 25.4 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 144.78 25.4 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "fd9ae868-48b9-4197-8fb2-fd8db6394944")
		)
		(pin "2"
			(uuid "dd33e910-486e-404b-9d87-118d13491c88")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "C9")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:MOUNTING_HOLE")
		(at 162.56 172.72 0)
		(unit 1)
		(exclude_from_sim yes)
		(in_bom no)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-000054020bea")
		(property "Reference" "P101"
			(at 164.592 172.72 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(justify left)
			)
		)
		(property "Value" "CONN_1"
			(at 162.56 171.323 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" "MountingHole:MountingHole_4.3mm_M4"
			(at 162.56 172.72 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 162.56 172.72 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 162.56 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "P101")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:MOUNTING_HOLE")
		(at 162.56 176.53 0)
		(unit 1)
		(exclude_from_sim yes)
		(in_bom no)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-000054020da9")
		(property "Reference" "P102"
			(at 164.592 176.53 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(justify left)
			)
		)
		(property "Value" "CONN_1"
			(at 162.56 175.133 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" "MountingHole:MountingHole_4.3mm_M4"
			(at 162.56 176.53 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 162.56 176.53 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 162.56 176.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "P102")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:MOUNTING_HOLE")
		(at 162.56 180.34 0)
		(unit 1)
		(exclude_from_sim yes)
		(in_bom no)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-000054020dc2")
		(property "Reference" "P103"
			(at 164.592 180.34 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(justify left)
			)
		)
		(property "Value" "CONN_1"
			(at 162.56 178.943 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" "MountingHole:MountingHole_4.3mm_M4"
			(at 162.56 180.34 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 162.56 180.34 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 162.56 180.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "P103")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:MOUNTING_HOLE")
		(at 162.56 184.15 0)
		(unit 1)
		(exclude_from_sim yes)
		(in_bom no)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-000054020de3")
		(property "Reference" "P104"
			(at 164.592 184.15 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(justify left)
			)
		)
		(property "Value" "CONN_1"
			(at 162.56 182.753 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" "MountingHole:MountingHole_4.3mm_M4"
			(at 162.56 184.15 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 162.56 184.15 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 162.56 184.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "P104")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:MOUNTING_HOLE")
		(at 162.56 187.96 0)
		(unit 1)
		(exclude_from_sim yes)
		(in_bom no)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-000054020e5d")
		(property "Reference" "P105"
			(at 164.592 187.96 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(justify left)
			)
		)
		(property "Value" "CONN_1"
			(at 162.56 186.563 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" "MountingHole:MountingHole_4.3mm_M4"
			(at 162.56 187.96 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 162.56 187.96 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 162.56 187.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "P105")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:MOUNTING_HOLE")
		(at 162.56 191.77 0)
		(unit 1)
		(exclude_from_sim yes)
		(in_bom no)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-000054020e76")
		(property "Reference" "P106"
			(at 164.592 191.77 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(justify left)
			)
		)
		(property "Value" "CONN_1"
			(at 162.56 190.373 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" "MountingHole:MountingHole_4.3mm_M4"
			(at 162.56 191.77 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 162.56 191.77 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 162.56 191.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "P106")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 109.22 85.09 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005a230fb8")
		(property "Reference" "#PWR047"
			(at 109.22 85.09 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 109.22 86.868 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 109.22 85.09 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 109.22 85.09 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 109.22 85.09 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "0db44c3a-4510-42ae-85ff-17add3000409")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR047")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 111.76 111.76 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005a231124")
		(property "Reference" "#PWR048"
			(at 111.76 111.76 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 111.76 113.538 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 111.76 111.76 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 111.76 111.76 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 111.76 111.76 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "6d662ca4-8a2e-436c-867e-2ebb486e7418")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR048")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 109.22 134.62 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005a231320")
		(property "Reference" "#PWR049"
			(at 109.22 134.62 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 109.22 136.398 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 109.22 134.62 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 109.22 134.62 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 109.22 134.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "abbd2a0b-65ca-41de-9490-aadf065dd60c")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR049")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VCC")
		(at 109.22 124.46 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005a23151c")
		(property "Reference" "#PWR050"
			(at 109.22 121.92 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 109.22 121.92 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 109.22 124.46 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 109.22 124.46 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 109.22 124.46 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "1889a737-f0ac-4267-a6ee-b69d4ea7956a")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR050")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VCC")
		(at 111.76 101.6 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005a2319de")
		(property "Reference" "#PWR051"
			(at 111.76 99.06 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 111.76 99.06 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 111.76 101.6 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 111.76 101.6 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 111.76 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "0c4aae92-8212-4fcb-9374-481df5390583")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR051")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VCC")
		(at 109.22 74.93 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005a231b05")
		(property "Reference" "#PWR052"
			(at 109.22 72.39 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 109.22 72.39 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 109.22 74.93 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 109.22 74.93 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 109.22 74.93 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "90e988b1-a1d7-40e2-bfcf-f76a5e7f984f")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR052")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:GND")
		(at 99.06 48.26 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005a234890")
		(property "Reference" "#PWR06"
			(at 99.06 48.26 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 99.06 50.038 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 99.06 48.26 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 99.06 48.26 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 99.06 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "43b99ea6-1d44-4bb7-bdd5-0a1884b6e024")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR06")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:VCC")
		(at 99.06 38.1 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005a234929")
		(property "Reference" "#PWR09"
			(at 99.06 35.56 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 99.06 35.56 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 99.06 38.1 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 99.06 38.1 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 99.06 38.1 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "6ee58cc2-fbea-4572-ad2f-45e593617f1d")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "#PWR09")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "pic_programmer:7805")
		(at 68.58 170.18 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "00000000-0000-0000-0000-00005a238df8")
		(property "Reference" "U3"
			(at 72.39 175.1584 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "7805"
			(at 68.58 165.1 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" "Package_TO_SOT_THT:TO-220-3_Horizontal_TabDown"
			(at 68.58 177.8 0)
			(effects
				(font
					(size 0.381 0.381)
				)
			)
		)
		(property "Datasheet" "www.st.com/resource/en/datasheet/l78.pdf"
			(at 68.58 170.18 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 68.58 170.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "8df2ae42-1a2d-4660-b7a9-f0a141785064")
		)
		(pin "2"
			(uuid "905289ea-3c39-4ced-9d91-2d796770e795")
		)
		(pin "3"
			(uuid "54a14c6b-da3a-4924-b7e2-572f6959fe7c")
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(reference "U3")
					(unit 1)
				)
			)
		)
	)
	(sheet
		(at 233.68 78.74)
		(size 39.37 30.48)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(stroke
			(width 0)
			(type solid)
		)
		(fill
			(color 0 0 0 0.0000)
		)
		(uuid "00000000-0000-0000-0000-00004804a5e2")
		(property "Sheetname" "pic_sockets"
			(at 233.68 77.9775 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left bottom)
			)
		)
		(property "Sheetfile" "pic_sockets.kicad_sch"
			(at 233.68 109.8301 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left top)
			)
		)
		(pin "VPP-MCLR" input
			(at 233.68 88.9 180)
			(uuid "4f5ccd8c-8f94-4906-8b6b-52cfd5ec3797")
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(pin "CLOCK-RB6" input
			(at 233.68 105.41 180)
			(uuid "18190584-a843-4c9e-a97e-f2621f0a8d04")
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(pin "DATA-RB7" input
			(at 233.68 97.79 180)
			(uuid "17d31f7d-0761-4df1-b4b6-3a74a68e0776")
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(pin "VCC_PIC" input
			(at 233.68 81.28 180)
			(uuid "586ecb9b-c34b-43af-a37d-b273f3aea560")
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify left)
			)
		)
		(instances
			(project "pic_programmer"
				(path "/2e45d1d2-c73f-46e5-98d6-3a9dc360bff5"
					(page "2")
				)
			)
		)
	)
	(sheet_instances
		(path "/"
			(page "1")
		)
	)
	(embedded_fonts no)
)
