##########################################################################
# Build KiCad on Windows and save the results
##########################################################################
win64_build:
  stage: build
  tags:
    - kicad-windows-ltsc2022
  interruptible: false
  image: registry.gitlab.com/kicad/kicad-ci/windows-build-image/ltsc2022-msvc:latest
  variables:
    # Switch the compressor to fastzip and reduce the compression level
    FF_USE_FASTZIP: "true"
    CACHE_COMPRESSION_LEVEL: "fast"
  cache:
    key: win64-vcpkg-$CI_COMMIT_REF_SLUG
    paths:
      - build\windows\vcpkg_installed
      - .vcpkgCache
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_PROJECT_ROOT_NAMESPACE == "kicad"
  script:
    - C:\builder\build.ps1 -Env -Arch x64
    - $vcpkgCache=Join-Path -Path (Get-Location) -ChildPath ".vcpkgCache";$env:VCPKG_DEFAULT_BINARY_CACHE=$vcpkgCache;New-Item -ItemType Directory -Force -Path $vcpkgCache
    - mkdir -p build/windows -Force
    - cd build/windows
    - cmake `
      -G "Ninja" `
      -DCMAKE_TOOLCHAIN_FILE=C:\builder\vcpkg\scripts\buildsystems\vcpkg.cmake `
      -DCMAKE_BUILD_TYPE=Debug `
      -DKICAD_SCRIPTING_WXPYTHON=OFF `
      -DKICAD_USE_OCC=ON `
      -DKICAD_BUILD_PNS_DEBUG_TOOL=ON `
      -DKICAD_USE_3DCONNEXION=ON `
      -DVCPKG_BUILD_TYPE=debug `
      ../../
    - cmake --build . 2>&1 | tee compilation_log.txt
    - cd ../../
  artifacts:
    # Only save the artifacts that are needed for running the tests in the next stage
    # and the compilation log. The entire build directory is too large to save as an
    # artifact.
    expire_in: 2 hrs
    when: always
    paths:
      - build/windows/compilation_log.txt
