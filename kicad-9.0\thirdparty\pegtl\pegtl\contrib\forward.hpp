// Copyright (c) 2020-2021 Dr<PERSON> <PERSON> and <PERSON>
// Distributed under the Boost Software License, Version 1.0.
// (See accompanying file LICENSE_1_0.txt or copy at https://www.boost.org/LICENSE_1_0.txt)

#ifndef TAO_PEGTL_CONTRIB_FORWARD_HPP
#define TAO_PEGTL_CONTRIB_FORWARD_HPP

#include "../config.hpp"

namespace TAO_PEGTL_NAMESPACE
{
   template< typename Name, typename Rule, typename = void >
   struct analyze_traits;

}  // namespace TAO_PEGTL_NAMESPACE

#endif
