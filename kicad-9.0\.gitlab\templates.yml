##########################################################################
# This will allow the job to run whenever code events trigger it.
##########################################################################
.only_code:
  only:
    - master
    - branches
    - merge_requests
    - pushes
  except:
    - schedules

##########################################################################
# Define a template for all the unit tests, since each one is run in a separate
# job to make the display nicer.
##########################################################################
.unit_test:
  stage: test
  when: on_success
  variables:
    BOOST_TEST_LOGGER: 'JUNIT,warning,test_results.${TEST}.xml:HRF,message'
    CTEST_OUTPUT_ON_FAILURE: 1
  script:
    - cd build/linux/qa
    - ctest -R qa_${TEST}
  # GitLab supports displaying the results in the GUI through JUNIT artifacts
  # (https://docs.gitlab.com/ee/ci/junit_test_reports.html)
  # so we upload the JUNIT results.
  artifacts:
    when: always
    paths:
      - build/linux/qa/**/*.log
      - build/linux/qa/**/output/*
      - build/linux/qa/**/*.xml
    reports:
      junit:
        - build/linux/qa/**/*.xml