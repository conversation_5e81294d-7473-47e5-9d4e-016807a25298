/*
 * This program source code file is part of KiCad, a free EDA CAD application.
 *
 * Copyright (C) 2016 <PERSON> <mrluze<PERSON>@ua.pt>
 * Copyright The KiCad Developers, see AUTHORS.txt for contributors.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, you may find one here:
 * http://www.gnu.org/licenses/old-licenses/gpl-2.0.html
 * or you may search the http://www.gnu.org website for the version 2 license,
 * or you may write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA
 */

 /**
 * @file buffers_debug.h
 * @brief
 */

#ifndef BUFFER_DEBUG_H
#define BUFFER_DEBUG_H

#include <plugins/3dapi/xv3d_types.h>
#include <wx/string.h>

void DBG_SaveBuffer( const wxString& aFileName, const unsigned char *aInBuffer,
                     unsigned int aXSize, unsigned int aYSize );

void DBG_SaveBuffer( const wxString& aFileName, const float *aInBuffer,
                     unsigned int aXSize, unsigned int aYSize );

void DBG_SaveBuffer( const wxString& aFileName, const SFVEC3F *aInBuffer,
                     unsigned int aXSize, unsigned int aYSize );

void DBG_SaveBuffer( const wxString& aFileName, const SFVEC4F *aInBuffer,
                     unsigned int aXSize, unsigned int aYSize );

void DBG_SaveNormalsBuffer( const wxString& aFileName, const SFVEC3F *aInNormalsBuffer,
                            unsigned int aXSize, unsigned int aYSize );

#endif   // BUFFER_DEBUG_H
