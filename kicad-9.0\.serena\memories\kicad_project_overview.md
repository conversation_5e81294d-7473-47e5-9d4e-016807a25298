# KiCad Project Overview

## Purpose
KiCad is a free EDA CAD application for electronic design automation. It includes:
- **Schematic Editor** (eeschema) - for creating circuit schematics
- **PCB Editor** (pcbnew) - for designing printed circuit boards
- **3D Viewer** - for visualizing PCB designs in 3D
- **Symbol Editor** - for creating schematic symbols
- **Footprint Editor** - for creating PCB footprints
- **Gerber Viewer** (gerbview) - for viewing manufacturing files

## Tech Stack
- **Language**: C++ (primary), Python (scripting)
- **Build System**: CMake with custom build steps
- **UI Framework**: wxWidgets
- **File Formats**: S-expression based (.kicad_sch, .kicad_pcb, .kicad_pro)
- **Graphics**: OpenGL for rendering
- **Documentation**: Doxygen

## Key Architecture
- Modular design with separate applications sharing common libraries
- S-expression file format for all data storage
- Plugin architecture for 3D models and importers
- Python scripting integration
- Custom lexical analyzer system for file parsing