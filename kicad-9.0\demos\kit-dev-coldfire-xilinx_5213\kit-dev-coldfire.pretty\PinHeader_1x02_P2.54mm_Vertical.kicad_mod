(footprint "PinHeader_1x02_P2.54mm_Vertical"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "Through hole straight pin header, 1x02, 2.54mm pitch, single row")
	(tags "Through hole pin header THT 1x02 2.54mm single row")
	(property "Reference" "REF**"
		(at 0 -2.33 0)
		(layer "F.SilkS")
		(uuid "f33b393e-1f67-4a61-b482-0e4661731262")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "JUMPER"
		(at 0 4.87 0)
		(layer "F.Fab")
		(uuid "b69b613d-71f1-4826-bd6f-ebe281d80e22")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "77507970-2f11-4c99-8da4-8907ec22607a")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "8236c505-08ba-4a1e-979e-59977dee43c4")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -1.33 -1.33)
		(end 0 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "fcb5fde4-c0b5-48ac-a8a3-a27daecb50c7")
	)
	(fp_line
		(start -1.33 0)
		(end -1.33 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f54df766-7f73-4fdb-a295-b1a77dba43f3")
	)
	(fp_line
		(start -1.33 1.27)
		(end -1.33 3.87)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "d4f885e1-ec24-4231-8122-0140a71e2126")
	)
	(fp_line
		(start -1.33 1.27)
		(end 1.33 1.27)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "7ff1d2c9-9ad6-432c-a2c6-bcaa77e1e0d5")
	)
	(fp_line
		(start -1.33 3.87)
		(end 1.33 3.87)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "4bb3f35b-71f2-42cf-a683-cc4718667b2a")
	)
	(fp_line
		(start 1.33 1.27)
		(end 1.33 3.87)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "d389f327-b7a4-49a5-af9b-cdc7fb9ed8ed")
	)
	(fp_line
		(start -1.8 -1.8)
		(end -1.8 4.35)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "bcf40067-6277-42f0-8b07-8d8d27f87b29")
	)
	(fp_line
		(start -1.8 4.35)
		(end 1.8 4.35)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "bc07258e-8de0-459c-a7ac-bb8a27228413")
	)
	(fp_line
		(start 1.8 -1.8)
		(end -1.8 -1.8)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "ded08692-4566-4ce5-a19e-e62870bc5cea")
	)
	(fp_line
		(start 1.8 4.35)
		(end 1.8 -1.8)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "afa988d7-a21e-442b-91d2-02a0a977dd9c")
	)
	(fp_line
		(start -1.27 -0.635)
		(end -0.635 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "3b9ca0c9-0306-4c54-9289-5ca601dbd577")
	)
	(fp_line
		(start -1.27 3.81)
		(end -1.27 -0.635)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "4750e6e9-c805-44c6-9a06-4fab6715a02b")
	)
	(fp_line
		(start -0.635 -1.27)
		(end 1.27 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "041b54a6-d687-4175-80e8-da0b7d793a52")
	)
	(fp_line
		(start 1.27 -1.27)
		(end 1.27 3.81)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "ece30127-437e-466b-bcba-7696230981b2")
	)
	(fp_line
		(start 1.27 3.81)
		(end -1.27 3.81)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "94debe54-8870-4843-aab9-40c957d3b29c")
	)
	(fp_text user "${REFERENCE}"
		(at 0 1.27 270)
		(layer "F.Fab")
		(uuid "084a0f1a-3114-418e-b749-a14406292a9f")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "86267160-aaba-4691-9e8b-0a14029cea11")
	)
	(pad "2" thru_hole oval
		(at 0 2.54)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "dcb65869-4bb9-41ea-bf2e-eda8d4b72f17")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x02_P2.54mm_Vertical.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
