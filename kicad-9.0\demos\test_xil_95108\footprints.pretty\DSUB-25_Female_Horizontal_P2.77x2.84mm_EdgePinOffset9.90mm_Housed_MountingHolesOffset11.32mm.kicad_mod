(footprint "DSUB-25_Female_Horizontal_P2.77x2.84mm_EdgePinOffset9.90mm_Housed_MountingHolesOffset11.32mm"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(locked yes)
	(layer "F.Cu")
	(descr "25-pin D-Sub connector, horizontal/angled (90 deg), THT-mount, female, pitch 2.77x2.84mm, pin-PCB-offset 9.9mm, distance of mounting holes 47.1mm, distance of mounting holes to PCB edge 11.32mm, see https://disti-assets.s3.amazonaws.com/tonar/files/datasheets/16730.pdf")
	(tags "25-pin D-Sub connector horizontal angled 90deg THT female pitch 2.77x2.84mm pin-PCB-offset 9.9mm mounting-holes-distance 47.1mm mounting-hole-offset 11.32mm")
	(property "Reference" "REF**"
		(at -16.62 -2.8 0)
		(layer "F.SilkS")
		(uuid "12e10651-0a69-4f8d-9fbf-1902c2e04227")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "DB25"
		(at -4.826 5.715 0)
		(layer "F.Fab")
		(uuid "a38ee036-895d-4e5c-aa89-c5cf9786fbb9")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "7ba57173-5df2-4b70-8b08-592f5e010a67")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "f30d4d40-1202-404a-ba08-1ad4a98871b6")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -43.23 -1.86)
		(end 9.99 -1.86)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "2ce946c1-8d28-4615-9332-accec7b50741")
	)
	(fp_line
		(start -43.23 12.68)
		(end -43.23 -1.86)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "c29ffa17-b48f-4718-94b2-d66c480a4e9b")
	)
	(fp_line
		(start -0.25 -2.754338)
		(end 0.25 -2.754338)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "10cd2155-c9aa-440b-9981-ab3bf868083a")
	)
	(fp_line
		(start 0 -2.321325)
		(end -0.25 -2.754338)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "be1b14ed-03cf-493a-837b-ac4e5cc26bfb")
	)
	(fp_line
		(start 0.25 -2.754338)
		(end 0 -2.321325)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "0a271ea5-76f5-4799-be11-ca5e84390b6a")
	)
	(fp_line
		(start 9.99 -1.86)
		(end 9.99 12.68)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "00f344a6-9294-4537-8d94-d03296c16ec5")
	)
	(fp_line
		(start -43.67 -2.31)
		(end -43.67 19.81)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "172e8d5d-5716-4408-a369-8b871ed0bd68")
	)
	(fp_line
		(start -43.67 19.81)
		(end 10.43 19.81)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "d9339779-1052-463d-b10b-f03138175eb3")
	)
	(fp_line
		(start 10.43 -2.31)
		(end -43.67 -2.31)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "4800a4ad-449a-40b8-89e7-164ab6e56600")
	)
	(fp_line
		(start 10.43 19.81)
		(end 10.43 -2.31)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "7c757007-b7f8-46a8-96e1-7a67f334ac17")
	)
	(fp_line
		(start -43.17 -1.8)
		(end -43.17 12.74)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "384fd9f4-ad26-43fb-aba7-797e23daf153")
	)
	(fp_line
		(start -43.17 12.74)
		(end -43.17 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "a4635a77-947e-4846-9508-4be92391b81c")
	)
	(fp_line
		(start -43.17 12.74)
		(end 9.93 12.74)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "03866407-2a33-4c11-a393-201fd52be630")
	)
	(fp_line
		(start -43.17 13.14)
		(end 9.93 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "5db72bd9-1d20-41d0-8b75-fa190bc1743d")
	)
	(fp_line
		(start -42.67 13.14)
		(end -42.67 18.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "94c379e5-2e39-40d9-84ab-ac3f7001f950")
	)
	(fp_line
		(start -42.67 18.14)
		(end -37.67 18.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "7b3a9aeb-07a5-4704-aebd-4ac861b92abf")
	)
	(fp_line
		(start -41.77 12.74)
		(end -41.77 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "f905f7b7-9f33-4e2c-b7e5-76f2eee3c655")
	)
	(fp_line
		(start -38.57 12.74)
		(end -38.57 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "bd6cda3f-**************-c859a3182e49")
	)
	(fp_line
		(start -37.67 13.14)
		(end -42.67 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "bf4beca7-e7e4-44c7-b5d7-adfeb1cafe57")
	)
	(fp_line
		(start -37.67 18.14)
		(end -37.67 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "1c373b44-25a0-4aff-8508-e987ca07f79e")
	)
	(fp_line
		(start -35.77 13.14)
		(end -35.77 19.31)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "b0df89e5-80ee-4586-84b1-bf00b9352a7c")
	)
	(fp_line
		(start -35.77 19.31)
		(end 2.53 19.31)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "26ecffbc-b78b-4f4d-8c11-19d39d05bdc5")
	)
	(fp_line
		(start 2.53 13.14)
		(end -35.77 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "c69c21c5-10d4-46a9-99bf-2a8a9295e436")
	)
	(fp_line
		(start 2.53 19.31)
		(end 2.53 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "3d4227ef-9812-49ec-bae9-c75178fc7878")
	)
	(fp_line
		(start 4.43 13.14)
		(end 4.43 18.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "d8b8f3c8-4f2e-4b9b-aa86-3e4043290f97")
	)
	(fp_line
		(start 4.43 18.14)
		(end 9.43 18.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "48a92551-def5-448f-a4b5-1d5d3fc6d751")
	)
	(fp_line
		(start 5.33 12.74)
		(end 5.33 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "6be79c25-215a-47e5-9123-87b6dcf9304d")
	)
	(fp_line
		(start 8.53 12.74)
		(end 8.53 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "d2f0e130-a490-48f3-b762-847c6dc1a570")
	)
	(fp_line
		(start 9.43 13.14)
		(end 4.43 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "3bd9118a-4a88-46d8-aae7-b3e6846918c3")
	)
	(fp_line
		(start 9.43 18.14)
		(end 9.43 13.14)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "b0cf6641-e6e7-4eb7-9852-d78ce36f168d")
	)
	(fp_line
		(start 9.93 -1.8)
		(end -43.17 -1.8)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "bd9d07f5-f33f-41fa-8c24-837f905c05ec")
	)
	(fp_line
		(start 9.93 12.74)
		(end -43.17 12.74)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "f3d5a1c7-d3c5-408d-9d94-7d888caf69e7")
	)
	(fp_line
		(start 9.93 12.74)
		(end 9.93 -1.8)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "0ac5e246-6249-4a89-ad04-346ba5880791")
	)
	(fp_line
		(start 9.93 13.14)
		(end 9.93 12.74)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "5a815410-19be-4128-8c90-9530c4ffcb9e")
	)
	(fp_arc
		(start -41.77 1.42)
		(mid -40.17 -0.18)
		(end -38.57 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "431c7318-f6cd-481b-b2ad-4e16338592a8")
	)
	(fp_arc
		(start 5.33 1.42)
		(mid 6.93 -0.18)
		(end 8.53 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "de69a4dd-c070-487d-8d4f-440afb6fc69d")
	)
	(fp_text user "${REFERENCE}"
		(at -13.97 5.715 0)
		(layer "F.Fab")
		(uuid "b5abe938-8544-4d46-93dd-cdd6013f7a65")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "0" thru_hole circle
		(at -40.17 1.42)
		(size 4 4)
		(drill 3.2)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "60425bb2-ec79-44e4-a065-4314f5429a3b")
	)
	(pad "0" thru_hole circle
		(at 6.93 1.42)
		(size 4 4)
		(drill 3.2)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "322ea4bb-e41a-4927-92e8-b156b04323ff")
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "2ba6e54d-6722-4aaa-920f-8d179ad285b1")
	)
	(pad "2" thru_hole circle
		(at -2.77 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "fe375780-620b-4954-ad6c-b0200ed1f0a3")
	)
	(pad "3" thru_hole circle
		(at -5.54 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "819630a6-aeb1-4316-9e98-3fbcecebfb13")
	)
	(pad "4" thru_hole circle
		(at -8.31 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "ed69f61e-848f-42ed-a023-8c662450c9aa")
	)
	(pad "5" thru_hole circle
		(at -11.08 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "dcc324c3-5571-403e-93d8-36791046df66")
	)
	(pad "6" thru_hole circle
		(at -13.85 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "595b902f-f13c-4e4b-8deb-72d2afa2e353")
	)
	(pad "7" thru_hole circle
		(at -16.62 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0538338b-3f54-43c7-9599-f47f69125da5")
	)
	(pad "8" thru_hole circle
		(at -19.39 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "e0d0136f-2adb-4ef8-a54b-60fae04a413f")
	)
	(pad "9" thru_hole circle
		(at -22.16 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c311c508-d45d-4ee0-89d5-d1f59cd5bedb")
	)
	(pad "10" thru_hole circle
		(at -24.93 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "ec0a6abd-0919-4975-85aa-706c064abb3c")
	)
	(pad "11" thru_hole circle
		(at -27.7 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "4e73ea1c-f18e-4375-8e44-65240355fba3")
	)
	(pad "12" thru_hole circle
		(at -30.47 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7b947a76-4d24-4b88-b1f5-8810551a65a8")
	)
	(pad "13" thru_hole circle
		(at -33.24 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "91c48f04-8b4a-4907-8ce1-b6a82eb2db27")
	)
	(pad "14" thru_hole circle
		(at -1.385 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0c966fd0-4fc2-4f00-baf0-da2325176535")
	)
	(pad "15" thru_hole circle
		(at -4.155 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "1886dad6-6bbf-43d4-9643-ae66951bba8e")
	)
	(pad "16" thru_hole circle
		(at -6.925 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "62018bee-e593-4665-b79e-a028882e3536")
	)
	(pad "17" thru_hole circle
		(at -9.695 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "11103ef1-25e0-4c4b-8719-221ae082b5a8")
	)
	(pad "18" thru_hole circle
		(at -12.465 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "e3e05bdb-5c8e-4108-9e90-f50505f32e18")
	)
	(pad "19" thru_hole circle
		(at -15.235 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "24e96643-61b6-49bf-b92e-4fd346881fbd")
	)
	(pad "20" thru_hole circle
		(at -18.005 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "716cdb41-8e6d-4dbb-bff1-f3adddd7352a")
	)
	(pad "21" thru_hole circle
		(at -20.775 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "fb52c741-7a7c-4fa4-85d3-6ff744b5effd")
	)
	(pad "22" thru_hole circle
		(at -23.545 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "211da64b-5205-4267-aa57-ab8d7ebdc169")
	)
	(pad "23" thru_hole circle
		(at -26.315 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "fbc6ab4a-98d2-4d06-9270-4d66a55da1a2")
	)
	(pad "24" thru_hole circle
		(at -29.085 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "12d4f7d1-974d-4bb9-9997-a8037c978638")
	)
	(pad "25" thru_hole circle
		(at -31.855 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "1a202eea-aca7-4261-81ad-d22c30aeddfe")
	)
	(embedded_fonts no)
	(model "${KICAD8_3DMODEL_DIR}/Connector_Dsub.3dshapes/DSUB-25_Female_Horizontal_P2.77x2.84mm_EdgePinOffset9.90mm_Housed_MountingHolesOffset11.32mm.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
