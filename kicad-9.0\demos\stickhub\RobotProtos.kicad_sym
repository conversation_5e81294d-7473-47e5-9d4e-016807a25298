(kicad_symbol_lib
	(version 20241209)
	(generator "kicad_symbol_editor")
	(generator_version "9.0")
	(symbol "+1V8"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+1V8"
			(at 0 3.556 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"+1V8\""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "global power"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "+1V8_0_1"
			(polyline
				(pts
					(xy -0.762 1.27) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 2.54) (xy 0.762 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "+1V8_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "+1V8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "+3.3V"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+3.3V"
			(at 0 3.556 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"+3.3V\""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "global power"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "+3.3V_0_1"
			(polyline
				(pts
					(xy -0.762 1.27) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 2.54) (xy 0.762 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "+3.3V_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "+3.3V"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "+5V"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+5V"
			(at 0 3.556 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"+5V\""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "global power"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "+5V_0_1"
			(polyline
				(pts
					(xy -0.762 1.27) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 2.54) (xy 0.762 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "+5V_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "+5V"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CPsmall"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "C"
			(at 2.667 0 90)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Value" "CPsmall"
			(at 3.937 0 90)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" "SH_Capacitors:CP_Elec_6.3x7.7"
			(at 1.905 0 90)
			(effects
				(font
					(size 0.2032 0.2032)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "CP* SM*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "CPsmall_0_1"
			(rectangle
				(start -1.016 0.508)
				(end 1.016 -0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy 1.524 0.762) (xy 1.524 -1.016) (xy -1.524 -1.016) (xy -1.524 0.762)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "CPsmall_1_1"
			(pin passive line
				(at 0 2.54 270)
				(length 2.032)
				(name "+"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 0 -2.54 90)
				(length 1.524)
				(name "-"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Conn_01x01"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at 0 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x01"
			(at 0 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Generic connector, single row, 01x01, script generated (kicad-library-utils/schlib/autogen/connector/)"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "connector"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Connector*:*_1x??_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Conn_01x01_1_1"
			(rectangle
				(start -1.27 1.27)
				(end 1.27 -1.27)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(rectangle
				(start -1.27 0.127)
				(end 0 -0.127)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at -5.08 0 0)
				(length 3.81)
				(name "Pin_1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Conn_01x04_Male"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x04_Male"
			(at 0 -7.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Generic connector, single row, 01x04, script generated (kicad-library-utils/schlib/autogen/connector/)"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "connector"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Connector*:*_1x??_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Conn_01x04_Male_1_1"
			(rectangle
				(start 0.8636 2.667)
				(end 0 2.413)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(rectangle
				(start 0.8636 0.127)
				(end 0 -0.127)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(rectangle
				(start 0.8636 -2.413)
				(end 0 -2.667)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(rectangle
				(start 0.8636 -4.953)
				(end 0 -5.207)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy 1.27 2.54) (xy 0.8636 2.54)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.27 0) (xy 0.8636 0)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.27 -2.54) (xy 0.8636 -2.54)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.27 -5.08) (xy 0.8636 -5.08)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at 5.08 2.54 180)
				(length 3.81)
				(name "Pin_1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 5.08 0 180)
				(length 3.81)
				(name "Pin_2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 5.08 -2.54 180)
				(length 3.81)
				(name "Pin_3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 5.08 -5.08 180)
				(length 3.81)
				(name "Pin_4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Conn_01x04_MountingPin"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x04_MountingPin"
			(at 1.27 -7.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Generic connectable mounting pin connector, single row, 01x04, script generated (kicad-library-utils/schlib/autogen/connector/)"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "connector"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Connector*:*_1x??-1MP*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Conn_01x04_MountingPin_1_1"
			(rectangle
				(start -1.27 3.81)
				(end 1.27 -6.35)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(rectangle
				(start -1.27 2.667)
				(end 0 2.413)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 0.127)
				(end 0 -0.127)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -2.413)
				(end 0 -2.667)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 -4.953)
				(end 0 -5.207)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.016 -7.112) (xy 1.016 -7.112)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(text "Mounting"
				(at 0 -6.731 0)
				(effects
					(font
						(size 0.381 0.381)
					)
				)
			)
			(pin passive line
				(at -5.08 2.54 0)
				(length 3.81)
				(name "Pin_1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 0 0)
				(length 3.81)
				(name "Pin_2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -2.54 0)
				(length 3.81)
				(name "Pin_3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -5.08 0)
				(length 3.81)
				(name "Pin_4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 0 -10.16 90)
				(length 3.048)
				(name "MountPin"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "MP"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Crystal_small_4P"
		(pin_names
			(offset 0.0254)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "Y"
			(at 0 4.445 0)
			(effects
				(font
					(size 0.9906 0.9906)
				)
			)
		)
		(property "Value" "Crystal_small_4P"
			(at 0 3.175 0)
			(effects
				(font
					(size 0.6096 0.6096)
				)
			)
		)
		(property "Footprint" "Crystal:Crystal_4-SMD_2.5x2mm"
			(at 0 -3.302 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 0.9906 0.9906)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Crystal_small_4P_0_1"
			(polyline
				(pts
					(xy -1.27 1.27) (xy -1.27 -1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 -2.54) (xy 1.27 -2.54)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -0.635 1.905)
				(end 0.635 -1.905)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(polyline
				(pts
					(xy 1.27 1.27) (xy 1.27 -1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "Crystal_small_4P_1_1"
			(pin passive line
				(at -2.54 0 0)
				(length 1.27)
				(name "~"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
			(pin passive line
				(at -2.54 -2.54 0)
				(length 1.27)
				(name "GND"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 0 180)
				(length 1.27)
				(name "~"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 -2.54 180)
				(length 1.27)
				(name "GND"
					(effects
						(font
							(size 0.6096 0.6096)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 0.6096 0.6096)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Csmall"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.254)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "C"
			(at 0.508 1.27 0)
			(effects
				(font
					(size 0.9906 0.9906)
				)
				(justify left)
			)
		)
		(property "Value" "Csmall"
			(at 0.508 -1.27 0)
			(effects
				(font
					(size 0.7874 0.7874)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:1608_C"
			(at 0.762 -3.556 0)
			(effects
				(font
					(size 0.254 0.254)
					(bold yes)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0.508 1.27 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=+ 2=-"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "C"
			(at 3.302 5.08 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Sim.Params" "c=\"\""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Voltage" "16V"
			(at 0.762 -2.032 0)
			(effects
				(font
					(size 0.254 0.254)
					(bold yes)
				)
				(justify left)
			)
		)
		(property "Tolerance" "10%"
			(at 2.286 -2.032 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Package" "1608"
			(at 0.762 -2.54 0)
			(effects
				(font
					(size 0.2032 0.2032)
					(bold yes)
				)
				(justify left)
			)
		)
		(property "Footprint_Density" "C"
			(at 3.556 -2.54 0)
			(effects
				(font
					(size 0.254 0.254)
					(bold yes)
				)
			)
		)
		(property "Dielectric" "X7R"
			(at 3.302 -2.032 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "ki_fp_filters" "SM* C? C1-1"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Csmall_0_1"
			(rectangle
				(start -1.397 -0.254)
				(end 1.397 -0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(rectangle
				(start 1.397 0.508)
				(end -1.397 0.254)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "Csmall_1_1"
			(pin passive line
				(at 0 2.54 270)
				(length 2.286)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 0 -2.54 90)
				(length 2.286)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "D_TVS_Filled"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "D"
			(at 0 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "D_TVS_Filled"
			(at 0 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Bidirectional transient-voltage-suppression diode, filled shape"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "diode TVS thyrector"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "TO-???* *_Diode_* *SingleDiode* D_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "D_TVS_Filled_0_1"
			(polyline
				(pts
					(xy -2.54 -1.27) (xy 0 0) (xy -2.54 1.27) (xy -2.54 -1.27)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy 0.508 1.27) (xy 0 1.27) (xy 0 -1.27) (xy -0.508 -1.27)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.27 0) (xy -1.27 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 2.54 1.27) (xy 2.54 -1.27) (xy 0 0) (xy 2.54 1.27)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "D_TVS_Filled_1_1"
			(pin passive line
				(at -3.81 0 0)
				(length 2.54)
				(name "A1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 3.81 0 180)
				(length 2.54)
				(name "A2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Dialog_SLG5NT1487V"
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Dialog_SLG5NT1487V"
			(at 0 8.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Package_DFN_QFN:TDFN-8_1.5x2mm_Fused-Lead_JEDEC_MO-252_W2015D"
			(at 0 -10.668 0)
			(effects
				(font
					(size 0.25 0.25)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Dialog_SLG5NT1487V_0_1"
			(rectangle
				(start -5.08 7.62)
				(end 5.08 -10.16)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type background)
				)
			)
		)
		(symbol "Dialog_SLG5NT1487V_1_1"
			(pin power_in line
				(at -7.62 5.08 0)
				(length 2.54)
				(name "D"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -7.62 -2.54 0)
				(length 2.54)
				(name "VDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -7.62 -7.62 0)
				(length 2.54)
				(name "ON"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_out line
				(at 7.62 5.08 180)
				(length 2.54)
				(name "S"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 7.62 -2.54 180)
				(length 2.54)
				(name "CAP"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 -7.62 180)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "GND"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -6.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "global power"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "GND_0_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "GND_1_1"
			(pin power_in line
				(at 0 0 270)
				(length 0)
				(hide yes)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "JUMPER"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.762)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "JP"
			(at -1.27 -1.27 0)
			(effects
				(font
					(size 0.7874 0.7874)
				)
				(justify right)
			)
		)
		(property "Value" "JUMPER"
			(at -0.635 -1.27 0)
			(effects
				(font
					(size 0.7874 0.7874)
				)
				(justify left)
			)
		)
		(property "Footprint" "Connector_PinHeader_2.54mm:PinHeader_1x02_P2.54mm_Vertical"
			(at 0 1.524 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "JUMPER_0_1"
			(circle
				(center -1.27 0)
				(radius 0.381)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -0.889 0.508)
				(mid 0 1.0239)
				(end 0.889 0.508)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 0)
				(radius 0.381)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at -2.54 0 0)
				(length 0.8128)
				(name "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.9906 0.9906)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 0 180)
				(length 0.8128)
				(name "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 0.9906 0.9906)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "LED_DUAL_red_green"
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "D"
			(at 0 6.096 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "LED_DUAL_red_green"
			(at 0 -6.477 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "LED_SMD:Duo_LED_1.6x0.8_Kingbright_APHB1608LZGKSURKC"
			(at 0 -5.461 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "LED-* LED_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "LED_DUAL_red_green_0_0"
			(polyline
				(pts
					(xy 2.54 0) (xy -2.54 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(text "red"
				(at 0 0.635 0)
				(effects
					(font
						(size 0.6096 0.6096)
					)
				)
			)
			(text "green"
				(at 0 -0.508 0)
				(effects
					(font
						(size 0.6096 0.6096)
					)
				)
			)
		)
		(symbol "LED_DUAL_red_green_0_1"
			(rectangle
				(start -2.54 5.08)
				(end 2.54 -5.08)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.54 2.54) (xy -1.27 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 1.27) (xy -1.27 3.81)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 -1.27) (xy -1.27 -3.81)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 -2.54) (xy -2.54 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -0.508 4.064) (xy -0.508 3.81)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 4.318) (xy 0 4.064)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0.254 3.556) (xy -0.508 4.318) (xy -0.508 4.064)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0.254 -3.556) (xy -0.508 -4.318) (xy -0.508 -3.81)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0.762 3.81) (xy 0 4.572) (xy 0 4.318)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0.762 -3.81) (xy 0 -4.572) (xy 0 -4.064)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.27 1.27) (xy -1.27 2.54) (xy 1.27 3.81)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy 1.27 -1.27) (xy -1.27 -2.54) (xy 1.27 -3.81)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy 2.54 2.54) (xy 1.27 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 2.54 -2.54) (xy 1.27 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "LED_DUAL_red_green_1_1"
			(pin passive line
				(at -5.08 2.54 0)
				(length 2.54)
				(name "4"
					(effects
						(font
							(size 0.7874 0.7874)
						)
					)
				)
				(number "RK"
					(effects
						(font
							(size 0.7874 0.7874)
						)
					)
				)
			)
			(pin passive line
				(at -5.08 -2.54 0)
				(length 2.54)
				(name "2"
					(effects
						(font
							(size 0.7874 0.7874)
						)
					)
				)
				(number "GK"
					(effects
						(font
							(size 0.7874 0.7874)
						)
					)
				)
			)
			(pin passive line
				(at 5.08 2.54 180)
				(length 2.54)
				(name "3"
					(effects
						(font
							(size 0.7874 0.7874)
						)
					)
				)
				(number "RA"
					(effects
						(font
							(size 0.7874 0.7874)
						)
					)
				)
			)
			(pin passive line
				(at 5.08 -2.54 180)
				(length 2.54)
				(name "1"
					(effects
						(font
							(size 0.7874 0.7874)
						)
					)
				)
				(number "GA"
					(effects
						(font
							(size 0.7874 0.7874)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Logo"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "LOGO"
			(at 0 -2.794 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Value" "Logo"
			(at 1.778 0.762 0)
			(effects
				(font
					(size 0.9906 0.9906)
				)
				(justify left)
			)
		)
		(property "Footprint" "Logo"
			(at 1.778 -0.762 0)
			(effects
				(font
					(size 0.9906 0.9906)
				)
				(justify left)
			)
		)
		(property "Datasheet" ""
			(at 0 -11.43 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Logo_0_1"
			(arc
				(start -1.016 0)
				(mid -0.413 -0.3693)
				(end -0.127 -1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 1.27)
				(end 1.27 -1.27)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -0.127 -1.016)
				(mid 0.1391 0.1718)
				(end 1.016 1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Logo_2"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "LOGO"
			(at 0 -2.794 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Value" "Logo"
			(at 1.778 0.762 0)
			(effects
				(font
					(size 0.9906 0.9906)
				)
				(justify left)
			)
		)
		(property "Footprint" "Logo"
			(at 1.778 -0.762 0)
			(effects
				(font
					(size 0.9906 0.9906)
				)
				(justify left)
			)
		)
		(property "Datasheet" ""
			(at 0 -11.43 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Logo_2_0_1"
			(arc
				(start -1.016 0)
				(mid -0.413 -0.3693)
				(end -0.127 -1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 1.27)
				(end 1.27 -1.27)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -0.127 -1.016)
				(mid 0.1391 0.1718)
				(end 1.016 1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "Logo_3"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "LOGO"
			(at 0 -2.794 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Value" "Logo"
			(at 1.778 0.762 0)
			(effects
				(font
					(size 0.9906 0.9906)
				)
				(justify left)
			)
		)
		(property "Footprint" "Logo"
			(at 1.778 -0.762 0)
			(effects
				(font
					(size 0.9906 0.9906)
				)
				(justify left)
			)
		)
		(property "Datasheet" ""
			(at 0 -11.43 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "Logo_3_0_1"
			(arc
				(start -1.016 0)
				(mid -0.413 -0.3693)
				(end -0.127 -1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 1.27)
				(end 1.27 -1.27)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -0.127 -1.016)
				(mid 0.1391 0.1718)
				(end 1.016 1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "MaxLinear_XR22417-48"
		(pin_names
			(offset 1.016)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 0 59.69 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "MaxLinear_XR22417-48"
			(at 0 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Package_QFP:LQFP-48_7x7mm_P0.5mm"
			(at 0 -70.612 0)
			(effects
				(font
					(size 0.25 0.25)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "7-Port USB 2.0 hub IC"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "MaxLinear_XR22417-48_0_1"
			(rectangle
				(start -12.7 63.5)
				(end 12.7 -71.12)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
		)
		(symbol "MaxLinear_XR22417-48_1_1"
			(pin bidirectional line
				(at -15.24 60.96 0)
				(length 2.54)
				(name "UP_D-"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "29"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -15.24 58.42 0)
				(length 2.54)
				(name "UP_D+"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "30"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -15.24 48.26 0)
				(length 2.54)
				(name "VCC5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -15.24 38.1 0)
				(length 2.54)
				(name "VBUS_SENSE"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "41"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_out line
				(at -15.24 22.86 0)
				(length 2.54)
				(name "3V3_OUT"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -15.24 20.32 0)
				(length 2.54)
				(name "VCC33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -15.24 17.78 0)
				(length 2.54)
				(name "VCC33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "20"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -15.24 15.24 0)
				(length 2.54)
				(name "VCC33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "26"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -15.24 12.7 0)
				(length 2.54)
				(name "VCC33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "31"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -15.24 10.16 0)
				(length 2.54)
				(name "VCC33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "36"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -15.24 7.62 0)
				(length 2.54)
				(name "VCC33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "42"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -15.24 2.54 0)
				(length 2.54)
				(name "TEST1#"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "43"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -15.24 0 0)
				(length 2.54)
				(name "TEST3#"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "45"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -15.24 -2.54 0)
				(length 2.54)
				(name "EXT_RST#"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "40"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_out line
				(at -15.24 -20.32 0)
				(length 2.54)
				(name "1V8_OUT"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "25"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -15.24 -22.86 0)
				(length 2.54)
				(name "VCC18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -15.24 -25.4 0)
				(length 2.54)
				(name "VCC18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "21"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -15.24 -27.94 0)
				(length 2.54)
				(name "VCC18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "28"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -15.24 -33.02 0)
				(length 2.54)
				(name "XTALIN"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "22"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at -15.24 -48.26 0)
				(length 2.54)
				(name "XTALOUT"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "23"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -15.24 -53.34 0)
				(length 2.54)
				(name "REXT"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "27"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at -15.24 -60.96 0)
				(length 2.54)
				(name "TEST#2/DNC"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "44"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -63.5 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -66.04 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "24"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -68.58 0)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "39"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 60.96 180)
				(length 2.54)
				(name "USB1D-"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 58.42 180)
				(length 2.54)
				(name "USB1D+"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "19"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 48.26 180)
				(length 2.54)
				(name "USB2D-"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 45.72 180)
				(length 2.54)
				(name "USB2D+"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 35.56 180)
				(length 2.54)
				(name "USB3D-"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 33.02 180)
				(length 2.54)
				(name "USB3D+"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 22.86 180)
				(length 2.54)
				(name "USB4D-"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 20.32 180)
				(length 2.54)
				(name "USB4D+"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 10.16 180)
				(length 2.54)
				(name "USB5D-"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "32"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 7.62 180)
				(length 2.54)
				(name "USB5D+"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "33"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 -2.54 180)
				(length 2.54)
				(name "USB6D-"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "34"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 -5.08 180)
				(length 2.54)
				(name "USB6D+"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "35"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 -15.24 180)
				(length 2.54)
				(name "USB7D-"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "37"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 -17.78 180)
				(length 2.54)
				(name "USB7D+"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "38"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 -27.94 180)
				(length 2.54)
				(name "TEST#/SDA"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 15.24 -33.02 180)
				(length 2.54)
				(name "LED1/SCL"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 15.24 -38.1 180)
				(length 2.54)
				(name "LED2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 15.24 -43.18 180)
				(length 2.54)
				(name "LED3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 15.24 -48.26 180)
				(length 2.54)
				(name "LED4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 15.24 -53.34 180)
				(length 2.54)
				(name "LED5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "48"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 15.24 -58.42 180)
				(length 2.54)
				(name "LED6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "47"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 15.24 -63.5 180)
				(length 2.54)
				(name "LED7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "46"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 15.24 -68.58 180)
				(length 2.54)
				(name "LED_CTL"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "MountingHole"
		(pin_names
			(offset 1.016)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "H"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "MountingHole"
			(at 0 3.175 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Mounting Hole without connection"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "mounting hole"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "MountingHole*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "MountingHole_0_1"
			(circle
				(center 0 0)
				(radius 1.27)
				(stroke
					(width 1.27)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "PWR_FLAG"
		(power)
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#FLG"
			(at 0 1.905 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "PWR_FLAG"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Special symbol for telling ERC where power comes from"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_keywords" "flag power"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "PWR_FLAG_0_0"
			(pin power_out line
				(at 0 0 90)
				(length 0)
				(name "pwr"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "PWR_FLAG_0_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 1.27) (xy -1.016 1.905) (xy 0 2.54) (xy 1.016 1.905) (xy 0 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "RSMALL"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "R"
			(at -1.524 -0.508 90)
			(effects
				(font
					(size 0.9906 0.9906)
				)
				(justify right)
			)
		)
		(property "Value" "RSMALL"
			(at -1.524 0.254 90)
			(effects
				(font
					(size 0.9906 0.9906)
				)
				(justify left)
			)
		)
		(property "Footprint" "Resistor_SMD:1608_C"
			(at -1.778 0 90)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 1.524 0 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "USD_per_1000" "0,024"
			(at 4.064 2.54 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "USD_per_1" "0,08"
			(at 6.604 5.08 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "R? SM0603 SM0805 R?-* SM1206"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "RSMALL_0_1"
			(polyline
				(pts
					(xy -0.508 1.524) (xy 0 1.778) (xy 0 2.032)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -0.508 -1.524) (xy 0 -1.778) (xy 0 -2.032)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -0.508 -1.524) (xy 0.508 -1.016) (xy -0.508 -0.508) (xy 0.508 0) (xy -0.508 0.508) (xy 0.508 1.016)
					(xy -0.508 1.524)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "RSMALL_1_1"
			(pin passive line
				(at 0 2.54 270)
				(length 0.508)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 0 -2.54 90)
				(length 0.508)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "VIN"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VIN"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "VIN_0_1"
			(circle
				(center 0 1.905)
				(radius 0.635)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 1.27)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "VIN_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "VIN"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
)
