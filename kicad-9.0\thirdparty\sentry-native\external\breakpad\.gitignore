# Copyright 2014 Google LLC
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are
# met:
#
#     * Redistributions of source code must retain the above copyright
# notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above
# copyright notice, this list of conditions and the following disclaimer
# in the documentation and/or other materials provided with the
# distribution.
#     * Neither the name of Google LLC nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
# "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
# LIMITED TO, THE IMPLIED WARRANTIES OF <PERSON><PERSON><PERSON><PERSON>ABILITY AND FITNESS FOR
# A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
# OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
# SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIA<PERSON> DAMAGES (INCLUDING, BUT NOT
# LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
# DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
# THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

# Ignore other VCSs.
.repo/
.svn/

# Ignore common compiled artifacts.
*~
*.dwo
*.o
lib*.a
/breakpad.pc
/breakpad-client.pc
/src/client/linux/linux_client_unittest_shlib
/src/client/linux/linux_dumper_unittest_helper
/src/common/linux/google_crashdump_uploader_test
/src/processor/microdump_stackwalk
/src/processor/minidump_dump
/src/processor/minidump_stackwalk
/src/tools/linux/core2md/core2md
/src/tools/linux/core_handler/core_handler
/src/tools/linux/dump_syms/dump_syms
/src/tools/linux/md2core/minidump-2-core
/src/tools/linux/pid2md/pid2md
/src/tools/linux/symupload/minidump_upload
/src/tools/linux/symupload/sym_upload
/src/tools/mac/dump_syms/dump_syms
/src/tools/mac/dump_syms/dump_syms_mac

# Ignore unit test artifacts.
*_unittest
*.log
*.trs

# Ignore autotools generated artifacts.
.deps
.dirstamp
autom4te.cache/
/config.cache
config.h
/config.log
/config.status
/Makefile
stamp-h1

# Ignore generated Visual Studio artifacts.
*.filters
*.sdf
*.sln
*.suo
*.vcproj
*.vcxproj

# Ignore generated Makefiles
src/Makefile
*.Makefile
*.target.mk

# Ignore compiled Python files.
*.pyc

# Ignore directories gclient syncs.
src/testing
src/third_party/lss
src/third_party/protobuf
