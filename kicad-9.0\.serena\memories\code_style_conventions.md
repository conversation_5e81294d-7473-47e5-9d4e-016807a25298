# KiCad Code Style and Conventions

## General Conventions
- **Language**: Modern C++ (C++14/17 features used)
- **Platform**: Cross-platform (Windows, Linux, macOS)
- **Encoding**: UTF-8 for all source files
- **License**: GPL headers in all source files

## Naming Conventions
- **Classes**: PascalCase (e.g., `DSNLEXER`, `PCB_LEXER`)
- **Functions**: PascalCase (e.g., `NextTok()`, `ParseDouble()`)
- **Variables**: camelCase with prefixes (e.g., `m_memberVar`, `aParameter`)
- **Constants**: ALL_CAPS (e.g., `DSN_EOF`, `T_SYMBOL`)
- **Enums**: ALL_CAPS with T_ prefix for tokens (e.g., `T_at`, `T_layer`)

## Code Organization
- **Headers**: Use include guards or #pragma once
- **Includes**: System includes first, then local includes
- **Namespaces**: Used for token enums to avoid collisions
- **Documentation**: Doxygen-style comments for public APIs

## S-Expression Specific Conventions
- **Keywords**: Lowercase with underscores (e.g., `pcb_layer`, `via_size`)
- **Token Enums**: T_ prefix (e.g., `T_pcb_layer` for "pcb_layer" keyword)
- **Lexer Classes**: UPPERCASE_LEXER pattern (e.g., `PCB_LEXER`)
- **Parser Classes**: UPPERCASE_PARSER pattern (e.g., `PCB_PARSER`)

## File Structure
- **Keywords**: Plain text files with .keywords extension
- **Generated**: Auto-generated files marked with warning comments
- **Parsers**: Typically inherit from corresponding lexer class

## Error Handling
- Extensive use of exceptions for parsing errors
- Line number and position tracking in error messages
- Descriptive error messages with context

## Build Integration
- CMake generates lexer code during build process  
- Dependencies automatically tracked for regeneration
- Export macros for cross-platform symbol visibility