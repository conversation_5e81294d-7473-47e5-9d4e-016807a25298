/*
 * Copyright (c) 2000-2007 Apple Inc. All rights reserved.
 */
#ifndef _BSD_ARM__TYPES_H_
#define _BSD_ARM__TYPES_H_

#if defined (__arm__) || defined (__arm64__) || defined (__aarch64__)


typedef long                    __darwin_intptr_t;
typedef unsigned int            __darwin_natural_t;


#endif /* defined (__arm__) || defined (__arm64__) || defined (__aarch64__) */

#endif  /* _BSD_ARM__TYPES_H_ */