(kicad_symbol_lib (version 20220914) (generator kicad_symbol_editor)
  (symbol "Antenna" (pin_numbers hide) (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
    (property "Reference" "AE" (at -1.905 1.905 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "Antenna" (at -1.905 0 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "Antenna_0_1"
      (polyline
        (pts
          (xy 0 2.54)
          (xy 0 -3.81)
        )
        (stroke (width 0.254) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 1.27 2.54)
          (xy 0 -2.54)
          (xy -1.27 2.54)
        )
        (stroke (width 0.254) (type default))
        (fill (type none))
      )
    )
    (symbol "Antenna_1_1"
      (pin input line (at 0 -5.08 90) (length 2.54)
        (name "A" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
    (property "Reference" "#PWR" (at 0 -6.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 0 -3.81 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "GND_0_1"
      (polyline
        (pts
          (xy 0 0)
          (xy 0 -1.27)
          (xy 1.27 -1.27)
          (xy 0 -2.54)
          (xy -1.27 -1.27)
          (xy 0 -1.27)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
    )
    (symbol "GND_1_1"
      (pin power_in line (at 0 0 270) (length 0) hide
        (name "GND" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "Jumper-device" (pin_names (offset 0.762) hide) (in_bom yes) (on_board yes)
    (property "Reference" "JP" (at 0 3.81 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "Jumper-device" (at 0 -2.032 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "Jumper-device_0_1"
      (circle (center -2.54 0) (radius 0.889)
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (arc (start 2.5146 1.27) (mid 0.0078 2.5097) (end -2.4892 1.27)
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (circle (center 2.54 0) (radius 0.889)
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (pin passive line (at -7.62 0 0) (length 4.191)
        (name "1" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 7.62 0 180) (length 4.191)
        (name "2" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
    (property "Reference" "R" (at 2.032 0 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "R" (at 0 0 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at -1.778 0 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "ki_fp_filters" "R_* R_*" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "R_0_1"
      (rectangle (start -1.016 -2.54) (end 1.016 2.54)
        (stroke (width 0.254) (type default))
        (fill (type none))
      )
    )
    (symbol "R_1_1"
      (pin passive line (at 0 3.81 270) (length 1.27)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -3.81 90) (length 1.27)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
)
