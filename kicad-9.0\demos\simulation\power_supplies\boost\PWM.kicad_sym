(kicad_symbol_lib (version 20230620) (generator kicad_symbol_editor)
  (symbol "PWM" (pin_names (offset 1.016) hide) (exclude_from_sim no) (in_bom yes) (on_board yes)
    (property "Reference" "A1" (at -3.81 1.7146 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "PWM" (at -3.81 -0.8254 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "https://ngspice.sourceforge.io/docs.html" (at 0 16.51 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Description" "Voltage-dependent Voltage source symbol for simulation only" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Library" "pwm2_model.lib" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Name" "pwm" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "SUBCKT" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Pins" "1=out+ 2=out- 3=in+ 4=in-" (at -27.94 -6.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Params" "freq=100k vlo=0.7 vhi=3.5" (at -16.51 -3.81 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "ki_keywords" "simulation" (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "PWM_0_1"
      (polyline
        (pts
          (xy 0 -1.27)
          (xy 0 -2.286)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 0.254 -1.778)
          (xy 0 -1.27)
          (xy -0.254 -1.778)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (circle (center 0 0) (radius 2.54)
        (stroke (width 0.254) (type default))
        (fill (type background))
      )
    )
    (symbol "PWM_1_1"
      (polyline
        (pts
          (xy 0.254 3.81)
          (xy 0.762 3.81)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 0.508 4.064)
          (xy 0.508 3.556)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 1.905 -3.175)
          (xy 3.175 -1.905)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 5.334 3.81)
          (xy 5.842 3.81)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 5.588 4.064)
          (xy 5.588 3.556)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 6.985 1.905)
          (xy 8.255 3.175)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 6.985 3.175)
          (xy 8.255 3.175)
          (xy 8.255 1.905)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (circle (center 5.08 0) (radius 2.54)
        (stroke (width 0.254) (type default))
        (fill (type background))
      )
      (text "V" (at 5.08 -0.254 0)
        (effects (font (size 1.27 1.27)))
      )
      (pin input line (at 0 5.08 270) (length 2.54)
        (name "N+" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at 0 -5.08 90) (length 2.54)
        (name "N-" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 5.08 5.08 270) (length 2.54)
        (name "C+" (effects (font (size 1.27 1.27))))
        (number "3" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 5.08 -5.08 90) (length 2.54)
        (name "C-" (effects (font (size 1.27 1.27))))
        (number "4" (effects (font (size 1.27 1.27))))
      )
    )
  )
)
