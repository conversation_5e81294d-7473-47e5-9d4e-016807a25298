(kicad_sch
	(version 20250114)
	(generator "eeschema")
	(generator_version "9.0")
	(uuid "6706efee-2dfb-46b6-9a7f-ce6f339418f7")
	(paper "A4")
	(lib_symbols
		(symbol "Connector:TestPoint"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.762)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "TP"
				(at 0 6.858 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "TestPoint"
				(at 0 5.08 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 5.08 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 5.08 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "test point"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "test point tp"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Pin* Test*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "TestPoint_0_1"
				(circle
					(center 0 3.302)
					(radius 0.762)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "TestPoint_1_1"
				(pin passive line
					(at 0 0 90)
					(length 2.54)
					(name "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Connector:TestPoint_Small"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.762)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "TP"
				(at 0 3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "TestPoint_Small"
				(at 0 2.032 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 5.08 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 5.08 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "test point"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "test point tp"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Pin* Test*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "TestPoint_Small_0_1"
				(circle
					(center 0 0)
					(radius 0.508)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "TestPoint_Small_1_1"
				(pin passive line
					(at 0 0 90)
					(length 0)
					(name "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Connector_Generic_MountingPin:Conn_01x05_MountingPin"
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "J"
				(at 0 7.62 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "Conn_01x05_MountingPin"
				(at 1.27 -7.62 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Generic connectable mounting pin connector, single row, 01x05, script generated (kicad-library-utils/schlib/autogen/connector/)"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "connector"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Connector*:*_1x??-1MP*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Conn_01x05_MountingPin_1_1"
				(rectangle
					(start -1.27 6.35)
					(end 1.27 -6.35)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(rectangle
					(start -1.27 5.207)
					(end 0 4.953)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 2.667)
					(end 0 2.413)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 0.127)
					(end 0 -0.127)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 -2.413)
					(end 0 -2.667)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 -4.953)
					(end 0 -5.207)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.016 -7.112) (xy 1.016 -7.112)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(text "Mounting"
					(at 0 -6.731 0)
					(effects
						(font
							(size 0.381 0.381)
						)
					)
				)
				(pin passive line
					(at -5.08 5.08 0)
					(length 3.81)
					(name "Pin_1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 2.54 0)
					(length 3.81)
					(name "Pin_2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 0 0)
					(length 3.81)
					(name "Pin_3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 -2.54 0)
					(length 3.81)
					(name "Pin_4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 -5.08 0)
					(length 3.81)
					(name "Pin_5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -10.16 90)
					(length 3.048)
					(name "MountPin"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "MP"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Device:C_Small"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.254)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "C"
				(at 0.254 1.778 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "C_Small"
				(at 0.254 -2.032 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Unpolarized capacitor, small symbol"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "capacitor cap"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "C_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "C_Small_0_1"
				(polyline
					(pts
						(xy -1.524 0.508) (xy 1.524 0.508)
					)
					(stroke
						(width 0.3048)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.524 -0.508) (xy 1.524 -0.508)
					)
					(stroke
						(width 0.3302)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "C_Small_1_1"
				(pin passive line
					(at 0 2.54 270)
					(length 2.032)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -2.54 90)
					(length 2.032)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Device:Crystal_Small"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "Y"
				(at 0 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "Crystal_Small"
				(at 0 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Two pin crystal, small symbol"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "quartz ceramic resonator oscillator"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Crystal*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Crystal_Small_0_1"
				(polyline
					(pts
						(xy -1.27 -0.762) (xy -1.27 0.762)
					)
					(stroke
						(width 0.381)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -0.762 -1.524)
					(end 0.762 1.524)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -0.762) (xy 1.27 0.762)
					)
					(stroke
						(width 0.381)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "Crystal_Small_1_1"
				(pin passive line
					(at -2.54 0 0)
					(length 1.27)
					(name "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 0 180)
					(length 1.27)
					(name "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Device:LED_Small"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.254)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "D"
				(at -1.27 3.175 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "LED_Small"
				(at -4.445 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Light emitting diode, small symbol"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Pin" "1=K 2=A"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "LED diode light-emitting-diode"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "LED* LED_SMD:* LED_THT:*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "LED_Small_0_1"
				(polyline
					(pts
						(xy -0.762 -1.016) (xy -0.762 1.016)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0.762) (xy -0.508 1.27) (xy -0.254 1.27) (xy -0.508 1.27) (xy -0.508 1.016)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.508 1.27) (xy 0 1.778) (xy 0.254 1.778) (xy 0 1.778) (xy 0 1.524)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.762 -1.016) (xy -0.762 0) (xy 0.762 1.016) (xy 0.762 -1.016)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.016 0) (xy -0.762 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "LED_Small_1_1"
				(pin passive line
					(at -2.54 0 0)
					(length 1.778)
					(name "K"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 0 180)
					(length 1.778)
					(name "A"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Device:R_Small_US"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.254)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "R"
				(at 0.762 0.508 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "R_Small_US"
				(at 0.762 -1.016 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Resistor, small US symbol"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "r resistor"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "R_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "R_Small_US_1_1"
				(polyline
					(pts
						(xy 0 1.524) (xy 1.016 1.143) (xy 0 0.762) (xy -1.016 0.381) (xy 0 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 1.016 -0.381) (xy 0 -0.762) (xy -1.016 -1.143) (xy 0 -1.524)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(pin passive line
					(at 0 2.54 270)
					(length 1.016)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -2.54 90)
					(length 1.016)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Graphic:Logo_Open_Hardware_Small"
			(exclude_from_sim no)
			(in_bom no)
			(on_board no)
			(property "Reference" "#SYM"
				(at 0 6.985 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "Logo_Open_Hardware_Small"
				(at 0 -5.715 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Open Hardware logo, small"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Enable" "0"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "Logo"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Logo_Open_Hardware_Small_0_1"
				(polyline
					(pts
						(xy 3.3528 -4.3434) (xy 3.302 -4.318) (xy 3.175 -4.2418) (xy 2.9972 -4.1148) (xy 2.7686 -3.9624)
						(xy 2.54 -3.81) (xy 2.3622 -3.7084) (xy 2.2352 -3.6068) (xy 2.1844 -3.5814) (xy 2.159 -3.6068)
						(xy 2.0574 -3.6576) (xy 1.905 -3.7338) (xy 1.8034 -3.7846) (xy 1.6764 -3.8354) (xy 1.6002 -3.8354)
						(xy 1.6002 -3.8354) (xy 1.5494 -3.7338) (xy 1.4732 -3.5306) (xy 1.3462 -3.302) (xy 1.2446 -3.0226)
						(xy 1.1176 -2.7178) (xy 0.9652 -2.413) (xy 0.8636 -2.1082) (xy 0.7366 -1.8288) (xy 0.6604 -1.6256)
						(xy 0.6096 -1.4732) (xy 0.5842 -1.397) (xy 0.5842 -1.397) (xy 0.6604 -1.3208) (xy 0.7874 -1.2446)
						(xy 1.0414 -1.016) (xy 1.2954 -0.6858) (xy 1.4478 -0.3302) (xy 1.524 0.0762) (xy 1.4732 0.4572)
						(xy 1.3208 0.8128) (xy 1.0668 1.143) (xy 0.762 1.3716) (xy 0.4064 1.524) (xy 0 1.5748) (xy -0.381 1.5494)
						(xy -0.7366 1.397) (xy -1.0668 1.143) (xy -1.2192 0.9906) (xy -1.397 0.6604) (xy -1.524 0.3048)
						(xy -1.524 0.2286) (xy -1.4986 -0.1778) (xy -1.397 -0.5334) (xy -1.1938 -0.8636) (xy -0.9144 -1.143)
						(xy -0.8636 -1.1684) (xy -0.7366 -1.27) (xy -0.635 -1.3462) (xy -0.5842 -1.397) (xy -1.0668 -2.5908)
						(xy -1.143 -2.794) (xy -1.2954 -3.1242) (xy -1.397 -3.4036) (xy -1.4986 -3.6322) (xy -1.5748 -3.7846)
						(xy -1.6002 -3.8354) (xy -1.6002 -3.8354) (xy -1.651 -3.8354) (xy -1.7272 -3.81) (xy -1.905 -3.7338)
						(xy -2.0066 -3.683) (xy -2.1336 -3.6068) (xy -2.2098 -3.5814) (xy -2.2606 -3.6068) (xy -2.3622 -3.683)
						(xy -2.54 -3.81) (xy -2.7686 -3.9624) (xy -2.9718 -4.0894) (xy -3.1496 -4.2164) (xy -3.302 -4.318)
						(xy -3.3528 -4.3434) (xy -3.3782 -4.3434) (xy -3.429 -4.318) (xy -3.5306 -4.2164) (xy -3.7084 -4.064)
						(xy -3.937 -3.8354) (xy -3.9624 -3.81) (xy -4.1656 -3.6068) (xy -4.318 -3.4544) (xy -4.4196 -3.3274)
						(xy -4.445 -3.2766) (xy -4.445 -3.2766) (xy -4.4196 -3.2258) (xy -4.318 -3.0734) (xy -4.2164 -2.8956)
						(xy -4.064 -2.667) (xy -3.6576 -2.0828) (xy -3.8862 -1.5494) (xy -3.937 -1.3716) (xy -4.0386 -1.1684)
						(xy -4.0894 -1.0414) (xy -4.1148 -0.9652) (xy -4.191 -0.9398) (xy -4.318 -0.9144) (xy -4.5466 -0.8636)
						(xy -4.8006 -0.8128) (xy -5.0546 -0.7874) (xy -5.2578 -0.7366) (xy -5.4356 -0.7112) (xy -5.5118 -0.6858)
						(xy -5.5118 -0.6858) (xy -5.5372 -0.635) (xy -5.5372 -0.5588) (xy -5.5372 -0.4318) (xy -5.5626 -0.2286)
						(xy -5.5626 0.0762) (xy -5.5626 0.127) (xy -5.5372 0.4064) (xy -5.5372 0.635) (xy -5.5372 0.762)
						(xy -5.5372 0.8382) (xy -5.5372 0.8382) (xy -5.461 0.8382) (xy -5.3086 0.889) (xy -5.08 0.9144)
						(xy -4.826 0.9652) (xy -4.8006 0.9906) (xy -4.5466 1.0414) (xy -4.318 1.0668) (xy -4.1656 1.1176)
						(xy -4.0894 1.143) (xy -4.0894 1.143) (xy -4.0386 1.2446) (xy -3.9624 1.4224) (xy -3.8608 1.6256)
						(xy -3.7846 1.8288) (xy -3.7084 2.0066) (xy -3.6576 2.159) (xy -3.6322 2.2098) (xy -3.6322 2.2098)
						(xy -3.683 2.286) (xy -3.7592 2.413) (xy -3.8862 2.5908) (xy -4.064 2.8194) (xy -4.064 2.8448)
						(xy -4.2164 3.0734) (xy -4.3434 3.2512) (xy -4.4196 3.3782) (xy -4.445 3.4544) (xy -4.445 3.4544)
						(xy -4.3942 3.5052) (xy -4.2926 3.6322) (xy -4.1148 3.81) (xy -3.937 4.0132) (xy -3.8608 4.064)
						(xy -3.6576 4.2926) (xy -3.5052 4.4196) (xy -3.4036 4.4958) (xy -3.3528 4.5212) (xy -3.3528 4.5212)
						(xy -3.302 4.4704) (xy -3.1496 4.3688) (xy -2.9718 4.2418) (xy -2.7432 4.0894) (xy -2.7178 4.0894)
						(xy -2.4892 3.937) (xy -2.3114 3.81) (xy -2.1844 3.7084) (xy -2.1336 3.683) (xy -2.1082 3.683)
						(xy -2.032 3.7084) (xy -1.8542 3.7592) (xy -1.6764 3.8354) (xy -1.4732 3.937) (xy -1.27 4.0132)
						(xy -1.143 4.064) (xy -1.0668 4.1148) (xy -1.0668 4.1148) (xy -1.0414 4.191) (xy -1.016 4.3434)
						(xy -0.9652 4.572) (xy -0.9144 4.8514) (xy -0.889 4.9022) (xy -0.8382 5.1562) (xy -0.8128 5.3848)
						(xy -0.7874 5.5372) (xy -0.762 5.588) (xy -0.7112 5.6134) (xy -0.5842 5.6134) (xy -0.4064 5.6134)
						(xy -0.1524 5.6134) (xy 0.0762 5.6134) (xy 0.3302 5.6134) (xy 0.5334 5.6134) (xy 0.6858 5.588)
						(xy 0.7366 5.588) (xy 0.7366 5.588) (xy 0.762 5.5118) (xy 0.8128 5.334) (xy 0.8382 5.1054) (xy 0.9144 4.826)
						(xy 0.9144 4.7752) (xy 0.9652 4.5212) (xy 1.016 4.2926) (xy 1.0414 4.1402) (xy 1.0668 4.0894)
						(xy 1.0668 4.0894) (xy 1.1938 4.0386) (xy 1.3716 3.9624) (xy 1.5748 3.8608) (xy 2.0828 3.6576)
						(xy 2.7178 4.0894) (xy 2.7686 4.1402) (xy 2.9972 4.2926) (xy 3.175 4.4196) (xy 3.302 4.4958) (xy 3.3782 4.5212)
						(xy 3.3782 4.5212) (xy 3.429 4.4704) (xy 3.556 4.3434) (xy 3.7338 4.191) (xy 3.9116 3.9878) (xy 4.064 3.8354)
						(xy 4.2418 3.6576) (xy 4.3434 3.556) (xy 4.4196 3.4798) (xy 4.4196 3.429) (xy 4.4196 3.4036) (xy 4.3942 3.3274)
						(xy 4.2926 3.2004) (xy 4.1656 2.9972) (xy 4.0132 2.794) (xy 3.8862 2.5908) (xy 3.7592 2.3876)
						(xy 3.6576 2.2352) (xy 3.6322 2.159) (xy 3.6322 2.1336) (xy 3.683 2.0066) (xy 3.7592 1.8288) (xy 3.8608 1.6002)
						(xy 4.064 1.1176) (xy 4.3942 1.0414) (xy 4.5974 1.016) (xy 4.8768 0.9652) (xy 5.1308 0.9144) (xy 5.5372 0.8382)
						(xy 5.5626 -0.6604) (xy 5.4864 -0.6858) (xy 5.4356 -0.6858) (xy 5.2832 -0.7366) (xy 5.0546 -0.762)
						(xy 4.8006 -0.8128) (xy 4.5974 -0.8636) (xy 4.3688 -0.9144) (xy 4.2164 -0.9398) (xy 4.1402 -0.9398)
						(xy 4.1148 -0.9652) (xy 4.064 -1.0668) (xy 3.9878 -1.2446) (xy 3.9116 -1.4478) (xy 3.81 -1.651)
						(xy 3.7338 -1.8542) (xy 3.683 -2.0066) (xy 3.6576 -2.0828) (xy 3.683 -2.1336) (xy 3.7846 -2.2606)
						(xy 3.8862 -2.4638) (xy 4.0386 -2.667) (xy 4.191 -2.8956) (xy 4.318 -3.0734) (xy 4.3942 -3.2004)
						(xy 4.445 -3.2766) (xy 4.4196 -3.3274) (xy 4.3434 -3.429) (xy 4.1656 -3.5814) (xy 3.937 -3.8354)
						(xy 3.8862 -3.8608) (xy 3.683 -4.064) (xy 3.5306 -4.2164) (xy 3.4036 -4.318) (xy 3.3528 -4.3434)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "LordsBoards-Graphic:LordsBoardsLogo"
			(exclude_from_sim no)
			(in_bom no)
			(on_board no)
			(property "Reference" "#SYM"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "LordsBoardsLogo_1_0"
				(polyline
					(pts
						(xy -5.5268 4.7992) (xy -5.473 4.7941) (xy -5.4197 4.7851) (xy -5.3671 4.7724) (xy -5.3154 4.7559)
						(xy -5.2647 4.7357) (xy -5.2152 4.7117) (xy -5.1671 4.6841) (xy -5.1206 4.6528) (xy -5.0759 4.6179)
						(xy -2.4774 2.4374) (xy -2.4704 2.4318) (xy -2.4632 2.4265) (xy -2.4558 2.4216) (xy -2.4483 2.4171)
						(xy -2.4406 2.413) (xy -2.4328 2.4092) (xy -2.4249 2.4058) (xy -2.4169 2.4028) (xy -2.4089 2.4001)
						(xy -2.4007 2.3978) (xy -2.3924 2.3959) (xy -2.3841 2.3943) (xy -2.3758 2.3931) (xy -2.3674 2.3923)
						(xy -2.359 2.3918) (xy -2.3506 2.3918) (xy -2.3422 2.392) (xy -2.3338 2.3927) (xy -2.3255 2.3937)
						(xy -2.3172 2.3951) (xy -2.3089 2.3968) (xy -2.3007 2.3989) (xy -2.2926 2.4014) (xy -2.2846 2.4042)
						(xy -2.2766 2.4074) (xy -2.2688 2.411) (xy -2.2611 2.4149) (xy -2.2536 2.4192) (xy -2.2462 2.4239)
						(xy -2.239 2.4289) (xy -2.2319 2.4343) (xy -2.225 2.4401) (xy -0.5157 3.9374) (xy -0.488 3.9606)
						(xy -0.4595 3.9822) (xy -0.4303 4.0024) (xy -0.4003 4.021) (xy -0.3697 4.0382) (xy -0.3385 4.0539)
						(xy -0.3068 4.0681) (xy -0.2745 4.0808) (xy -0.2418 4.092) (xy -0.2088 4.1018) (xy -0.1754 4.11)
						(xy -0.1418 4.1168) (xy -0.1079 4.122) (xy -0.0739 4.1258) (xy -0.0397 4.1281) (xy -0.0055 4.1288)
						(xy 0.0287 4.1281) (xy 0.0628 4.1259) (xy 0.0969 4.1223) (xy 0.1307 4.1171) (xy 0.1644 4.1104)
						(xy 0.1978 4.1023) (xy 0.2309 4.0926) (xy 0.2636 4.0815) (xy 0.2958 4.0688) (xy 0.3276 4.0547)
						(xy 0.3589 4.0391) (xy 0.3895 4.022) (xy 0.4195 4.0034) (xy 0.4488 3.9833) (xy 0.4774 3.9617)
						(xy 0.5051 3.9387) (xy 2.2245 2.4392) (xy 2.2314 2.4335) (xy 2.2385 2.4281) (xy 2.2457 2.4231)
						(xy 2.2531 2.4184) (xy 2.2607 2.4141) (xy 2.2684 2.4102) (xy 2.2762 2.4066) (xy 2.2841 2.4034)
						(xy 2.2921 2.4006) (xy 2.3003 2.3982) (xy 2.3085 2.3961) (xy 2.3167 2.3943) (xy 2.325 2.393) (xy 2.3334 2.392)
						(xy 2.3417 2.3914) (xy 2.3501 2.3911) (xy 2.3585 2.3912) (xy 2.3669 2.3917) (xy 2.3753 2.3925)
						(xy 2.3836 2.3937) (xy 2.3919 2.3953) (xy 2.4001 2.3972) (xy 2.4082 2.3995) (xy 2.4163 2.4022)
						(xy 2.4243 2.4052) (xy 2.4322 2.4087) (xy 2.4399 2.4124) (xy 2.4475 2.4166) (xy 2.455 2.4211)
						(xy 2.4624 2.4259) (xy 2.4695 2.4312) (xy 2.4765 2.4368) (xy 5.0759 4.6179) (xy 5.1206 4.6528)
						(xy 5.1671 4.6841) (xy 5.2152 4.7117) (xy 5.2647 4.7356) (xy 5.3154 4.7558) (xy 5.3671 4.7723)
						(xy 5.4197 4.7851) (xy 5.473 4.794) (xy 5.5268 4.7992) (xy 5.5809 4.8005) (xy 5.6351 4.798) (xy 5.6893 4.7916)
						(xy 5.7433 4.7813) (xy 5.7969 4.7671) (xy 5.8499 4.7489) (xy 5.9021 4.7268) (xy 5.9527 4.701)
						(xy 6.0007 4.6721) (xy 6.046 4.6402) (xy 6.0886 4.6054) (xy 6.1284 4.568) (xy 6.1652 4.5281) (xy 6.199 4.4858)
						(xy 6.2296 4.4413) (xy 6.257 4.3947) (xy 6.2811 4.3462) (xy 6.3017 4.296) (xy 6.3189 4.2441) (xy 6.3323 4.1908)
						(xy 6.3421 4.1362) (xy 6.348 4.0805) (xy 6.35 4.0238) (xy 6.35 -4.0327) (xy 6.349 -4.0725) (xy 6.346 -4.1118)
						(xy 6.341 -4.1506) (xy 6.3342 -4.1888) (xy 6.3255 -4.2263) (xy 6.3151 -4.2631) (xy 6.3029 -4.2991)
						(xy 6.289 -4.3343) (xy 6.2734 -4.3686) (xy 6.2563 -4.4021) (xy 6.2376 -4.4345) (xy 6.2174 -4.466)
						(xy 6.1957 -4.4964) (xy 6.1727 -4.5257) (xy 6.1483 -4.5539) (xy 6.1226 -4.5808) (xy 6.0956 -4.6065)
						(xy 6.0675 -4.6309) (xy 6.0382 -4.654) (xy 6.0077 -4.6756) (xy 5.9763 -4.6958) (xy 5.9438 -4.7145)
						(xy 5.9104 -4.7316) (xy 5.876 -4.7472) (xy 5.8408 -4.7611) (xy 5.8048 -4.7733) (xy 5.768 -4.7838)
						(xy 5.7305 -4.7924) (xy 5.6924 -4.7993) (xy 5.6536 -4.8042) (xy 5.6143 -4.8072) (xy 5.5744 -4.8082)
						(xy -5.5744 -4.8082) (xy -5.6143 -4.8072) (xy -5.6536 -4.8042) (xy -5.6924 -4.7993) (xy -5.7306 -4.7924)
						(xy -5.7681 -4.7838) (xy -5.8048 -4.7733) (xy -5.8409 -4.7611) (xy -5.8761 -4.7472) (xy -5.9104 -4.7316)
						(xy -5.9438 -4.7145) (xy -5.9763 -4.6958) (xy -6.0078 -4.6756) (xy -6.0382 -4.654) (xy -6.0675 -4.6309)
						(xy -6.0957 -4.6065) (xy -6.1226 -4.5808) (xy -6.1483 -4.5539) (xy -6.1727 -4.5257) (xy -6.1957 -4.4964)
						(xy -6.2174 -4.466) (xy -6.2376 -4.4345) (xy -6.2563 -4.4021) (xy -6.2734 -4.3686) (xy -6.289 -4.3343)
						(xy -6.3029 -4.2991) (xy -6.3151 -4.2631) (xy -6.3255 -4.2263) (xy -6.3342 -4.1888) (xy -6.341 -4.1506)
						(xy -6.346 -4.1118) (xy -6.349 -4.0725) (xy -6.35 -4.0327) (xy -6.35 1.5556) (xy -5.7683 1.5556)
						(xy -5.7683 -4.0327) (xy -5.7681 -4.0426) (xy -5.7673 -4.0525) (xy -5.7661 -4.0622) (xy -5.7644 -4.0717)
						(xy -5.7622 -4.0811) (xy -5.7596 -4.0903) (xy -5.7566 -4.0993) (xy -5.7531 -4.1081) (xy -5.7492 -4.1167)
						(xy -5.7449 -4.1251) (xy -5.7403 -4.1332) (xy -5.7352 -4.1411) (xy -5.7298 -4.1487) (xy -5.7241 -4.156)
						(xy -5.718 -4.163) (xy -5.7115 -4.1698) (xy -5.7048 -4.1762) (xy -5.6978 -4.1823) (xy -5.6905 -4.188)
						(xy -5.6829 -4.1934) (xy -5.675 -4.1985) (xy -5.6669 -4.2032) (xy -5.6585 -4.2074) (xy -5.6499 -4.2113)
						(xy -5.6411 -4.2148) (xy -5.6321 -4.2178) (xy -5.6229 -4.2204) (xy -5.6135 -4.2226) (xy -5.604 -4.2243)
						(xy -5.5943 -4.2255) (xy -5.5844 -4.2263) (xy -5.5744 -4.2265) (xy 5.5744 -4.2265) (xy 5.5844 -4.2263)
						(xy 5.5942 -4.2255) (xy 5.6039 -4.2243) (xy 5.6135 -4.2226) (xy 5.6229 -4.2204) (xy 5.6321 -4.2178)
						(xy 5.6411 -4.2148) (xy 5.6499 -4.2113) (xy 5.6585 -4.2074) (xy 5.6668 -4.2032) (xy 5.6749 -4.1985)
						(xy 5.6828 -4.1934) (xy 5.6904 -4.188) (xy 5.6977 -4.1823) (xy 5.7048 -4.1762) (xy 5.7115 -4.1698)
						(xy 5.7179 -4.163) (xy 5.724 -4.156) (xy 5.7298 -4.1487) (xy 5.7352 -4.1411) (xy 5.7402 -4.1332)
						(xy 5.7449 -4.1251) (xy 5.7492 -4.1167) (xy 5.7531 -4.1081) (xy 5.7565 -4.0993) (xy 5.7596 -4.0903)
						(xy 5.7622 -4.0811) (xy 5.7644 -4.0717) (xy 5.7661 -4.0622) (xy 5.7673 -4.0525) (xy 5.7681 -4.0426)
						(xy 5.7683 -4.0327) (xy 5.7683 1.5556) (xy 5.768 1.5681) (xy 5.7671 1.5804) (xy 5.7655 1.5925)
						(xy 5.7634 1.6045) (xy 5.7607 1.6162) (xy 5.7574 1.6277) (xy 5.7536 1.6389) (xy 5.7493 1.6499)
						(xy 5.7444 1.6607) (xy 5.7391 1.6711) (xy 5.7332 1.6813) (xy 5.7269 1.6911) (xy 5.7201 1.7006)
						(xy 5.713 1.7098) (xy 5.7053 1.7186) (xy 5.6973 1.727) (xy 5.6889 1.735) (xy 5.6801 1.7426) (xy 5.6709 1.7498)
						(xy 5.6614 1.7566) (xy 5.6516 1.7629) (xy 5.6414 1.7687) (xy 5.631 1.7741) (xy 5.6203 1.7789)
						(xy 5.6093 1.7833) (xy 5.598 1.7871) (xy 5.5865 1.7904) (xy 5.5748 1.7931) (xy 5.5628 1.7952)
						(xy 5.5507 1.7968) (xy 5.5384 1.7977) (xy 5.526 1.798) (xy -5.526 1.798) (xy -5.5384 1.7977) (xy -5.5507 1.7968)
						(xy -5.5629 1.7952) (xy -5.5748 1.7931) (xy -5.5865 1.7904) (xy -5.598 1.7871) (xy -5.6093 1.7833)
						(xy -5.6203 1.7789) (xy -5.631 1.7741) (xy -5.6415 1.7687) (xy -5.6516 1.7629) (xy -5.6615 1.7566)
						(xy -5.671 1.7498) (xy -5.6801 1.7426) (xy -5.6889 1.735) (xy -5.6973 1.727) (xy -5.7054 1.7186)
						(xy -5.713 1.7098) (xy -5.7202 1.7006) (xy -5.7269 1.6911) (xy -5.7332 1.6813) (xy -5.7391 1.6711)
						(xy -5.7444 1.6607) (xy -5.7493 1.6499) (xy -5.7536 1.6389) (xy -5.7574 1.6277) (xy -5.7607 1.6162)
						(xy -5.7634 1.6045) (xy -5.7655 1.5925) (xy -5.7671 1.5804) (xy -5.768 1.5681) (xy -5.7683 1.5556)
						(xy -6.35 1.5556) (xy -6.35 4.0238) (xy -6.3495 4.0523) (xy -6.348 4.0805) (xy -6.3455 4.1085)
						(xy -6.3421 4.1362) (xy -6.3377 4.1637) (xy -6.3323 4.1908) (xy -6.3261 4.2176) (xy -6.3189 4.2441)
						(xy -6.3108 4.2702) (xy -6.3018 4.296) (xy -6.2919 4.3213) (xy -6.2811 4.3462) (xy -6.2695 4.3707)
						(xy -6.2571 4.3947) (xy -6.2438 4.4182) (xy -6.2297 4.4413) (xy -6.2148 4.4638) (xy -6.199 4.4858)
						(xy -6.1825 4.5072) (xy -6.1653 4.5281) (xy -6.1472 4.5484) (xy -6.1285 4.568) (xy -6.1089 4.587)
						(xy -6.0887 4.6054) (xy -6.0677 4.6231) (xy -6.0461 4.6402) (xy -6.0238 4.6565) (xy -6.0007 4.6721)
						(xy -5.9771 4.6869) (xy -5.9527 4.701) (xy -5.9278 4.7143) (xy -5.9022 4.7268) (xy -5.85 4.7489)
						(xy -5.797 4.7671) (xy -5.7434 4.7813) (xy -5.6894 4.7916) (xy -5.6352 4.798) (xy -5.5809 4.8006)
						(xy -5.5268 4.7992)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -4.5733 1.0153) (xy -4.5659 1.0147) (xy -4.5586 1.0138) (xy -4.5515 1.0125) (xy -4.5444 1.0109)
						(xy -4.5375 1.0089) (xy -4.5308 1.0067) (xy -4.5242 1.0041) (xy -4.5177 1.0011) (xy -4.5115 0.9979)
						(xy -4.5054 0.9944) (xy -4.4995 0.9906) (xy -4.4938 0.9866) (xy -4.4883 0.9823) (xy -4.483 0.9777)
						(xy -4.4779 0.9729) (xy -4.4731 0.9678) (xy -4.4686 0.9626) (xy -4.4642 0.9571) (xy -4.4602 0.9514)
						(xy -4.4564 0.9455) (xy -4.4529 0.9394) (xy -4.4497 0.9331) (xy -4.4468 0.9267) (xy -4.4442 0.9201)
						(xy -4.4419 0.9133) (xy -4.4399 0.9064) (xy -4.4383 0.8994) (xy -4.437 0.8922) (xy -4.4361 0.8849)
						(xy -4.4355 0.8776) (xy -4.4353 0.8701) (xy -4.4353 0.3369) (xy -4.4355 0.3294) (xy -4.4361 0.322)
						(xy -4.437 0.3147) (xy -4.4383 0.3076) (xy -4.4399 0.3005) (xy -4.4419 0.2936) (xy -4.4442 0.2869)
						(xy -4.4468 0.2803) (xy -4.4497 0.2738) (xy -4.4529 0.2676) (xy -4.4564 0.2615) (xy -4.4602 0.2556)
						(xy -4.4642 0.2499) (xy -4.4686 0.2444) (xy -4.4731 0.2391) (xy -4.4779 0.2341) (xy -4.483 0.2292)
						(xy -4.4883 0.2247) (xy -4.4938 0.2203) (xy -4.4995 0.2163) (xy -4.5054 0.2125) (xy -4.5115 0.209)
						(xy -4.5177 0.2058) (xy -4.5242 0.2029) (xy -4.5308 0.2003) (xy -4.5375 0.198) (xy -4.5444 0.196)
						(xy -4.5515 0.1944) (xy -4.5586 0.1931) (xy -4.5659 0.1922) (xy -4.5733 0.1916) (xy -4.5808 0.1914)
						(xy -5.114 0.1914) (xy -5.1214 0.1916) (xy -5.1288 0.1922) (xy -5.1361 0.1931) (xy -5.1433 0.1944)
						(xy -5.1503 0.196) (xy -5.1572 0.198) (xy -5.1639 0.2003) (xy -5.1705 0.2029) (xy -5.177 0.2058)
						(xy -5.1833 0.209) (xy -5.1893 0.2125) (xy -5.1952 0.2163) (xy -5.2009 0.2203) (xy -5.2064 0.2247)
						(xy -5.2117 0.2292) (xy -5.2168 0.2341) (xy -5.2216 0.2391) (xy -5.2262 0.2444) (xy -5.2305 0.2499)
						(xy -5.2345 0.2556) (xy -5.2383 0.2615) (xy -5.2418 0.2676) (xy -5.245 0.2738) (xy -5.2479 0.2803)
						(xy -5.2506 0.2869) (xy -5.2528 0.2936) (xy -5.2548 0.3005) (xy -5.2564 0.3076) (xy -5.2577 0.3147)
						(xy -5.2586 0.322) (xy -5.2592 0.3294) (xy -5.2594 0.3369) (xy -5.2594 0.8701) (xy -5.2592 0.8776)
						(xy -5.2586 0.8849) (xy -5.2577 0.8922) (xy -5.2564 0.8994) (xy -5.2548 0.9064) (xy -5.2528 0.9133)
						(xy -5.2506 0.9201) (xy -5.2479 0.9267) (xy -5.245 0.9331) (xy -5.2418 0.9394) (xy -5.2383 0.9455)
						(xy -5.2345 0.9514) (xy -5.2305 0.9571) (xy -5.2262 0.9626) (xy -5.2216 0.9678) (xy -5.2168 0.9729)
						(xy -5.2117 0.9777) (xy -5.2064 0.9823) (xy -5.2009 0.9866) (xy -5.1952 0.9906) (xy -5.1893 0.9944)
						(xy -5.1833 0.9979) (xy -5.177 1.0011) (xy -5.1705 1.0041) (xy -5.1639 1.0067) (xy -5.1572 1.0089)
						(xy -5.1503 1.0109) (xy -5.1433 1.0125) (xy -5.1361 1.0138) (xy -5.1288 1.0147) (xy -5.1214 1.0153)
						(xy -5.114 1.0155) (xy -4.5808 1.0155) (xy -4.5733 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -4.5733 -0.1965) (xy -4.5659 -0.1971) (xy -4.5586 -0.198) (xy -4.5515 -0.1993) (xy -4.5444 -0.2009)
						(xy -4.5375 -0.2029) (xy -4.5308 -0.2052) (xy -4.5242 -0.2078) (xy -4.5177 -0.2107) (xy -4.5115 -0.2139)
						(xy -4.5054 -0.2174) (xy -4.4995 -0.2212) (xy -4.4938 -0.2253) (xy -4.4883 -0.2296) (xy -4.483 -0.2341)
						(xy -4.4779 -0.239) (xy -4.4731 -0.244) (xy -4.4686 -0.2493) (xy -4.4642 -0.2548) (xy -4.4602 -0.2605)
						(xy -4.4564 -0.2664) (xy -4.4529 -0.2725) (xy -4.4497 -0.2787) (xy -4.4468 -0.2852) (xy -4.4442 -0.2918)
						(xy -4.4419 -0.2985) (xy -4.4399 -0.3054) (xy -4.4383 -0.3125) (xy -4.437 -0.3196) (xy -4.4361 -0.3269)
						(xy -4.4355 -0.3343) (xy -4.4353 -0.3418) (xy -4.4353 -0.875) (xy -4.4355 -0.8825) (xy -4.4361 -0.8898)
						(xy -4.437 -0.8971) (xy -4.4383 -0.9043) (xy -4.4399 -0.9113) (xy -4.4419 -0.9182) (xy -4.4442 -0.925)
						(xy -4.4468 -0.9316) (xy -4.4497 -0.938) (xy -4.4529 -0.9443) (xy -4.4564 -0.9504) (xy -4.4602 -0.9563)
						(xy -4.4642 -0.962) (xy -4.4686 -0.9675) (xy -4.4731 -0.9727) (xy -4.4779 -0.9778) (xy -4.483 -0.9826)
						(xy -4.4883 -0.9872) (xy -4.4938 -0.9915) (xy -4.4995 -0.9956) (xy -4.5054 -0.9993) (xy -4.5115 -1.0028)
						(xy -4.5177 -1.006) (xy -4.5242 -1.009) (xy -4.5308 -1.0116) (xy -4.5375 -1.0139) (xy -4.5444 -1.0158)
						(xy -4.5515 -1.0174) (xy -4.5586 -1.0187) (xy -4.5659 -1.0196) (xy -4.5733 -1.0202) (xy -4.5808 -1.0204)
						(xy -5.114 -1.0204) (xy -5.1214 -1.0202) (xy -5.1288 -1.0196) (xy -5.1361 -1.0187) (xy -5.1433 -1.0174)
						(xy -5.1503 -1.0158) (xy -5.1572 -1.0139) (xy -5.1639 -1.0116) (xy -5.1705 -1.009) (xy -5.177 -1.006)
						(xy -5.1833 -1.0028) (xy -5.1893 -0.9993) (xy -5.1952 -0.9956) (xy -5.2009 -0.9915) (xy -5.2064 -0.9872)
						(xy -5.2117 -0.9826) (xy -5.2168 -0.9778) (xy -5.2216 -0.9727) (xy -5.2262 -0.9675) (xy -5.2305 -0.962)
						(xy -5.2345 -0.9563) (xy -5.2383 -0.9504) (xy -5.2418 -0.9443) (xy -5.245 -0.938) (xy -5.2479 -0.9316)
						(xy -5.2506 -0.925) (xy -5.2528 -0.9182) (xy -5.2548 -0.9113) (xy -5.2564 -0.9043) (xy -5.2577 -0.8971)
						(xy -5.2586 -0.8898) (xy -5.2592 -0.8825) (xy -5.2594 -0.875) (xy -5.2594 -0.3418) (xy -5.2592 -0.3343)
						(xy -5.2586 -0.3269) (xy -5.2577 -0.3196) (xy -5.2564 -0.3125) (xy -5.2548 -0.3054) (xy -5.2528 -0.2985)
						(xy -5.2506 -0.2918) (xy -5.2479 -0.2852) (xy -5.245 -0.2787) (xy -5.2418 -0.2725) (xy -5.2383 -0.2664)
						(xy -5.2345 -0.2605) (xy -5.2305 -0.2548) (xy -5.2262 -0.2493) (xy -5.2216 -0.244) (xy -5.2168 -0.239)
						(xy -5.2117 -0.2341) (xy -5.2064 -0.2296) (xy -5.2009 -0.2253) (xy -5.1952 -0.2212) (xy -5.1893 -0.2174)
						(xy -5.1833 -0.2139) (xy -5.177 -0.2107) (xy -5.1705 -0.2078) (xy -5.1639 -0.2052) (xy -5.1572 -0.2029)
						(xy -5.1503 -0.2009) (xy -5.1433 -0.1993) (xy -5.1361 -0.198) (xy -5.1288 -0.1971) (xy -5.1214 -0.1965)
						(xy -5.114 -0.1964) (xy -4.5808 -0.1964) (xy -4.5733 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -4.5733 -2.6029) (xy -4.5659 -2.6035) (xy -4.5586 -2.6044) (xy -4.5515 -2.6057) (xy -4.5444 -2.6073)
						(xy -4.5375 -2.6093) (xy -4.5308 -2.6115) (xy -4.5242 -2.6141) (xy -4.5177 -2.6171) (xy -4.5115 -2.6203)
						(xy -4.5054 -2.6238) (xy -4.4995 -2.6276) (xy -4.4938 -2.6316) (xy -4.4883 -2.6359) (xy -4.483 -2.6405)
						(xy -4.4779 -2.6453) (xy -4.4731 -2.6504) (xy -4.4686 -2.6556) (xy -4.4642 -2.6611) (xy -4.4602 -2.6668)
						(xy -4.4564 -2.6727) (xy -4.4529 -2.6788) (xy -4.4497 -2.6851) (xy -4.4468 -2.6915) (xy -4.4442 -2.6981)
						(xy -4.4419 -2.7049) (xy -4.4399 -2.7118) (xy -4.4383 -2.7188) (xy -4.437 -2.726) (xy -4.4361 -2.7333)
						(xy -4.4355 -2.7406) (xy -4.4353 -2.7481) (xy -4.4353 -3.2813) (xy -4.4355 -3.2888) (xy -4.4361 -3.2962)
						(xy -4.437 -3.3035) (xy -4.4383 -3.3106) (xy -4.4399 -3.3177) (xy -4.4419 -3.3246) (xy -4.4442 -3.3313)
						(xy -4.4468 -3.3379) (xy -4.4497 -3.3444) (xy -4.4529 -3.3506) (xy -4.4564 -3.3567) (xy -4.4602 -3.3626)
						(xy -4.4642 -3.3683) (xy -4.4686 -3.3738) (xy -4.4731 -3.3791) (xy -4.4779 -3.3841) (xy -4.483 -3.389)
						(xy -4.4883 -3.3935) (xy -4.4938 -3.3979) (xy -4.4995 -3.4019) (xy -4.5054 -3.4057) (xy -4.5115 -3.4092)
						(xy -4.5177 -3.4124) (xy -4.5242 -3.4153) (xy -4.5308 -3.4179) (xy -4.5375 -3.4202) (xy -4.5444 -3.4222)
						(xy -4.5515 -3.4238) (xy -4.5586 -3.4251) (xy -4.5659 -3.426) (xy -4.5733 -3.4266) (xy -4.5808 -3.4268)
						(xy -5.114 -3.4268) (xy -5.1214 -3.4266) (xy -5.1288 -3.426) (xy -5.1361 -3.4251) (xy -5.1433 -3.4238)
						(xy -5.1503 -3.4222) (xy -5.1572 -3.4202) (xy -5.1639 -3.4179) (xy -5.1705 -3.4153) (xy -5.177 -3.4124)
						(xy -5.1833 -3.4092) (xy -5.1893 -3.4057) (xy -5.1952 -3.4019) (xy -5.2009 -3.3979) (xy -5.2064 -3.3935)
						(xy -5.2117 -3.389) (xy -5.2168 -3.3841) (xy -5.2216 -3.3791) (xy -5.2262 -3.3738) (xy -5.2305 -3.3683)
						(xy -5.2345 -3.3626) (xy -5.2383 -3.3567) (xy -5.2418 -3.3506) (xy -5.245 -3.3444) (xy -5.2479 -3.3379)
						(xy -5.2506 -3.3313) (xy -5.2528 -3.3246) (xy -5.2548 -3.3177) (xy -5.2564 -3.3106) (xy -5.2577 -3.3035)
						(xy -5.2586 -3.2962) (xy -5.2592 -3.2888) (xy -5.2594 -3.2813) (xy -5.2594 -2.7481) (xy -5.2592 -2.7406)
						(xy -5.2586 -2.7333) (xy -5.2577 -2.726) (xy -5.2564 -2.7188) (xy -5.2548 -2.7118) (xy -5.2528 -2.7049)
						(xy -5.2506 -2.6981) (xy -5.2479 -2.6915) (xy -5.245 -2.6851) (xy -5.2418 -2.6788) (xy -5.2383 -2.6727)
						(xy -5.2345 -2.6668) (xy -5.2305 -2.6611) (xy -5.2262 -2.6556) (xy -5.2216 -2.6504) (xy -5.2168 -2.6453)
						(xy -5.2117 -2.6405) (xy -5.2064 -2.6359) (xy -5.2009 -2.6316) (xy -5.1952 -2.6276) (xy -5.1893 -2.6238)
						(xy -5.1833 -2.6203) (xy -5.177 -2.6171) (xy -5.1705 -2.6141) (xy -5.1639 -2.6115) (xy -5.1572 -2.6093)
						(xy -5.1503 -2.6073) (xy -5.1433 -2.6057) (xy -5.1361 -2.6044) (xy -5.1288 -2.6035) (xy -5.1214 -2.6029)
						(xy -5.114 -2.6027) (xy -4.5808 -2.6027) (xy -4.5733 -2.6029)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -4.5733 -2.6202) (xy -4.5659 -2.6208) (xy -4.5586 -2.6217) (xy -4.5515 -2.623) (xy -4.5444 -2.6246)
						(xy -4.5375 -2.6266) (xy -4.5308 -2.6288) (xy -4.5242 -2.6314) (xy -4.5177 -2.6344) (xy -4.5115 -2.6376)
						(xy -4.5054 -2.6411) (xy -4.4995 -2.6449) (xy -4.4938 -2.6489) (xy -4.4883 -2.6532) (xy -4.483 -2.6578)
						(xy -4.4779 -2.6626) (xy -4.4731 -2.6677) (xy -4.4686 -2.673) (xy -4.4642 -2.6784) (xy -4.4602 -2.6841)
						(xy -4.4564 -2.69) (xy -4.4529 -2.6961) (xy -4.4497 -2.7024) (xy -4.4468 -2.7088) (xy -4.4442 -2.7155)
						(xy -4.4419 -2.7222) (xy -4.4399 -2.7291) (xy -4.4383 -2.7361) (xy -4.437 -2.7433) (xy -4.4361 -2.7506)
						(xy -4.4355 -2.758) (xy -4.4353 -2.7654) (xy -4.4353 -3.2986) (xy -4.4355 -3.3061) (xy -4.4361 -3.3135)
						(xy -4.437 -3.3208) (xy -4.4383 -3.3279) (xy -4.4399 -3.335) (xy -4.4419 -3.3419) (xy -4.4442 -3.3486)
						(xy -4.4468 -3.3552) (xy -4.4497 -3.3617) (xy -4.4529 -3.3679) (xy -4.4564 -3.374) (xy -4.4602 -3.3799)
						(xy -4.4642 -3.3856) (xy -4.4686 -3.3911) (xy -4.4731 -3.3964) (xy -4.4779 -3.4015) (xy -4.483 -3.4063)
						(xy -4.4883 -3.4108) (xy -4.4938 -3.4152) (xy -4.4995 -3.4192) (xy -4.5054 -3.423) (xy -4.5115 -3.4265)
						(xy -4.5177 -3.4297) (xy -4.5242 -3.4326) (xy -4.5308 -3.4352) (xy -4.5375 -3.4375) (xy -4.5444 -3.4395)
						(xy -4.5515 -3.4411) (xy -4.5586 -3.4424) (xy -4.5659 -3.4433) (xy -4.5733 -3.4439) (xy -4.5808 -3.4441)
						(xy -5.114 -3.4441) (xy -5.1214 -3.4439) (xy -5.1288 -3.4433) (xy -5.1361 -3.4424) (xy -5.1433 -3.4411)
						(xy -5.1503 -3.4395) (xy -5.1572 -3.4375) (xy -5.1639 -3.4352) (xy -5.1705 -3.4326) (xy -5.177 -3.4297)
						(xy -5.1833 -3.4265) (xy -5.1893 -3.423) (xy -5.1952 -3.4192) (xy -5.2009 -3.4152) (xy -5.2064 -3.4108)
						(xy -5.2117 -3.4063) (xy -5.2168 -3.4015) (xy -5.2216 -3.3964) (xy -5.2262 -3.3911) (xy -5.2305 -3.3856)
						(xy -5.2345 -3.3799) (xy -5.2383 -3.374) (xy -5.2418 -3.3679) (xy -5.245 -3.3617) (xy -5.2479 -3.3552)
						(xy -5.2506 -3.3486) (xy -5.2528 -3.3419) (xy -5.2548 -3.335) (xy -5.2564 -3.3279) (xy -5.2577 -3.3208)
						(xy -5.2586 -3.3135) (xy -5.2592 -3.3061) (xy -5.2594 -3.2986) (xy -5.2594 -2.7654) (xy -5.2592 -2.758)
						(xy -5.2586 -2.7506) (xy -5.2577 -2.7433) (xy -5.2564 -2.7361) (xy -5.2548 -2.7291) (xy -5.2528 -2.7222)
						(xy -5.2506 -2.7155) (xy -5.2479 -2.7088) (xy -5.245 -2.7024) (xy -5.2418 -2.6961) (xy -5.2383 -2.69)
						(xy -5.2345 -2.6841) (xy -5.2305 -2.6784) (xy -5.2262 -2.673) (xy -5.2216 -2.6677) (xy -5.2168 -2.6626)
						(xy -5.2117 -2.6578) (xy -5.2064 -2.6532) (xy -5.2009 -2.6489) (xy -5.1952 -2.6449) (xy -5.1893 -2.6411)
						(xy -5.1833 -2.6376) (xy -5.177 -2.6344) (xy -5.1705 -2.6314) (xy -5.1639 -2.6288) (xy -5.1572 -2.6266)
						(xy -5.1503 -2.6246) (xy -5.1433 -2.623) (xy -5.1361 -2.6217) (xy -5.1288 -2.6208) (xy -5.1214 -2.6202)
						(xy -5.114 -2.62) (xy -4.5808 -2.62) (xy -4.5733 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3668 -1.4084) (xy -3.3594 -1.4089) (xy -3.3521 -1.4099) (xy -3.345 -1.4111) (xy -3.3379 -1.4128)
						(xy -3.331 -1.4147) (xy -3.3243 -1.417) (xy -3.3177 -1.4196) (xy -3.3112 -1.4225) (xy -3.305 -1.4257)
						(xy -3.2989 -1.4292) (xy -3.293 -1.433) (xy -3.2873 -1.4371) (xy -3.2818 -1.4414) (xy -3.2765 -1.446)
						(xy -3.2714 -1.4508) (xy -3.2666 -1.4558) (xy -3.2621 -1.4611) (xy -3.2577 -1.4666) (xy -3.2537 -1.4723)
						(xy -3.2499 -1.4782) (xy -3.2464 -1.4843) (xy -3.2432 -1.4906) (xy -3.2403 -1.497) (xy -3.2377 -1.5036)
						(xy -3.2354 -1.5104) (xy -3.2334 -1.5173) (xy -3.2318 -1.5243) (xy -3.2305 -1.5315) (xy -3.2296 -1.5387)
						(xy -3.229 -1.5461) (xy -3.2288 -1.5536) (xy -3.2288 -2.0868) (xy -3.229 -2.0943) (xy -3.2296 -2.1017)
						(xy -3.2305 -2.1089) (xy -3.2318 -2.1161) (xy -3.2334 -2.1231) (xy -3.2354 -2.13) (xy -3.2377 -2.1368)
						(xy -3.2403 -2.1434) (xy -3.2432 -2.1498) (xy -3.2464 -2.1561) (xy -3.2499 -2.1622) (xy -3.2537 -2.1681)
						(xy -3.2577 -2.1738) (xy -3.2621 -2.1793) (xy -3.2666 -2.1846) (xy -3.2714 -2.1896) (xy -3.2765 -2.1944)
						(xy -3.2818 -2.199) (xy -3.2873 -2.2033) (xy -3.293 -2.2074) (xy -3.2989 -2.2112) (xy -3.305 -2.2147)
						(xy -3.3112 -2.2179) (xy -3.3177 -2.2208) (xy -3.3243 -2.2234) (xy -3.331 -2.2257) (xy -3.3379 -2.2276)
						(xy -3.345 -2.2293) (xy -3.3521 -2.2306) (xy -3.3594 -2.2315) (xy -3.3668 -2.232) (xy -3.3743 -2.2322)
						(xy -3.4549 -2.2322) (xy -3.9075 -2.2322) (xy -4.5861 -2.2322) (xy -5.0226 -2.2322) (xy -5.1193 -2.2322)
						(xy -5.1268 -2.232) (xy -5.1342 -2.2315) (xy -5.1414 -2.2306) (xy -5.1486 -2.2293) (xy -5.1556 -2.2276)
						(xy -5.1625 -2.2257) (xy -5.1693 -2.2234) (xy -5.1759 -2.2208) (xy -5.1823 -2.2179) (xy -5.1886 -2.2147)
						(xy -5.1947 -2.2112) (xy -5.2006 -2.2074) (xy -5.2063 -2.2033) (xy -5.2118 -2.199) (xy -5.2171 -2.1944)
						(xy -5.2221 -2.1896) (xy -5.2269 -2.1846) (xy -5.2315 -2.1793) (xy -5.2358 -2.1738) (xy -5.2399 -2.1681)
						(xy -5.2437 -2.1622) (xy -5.2472 -2.1561) (xy -5.2504 -2.1498) (xy -5.2533 -2.1434) (xy -5.2559 -2.1368)
						(xy -5.2582 -2.13) (xy -5.2601 -2.1231) (xy -5.2618 -2.1161) (xy -5.263 -2.1089) (xy -5.264 -2.1017)
						(xy -5.2645 -2.0943) (xy -5.2647 -2.0868) (xy -5.2647 -1.5536) (xy -5.2645 -1.5461) (xy -5.264 -1.5387)
						(xy -5.263 -1.5315) (xy -5.2618 -1.5243) (xy -5.2601 -1.5173) (xy -5.2582 -1.5104) (xy -5.2559 -1.5036)
						(xy -5.2533 -1.497) (xy -5.2504 -1.4906) (xy -5.2472 -1.4843) (xy -5.2437 -1.4782) (xy -5.2399 -1.4723)
						(xy -5.2358 -1.4666) (xy -5.2315 -1.4611) (xy -5.2269 -1.4558) (xy -5.2221 -1.4508) (xy -5.2171 -1.446)
						(xy -5.2118 -1.4414) (xy -5.2063 -1.4371) (xy -5.2006 -1.433) (xy -5.1947 -1.4292) (xy -5.1886 -1.4257)
						(xy -5.1823 -1.4225) (xy -5.1759 -1.4196) (xy -5.1693 -1.417) (xy -5.1625 -1.4147) (xy -5.1556 -1.4128)
						(xy -5.1486 -1.4111) (xy -5.1414 -1.4099) (xy -5.1342 -1.4089) (xy -5.1268 -1.4084) (xy -5.1193 -1.4082)
						(xy -5.0226 -1.4082) (xy -4.5861 -1.4082) (xy -3.9075 -1.4082) (xy -3.4549 -1.4082) (xy -3.3743 -1.4082)
						(xy -3.3668 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3561 1.0153) (xy -3.3487 1.0147) (xy -3.3415 1.0138) (xy -3.3343 1.0125) (xy -3.3273 1.0109)
						(xy -3.3204 1.0089) (xy -3.3136 1.0067) (xy -3.307 1.0041) (xy -3.3006 1.0011) (xy -3.2943 0.9979)
						(xy -3.2882 0.9944) (xy -3.2823 0.9906) (xy -3.2766 0.9866) (xy -3.2711 0.9823) (xy -3.2658 0.9777)
						(xy -3.2608 0.9729) (xy -3.256 0.9678) (xy -3.2514 0.9626) (xy -3.2471 0.9571) (xy -3.243 0.9514)
						(xy -3.2392 0.9455) (xy -3.2357 0.9394) (xy -3.2325 0.9331) (xy -3.2296 0.9267) (xy -3.227 0.9201)
						(xy -3.2247 0.9133) (xy -3.2228 0.9064) (xy -3.2211 0.8994) (xy -3.2199 0.8922) (xy -3.2189 0.8849)
						(xy -3.2184 0.8776) (xy -3.2182 0.8701) (xy -3.2182 0.3369) (xy -3.2184 0.3294) (xy -3.2189 0.322)
						(xy -3.2199 0.3147) (xy -3.2211 0.3076) (xy -3.2228 0.3005) (xy -3.2247 0.2936) (xy -3.227 0.2869)
						(xy -3.2296 0.2803) (xy -3.2325 0.2738) (xy -3.2357 0.2676) (xy -3.2392 0.2615) (xy -3.243 0.2556)
						(xy -3.2471 0.2499) (xy -3.2514 0.2444) (xy -3.256 0.2391) (xy -3.2608 0.2341) (xy -3.2658 0.2292)
						(xy -3.2711 0.2247) (xy -3.2766 0.2203) (xy -3.2823 0.2163) (xy -3.2882 0.2125) (xy -3.2943 0.209)
						(xy -3.3006 0.2058) (xy -3.307 0.2029) (xy -3.3136 0.2003) (xy -3.3204 0.198) (xy -3.3273 0.196)
						(xy -3.3343 0.1944) (xy -3.3415 0.1931) (xy -3.3487 0.1922) (xy -3.3561 0.1916) (xy -3.3636 0.1914)
						(xy -3.8968 0.1914) (xy -3.9043 0.1916) (xy -3.9117 0.1922) (xy -3.9189 0.1931) (xy -3.9261 0.1944)
						(xy -3.9331 0.196) (xy -3.94 0.198) (xy -3.9468 0.2003) (xy -3.9534 0.2029) (xy -3.9598 0.2058)
						(xy -3.9661 0.209) (xy -3.9722 0.2125) (xy -3.9781 0.2163) (xy -3.9838 0.2203) (xy -3.9893 0.2247)
						(xy -3.9946 0.2292) (xy -3.9996 0.2341) (xy -4.0044 0.2391) (xy -4.009 0.2444) (xy -4.0133 0.2499)
						(xy -4.0174 0.2556) (xy -4.0212 0.2615) (xy -4.0247 0.2676) (xy -4.0279 0.2738) (xy -4.0308 0.2803)
						(xy -4.0334 0.2869) (xy -4.0357 0.2936) (xy -4.0376 0.3005) (xy -4.0393 0.3076) (xy -4.0405 0.3147)
						(xy -4.0415 0.322) (xy -4.042 0.3294) (xy -4.0422 0.3369) (xy -4.0422 0.8701) (xy -4.042 0.8776)
						(xy -4.0415 0.8849) (xy -4.0405 0.8922) (xy -4.0393 0.8994) (xy -4.0376 0.9064) (xy -4.0357 0.9133)
						(xy -4.0334 0.9201) (xy -4.0308 0.9267) (xy -4.0279 0.9331) (xy -4.0247 0.9394) (xy -4.0212 0.9455)
						(xy -4.0174 0.9514) (xy -4.0133 0.9571) (xy -4.009 0.9626) (xy -4.0044 0.9678) (xy -3.9996 0.9729)
						(xy -3.9946 0.9777) (xy -3.9893 0.9823) (xy -3.9838 0.9866) (xy -3.9781 0.9906) (xy -3.9722 0.9944)
						(xy -3.9661 0.9979) (xy -3.9598 1.0011) (xy -3.9534 1.0041) (xy -3.9468 1.0067) (xy -3.94 1.0089)
						(xy -3.9331 1.0109) (xy -3.9261 1.0125) (xy -3.9189 1.0138) (xy -3.9117 1.0147) (xy -3.9043 1.0153)
						(xy -3.8968 1.0155) (xy -3.3636 1.0155) (xy -3.3561 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3561 -0.1965) (xy -3.3487 -0.1971) (xy -3.3415 -0.198) (xy -3.3343 -0.1993) (xy -3.3273 -0.2009)
						(xy -3.3204 -0.2029) (xy -3.3136 -0.2052) (xy -3.307 -0.2078) (xy -3.3006 -0.2107) (xy -3.2943 -0.2139)
						(xy -3.2882 -0.2174) (xy -3.2823 -0.2212) (xy -3.2766 -0.2253) (xy -3.2711 -0.2296) (xy -3.2658 -0.2341)
						(xy -3.2608 -0.239) (xy -3.256 -0.244) (xy -3.2514 -0.2493) (xy -3.2471 -0.2548) (xy -3.243 -0.2605)
						(xy -3.2392 -0.2664) (xy -3.2357 -0.2725) (xy -3.2325 -0.2787) (xy -3.2296 -0.2852) (xy -3.227 -0.2918)
						(xy -3.2247 -0.2985) (xy -3.2228 -0.3054) (xy -3.2211 -0.3125) (xy -3.2199 -0.3196) (xy -3.2189 -0.3269)
						(xy -3.2184 -0.3343) (xy -3.2182 -0.3418) (xy -3.2182 -0.875) (xy -3.2184 -0.8825) (xy -3.2189 -0.8898)
						(xy -3.2199 -0.8971) (xy -3.2211 -0.9043) (xy -3.2228 -0.9113) (xy -3.2247 -0.9182) (xy -3.227 -0.925)
						(xy -3.2296 -0.9316) (xy -3.2325 -0.938) (xy -3.2357 -0.9443) (xy -3.2392 -0.9504) (xy -3.243 -0.9563)
						(xy -3.2471 -0.962) (xy -3.2514 -0.9675) (xy -3.256 -0.9727) (xy -3.2608 -0.9778) (xy -3.2658 -0.9826)
						(xy -3.2711 -0.9872) (xy -3.2766 -0.9915) (xy -3.2823 -0.9956) (xy -3.2882 -0.9993) (xy -3.2943 -1.0028)
						(xy -3.3006 -1.006) (xy -3.307 -1.009) (xy -3.3136 -1.0116) (xy -3.3204 -1.0139) (xy -3.3273 -1.0158)
						(xy -3.3343 -1.0174) (xy -3.3415 -1.0187) (xy -3.3487 -1.0196) (xy -3.3561 -1.0202) (xy -3.3636 -1.0204)
						(xy -3.8968 -1.0204) (xy -3.9043 -1.0202) (xy -3.9117 -1.0196) (xy -3.9189 -1.0187) (xy -3.9261 -1.0174)
						(xy -3.9331 -1.0158) (xy -3.94 -1.0139) (xy -3.9468 -1.0116) (xy -3.9534 -1.009) (xy -3.9598 -1.006)
						(xy -3.9661 -1.0028) (xy -3.9722 -0.9993) (xy -3.9781 -0.9956) (xy -3.9838 -0.9915) (xy -3.9893 -0.9872)
						(xy -3.9946 -0.9826) (xy -3.9996 -0.9778) (xy -4.0044 -0.9727) (xy -4.009 -0.9675) (xy -4.0133 -0.962)
						(xy -4.0174 -0.9563) (xy -4.0212 -0.9504) (xy -4.0247 -0.9443) (xy -4.0279 -0.938) (xy -4.0308 -0.9316)
						(xy -4.0334 -0.925) (xy -4.0357 -0.9182) (xy -4.0376 -0.9113) (xy -4.0393 -0.9043) (xy -4.0405 -0.8971)
						(xy -4.0415 -0.8898) (xy -4.042 -0.8825) (xy -4.0422 -0.875) (xy -4.0422 -0.3418) (xy -4.042 -0.3343)
						(xy -4.0415 -0.3269) (xy -4.0405 -0.3196) (xy -4.0393 -0.3125) (xy -4.0376 -0.3054) (xy -4.0357 -0.2985)
						(xy -4.0334 -0.2918) (xy -4.0308 -0.2852) (xy -4.0279 -0.2787) (xy -4.0247 -0.2725) (xy -4.0212 -0.2664)
						(xy -4.0174 -0.2605) (xy -4.0133 -0.2548) (xy -4.009 -0.2493) (xy -4.0044 -0.244) (xy -3.9996 -0.239)
						(xy -3.9946 -0.2341) (xy -3.9893 -0.2296) (xy -3.9838 -0.2253) (xy -3.9781 -0.2212) (xy -3.9722 -0.2174)
						(xy -3.9661 -0.2139) (xy -3.9598 -0.2107) (xy -3.9534 -0.2078) (xy -3.9468 -0.2052) (xy -3.94 -0.2029)
						(xy -3.9331 -0.2009) (xy -3.9261 -0.1993) (xy -3.9189 -0.198) (xy -3.9117 -0.1971) (xy -3.9043 -0.1965)
						(xy -3.8968 -0.1964) (xy -3.3636 -0.1964) (xy -3.3561 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3561 -2.6029) (xy -3.3487 -2.6035) (xy -3.3415 -2.6044) (xy -3.3343 -2.6057) (xy -3.3273 -2.6073)
						(xy -3.3204 -2.6093) (xy -3.3136 -2.6115) (xy -3.307 -2.6141) (xy -3.3006 -2.6171) (xy -3.2943 -2.6203)
						(xy -3.2882 -2.6238) (xy -3.2823 -2.6276) (xy -3.2766 -2.6316) (xy -3.2711 -2.6359) (xy -3.2658 -2.6405)
						(xy -3.2608 -2.6453) (xy -3.256 -2.6504) (xy -3.2514 -2.6556) (xy -3.2471 -2.6611) (xy -3.243 -2.6668)
						(xy -3.2392 -2.6727) (xy -3.2357 -2.6788) (xy -3.2325 -2.6851) (xy -3.2296 -2.6915) (xy -3.227 -2.6981)
						(xy -3.2247 -2.7049) (xy -3.2228 -2.7118) (xy -3.2211 -2.7188) (xy -3.2199 -2.726) (xy -3.2189 -2.7333)
						(xy -3.2184 -2.7406) (xy -3.2182 -2.7481) (xy -3.2182 -3.2813) (xy -3.2184 -3.2888) (xy -3.2189 -3.2962)
						(xy -3.2199 -3.3035) (xy -3.2211 -3.3106) (xy -3.2228 -3.3177) (xy -3.2247 -3.3246) (xy -3.227 -3.3313)
						(xy -3.2296 -3.3379) (xy -3.2325 -3.3444) (xy -3.2357 -3.3506) (xy -3.2392 -3.3567) (xy -3.243 -3.3626)
						(xy -3.2471 -3.3683) (xy -3.2514 -3.3738) (xy -3.256 -3.3791) (xy -3.2608 -3.3841) (xy -3.2658 -3.389)
						(xy -3.2711 -3.3935) (xy -3.2766 -3.3979) (xy -3.2823 -3.4019) (xy -3.2882 -3.4057) (xy -3.2943 -3.4092)
						(xy -3.3006 -3.4124) (xy -3.307 -3.4153) (xy -3.3136 -3.4179) (xy -3.3204 -3.4202) (xy -3.3273 -3.4222)
						(xy -3.3343 -3.4238) (xy -3.3415 -3.4251) (xy -3.3487 -3.426) (xy -3.3561 -3.4266) (xy -3.3636 -3.4268)
						(xy -3.8968 -3.4268) (xy -3.9043 -3.4266) (xy -3.9117 -3.426) (xy -3.9189 -3.4251) (xy -3.9261 -3.4238)
						(xy -3.9331 -3.4222) (xy -3.94 -3.4202) (xy -3.9468 -3.4179) (xy -3.9534 -3.4153) (xy -3.9598 -3.4124)
						(xy -3.9661 -3.4092) (xy -3.9722 -3.4057) (xy -3.9781 -3.4019) (xy -3.9838 -3.3979) (xy -3.9893 -3.3935)
						(xy -3.9946 -3.389) (xy -3.9996 -3.3841) (xy -4.0044 -3.3791) (xy -4.009 -3.3738) (xy -4.0133 -3.3683)
						(xy -4.0174 -3.3626) (xy -4.0212 -3.3567) (xy -4.0247 -3.3506) (xy -4.0279 -3.3444) (xy -4.0308 -3.3379)
						(xy -4.0334 -3.3313) (xy -4.0357 -3.3246) (xy -4.0376 -3.3177) (xy -4.0393 -3.3106) (xy -4.0405 -3.3035)
						(xy -4.0415 -3.2962) (xy -4.042 -3.2888) (xy -4.0422 -3.2813) (xy -4.0422 -2.7481) (xy -4.042 -2.7406)
						(xy -4.0415 -2.7333) (xy -4.0405 -2.726) (xy -4.0393 -2.7188) (xy -4.0376 -2.7118) (xy -4.0357 -2.7049)
						(xy -4.0334 -2.6981) (xy -4.0308 -2.6915) (xy -4.0279 -2.6851) (xy -4.0247 -2.6788) (xy -4.0212 -2.6727)
						(xy -4.0174 -2.6668) (xy -4.0133 -2.6611) (xy -4.009 -2.6556) (xy -4.0044 -2.6504) (xy -3.9996 -2.6453)
						(xy -3.9946 -2.6405) (xy -3.9893 -2.6359) (xy -3.9838 -2.6316) (xy -3.9781 -2.6276) (xy -3.9722 -2.6238)
						(xy -3.9661 -2.6203) (xy -3.9598 -2.6171) (xy -3.9534 -2.6141) (xy -3.9468 -2.6115) (xy -3.94 -2.6093)
						(xy -3.9331 -2.6073) (xy -3.9261 -2.6057) (xy -3.9189 -2.6044) (xy -3.9117 -2.6035) (xy -3.9043 -2.6029)
						(xy -3.8968 -2.6027) (xy -3.3636 -2.6027) (xy -3.3561 -2.6029)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3561 -2.6202) (xy -3.3487 -2.6208) (xy -3.3415 -2.6217) (xy -3.3343 -2.623) (xy -3.3273 -2.6246)
						(xy -3.3204 -2.6266) (xy -3.3136 -2.6288) (xy -3.307 -2.6314) (xy -3.3006 -2.6344) (xy -3.2943 -2.6376)
						(xy -3.2882 -2.6411) (xy -3.2823 -2.6449) (xy -3.2766 -2.6489) (xy -3.2711 -2.6532) (xy -3.2658 -2.6578)
						(xy -3.2608 -2.6626) (xy -3.256 -2.6677) (xy -3.2514 -2.673) (xy -3.2471 -2.6784) (xy -3.243 -2.6841)
						(xy -3.2392 -2.69) (xy -3.2357 -2.6961) (xy -3.2325 -2.7024) (xy -3.2296 -2.7088) (xy -3.227 -2.7155)
						(xy -3.2247 -2.7222) (xy -3.2228 -2.7291) (xy -3.2211 -2.7361) (xy -3.2199 -2.7433) (xy -3.2189 -2.7506)
						(xy -3.2184 -2.758) (xy -3.2182 -2.7654) (xy -3.2182 -3.2986) (xy -3.2184 -3.3061) (xy -3.2189 -3.3135)
						(xy -3.2199 -3.3208) (xy -3.2211 -3.3279) (xy -3.2228 -3.335) (xy -3.2247 -3.3419) (xy -3.227 -3.3486)
						(xy -3.2296 -3.3552) (xy -3.2325 -3.3617) (xy -3.2357 -3.3679) (xy -3.2392 -3.374) (xy -3.243 -3.3799)
						(xy -3.2471 -3.3856) (xy -3.2514 -3.3911) (xy -3.256 -3.3964) (xy -3.2608 -3.4015) (xy -3.2658 -3.4063)
						(xy -3.2711 -3.4108) (xy -3.2766 -3.4152) (xy -3.2823 -3.4192) (xy -3.2882 -3.423) (xy -3.2943 -3.4265)
						(xy -3.3006 -3.4297) (xy -3.307 -3.4326) (xy -3.3136 -3.4352) (xy -3.3204 -3.4375) (xy -3.3273 -3.4395)
						(xy -3.3343 -3.4411) (xy -3.3415 -3.4424) (xy -3.3487 -3.4433) (xy -3.3561 -3.4439) (xy -3.3636 -3.4441)
						(xy -3.8968 -3.4441) (xy -3.9043 -3.4439) (xy -3.9117 -3.4433) (xy -3.9189 -3.4424) (xy -3.9261 -3.4411)
						(xy -3.9331 -3.4395) (xy -3.94 -3.4375) (xy -3.9468 -3.4352) (xy -3.9534 -3.4326) (xy -3.9598 -3.4297)
						(xy -3.9661 -3.4265) (xy -3.9722 -3.423) (xy -3.9781 -3.4192) (xy -3.9838 -3.4152) (xy -3.9893 -3.4108)
						(xy -3.9946 -3.4063) (xy -3.9996 -3.4015) (xy -4.0044 -3.3964) (xy -4.009 -3.3911) (xy -4.0133 -3.3856)
						(xy -4.0174 -3.3799) (xy -4.0212 -3.374) (xy -4.0247 -3.3679) (xy -4.0279 -3.3617) (xy -4.0308 -3.3552)
						(xy -4.0334 -3.3486) (xy -4.0357 -3.3419) (xy -4.0376 -3.335) (xy -4.0393 -3.3279) (xy -4.0405 -3.3208)
						(xy -4.0415 -3.3135) (xy -4.042 -3.3061) (xy -4.0422 -3.2986) (xy -4.0422 -2.7654) (xy -4.042 -2.758)
						(xy -4.0415 -2.7506) (xy -4.0405 -2.7433) (xy -4.0393 -2.7361) (xy -4.0376 -2.7291) (xy -4.0357 -2.7222)
						(xy -4.0334 -2.7155) (xy -4.0308 -2.7088) (xy -4.0279 -2.7024) (xy -4.0247 -2.6961) (xy -4.0212 -2.69)
						(xy -4.0174 -2.6841) (xy -4.0133 -2.6784) (xy -4.009 -2.673) (xy -4.0044 -2.6677) (xy -3.9996 -2.6626)
						(xy -3.9946 -2.6578) (xy -3.9893 -2.6532) (xy -3.9838 -2.6489) (xy -3.9781 -2.6449) (xy -3.9722 -2.6411)
						(xy -3.9661 -2.6376) (xy -3.9598 -2.6344) (xy -3.9534 -2.6314) (xy -3.9468 -2.6288) (xy -3.94 -2.6266)
						(xy -3.9331 -2.6246) (xy -3.9261 -2.623) (xy -3.9189 -2.6217) (xy -3.9117 -2.6208) (xy -3.9043 -2.6202)
						(xy -3.8968 -2.62) (xy -3.3636 -2.62) (xy -3.3561 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -2.1496 1.0153) (xy -2.1422 1.0147) (xy -2.135 1.0138) (xy -2.1278 1.0125) (xy -2.1208 1.0109)
						(xy -2.1139 1.0089) (xy -2.1071 1.0067) (xy -2.1005 1.0041) (xy -2.0941 1.0011) (xy -2.0878 0.9979)
						(xy -2.0817 0.9944) (xy -2.0758 0.9906) (xy -2.0701 0.9866) (xy -2.0646 0.9823) (xy -2.0593 0.9777)
						(xy -2.0543 0.9729) (xy -2.0495 0.9678) (xy -2.0449 0.9626) (xy -2.0406 0.9571) (xy -2.0365 0.9514)
						(xy -2.0327 0.9455) (xy -2.0292 0.9394) (xy -2.026 0.9331) (xy -2.0231 0.9267) (xy -2.0205 0.9201)
						(xy -2.0182 0.9133) (xy -2.0163 0.9064) (xy -2.0146 0.8994) (xy -2.0133 0.8922) (xy -2.0124 0.8849)
						(xy -2.0119 0.8776) (xy -2.0117 0.8701) (xy -2.0117 0.3369) (xy -2.0119 0.3294) (xy -2.0124 0.322)
						(xy -2.0133 0.3147) (xy -2.0146 0.3076) (xy -2.0163 0.3005) (xy -2.0182 0.2936) (xy -2.0205 0.2869)
						(xy -2.0231 0.2803) (xy -2.026 0.2738) (xy -2.0292 0.2676) (xy -2.0327 0.2615) (xy -2.0365 0.2556)
						(xy -2.0406 0.2499) (xy -2.0449 0.2444) (xy -2.0495 0.2391) (xy -2.0543 0.2341) (xy -2.0593 0.2292)
						(xy -2.0646 0.2247) (xy -2.0701 0.2203) (xy -2.0758 0.2163) (xy -2.0817 0.2125) (xy -2.0878 0.209)
						(xy -2.0941 0.2058) (xy -2.1005 0.2029) (xy -2.1071 0.2003) (xy -2.1139 0.198) (xy -2.1208 0.196)
						(xy -2.1278 0.1944) (xy -2.135 0.1931) (xy -2.1422 0.1922) (xy -2.1496 0.1916) (xy -2.1571 0.1914)
						(xy -2.6903 0.1914) (xy -2.6978 0.1916) (xy -2.7052 0.1922) (xy -2.7124 0.1931) (xy -2.7196 0.1944)
						(xy -2.7266 0.196) (xy -2.7335 0.198) (xy -2.7403 0.2003) (xy -2.7469 0.2029) (xy -2.7533 0.2058)
						(xy -2.7596 0.209) (xy -2.7657 0.2125) (xy -2.7716 0.2163) (xy -2.7773 0.2203) (xy -2.7828 0.2247)
						(xy -2.7881 0.2292) (xy -2.7931 0.2341) (xy -2.7979 0.2391) (xy -2.8025 0.2444) (xy -2.8068 0.2499)
						(xy -2.8109 0.2556) (xy -2.8147 0.2615) (xy -2.8182 0.2676) (xy -2.8214 0.2738) (xy -2.8243 0.2803)
						(xy -2.8269 0.2869) (xy -2.8292 0.2936) (xy -2.8311 0.3005) (xy -2.8328 0.3076) (xy -2.834 0.3147)
						(xy -2.835 0.322) (xy -2.8355 0.3294) (xy -2.8357 0.3369) (xy -2.8357 0.8701) (xy -2.8355 0.8776)
						(xy -2.835 0.8849) (xy -2.834 0.8922) (xy -2.8328 0.8994) (xy -2.8311 0.9064) (xy -2.8292 0.9133)
						(xy -2.8269 0.9201) (xy -2.8243 0.9267) (xy -2.8214 0.9331) (xy -2.8182 0.9394) (xy -2.8147 0.9455)
						(xy -2.8109 0.9514) (xy -2.8068 0.9571) (xy -2.8025 0.9626) (xy -2.7979 0.9678) (xy -2.7931 0.9729)
						(xy -2.7881 0.9777) (xy -2.7828 0.9823) (xy -2.7773 0.9866) (xy -2.7716 0.9906) (xy -2.7657 0.9944)
						(xy -2.7596 0.9979) (xy -2.7533 1.0011) (xy -2.7469 1.0041) (xy -2.7403 1.0067) (xy -2.7335 1.0089)
						(xy -2.7266 1.0109) (xy -2.7196 1.0125) (xy -2.7124 1.0138) (xy -2.7052 1.0147) (xy -2.6978 1.0153)
						(xy -2.6903 1.0155) (xy -2.1571 1.0155) (xy -2.1496 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -2.1496 -0.1965) (xy -2.1422 -0.1971) (xy -2.135 -0.198) (xy -2.1278 -0.1993) (xy -2.1208 -0.2009)
						(xy -2.1139 -0.2029) (xy -2.1071 -0.2052) (xy -2.1005 -0.2078) (xy -2.0941 -0.2107) (xy -2.0878 -0.2139)
						(xy -2.0817 -0.2174) (xy -2.0758 -0.2212) (xy -2.0701 -0.2253) (xy -2.0646 -0.2296) (xy -2.0593 -0.2341)
						(xy -2.0543 -0.239) (xy -2.0495 -0.244) (xy -2.0449 -0.2493) (xy -2.0406 -0.2548) (xy -2.0365 -0.2605)
						(xy -2.0327 -0.2664) (xy -2.0292 -0.2725) (xy -2.026 -0.2787) (xy -2.0231 -0.2852) (xy -2.0205 -0.2918)
						(xy -2.0182 -0.2985) (xy -2.0163 -0.3054) (xy -2.0146 -0.3125) (xy -2.0133 -0.3196) (xy -2.0124 -0.3269)
						(xy -2.0119 -0.3343) (xy -2.0117 -0.3418) (xy -2.0117 -0.875) (xy -2.0119 -0.8825) (xy -2.0124 -0.8898)
						(xy -2.0133 -0.8971) (xy -2.0146 -0.9043) (xy -2.0163 -0.9113) (xy -2.0182 -0.9182) (xy -2.0205 -0.925)
						(xy -2.0231 -0.9316) (xy -2.026 -0.938) (xy -2.0292 -0.9443) (xy -2.0327 -0.9504) (xy -2.0365 -0.9563)
						(xy -2.0406 -0.962) (xy -2.0449 -0.9675) (xy -2.0495 -0.9727) (xy -2.0543 -0.9778) (xy -2.0593 -0.9826)
						(xy -2.0646 -0.9872) (xy -2.0701 -0.9915) (xy -2.0758 -0.9956) (xy -2.0817 -0.9993) (xy -2.0878 -1.0028)
						(xy -2.0941 -1.006) (xy -2.1005 -1.009) (xy -2.1071 -1.0116) (xy -2.1139 -1.0139) (xy -2.1208 -1.0158)
						(xy -2.1278 -1.0174) (xy -2.135 -1.0187) (xy -2.1422 -1.0196) (xy -2.1496 -1.0202) (xy -2.1571 -1.0204)
						(xy -2.6903 -1.0204) (xy -2.6978 -1.0202) (xy -2.7052 -1.0196) (xy -2.7124 -1.0187) (xy -2.7196 -1.0174)
						(xy -2.7266 -1.0158) (xy -2.7335 -1.0139) (xy -2.7403 -1.0116) (xy -2.7469 -1.009) (xy -2.7533 -1.006)
						(xy -2.7596 -1.0028) (xy -2.7657 -0.9993) (xy -2.7716 -0.9956) (xy -2.7773 -0.9915) (xy -2.7828 -0.9872)
						(xy -2.7881 -0.9826) (xy -2.7931 -0.9778) (xy -2.7979 -0.9727) (xy -2.8025 -0.9675) (xy -2.8068 -0.962)
						(xy -2.8109 -0.9563) (xy -2.8147 -0.9504) (xy -2.8182 -0.9443) (xy -2.8214 -0.938) (xy -2.8243 -0.9316)
						(xy -2.8269 -0.925) (xy -2.8292 -0.9182) (xy -2.8311 -0.9113) (xy -2.8328 -0.9043) (xy -2.834 -0.8971)
						(xy -2.835 -0.8898) (xy -2.8355 -0.8825) (xy -2.8357 -0.875) (xy -2.8357 -0.3418) (xy -2.8355 -0.3343)
						(xy -2.835 -0.3269) (xy -2.834 -0.3196) (xy -2.8328 -0.3125) (xy -2.8311 -0.3054) (xy -2.8292 -0.2985)
						(xy -2.8269 -0.2918) (xy -2.8243 -0.2852) (xy -2.8214 -0.2787) (xy -2.8182 -0.2725) (xy -2.8147 -0.2664)
						(xy -2.8109 -0.2605) (xy -2.8068 -0.2548) (xy -2.8025 -0.2493) (xy -2.7979 -0.244) (xy -2.7931 -0.239)
						(xy -2.7881 -0.2341) (xy -2.7828 -0.2296) (xy -2.7773 -0.2253) (xy -2.7716 -0.2212) (xy -2.7657 -0.2174)
						(xy -2.7596 -0.2139) (xy -2.7533 -0.2107) (xy -2.7469 -0.2078) (xy -2.7403 -0.2052) (xy -2.7335 -0.2029)
						(xy -2.7266 -0.2009) (xy -2.7196 -0.1993) (xy -2.7124 -0.198) (xy -2.7052 -0.1971) (xy -2.6978 -0.1965)
						(xy -2.6903 -0.1964) (xy -2.1571 -0.1964) (xy -2.1496 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -2.1496 -1.4084) (xy -2.1422 -1.4089) (xy -2.135 -1.4099) (xy -2.1278 -1.4111) (xy -2.1208 -1.4128)
						(xy -2.1139 -1.4147) (xy -2.1071 -1.417) (xy -2.1005 -1.4196) (xy -2.0941 -1.4225) (xy -2.0878 -1.4257)
						(xy -2.0817 -1.4292) (xy -2.0758 -1.433) (xy -2.0701 -1.4371) (xy -2.0646 -1.4414) (xy -2.0593 -1.446)
						(xy -2.0543 -1.4508) (xy -2.0495 -1.4558) (xy -2.0449 -1.4611) (xy -2.0406 -1.4666) (xy -2.0365 -1.4723)
						(xy -2.0327 -1.4782) (xy -2.0292 -1.4843) (xy -2.026 -1.4906) (xy -2.0231 -1.497) (xy -2.0205 -1.5036)
						(xy -2.0182 -1.5104) (xy -2.0163 -1.5173) (xy -2.0146 -1.5243) (xy -2.0133 -1.5315) (xy -2.0124 -1.5387)
						(xy -2.0119 -1.5461) (xy -2.0117 -1.5536) (xy -2.0117 -2.0868) (xy -2.0119 -2.0943) (xy -2.0124 -2.1017)
						(xy -2.0133 -2.1089) (xy -2.0146 -2.1161) (xy -2.0163 -2.1231) (xy -2.0182 -2.13) (xy -2.0205 -2.1368)
						(xy -2.0231 -2.1434) (xy -2.026 -2.1498) (xy -2.0292 -2.1561) (xy -2.0327 -2.1622) (xy -2.0365 -2.1681)
						(xy -2.0406 -2.1738) (xy -2.0449 -2.1793) (xy -2.0495 -2.1846) (xy -2.0543 -2.1896) (xy -2.0593 -2.1944)
						(xy -2.0646 -2.199) (xy -2.0701 -2.2033) (xy -2.0758 -2.2074) (xy -2.0817 -2.2112) (xy -2.0878 -2.2147)
						(xy -2.0941 -2.2179) (xy -2.1005 -2.2208) (xy -2.1071 -2.2234) (xy -2.1139 -2.2257) (xy -2.1208 -2.2276)
						(xy -2.1278 -2.2293) (xy -2.135 -2.2306) (xy -2.1422 -2.2315) (xy -2.1496 -2.232) (xy -2.1571 -2.2322)
						(xy -2.6903 -2.2322) (xy -2.6978 -2.232) (xy -2.7052 -2.2315) (xy -2.7124 -2.2306) (xy -2.7196 -2.2293)
						(xy -2.7266 -2.2276) (xy -2.7335 -2.2257) (xy -2.7403 -2.2234) (xy -2.7469 -2.2208) (xy -2.7533 -2.2179)
						(xy -2.7596 -2.2147) (xy -2.7657 -2.2112) (xy -2.7716 -2.2074) (xy -2.7773 -2.2033) (xy -2.7828 -2.199)
						(xy -2.7881 -2.1944) (xy -2.7931 -2.1896) (xy -2.7979 -2.1846) (xy -2.8025 -2.1793) (xy -2.8068 -2.1738)
						(xy -2.8109 -2.1681) (xy -2.8147 -2.1622) (xy -2.8182 -2.1561) (xy -2.8214 -2.1498) (xy -2.8243 -2.1434)
						(xy -2.8269 -2.1368) (xy -2.8292 -2.13) (xy -2.8311 -2.1231) (xy -2.8328 -2.1161) (xy -2.834 -2.1089)
						(xy -2.835 -2.1017) (xy -2.8355 -2.0943) (xy -2.8357 -2.0868) (xy -2.8357 -1.5536) (xy -2.8355 -1.5461)
						(xy -2.835 -1.5387) (xy -2.834 -1.5315) (xy -2.8328 -1.5243) (xy -2.8311 -1.5173) (xy -2.8292 -1.5104)
						(xy -2.8269 -1.5036) (xy -2.8243 -1.497) (xy -2.8214 -1.4906) (xy -2.8182 -1.4843) (xy -2.8147 -1.4782)
						(xy -2.8109 -1.4723) (xy -2.8068 -1.4666) (xy -2.8025 -1.4611) (xy -2.7979 -1.4558) (xy -2.7931 -1.4508)
						(xy -2.7881 -1.446) (xy -2.7828 -1.4414) (xy -2.7773 -1.4371) (xy -2.7716 -1.433) (xy -2.7657 -1.4292)
						(xy -2.7596 -1.4257) (xy -2.7533 -1.4225) (xy -2.7469 -1.4196) (xy -2.7403 -1.417) (xy -2.7335 -1.4147)
						(xy -2.7266 -1.4128) (xy -2.7196 -1.4111) (xy -2.7124 -1.4099) (xy -2.7052 -1.4089) (xy -2.6978 -1.4084)
						(xy -2.6903 -1.4082) (xy -2.1571 -1.4082) (xy -2.1496 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -0.9378 1.0153) (xy -0.9304 1.0147) (xy -0.9231 1.0138) (xy -0.916 1.0125) (xy -0.9089 1.0109)
						(xy -0.902 1.0089) (xy -0.8953 1.0067) (xy -0.8887 1.0041) (xy -0.8822 1.0011) (xy -0.876 0.9979)
						(xy -0.8699 0.9944) (xy -0.864 0.9906) (xy -0.8583 0.9866) (xy -0.8528 0.9823) (xy -0.8475 0.9777)
						(xy -0.8424 0.9729) (xy -0.8376 0.9678) (xy -0.8331 0.9626) (xy -0.8287 0.9571) (xy -0.8247 0.9514)
						(xy -0.8209 0.9455) (xy -0.8174 0.9394) (xy -0.8142 0.9331) (xy -0.8113 0.9267) (xy -0.8087 0.9201)
						(xy -0.8064 0.9133) (xy -0.8044 0.9064) (xy -0.8028 0.8994) (xy -0.8015 0.8922) (xy -0.8006 0.8849)
						(xy -0.8 0.8776) (xy -0.7998 0.8701) (xy -0.7998 0.3369) (xy -0.8 0.3294) (xy -0.8006 0.322) (xy -0.8015 0.3147)
						(xy -0.8028 0.3076) (xy -0.8044 0.3005) (xy -0.8064 0.2936) (xy -0.8087 0.2869) (xy -0.8113 0.2803)
						(xy -0.8142 0.2738) (xy -0.8174 0.2676) (xy -0.8209 0.2615) (xy -0.8247 0.2556) (xy -0.8287 0.2499)
						(xy -0.8331 0.2444) (xy -0.8376 0.2391) (xy -0.8424 0.2341) (xy -0.8475 0.2292) (xy -0.8528 0.2247)
						(xy -0.8583 0.2203) (xy -0.864 0.2163) (xy -0.8699 0.2125) (xy -0.876 0.209) (xy -0.8822 0.2058)
						(xy -0.8887 0.2029) (xy -0.8953 0.2003) (xy -0.902 0.198) (xy -0.9089 0.196) (xy -0.916 0.1944)
						(xy -0.9231 0.1931) (xy -0.9304 0.1922) (xy -0.9378 0.1916) (xy -0.9453 0.1914) (xy -1.4785 0.1914)
						(xy -1.4859 0.1916) (xy -1.4933 0.1922) (xy -1.5006 0.1931) (xy -1.5078 0.1944) (xy -1.5148 0.196)
						(xy -1.5217 0.198) (xy -1.5284 0.2003) (xy -1.5351 0.2029) (xy -1.5415 0.2058) (xy -1.5478 0.209)
						(xy -1.5539 0.2125) (xy -1.5598 0.2163) (xy -1.5655 0.2203) (xy -1.5709 0.2247) (xy -1.5762 0.2292)
						(xy -1.5813 0.2341) (xy -1.5861 0.2391) (xy -1.5907 0.2444) (xy -1.595 0.2499) (xy -1.599 0.2556)
						(xy -1.6028 0.2615) (xy -1.6063 0.2676) (xy -1.6095 0.2738) (xy -1.6125 0.2803) (xy -1.6151 0.2869)
						(xy -1.6173 0.2936) (xy -1.6193 0.3005) (xy -1.6209 0.3076) (xy -1.6222 0.3147) (xy -1.6231 0.322)
						(xy -1.6237 0.3294) (xy -1.6239 0.3369) (xy -1.6239 0.8701) (xy -1.6237 0.8776) (xy -1.6231 0.8849)
						(xy -1.6222 0.8922) (xy -1.6209 0.8994) (xy -1.6193 0.9064) (xy -1.6173 0.9133) (xy -1.6151 0.9201)
						(xy -1.6125 0.9267) (xy -1.6095 0.9331) (xy -1.6063 0.9394) (xy -1.6028 0.9455) (xy -1.599 0.9514)
						(xy -1.595 0.9571) (xy -1.5907 0.9626) (xy -1.5861 0.9678) (xy -1.5813 0.9729) (xy -1.5762 0.9777)
						(xy -1.5709 0.9823) (xy -1.5655 0.9866) (xy -1.5598 0.9906) (xy -1.5539 0.9944) (xy -1.5478 0.9979)
						(xy -1.5415 1.0011) (xy -1.5351 1.0041) (xy -1.5284 1.0067) (xy -1.5217 1.0089) (xy -1.5148 1.0109)
						(xy -1.5078 1.0125) (xy -1.5006 1.0138) (xy -1.4933 1.0147) (xy -1.4859 1.0153) (xy -1.4785 1.0155)
						(xy -0.9453 1.0155) (xy -0.9378 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -0.9378 -0.1965) (xy -0.9304 -0.1971) (xy -0.9231 -0.198) (xy -0.916 -0.1993) (xy -0.9089 -0.2009)
						(xy -0.902 -0.2029) (xy -0.8953 -0.2052) (xy -0.8887 -0.2078) (xy -0.8822 -0.2107) (xy -0.876 -0.2139)
						(xy -0.8699 -0.2174) (xy -0.864 -0.2212) (xy -0.8583 -0.2253) (xy -0.8528 -0.2296) (xy -0.8475 -0.2341)
						(xy -0.8424 -0.239) (xy -0.8376 -0.244) (xy -0.8331 -0.2493) (xy -0.8287 -0.2548) (xy -0.8247 -0.2605)
						(xy -0.8209 -0.2664) (xy -0.8174 -0.2725) (xy -0.8142 -0.2787) (xy -0.8113 -0.2852) (xy -0.8087 -0.2918)
						(xy -0.8064 -0.2985) (xy -0.8044 -0.3054) (xy -0.8028 -0.3125) (xy -0.8015 -0.3196) (xy -0.8006 -0.3269)
						(xy -0.8 -0.3343) (xy -0.7998 -0.3418) (xy -0.7998 -0.875) (xy -0.8 -0.8825) (xy -0.8006 -0.8898)
						(xy -0.8015 -0.8971) (xy -0.8028 -0.9043) (xy -0.8044 -0.9113) (xy -0.8064 -0.9182) (xy -0.8087 -0.925)
						(xy -0.8113 -0.9316) (xy -0.8142 -0.938) (xy -0.8174 -0.9443) (xy -0.8209 -0.9504) (xy -0.8247 -0.9563)
						(xy -0.8287 -0.962) (xy -0.8331 -0.9675) (xy -0.8376 -0.9727) (xy -0.8424 -0.9778) (xy -0.8475 -0.9826)
						(xy -0.8528 -0.9872) (xy -0.8583 -0.9915) (xy -0.864 -0.9956) (xy -0.8699 -0.9993) (xy -0.876 -1.0028)
						(xy -0.8822 -1.006) (xy -0.8887 -1.009) (xy -0.8953 -1.0116) (xy -0.902 -1.0139) (xy -0.9089 -1.0158)
						(xy -0.916 -1.0174) (xy -0.9231 -1.0187) (xy -0.9304 -1.0196) (xy -0.9378 -1.0202) (xy -0.9453 -1.0204)
						(xy -1.4785 -1.0204) (xy -1.4859 -1.0202) (xy -1.4933 -1.0196) (xy -1.5006 -1.0187) (xy -1.5078 -1.0174)
						(xy -1.5148 -1.0158) (xy -1.5217 -1.0139) (xy -1.5284 -1.0116) (xy -1.5351 -1.009) (xy -1.5415 -1.006)
						(xy -1.5478 -1.0028) (xy -1.5539 -0.9993) (xy -1.5598 -0.9956) (xy -1.5655 -0.9915) (xy -1.5709 -0.9872)
						(xy -1.5762 -0.9826) (xy -1.5813 -0.9778) (xy -1.5861 -0.9727) (xy -1.5907 -0.9675) (xy -1.595 -0.962)
						(xy -1.599 -0.9563) (xy -1.6028 -0.9504) (xy -1.6063 -0.9443) (xy -1.6095 -0.938) (xy -1.6125 -0.9316)
						(xy -1.6151 -0.925) (xy -1.6173 -0.9182) (xy -1.6193 -0.9113) (xy -1.6209 -0.9043) (xy -1.6222 -0.8971)
						(xy -1.6231 -0.8898) (xy -1.6237 -0.8825) (xy -1.6239 -0.875) (xy -1.6239 -0.3418) (xy -1.6237 -0.3343)
						(xy -1.6231 -0.3269) (xy -1.6222 -0.3196) (xy -1.6209 -0.3125) (xy -1.6193 -0.3054) (xy -1.6173 -0.2985)
						(xy -1.6151 -0.2918) (xy -1.6125 -0.2852) (xy -1.6095 -0.2787) (xy -1.6063 -0.2725) (xy -1.6028 -0.2664)
						(xy -1.599 -0.2605) (xy -1.595 -0.2548) (xy -1.5907 -0.2493) (xy -1.5861 -0.244) (xy -1.5813 -0.239)
						(xy -1.5762 -0.2341) (xy -1.5709 -0.2296) (xy -1.5655 -0.2253) (xy -1.5598 -0.2212) (xy -1.5539 -0.2174)
						(xy -1.5478 -0.2139) (xy -1.5415 -0.2107) (xy -1.5351 -0.2078) (xy -1.5284 -0.2052) (xy -1.5217 -0.2029)
						(xy -1.5148 -0.2009) (xy -1.5078 -0.1993) (xy -1.5006 -0.198) (xy -1.4933 -0.1971) (xy -1.4859 -0.1965)
						(xy -1.4785 -0.1964) (xy -0.9453 -0.1964) (xy -0.9378 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -0.9378 -1.4084) (xy -0.9304 -1.4089) (xy -0.9231 -1.4099) (xy -0.916 -1.4111) (xy -0.9089 -1.4128)
						(xy -0.902 -1.4147) (xy -0.8953 -1.417) (xy -0.8887 -1.4196) (xy -0.8822 -1.4225) (xy -0.876 -1.4257)
						(xy -0.8699 -1.4292) (xy -0.864 -1.433) (xy -0.8583 -1.4371) (xy -0.8528 -1.4414) (xy -0.8475 -1.446)
						(xy -0.8424 -1.4508) (xy -0.8376 -1.4558) (xy -0.8331 -1.4611) (xy -0.8287 -1.4666) (xy -0.8247 -1.4723)
						(xy -0.8209 -1.4782) (xy -0.8174 -1.4843) (xy -0.8142 -1.4906) (xy -0.8113 -1.497) (xy -0.8087 -1.5036)
						(xy -0.8064 -1.5104) (xy -0.8044 -1.5173) (xy -0.8028 -1.5243) (xy -0.8015 -1.5315) (xy -0.8006 -1.5387)
						(xy -0.8 -1.5461) (xy -0.7998 -1.5536) (xy -0.7998 -2.0868) (xy -0.8 -2.0943) (xy -0.8006 -2.1017)
						(xy -0.8015 -2.1089) (xy -0.8028 -2.1161) (xy -0.8044 -2.1231) (xy -0.8064 -2.13) (xy -0.8087 -2.1368)
						(xy -0.8113 -2.1434) (xy -0.8142 -2.1498) (xy -0.8174 -2.1561) (xy -0.8209 -2.1622) (xy -0.8247 -2.1681)
						(xy -0.8287 -2.1738) (xy -0.8331 -2.1793) (xy -0.8376 -2.1846) (xy -0.8424 -2.1896) (xy -0.8475 -2.1944)
						(xy -0.8528 -2.199) (xy -0.8583 -2.2033) (xy -0.864 -2.2074) (xy -0.8699 -2.2112) (xy -0.876 -2.2147)
						(xy -0.8822 -2.2179) (xy -0.8887 -2.2208) (xy -0.8953 -2.2234) (xy -0.902 -2.2257) (xy -0.9089 -2.2276)
						(xy -0.916 -2.2293) (xy -0.9231 -2.2306) (xy -0.9304 -2.2315) (xy -0.9378 -2.232) (xy -0.9453 -2.2322)
						(xy -1.4785 -2.2322) (xy -1.4859 -2.232) (xy -1.4933 -2.2315) (xy -1.5006 -2.2306) (xy -1.5078 -2.2293)
						(xy -1.5148 -2.2276) (xy -1.5217 -2.2257) (xy -1.5284 -2.2234) (xy -1.5351 -2.2208) (xy -1.5415 -2.2179)
						(xy -1.5478 -2.2147) (xy -1.5539 -2.2112) (xy -1.5598 -2.2074) (xy -1.5655 -2.2033) (xy -1.5709 -2.199)
						(xy -1.5762 -2.1944) (xy -1.5813 -2.1896) (xy -1.5861 -2.1846) (xy -1.5907 -2.1793) (xy -1.595 -2.1738)
						(xy -1.599 -2.1681) (xy -1.6028 -2.1622) (xy -1.6063 -2.1561) (xy -1.6095 -2.1498) (xy -1.6125 -2.1434)
						(xy -1.6151 -2.1368) (xy -1.6173 -2.13) (xy -1.6193 -2.1231) (xy -1.6209 -2.1161) (xy -1.6222 -2.1089)
						(xy -1.6231 -2.1017) (xy -1.6237 -2.0943) (xy -1.6239 -2.0868) (xy -1.6239 -1.5536) (xy -1.6237 -1.5461)
						(xy -1.6231 -1.5387) (xy -1.6222 -1.5315) (xy -1.6209 -1.5243) (xy -1.6193 -1.5173) (xy -1.6173 -1.5104)
						(xy -1.6151 -1.5036) (xy -1.6125 -1.497) (xy -1.6095 -1.4906) (xy -1.6063 -1.4843) (xy -1.6028 -1.4782)
						(xy -1.599 -1.4723) (xy -1.595 -1.4666) (xy -1.5907 -1.4611) (xy -1.5861 -1.4558) (xy -1.5813 -1.4508)
						(xy -1.5762 -1.446) (xy -1.5709 -1.4414) (xy -1.5655 -1.4371) (xy -1.5598 -1.433) (xy -1.5539 -1.4292)
						(xy -1.5478 -1.4257) (xy -1.5415 -1.4225) (xy -1.5351 -1.4196) (xy -1.5284 -1.417) (xy -1.5217 -1.4147)
						(xy -1.5148 -1.4128) (xy -1.5078 -1.4111) (xy -1.5006 -1.4099) (xy -1.4933 -1.4089) (xy -1.4859 -1.4084)
						(xy -1.4785 -1.4082) (xy -0.9453 -1.4082) (xy -0.9378 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0.2687 1.0153) (xy 0.2761 1.0147) (xy 0.2834 1.0138) (xy 0.2905 1.0125) (xy 0.2976 1.0109)
						(xy 0.3045 1.0089) (xy 0.3112 1.0067) (xy 0.3178 1.0041) (xy 0.3243 1.0011) (xy 0.3305 0.9979)
						(xy 0.3366 0.9944) (xy 0.3425 0.9906) (xy 0.3482 0.9866) (xy 0.3537 0.9823) (xy 0.359 0.9777)
						(xy 0.3641 0.9729) (xy 0.3689 0.9678) (xy 0.3734 0.9626) (xy 0.3778 0.9571) (xy 0.3818 0.9514)
						(xy 0.3856 0.9455) (xy 0.3891 0.9394) (xy 0.3923 0.9331) (xy 0.3952 0.9267) (xy 0.3978 0.9201)
						(xy 0.4001 0.9133) (xy 0.4021 0.9064) (xy 0.4037 0.8994) (xy 0.405 0.8922) (xy 0.4059 0.8849)
						(xy 0.4065 0.8776) (xy 0.4067 0.8701) (xy 0.4067 0.3369) (xy 0.4065 0.3294) (xy 0.4059 0.322)
						(xy 0.405 0.3147) (xy 0.4037 0.3076) (xy 0.4021 0.3005) (xy 0.4001 0.2936) (xy 0.3978 0.2869)
						(xy 0.3952 0.2803) (xy 0.3923 0.2738) (xy 0.3891 0.2676) (xy 0.3856 0.2615) (xy 0.3818 0.2556)
						(xy 0.3778 0.2499) (xy 0.3734 0.2444) (xy 0.3689 0.2391) (xy 0.3641 0.2341) (xy 0.359 0.2292)
						(xy 0.3537 0.2247) (xy 0.3482 0.2203) (xy 0.3425 0.2163) (xy 0.3366 0.2125) (xy 0.3305 0.209)
						(xy 0.3243 0.2058) (xy 0.3178 0.2029) (xy 0.3112 0.2003) (xy 0.3045 0.198) (xy 0.2976 0.196) (xy 0.2905 0.1944)
						(xy 0.2834 0.1931) (xy 0.2761 0.1922) (xy 0.2687 0.1916) (xy 0.2612 0.1914) (xy -0.272 0.1914)
						(xy -0.2794 0.1916) (xy -0.2868 0.1922) (xy -0.2941 0.1931) (xy -0.3013 0.1944) (xy -0.3083 0.196)
						(xy -0.3152 0.198) (xy -0.3219 0.2003) (xy -0.3286 0.2029) (xy -0.335 0.2058) (xy -0.3413 0.209)
						(xy -0.3474 0.2125) (xy -0.3533 0.2163) (xy -0.359 0.2203) (xy -0.3644 0.2247) (xy -0.3697 0.2292)
						(xy -0.3748 0.2341) (xy -0.3796 0.2391) (xy -0.3842 0.2444) (xy -0.3885 0.2499) (xy -0.3925 0.2556)
						(xy -0.3963 0.2615) (xy -0.3998 0.2676) (xy -0.403 0.2738) (xy -0.406 0.2803) (xy -0.4086 0.2869)
						(xy -0.4108 0.2936) (xy -0.4128 0.3005) (xy -0.4144 0.3076) (xy -0.4157 0.3147) (xy -0.4166 0.322)
						(xy -0.4172 0.3294) (xy -0.4174 0.3369) (xy -0.4174 0.8701) (xy -0.4172 0.8776) (xy -0.4166 0.8849)
						(xy -0.4157 0.8922) (xy -0.4144 0.8994) (xy -0.4128 0.9064) (xy -0.4108 0.9133) (xy -0.4086 0.9201)
						(xy -0.406 0.9267) (xy -0.403 0.9331) (xy -0.3998 0.9394) (xy -0.3963 0.9455) (xy -0.3925 0.9514)
						(xy -0.3885 0.9571) (xy -0.3842 0.9626) (xy -0.3796 0.9678) (xy -0.3748 0.9729) (xy -0.3697 0.9777)
						(xy -0.3644 0.9823) (xy -0.359 0.9866) (xy -0.3533 0.9906) (xy -0.3474 0.9944) (xy -0.3413 0.9979)
						(xy -0.335 1.0011) (xy -0.3286 1.0041) (xy -0.3219 1.0067) (xy -0.3152 1.0089) (xy -0.3083 1.0109)
						(xy -0.3013 1.0125) (xy -0.2941 1.0138) (xy -0.2868 1.0147) (xy -0.2794 1.0153) (xy -0.272 1.0155)
						(xy 0.2612 1.0155) (xy 0.2687 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0.2687 -0.1965) (xy 0.2761 -0.1971) (xy 0.2834 -0.198) (xy 0.2905 -0.1993) (xy 0.2976 -0.2009)
						(xy 0.3045 -0.2029) (xy 0.3112 -0.2052) (xy 0.3178 -0.2078) (xy 0.3243 -0.2107) (xy 0.3305 -0.2139)
						(xy 0.3366 -0.2174) (xy 0.3425 -0.2212) (xy 0.3482 -0.2253) (xy 0.3537 -0.2296) (xy 0.359 -0.2341)
						(xy 0.3641 -0.239) (xy 0.3689 -0.244) (xy 0.3734 -0.2493) (xy 0.3778 -0.2548) (xy 0.3818 -0.2605)
						(xy 0.3856 -0.2664) (xy 0.3891 -0.2725) (xy 0.3923 -0.2787) (xy 0.3952 -0.2852) (xy 0.3978 -0.2918)
						(xy 0.4001 -0.2985) (xy 0.4021 -0.3054) (xy 0.4037 -0.3125) (xy 0.405 -0.3196) (xy 0.4059 -0.3269)
						(xy 0.4065 -0.3343) (xy 0.4067 -0.3418) (xy 0.4067 -0.875) (xy 0.4065 -0.8825) (xy 0.4059 -0.8898)
						(xy 0.405 -0.8971) (xy 0.4037 -0.9043) (xy 0.4021 -0.9113) (xy 0.4001 -0.9182) (xy 0.3978 -0.925)
						(xy 0.3952 -0.9316) (xy 0.3923 -0.938) (xy 0.3891 -0.9443) (xy 0.3856 -0.9504) (xy 0.3818 -0.9563)
						(xy 0.3778 -0.962) (xy 0.3734 -0.9675) (xy 0.3689 -0.9727) (xy 0.3641 -0.9778) (xy 0.359 -0.9826)
						(xy 0.3537 -0.9872) (xy 0.3482 -0.9915) (xy 0.3425 -0.9956) (xy 0.3366 -0.9993) (xy 0.3305 -1.0028)
						(xy 0.3243 -1.006) (xy 0.3178 -1.009) (xy 0.3112 -1.0116) (xy 0.3045 -1.0139) (xy 0.2976 -1.0158)
						(xy 0.2905 -1.0174) (xy 0.2834 -1.0187) (xy 0.2761 -1.0196) (xy 0.2687 -1.0202) (xy 0.2612 -1.0204)
						(xy -0.272 -1.0204) (xy -0.2794 -1.0202) (xy -0.2868 -1.0196) (xy -0.2941 -1.0187) (xy -0.3013 -1.0174)
						(xy -0.3083 -1.0158) (xy -0.3152 -1.0139) (xy -0.3219 -1.0116) (xy -0.3286 -1.009) (xy -0.335 -1.006)
						(xy -0.3413 -1.0028) (xy -0.3474 -0.9993) (xy -0.3533 -0.9956) (xy -0.359 -0.9915) (xy -0.3644 -0.9872)
						(xy -0.3697 -0.9826) (xy -0.3748 -0.9778) (xy -0.3796 -0.9727) (xy -0.3842 -0.9675) (xy -0.3885 -0.962)
						(xy -0.3925 -0.9563) (xy -0.3963 -0.9504) (xy -0.3998 -0.9443) (xy -0.403 -0.938) (xy -0.406 -0.9316)
						(xy -0.4086 -0.925) (xy -0.4108 -0.9182) (xy -0.4128 -0.9113) (xy -0.4144 -0.9043) (xy -0.4157 -0.8971)
						(xy -0.4166 -0.8898) (xy -0.4172 -0.8825) (xy -0.4174 -0.875) (xy -0.4174 -0.3418) (xy -0.4172 -0.3343)
						(xy -0.4166 -0.3269) (xy -0.4157 -0.3196) (xy -0.4144 -0.3125) (xy -0.4128 -0.3054) (xy -0.4108 -0.2985)
						(xy -0.4086 -0.2918) (xy -0.406 -0.2852) (xy -0.403 -0.2787) (xy -0.3998 -0.2725) (xy -0.3963 -0.2664)
						(xy -0.3925 -0.2605) (xy -0.3885 -0.2548) (xy -0.3842 -0.2493) (xy -0.3796 -0.244) (xy -0.3748 -0.239)
						(xy -0.3697 -0.2341) (xy -0.3644 -0.2296) (xy -0.359 -0.2253) (xy -0.3533 -0.2212) (xy -0.3474 -0.2174)
						(xy -0.3413 -0.2139) (xy -0.335 -0.2107) (xy -0.3286 -0.2078) (xy -0.3219 -0.2052) (xy -0.3152 -0.2029)
						(xy -0.3083 -0.2009) (xy -0.3013 -0.1993) (xy -0.2941 -0.198) (xy -0.2868 -0.1971) (xy -0.2794 -0.1965)
						(xy -0.272 -0.1964) (xy 0.2612 -0.1964) (xy 0.2687 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0.2687 -1.4084) (xy 0.2761 -1.4089) (xy 0.2834 -1.4099) (xy 0.2905 -1.4111) (xy 0.2976 -1.4128)
						(xy 0.3045 -1.4147) (xy 0.3112 -1.417) (xy 0.3178 -1.4196) (xy 0.3243 -1.4225) (xy 0.3305 -1.4257)
						(xy 0.3366 -1.4292) (xy 0.3425 -1.433) (xy 0.3482 -1.4371) (xy 0.3537 -1.4414) (xy 0.359 -1.446)
						(xy 0.3641 -1.4508) (xy 0.3689 -1.4558) (xy 0.3734 -1.4611) (xy 0.3778 -1.4666) (xy 0.3818 -1.4723)
						(xy 0.3856 -1.4782) (xy 0.3891 -1.4843) (xy 0.3923 -1.4906) (xy 0.3952 -1.497) (xy 0.3978 -1.5036)
						(xy 0.4001 -1.5104) (xy 0.4021 -1.5173) (xy 0.4037 -1.5243) (xy 0.405 -1.5315) (xy 0.4059 -1.5387)
						(xy 0.4065 -1.5461) (xy 0.4067 -1.5536) (xy 0.4067 -2.0868) (xy 0.4065 -2.0943) (xy 0.4059 -2.1017)
						(xy 0.405 -2.1089) (xy 0.4037 -2.1161) (xy 0.4021 -2.1231) (xy 0.4001 -2.13) (xy 0.3978 -2.1368)
						(xy 0.3952 -2.1434) (xy 0.3923 -2.1498) (xy 0.3891 -2.1561) (xy 0.3856 -2.1622) (xy 0.3818 -2.1681)
						(xy 0.3778 -2.1738) (xy 0.3734 -2.1793) (xy 0.3689 -2.1846) (xy 0.3641 -2.1896) (xy 0.359 -2.1944)
						(xy 0.3537 -2.199) (xy 0.3482 -2.2033) (xy 0.3425 -2.2074) (xy 0.3366 -2.2112) (xy 0.3305 -2.2147)
						(xy 0.3243 -2.2179) (xy 0.3178 -2.2208) (xy 0.3112 -2.2234) (xy 0.3045 -2.2257) (xy 0.2976 -2.2276)
						(xy 0.2905 -2.2293) (xy 0.2834 -2.2306) (xy 0.2761 -2.2315) (xy 0.2687 -2.232) (xy 0.2612 -2.2322)
						(xy -0.272 -2.2322) (xy -0.2794 -2.232) (xy -0.2868 -2.2315) (xy -0.2941 -2.2306) (xy -0.3013 -2.2293)
						(xy -0.3083 -2.2276) (xy -0.3152 -2.2257) (xy -0.3219 -2.2234) (xy -0.3286 -2.2208) (xy -0.335 -2.2179)
						(xy -0.3413 -2.2147) (xy -0.3474 -2.2112) (xy -0.3533 -2.2074) (xy -0.359 -2.2033) (xy -0.3644 -2.199)
						(xy -0.3697 -2.1944) (xy -0.3748 -2.1896) (xy -0.3796 -2.1846) (xy -0.3842 -2.1793) (xy -0.3885 -2.1738)
						(xy -0.3925 -2.1681) (xy -0.3963 -2.1622) (xy -0.3998 -2.1561) (xy -0.403 -2.1498) (xy -0.406 -2.1434)
						(xy -0.4086 -2.1368) (xy -0.4108 -2.13) (xy -0.4128 -2.1231) (xy -0.4144 -2.1161) (xy -0.4157 -2.1089)
						(xy -0.4166 -2.1017) (xy -0.4172 -2.0943) (xy -0.4174 -2.0868) (xy -0.4174 -1.5536) (xy -0.4172 -1.5461)
						(xy -0.4166 -1.5387) (xy -0.4157 -1.5315) (xy -0.4144 -1.5243) (xy -0.4128 -1.5173) (xy -0.4108 -1.5104)
						(xy -0.4086 -1.5036) (xy -0.406 -1.497) (xy -0.403 -1.4906) (xy -0.3998 -1.4843) (xy -0.3963 -1.4782)
						(xy -0.3925 -1.4723) (xy -0.3885 -1.4666) (xy -0.3842 -1.4611) (xy -0.3796 -1.4558) (xy -0.3748 -1.4508)
						(xy -0.3697 -1.446) (xy -0.3644 -1.4414) (xy -0.359 -1.4371) (xy -0.3533 -1.433) (xy -0.3474 -1.4292)
						(xy -0.3413 -1.4257) (xy -0.335 -1.4225) (xy -0.3286 -1.4196) (xy -0.3219 -1.417) (xy -0.3152 -1.4147)
						(xy -0.3083 -1.4128) (xy -0.3013 -1.4111) (xy -0.2941 -1.4099) (xy -0.2868 -1.4089) (xy -0.2794 -1.4084)
						(xy -0.272 -1.4082) (xy 0.2612 -1.4082) (xy 0.2687 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 1.4859 1.0153) (xy 1.4933 1.0147) (xy 1.5005 1.0138) (xy 1.5077 1.0125) (xy 1.5147 1.0109)
						(xy 1.5216 1.0089) (xy 1.5284 1.0067) (xy 1.535 1.0041) (xy 1.5414 1.0011) (xy 1.5477 0.9979)
						(xy 1.5538 0.9944) (xy 1.5597 0.9906) (xy 1.5654 0.9866) (xy 1.5709 0.9823) (xy 1.5762 0.9777)
						(xy 1.5812 0.9729) (xy 1.586 0.9678) (xy 1.5906 0.9626) (xy 1.5949 0.9571) (xy 1.599 0.9514) (xy 1.6028 0.9455)
						(xy 1.6063 0.9394) (xy 1.6095 0.9331) (xy 1.6124 0.9267) (xy 1.615 0.9201) (xy 1.6173 0.9133)
						(xy 1.6192 0.9064) (xy 1.6209 0.8994) (xy 1.6221 0.8922) (xy 1.6231 0.8849) (xy 1.6236 0.8776)
						(xy 1.6238 0.8701) (xy 1.6238 0.3369) (xy 1.6236 0.3294) (xy 1.6231 0.322) (xy 1.6221 0.3147)
						(xy 1.6209 0.3076) (xy 1.6192 0.3005) (xy 1.6173 0.2936) (xy 1.615 0.2869) (xy 1.6124 0.2803)
						(xy 1.6095 0.2738) (xy 1.6063 0.2676) (xy 1.6028 0.2615) (xy 1.599 0.2556) (xy 1.5949 0.2499)
						(xy 1.5906 0.2444) (xy 1.586 0.2391) (xy 1.5812 0.2341) (xy 1.5762 0.2292) (xy 1.5709 0.2247)
						(xy 1.5654 0.2203) (xy 1.5597 0.2163) (xy 1.5538 0.2125) (xy 1.5477 0.209) (xy 1.5414 0.2058)
						(xy 1.535 0.2029) (xy 1.5284 0.2003) (xy 1.5216 0.198) (xy 1.5147 0.196) (xy 1.5077 0.1944) (xy 1.5005 0.1931)
						(xy 1.4933 0.1922) (xy 1.4859 0.1916) (xy 1.4784 0.1914) (xy 0.9452 0.1914) (xy 0.9377 0.1916)
						(xy 0.9303 0.1922) (xy 0.9231 0.1931) (xy 0.9159 0.1944) (xy 0.9089 0.196) (xy 0.902 0.198) (xy 0.8952 0.2003)
						(xy 0.8886 0.2029) (xy 0.8822 0.2058) (xy 0.8759 0.209) (xy 0.8698 0.2125) (xy 0.8639 0.2163)
						(xy 0.8582 0.2203) (xy 0.8527 0.2247) (xy 0.8474 0.2292) (xy 0.8424 0.2341) (xy 0.8376 0.2391)
						(xy 0.833 0.2444) (xy 0.8287 0.2499) (xy 0.8246 0.2556) (xy 0.8208 0.2615) (xy 0.8173 0.2676)
						(xy 0.8141 0.2738) (xy 0.8112 0.2803) (xy 0.8086 0.2869) (xy 0.8063 0.2936) (xy 0.8044 0.3005)
						(xy 0.8027 0.3076) (xy 0.8015 0.3147) (xy 0.8005 0.322) (xy 0.8 0.3294) (xy 0.7998 0.3369) (xy 0.7998 0.8701)
						(xy 0.8 0.8776) (xy 0.8005 0.8849) (xy 0.8015 0.8922) (xy 0.8027 0.8994) (xy 0.8044 0.9064) (xy 0.8063 0.9133)
						(xy 0.8086 0.9201) (xy 0.8112 0.9267) (xy 0.8141 0.9331) (xy 0.8173 0.9394) (xy 0.8208 0.9455)
						(xy 0.8246 0.9514) (xy 0.8287 0.9571) (xy 0.833 0.9626) (xy 0.8376 0.9678) (xy 0.8424 0.9729)
						(xy 0.8474 0.9777) (xy 0.8527 0.9823) (xy 0.8582 0.9866) (xy 0.8639 0.9906) (xy 0.8698 0.9944)
						(xy 0.8759 0.9979) (xy 0.8822 1.0011) (xy 0.8886 1.0041) (xy 0.8952 1.0067) (xy 0.902 1.0089)
						(xy 0.9089 1.0109) (xy 0.9159 1.0125) (xy 0.9231 1.0138) (xy 0.9303 1.0147) (xy 0.9377 1.0153)
						(xy 0.9452 1.0155) (xy 1.4784 1.0155) (xy 1.4859 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 1.4859 -0.1965) (xy 1.4933 -0.1971) (xy 1.5005 -0.198) (xy 1.5077 -0.1993) (xy 1.5147 -0.2009)
						(xy 1.5216 -0.2029) (xy 1.5284 -0.2052) (xy 1.535 -0.2078) (xy 1.5414 -0.2107) (xy 1.5477 -0.2139)
						(xy 1.5538 -0.2174) (xy 1.5597 -0.2212) (xy 1.5654 -0.2253) (xy 1.5709 -0.2296) (xy 1.5762 -0.2341)
						(xy 1.5812 -0.239) (xy 1.586 -0.244) (xy 1.5906 -0.2493) (xy 1.5949 -0.2548) (xy 1.599 -0.2605)
						(xy 1.6028 -0.2664) (xy 1.6063 -0.2725) (xy 1.6095 -0.2787) (xy 1.6124 -0.2852) (xy 1.615 -0.2918)
						(xy 1.6173 -0.2985) (xy 1.6192 -0.3054) (xy 1.6209 -0.3125) (xy 1.6221 -0.3196) (xy 1.6231 -0.3269)
						(xy 1.6236 -0.3343) (xy 1.6238 -0.3418) (xy 1.6238 -0.875) (xy 1.6236 -0.8825) (xy 1.6231 -0.8898)
						(xy 1.6221 -0.8971) (xy 1.6209 -0.9043) (xy 1.6192 -0.9113) (xy 1.6173 -0.9182) (xy 1.615 -0.925)
						(xy 1.6124 -0.9316) (xy 1.6095 -0.938) (xy 1.6063 -0.9443) (xy 1.6028 -0.9504) (xy 1.599 -0.9563)
						(xy 1.5949 -0.962) (xy 1.5906 -0.9675) (xy 1.586 -0.9727) (xy 1.5812 -0.9778) (xy 1.5762 -0.9826)
						(xy 1.5709 -0.9872) (xy 1.5654 -0.9915) (xy 1.5597 -0.9956) (xy 1.5538 -0.9993) (xy 1.5477 -1.0028)
						(xy 1.5414 -1.006) (xy 1.535 -1.009) (xy 1.5284 -1.0116) (xy 1.5216 -1.0139) (xy 1.5147 -1.0158)
						(xy 1.5077 -1.0174) (xy 1.5005 -1.0187) (xy 1.4933 -1.0196) (xy 1.4859 -1.0202) (xy 1.4784 -1.0204)
						(xy 0.9452 -1.0204) (xy 0.9377 -1.0202) (xy 0.9303 -1.0196) (xy 0.9231 -1.0187) (xy 0.9159 -1.0174)
						(xy 0.9089 -1.0158) (xy 0.902 -1.0139) (xy 0.8952 -1.0116) (xy 0.8886 -1.009) (xy 0.8822 -1.006)
						(xy 0.8759 -1.0028) (xy 0.8698 -0.9993) (xy 0.8639 -0.9956) (xy 0.8582 -0.9915) (xy 0.8527 -0.9872)
						(xy 0.8474 -0.9826) (xy 0.8424 -0.9778) (xy 0.8376 -0.9727) (xy 0.833 -0.9675) (xy 0.8287 -0.962)
						(xy 0.8246 -0.9563) (xy 0.8208 -0.9504) (xy 0.8173 -0.9443) (xy 0.8141 -0.938) (xy 0.8112 -0.9316)
						(xy 0.8086 -0.925) (xy 0.8063 -0.9182) (xy 0.8044 -0.9113) (xy 0.8027 -0.9043) (xy 0.8015 -0.8971)
						(xy 0.8005 -0.8898) (xy 0.8 -0.8825) (xy 0.7998 -0.875) (xy 0.7998 -0.3418) (xy 0.8 -0.3343) (xy 0.8005 -0.3269)
						(xy 0.8015 -0.3196) (xy 0.8027 -0.3125) (xy 0.8044 -0.3054) (xy 0.8063 -0.2985) (xy 0.8086 -0.2918)
						(xy 0.8112 -0.2852) (xy 0.8141 -0.2787) (xy 0.8173 -0.2725) (xy 0.8208 -0.2664) (xy 0.8246 -0.2605)
						(xy 0.8287 -0.2548) (xy 0.833 -0.2493) (xy 0.8376 -0.244) (xy 0.8424 -0.239) (xy 0.8474 -0.2341)
						(xy 0.8527 -0.2296) (xy 0.8582 -0.2253) (xy 0.8639 -0.2212) (xy 0.8698 -0.2174) (xy 0.8759 -0.2139)
						(xy 0.8822 -0.2107) (xy 0.8886 -0.2078) (xy 0.8952 -0.2052) (xy 0.902 -0.2029) (xy 0.9089 -0.2009)
						(xy 0.9159 -0.1993) (xy 0.9231 -0.198) (xy 0.9303 -0.1971) (xy 0.9377 -0.1965) (xy 0.9452 -0.1964)
						(xy 1.4784 -0.1964) (xy 1.4859 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 1.4859 -1.4084) (xy 1.4933 -1.4089) (xy 1.5005 -1.4099) (xy 1.5077 -1.4111) (xy 1.5147 -1.4128)
						(xy 1.5216 -1.4147) (xy 1.5284 -1.417) (xy 1.535 -1.4196) (xy 1.5414 -1.4225) (xy 1.5477 -1.4257)
						(xy 1.5538 -1.4292) (xy 1.5597 -1.433) (xy 1.5654 -1.4371) (xy 1.5709 -1.4414) (xy 1.5762 -1.446)
						(xy 1.5812 -1.4508) (xy 1.586 -1.4558) (xy 1.5906 -1.4611) (xy 1.5949 -1.4666) (xy 1.599 -1.4723)
						(xy 1.6028 -1.4782) (xy 1.6063 -1.4843) (xy 1.6095 -1.4906) (xy 1.6124 -1.497) (xy 1.615 -1.5036)
						(xy 1.6173 -1.5104) (xy 1.6192 -1.5173) (xy 1.6209 -1.5243) (xy 1.6221 -1.5315) (xy 1.6231 -1.5387)
						(xy 1.6236 -1.5461) (xy 1.6238 -1.5536) (xy 1.6238 -2.0868) (xy 1.6236 -2.0943) (xy 1.6231 -2.1017)
						(xy 1.6221 -2.1089) (xy 1.6209 -2.1161) (xy 1.6192 -2.1231) (xy 1.6173 -2.13) (xy 1.615 -2.1368)
						(xy 1.6124 -2.1434) (xy 1.6095 -2.1498) (xy 1.6063 -2.1561) (xy 1.6028 -2.1622) (xy 1.599 -2.1681)
						(xy 1.5949 -2.1738) (xy 1.5906 -2.1793) (xy 1.586 -2.1846) (xy 1.5812 -2.1896) (xy 1.5762 -2.1944)
						(xy 1.5709 -2.199) (xy 1.5654 -2.2033) (xy 1.5597 -2.2074) (xy 1.5538 -2.2112) (xy 1.5477 -2.2147)
						(xy 1.5414 -2.2179) (xy 1.535 -2.2208) (xy 1.5284 -2.2234) (xy 1.5216 -2.2257) (xy 1.5147 -2.2276)
						(xy 1.5077 -2.2293) (xy 1.5005 -2.2306) (xy 1.4933 -2.2315) (xy 1.4859 -2.232) (xy 1.4784 -2.2322)
						(xy 0.9452 -2.2322) (xy 0.9377 -2.232) (xy 0.9303 -2.2315) (xy 0.9231 -2.2306) (xy 0.9159 -2.2293)
						(xy 0.9089 -2.2276) (xy 0.902 -2.2257) (xy 0.8952 -2.2234) (xy 0.8886 -2.2208) (xy 0.8822 -2.2179)
						(xy 0.8759 -2.2147) (xy 0.8698 -2.2112) (xy 0.8639 -2.2074) (xy 0.8582 -2.2033) (xy 0.8527 -2.199)
						(xy 0.8474 -2.1944) (xy 0.8424 -2.1896) (xy 0.8376 -2.1846) (xy 0.833 -2.1793) (xy 0.8287 -2.1738)
						(xy 0.8246 -2.1681) (xy 0.8208 -2.1622) (xy 0.8173 -2.1561) (xy 0.8141 -2.1498) (xy 0.8112 -2.1434)
						(xy 0.8086 -2.1368) (xy 0.8063 -2.13) (xy 0.8044 -2.1231) (xy 0.8027 -2.1161) (xy 0.8015 -2.1089)
						(xy 0.8005 -2.1017) (xy 0.8 -2.0943) (xy 0.7998 -2.0868) (xy 0.7998 -1.5536) (xy 0.8 -1.5461)
						(xy 0.8005 -1.5387) (xy 0.8015 -1.5315) (xy 0.8027 -1.5243) (xy 0.8044 -1.5173) (xy 0.8063 -1.5104)
						(xy 0.8086 -1.5036) (xy 0.8112 -1.497) (xy 0.8141 -1.4906) (xy 0.8173 -1.4843) (xy 0.8208 -1.4782)
						(xy 0.8246 -1.4723) (xy 0.8287 -1.4666) (xy 0.833 -1.4611) (xy 0.8376 -1.4558) (xy 0.8424 -1.4508)
						(xy 0.8474 -1.446) (xy 0.8527 -1.4414) (xy 0.8582 -1.4371) (xy 0.8639 -1.433) (xy 0.8698 -1.4292)
						(xy 0.8759 -1.4257) (xy 0.8822 -1.4225) (xy 0.8886 -1.4196) (xy 0.8952 -1.417) (xy 0.902 -1.4147)
						(xy 0.9089 -1.4128) (xy 0.9159 -1.4111) (xy 0.9231 -1.4099) (xy 0.9303 -1.4089) (xy 0.9377 -1.4084)
						(xy 0.9452 -1.4082) (xy 1.4784 -1.4082) (xy 1.4859 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6549 -2.6202) (xy 2.6643 -2.6208) (xy 2.6735 -2.6217) (xy 2.6826 -2.623) (xy 2.6916 -2.6246)
						(xy 2.7004 -2.6266) (xy 2.7089 -2.6288) (xy 2.7174 -2.6314) (xy 2.7255 -2.6344) (xy 2.7335 -2.6376)
						(xy 2.7413 -2.6411) (xy 2.7488 -2.6449) (xy 2.756 -2.6489) (xy 2.763 -2.6532) (xy 2.7697 -2.6578)
						(xy 2.7761 -2.6626) (xy 2.7823 -2.6677) (xy 2.7881 -2.673) (xy 2.7936 -2.6784) (xy 2.7987 -2.6841)
						(xy 2.8035 -2.69) (xy 2.808 -2.6961) (xy 2.8121 -2.7024) (xy 2.8158 -2.7088) (xy 2.8191 -2.7155)
						(xy 2.822 -2.7222) (xy 2.8245 -2.7291) (xy 2.8266 -2.7361) (xy 2.8282 -2.7433) (xy 2.8294 -2.7506)
						(xy 2.8301 -2.758) (xy 2.8303 -2.7654) (xy 2.8303 -3.2986) (xy 2.8301 -3.3061) (xy 2.8294 -3.3135)
						(xy 2.8282 -3.3208) (xy 2.8266 -3.3279) (xy 2.8245 -3.335) (xy 2.822 -3.3419) (xy 2.8191 -3.3486)
						(xy 2.8158 -3.3552) (xy 2.8121 -3.3617) (xy 2.808 -3.3679) (xy 2.8035 -3.374) (xy 2.7987 -3.3799)
						(xy 2.7936 -3.3856) (xy 2.7881 -3.3911) (xy 2.7823 -3.3964) (xy 2.7761 -3.4015) (xy 2.763 -3.4108)
						(xy 2.7488 -3.4192) (xy 2.7335 -3.4265) (xy 2.7174 -3.4326) (xy 2.7004 -3.4375) (xy 2.6826 -3.4411)
						(xy 2.6643 -3.4433) (xy 2.6549 -3.4439) (xy 2.6453 -3.4441) (xy 2.299 -3.4441) (xy 1.9671 -3.4441)
						(xy 1.1106 -3.4441) (xy 0.4323 -3.4441) (xy -0.4378 -3.4441) (xy -1.116 -3.4441) (xy -1.9725 -3.4441)
						(xy -2.3044 -3.4441) (xy -2.6508 -3.4441) (xy -2.6603 -3.4439) (xy -2.6697 -3.4433) (xy -2.6789 -3.4424)
						(xy -2.688 -3.4411) (xy -2.697 -3.4395) (xy -2.7058 -3.4375) (xy -2.7144 -3.4352) (xy -2.7228 -3.4326)
						(xy -2.731 -3.4297) (xy -2.7389 -3.4265) (xy -2.7467 -3.423) (xy -2.7542 -3.4192) (xy -2.7614 -3.4152)
						(xy -2.7684 -3.4108) (xy -2.7751 -3.4063) (xy -2.7816 -3.4015) (xy -2.7877 -3.3964) (xy -2.7935 -3.3911)
						(xy -2.799 -3.3856) (xy -2.8041 -3.3799) (xy -2.809 -3.374) (xy -2.8134 -3.3679) (xy -2.8175 -3.3617)
						(xy -2.8212 -3.3552) (xy -2.8245 -3.3486) (xy -2.8274 -3.3419) (xy -2.8299 -3.335) (xy -2.832 -3.3279)
						(xy -2.8336 -3.3208) (xy -2.8348 -3.3135) (xy -2.8355 -3.3061) (xy -2.8357 -3.2986) (xy -2.8357 -2.7654)
						(xy -2.8355 -2.758) (xy -2.8348 -2.7506) (xy -2.8336 -2.7433) (xy -2.832 -2.7361) (xy -2.8299 -2.7291)
						(xy -2.8274 -2.7222) (xy -2.8245 -2.7155) (xy -2.8212 -2.7088) (xy -2.8175 -2.7024) (xy -2.8134 -2.6961)
						(xy -2.809 -2.69) (xy -2.8041 -2.6841) (xy -2.799 -2.6784) (xy -2.7935 -2.673) (xy -2.7877 -2.6677)
						(xy -2.7816 -2.6626) (xy -2.7684 -2.6532) (xy -2.7542 -2.6449) (xy -2.7389 -2.6376) (xy -2.7228 -2.6314)
						(xy -2.7058 -2.6266) (xy -2.688 -2.623) (xy -2.6697 -2.6208) (xy -2.6603 -2.6202) (xy -2.6508 -2.62)
						(xy -2.3044 -2.62) (xy -1.9725 -2.62) (xy -1.116 -2.62) (xy -0.4378 -2.62) (xy 0.4323 -2.62) (xy 1.1106 -2.62)
						(xy 1.9671 -2.62) (xy 2.299 -2.62) (xy 2.6453 -2.62) (xy 2.6549 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6924 1.0326) (xy 2.6998 1.032) (xy 2.707 1.0311) (xy 2.7142 1.0298) (xy 2.7212 1.0282) (xy 2.7281 1.0262)
						(xy 2.7349 1.024) (xy 2.7415 1.0214) (xy 2.7479 1.0184) (xy 2.7542 1.0152) (xy 2.7603 1.0117)
						(xy 2.7662 1.0079) (xy 2.7719 1.0039) (xy 2.7774 0.9996) (xy 2.7827 0.995) (xy 2.7877 0.9902)
						(xy 2.7925 0.9851) (xy 2.7971 0.9798) (xy 2.8014 0.9744) (xy 2.8055 0.9687) (xy 2.8093 0.9628)
						(xy 2.8128 0.9567) (xy 2.816 0.9504) (xy 2.8189 0.944) (xy 2.8215 0.9373) (xy 2.8238 0.9306) (xy 2.8257 0.9237)
						(xy 2.8274 0.9167) (xy 2.8286 0.9095) (xy 2.8296 0.9022) (xy 2.8301 0.8948) (xy 2.8303 0.8874)
						(xy 2.8303 0.3542) (xy 2.8301 0.3467) (xy 2.8296 0.3393) (xy 2.8286 0.332) (xy 2.8274 0.3249)
						(xy 2.8257 0.3178) (xy 2.8238 0.3109) (xy 2.8215 0.3042) (xy 2.8189 0.2976) (xy 2.816 0.2911)
						(xy 2.8128 0.2849) (xy 2.8093 0.2788) (xy 2.8055 0.2729) (xy 2.8014 0.2672) (xy 2.7971 0.2617)
						(xy 2.7925 0.2564) (xy 2.7877 0.2513) (xy 2.7827 0.2465) (xy 2.7774 0.242) (xy 2.7719 0.2376)
						(xy 2.7662 0.2336) (xy 2.7603 0.2298) (xy 2.7542 0.2263) (xy 2.7479 0.2231) (xy 2.7415 0.2202)
						(xy 2.7349 0.2176) (xy 2.7281 0.2153) (xy 2.7212 0.2133) (xy 2.7142 0.2117) (xy 2.707 0.2104)
						(xy 2.6998 0.2095) (xy 2.6924 0.2089) (xy 2.6849 0.2087) (xy 2.1517 0.2087) (xy 2.1442 0.2089)
						(xy 2.1368 0.2095) (xy 2.1296 0.2104) (xy 2.1224 0.2117) (xy 2.1154 0.2133) (xy 2.1085 0.2153)
						(xy 2.1017 0.2176) (xy 2.0951 0.2202) (xy 2.0887 0.2231) (xy 2.0824 0.2263) (xy 2.0763 0.2298)
						(xy 2.0704 0.2336) (xy 2.0647 0.2376) (xy 2.0592 0.242) (xy 2.0539 0.2465) (xy 2.0489 0.2513)
						(xy 2.0441 0.2564) (xy 2.0395 0.2617) (xy 2.0352 0.2672) (xy 2.0311 0.2729) (xy 2.0273 0.2788)
						(xy 2.0238 0.2849) (xy 2.0206 0.2911) (xy 2.0177 0.2976) (xy 2.0151 0.3042) (xy 2.0128 0.3109)
						(xy 2.0109 0.3178) (xy 2.0092 0.3249) (xy 2.008 0.332) (xy 2.007 0.3393) (xy 2.0065 0.3467) (xy 2.0063 0.3542)
						(xy 2.0063 0.8874) (xy 2.0065 0.8948) (xy 2.007 0.9022) (xy 2.008 0.9095) (xy 2.0092 0.9167) (xy 2.0109 0.9237)
						(xy 2.0128 0.9306) (xy 2.0151 0.9373) (xy 2.0177 0.944) (xy 2.0206 0.9504) (xy 2.0238 0.9567)
						(xy 2.0273 0.9628) (xy 2.0311 0.9687) (xy 2.0352 0.9744) (xy 2.0395 0.9798) (xy 2.0441 0.9851)
						(xy 2.0489 0.9902) (xy 2.0539 0.995) (xy 2.0592 0.9996) (xy 2.0647 1.0039) (xy 2.0704 1.0079)
						(xy 2.0763 1.0117) (xy 2.0824 1.0152) (xy 2.0887 1.0184) (xy 2.0951 1.0214) (xy 2.1017 1.024)
						(xy 2.1085 1.0262) (xy 2.1154 1.0282) (xy 2.1224 1.0298) (xy 2.1296 1.0311) (xy 2.1368 1.032)
						(xy 2.1442 1.0326) (xy 2.1517 1.0328) (xy 2.6849 1.0328) (xy 2.6924 1.0326)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6924 1.0153) (xy 2.6998 1.0147) (xy 2.707 1.0138) (xy 2.7142 1.0125) (xy 2.7212 1.0109)
						(xy 2.7281 1.0089) (xy 2.7349 1.0067) (xy 2.7415 1.0041) (xy 2.7479 1.0011) (xy 2.7542 0.9979)
						(xy 2.7603 0.9944) (xy 2.7662 0.9906) (xy 2.7719 0.9866) (xy 2.7774 0.9823) (xy 2.7827 0.9777)
						(xy 2.7877 0.9729) (xy 2.7925 0.9678) (xy 2.7971 0.9626) (xy 2.8014 0.9571) (xy 2.8055 0.9514)
						(xy 2.8093 0.9455) (xy 2.8128 0.9394) (xy 2.816 0.9331) (xy 2.8189 0.9267) (xy 2.8215 0.9201)
						(xy 2.8238 0.9133) (xy 2.8257 0.9064) (xy 2.8274 0.8994) (xy 2.8286 0.8922) (xy 2.8296 0.8849)
						(xy 2.8301 0.8776) (xy 2.8303 0.8701) (xy 2.8303 0.3369) (xy 2.8301 0.3294) (xy 2.8296 0.322)
						(xy 2.8286 0.3147) (xy 2.8274 0.3076) (xy 2.8257 0.3005) (xy 2.8238 0.2936) (xy 2.8215 0.2869)
						(xy 2.8189 0.2803) (xy 2.816 0.2738) (xy 2.8128 0.2676) (xy 2.8093 0.2615) (xy 2.8055 0.2556)
						(xy 2.8014 0.2499) (xy 2.7971 0.2444) (xy 2.7925 0.2391) (xy 2.7877 0.2341) (xy 2.7827 0.2292)
						(xy 2.7774 0.2247) (xy 2.7719 0.2203) (xy 2.7662 0.2163) (xy 2.7603 0.2125) (xy 2.7542 0.209)
						(xy 2.7479 0.2058) (xy 2.7415 0.2029) (xy 2.7349 0.2003) (xy 2.7281 0.198) (xy 2.7212 0.196) (xy 2.7142 0.1944)
						(xy 2.707 0.1931) (xy 2.6998 0.1922) (xy 2.6924 0.1916) (xy 2.6849 0.1914) (xy 2.1517 0.1914)
						(xy 2.1442 0.1916) (xy 2.1368 0.1922) (xy 2.1296 0.1931) (xy 2.1224 0.1944) (xy 2.1154 0.196)
						(xy 2.1085 0.198) (xy 2.1017 0.2003) (xy 2.0951 0.2029) (xy 2.0887 0.2058) (xy 2.0824 0.209) (xy 2.0763 0.2125)
						(xy 2.0704 0.2163) (xy 2.0647 0.2203) (xy 2.0592 0.2247) (xy 2.0539 0.2292) (xy 2.0489 0.2341)
						(xy 2.0441 0.2391) (xy 2.0395 0.2444) (xy 2.0352 0.2499) (xy 2.0311 0.2556) (xy 2.0273 0.2615)
						(xy 2.0238 0.2676) (xy 2.0206 0.2738) (xy 2.0177 0.2803) (xy 2.0151 0.2869) (xy 2.0128 0.2936)
						(xy 2.0109 0.3005) (xy 2.0092 0.3076) (xy 2.008 0.3147) (xy 2.007 0.322) (xy 2.0065 0.3294) (xy 2.0063 0.3369)
						(xy 2.0063 0.8701) (xy 2.0065 0.8776) (xy 2.007 0.8849) (xy 2.008 0.8922) (xy 2.0092 0.8994) (xy 2.0109 0.9064)
						(xy 2.0128 0.9133) (xy 2.0151 0.9201) (xy 2.0177 0.9267) (xy 2.0206 0.9331) (xy 2.0238 0.9394)
						(xy 2.0273 0.9455) (xy 2.0311 0.9514) (xy 2.0352 0.9571) (xy 2.0395 0.9626) (xy 2.0441 0.9678)
						(xy 2.0489 0.9729) (xy 2.0539 0.9777) (xy 2.0592 0.9823) (xy 2.0647 0.9866) (xy 2.0704 0.9906)
						(xy 2.0763 0.9944) (xy 2.0824 0.9979) (xy 2.0887 1.0011) (xy 2.0951 1.0041) (xy 2.1017 1.0067)
						(xy 2.1085 1.0089) (xy 2.1154 1.0109) (xy 2.1224 1.0125) (xy 2.1296 1.0138) (xy 2.1368 1.0147)
						(xy 2.1442 1.0153) (xy 2.1517 1.0155) (xy 2.6849 1.0155) (xy 2.6924 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6924 -0.1965) (xy 2.6998 -0.1971) (xy 2.707 -0.198) (xy 2.7142 -0.1993) (xy 2.7212 -0.2009)
						(xy 2.7281 -0.2029) (xy 2.7349 -0.2052) (xy 2.7415 -0.2078) (xy 2.7479 -0.2107) (xy 2.7542 -0.2139)
						(xy 2.7603 -0.2174) (xy 2.7662 -0.2212) (xy 2.7719 -0.2253) (xy 2.7774 -0.2296) (xy 2.7827 -0.2341)
						(xy 2.7877 -0.239) (xy 2.7925 -0.244) (xy 2.7971 -0.2493) (xy 2.8014 -0.2548) (xy 2.8055 -0.2605)
						(xy 2.8093 -0.2664) (xy 2.8128 -0.2725) (xy 2.816 -0.2787) (xy 2.8189 -0.2852) (xy 2.8215 -0.2918)
						(xy 2.8238 -0.2985) (xy 2.8257 -0.3054) (xy 2.8274 -0.3125) (xy 2.8286 -0.3196) (xy 2.8296 -0.3269)
						(xy 2.8301 -0.3343) (xy 2.8303 -0.3418) (xy 2.8303 -0.875) (xy 2.8301 -0.8825) (xy 2.8296 -0.8898)
						(xy 2.8286 -0.8971) (xy 2.8274 -0.9043) (xy 2.8257 -0.9113) (xy 2.8238 -0.9182) (xy 2.8215 -0.925)
						(xy 2.8189 -0.9316) (xy 2.816 -0.938) (xy 2.8128 -0.9443) (xy 2.8093 -0.9504) (xy 2.8055 -0.9563)
						(xy 2.8014 -0.962) (xy 2.7971 -0.9675) (xy 2.7925 -0.9727) (xy 2.7877 -0.9778) (xy 2.7827 -0.9826)
						(xy 2.7774 -0.9872) (xy 2.7719 -0.9915) (xy 2.7662 -0.9956) (xy 2.7603 -0.9993) (xy 2.7542 -1.0028)
						(xy 2.7479 -1.006) (xy 2.7415 -1.009) (xy 2.7349 -1.0116) (xy 2.7281 -1.0139) (xy 2.7212 -1.0158)
						(xy 2.7142 -1.0174) (xy 2.707 -1.0187) (xy 2.6998 -1.0196) (xy 2.6924 -1.0202) (xy 2.6849 -1.0204)
						(xy 2.1517 -1.0204) (xy 2.1442 -1.0202) (xy 2.1368 -1.0196) (xy 2.1296 -1.0187) (xy 2.1224 -1.0174)
						(xy 2.1154 -1.0158) (xy 2.1085 -1.0139) (xy 2.1017 -1.0116) (xy 2.0951 -1.009) (xy 2.0887 -1.006)
						(xy 2.0824 -1.0028) (xy 2.0763 -0.9993) (xy 2.0704 -0.9956) (xy 2.0647 -0.9915) (xy 2.0592 -0.9872)
						(xy 2.0539 -0.9826) (xy 2.0489 -0.9778) (xy 2.0441 -0.9727) (xy 2.0395 -0.9675) (xy 2.0352 -0.962)
						(xy 2.0311 -0.9563) (xy 2.0273 -0.9504) (xy 2.0238 -0.9443) (xy 2.0206 -0.938) (xy 2.0177 -0.9316)
						(xy 2.0151 -0.925) (xy 2.0128 -0.9182) (xy 2.0109 -0.9113) (xy 2.0092 -0.9043) (xy 2.008 -0.8971)
						(xy 2.007 -0.8898) (xy 2.0065 -0.8825) (xy 2.0063 -0.875) (xy 2.0063 -0.3418) (xy 2.0065 -0.3343)
						(xy 2.007 -0.3269) (xy 2.008 -0.3196) (xy 2.0092 -0.3125) (xy 2.0109 -0.3054) (xy 2.0128 -0.2985)
						(xy 2.0151 -0.2918) (xy 2.0177 -0.2852) (xy 2.0206 -0.2787) (xy 2.0238 -0.2725) (xy 2.0273 -0.2664)
						(xy 2.0311 -0.2605) (xy 2.0352 -0.2548) (xy 2.0395 -0.2493) (xy 2.0441 -0.244) (xy 2.0489 -0.239)
						(xy 2.0539 -0.2341) (xy 2.0592 -0.2296) (xy 2.0647 -0.2253) (xy 2.0704 -0.2212) (xy 2.0763 -0.2174)
						(xy 2.0824 -0.2139) (xy 2.0887 -0.2107) (xy 2.0951 -0.2078) (xy 2.1017 -0.2052) (xy 2.1085 -0.2029)
						(xy 2.1154 -0.2009) (xy 2.1224 -0.1993) (xy 2.1296 -0.198) (xy 2.1368 -0.1971) (xy 2.1442 -0.1965)
						(xy 2.1517 -0.1964) (xy 2.6849 -0.1964) (xy 2.6924 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6924 -1.4084) (xy 2.6998 -1.4089) (xy 2.707 -1.4099) (xy 2.7142 -1.4111) (xy 2.7212 -1.4128)
						(xy 2.7281 -1.4147) (xy 2.7349 -1.417) (xy 2.7415 -1.4196) (xy 2.7479 -1.4225) (xy 2.7542 -1.4257)
						(xy 2.7603 -1.4292) (xy 2.7662 -1.433) (xy 2.7719 -1.4371) (xy 2.7774 -1.4414) (xy 2.7827 -1.446)
						(xy 2.7877 -1.4508) (xy 2.7925 -1.4558) (xy 2.7971 -1.4611) (xy 2.8014 -1.4666) (xy 2.8055 -1.4723)
						(xy 2.8093 -1.4782) (xy 2.8128 -1.4843) (xy 2.816 -1.4906) (xy 2.8189 -1.497) (xy 2.8215 -1.5036)
						(xy 2.8238 -1.5104) (xy 2.8257 -1.5173) (xy 2.8274 -1.5243) (xy 2.8286 -1.5315) (xy 2.8296 -1.5387)
						(xy 2.8301 -1.5461) (xy 2.8303 -1.5536) (xy 2.8303 -2.0868) (xy 2.8301 -2.0943) (xy 2.8296 -2.1017)
						(xy 2.8286 -2.1089) (xy 2.8274 -2.1161) (xy 2.8257 -2.1231) (xy 2.8238 -2.13) (xy 2.8215 -2.1368)
						(xy 2.8189 -2.1434) (xy 2.816 -2.1498) (xy 2.8128 -2.1561) (xy 2.8093 -2.1622) (xy 2.8055 -2.1681)
						(xy 2.8014 -2.1738) (xy 2.7971 -2.1793) (xy 2.7925 -2.1846) (xy 2.7877 -2.1896) (xy 2.7827 -2.1944)
						(xy 2.7774 -2.199) (xy 2.7719 -2.2033) (xy 2.7662 -2.2074) (xy 2.7603 -2.2112) (xy 2.7542 -2.2147)
						(xy 2.7479 -2.2179) (xy 2.7415 -2.2208) (xy 2.7349 -2.2234) (xy 2.7281 -2.2257) (xy 2.7212 -2.2276)
						(xy 2.7142 -2.2293) (xy 2.707 -2.2306) (xy 2.6998 -2.2315) (xy 2.6924 -2.232) (xy 2.6849 -2.2322)
						(xy 2.1517 -2.2322) (xy 2.1442 -2.232) (xy 2.1368 -2.2315) (xy 2.1296 -2.2306) (xy 2.1224 -2.2293)
						(xy 2.1154 -2.2276) (xy 2.1085 -2.2257) (xy 2.1017 -2.2234) (xy 2.0951 -2.2208) (xy 2.0887 -2.2179)
						(xy 2.0824 -2.2147) (xy 2.0763 -2.2112) (xy 2.0704 -2.2074) (xy 2.0647 -2.2033) (xy 2.0592 -2.199)
						(xy 2.0539 -2.1944) (xy 2.0489 -2.1896) (xy 2.0441 -2.1846) (xy 2.0395 -2.1793) (xy 2.0352 -2.1738)
						(xy 2.0311 -2.1681) (xy 2.0273 -2.1622) (xy 2.0238 -2.1561) (xy 2.0206 -2.1498) (xy 2.0177 -2.1434)
						(xy 2.0151 -2.1368) (xy 2.0128 -2.13) (xy 2.0109 -2.1231) (xy 2.0092 -2.1161) (xy 2.008 -2.1089)
						(xy 2.007 -2.1017) (xy 2.0065 -2.0943) (xy 2.0063 -2.0868) (xy 2.0063 -1.5536) (xy 2.0065 -1.5461)
						(xy 2.007 -1.5387) (xy 2.008 -1.5315) (xy 2.0092 -1.5243) (xy 2.0109 -1.5173) (xy 2.0128 -1.5104)
						(xy 2.0151 -1.5036) (xy 2.0177 -1.497) (xy 2.0206 -1.4906) (xy 2.0238 -1.4843) (xy 2.0273 -1.4782)
						(xy 2.0311 -1.4723) (xy 2.0352 -1.4666) (xy 2.0395 -1.4611) (xy 2.0441 -1.4558) (xy 2.0489 -1.4508)
						(xy 2.0539 -1.446) (xy 2.0592 -1.4414) (xy 2.0647 -1.4371) (xy 2.0704 -1.433) (xy 2.0763 -1.4292)
						(xy 2.0824 -1.4257) (xy 2.0887 -1.4225) (xy 2.0951 -1.4196) (xy 2.1017 -1.417) (xy 2.1085 -1.4147)
						(xy 2.1154 -1.4128) (xy 2.1224 -1.4111) (xy 2.1296 -1.4099) (xy 2.1368 -1.4089) (xy 2.1442 -1.4084)
						(xy 2.1517 -1.4082) (xy 2.6849 -1.4082) (xy 2.6924 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 3.9042 -0.1965) (xy 3.9116 -0.1971) (xy 3.9189 -0.198) (xy 3.926 -0.1993) (xy 3.9331 -0.2009)
						(xy 3.94 -0.2029) (xy 3.9467 -0.2052) (xy 3.9533 -0.2078) (xy 3.9598 -0.2107) (xy 3.966 -0.2139)
						(xy 3.9721 -0.2174) (xy 3.978 -0.2212) (xy 3.9837 -0.2253) (xy 3.9892 -0.2296) (xy 3.9945 -0.2341)
						(xy 3.9995 -0.239) (xy 4.0044 -0.244) (xy 4.0089 -0.2493) (xy 4.0133 -0.2548) (xy 4.0173 -0.2605)
						(xy 4.0211 -0.2664) (xy 4.0246 -0.2725) (xy 4.0278 -0.2787) (xy 4.0307 -0.2852) (xy 4.0333 -0.2918)
						(xy 4.0356 -0.2985) (xy 4.0376 -0.3054) (xy 4.0392 -0.3125) (xy 4.0405 -0.3196) (xy 4.0414 -0.3269)
						(xy 4.042 -0.3343) (xy 4.0422 -0.3418) (xy 4.0422 -0.875) (xy 4.042 -0.8825) (xy 4.0414 -0.8898)
						(xy 4.0405 -0.8971) (xy 4.0392 -0.9043) (xy 4.0376 -0.9113) (xy 4.0356 -0.9182) (xy 4.0333 -0.925)
						(xy 4.0307 -0.9316) (xy 4.0278 -0.938) (xy 4.0246 -0.9443) (xy 4.0211 -0.9504) (xy 4.0173 -0.9563)
						(xy 4.0133 -0.962) (xy 4.0089 -0.9675) (xy 4.0044 -0.9727) (xy 3.9995 -0.9778) (xy 3.9945 -0.9826)
						(xy 3.9892 -0.9872) (xy 3.9837 -0.9915) (xy 3.978 -0.9956) (xy 3.9721 -0.9993) (xy 3.966 -1.0028)
						(xy 3.9598 -1.006) (xy 3.9533 -1.009) (xy 3.9467 -1.0116) (xy 3.94 -1.0139) (xy 3.9331 -1.0158)
						(xy 3.926 -1.0174) (xy 3.9189 -1.0187) (xy 3.9116 -1.0196) (xy 3.9042 -1.0202) (xy 3.8967 -1.0204)
						(xy 3.3635 -1.0204) (xy 3.3561 -1.0202) (xy 3.3487 -1.0196) (xy 3.3414 -1.0187) (xy 3.3342 -1.0174)
						(xy 3.3272 -1.0158) (xy 3.3203 -1.0139) (xy 3.3135 -1.0116) (xy 3.3069 -1.009) (xy 3.3005 -1.006)
						(xy 3.2942 -1.0028) (xy 3.2881 -0.9993) (xy 3.2822 -0.9956) (xy 3.2765 -0.9915) (xy 3.271 -0.9872)
						(xy 3.2658 -0.9826) (xy 3.2607 -0.9778) (xy 3.2559 -0.9727) (xy 3.2513 -0.9675) (xy 3.247 -0.962)
						(xy 3.243 -0.9563) (xy 3.2392 -0.9504) (xy 3.2357 -0.9443) (xy 3.2325 -0.938) (xy 3.2295 -0.9316)
						(xy 3.2269 -0.925) (xy 3.2247 -0.9182) (xy 3.2227 -0.9113) (xy 3.2211 -0.9043) (xy 3.2198 -0.8971)
						(xy 3.2189 -0.8898) (xy 3.2183 -0.8825) (xy 3.2181 -0.875) (xy 3.2181 -0.3418) (xy 3.2183 -0.3343)
						(xy 3.2189 -0.3269) (xy 3.2198 -0.3196) (xy 3.2211 -0.3125) (xy 3.2227 -0.3054) (xy 3.2247 -0.2985)
						(xy 3.2269 -0.2918) (xy 3.2295 -0.2852) (xy 3.2325 -0.2787) (xy 3.2357 -0.2725) (xy 3.2392 -0.2664)
						(xy 3.243 -0.2605) (xy 3.247 -0.2548) (xy 3.2513 -0.2493) (xy 3.2559 -0.244) (xy 3.2607 -0.239)
						(xy 3.2658 -0.2341) (xy 3.271 -0.2296) (xy 3.2765 -0.2253) (xy 3.2822 -0.2212) (xy 3.2881 -0.2174)
						(xy 3.2942 -0.2139) (xy 3.3005 -0.2107) (xy 3.3069 -0.2078) (xy 3.3135 -0.2052) (xy 3.3203 -0.2029)
						(xy 3.3272 -0.2009) (xy 3.3342 -0.1993) (xy 3.3414 -0.198) (xy 3.3487 -0.1971) (xy 3.3561 -0.1965)
						(xy 3.3635 -0.1964) (xy 3.8967 -0.1964) (xy 3.9042 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 3.9042 -2.6029) (xy 3.9116 -2.6035) (xy 3.9189 -2.6044) (xy 3.926 -2.6057) (xy 3.9331 -2.6073)
						(xy 3.94 -2.6093) (xy 3.9467 -2.6115) (xy 3.9533 -2.6141) (xy 3.9598 -2.6171) (xy 3.966 -2.6203)
						(xy 3.9721 -2.6238) (xy 3.978 -2.6276) (xy 3.9837 -2.6316) (xy 3.9892 -2.6359) (xy 3.9945 -2.6405)
						(xy 3.9995 -2.6453) (xy 4.0044 -2.6504) (xy 4.0089 -2.6556) (xy 4.0133 -2.6611) (xy 4.0173 -2.6668)
						(xy 4.0211 -2.6727) (xy 4.0246 -2.6788) (xy 4.0278 -2.6851) (xy 4.0307 -2.6915) (xy 4.0333 -2.6981)
						(xy 4.0356 -2.7049) (xy 4.0376 -2.7118) (xy 4.0392 -2.7188) (xy 4.0405 -2.726) (xy 4.0414 -2.7333)
						(xy 4.042 -2.7406) (xy 4.0422 -2.7481) (xy 4.0422 -3.2813) (xy 4.042 -3.2888) (xy 4.0414 -3.2962)
						(xy 4.0405 -3.3035) (xy 4.0392 -3.3106) (xy 4.0376 -3.3177) (xy 4.0356 -3.3246) (xy 4.0333 -3.3313)
						(xy 4.0307 -3.3379) (xy 4.0278 -3.3444) (xy 4.0246 -3.3506) (xy 4.0211 -3.3567) (xy 4.0173 -3.3626)
						(xy 4.0133 -3.3683) (xy 4.0089 -3.3738) (xy 4.0044 -3.3791) (xy 3.9995 -3.3841) (xy 3.9945 -3.389)
						(xy 3.9892 -3.3935) (xy 3.9837 -3.3979) (xy 3.978 -3.4019) (xy 3.9721 -3.4057) (xy 3.966 -3.4092)
						(xy 3.9598 -3.4124) (xy 3.9533 -3.4153) (xy 3.9467 -3.4179) (xy 3.94 -3.4202) (xy 3.9331 -3.4222)
						(xy 3.926 -3.4238) (xy 3.9189 -3.4251) (xy 3.9116 -3.426) (xy 3.9042 -3.4266) (xy 3.8967 -3.4268)
						(xy 3.3635 -3.4268) (xy 3.3561 -3.4266) (xy 3.3487 -3.426) (xy 3.3414 -3.4251) (xy 3.3342 -3.4238)
						(xy 3.3272 -3.4222) (xy 3.3203 -3.4202) (xy 3.3135 -3.4179) (xy 3.3069 -3.4153) (xy 3.3005 -3.4124)
						(xy 3.2942 -3.4092) (xy 3.2881 -3.4057) (xy 3.2822 -3.4019) (xy 3.2765 -3.3979) (xy 3.271 -3.3935)
						(xy 3.2658 -3.389) (xy 3.2607 -3.3841) (xy 3.2559 -3.3791) (xy 3.2513 -3.3738) (xy 3.247 -3.3683)
						(xy 3.243 -3.3626) (xy 3.2392 -3.3567) (xy 3.2357 -3.3506) (xy 3.2325 -3.3444) (xy 3.2295 -3.3379)
						(xy 3.2269 -3.3313) (xy 3.2247 -3.3246) (xy 3.2227 -3.3177) (xy 3.2211 -3.3106) (xy 3.2198 -3.3035)
						(xy 3.2189 -3.2962) (xy 3.2183 -3.2888) (xy 3.2181 -3.2813) (xy 3.2181 -2.7481) (xy 3.2183 -2.7406)
						(xy 3.2189 -2.7333) (xy 3.2198 -2.726) (xy 3.2211 -2.7188) (xy 3.2227 -2.7118) (xy 3.2247 -2.7049)
						(xy 3.2269 -2.6981) (xy 3.2295 -2.6915) (xy 3.2325 -2.6851) (xy 3.2357 -2.6788) (xy 3.2392 -2.6727)
						(xy 3.243 -2.6668) (xy 3.247 -2.6611) (xy 3.2513 -2.6556) (xy 3.2559 -2.6504) (xy 3.2607 -2.6453)
						(xy 3.2658 -2.6405) (xy 3.271 -2.6359) (xy 3.2765 -2.6316) (xy 3.2822 -2.6276) (xy 3.2881 -2.6238)
						(xy 3.2942 -2.6203) (xy 3.3005 -2.6171) (xy 3.3069 -2.6141) (xy 3.3135 -2.6115) (xy 3.3203 -2.6093)
						(xy 3.3272 -2.6073) (xy 3.3342 -2.6057) (xy 3.3414 -2.6044) (xy 3.3487 -2.6035) (xy 3.3561 -2.6029)
						(xy 3.3635 -2.6027) (xy 3.8967 -2.6027) (xy 3.9042 -2.6029)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 3.9042 -2.6202) (xy 3.9116 -2.6208) (xy 3.9189 -2.6217) (xy 3.926 -2.623) (xy 3.9331 -2.6246)
						(xy 3.94 -2.6266) (xy 3.9467 -2.6288) (xy 3.9533 -2.6314) (xy 3.9598 -2.6344) (xy 3.966 -2.6376)
						(xy 3.9721 -2.6411) (xy 3.978 -2.6449) (xy 3.9837 -2.6489) (xy 3.9892 -2.6532) (xy 3.9945 -2.6578)
						(xy 3.9995 -2.6626) (xy 4.0044 -2.6677) (xy 4.0089 -2.673) (xy 4.0133 -2.6784) (xy 4.0173 -2.6841)
						(xy 4.0211 -2.69) (xy 4.0246 -2.6961) (xy 4.0278 -2.7024) (xy 4.0307 -2.7088) (xy 4.0333 -2.7155)
						(xy 4.0356 -2.7222) (xy 4.0376 -2.7291) (xy 4.0392 -2.7361) (xy 4.0405 -2.7433) (xy 4.0414 -2.7506)
						(xy 4.042 -2.758) (xy 4.0422 -2.7654) (xy 4.0422 -3.2986) (xy 4.042 -3.3061) (xy 4.0414 -3.3135)
						(xy 4.0405 -3.3208) (xy 4.0392 -3.3279) (xy 4.0376 -3.335) (xy 4.0356 -3.3419) (xy 4.0333 -3.3486)
						(xy 4.0307 -3.3552) (xy 4.0278 -3.3617) (xy 4.0246 -3.3679) (xy 4.0211 -3.374) (xy 4.0173 -3.3799)
						(xy 4.0133 -3.3856) (xy 4.0089 -3.3911) (xy 4.0044 -3.3964) (xy 3.9995 -3.4015) (xy 3.9945 -3.4063)
						(xy 3.9892 -3.4108) (xy 3.9837 -3.4152) (xy 3.978 -3.4192) (xy 3.9721 -3.423) (xy 3.966 -3.4265)
						(xy 3.9598 -3.4297) (xy 3.9533 -3.4326) (xy 3.9467 -3.4352) (xy 3.94 -3.4375) (xy 3.9331 -3.4395)
						(xy 3.926 -3.4411) (xy 3.9189 -3.4424) (xy 3.9116 -3.4433) (xy 3.9042 -3.4439) (xy 3.8967 -3.4441)
						(xy 3.3635 -3.4441) (xy 3.3561 -3.4439) (xy 3.3487 -3.4433) (xy 3.3414 -3.4424) (xy 3.3342 -3.4411)
						(xy 3.3272 -3.4395) (xy 3.3203 -3.4375) (xy 3.3135 -3.4352) (xy 3.3069 -3.4326) (xy 3.3005 -3.4297)
						(xy 3.2942 -3.4265) (xy 3.2881 -3.423) (xy 3.2822 -3.4192) (xy 3.2765 -3.4152) (xy 3.271 -3.4108)
						(xy 3.2658 -3.4063) (xy 3.2607 -3.4015) (xy 3.2559 -3.3964) (xy 3.2513 -3.3911) (xy 3.247 -3.3856)
						(xy 3.243 -3.3799) (xy 3.2392 -3.374) (xy 3.2357 -3.3679) (xy 3.2325 -3.3617) (xy 3.2295 -3.3552)
						(xy 3.2269 -3.3486) (xy 3.2247 -3.3419) (xy 3.2227 -3.335) (xy 3.2211 -3.3279) (xy 3.2198 -3.3208)
						(xy 3.2189 -3.3135) (xy 3.2183 -3.3061) (xy 3.2181 -3.2986) (xy 3.2181 -2.7654) (xy 3.2183 -2.758)
						(xy 3.2189 -2.7506) (xy 3.2198 -2.7433) (xy 3.2211 -2.7361) (xy 3.2227 -2.7291) (xy 3.2247 -2.7222)
						(xy 3.2269 -2.7155) (xy 3.2295 -2.7088) (xy 3.2325 -2.7024) (xy 3.2357 -2.6961) (xy 3.2392 -2.69)
						(xy 3.243 -2.6841) (xy 3.247 -2.6784) (xy 3.2513 -2.673) (xy 3.2559 -2.6677) (xy 3.2607 -2.6626)
						(xy 3.2658 -2.6578) (xy 3.271 -2.6532) (xy 3.2765 -2.6489) (xy 3.2822 -2.6449) (xy 3.2881 -2.6411)
						(xy 3.2942 -2.6376) (xy 3.3005 -2.6344) (xy 3.3069 -2.6314) (xy 3.3135 -2.6288) (xy 3.3203 -2.6266)
						(xy 3.3272 -2.6246) (xy 3.3342 -2.623) (xy 3.3414 -2.6217) (xy 3.3487 -2.6208) (xy 3.3561 -2.6202)
						(xy 3.3635 -2.62) (xy 3.8967 -2.62) (xy 3.9042 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 1.0153) (xy 5.1288 1.0147) (xy 5.136 1.0138) (xy 5.1432 1.0125) (xy 5.1502 1.0109)
						(xy 5.1571 1.0089) (xy 5.1639 1.0067) (xy 5.1705 1.004) (xy 5.1769 1.0011) (xy 5.1832 0.9979)
						(xy 5.1893 0.9944) (xy 5.1952 0.9906) (xy 5.2009 0.9866) (xy 5.2064 0.9823) (xy 5.2117 0.9777)
						(xy 5.2167 0.9729) (xy 5.2215 0.9678) (xy 5.2261 0.9625) (xy 5.2304 0.9571) (xy 5.2345 0.9513)
						(xy 5.2383 0.9454) (xy 5.2418 0.9394) (xy 5.245 0.9331) (xy 5.2479 0.9266) (xy 5.2505 0.92) (xy 5.2528 0.9133)
						(xy 5.2547 0.9064) (xy 5.2564 0.8994) (xy 5.2576 0.8922) (xy 5.2586 0.8849) (xy 5.2591 0.8775)
						(xy 5.2593 0.8701) (xy 5.2593 0.3369) (xy 5.2591 0.3294) (xy 5.2586 0.322) (xy 5.2576 0.3147)
						(xy 5.2564 0.3076) (xy 5.2547 0.3005) (xy 5.2528 0.2936) (xy 5.2505 0.2869) (xy 5.2479 0.2803)
						(xy 5.245 0.2738) (xy 5.2418 0.2676) (xy 5.2383 0.2615) (xy 5.2345 0.2556) (xy 5.2304 0.2499)
						(xy 5.2261 0.2444) (xy 5.2215 0.2391) (xy 5.2167 0.234) (xy 5.2117 0.2292) (xy 5.2064 0.2247)
						(xy 5.2009 0.2203) (xy 5.1952 0.2163) (xy 5.1893 0.2125) (xy 5.1832 0.209) (xy 5.1769 0.2058)
						(xy 5.1705 0.2029) (xy 5.1639 0.2003) (xy 5.1571 0.198) (xy 5.1502 0.196) (xy 5.1432 0.1944) (xy 5.136 0.1931)
						(xy 5.1288 0.1922) (xy 5.1214 0.1916) (xy 5.1139 0.1914) (xy 5.0226 0.1914) (xy 4.5807 0.1914)
						(xy 3.8967 0.1914) (xy 3.4549 0.1914) (xy 3.3635 0.1914) (xy 3.3561 0.1916) (xy 3.3487 0.1922)
						(xy 3.3414 0.1931) (xy 3.3342 0.1944) (xy 3.3272 0.196) (xy 3.3203 0.198) (xy 3.3135 0.2003) (xy 3.3069 0.2029)
						(xy 3.3005 0.2058) (xy 3.2942 0.209) (xy 3.2881 0.2125) (xy 3.2822 0.2163) (xy 3.2765 0.2203)
						(xy 3.271 0.2247) (xy 3.2658 0.2292) (xy 3.2607 0.234) (xy 3.2559 0.2391) (xy 3.2513 0.2444) (xy 3.247 0.2499)
						(xy 3.243 0.2556) (xy 3.2392 0.2615) (xy 3.2357 0.2676) (xy 3.2325 0.2738) (xy 3.2295 0.2803)
						(xy 3.2269 0.2869) (xy 3.2247 0.2936) (xy 3.2227 0.3005) (xy 3.2211 0.3076) (xy 3.2198 0.3147)
						(xy 3.2189 0.322) (xy 3.2183 0.3294) (xy 3.2181 0.3369) (xy 3.2181 0.8701) (xy 3.2183 0.8775)
						(xy 3.2189 0.8849) (xy 3.2198 0.8922) (xy 3.2211 0.8994) (xy 3.2227 0.9064) (xy 3.2247 0.9133)
						(xy 3.2269 0.92) (xy 3.2295 0.9266) (xy 3.2325 0.9331) (xy 3.2357 0.9394) (xy 3.2392 0.9454) (xy 3.243 0.9513)
						(xy 3.247 0.9571) (xy 3.2513 0.9625) (xy 3.2559 0.9678) (xy 3.2607 0.9729) (xy 3.2658 0.9777)
						(xy 3.271 0.9823) (xy 3.2765 0.9866) (xy 3.2822 0.9906) (xy 3.2881 0.9944) (xy 3.2942 0.9979)
						(xy 3.3005 1.0011) (xy 3.3069 1.004) (xy 3.3135 1.0067) (xy 3.3203 1.0089) (xy 3.3272 1.0109)
						(xy 3.3342 1.0125) (xy 3.3414 1.0138) (xy 3.3487 1.0147) (xy 3.3561 1.0153) (xy 3.3635 1.0155)
						(xy 3.4549 1.0155) (xy 3.8967 1.0155) (xy 4.5807 1.0155) (xy 5.0226 1.0155) (xy 5.1139 1.0155)
						(xy 5.1214 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 -0.1965) (xy 5.1288 -0.1971) (xy 5.136 -0.198) (xy 5.1432 -0.1993) (xy 5.1502 -0.2009)
						(xy 5.1571 -0.2029) (xy 5.1639 -0.2052) (xy 5.1705 -0.2078) (xy 5.1769 -0.2107) (xy 5.1832 -0.2139)
						(xy 5.1893 -0.2174) (xy 5.1952 -0.2212) (xy 5.2009 -0.2253) (xy 5.2064 -0.2296) (xy 5.2117 -0.2341)
						(xy 5.2167 -0.239) (xy 5.2215 -0.244) (xy 5.2261 -0.2493) (xy 5.2304 -0.2548) (xy 5.2345 -0.2605)
						(xy 5.2383 -0.2664) (xy 5.2418 -0.2725) (xy 5.245 -0.2787) (xy 5.2479 -0.2852) (xy 5.2505 -0.2918)
						(xy 5.2528 -0.2985) (xy 5.2547 -0.3054) (xy 5.2564 -0.3125) (xy 5.2576 -0.3196) (xy 5.2586 -0.3269)
						(xy 5.2591 -0.3343) (xy 5.2593 -0.3418) (xy 5.2593 -0.875) (xy 5.2591 -0.8825) (xy 5.2586 -0.8898)
						(xy 5.2576 -0.8971) (xy 5.2564 -0.9043) (xy 5.2547 -0.9113) (xy 5.2528 -0.9182) (xy 5.2505 -0.925)
						(xy 5.2479 -0.9316) (xy 5.245 -0.938) (xy 5.2418 -0.9443) (xy 5.2383 -0.9504) (xy 5.2345 -0.9563)
						(xy 5.2304 -0.962) (xy 5.2261 -0.9675) (xy 5.2215 -0.9727) (xy 5.2167 -0.9778) (xy 5.2117 -0.9826)
						(xy 5.2064 -0.9872) (xy 5.2009 -0.9915) (xy 5.1952 -0.9956) (xy 5.1893 -0.9993) (xy 5.1832 -1.0028)
						(xy 5.1769 -1.006) (xy 5.1705 -1.009) (xy 5.1639 -1.0116) (xy 5.1571 -1.0139) (xy 5.1502 -1.0158)
						(xy 5.1432 -1.0174) (xy 5.136 -1.0187) (xy 5.1288 -1.0196) (xy 5.1214 -1.0202) (xy 5.1139 -1.0204)
						(xy 4.5807 -1.0204) (xy 4.5732 -1.0202) (xy 4.5658 -1.0196) (xy 4.5586 -1.0187) (xy 4.5514 -1.0174)
						(xy 4.5444 -1.0158) (xy 4.5375 -1.0139) (xy 4.5307 -1.0116) (xy 4.5241 -1.009) (xy 4.5177 -1.006)
						(xy 4.5114 -1.0028) (xy 4.5053 -0.9993) (xy 4.4994 -0.9956) (xy 4.4937 -0.9915) (xy 4.4882 -0.9872)
						(xy 4.4829 -0.9826) (xy 4.4779 -0.9778) (xy 4.4731 -0.9727) (xy 4.4685 -0.9675) (xy 4.4642 -0.962)
						(xy 4.4601 -0.9563) (xy 4.4563 -0.9504) (xy 4.4528 -0.9443) (xy 4.4496 -0.938) (xy 4.4467 -0.9316)
						(xy 4.4441 -0.925) (xy 4.4418 -0.9182) (xy 4.4399 -0.9113) (xy 4.4382 -0.9043) (xy 4.437 -0.8971)
						(xy 4.436 -0.8898) (xy 4.4355 -0.8825) (xy 4.4353 -0.875) (xy 4.4353 -0.3418) (xy 4.4355 -0.3343)
						(xy 4.436 -0.3269) (xy 4.437 -0.3196) (xy 4.4382 -0.3125) (xy 4.4399 -0.3054) (xy 4.4418 -0.2985)
						(xy 4.4441 -0.2918) (xy 4.4467 -0.2852) (xy 4.4496 -0.2787) (xy 4.4528 -0.2725) (xy 4.4563 -0.2664)
						(xy 4.4601 -0.2605) (xy 4.4642 -0.2548) (xy 4.4685 -0.2493) (xy 4.4731 -0.244) (xy 4.4779 -0.239)
						(xy 4.4829 -0.2341) (xy 4.4882 -0.2296) (xy 4.4937 -0.2253) (xy 4.4994 -0.2212) (xy 4.5053 -0.2174)
						(xy 4.5114 -0.2139) (xy 4.5177 -0.2107) (xy 4.5241 -0.2078) (xy 4.5307 -0.2052) (xy 4.5375 -0.2029)
						(xy 4.5444 -0.2009) (xy 4.5514 -0.1993) (xy 4.5586 -0.198) (xy 4.5658 -0.1971) (xy 4.5732 -0.1965)
						(xy 4.5807 -0.1964) (xy 5.1139 -0.1964) (xy 5.1214 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 -1.4084) (xy 5.1288 -1.4089) (xy 5.136 -1.4099) (xy 5.1432 -1.4111) (xy 5.1502 -1.4128)
						(xy 5.1571 -1.4147) (xy 5.1639 -1.417) (xy 5.1705 -1.4196) (xy 5.1769 -1.4225) (xy 5.1832 -1.4257)
						(xy 5.1893 -1.4292) (xy 5.1952 -1.433) (xy 5.2009 -1.4371) (xy 5.2064 -1.4414) (xy 5.2117 -1.446)
						(xy 5.2167 -1.4508) (xy 5.2215 -1.4558) (xy 5.2261 -1.4611) (xy 5.2304 -1.4666) (xy 5.2345 -1.4723)
						(xy 5.2383 -1.4782) (xy 5.2418 -1.4843) (xy 5.245 -1.4906) (xy 5.2479 -1.497) (xy 5.2505 -1.5036)
						(xy 5.2528 -1.5104) (xy 5.2547 -1.5173) (xy 5.2564 -1.5243) (xy 5.2576 -1.5315) (xy 5.2586 -1.5387)
						(xy 5.2591 -1.5461) (xy 5.2593 -1.5536) (xy 5.2593 -2.0868) (xy 5.2591 -2.0943) (xy 5.2586 -2.1017)
						(xy 5.2576 -2.1089) (xy 5.2564 -2.1161) (xy 5.2547 -2.1231) (xy 5.2528 -2.13) (xy 5.2505 -2.1368)
						(xy 5.2479 -2.1434) (xy 5.245 -2.1498) (xy 5.2418 -2.1561) (xy 5.2383 -2.1622) (xy 5.2345 -2.1681)
						(xy 5.2304 -2.1738) (xy 5.2261 -2.1793) (xy 5.2215 -2.1846) (xy 5.2167 -2.1896) (xy 5.2117 -2.1944)
						(xy 5.2064 -2.199) (xy 5.2009 -2.2033) (xy 5.1952 -2.2074) (xy 5.1893 -2.2112) (xy 5.1832 -2.2147)
						(xy 5.1769 -2.2179) (xy 5.1705 -2.2208) (xy 5.1639 -2.2234) (xy 5.1571 -2.2257) (xy 5.1502 -2.2276)
						(xy 5.1432 -2.2293) (xy 5.136 -2.2306) (xy 5.1288 -2.2315) (xy 5.1214 -2.232) (xy 5.1139 -2.2322)
						(xy 5.0226 -2.2322) (xy 4.5807 -2.2322) (xy 3.8967 -2.2322) (xy 3.4549 -2.2322) (xy 3.3635 -2.2322)
						(xy 3.3561 -2.232) (xy 3.3487 -2.2315) (xy 3.3414 -2.2306) (xy 3.3342 -2.2293) (xy 3.3272 -2.2276)
						(xy 3.3203 -2.2257) (xy 3.3135 -2.2234) (xy 3.3069 -2.2208) (xy 3.3005 -2.2179) (xy 3.2942 -2.2147)
						(xy 3.2881 -2.2112) (xy 3.2822 -2.2074) (xy 3.2765 -2.2033) (xy 3.271 -2.199) (xy 3.2658 -2.1944)
						(xy 3.2607 -2.1896) (xy 3.2559 -2.1846) (xy 3.2513 -2.1793) (xy 3.247 -2.1738) (xy 3.243 -2.1681)
						(xy 3.2392 -2.1622) (xy 3.2357 -2.1561) (xy 3.2325 -2.1498) (xy 3.2295 -2.1434) (xy 3.2269 -2.1368)
						(xy 3.2247 -2.13) (xy 3.2227 -2.1231) (xy 3.2211 -2.1161) (xy 3.2198 -2.1089) (xy 3.2189 -2.1017)
						(xy 3.2183 -2.0943) (xy 3.2181 -2.0868) (xy 3.2181 -1.5536) (xy 3.2183 -1.5461) (xy 3.2189 -1.5387)
						(xy 3.2198 -1.5315) (xy 3.2211 -1.5243) (xy 3.2227 -1.5173) (xy 3.2247 -1.5104) (xy 3.2269 -1.5036)
						(xy 3.2295 -1.497) (xy 3.2325 -1.4906) (xy 3.2357 -1.4843) (xy 3.2392 -1.4782) (xy 3.243 -1.4723)
						(xy 3.247 -1.4666) (xy 3.2513 -1.4611) (xy 3.2559 -1.4558) (xy 3.2607 -1.4508) (xy 3.2658 -1.446)
						(xy 3.271 -1.4414) (xy 3.2765 -1.4371) (xy 3.2822 -1.433) (xy 3.2881 -1.4292) (xy 3.2942 -1.4257)
						(xy 3.3005 -1.4225) (xy 3.3069 -1.4196) (xy 3.3135 -1.417) (xy 3.3203 -1.4147) (xy 3.3272 -1.4128)
						(xy 3.3342 -1.4111) (xy 3.3414 -1.4099) (xy 3.3487 -1.4089) (xy 3.3561 -1.4084) (xy 3.3635 -1.4082)
						(xy 3.4549 -1.4082) (xy 3.8967 -1.4082) (xy 4.5807 -1.4082) (xy 5.0226 -1.4082) (xy 5.1139 -1.4082)
						(xy 5.1214 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 -2.6029) (xy 5.1288 -2.6035) (xy 5.136 -2.6044) (xy 5.1432 -2.6057) (xy 5.1502 -2.6073)
						(xy 5.1571 -2.6093) (xy 5.1639 -2.6115) (xy 5.1705 -2.6141) (xy 5.1769 -2.6171) (xy 5.1832 -2.6203)
						(xy 5.1893 -2.6238) (xy 5.1952 -2.6276) (xy 5.2009 -2.6316) (xy 5.2064 -2.6359) (xy 5.2117 -2.6405)
						(xy 5.2167 -2.6453) (xy 5.2215 -2.6504) (xy 5.2261 -2.6556) (xy 5.2304 -2.6611) (xy 5.2345 -2.6668)
						(xy 5.2383 -2.6727) (xy 5.2418 -2.6788) (xy 5.245 -2.6851) (xy 5.2479 -2.6915) (xy 5.2505 -2.6981)
						(xy 5.2528 -2.7049) (xy 5.2547 -2.7118) (xy 5.2564 -2.7188) (xy 5.2576 -2.726) (xy 5.2586 -2.7333)
						(xy 5.2591 -2.7406) (xy 5.2593 -2.7481) (xy 5.2593 -3.2813) (xy 5.2591 -3.2888) (xy 5.2586 -3.2962)
						(xy 5.2576 -3.3035) (xy 5.2564 -3.3106) (xy 5.2547 -3.3177) (xy 5.2528 -3.3246) (xy 5.2505 -3.3313)
						(xy 5.2479 -3.3379) (xy 5.245 -3.3444) (xy 5.2418 -3.3506) (xy 5.2383 -3.3567) (xy 5.2345 -3.3626)
						(xy 5.2304 -3.3683) (xy 5.2261 -3.3738) (xy 5.2215 -3.3791) (xy 5.2167 -3.3841) (xy 5.2117 -3.389)
						(xy 5.2064 -3.3935) (xy 5.2009 -3.3979) (xy 5.1952 -3.4019) (xy 5.1893 -3.4057) (xy 5.1832 -3.4092)
						(xy 5.1769 -3.4124) (xy 5.1705 -3.4153) (xy 5.1639 -3.4179) (xy 5.1571 -3.4202) (xy 5.1502 -3.4222)
						(xy 5.1432 -3.4238) (xy 5.136 -3.4251) (xy 5.1288 -3.426) (xy 5.1214 -3.4266) (xy 5.1139 -3.4268)
						(xy 4.5807 -3.4268) (xy 4.5732 -3.4266) (xy 4.5658 -3.426) (xy 4.5586 -3.4251) (xy 4.5514 -3.4238)
						(xy 4.5444 -3.4222) (xy 4.5375 -3.4202) (xy 4.5307 -3.4179) (xy 4.5241 -3.4153) (xy 4.5177 -3.4124)
						(xy 4.5114 -3.4092) (xy 4.5053 -3.4057) (xy 4.4994 -3.4019) (xy 4.4937 -3.3979) (xy 4.4882 -3.3935)
						(xy 4.4829 -3.389) (xy 4.4779 -3.3841) (xy 4.4731 -3.3791) (xy 4.4685 -3.3738) (xy 4.4642 -3.3683)
						(xy 4.4601 -3.3626) (xy 4.4563 -3.3567) (xy 4.4528 -3.3506) (xy 4.4496 -3.3444) (xy 4.4467 -3.3379)
						(xy 4.4441 -3.3313) (xy 4.4418 -3.3246) (xy 4.4399 -3.3177) (xy 4.4382 -3.3106) (xy 4.437 -3.3035)
						(xy 4.436 -3.2962) (xy 4.4355 -3.2888) (xy 4.4353 -3.2813) (xy 4.4353 -2.7481) (xy 4.4355 -2.7406)
						(xy 4.436 -2.7333) (xy 4.437 -2.726) (xy 4.4382 -2.7188) (xy 4.4399 -2.7118) (xy 4.4418 -2.7049)
						(xy 4.4441 -2.6981) (xy 4.4467 -2.6915) (xy 4.4496 -2.6851) (xy 4.4528 -2.6788) (xy 4.4563 -2.6727)
						(xy 4.4601 -2.6668) (xy 4.4642 -2.6611) (xy 4.4685 -2.6556) (xy 4.4731 -2.6504) (xy 4.4779 -2.6453)
						(xy 4.4829 -2.6405) (xy 4.4882 -2.6359) (xy 4.4937 -2.6316) (xy 4.4994 -2.6276) (xy 4.5053 -2.6238)
						(xy 4.5114 -2.6203) (xy 4.5177 -2.6171) (xy 4.5241 -2.6141) (xy 4.5307 -2.6115) (xy 4.5375 -2.6093)
						(xy 4.5444 -2.6073) (xy 4.5514 -2.6057) (xy 4.5586 -2.6044) (xy 4.5658 -2.6035) (xy 4.5732 -2.6029)
						(xy 4.5807 -2.6027) (xy 5.1139 -2.6027) (xy 5.1214 -2.6029)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 -2.6202) (xy 5.1288 -2.6208) (xy 5.136 -2.6217) (xy 5.1432 -2.623) (xy 5.1502 -2.6246)
						(xy 5.1571 -2.6266) (xy 5.1639 -2.6288) (xy 5.1705 -2.6314) (xy 5.1769 -2.6344) (xy 5.1832 -2.6376)
						(xy 5.1893 -2.6411) (xy 5.1952 -2.6449) (xy 5.2009 -2.6489) (xy 5.2064 -2.6532) (xy 5.2117 -2.6578)
						(xy 5.2167 -2.6626) (xy 5.2215 -2.6677) (xy 5.2261 -2.673) (xy 5.2304 -2.6784) (xy 5.2345 -2.6841)
						(xy 5.2383 -2.69) (xy 5.2418 -2.6961) (xy 5.245 -2.7024) (xy 5.2479 -2.7088) (xy 5.2505 -2.7155)
						(xy 5.2528 -2.7222) (xy 5.2547 -2.7291) (xy 5.2564 -2.7361) (xy 5.2576 -2.7433) (xy 5.2586 -2.7506)
						(xy 5.2591 -2.758) (xy 5.2593 -2.7654) (xy 5.2593 -3.2986) (xy 5.2591 -3.3061) (xy 5.2586 -3.3135)
						(xy 5.2576 -3.3208) (xy 5.2564 -3.3279) (xy 5.2547 -3.335) (xy 5.2528 -3.3419) (xy 5.2505 -3.3486)
						(xy 5.2479 -3.3552) (xy 5.245 -3.3617) (xy 5.2418 -3.3679) (xy 5.2383 -3.374) (xy 5.2345 -3.3799)
						(xy 5.2304 -3.3856) (xy 5.2261 -3.3911) (xy 5.2215 -3.3964) (xy 5.2167 -3.4015) (xy 5.2117 -3.4063)
						(xy 5.2064 -3.4108) (xy 5.2009 -3.4152) (xy 5.1952 -3.4192) (xy 5.1893 -3.423) (xy 5.1832 -3.4265)
						(xy 5.1769 -3.4297) (xy 5.1705 -3.4326) (xy 5.1639 -3.4352) (xy 5.1571 -3.4375) (xy 5.1502 -3.4395)
						(xy 5.1432 -3.4411) (xy 5.136 -3.4424) (xy 5.1288 -3.4433) (xy 5.1214 -3.4439) (xy 5.1139 -3.4441)
						(xy 4.5807 -3.4441) (xy 4.5732 -3.4439) (xy 4.5658 -3.4433) (xy 4.5586 -3.4424) (xy 4.5514 -3.4411)
						(xy 4.5444 -3.4395) (xy 4.5375 -3.4375) (xy 4.5307 -3.4352) (xy 4.5241 -3.4326) (xy 4.5177 -3.4297)
						(xy 4.5114 -3.4265) (xy 4.5053 -3.423) (xy 4.4994 -3.4192) (xy 4.4937 -3.4152) (xy 4.4882 -3.4108)
						(xy 4.4829 -3.4063) (xy 4.4779 -3.4015) (xy 4.4731 -3.3964) (xy 4.4685 -3.3911) (xy 4.4642 -3.3856)
						(xy 4.4601 -3.3799) (xy 4.4563 -3.374) (xy 4.4528 -3.3679) (xy 4.4496 -3.3617) (xy 4.4467 -3.3552)
						(xy 4.4441 -3.3486) (xy 4.4418 -3.3419) (xy 4.4399 -3.335) (xy 4.4382 -3.3279) (xy 4.437 -3.3208)
						(xy 4.436 -3.3135) (xy 4.4355 -3.3061) (xy 4.4353 -3.2986) (xy 4.4353 -2.7654) (xy 4.4355 -2.758)
						(xy 4.436 -2.7506) (xy 4.437 -2.7433) (xy 4.4382 -2.7361) (xy 4.4399 -2.7291) (xy 4.4418 -2.7222)
						(xy 4.4441 -2.7155) (xy 4.4467 -2.7088) (xy 4.4496 -2.7024) (xy 4.4528 -2.6961) (xy 4.4563 -2.69)
						(xy 4.4601 -2.6841) (xy 4.4642 -2.6784) (xy 4.4685 -2.673) (xy 4.4731 -2.6677) (xy 4.4779 -2.6626)
						(xy 4.4829 -2.6578) (xy 4.4882 -2.6532) (xy 4.4937 -2.6489) (xy 4.4994 -2.6449) (xy 4.5053 -2.6411)
						(xy 4.5114 -2.6376) (xy 4.5177 -2.6344) (xy 4.5241 -2.6314) (xy 4.5307 -2.6288) (xy 4.5375 -2.6266)
						(xy 4.5444 -2.6246) (xy 4.5514 -2.623) (xy 4.5586 -2.6217) (xy 4.5658 -2.6208) (xy 4.5732 -2.6202)
						(xy 4.5807 -2.62) (xy 5.1139 -2.62) (xy 5.1214 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Memory_Flash:W25Q32JVZP"
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at -6.35 11.43 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "W25Q32JVZP"
				(at 7.62 11.43 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" "Package_SON:WSON-8-1EP_6x5mm_P1.27mm_EP3.4x4.3mm"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "http://www.winbond.com/resource-files/w25q32jv%20revg%2003272018%20plus.pdf"
				(at 0 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "32Mbit / 4MiB Serial Flash Memory, Standard/Dual/Quad SPI, 2.7-3.6V, WSON-8"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "flash memory SPI"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "WSON*1EP*6x5mm*P1.27mm*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "W25Q32JVZP_0_1"
				(rectangle
					(start -7.62 10.16)
					(end 10.16 -10.16)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "W25Q32JVZP_1_1"
				(pin input line
					(at -10.16 7.62 0)
					(length 2.54)
					(name "~{CS}"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at -10.16 5.08 0)
					(length 2.54)
					(name "CLK"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -10.16 2.54 0)
					(length 2.54)
					(name "DI/IO_{0}"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -10.16 0 0)
					(length 2.54)
					(name "DO/IO_{1}"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -10.16 -2.54 0)
					(length 2.54)
					(name "~{WP}/IO_{2}"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -10.16 -5.08 0)
					(length 2.54)
					(name "~{HOLD}/~{RESET}/IO_{3}"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 0 12.7 270)
					(length 2.54)
					(name "VCC"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 0 -12.7 90)
					(length 2.54)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 -12.7 90)
					(length 2.54)
					(name "EP"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Switch:SW_Push"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 1.016)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "SW"
				(at 1.27 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "SW_Push"
				(at 0 -1.524 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 5.08 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 5.08 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Push button switch, generic, two pins"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "switch normally-open pushbutton push-button"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "SW_Push_0_1"
				(circle
					(center -2.032 0)
					(radius 0.508)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 1.27) (xy 0 3.048)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 2.032 0)
					(radius 0.508)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 2.54 1.27) (xy -2.54 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(pin passive line
					(at -5.08 0 0)
					(length 2.54)
					(name "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 0 180)
					(length 2.54)
					(name "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "nordic-lib-kicad-nrf54-modules:BM15x"
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at 0 0 0)
				(do_not_autoplace)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "BM15x"
				(at 0 -2.54 0)
				(do_not_autoplace)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" "nordic-lib-kicad-nrf54-modules:BM15x-LGA-45_15.8x10mm"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "https://static1.squarespace.com/static/561459a2e4b0b39f5cefa12e/t/66c3d7591cc204465c3f6e4f/1724110684125/BM15C+Product+Specifications.pdf"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "nRF54L15"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "BM15x_0_0"
				(pin bidirectional line
					(at -20.32 33.02 0)
					(length 2.54)
					(name "P1.00/XL1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "D8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 30.48 0)
					(length 2.54)
					(name "P1.01/XL2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "C9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 27.94 0)
					(length 2.54)
					(name "P1.02/NFC1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "D9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 25.4 0)
					(length 2.54)
					(name "P1.03/NFC2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "E8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 22.86 0)
					(length 2.54)
					(name "P1.04/AIN0"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "E9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 20.32 0)
					(length 2.54)
					(name "P1.05/AIN1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "F8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 17.78 0)
					(length 2.54)
					(name "P1.06/AIN2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "F9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 15.24 0)
					(length 2.54)
					(name "P1.07/AIN3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "G9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 12.7 0)
					(length 2.54)
					(name "P1.08"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "G8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 10.16 0)
					(length 2.54)
					(name "P1.09"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "A3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 7.62 0)
					(length 2.54)
					(name "P1.10"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 5.08 0)
					(length 2.54)
					(name "P1.11/AIN4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 2.54 0)
					(length 2.54)
					(name "P1.12/AIN5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 0 0)
					(length 2.54)
					(name "P1.13/AIN6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 -2.54 0)
					(length 2.54)
					(name "P1.14/AIN7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 -7.62 0)
					(length 2.54)
					(name "P2.00"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "H9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 -10.16 0)
					(length 2.54)
					(name "P2.01"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "J9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 -12.7 0)
					(length 2.54)
					(name "P2.02"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "J8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 -15.24 0)
					(length 2.54)
					(name "P2.03"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "J7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 -17.78 0)
					(length 2.54)
					(name "P2.04"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "K9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 -20.32 0)
					(length 2.54)
					(name "P2.05"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "K8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 -22.86 0)
					(length 2.54)
					(name "P2.06"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "K7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 -25.4 0)
					(length 2.54)
					(name "P2.07"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "J6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 -27.94 0)
					(length 2.54)
					(name "P2.08"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "K6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 -30.48 0)
					(length 2.54)
					(name "P2.09"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "J5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -20.32 -33.02 0)
					(length 2.54)
					(name "P2.10"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "K5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 38.1 270)
					(length 2.54)
					(hide yes)
					(name "VDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "C8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -38.1 90)
					(length 2.54)
					(hide yes)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "A9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -38.1 90)
					(length 2.54)
					(hide yes)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -38.1 90)
					(length 2.54)
					(hide yes)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -38.1 90)
					(length 2.54)
					(hide yes)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -38.1 90)
					(length 2.54)
					(hide yes)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "D1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -38.1 90)
					(length 2.54)
					(hide yes)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "E1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -38.1 90)
					(length 2.54)
					(hide yes)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "F1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -38.1 90)
					(length 2.54)
					(hide yes)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "H8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 20.32 33.02 180)
					(length 2.54)
					(name "P0.00"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "J4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 20.32 30.48 180)
					(length 2.54)
					(name "P0.01"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "K4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 20.32 27.94 180)
					(length 2.54)
					(name "P0.02"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "K2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 20.32 25.4 180)
					(length 2.54)
					(name "P0.03"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "J2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 20.32 22.86 180)
					(length 2.54)
					(name "P0.04"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "H2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at 20.32 -27.94 180)
					(length 2.54)
					(name "SWDIO"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "J3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at 20.32 -30.48 180)
					(length 2.54)
					(name "SWDCLK"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "K3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at 20.32 -33.02 180)
					(length 2.54)
					(name "~{RESET}"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "G2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "BM15x_1_0"
				(pin power_in line
					(at 0 38.1 270)
					(length 2.54)
					(name "VDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 0 -38.1 90)
					(length 2.54)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "A1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "BM15x_1_1"
				(rectangle
					(start -17.78 35.56)
					(end 17.78 -35.56)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:GND"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "GND"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "GND_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "GND_1_1"
				(pin power_in line
					(at 0 0 270)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:VDD"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "VDD"
				(at 0 3.556 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"VDD\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VDD_0_1"
				(polyline
					(pts
						(xy -0.762 1.27) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 2.54) (xy 0.762 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "VDD_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
	)
	(junction
		(at 237.49 95.25)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "1808a0b9-9c26-4904-9e01-e6b60ee4e7d2")
	)
	(junction
		(at 137.16 43.18)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "*************-46ff-9f89-b2c6fe8886ac")
	)
	(junction
		(at 90.17 69.85)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "415e9e4d-0ab6-4432-bd6a-a82d34b938e8")
	)
	(junction
		(at 96.52 62.23)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "4809da64-85df-4014-9ff5-dde1f143b9d8")
	)
	(junction
		(at 63.5 118.11)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "4d4fb46a-9d34-4df8-bace-27d6ce15d1cc")
	)
	(junction
		(at 83.82 64.77)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "6474d86c-62e4-42f0-9e35-447628a7a68d")
	)
	(junction
		(at 57.15 80.01)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "7789126a-3b99-47f1-a892-3f8f267a34f9")
	)
	(junction
		(at 173.99 123.19)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "981a9dfc-452a-45b0-971d-4754ce4bb00e")
	)
	(junction
		(at 173.99 118.11)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "bb17a860-50ce-4c4d-b758-c1f72c905ef4")
	)
	(junction
		(at 158.75 123.19)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "db9ad6aa-036b-4241-b391-2260aca05fd8")
	)
	(junction
		(at 158.75 133.35)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "e10131bc-97fc-4d4c-b9f4-ca317836967e")
	)
	(junction
		(at 173.99 120.65)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "eab5ff0f-7b01-4cc5-8de5-48c765360f5f")
	)
	(junction
		(at 180.34 59.69)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "ed230232-6966-410d-8961-65a67aef69b7")
	)
	(no_connect
		(at 81.28 58.42)
		(uuid "227ad91a-e7f5-4983-a2b0-7768a41db083")
	)
	(no_connect
		(at 81.28 63.5)
		(uuid "4b2b3c98-a050-402e-b32a-0b328f75b369")
	)
	(no_connect
		(at 81.28 68.58)
		(uuid "8e47e16a-1ebe-4783-91ff-92fb4bf891f7")
	)
	(bus_entry
		(at 254 46.99)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "088d049e-da2f-4cc8-aa19-8b6973a7576a")
	)
	(bus_entry
		(at 251.46 40.64)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0ce5df07-7a5c-4c3b-8a2d-b08963cfa78d")
	)
	(bus_entry
		(at 254 57.15)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "15cc4498-72bd-45c6-9d8a-9cddf099372d")
	)
	(bus_entry
		(at 254 102.87)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "197c2bd1-4022-45c6-9872-cbff15a458cf")
	)
	(bus_entry
		(at 254 67.31)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1fc366eb-7b2e-4799-b6c2-549fd94f7885")
	)
	(bus_entry
		(at 251.46 35.56)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2ec889be-305a-44c2-8909-46f64a718fb9")
	)
	(bus_entry
		(at 179.07 123.19)
		(size 2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "37d9e8a2-37e0-4e19-a688-453209f7dc75")
	)
	(bus_entry
		(at 254 85.09)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4c13715d-2fd9-4e69-a8de-ee87a67b19a9")
	)
	(bus_entry
		(at 254 72.39)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "641a00c0-1003-4552-88a3-38b5cdbf03ef")
	)
	(bus_entry
		(at 254 100.33)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "709aa5ef-3d02-4506-8632-7e41e83e06ae")
	)
	(bus_entry
		(at 254 69.85)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "73ba038d-3e5d-4984-b8e6-72383a0bb1cd")
	)
	(bus_entry
		(at 251.46 113.03)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "77198faa-0233-4263-9bb7-1ed2ff225c10")
	)
	(bus_entry
		(at 179.07 120.65)
		(size 2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "81471702-03c3-4651-a582-31151ab1679b")
	)
	(bus_entry
		(at 251.46 110.49)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "84477bef-d562-4c47-b512-473ba532079c")
	)
	(bus_entry
		(at 254 77.47)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "91566808-ba06-48a9-91be-10ffe98d60a0")
	)
	(bus_entry
		(at 254 64.77)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9e55b993-715d-4aba-8f0a-21c3a3caec63")
	)
	(bus_entry
		(at 254 52.07)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a90a8613-59a2-48aa-a38e-2826a15d4fd0")
	)
	(bus_entry
		(at 254 74.93)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b28aa47d-de11-435c-8e70-a8aeff23286a")
	)
	(bus_entry
		(at 254 59.69)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "c96c1b9d-0349-4465-bbe1-c514dc12f03a")
	)
	(bus_entry
		(at 254 82.55)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cb375355-1d9d-49d3-afbe-6063fda9ce53")
	)
	(bus_entry
		(at 251.46 38.1)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ce5782ed-3dc5-40e9-b6e6-d4ddf3398331")
	)
	(bus_entry
		(at 254 49.53)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "dec26957-285d-4aa5-8904-5912f51b46a5")
	)
	(bus_entry
		(at 254 54.61)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e55352ba-59ee-44c2-9e9b-2e9ff3bf3a50")
	)
	(bus_entry
		(at 179.07 118.11)
		(size 2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ecf3ee9f-073c-4fe3-a525-e46cb4dd498d")
	)
	(bus_entry
		(at 251.46 115.57)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f723cf2a-2723-4af3-9055-d5cb8a7d148b")
	)
	(bus_entry
		(at 254 44.45)
		(size -2.54 2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "fc7d006c-0412-4a48-9f5e-dfa40f0a4230")
	)
	(wire
		(pts
			(xy 157.48 41.91) (xy 167.64 41.91)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "005ae2a7-a318-4cd3-8c2e-26b656147aa0")
	)
	(wire
		(pts
			(xy 116.84 54.61) (xy 107.95 54.61)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0524935d-3de3-4578-bdb3-ad5414679e83")
	)
	(wire
		(pts
			(xy 251.46 40.64) (xy 250.19 40.64)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "06ffa5f1-da07-48e5-bdee-21ca260d63ba")
	)
	(wire
		(pts
			(xy 250.19 113.03) (xy 251.46 113.03)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0ae1ad01-9f78-42c2-8d4e-4b60bfca6101")
	)
	(wire
		(pts
			(xy 251.46 59.69) (xy 250.19 59.69)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0bf85a1f-32e5-4670-9729-06843053f076")
	)
	(wire
		(pts
			(xy 250.19 49.53) (xy 251.46 49.53)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0fba3a4e-a90d-4360-b8e3-a860f7eedc15")
	)
	(bus
		(pts
			(xy 254 44.45) (xy 254 46.99)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "10a92ed9-7041-438b-9b0a-66f954cbdafd")
	)
	(wire
		(pts
			(xy 173.99 120.65) (xy 179.07 120.65)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "12cfce8a-01ad-429c-9c8f-93ab0257c46b")
	)
	(bus
		(pts
			(xy 254 67.31) (xy 254 69.85)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "134deaf6-f2ef-4608-b455-e7ada28b4d7a")
	)
	(wire
		(pts
			(xy 157.48 67.31) (xy 181.61 67.31)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "142d2e84-531d-4b7c-aebe-5473c0c52d45")
	)
	(wire
		(pts
			(xy 250.19 69.85) (xy 251.46 69.85)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1673d5b4-bce1-48e1-bf79-0fe7cfdca318")
	)
	(wire
		(pts
			(xy 236.22 105.41) (xy 251.46 105.41)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "16f68cc5-46e2-432c-8bdb-b02527c2e223")
	)
	(wire
		(pts
			(xy 251.46 57.15) (xy 250.19 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "19bb2c4a-366b-4817-ab0c-591d0764b965")
	)
	(wire
		(pts
			(xy 157.48 120.65) (xy 173.99 120.65)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1a1cbe9f-b6ea-46bc-8a6f-69769ecb033d")
	)
	(wire
		(pts
			(xy 81.28 60.96) (xy 81.28 62.23)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1fb30246-1e79-43ed-88a8-f26202e0c951")
	)
	(wire
		(pts
			(xy 173.99 118.11) (xy 179.07 118.11)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "20efb515-2d8b-4610-bddf-8fb2cd027cd9")
	)
	(wire
		(pts
			(xy 158.75 123.19) (xy 173.99 123.19)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2bdae38b-9760-4ea3-a6eb-3c144837dccb")
	)
	(bus
		(pts
			(xy 254 35.56) (xy 254 33.02)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2bffd8e5-7bf9-4417-9f73-1b2d850289d4")
	)
	(wire
		(pts
			(xy 250.19 74.93) (xy 251.46 74.93)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2f6aebb0-63ce-4b32-ac8a-ff0f29773009")
	)
	(bus
		(pts
			(xy 254 69.85) (xy 254 72.39)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "33605ca0-b150-405e-87ec-cc8376ffb0bb")
	)
	(wire
		(pts
			(xy 137.16 43.18) (xy 137.16 52.07)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "346d80e7-2d9e-433c-97e8-50ec633fe948")
	)
	(wire
		(pts
			(xy 96.52 67.31) (xy 96.52 69.85)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "3ab910dc-8705-4666-adfc-5b8d6e27682a")
	)
	(wire
		(pts
			(xy 181.61 67.31) (xy 181.61 64.77)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "3af06a99-1c25-4260-a41b-1f94f2a18b04")
	)
	(wire
		(pts
			(xy 250.19 85.09) (xy 251.46 85.09)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4144d11e-ca77-481f-8e20-d3ae7d8ad152")
	)
	(wire
		(pts
			(xy 157.48 64.77) (xy 179.07 64.77)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "437e94c4-b658-4ada-b135-f47154477be3")
	)
	(bus
		(pts
			(xy 254 64.77) (xy 254 67.31)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "44a4de79-80fa-4ddd-be86-015193305d31")
	)
	(wire
		(pts
			(xy 251.46 35.56) (xy 250.19 35.56)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "506199fa-e6d9-44d6-bd25-115c4f0b0f48")
	)
	(wire
		(pts
			(xy 250.19 87.63) (xy 251.46 87.63)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "582cd8b4-3177-484a-a277-b941f17724cc")
	)
	(bus
		(pts
			(xy 181.61 123.19) (xy 181.61 125.73)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "597c9427-79d0-4c93-8c2a-785443e65a6b")
	)
	(wire
		(pts
			(xy 179.07 59.69) (xy 180.34 59.69)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "59d81eac-ef85-46f4-945b-b17b476c8fe3")
	)
	(wire
		(pts
			(xy 250.19 67.31) (xy 251.46 67.31)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5ce5e8d0-42e4-43cb-a078-16902b26bf54")
	)
	(wire
		(pts
			(xy 158.75 123.19) (xy 157.48 123.19)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "685decc4-a978-4ed3-b067-def92147e7cb")
	)
	(wire
		(pts
			(xy 132.08 43.18) (xy 137.16 43.18)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "68cf8420-f9cd-4a09-87c8-12fde35d39a6")
	)
	(bus
		(pts
			(xy 254 72.39) (xy 254 74.93)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6f196ef1-4cab-49bd-aed1-711f2aa31a32")
	)
	(wire
		(pts
			(xy 250.19 72.39) (xy 251.46 72.39)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "749d746b-0bed-43a9-9fe3-874e12606064")
	)
	(wire
		(pts
			(xy 63.5 80.01) (xy 63.5 92.71)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "810805fe-de1f-4329-953f-9989bb493341")
	)
	(wire
		(pts
			(xy 250.19 110.49) (xy 251.46 110.49)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "8296408e-37a8-4c7c-8f8d-e2784219f48b")
	)
	(bus
		(pts
			(xy 181.61 120.65) (xy 181.61 123.19)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "82edfd6a-d18b-4807-b653-7df124512c4d")
	)
	(wire
		(pts
			(xy 81.28 62.23) (xy 96.52 62.23)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "88309b03-ffba-426b-86eb-ac9f2f0ac938")
	)
	(bus
		(pts
			(xy 254 100.33) (xy 254 102.87)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "92b62be0-c6a2-40d3-a3b2-367060f1e502")
	)
	(wire
		(pts
			(xy 238.76 102.87) (xy 251.46 102.87)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "985fcea6-97ec-4004-8f19-7578de950314")
	)
	(wire
		(pts
			(xy 250.19 77.47) (xy 251.46 77.47)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9acb4e79-205e-4732-93e1-0f360d472907")
	)
	(bus
		(pts
			(xy 254 46.99) (xy 254 49.53)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9c4b4221-494d-49eb-8d88-a541af547b83")
	)
	(wire
		(pts
			(xy 250.19 52.07) (xy 251.46 52.07)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9d3206a4-6978-43b6-bf0c-e67c182eee58")
	)
	(wire
		(pts
			(xy 173.99 123.19) (xy 179.07 123.19)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9fa3c525-a2fb-42d0-848e-17289fd5e261")
	)
	(wire
		(pts
			(xy 81.28 64.77) (xy 81.28 66.04)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a028613c-ffa2-4013-8583-e83db8b2252b")
	)
	(bus
		(pts
			(xy 254 110.49) (xy 254 113.03)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a31bc541-09a2-4ec0-b974-dfb8c62c9026")
	)
	(wire
		(pts
			(xy 158.75 133.35) (xy 161.29 133.35)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a366f6f8-4fc7-404e-9a88-601476a1e39e")
	)
	(wire
		(pts
			(xy 157.48 118.11) (xy 173.99 118.11)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a82cf566-4dc0-413d-9e33-ce794e72d627")
	)
	(bus
		(pts
			(xy 254 54.61) (xy 254 52.07)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a82dc4c1-4b32-43d4-a4b1-6c46f58bda8d")
	)
	(wire
		(pts
			(xy 157.48 41.91) (xy 157.48 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "aa73a214-2596-4ea0-a6c9-9802318b54eb")
	)
	(bus
		(pts
			(xy 254 74.93) (xy 254 77.47)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "aac67f82-d63c-4eea-9938-d9940aa150fa")
	)
	(wire
		(pts
			(xy 107.95 59.69) (xy 116.84 59.69)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "af5b66b9-7ec2-4b5b-9a5b-fd44af192e84")
	)
	(wire
		(pts
			(xy 237.49 95.25) (xy 238.76 95.25)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b0c7fd9a-d400-494c-b721-e5cbc246cc07")
	)
	(wire
		(pts
			(xy 57.15 80.01) (xy 63.5 80.01)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b5308f70-**************-954e114487a1")
	)
	(wire
		(pts
			(xy 63.5 118.11) (xy 60.96 118.11)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "bc63b349-afd0-462d-a554-f8269e8ad02e")
	)
	(bus
		(pts
			(xy 254 38.1) (xy 254 35.56)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "be0e7ca6-4078-479b-b976-fb59c1a27de1")
	)
	(bus
		(pts
			(xy 254 57.15) (xy 254 59.69)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "be418758-fc55-49db-abc4-70fb85ac5e33")
	)
	(wire
		(pts
			(xy 83.82 64.77) (xy 81.28 64.77)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "c8bea804-b1e7-41fd-b2e5-ec52d4c06da5")
	)
	(wire
		(pts
			(xy 116.84 54.61) (xy 116.84 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cafe07a7-2ccd-4182-866b-5faed0a55ee8")
	)
	(bus
		(pts
			(xy 254 107.95) (xy 254 110.49)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cc89084f-5fc8-4d27-89d1-5dc5b56c40ca")
	)
	(wire
		(pts
			(xy 180.34 59.69) (xy 181.61 59.69)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cd6a84a5-adfa-459a-8f7c-e01ce208aa41")
	)
	(bus
		(pts
			(xy 254 82.55) (xy 254 85.09)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d3e2ff84-175b-42b6-b44b-bc4fdbf6eddd")
	)
	(wire
		(pts
			(xy 251.46 62.23) (xy 250.19 62.23)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "da3fc1cb-7243-406a-a25a-2105bacc7646")
	)
	(wire
		(pts
			(xy 236.22 105.41) (xy 236.22 100.33)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ddc60004-875d-4eec-9e85-5d21ae2aa5a8")
	)
	(wire
		(pts
			(xy 96.52 62.23) (xy 116.84 62.23)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "decaebc8-e902-4b11-a347-d5fe3e91e0db")
	)
	(wire
		(pts
			(xy 250.19 115.57) (xy 251.46 115.57)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "df9b610b-a618-46f0-98e9-111cac50ba02")
	)
	(bus
		(pts
			(xy 254 49.53) (xy 254 52.07)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e76177e0-b9e5-4654-b33c-eb67d492df40")
	)
	(bus
		(pts
			(xy 254 54.61) (xy 254 57.15)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e871aabb-f3a9-47c7-95d8-72aaa1fa6d89")
	)
	(wire
		(pts
			(xy 251.46 38.1) (xy 250.19 38.1)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e894edf3-8d55-4b52-aff6-ad50078fc9a4")
	)
	(wire
		(pts
			(xy 93.98 113.03) (xy 116.84 113.03)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e91f4dd3-69f9-4c19-a109-2ae8ecbd6de5")
	)
	(wire
		(pts
			(xy 250.19 46.99) (xy 251.46 46.99)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e9ec1608-86b5-4622-9710-7df03d1bbee9")
	)
	(wire
		(pts
			(xy 236.22 95.25) (xy 237.49 95.25)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ec8e982f-1849-427a-8dd2-bf8309585d0d")
	)
	(wire
		(pts
			(xy 250.19 54.61) (xy 251.46 54.61)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "eff2d6c5-06da-4a29-a590-ff08af5bf8e9")
	)
	(wire
		(pts
			(xy 96.52 69.85) (xy 90.17 69.85)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f03b5e6c-7e0d-4243-9a47-60eeaef796c1")
	)
	(wire
		(pts
			(xy 83.82 64.77) (xy 116.84 64.77)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f1eb0ed2-df27-45f7-8612-b351acb309f0")
	)
	(wire
		(pts
			(xy 90.17 69.85) (xy 83.82 69.85)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "fc14b993-0a04-40aa-94fc-f3b65e4f0a74")
	)
	(wire
		(pts
			(xy 250.19 80.01) (xy 251.46 80.01)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "fe7fd6d3-4d6a-4a50-a2b5-61f432310294")
	)
	(wire
		(pts
			(xy 238.76 102.87) (xy 238.76 100.33)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "fef2ed1d-d095-47bc-a6ba-7855409a5fe3")
	)
	(label "UART_TRG.RX"
		(at 250.19 87.63 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "0274289d-ba92-41c2-be3a-a8e02ec2075f")
	)
	(label "DIG.D9"
		(at 250.19 52.07 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "0a6f57d8-e7b0-4c3e-9c64-c69ddf4443c7")
	)
	(label "NPM1300.SDA"
		(at 157.48 67.31 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "11247fc5-4885-495f-8022-470519335e58")
	)
	(label "SWD_TRG.~{RESET}"
		(at 157.48 123.19 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "1318944d-6af1-410e-9930-ce4df274f8f5")
	)
	(label "D3_FLASH"
		(at 73.66 110.49 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "151c099f-1387-42d9-9eea-02b8226e832c")
	)
	(label "ANALOG.A2"
		(at 250.19 72.39 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "1bb7eaa2-68e8-4c63-b6b3-4c13d89f5f05")
	)
	(label "DIG.D12"
		(at 116.84 120.65 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "1bbfce42-f132-4a7f-94ec-49a790ba80f4")
	)
	(label "DIG.D11"
		(at 157.48 57.15 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "1cfea409-4000-4de7-84e2-3a48c0d81b53")
	)
	(label "I2C_54.SCL"
		(at 250.19 102.87 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "20c9713a-6069-48f7-aa1f-15f5d5bfc2b5")
	)
	(label "SPI.MISO"
		(at 73.66 105.41 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "210c390c-f9d0-4859-93ff-42e765ca651e")
	)
	(label "DIG.D12"
		(at 250.19 59.69 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "2ad70248-**************-b18d0776b8e2")
	)
	(label "SPI.SCK"
		(at 250.19 110.49 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "2f85f2a7-2f73-40db-8d66-64c7d1ab3755")
	)
	(label "I2C_54.SDA"
		(at 250.19 105.41 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "32f2b520-9053-48de-993b-7247eb1c0ecf")
	)
	(label "ANALOG.A5"
		(at 116.84 90.17 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "33f3cfb8-1c42-44fa-9351-691ee30ecf07")
	)
	(label "UART_TRG.TX"
		(at 116.84 82.55 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "36763227-d3a7-4eaa-9c9f-00d827ad6a4f")
	)
	(label "ANALOG.A3"
		(at 116.84 74.93 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "36a6d1c6-0a1e-4c1c-b189-1b40aab5f40c")
	)
	(label "~{CS}_FLASH"
		(at 116.84 110.49 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "3e5d8a3a-f31a-44e2-a0b7-e1427d3c869b")
	)
	(label "ANALOG.A3"
		(at 250.19 74.93 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "42546169-427f-4ce5-8406-9469b9d5f577")
	)
	(label "UART_TRG.TX"
		(at 250.19 85.09 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "437b1aa1-d918-4186-810a-b92f613489af")
	)
	(label "DIG.D5"
		(at 116.84 92.71 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "443fe1ef-a93d-47c4-9ce2-226edbce5ec0")
	)
	(label "NPM1300.SCL"
		(at 250.19 38.1 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "4f8681d5-8696-4933-8d20-9fb073b05df2")
	)
	(label "SPI.SCK"
		(at 73.66 100.33 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "56114402-6d56-4c76-a5ff-eb9ae2fc1ea4")
	)
	(label "ANALOG.A4"
		(at 250.19 77.47 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "5f3bff81-240e-4065-83de-35a10e8d398e")
	)
	(label "UART_TRG.RX"
		(at 116.84 80.01 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "6b3de00d-1b49-472b-8526-9936906f4757")
	)
	(label "ANALOG.A1"
		(at 116.84 69.85 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "6ba7c847-b659-4f1e-9885-579d6f2bb2fe")
	)
	(label "I2C_54.SCL"
		(at 116.84 77.47 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "7175dd51-0c9a-4fe2-a984-05f26695acc9")
	)
	(label "ANALOG.A1"
		(at 250.19 69.85 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "784be193-f2fe-4c17-8880-42d83272a538")
	)
	(label "SPI.MOSI"
		(at 250.19 113.03 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "908f7a7d-455a-420f-bd62-bd00ab83dd7e")
	)
	(label "DIG.D9"
		(at 116.84 123.19 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "9177ddf2-4984-49f1-9c29-af5e5e5f3c65")
	)
	(label "NPM1300.SDA"
		(at 250.19 35.56 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "94d9ecb8-c645-4600-90ae-44140e3f150a")
	)
	(label "SPI.MISO"
		(at 116.84 107.95 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "999ec187-e728-418b-af89-6ceffc8e1f34")
	)
	(label "ANALOG.A0"
		(at 250.19 67.31 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "9c8d5f50-1173-4f45-956b-9eb9afbefd42")
	)
	(label "DIG.D10"
		(at 116.84 118.11 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "9d1dbe4b-288a-4f77-a8f0-d344c0f462dd")
	)
	(label "SWD_TRG.SWDCLK"
		(at 157.48 120.65 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "a45b766a-f641-4d34-9a4a-c80c3fccba1f")
	)
	(label "ANALOG.A5"
		(at 250.19 80.01 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "a525702c-6ae3-4180-8e16-904d39404cd1")
	)
	(label "DIG.D13"
		(at 250.19 62.23 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "a525fea6-3870-4cef-8611-2439c66da371")
	)
	(label "D2_FLASH"
		(at 116.84 105.41 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "a82657d6-2cd9-44a2-a5e2-537214fb4101")
	)
	(label "ANALOG.A4"
		(at 116.84 87.63 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "af18aaef-7eb3-493a-b2c4-8336062ddc73")
	)
	(label "ANALOG.A2"
		(at 116.84 72.39 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "af4c0937-49aa-4f7d-a85c-8bd497ffc46e")
	)
	(label "DIG.D6"
		(at 250.19 49.53 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "b47deb38-dcf5-4940-bcc8-be27710e0238")
	)
	(label "DIG.D10"
		(at 250.19 54.61 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "b605026f-2531-4672-b388-a81968b25053")
	)
	(label "NPM1300.SCL"
		(at 157.48 64.77 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "ba844197-847a-4ec7-8928-34e2b8a6738a")
	)
	(label "SWD_TRG.SWDIO"
		(at 157.48 118.11 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "bb29c702-2b32-479a-a578-9808df8834ae")
	)
	(label "SPI.MOSI"
		(at 116.84 102.87 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "c707a9a7-c620-4ace-bfae-a62664b65dac")
	)
	(label "SPI.MISO"
		(at 250.19 115.57 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "c7af8b73-40af-41a7-b06c-2be7f4759452")
	)
	(label "DIG.D13"
		(at 116.84 115.57 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "cbb69fbc-ecbc-4753-a7ed-2be10dd79d69")
	)
	(label "DIG.D6"
		(at 157.48 59.69 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "d1aa41d8-ae00-4af5-a8db-858479305c20")
	)
	(label "DIG.D11"
		(at 250.19 57.15 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "d8644d73-883d-442a-bf04-fbf53dbc2903")
	)
	(label "~{CS}_FLASH"
		(at 73.66 97.79 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "df0a3386-bd5e-45e0-997f-1ae885c241cc")
	)
	(label "I2C_54.SDA"
		(at 116.84 85.09 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "e307c125-57d4-4888-a3b7-d18cc8971fda")
	)
	(label "DIG.D5"
		(at 250.19 46.99 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "e90dd2b2-5974-4733-81c2-efb6ab9bd682")
	)
	(label "ANALOG.A0"
		(at 116.84 67.31 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "eb38fbf6-7de1-4eba-bfaf-97207d68f78e")
	)
	(label "NPM1300.INT"
		(at 157.48 62.23 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "ec14e357-3d10-4ae8-b222-11a5c9c17223")
	)
	(label "SPI.MOSI"
		(at 73.66 102.87 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "ecadabda-042d-4df8-a242-8577027c1859")
	)
	(label "SPI.SCK"
		(at 116.84 100.33 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "ed3bb16f-1dfd-40df-9817-a43bd303a023")
	)
	(label "D3_FLASH"
		(at 116.84 97.79 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "ed9aae51-b81d-4745-9810-8509a132380c")
	)
	(label "NPM1300.INT"
		(at 250.19 40.64 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "f0891d00-7c91-4954-8366-392060212ef7")
	)
	(label "D2_FLASH"
		(at 73.66 107.95 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "f23dbac5-e274-46fd-85b3-92789ad04e5e")
	)
	(hierarchical_label "I2C_54{SCL, SDA}"
		(shape input)
		(at 254 100.33 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "5984b782-025f-4102-88b3-f5b19baaf1ae")
	)
	(hierarchical_label "SWD_TRG{~{RESET}, SWDIO, SWDCLK}"
		(shape input)
		(at 181.61 125.73 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "68c44309-6774-4c12-b14d-390a8408324c")
	)
	(hierarchical_label "SPI{SCK, MOSI, MISO}"
		(shape input)
		(at 254 107.95 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "924c0571-b9e9-4c92-abe0-a710f4ffdf46")
	)
	(hierarchical_label "NPM1300{SCL, SDA, INT}"
		(shape input)
		(at 254 33.02 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "9e9609fb-9327-4c14-81f0-60a830b75106")
	)
	(hierarchical_label "ANALOG{A[0..5]}"
		(shape input)
		(at 254 64.77 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "dc2137fb-0031-430b-9913-439dee03214d")
	)
	(hierarchical_label "DIG{D5 D6 D[9..13]}"
		(shape input)
		(at 254 44.45 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "ddc7a65c-a7f7-470a-a92d-ed0ed15e0842")
	)
	(hierarchical_label "UART_TRG{TX, RX}"
		(shape input)
		(at 254 82.55 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "f0563863-e429-43b9-82f7-98aa501307e9")
	)
	(symbol
		(lib_id "power:GND")
		(at 93.98 123.19 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "18db1271-36d3-400b-81df-8fab938370a9")
		(property "Reference" "#PWR045"
			(at 93.98 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 93.98 128.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 93.98 123.19 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 93.98 123.19 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 93.98 123.19 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "6386c280-d5a1-42e6-9d49-ea2a4d581b04")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#PWR045")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:LED_Small")
		(at 93.98 115.57 270)
		(mirror x)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "1c62ec3b-bdd9-4fb9-a55c-66226a9c045f")
		(property "Reference" "D1"
			(at 91.44 114.2364 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "LED_Small"
			(at 91.44 116.7764 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "LED_SMD:LED_0603_1608Metric"
			(at 93.98 115.57 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 93.98 115.57 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Light emitting diode, small symbol"
			(at 93.98 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C125103"
			(at 93.98 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pin" "1=K 2=A"
			(at 93.98 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "7f2bac1b-fb59-4ea3-88fa-f84ffc16a4c0")
		)
		(pin "1"
			(uuid "e9da345d-a52a-445b-8a0a-73383981690d")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "D1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Memory_Flash:W25Q32JVZP")
		(at 63.5 105.41 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "205f5e89-5c9c-4530-a510-48e2161cb311")
		(property "Reference" "U4"
			(at 52.324 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "W25Q16JVUXIQ"
			(at 52.324 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Package_SON:Winbond_USON-8-1EP_3x2mm_P0.5mm_EP0.2x1.6mm"
			(at 63.5 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "http://www.winbond.com/resource-files/w25q32jv%20revg%2003272018%20plus.pdf"
			(at 63.5 107.95 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "32Mbit / 4MiB Serial Flash Memory, Standard/Dual/Quad SPI, 2.7-3.6V, WSON-8"
			(at 63.5 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C2843335"
			(at 63.5 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "1f83e81e-a85f-48e1-b653-ec8c713cc63c")
		)
		(pin "9"
			(uuid "9cab5253-cfac-4056-b1ee-c95d90f46eea")
		)
		(pin "4"
			(uuid "cb14dfbc-61b1-4c07-a589-5a8e61aa8a32")
		)
		(pin "5"
			(uuid "dd8ac4a5-c2fc-4d07-9897-5b5feaa5a61e")
		)
		(pin "7"
			(uuid "c5758e73-388c-4b45-b065-88cd3e5f39cc")
		)
		(pin "6"
			(uuid "569cbec7-0573-4349-93fe-cb204986ff5f")
		)
		(pin "2"
			(uuid "1d8a3c05-f209-4381-821d-a71a19b0d39c")
		)
		(pin "3"
			(uuid "f976a137-f66f-4163-b595-d28b09604aa5")
		)
		(pin "8"
			(uuid "ec298fc3-72ca-47b7-9dc8-f39ef3d30a69")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "U4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R_Small_US")
		(at 179.07 62.23 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "255a6d7f-72ad-424a-9652-ec62664ce713")
		(property "Reference" "R5"
			(at 176.53 60.9599 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10kR"
			(at 176.53 63.4999 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 179.07 62.23 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 179.07 62.23 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor, small US symbol"
			(at 179.07 62.23 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C25744"
			(at 179.07 62.23 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "888b1564-b11f-459c-b5be-99680cdc6745")
		)
		(pin "2"
			(uuid "295b5e2a-3084-4c23-a45c-16d7b19d9645")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "R5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 158.75 133.35 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "2ed79953-62ff-457b-93e2-491f8a633aea")
		(property "Reference" "#PWR010"
			(at 158.75 139.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 158.75 138.43 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 158.75 133.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 158.75 133.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 158.75 133.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "a1004ab8-29d8-4da5-adfb-94f501773ca6")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#PWR010")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:Crystal_Small")
		(at 107.95 57.15 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "325234f9-a53c-45e5-84df-74b38427493f")
		(property "Reference" "Y1"
			(at 105.41 55.8799 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "32.768KHz"
			(at 105.41 58.4199 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Crystal:Crystal_SMD_3215-2Pin_3.2x1.5mm"
			(at 107.95 57.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 107.95 57.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Two pin crystal, small symbol"
			(at 107.95 57.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C32346"
			(at 107.95 57.15 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "3306bb7b-9401-4ca1-b38d-f45eb0a83228")
		)
		(pin "1"
			(uuid "61944fcc-4d93-406f-a10c-e3f54c3bbbb0")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "Y1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R_Small_US")
		(at 236.22 97.79 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom no)
		(on_board yes)
		(dnp yes)
		(uuid "33788435-100c-4faa-9388-ed4ef76d1215")
		(property "Reference" "R3"
			(at 233.68 96.5199 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10kR"
			(at 233.68 99.0599 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 236.22 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 236.22 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor, small US symbol"
			(at 236.22 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C25744"
			(at 236.22 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "5c891a27-7266-4ffe-81bf-ce16444f8544")
		)
		(pin "2"
			(uuid "ea614e9d-517f-4b22-8545-2c27fa61f49d")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "R3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "nordic-lib-kicad-nrf54-modules:BM15x")
		(at 137.16 90.17 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "3f1af632-f9ee-4944-8463-8a30834f2882")
		(property "Reference" "U1"
			(at 137.16 90.17 0)
			(do_not_autoplace yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "BM15x"
			(at 137.16 92.71 0)
			(do_not_autoplace yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "nordic-lib-kicad-nrf54-modules:BM15x-LGA-45_15.8x10mm"
			(at 137.16 90.17 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://static1.squarespace.com/static/561459a2e4b0b39f5cefa12e/t/66c3d7591cc204465c3f6e4f/1724110684125/BM15C+Product+Specifications.pdf"
			(at 137.16 90.17 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 137.16 90.17 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C9900148376"
			(at 137.16 90.17 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "J4"
			(uuid "9662fdc8-7946-4b5a-a7cb-a1154c67232e")
		)
		(pin "K7"
			(uuid "cd8bd34f-dc1f-4640-9a93-1cc5ee669007")
		)
		(pin "K6"
			(uuid "935f517b-f47b-4a25-b449-7edd2e043c1b")
		)
		(pin "J6"
			(uuid "619101a6-934d-4933-b415-bc3363849855")
		)
		(pin "K8"
			(uuid "695db9c1-b2c0-4ac6-ae94-371f7e3680c2")
		)
		(pin "J5"
			(uuid "bd001d7b-3506-4f0d-83f0-e64460b0141e")
		)
		(pin "K2"
			(uuid "4d63e143-aa0e-41db-b1eb-0d443ddad739")
		)
		(pin "F1"
			(uuid "bd6807ca-cc52-4e7e-ad57-c0ce0080d235")
		)
		(pin "H8"
			(uuid "0538c8e9-1ac5-4ce1-9516-8d6aea23baaf")
		)
		(pin "B7"
			(uuid "b46d88fa-91bf-4f35-9c86-a63665c80b21")
		)
		(pin "J2"
			(uuid "a7a610b1-7b22-4bd4-bd3d-c1ef32eecd45")
		)
		(pin "K4"
			(uuid "2cd8e40f-19fe-42c6-983c-c92103767e23")
		)
		(pin "B5"
			(uuid "dcfa3b68-0648-47a7-b047-6a005e4a2eec")
		)
		(pin "A1"
			(uuid "e0a0222e-eb89-47af-9792-b2254e61b08c")
		)
		(pin "F9"
			(uuid "0053648e-5a16-4acf-b62f-b907abbc153c")
		)
		(pin "F8"
			(uuid "56957a03-debd-4bec-b91c-6a2d030bd2b2")
		)
		(pin "E9"
			(uuid "b6388794-9ac7-4af5-8d2f-24f25260da8c")
		)
		(pin "K5"
			(uuid "63ef99bd-1428-490b-8ac3-5fd041dd0bb7")
		)
		(pin "B9"
			(uuid "53ac80f9-2d30-4556-95c7-f6fcb98a5628")
		)
		(pin "B8"
			(uuid "2d58c619-6874-4c5e-94ec-b54f99c5110c")
		)
		(pin "E8"
			(uuid "5510ec56-9083-4985-b730-8b0a87b37f5f")
		)
		(pin "E1"
			(uuid "2e2ec9a8-08b1-4db6-889a-01b3434512e9")
		)
		(pin "H9"
			(uuid "b774f5e0-038f-4e97-9839-33756b15bafb")
		)
		(pin "C9"
			(uuid "15d7da4c-05bf-4eb6-a51a-04179629a53d")
		)
		(pin "G8"
			(uuid "1092153b-1b3c-4839-899e-7d18a8713be1")
		)
		(pin "J9"
			(uuid "2b2508e0-05e0-47ef-b6fa-98d599ec635e")
		)
		(pin "D1"
			(uuid "6ec727eb-1b85-451f-873b-909a43c2e9ff")
		)
		(pin "J8"
			(uuid "43ece906-d349-4a31-a97a-d5f7b9c708c5")
		)
		(pin "A9"
			(uuid "304101e9-ddd3-423d-aefb-35c0d2bfe7a4")
		)
		(pin "B3"
			(uuid "07683436-fb20-443a-9bd7-64a30badb8f4")
		)
		(pin "H2"
			(uuid "70e08f5b-666b-4338-86d2-356ee98847e7")
		)
		(pin "B4"
			(uuid "641bab04-86c9-40bf-9830-2ef585e2923a")
		)
		(pin "D9"
			(uuid "bea7dffe-c515-4651-bcdf-48be249887a0")
		)
		(pin "D8"
			(uuid "7b475048-8d94-4016-ae51-c2d356e8413f")
		)
		(pin "K9"
			(uuid "f677cece-c892-4b3d-8c6a-e121d03774c2")
		)
		(pin "G9"
			(uuid "b971cc97-6665-4b91-8d06-0eec50d6fff1")
		)
		(pin "C8"
			(uuid "86b79d41-d720-44ea-ac1e-7a3fcc3fe77b")
		)
		(pin "J7"
			(uuid "31ae61e3-febe-4d5d-a508-f5df42f0eeb1")
		)
		(pin "A3"
			(uuid "76dd574d-4a32-446c-a58e-8ec3ea0b7c68")
		)
		(pin "G2"
			(uuid "7268de7b-141d-4cc2-9432-b5a5e364b30c")
		)
		(pin "J3"
			(uuid "4103d91b-d7fd-4053-95ac-d58be49eda00")
		)
		(pin "K3"
			(uuid "f7f050d9-d1d7-4b20-a2ed-c676f7c8efef")
		)
		(pin "B1"
			(uuid "59a031fa-42db-497b-9451-742402edb745")
		)
		(pin "B2"
			(uuid "030c249c-b9b4-4969-912a-96df4ea94096")
		)
		(pin "B6"
			(uuid "8856f8c5-e8e2-4813-8421-4cb51ba1cc1e")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "U1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "LordsBoards-Graphic:LordsBoardsLogo")
		(at 185.42 173.99 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom no)
		(on_board no)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "433f52a5-d8ea-4053-ad23-50dc34d65e38")
		(property "Reference" "#SYM7"
			(at 185.42 173.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "~"
			(at 185.42 173.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 185.42 173.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 185.42 173.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 185.42 173.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#SYM7")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector_Generic_MountingPin:Conn_01x05_MountingPin")
		(at 76.2 63.5 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "46d8714f-e8bd-460e-a4ff-db227956e9ca")
		(property "Reference" "J6"
			(at 76.2 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x05_MountingPin"
			(at 76.2 54.61 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "RoyalBlue54L-Feather-Connector_FFC-FPC:CONN-SMD_5P-P0.50_HCTL_HC-FPC-05-09-5RLTAG"
			(at 76.2 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 76.2 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Generic connectable mounting pin connector, single row, 01x05, script generated (kicad-library-utils/schlib/autogen/connector/)"
			(at 76.2 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "e0f6480a-e70b-4aa9-b8f5-373cf4ae634d")
		)
		(pin "3"
			(uuid "0cdd956c-19a1-49f3-855e-2186b1fdfb22")
		)
		(pin "5"
			(uuid "f4978413-e607-4f40-810d-4f91618ff5b1")
		)
		(pin "MP"
			(uuid "f4eb0f1b-6626-4fe8-9baf-516361b03142")
		)
		(pin "4"
			(uuid "bd848905-38ef-4f07-9d30-d078692fff42")
		)
		(pin "2"
			(uuid "071607f8-a36f-4b97-9d99-dc49b86abe39")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "J6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R_Small_US")
		(at 181.61 62.23 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "48a2606a-9fa8-49a0-a358-18965bb62307")
		(property "Reference" "R7"
			(at 184.15 60.9599 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10kR"
			(at 184.15 63.4999 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 181.61 62.23 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 181.61 62.23 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor, small US symbol"
			(at 181.61 62.23 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C25744"
			(at 181.61 62.23 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "67cc0a05-4ec6-447d-b559-c92451de8588")
		)
		(pin "2"
			(uuid "2002e424-8014-4da6-8372-d4908ba97a44")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "R7")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VDD")
		(at 137.16 43.18 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "4b4653a5-a2e9-4a93-956a-f8483fe94a12")
		(property "Reference" "#PWR043"
			(at 137.16 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VDD"
			(at 137.16 38.1 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 137.16 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 137.16 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VDD\""
			(at 137.16 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "8b4e5666-e1bb-4665-af58-cb3824abcf05")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#PWR043")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 63.5 118.11 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "580c4c37-bfcd-42fa-941f-1acfa50fb6f8")
		(property "Reference" "#PWR011"
			(at 63.5 124.46 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 63.5 123.19 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 63.5 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 63.5 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 63.5 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "44eb1a93-c6b9-41a5-ad9d-2f68a30e1a68")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#PWR011")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 137.16 128.27 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "5a4027d3-e652-4e98-ba89-2556328647df")
		(property "Reference" "#PWR014"
			(at 137.16 134.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 137.16 133.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 137.16 128.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 137.16 128.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 137.16 128.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "5dae909e-3aba-4799-b8a3-a04abb4620ea")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#PWR014")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 83.82 67.31 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "6875339c-c8c2-429e-9e84-cbb5f1c61c02")
		(property "Reference" "C13"
			(at 86.36 66.0462 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "300pF"
			(at 86.36 68.5862 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 83.82 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 83.82 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 83.82 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C126505"
			(at 83.82 67.31 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "9c88a8c3-b8ef-465a-b3ba-fcb53940d0a2")
		)
		(pin "2"
			(uuid "6a3c0c6a-e193-44d8-8e1c-e28cb39fe1da")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "C13")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R_Small_US")
		(at 238.76 97.79 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom no)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "6b0eb701-6442-4577-a416-b11a7ee65f9c")
		(property "Reference" "R2"
			(at 241.3 96.5199 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "10kR"
			(at 241.3 99.0599 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 238.76 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 238.76 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor, small US symbol"
			(at 238.76 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C25744"
			(at 238.76 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "7b1c6ca3-b242-493e-a01b-f8d0258ad1c9")
		)
		(pin "2"
			(uuid "b74b4a1f-2525-4fee-b311-b526e17dc573")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "R2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Switch:SW_Push")
		(at 167.64 46.99 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "76328cc8-002d-4e17-8797-a6f6319668c5")
		(property "Reference" "SW2"
			(at 171.45 45.7199 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "SW_Push"
			(at 171.45 48.2599 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Button_Switch_SMD:SW_Push_1P1T_NO_Vertical_Wuerth_434133025816"
			(at 172.72 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 172.72 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Push button switch, generic, two pins"
			(at 167.64 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C139797"
			(at 167.64 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "16accd01-7dc9-47eb-8c4b-da8d00b62437")
		)
		(pin "1"
			(uuid "7d61d6b8-f7e6-4ae3-b930-f1e21616157b")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "SW2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 167.64 52.07 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "79f9ffd8-e04a-470e-aa2a-9b1556b8810f")
		(property "Reference" "#PWR039"
			(at 167.64 58.42 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 167.64 57.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 167.64 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 167.64 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 167.64 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "3ff41e40-fcc8-4cac-9d69-80cfa17b3dd0")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#PWR039")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VDD")
		(at 57.15 80.01 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "81fa4060-9413-457e-bbd8-ae3ed79d79cd")
		(property "Reference" "#PWR038"
			(at 57.15 83.82 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VDD"
			(at 57.15 74.93 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 57.15 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 57.15 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VDD\""
			(at 57.15 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "ca5a3a09-0aa0-492c-8074-9d657b5ec6b4")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#PWR038")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:TestPoint_Small")
		(at 173.99 118.11 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "8822ac4c-1db4-406a-80cc-b0a380a20564")
		(property "Reference" "TP1"
			(at 171.45 122.6821 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Value" "TestPoint"
			(at 171.45 120.1421 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "TestPoint:TestPoint_Pad_D2.0mm"
			(at 168.91 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 168.91 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "test point"
			(at 173.99 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "34818656-81be-4aeb-9e2b-940fdf649e5f")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "TP1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 132.08 48.26 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "89c5cb8e-eb79-4b31-a0ee-************")
		(property "Reference" "#PWR052"
			(at 132.08 54.61 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 132.08 53.34 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 132.08 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 132.08 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 132.08 48.26 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "004ff605-b931-4b36-88bd-6123524ff15e")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#PWR052")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:TestPoint_Small")
		(at 173.99 123.19 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "8f4ba958-792e-4db9-b6a2-e69a0fd5f004")
		(property "Reference" "TP2"
			(at 171.45 127.7621 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Value" "TestPoint"
			(at 171.45 125.2221 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "TestPoint:TestPoint_Pad_D2.0mm"
			(at 168.91 123.19 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 168.91 123.19 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "test point"
			(at 173.99 123.19 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "65931cc2-1acf-400c-b8c2-d3ed689f5f90")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "TP2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 132.08 45.72 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "93381792-76a1-4bfc-92ef-3531f7657bc3")
		(property "Reference" "C9"
			(at 129.54 44.4562 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "4.7uF"
			(at 129.54 46.9962 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 132.08 45.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 132.08 45.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 132.08 45.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C23733"
			(at 132.08 45.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "7f1f4b22-1cd8-4015-91a0-3600f426d096")
		)
		(pin "1"
			(uuid "4695b834-b98c-44b0-b229-80467be0f2d4")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "C9")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 57.15 85.09 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a00c6ad4-9315-4188-92f1-86c97a984839")
		(property "Reference" "#PWR037"
			(at 57.15 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 57.15 90.17 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 57.15 85.09 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 57.15 85.09 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 57.15 85.09 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "fb8c04b8-1bf7-4fb4-aa2e-27dcf3be3636")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#PWR037")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VDD")
		(at 180.34 59.69 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a5187a0d-2254-458a-8fb0-639726e9f4cf")
		(property "Reference" "#PWR050"
			(at 180.34 63.5 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VDD"
			(at 180.34 54.61 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 180.34 59.69 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 180.34 59.69 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VDD\""
			(at 180.34 59.69 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "4230f7fe-985d-482f-b10d-4ee10b764b28")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#PWR050")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 57.15 82.55 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "ae5dbfae-6959-48bf-9f34-46ef9af40d12")
		(property "Reference" "C12"
			(at 54.61 81.2862 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "100nF"
			(at 54.61 83.8262 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 57.15 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 57.15 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 57.15 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C307331"
			(at 57.15 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "cfca86a9-e440-40de-9a14-9dae6ae7d6d3")
		)
		(pin "2"
			(uuid "dde8e681-543c-4106-aee8-bb43399da95f")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "C12")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Switch:SW_Push")
		(at 158.75 128.27 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "b5b21bc3-6442-42cf-816b-59b8a4ec2375")
		(property "Reference" "SW1"
			(at 160.02 126.9999 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "SW_Push"
			(at 160.02 129.5399 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Button_Switch_SMD:SW_Push_1P1T_NO_Vertical_Wuerth_434133025816"
			(at 153.67 128.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 153.67 128.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Push button switch, generic, two pins"
			(at 158.75 128.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C139797"
			(at 158.75 128.27 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "9f616e92-0d63-4c0e-a88d-2083e94b1117")
		)
		(pin "1"
			(uuid "a80257b1-84a7-4776-8e03-39fceb8c7d5a")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "SW1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 76.2 73.66 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "bae2c5a8-a2fd-4f34-9326-5485e0315d2a")
		(property "Reference" "#PWR051"
			(at 76.2 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 76.2 78.74 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 76.2 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 76.2 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 76.2 73.66 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "d2c74556-3f1a-488e-a15b-162fdc7a2721")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#PWR051")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:TestPoint")
		(at 161.29 133.35 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom no)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "c6507987-d257-4712-baaf-df01c8816602")
		(property "Reference" "TP4"
			(at 165.8621 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Value" "TestPoint"
			(at 163.3221 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "TestPoint:TestPoint_Pad_D2.0mm"
			(at 161.29 138.43 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 161.29 138.43 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "test point"
			(at 161.29 133.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "2dbf018a-253c-406c-950e-a103057d4274")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "TP4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:TestPoint_Small")
		(at 173.99 120.65 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp yes)
		(fields_autoplaced yes)
		(uuid "da188f97-ddb4-4024-b580-6d1bdcd416c9")
		(property "Reference" "TP3"
			(at 171.45 125.2221 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Value" "TestPoint"
			(at 171.45 122.6821 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
		(property "Footprint" "TestPoint:TestPoint_Pad_D2.0mm"
			(at 168.91 120.65 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 168.91 120.65 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "test point"
			(at 173.99 120.65 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "7121dc6c-1adb-4141-aaae-81e7a0090904")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "TP3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Graphic:Logo_Open_Hardware_Small")
		(at 278.13 173.99 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom no)
		(on_board no)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "e419908b-4f3f-48bb-88f9-40f32c75bdc7")
		(property "Reference" "#SYM8"
			(at 278.13 167.005 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "Logo_Open_Hardware_Small"
			(at 278.13 179.705 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 278.13 173.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 278.13 173.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 278.13 173.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#SYM8")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R_Small_US")
		(at 93.98 120.65 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "eb3b906f-3dfe-43f1-b45d-b603ffa55d1e")
		(property "Reference" "R6"
			(at 91.44 119.3799 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "470R"
			(at 91.44 121.9199 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 93.98 120.65 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 93.98 120.65 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor, small US symbol"
			(at 93.98 120.65 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C25117"
			(at 93.98 120.65 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "7d75d6f3-c52e-407b-b589-58d032b43adf")
		)
		(pin "1"
			(uuid "380c5526-700b-4b8d-b4f7-1feaee3a6111")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "R6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VDD")
		(at 237.49 95.25 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "f0963477-9b5e-4ea8-80be-aef3e3e34bec")
		(property "Reference" "#PWR026"
			(at 237.49 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VDD"
			(at 237.49 90.17 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 237.49 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 237.49 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VDD\""
			(at 237.49 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "3fb1107d-7e3c-4f43-b210-e7a444a42ce1")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#PWR026")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 96.52 64.77 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "f44e58ed-06d7-49ec-924c-4c02cc98cc34")
		(property "Reference" "C14"
			(at 99.06 63.5062 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "300pF"
			(at 99.06 66.0462 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 96.52 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 96.52 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 96.52 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C126505"
			(at 96.52 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "a5f68342-b98b-415d-bf3b-da8e469cac9d")
		)
		(pin "2"
			(uuid "470d0708-6326-419d-9996-57afe6235a6d")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "C14")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 90.17 69.85 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "f8734ac0-fef4-4c89-a5ff-8b81d6a5edb4")
		(property "Reference" "#PWR046"
			(at 90.17 76.2 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 90.17 74.93 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 90.17 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 90.17 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 90.17 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "86918fbe-bc3b-4b48-a6c8-2caa9e1bcc4a")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/b280f472-530c-47a8-bd61-35bd1befbf04"
					(reference "#PWR046")
					(unit 1)
				)
			)
		)
	)
)
