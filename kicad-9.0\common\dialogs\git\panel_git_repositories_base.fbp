<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<wxFormBuilder_Project>
    <FileVersion major="1" minor="16" />
    <object class="Project" expanded="1">
        <property name="class_decoration"></property>
        <property name="code_generation">C++</property>
        <property name="disconnect_events">1</property>
        <property name="disconnect_mode">source_name</property>
        <property name="disconnect_php_events">0</property>
        <property name="disconnect_python_events">0</property>
        <property name="embedded_files_path">res</property>
        <property name="encoding">UTF-8</property>
        <property name="event_generation">connect</property>
        <property name="file">panel_git_repos_base</property>
        <property name="first_id">1000</property>
        <property name="help_provider">none</property>
        <property name="image_path_wrapper_function_name"></property>
        <property name="indent_with_spaces"></property>
        <property name="internationalize">1</property>
        <property name="name">PanelGitRepos</property>
        <property name="namespace"></property>
        <property name="path">.</property>
        <property name="precompiled_header"></property>
        <property name="relative_path">1</property>
        <property name="skip_lua_events">1</property>
        <property name="skip_php_events">1</property>
        <property name="skip_python_events">1</property>
        <property name="ui_table">UI</property>
        <property name="use_array_enum">0</property>
        <property name="use_enum">1</property>
        <property name="use_microsoft_bom">0</property>
        <object class="Panel" expanded="1">
            <property name="aui_managed">0</property>
            <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
            <property name="bg"></property>
            <property name="context_help"></property>
            <property name="context_menu">1</property>
            <property name="drag_accept_files">0</property>
            <property name="enabled">1</property>
            <property name="event_handler">impl_virtual</property>
            <property name="fg"></property>
            <property name="font"></property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="maximum_size"></property>
            <property name="minimum_size"></property>
            <property name="name">PANEL_GIT_REPOS_BASE</property>
            <property name="pos"></property>
            <property name="size">-1,-1</property>
            <property name="subclass">RESETTABLE_PANEL; widgets/resettable_panel.h; Not forward_declare</property>
            <property name="tooltip"></property>
            <property name="two_step_creation">0</property>
            <property name="window_extra_style"></property>
            <property name="window_name"></property>
            <property name="window_style">wxTAB_TRAVERSAL</property>
            <object class="wxBoxSizer" expanded="1">
                <property name="minimum_size"></property>
                <property name="name">bPanelSizer</property>
                <property name="orient">wxHORIZONTAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="1">
                    <property name="border">20</property>
                    <property name="flag">wxRIGHT</property>
                    <property name="proportion">0</property>
                    <object class="wxBoxSizer" expanded="1">
                        <property name="minimum_size"></property>
                        <property name="name">bLeftSizer</property>
                        <property name="orient">wxVERTICAL</property>
                        <property name="permission">none</property>
                        <object class="sizeritem" expanded="0">
                            <property name="border">10</property>
                            <property name="flag">wxEXPAND|wxLEFT|wxTOP</property>
                            <property name="proportion">0</property>
                            <object class="wxStaticText" expanded="0">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="close_button">1</property>
                                <property name="context_help"></property>
                                <property name="context_menu">1</property>
                                <property name="default_pane">0</property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="drag_accept_files">0</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="label">Git Commit Data</property>
                                <property name="markup">0</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size"></property>
                                <property name="moveable">1</property>
                                <property name="name">m_staticText12</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">1</property>
                                <property name="pos"></property>
                                <property name="resize">Resizable</property>
                                <property name="show">1</property>
                                <property name="size"></property>
                                <property name="style"></property>
                                <property name="subclass">; ; forward_declare</property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style"></property>
                                <property name="wrap">-1</property>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">13</property>
                            <property name="flag">wxBOTTOM|wxEXPAND|wxLEFT|wxTOP</property>
                            <property name="proportion">1</property>
                            <object class="wxFlexGridSizer" expanded="1">
                                <property name="cols">2</property>
                                <property name="flexible_direction">wxBOTH</property>
                                <property name="growablecols">1</property>
                                <property name="growablerows"></property>
                                <property name="hgap">0</property>
                                <property name="minimum_size"></property>
                                <property name="name">fgSizer1</property>
                                <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                                <property name="permission">none</property>
                                <property name="rows">0</property>
                                <property name="vgap">0</property>
                                <object class="sizeritem" expanded="0">
                                    <property name="border">5</property>
                                    <property name="flag">wxALL</property>
                                    <property name="proportion">0</property>
                                    <object class="wxCheckBox" expanded="0">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="checked">1</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="drag_accept_files">0</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="label">Use default values</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_cbDefault</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style"></property>
                                        <property name="subclass">; ; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="validator_data_type"></property>
                                        <property name="validator_style">wxFILTER_NONE</property>
                                        <property name="validator_type">wxDefaultValidator</property>
                                        <property name="validator_variable"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                        <event name="OnCheckBox">onDefaultClick</event>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="0">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND</property>
                                    <property name="proportion">0</property>
                                    <object class="spacer" expanded="0">
                                        <property name="height">0</property>
                                        <property name="permission">protected</property>
                                        <property name="width">0</property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="0">
                                    <property name="border">5</property>
                                    <property name="flag">wxALL</property>
                                    <property name="proportion">0</property>
                                    <object class="wxStaticText" expanded="0">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="drag_accept_files">0</property>
                                        <property name="enabled">0</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="label">Author name:</property>
                                        <property name="markup">0</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_authorLabel</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style"></property>
                                        <property name="subclass">; ; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                        <property name="wrap">-1</property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="0">
                                    <property name="border">5</property>
                                    <property name="flag">wxALL|wxEXPAND</property>
                                    <property name="proportion">0</property>
                                    <object class="wxTextCtrl" expanded="0">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="drag_accept_files">0</property>
                                        <property name="enabled">0</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="maxlength"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_author</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style"></property>
                                        <property name="subclass">; ; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="validator_data_type"></property>
                                        <property name="validator_style">wxFILTER_NONE</property>
                                        <property name="validator_type">wxDefaultValidator</property>
                                        <property name="validator_variable"></property>
                                        <property name="value"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="0">
                                    <property name="border">5</property>
                                    <property name="flag">wxALL</property>
                                    <property name="proportion">0</property>
                                    <object class="wxStaticText" expanded="0">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="drag_accept_files">0</property>
                                        <property name="enabled">0</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="label">Author e-mail:</property>
                                        <property name="markup">0</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_authorEmailLabel</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style"></property>
                                        <property name="subclass">; ; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                        <property name="wrap">-1</property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="0">
                                    <property name="border">5</property>
                                    <property name="flag">wxALL|wxEXPAND</property>
                                    <property name="proportion">0</property>
                                    <object class="wxTextCtrl" expanded="0">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="drag_accept_files">0</property>
                                        <property name="enabled">0</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="maxlength"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_authorEmail</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style"></property>
                                        <property name="subclass">; ; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="validator_data_type"></property>
                                        <property name="validator_style">wxFILTER_NONE</property>
                                        <property name="validator_type">wxDefaultValidator</property>
                                        <property name="validator_variable"></property>
                                        <property name="value"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                    </object>
                                </object>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="0">
                            <property name="border">5</property>
                            <property name="flag">wxEXPAND|wxBOTTOM</property>
                            <property name="proportion">0</property>
                            <object class="wxStaticLine" expanded="0">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="close_button">1</property>
                                <property name="context_help"></property>
                                <property name="context_menu">1</property>
                                <property name="default_pane">0</property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="drag_accept_files">0</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size"></property>
                                <property name="moveable">1</property>
                                <property name="name">m_staticline3</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">1</property>
                                <property name="pos"></property>
                                <property name="resize">Resizable</property>
                                <property name="show">1</property>
                                <property name="size"></property>
                                <property name="style">wxLI_HORIZONTAL</property>
                                <property name="subclass">; ; forward_declare</property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style"></property>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="0">
                            <property name="border">13</property>
                            <property name="flag">wxEXPAND|wxLEFT|wxRIGHT</property>
                            <property name="proportion">0</property>
                            <object class="wxStaticText" expanded="0">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="close_button">1</property>
                                <property name="context_help"></property>
                                <property name="context_menu">1</property>
                                <property name="default_pane">0</property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="drag_accept_files">0</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="label">Git Repositories</property>
                                <property name="markup">0</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size"></property>
                                <property name="moveable">1</property>
                                <property name="name">m_staticText20</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">1</property>
                                <property name="pos"></property>
                                <property name="resize">Resizable</property>
                                <property name="show">1</property>
                                <property name="size"></property>
                                <property name="style"></property>
                                <property name="subclass">; ; forward_declare</property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style"></property>
                                <property name="wrap">-1</property>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxEXPAND|wxLEFT|wxTOP</property>
                            <property name="proportion">0</property>
                            <object class="wxBoxSizer" expanded="1">
                                <property name="minimum_size"></property>
                                <property name="name">bAntialiasingSizer</property>
                                <property name="orient">wxVERTICAL</property>
                                <property name="permission">none</property>
                                <object class="sizeritem" expanded="0">
                                    <property name="border">5</property>
                                    <property name="flag">wxALL|wxEXPAND</property>
                                    <property name="proportion">5</property>
                                    <object class="wxGrid" expanded="0">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="autosize_cols">0</property>
                                        <property name="autosize_rows">0</property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="cell_bg"></property>
                                        <property name="cell_font"></property>
                                        <property name="cell_horiz_alignment">wxALIGN_LEFT</property>
                                        <property name="cell_text"></property>
                                        <property name="cell_vert_alignment">wxALIGN_TOP</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="col_label_horiz_alignment">wxALIGN_CENTER</property>
                                        <property name="col_label_size">22</property>
                                        <property name="col_label_values">&quot;Active&quot; &quot;Name&quot; &quot;Path&quot; &quot;Status&quot;</property>
                                        <property name="col_label_vert_alignment">wxALIGN_CENTER</property>
                                        <property name="cols">10</property>
                                        <property name="column_sizes">60,200,500,60,0,0,0,0,0,0</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="drag_accept_files">0</property>
                                        <property name="drag_col_move">0</property>
                                        <property name="drag_col_size">1</property>
                                        <property name="drag_grid_size">0</property>
                                        <property name="drag_row_size">1</property>
                                        <property name="editing">0</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="grid_line_color"></property>
                                        <property name="grid_lines">1</property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="label_bg"></property>
                                        <property name="label_font"></property>
                                        <property name="label_text"></property>
                                        <property name="margin_height">0</property>
                                        <property name="margin_width">0</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_grid</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="row_label_horiz_alignment">wxALIGN_CENTER</property>
                                        <property name="row_label_size">0</property>
                                        <property name="row_label_values"></property>
                                        <property name="row_label_vert_alignment">wxALIGN_CENTER</property>
                                        <property name="row_sizes"></property>
                                        <property name="rows">0</property>
                                        <property name="show">1</property>
                                        <property name="size">820,200</property>
                                        <property name="subclass">WX_GRID; widgets/wx_grid.h; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                        <event name="OnGridCellLeftDClick">onGridDClick</event>
                                    </object>
                                </object>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxALL|wxEXPAND</property>
                            <property name="proportion">1</property>
                            <object class="wxBoxSizer" expanded="1">
                                <property name="minimum_size"></property>
                                <property name="name">bButtonsSizer</property>
                                <property name="orient">wxHORIZONTAL</property>
                                <property name="permission">none</property>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxALL</property>
                                    <property name="proportion">0</property>
                                    <object class="wxBitmapButton" expanded="1">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="auth_needed">0</property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="bitmap"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="current"></property>
                                        <property name="default">0</property>
                                        <property name="default_pane">0</property>
                                        <property name="disabled"></property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="drag_accept_files">0</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="focus"></property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="label">MyButton</property>
                                        <property name="margins"></property>
                                        <property name="markup">0</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_btnAddRepo</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="position"></property>
                                        <property name="pressed"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style"></property>
                                        <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip">Add new repository</property>
                                        <property name="validator_data_type"></property>
                                        <property name="validator_style">wxFILTER_NONE</property>
                                        <property name="validator_type">wxDefaultValidator</property>
                                        <property name="validator_variable"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                        <event name="OnButtonClick">onAddClick</event>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxALL</property>
                                    <property name="proportion">0</property>
                                    <object class="wxBitmapButton" expanded="1">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="auth_needed">0</property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="bitmap"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="current"></property>
                                        <property name="default">0</property>
                                        <property name="default_pane">0</property>
                                        <property name="disabled"></property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="drag_accept_files">0</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="focus"></property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="label">MyButton</property>
                                        <property name="margins"></property>
                                        <property name="markup">0</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_btnEditRepo</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="position"></property>
                                        <property name="pressed"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style"></property>
                                        <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip">Edit repository properties</property>
                                        <property name="validator_data_type"></property>
                                        <property name="validator_style">wxFILTER_NONE</property>
                                        <property name="validator_type">wxDefaultValidator</property>
                                        <property name="validator_variable"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                        <event name="OnButtonClick">onEditClick</event>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND</property>
                                    <property name="proportion">1</property>
                                    <object class="spacer" expanded="1">
                                        <property name="height">0</property>
                                        <property name="permission">protected</property>
                                        <property name="width">0</property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxBOTTOM|wxRIGHT|wxTOP</property>
                                    <property name="proportion">0</property>
                                    <object class="wxBitmapButton" expanded="1">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="auth_needed">0</property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="bitmap"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="current"></property>
                                        <property name="default">0</property>
                                        <property name="default_pane">0</property>
                                        <property name="disabled"></property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="drag_accept_files">0</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="focus"></property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="label">MyButton</property>
                                        <property name="margins"></property>
                                        <property name="markup">0</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_btnDelete</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="position"></property>
                                        <property name="pressed"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style"></property>
                                        <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip">Remove Git Repository</property>
                                        <property name="validator_data_type"></property>
                                        <property name="validator_style">wxFILTER_NONE</property>
                                        <property name="validator_type">wxDefaultValidator</property>
                                        <property name="validator_variable"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                        <event name="OnButtonClick">onDeleteClick</event>
                                    </object>
                                </object>
                            </object>
                        </object>
                    </object>
                </object>
            </object>
        </object>
    </object>
</wxFormBuilder_Project>
