<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<wxFormBuilder_Project>
  <FileVersion major="1" minor="18"/>
  <object class="Project" expanded="true">
    <property name="code_generation">C++</property>
    <property name="cpp_class_decoration"></property>
    <property name="cpp_disconnect_events">1</property>
    <property name="cpp_event_generation">connect</property>
    <property name="cpp_help_provider">none</property>
    <property name="cpp_namespace"></property>
    <property name="cpp_precompiled_header"></property>
    <property name="cpp_use_array_enum">0</property>
    <property name="cpp_use_enum">0</property>
    <property name="embedded_files_path">res</property>
    <property name="encoding">UTF-8</property>
    <property name="file">panel_grid_settings_base</property>
    <property name="first_id">1000</property>
    <property name="internationalize">1</property>
    <property name="lua_skip_events">1</property>
    <property name="lua_ui_table">UI</property>
    <property name="name">panel_grid_settings_base</property>
    <property name="path">.</property>
    <property name="php_disconnect_events">0</property>
    <property name="php_disconnect_mode">source_name</property>
    <property name="php_skip_events">1</property>
    <property name="python_disconnect_events">0</property>
    <property name="python_disconnect_mode">source_name</property>
    <property name="python_image_path_wrapper_function_name"></property>
    <property name="python_indent_with_spaces"></property>
    <property name="python_skip_events">1</property>
    <property name="relative_path">1</property>
    <property name="use_microsoft_bom">0</property>
    <property name="use_native_eol">0</property>
    <object class="Panel" expanded="true">
      <property name="aui_managed">0</property>
      <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
      <property name="bg"></property>
      <property name="context_help"></property>
      <property name="context_menu">1</property>
      <property name="drag_accept_files">0</property>
      <property name="enabled">1</property>
      <property name="event_handler">impl_virtual</property>
      <property name="fg"></property>
      <property name="font"></property>
      <property name="hidden">0</property>
      <property name="id">wxID_ANY</property>
      <property name="maximum_size"></property>
      <property name="minimum_size"></property>
      <property name="name">PANEL_GRID_SETTINGS_BASE</property>
      <property name="pos"></property>
      <property name="size">-1,-1</property>
      <property name="subclass">RESETTABLE_PANEL; widgets/resettable_panel.h; forward_declare</property>
      <property name="tooltip"></property>
      <property name="two_step_creation">0</property>
      <property name="window_extra_style"></property>
      <property name="window_name"></property>
      <property name="window_style">wxTAB_TRAVERSAL</property>
      <object class="wxBoxSizer" expanded="true">
        <property name="minimum_size"></property>
        <property name="name">bSizerMain</property>
        <property name="orient">wxVERTICAL</property>
        <property name="permission">none</property>
        <object class="sizeritem" expanded="true">
          <property name="border">8</property>
          <property name="flag">wxEXPAND|wxTOP</property>
          <property name="proportion">1</property>
          <object class="wxBoxSizer" expanded="true">
            <property name="minimum_size"></property>
            <property name="name">bSizerColumns</property>
            <property name="orient">wxHORIZONTAL</property>
            <property name="permission">none</property>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxRIGHT|wxLEFT</property>
              <property name="proportion">0</property>
              <object class="wxBoxSizer" expanded="true">
                <property name="minimum_size"></property>
                <property name="name">bSizerLeftCol</property>
                <property name="orient">wxVERTICAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxTOP|wxRIGHT|wxLEFT</property>
                  <property name="proportion">0</property>
                  <object class="wxStaticText" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="label">Grids</property>
                    <property name="markup">0</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_gridsLabel</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass">; ; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <property name="wrap">-1</property>
                  </object>
                </object>
                <object class="sizeritem" expanded="true">
                  <property name="border">3</property>
                  <property name="flag">wxEXPAND|wxBOTTOM|wxLEFT</property>
                  <property name="proportion">1</property>
                  <object class="wxListBox" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="choices"></property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size">240,-1</property>
                    <property name="moveable">1</property>
                    <property name="name">m_currentGridCtrl</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass">; ; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="validator_data_type"></property>
                    <property name="validator_style">wxFILTER_NONE</property>
                    <property name="validator_type">wxDefaultValidator</property>
                    <property name="validator_variable"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <event name="OnListBoxDClick">OnEditGrid</event>
                  </object>
                </object>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND|wxBOTTOM|wxRIGHT</property>
                  <property name="proportion">0</property>
                  <object class="wxBoxSizer" expanded="true">
                    <property name="minimum_size"></property>
                    <property name="name">bSizerGridButtons</property>
                    <property name="orient">wxHORIZONTAL</property>
                    <property name="permission">none</property>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxRIGHT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxBitmapButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="auth_needed">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="bitmap"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="current"></property>
                        <property name="default">0</property>
                        <property name="default_pane">0</property>
                        <property name="disabled"></property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="focus"></property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Add Grid</property>
                        <property name="margins"></property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_addGridButton</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="position"></property>
                        <property name="pressed"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnButtonClick">OnAddGrid</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxBitmapButton" expanded="true">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="auth_needed">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="bitmap"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="current"></property>
                        <property name="default">0</property>
                        <property name="default_pane">0</property>
                        <property name="disabled"></property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="focus"></property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Edit Grid</property>
                        <property name="margins"></property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_editGridButton</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="position"></property>
                        <property name="pressed"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnButtonClick">OnEditGrid</event>
                        <event name="OnUpdateUI">OnUpdateEditGrid</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxBitmapButton" expanded="true">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="auth_needed">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="bitmap"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="current"></property>
                        <property name="default">0</property>
                        <property name="default_pane">0</property>
                        <property name="disabled"></property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="focus"></property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Move grid up in list</property>
                        <property name="margins"></property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_moveUpButton</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="position"></property>
                        <property name="pressed"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnButtonClick">OnMoveGridUp</event>
                        <event name="OnUpdateUI">OnUpdateMoveUp</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag"></property>
                      <property name="proportion">0</property>
                      <object class="wxBitmapButton" expanded="true">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="auth_needed">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="bitmap"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="current"></property>
                        <property name="default">0</property>
                        <property name="default_pane">0</property>
                        <property name="disabled"></property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="focus"></property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Move grid up in list</property>
                        <property name="margins"></property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_moveDownButton</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="position"></property>
                        <property name="pressed"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnButtonClick">OnMoveGridDown</event>
                        <event name="OnUpdateUI">OnUpdateMoveDown</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxEXPAND</property>
                      <property name="proportion">0</property>
                      <object class="spacer" expanded="false">
                        <property name="height">0</property>
                        <property name="permission">protected</property>
                        <property name="width">25</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag"></property>
                      <property name="proportion">0</property>
                      <object class="wxBitmapButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="auth_needed">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="bitmap"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="current"></property>
                        <property name="default">0</property>
                        <property name="default_pane">0</property>
                        <property name="disabled"></property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="focus"></property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Remove Grid</property>
                        <property name="margins"></property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_removeGridButton</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="position"></property>
                        <property name="pressed"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnButtonClick">OnRemoveGrid</event>
                        <event name="OnUpdateUI">OnUpdateRemove</event>
                      </object>
                    </object>
                  </object>
                </object>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">0</property>
              <object class="spacer" expanded="true">
                <property name="height">0</property>
                <property name="permission">protected</property>
                <property name="width">16</property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxTOP|wxRIGHT|wxLEFT</property>
              <property name="proportion">0</property>
              <object class="wxBoxSizer" expanded="true">
                <property name="minimum_size"></property>
                <property name="name">bSizerRightCol</property>
                <property name="orient">wxVERTICAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">12</property>
                  <property name="flag">wxRIGHT|wxLEFT</property>
                  <property name="proportion">0</property>
                  <object class="wxStaticText" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="label">Fast Grid Switching</property>
                    <property name="markup">0</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_staticText21</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass">; ; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <property name="wrap">-1</property>
                  </object>
                </object>
                <object class="sizeritem" expanded="true">
                  <property name="border">2</property>
                  <property name="flag">wxEXPAND|wxTOP|wxBOTTOM</property>
                  <property name="proportion">0</property>
                  <object class="wxStaticLine" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_staticline2</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style">wxLI_HORIZONTAL</property>
                    <property name="subclass">; ; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                  </object>
                </object>
                <object class="sizeritem" expanded="true">
                  <property name="border">10</property>
                  <property name="flag">wxEXPAND|wxTOP|wxBOTTOM|wxRIGHT</property>
                  <property name="proportion">0</property>
                  <object class="wxFlexGridSizer" expanded="true">
                    <property name="cols">3</property>
                    <property name="flexible_direction">wxBOTH</property>
                    <property name="growablecols">1</property>
                    <property name="growablerows"></property>
                    <property name="hgap">5</property>
                    <property name="minimum_size"></property>
                    <property name="name">fgSizer3</property>
                    <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                    <property name="permission">none</property>
                    <property name="rows">2</property>
                    <property name="vgap">6</property>
                    <object class="sizeritem" expanded="false">
                      <property name="border">8</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Grid 1:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticTextGrid1</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxEXPAND|wxALIGN_CENTER_VERTICAL</property>
                      <property name="proportion">0</property>
                      <object class="wxChoice" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="choices"></property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size">-1,-1</property>
                        <property name="moveable">1</property>
                        <property name="name">m_grid1Ctrl</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="selection">0</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">(hotkey)</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_grid1HotKey</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">8</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxALIGN_RIGHT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Grid 2:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticTextGrid2</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxEXPAND</property>
                      <property name="proportion">0</property>
                      <object class="wxChoice" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="choices"></property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size">-1,-1</property>
                        <property name="moveable">1</property>
                        <property name="name">m_grid2Ctrl</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="selection">0</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">(hotkey)</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_grid2HotKey</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                  </object>
                </object>
                <object class="sizeritem" expanded="true">
                  <property name="border">13</property>
                  <property name="flag">wxTOP|wxRIGHT|wxLEFT</property>
                  <property name="proportion">0</property>
                  <object class="wxStaticText" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="label">Grid Overrides</property>
                    <property name="markup">0</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_overridesLabel</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass">; ; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <property name="wrap">-1</property>
                  </object>
                </object>
                <object class="sizeritem" expanded="true">
                  <property name="border">2</property>
                  <property name="flag">wxEXPAND|wxTOP|wxBOTTOM</property>
                  <property name="proportion">0</property>
                  <object class="wxStaticLine" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_staticline3</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style">wxLI_HORIZONTAL</property>
                    <property name="subclass">; ; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                  </object>
                </object>
                <object class="sizeritem" expanded="true">
                  <property name="border">10</property>
                  <property name="flag">wxEXPAND|wxTOP|wxBOTTOM|wxRIGHT</property>
                  <property name="proportion">0</property>
                  <object class="wxFlexGridSizer" expanded="true">
                    <property name="cols">2</property>
                    <property name="flexible_direction">wxBOTH</property>
                    <property name="growablecols">1</property>
                    <property name="growablerows"></property>
                    <property name="hgap">0</property>
                    <property name="minimum_size"></property>
                    <property name="name">fgGridOverrides</property>
                    <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                    <property name="permission">none</property>
                    <property name="rows">0</property>
                    <property name="vgap">4</property>
                    <object class="sizeritem" expanded="false">
                      <property name="border">8</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxALIGN_LEFT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxCheckBox" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="checked">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Connected items:</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_checkGridOverrideConnected</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxALL</property>
                      <property name="proportion">0</property>
                      <object class="wxChoice" expanded="true">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="choices"></property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_gridOverrideConnectedChoice</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="selection">0</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">8</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxALIGN_LEFT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxCheckBox" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="checked">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Wires:</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_checkGridOverrideWires</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxALL</property>
                      <property name="proportion">0</property>
                      <object class="wxChoice" expanded="true">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="choices"></property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_gridOverrideWiresChoice</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="selection">0</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">8</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxALIGN_LEFT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxCheckBox" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="checked">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Vias:</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_checkGridOverrideVias</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxALL</property>
                      <property name="proportion">0</property>
                      <object class="wxChoice" expanded="true">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="choices"></property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_gridOverrideViasChoice</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="selection">0</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">8</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxALIGN_LEFT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxCheckBox" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="checked">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Text:</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_checkGridOverrideText</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxALL</property>
                      <property name="proportion">0</property>
                      <object class="wxChoice" expanded="true">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="choices"></property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_gridOverrideTextChoice</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="selection">0</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">8</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxALIGN_LEFT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxCheckBox" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="checked">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Graphics:</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_checkGridOverrideGraphics</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="flag">wxALL</property>
                      <property name="proportion">0</property>
                      <object class="wxChoice" expanded="true">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="choices"></property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_gridOverrideGraphicsChoice</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="selection">0</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                  </object>
                </object>
              </object>
            </object>
          </object>
        </object>
      </object>
    </object>
  </object>
</wxFormBuilder_Project>
