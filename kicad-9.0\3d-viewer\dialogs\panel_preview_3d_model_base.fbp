<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<wxFormBuilder_Project>
  <FileVersion major="1" minor="18"/>
  <object class="Project" expanded="true">
    <property name="code_generation">C++</property>
    <property name="cpp_class_decoration"></property>
    <property name="cpp_disconnect_events">1</property>
    <property name="cpp_event_generation">connect</property>
    <property name="cpp_help_provider">none</property>
    <property name="cpp_namespace"></property>
    <property name="cpp_precompiled_header"></property>
    <property name="cpp_use_array_enum">0</property>
    <property name="cpp_use_enum">0</property>
    <property name="embedded_files_path">res</property>
    <property name="encoding">UTF-8</property>
    <property name="file">panel_preview_3d_model_base</property>
    <property name="first_id">1000</property>
    <property name="internationalize">1</property>
    <property name="lua_skip_events">1</property>
    <property name="lua_ui_table">UI</property>
    <property name="name">panel_preview_3d_model_base</property>
    <property name="path">.</property>
    <property name="php_disconnect_events">0</property>
    <property name="php_disconnect_mode">source_name</property>
    <property name="php_skip_events">1</property>
    <property name="python_disconnect_events">0</property>
    <property name="python_disconnect_mode">source_name</property>
    <property name="python_image_path_wrapper_function_name"></property>
    <property name="python_indent_with_spaces"></property>
    <property name="python_skip_events">1</property>
    <property name="relative_path">1</property>
    <property name="use_microsoft_bom">0</property>
    <property name="use_native_eol">0</property>
    <object class="Panel" expanded="true">
      <property name="aui_managed">0</property>
      <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
      <property name="bg"></property>
      <property name="context_help"></property>
      <property name="context_menu">1</property>
      <property name="drag_accept_files">0</property>
      <property name="enabled">1</property>
      <property name="event_handler">impl_virtual</property>
      <property name="fg"></property>
      <property name="font"></property>
      <property name="hidden">0</property>
      <property name="id">wxID_ANY</property>
      <property name="maximum_size"></property>
      <property name="minimum_size"></property>
      <property name="name">PANEL_PREVIEW_3D_MODEL_BASE</property>
      <property name="pos"></property>
      <property name="size">-1,-1</property>
      <property name="subclass"></property>
      <property name="tooltip"></property>
      <property name="two_step_creation">0</property>
      <property name="window_extra_style"></property>
      <property name="window_name"></property>
      <property name="window_style">wxTAB_TRAVERSAL</property>
      <object class="wxBoxSizer" expanded="true">
        <property name="minimum_size"></property>
        <property name="name">bSizermain</property>
        <property name="orient">wxHORIZONTAL</property>
        <property name="permission">none</property>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxEXPAND</property>
          <property name="proportion">0</property>
          <object class="wxBoxSizer" expanded="true">
            <property name="minimum_size"></property>
            <property name="name">bSizerLeft</property>
            <property name="orient">wxVERTICAL</property>
            <property name="permission">none</property>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxTOP|wxRIGHT|wxLEFT</property>
              <property name="proportion">0</property>
              <object class="wxStaticBoxSizer" expanded="true">
                <property name="id">wxID_ANY</property>
                <property name="label">Scale</property>
                <property name="minimum_size"></property>
                <property name="name">sbSizerScale</property>
                <property name="orient">wxVERTICAL</property>
                <property name="parent">1</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">2</property>
                  <property name="flag">wxBOTTOM|wxEXPAND|wxRIGHT</property>
                  <property name="proportion">0</property>
                  <object class="wxFlexGridSizer" expanded="true">
                    <property name="cols">3</property>
                    <property name="flexible_direction">wxBOTH</property>
                    <property name="growablecols">1</property>
                    <property name="growablerows"></property>
                    <property name="hgap">3</property>
                    <property name="minimum_size"></property>
                    <property name="name">fgSizerScale</property>
                    <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                    <property name="permission">none</property>
                    <property name="rows">0</property>
                    <property name="vgap">3</property>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">X:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticText1</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxEXPAND</property>
                      <property name="proportion">0</property>
                      <object class="wxTextCtrl" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="maxlength">0</property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">xscale</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnMouseWheel">onMouseWheelScale</event>
                        <event name="OnText">updateOrientation</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxSpinButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_spinXscale</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style">wxSP_ARROW_KEYS|wxSP_VERTICAL</property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnSpinDown">onDecrementScale</event>
                        <event name="OnSpinUp">onIncrementScale</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Y:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticText2</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxEXPAND</property>
                      <property name="proportion">0</property>
                      <object class="wxTextCtrl" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="maxlength">0</property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">yscale</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnMouseWheel">onMouseWheelScale</event>
                        <event name="OnText">updateOrientation</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL</property>
                      <property name="proportion">0</property>
                      <object class="wxSpinButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_spinYscale</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style">wxSP_ARROW_KEYS|wxSP_VERTICAL</property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnSpinDown">onDecrementScale</event>
                        <event name="OnSpinUp">onIncrementScale</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Z:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticText3</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxEXPAND</property>
                      <property name="proportion">0</property>
                      <object class="wxTextCtrl" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="maxlength">0</property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">zscale</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnMouseWheel">onMouseWheelScale</event>
                        <event name="OnText">updateOrientation</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM</property>
                      <property name="proportion">0</property>
                      <object class="wxSpinButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_spinZscale</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style">wxSP_ARROW_KEYS|wxSP_VERTICAL</property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnSpinDown">onDecrementScale</event>
                        <event name="OnSpinUp">onIncrementScale</event>
                      </object>
                    </object>
                  </object>
                </object>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxLEFT|wxRIGHT|wxTOP</property>
              <property name="proportion">0</property>
              <object class="wxStaticBoxSizer" expanded="true">
                <property name="id">wxID_ANY</property>
                <property name="label">Rotation</property>
                <property name="minimum_size"></property>
                <property name="name">sbSizerRotation</property>
                <property name="orient">wxVERTICAL</property>
                <property name="parent">1</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">2</property>
                  <property name="flag">wxBOTTOM|wxRIGHT|wxEXPAND</property>
                  <property name="proportion">0</property>
                  <object class="wxFlexGridSizer" expanded="true">
                    <property name="cols">3</property>
                    <property name="flexible_direction">wxBOTH</property>
                    <property name="growablecols">1</property>
                    <property name="growablerows"></property>
                    <property name="hgap">3</property>
                    <property name="minimum_size"></property>
                    <property name="name">fgSizerRotate</property>
                    <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                    <property name="permission">none</property>
                    <property name="rows">0</property>
                    <property name="vgap">1</property>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">X:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticText11</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxEXPAND</property>
                      <property name="proportion">0</property>
                      <object class="wxTextCtrl" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="maxlength">0</property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">xrot</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnMouseWheel">onMouseWheelRot</event>
                        <event name="OnText">updateOrientation</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxSpinButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_spinXrot</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style">wxSP_ARROW_KEYS|wxSP_VERTICAL</property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnSpinDown">onDecrementRot</event>
                        <event name="OnSpinUp">onIncrementRot</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Y:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticText21</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxEXPAND</property>
                      <property name="proportion">0</property>
                      <object class="wxTextCtrl" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="maxlength">0</property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">yrot</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnMouseWheel">onMouseWheelRot</event>
                        <event name="OnText">updateOrientation</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL</property>
                      <property name="proportion">0</property>
                      <object class="wxSpinButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_spinYrot</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style">wxSP_ARROW_KEYS|wxSP_VERTICAL</property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnSpinDown">onDecrementRot</event>
                        <event name="OnSpinUp">onIncrementRot</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Z:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticText31</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxEXPAND</property>
                      <property name="proportion">0</property>
                      <object class="wxTextCtrl" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="maxlength">0</property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">zrot</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnMouseWheel">onMouseWheelRot</event>
                        <event name="OnText">updateOrientation</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM</property>
                      <property name="proportion">0</property>
                      <object class="wxSpinButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_spinZrot</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style">wxSP_ARROW_KEYS|wxSP_VERTICAL</property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnSpinDown">onDecrementRot</event>
                        <event name="OnSpinUp">onIncrementRot</event>
                      </object>
                    </object>
                  </object>
                </object>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxLEFT|wxRIGHT|wxTOP</property>
              <property name="proportion">0</property>
              <object class="wxStaticBoxSizer" expanded="true">
                <property name="id">wxID_ANY</property>
                <property name="label">Offset</property>
                <property name="minimum_size"></property>
                <property name="name">sbSizerOffset</property>
                <property name="orient">wxVERTICAL</property>
                <property name="parent">1</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">2</property>
                  <property name="flag">wxBOTTOM|wxRIGHT|wxEXPAND</property>
                  <property name="proportion">0</property>
                  <object class="wxFlexGridSizer" expanded="true">
                    <property name="cols">3</property>
                    <property name="flexible_direction">wxBOTH</property>
                    <property name="growablecols">1</property>
                    <property name="growablerows"></property>
                    <property name="hgap">3</property>
                    <property name="minimum_size"></property>
                    <property name="name">fgSizerOffset</property>
                    <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                    <property name="permission">none</property>
                    <property name="rows">0</property>
                    <property name="vgap">1</property>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">X:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticText12</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxEXPAND</property>
                      <property name="proportion">0</property>
                      <object class="wxTextCtrl" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="maxlength">0</property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">xoff</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnMouseWheel">onMouseWheelOffset</event>
                        <event name="OnText">updateOrientation</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxSpinButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_spinXoffset</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style">wxSP_ARROW_KEYS|wxSP_VERTICAL</property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnSpinDown">onDecrementOffset</event>
                        <event name="OnSpinUp">onIncrementOffset</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Y:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticText22</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxEXPAND</property>
                      <property name="proportion">0</property>
                      <object class="wxTextCtrl" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="maxlength">0</property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">yoff</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">public</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnMouseWheel">onMouseWheelOffset</event>
                        <event name="OnText">updateOrientation</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL</property>
                      <property name="proportion">0</property>
                      <object class="wxSpinButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_spinYoffset</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style">wxSP_ARROW_KEYS|wxSP_VERTICAL</property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnSpinDown">onDecrementOffset</event>
                        <event name="OnSpinUp">onIncrementOffset</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Z:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticText32</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxEXPAND</property>
                      <property name="proportion">0</property>
                      <object class="wxTextCtrl" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="maxlength">0</property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">zoff</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="value"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnMouseWheel">onMouseWheelOffset</event>
                        <event name="OnText">updateOrientation</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM</property>
                      <property name="proportion">0</property>
                      <object class="wxSpinButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer">0</property>
                        <property name="aui_name"></property>
                        <property name="aui_position">0</property>
                        <property name="aui_row">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_spinZoffset</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style">wxSP_ARROW_KEYS|wxSP_VERTICAL</property>
                        <property name="subclass"></property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnSpinDown">onDecrementOffset</event>
                        <event name="OnSpinUp">onIncrementOffset</event>
                      </object>
                    </object>
                  </object>
                </object>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxBOTTOM|wxEXPAND|wxLEFT|wxRIGHT|wxTOP</property>
              <property name="proportion">0</property>
              <object class="wxStaticBoxSizer" expanded="true">
                <property name="id">wxID_ANY</property>
                <property name="label">Opacity</property>
                <property name="minimum_size"></property>
                <property name="name">sbSizer4</property>
                <property name="orient">wxVERTICAL</property>
                <property name="parent">1</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">2</property>
                  <property name="flag">wxRIGHT|wxLEFT</property>
                  <property name="proportion">0</property>
                  <object class="wxSlider" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="maxValue">100</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="minValue">0</property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_opacity</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style">wxSL_HORIZONTAL</property>
                    <property name="subclass">; ; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="validator_data_type"></property>
                    <property name="validator_style">wxFILTER_NONE</property>
                    <property name="validator_type">wxDefaultValidator</property>
                    <property name="validator_variable"></property>
                    <property name="value">50</property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <event name="OnSlider">onOpacitySlider</event>
                  </object>
                </object>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">1</property>
              <object class="spacer" expanded="true">
                <property name="height">0</property>
                <property name="permission">protected</property>
                <property name="width">0</property>
              </object>
            </object>
          </object>
        </object>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxEXPAND|wxLEFT|wxRIGHT</property>
          <property name="proportion">1</property>
          <object class="wxBoxSizer" expanded="true">
            <property name="minimum_size"></property>
            <property name="name">bSizerRight</property>
            <property name="orient">wxVERTICAL</property>
            <property name="permission">none</property>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">0</property>
              <object class="wxBoxSizer" expanded="true">
                <property name="minimum_size"></property>
                <property name="name">bSizer6</property>
                <property name="orient">wxHORIZONTAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND|wxTOP|wxRIGHT|wxLEFT</property>
                  <property name="proportion">1</property>
                  <object class="wxStaticText" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font">,90,90,11,70,0</property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="label">Preview</property>
                    <property name="markup">0</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_previewLabel</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass">; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <property name="wrap">-1</property>
                  </object>
                </object>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxBOTTOM|wxEXPAND</property>
              <property name="proportion">1</property>
              <object class="wxBoxSizer" expanded="true">
                <property name="minimum_size">-1,-1</property>
                <property name="name">m_SizerPanelView</property>
                <property name="orient">wxVERTICAL</property>
                <property name="permission">protected</property>
              </object>
            </object>
          </object>
        </object>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxEXPAND|wxRIGHT</property>
          <property name="proportion">0</property>
          <object class="wxBoxSizer" expanded="true">
            <property name="minimum_size"></property>
            <property name="name">bSizer3DButtons</property>
            <property name="orient">wxVERTICAL</property>
            <property name="permission">none</property>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">0</property>
              <object class="spacer" expanded="true">
                <property name="height">14</property>
                <property name="permission">protected</property>
                <property name="width">0</property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxTOP</property>
              <property name="proportion">0</property>
              <object class="wxBitmapButton" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">MyButton</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_bpvISO</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">-1,-1</property>
                <property name="style"></property>
                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip">Enable/disable orthographic projection</property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">View3DISO</event>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxTOP</property>
              <property name="proportion">0</property>
              <object class="wxBitmapButton" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">MyButton</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_bpvBodyStyle</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">-1,-1</property>
                <property name="style"></property>
                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip">Show or hide the board body&#x0A;If hidden, show only copper and silkscreen layers.</property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">setBodyStyleView</event>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">0</property>
              <object class="spacer" expanded="true">
                <property name="height">20</property>
                <property name="permission">protected</property>
                <property name="width">0</property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxBOTTOM</property>
              <property name="proportion">0</property>
              <object class="wxBitmapButton" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">MyButton</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_bpvLeft</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">-1,-1</property>
                <property name="style"></property>
                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">View3DLeft</event>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxBOTTOM</property>
              <property name="proportion">0</property>
              <object class="wxBitmapButton" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">MyButton</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_bpvRight</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">-1,-1</property>
                <property name="style"></property>
                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">View3DRight</event>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxBOTTOM</property>
              <property name="proportion">0</property>
              <object class="wxBitmapButton" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">MyButton</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_bpvFront</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">-1,-1</property>
                <property name="style"></property>
                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">View3DFront</event>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxBOTTOM</property>
              <property name="proportion">0</property>
              <object class="wxBitmapButton" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">MyButton</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_bpvBack</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">-1,-1</property>
                <property name="style"></property>
                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">View3DBack</event>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxBOTTOM</property>
              <property name="proportion">0</property>
              <object class="wxBitmapButton" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">MyButton</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_bpvTop</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">-1,-1</property>
                <property name="style"></property>
                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">View3DTop</event>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag"></property>
              <property name="proportion">0</property>
              <object class="wxBitmapButton" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">MyButton</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_bpvBottom</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">-1,-1</property>
                <property name="style"></property>
                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">View3DBottom</event>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">0</property>
              <object class="spacer" expanded="true">
                <property name="height">20</property>
                <property name="permission">protected</property>
                <property name="width">0</property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxTOP</property>
              <property name="proportion">0</property>
              <object class="wxBitmapButton" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">MyButton</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_bpUpdate</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">-1,-1</property>
                <property name="style"></property>
                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip">Reload board and 3D models</property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">View3DUpdate</event>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxTOP|wxBOTTOM</property>
              <property name="proportion">0</property>
              <object class="wxBitmapButton" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">MyButton</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_bpSettings</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">View3DSettings</event>
              </object>
            </object>
          </object>
        </object>
      </object>
    </object>
  </object>
</wxFormBuilder_Project>
