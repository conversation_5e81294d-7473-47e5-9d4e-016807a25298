<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<wxFormBuilder_Project>
    <FileVersion major="1" minor="16" />
    <object class="Project" expanded="1">
        <property name="class_decoration"></property>
        <property name="code_generation">C++</property>
        <property name="disconnect_events">1</property>
        <property name="disconnect_mode">source_name</property>
        <property name="disconnect_php_events">0</property>
        <property name="disconnect_python_events">0</property>
        <property name="embedded_files_path">res</property>
        <property name="encoding">UTF-8</property>
        <property name="event_generation">connect</property>
        <property name="file">panel_design_block_lib_table_base</property>
        <property name="first_id">1000</property>
        <property name="help_provider">none</property>
        <property name="image_path_wrapper_function_name"></property>
        <property name="indent_with_spaces"></property>
        <property name="internationalize">1</property>
        <property name="name">panel_design_block_lib_table</property>
        <property name="namespace"></property>
        <property name="path">.</property>
        <property name="precompiled_header"></property>
        <property name="relative_path">1</property>
        <property name="skip_lua_events">1</property>
        <property name="skip_php_events">1</property>
        <property name="skip_python_events">1</property>
        <property name="ui_table">UI</property>
        <property name="use_array_enum">0</property>
        <property name="use_enum">0</property>
        <property name="use_microsoft_bom">0</property>
        <object class="Panel" expanded="1">
            <property name="aui_managed">0</property>
            <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
            <property name="bg"></property>
            <property name="context_help"></property>
            <property name="context_menu">1</property>
            <property name="enabled">1</property>
            <property name="event_handler">impl_virtual</property>
            <property name="fg"></property>
            <property name="font"></property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="maximum_size"></property>
            <property name="minimum_size"></property>
            <property name="name">PANEL_DESIGN_BLOCK_LIB_TABLE_BASE</property>
            <property name="pos"></property>
            <property name="size">-1,-1</property>
            <property name="subclass">; forward_declare</property>
            <property name="tooltip"></property>
            <property name="two_step_creation">0</property>
            <property name="window_extra_style"></property>
            <property name="window_name"></property>
            <property name="window_style">wxTAB_TRAVERSAL</property>
            <event name="OnUpdateUI">OnUpdateUI</event>
            <object class="wxBoxSizer" expanded="1">
                <property name="minimum_size"></property>
                <property name="name">bMainSizer</property>
                <property name="orient">wxVERTICAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="1">
                    <property name="border">5</property>
                    <property name="flag">wxEXPAND|wxRIGHT|wxLEFT</property>
                    <property name="proportion">1</property>
                    <object class="wxNotebook" expanded="1">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="bitmapsize"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_notebook</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <object class="notebookpage" expanded="1">
                            <property name="bitmap"></property>
                            <property name="label">Global Libraries</property>
                            <property name="select">1</property>
                            <object class="wxPanel" expanded="1">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="close_button">0</property>
                                <property name="context_help"></property>
                                <property name="context_menu">0</property>
                                <property name="default_pane">0</property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size"></property>
                                <property name="moveable">1</property>
                                <property name="name">m_global_panel</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">0</property>
                                <property name="pos"></property>
                                <property name="resize">Resizable</property>
                                <property name="show">1</property>
                                <property name="size"></property>
                                <property name="subclass"></property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style">wxTAB_TRAVERSAL</property>
                                <object class="wxBoxSizer" expanded="1">
                                    <property name="minimum_size"></property>
                                    <property name="name">m_global_sizer</property>
                                    <property name="orient">wxVERTICAL</property>
                                    <property name="permission">none</property>
                                    <object class="sizeritem" expanded="0">
                                        <property name="border">5</property>
                                        <property name="flag">wxALL|wxEXPAND</property>
                                        <property name="proportion">1</property>
                                        <object class="wxGrid" expanded="0">
                                            <property name="BottomDockable">1</property>
                                            <property name="LeftDockable">1</property>
                                            <property name="RightDockable">1</property>
                                            <property name="TopDockable">1</property>
                                            <property name="aui_layer"></property>
                                            <property name="aui_name"></property>
                                            <property name="aui_position"></property>
                                            <property name="aui_row"></property>
                                            <property name="autosize_cols">0</property>
                                            <property name="autosize_rows">0</property>
                                            <property name="best_size"></property>
                                            <property name="bg"></property>
                                            <property name="caption"></property>
                                            <property name="caption_visible">1</property>
                                            <property name="cell_bg"></property>
                                            <property name="cell_font"></property>
                                            <property name="cell_horiz_alignment">wxALIGN_LEFT</property>
                                            <property name="cell_text"></property>
                                            <property name="cell_vert_alignment">wxALIGN_CENTER</property>
                                            <property name="center_pane">0</property>
                                            <property name="close_button">1</property>
                                            <property name="col_label_horiz_alignment">wxALIGN_CENTER</property>
                                            <property name="col_label_size">22</property>
                                            <property name="col_label_values">&quot;Active&quot; &quot;Visible&quot; &quot;Nickname&quot; &quot;Library Path&quot; &quot;Library Format&quot; &quot;Options&quot; &quot;Description&quot;</property>
                                            <property name="col_label_vert_alignment">wxALIGN_CENTER</property>
                                            <property name="cols">7</property>
                                            <property name="column_sizes">48,48,100,240,100,80,240</property>
                                            <property name="context_help"></property>
                                            <property name="context_menu">1</property>
                                            <property name="default_pane">0</property>
                                            <property name="dock">Dock</property>
                                            <property name="dock_fixed">1</property>
                                            <property name="docking">Left</property>
                                            <property name="drag_col_move">0</property>
                                            <property name="drag_col_size">1</property>
                                            <property name="drag_grid_size">0</property>
                                            <property name="drag_row_size">0</property>
                                            <property name="editing">1</property>
                                            <property name="enabled">1</property>
                                            <property name="fg"></property>
                                            <property name="floatable">0</property>
                                            <property name="font"></property>
                                            <property name="grid_line_color"></property>
                                            <property name="grid_lines">1</property>
                                            <property name="gripper">0</property>
                                            <property name="hidden">0</property>
                                            <property name="id">wxID_ANY</property>
                                            <property name="label_bg"></property>
                                            <property name="label_font"></property>
                                            <property name="label_text"></property>
                                            <property name="margin_height">0</property>
                                            <property name="margin_width">0</property>
                                            <property name="max_size"></property>
                                            <property name="maximize_button">0</property>
                                            <property name="maximum_size"></property>
                                            <property name="min_size"></property>
                                            <property name="minimize_button">0</property>
                                            <property name="minimum_size"></property>
                                            <property name="moveable">0</property>
                                            <property name="name">m_global_grid</property>
                                            <property name="pane_border">1</property>
                                            <property name="pane_position"></property>
                                            <property name="pane_size"></property>
                                            <property name="permission">protected</property>
                                            <property name="pin_button">1</property>
                                            <property name="pos"></property>
                                            <property name="resize">Fixed</property>
                                            <property name="row_label_horiz_alignment">wxALIGN_CENTER</property>
                                            <property name="row_label_size">0</property>
                                            <property name="row_label_values"></property>
                                            <property name="row_label_vert_alignment">wxALIGN_CENTER</property>
                                            <property name="row_sizes"></property>
                                            <property name="rows">1</property>
                                            <property name="show">1</property>
                                            <property name="size"></property>
                                            <property name="subclass">WX_GRID; widgets/wx_grid.h; forward_declare</property>
                                            <property name="toolbar_pane">0</property>
                                            <property name="tooltip"></property>
                                            <property name="window_extra_style"></property>
                                            <property name="window_name"></property>
                                            <property name="window_style"></property>
                                        </object>
                                    </object>
                                </object>
                            </object>
                        </object>
                        <object class="notebookpage" expanded="1">
                            <property name="bitmap"></property>
                            <property name="label">Project Specific Libraries</property>
                            <property name="select">0</property>
                            <object class="wxPanel" expanded="1">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="close_button">1</property>
                                <property name="context_help"></property>
                                <property name="context_menu">0</property>
                                <property name="default_pane">0</property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size"></property>
                                <property name="moveable">1</property>
                                <property name="name">m_project_panel</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">1</property>
                                <property name="pos"></property>
                                <property name="resize">Resizable</property>
                                <property name="show">1</property>
                                <property name="size"></property>
                                <property name="subclass"></property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style">wxTAB_TRAVERSAL</property>
                                <object class="wxBoxSizer" expanded="1">
                                    <property name="minimum_size"></property>
                                    <property name="name">m_project_sizer</property>
                                    <property name="orient">wxVERTICAL</property>
                                    <property name="permission">none</property>
                                    <object class="sizeritem" expanded="0">
                                        <property name="border">5</property>
                                        <property name="flag">wxALL|wxEXPAND</property>
                                        <property name="proportion">1</property>
                                        <object class="wxGrid" expanded="0">
                                            <property name="BottomDockable">1</property>
                                            <property name="LeftDockable">1</property>
                                            <property name="RightDockable">1</property>
                                            <property name="TopDockable">1</property>
                                            <property name="aui_layer"></property>
                                            <property name="aui_name"></property>
                                            <property name="aui_position"></property>
                                            <property name="aui_row"></property>
                                            <property name="autosize_cols">0</property>
                                            <property name="autosize_rows">0</property>
                                            <property name="best_size"></property>
                                            <property name="bg"></property>
                                            <property name="caption"></property>
                                            <property name="caption_visible">1</property>
                                            <property name="cell_bg"></property>
                                            <property name="cell_font"></property>
                                            <property name="cell_horiz_alignment">wxALIGN_LEFT</property>
                                            <property name="cell_text"></property>
                                            <property name="cell_vert_alignment">wxALIGN_CENTER</property>
                                            <property name="center_pane">0</property>
                                            <property name="close_button">1</property>
                                            <property name="col_label_horiz_alignment">wxALIGN_CENTER</property>
                                            <property name="col_label_size">22</property>
                                            <property name="col_label_values">&quot;Active&quot; &quot;Visible&quot; &quot;Nickname&quot; &quot;Library Path&quot; &quot;LIbrary Format&quot; &quot;Options&quot; &quot;Description&quot;</property>
                                            <property name="col_label_vert_alignment">wxALIGN_CENTER</property>
                                            <property name="cols">7</property>
                                            <property name="column_sizes">48,48,100,240,100,80,240</property>
                                            <property name="context_help"></property>
                                            <property name="context_menu">1</property>
                                            <property name="default_pane">0</property>
                                            <property name="dock">Dock</property>
                                            <property name="dock_fixed">1</property>
                                            <property name="docking">Left</property>
                                            <property name="drag_col_move">0</property>
                                            <property name="drag_col_size">1</property>
                                            <property name="drag_grid_size">0</property>
                                            <property name="drag_row_size">0</property>
                                            <property name="editing">1</property>
                                            <property name="enabled">1</property>
                                            <property name="fg"></property>
                                            <property name="floatable">0</property>
                                            <property name="font"></property>
                                            <property name="grid_line_color"></property>
                                            <property name="grid_lines">1</property>
                                            <property name="gripper">0</property>
                                            <property name="hidden">0</property>
                                            <property name="id">wxID_ANY</property>
                                            <property name="label_bg"></property>
                                            <property name="label_font"></property>
                                            <property name="label_text"></property>
                                            <property name="margin_height">0</property>
                                            <property name="margin_width">0</property>
                                            <property name="max_size"></property>
                                            <property name="maximize_button">0</property>
                                            <property name="maximum_size"></property>
                                            <property name="min_size"></property>
                                            <property name="minimize_button">0</property>
                                            <property name="minimum_size"></property>
                                            <property name="moveable">0</property>
                                            <property name="name">m_project_grid</property>
                                            <property name="pane_border">1</property>
                                            <property name="pane_position"></property>
                                            <property name="pane_size"></property>
                                            <property name="permission">protected</property>
                                            <property name="pin_button">1</property>
                                            <property name="pos"></property>
                                            <property name="resize">Fixed</property>
                                            <property name="row_label_horiz_alignment">wxALIGN_CENTER</property>
                                            <property name="row_label_size">0</property>
                                            <property name="row_label_values"></property>
                                            <property name="row_label_vert_alignment">wxALIGN_CENTER</property>
                                            <property name="row_sizes"></property>
                                            <property name="rows">1</property>
                                            <property name="show">1</property>
                                            <property name="size"></property>
                                            <property name="subclass">WX_GRID; widgets/wx_grid.h; forward_declare</property>
                                            <property name="toolbar_pane">0</property>
                                            <property name="tooltip"></property>
                                            <property name="window_extra_style"></property>
                                            <property name="window_name"></property>
                                            <property name="window_style"></property>
                                        </object>
                                    </object>
                                </object>
                            </object>
                        </object>
                    </object>
                </object>
                <object class="sizeritem" expanded="1">
                    <property name="border">8</property>
                    <property name="flag">wxEXPAND|wxALL</property>
                    <property name="proportion">0</property>
                    <object class="wxBoxSizer" expanded="1">
                        <property name="minimum_size"></property>
                        <property name="name">bButtonsSizer</property>
                        <property name="orient">wxHORIZONTAL</property>
                        <property name="permission">none</property>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxTOP|wxBOTTOM|wxRIGHT</property>
                            <property name="proportion">0</property>
                            <object class="wxBitmapButton" expanded="1">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="auth_needed">0</property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="bitmap"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="close_button">1</property>
                                <property name="context_help"></property>
                                <property name="context_menu">1</property>
                                <property name="current"></property>
                                <property name="default">0</property>
                                <property name="default_pane">0</property>
                                <property name="disabled"></property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="focus"></property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="label">Add Library</property>
                                <property name="margins"></property>
                                <property name="markup">0</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size"></property>
                                <property name="moveable">1</property>
                                <property name="name">m_append_button</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">1</property>
                                <property name="pos"></property>
                                <property name="position"></property>
                                <property name="pressed"></property>
                                <property name="resize">Resizable</property>
                                <property name="show">1</property>
                                <property name="size">-1,-1</property>
                                <property name="style"></property>
                                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip">Add empty row to table</property>
                                <property name="validator_data_type"></property>
                                <property name="validator_style">wxFILTER_NONE</property>
                                <property name="validator_type">wxDefaultValidator</property>
                                <property name="validator_variable"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style"></property>
                                <event name="OnButtonClick">appendRowHandler</event>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxTOP|wxBOTTOM|wxRIGHT</property>
                            <property name="proportion">0</property>
                            <object class="CustomControl" expanded="1">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="class">SPLIT_BUTTON</property>
                                <property name="close_button">1</property>
                                <property name="construction">m_browseButton = new SPLIT_BUTTON( this, wxID_ANY, _( &quot;Add Existing&quot; ), wxDefaultPosition );</property>
                                <property name="context_help"></property>
                                <property name="context_menu">1</property>
                                <property name="declaration"></property>
                                <property name="default_pane">0</property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="include">#include &lt;widgets/split_button.h&gt;</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size">-1,-1</property>
                                <property name="moveable">1</property>
                                <property name="name">m_browseButton</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">1</property>
                                <property name="pos"></property>
                                <property name="resize">Resizable</property>
                                <property name="settings"></property>
                                <property name="show">1</property>
                                <property name="size">-1,-1</property>
                                <property name="subclass">; ; forward_declare</property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip">Add Existing</property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style"></property>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxTOP|wxBOTTOM|wxRIGHT</property>
                            <property name="proportion">0</property>
                            <object class="wxBitmapButton" expanded="1">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="auth_needed">0</property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="bitmap"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="close_button">1</property>
                                <property name="context_help"></property>
                                <property name="context_menu">1</property>
                                <property name="current"></property>
                                <property name="default">0</property>
                                <property name="default_pane">0</property>
                                <property name="disabled"></property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="focus"></property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="label">Move Up</property>
                                <property name="margins"></property>
                                <property name="markup">0</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size"></property>
                                <property name="moveable">1</property>
                                <property name="name">m_move_up_button</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">1</property>
                                <property name="pos"></property>
                                <property name="position"></property>
                                <property name="pressed"></property>
                                <property name="resize">Resizable</property>
                                <property name="show">1</property>
                                <property name="size">-1,-1</property>
                                <property name="style"></property>
                                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip">Move up</property>
                                <property name="validator_data_type"></property>
                                <property name="validator_style">wxFILTER_NONE</property>
                                <property name="validator_type">wxDefaultValidator</property>
                                <property name="validator_variable"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style"></property>
                                <event name="OnButtonClick">moveUpHandler</event>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxTOP|wxBOTTOM|wxRIGHT</property>
                            <property name="proportion">0</property>
                            <object class="wxBitmapButton" expanded="1">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="auth_needed">0</property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="bitmap"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="close_button">1</property>
                                <property name="context_help"></property>
                                <property name="context_menu">1</property>
                                <property name="current"></property>
                                <property name="default">0</property>
                                <property name="default_pane">0</property>
                                <property name="disabled"></property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="focus"></property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="label">Move Down</property>
                                <property name="margins"></property>
                                <property name="markup">0</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size"></property>
                                <property name="moveable">1</property>
                                <property name="name">m_move_down_button</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">1</property>
                                <property name="pos"></property>
                                <property name="position"></property>
                                <property name="pressed"></property>
                                <property name="resize">Resizable</property>
                                <property name="show">1</property>
                                <property name="size">-1,-1</property>
                                <property name="style"></property>
                                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip">Move down</property>
                                <property name="validator_data_type"></property>
                                <property name="validator_style">wxFILTER_NONE</property>
                                <property name="validator_type">wxDefaultValidator</property>
                                <property name="validator_variable"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style"></property>
                                <event name="OnButtonClick">moveDownHandler</event>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxEXPAND</property>
                            <property name="proportion">0</property>
                            <object class="spacer" expanded="1">
                                <property name="height">0</property>
                                <property name="permission">protected</property>
                                <property name="width">20</property>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxTOP|wxBOTTOM|wxRIGHT</property>
                            <property name="proportion">0</property>
                            <object class="wxBitmapButton" expanded="1">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="auth_needed">0</property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="bitmap"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="close_button">1</property>
                                <property name="context_help"></property>
                                <property name="context_menu">1</property>
                                <property name="current"></property>
                                <property name="default">0</property>
                                <property name="default_pane">0</property>
                                <property name="disabled"></property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="focus"></property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="label">Remove Library</property>
                                <property name="margins"></property>
                                <property name="markup">0</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size"></property>
                                <property name="moveable">1</property>
                                <property name="name">m_delete_button</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">1</property>
                                <property name="pos"></property>
                                <property name="position"></property>
                                <property name="pressed"></property>
                                <property name="resize">Resizable</property>
                                <property name="show">1</property>
                                <property name="size">-1,-1</property>
                                <property name="style"></property>
                                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip">Remove library from table</property>
                                <property name="validator_data_type"></property>
                                <property name="validator_style">wxFILTER_NONE</property>
                                <property name="validator_type">wxDefaultValidator</property>
                                <property name="validator_variable"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style"></property>
                                <event name="OnButtonClick">deleteRowHandler</event>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxEXPAND</property>
                            <property name="proportion">1</property>
                            <object class="spacer" expanded="1">
                                <property name="height">0</property>
                                <property name="permission">protected</property>
                                <property name="width">20</property>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxALIGN_CENTER_VERTICAL|wxALL</property>
                            <property name="proportion">0</property>
                            <object class="wxButton" expanded="1">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="auth_needed">0</property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="bitmap"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="close_button">1</property>
                                <property name="context_help"></property>
                                <property name="context_menu">1</property>
                                <property name="current"></property>
                                <property name="default">0</property>
                                <property name="default_pane">0</property>
                                <property name="disabled"></property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="focus"></property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="label">Migrate Libraries</property>
                                <property name="margins"></property>
                                <property name="markup">0</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size"></property>
                                <property name="moveable">1</property>
                                <property name="name">m_migrate_libs_button</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">1</property>
                                <property name="pos"></property>
                                <property name="position"></property>
                                <property name="pressed"></property>
                                <property name="resize">Resizable</property>
                                <property name="show">1</property>
                                <property name="size"></property>
                                <property name="style"></property>
                                <property name="subclass">; ; forward_declare</property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip"></property>
                                <property name="validator_data_type"></property>
                                <property name="validator_style">wxFILTER_NONE</property>
                                <property name="validator_type">wxDefaultValidator</property>
                                <property name="validator_variable"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style"></property>
                                <event name="OnButtonClick">onMigrateLibraries</event>
                            </object>
                        </object>
                    </object>
                </object>
                <object class="sizeritem" expanded="1">
                    <property name="border">8</property>
                    <property name="flag">wxTOP|wxRIGHT|wxLEFT|wxEXPAND</property>
                    <property name="proportion">0</property>
                    <object class="wxStaticText" expanded="1">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Available path substitutions:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">stPathsLabel</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">none</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                    </object>
                </object>
                <object class="sizeritem" expanded="1">
                    <property name="border">5</property>
                    <property name="flag">wxEXPAND</property>
                    <property name="proportion">0</property>
                    <object class="spacer" expanded="1">
                        <property name="height">2</property>
                        <property name="permission">protected</property>
                        <property name="width">0</property>
                    </object>
                </object>
                <object class="sizeritem" expanded="0">
                    <property name="border">5</property>
                    <property name="flag">wxEXPAND|wxBOTTOM|wxRIGHT|wxLEFT</property>
                    <property name="proportion">0</property>
                    <object class="wxGrid" expanded="0">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="autosize_cols">1</property>
                        <property name="autosize_rows">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="cell_bg"></property>
                        <property name="cell_font"></property>
                        <property name="cell_horiz_alignment">wxALIGN_LEFT</property>
                        <property name="cell_text"></property>
                        <property name="cell_vert_alignment">wxALIGN_CENTER</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="col_label_horiz_alignment">wxALIGN_CENTER</property>
                        <property name="col_label_size">0</property>
                        <property name="col_label_values"></property>
                        <property name="col_label_vert_alignment">wxALIGN_CENTER</property>
                        <property name="cols">2</property>
                        <property name="column_sizes">150,500</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_col_move">0</property>
                        <property name="drag_col_size">1</property>
                        <property name="drag_grid_size">0</property>
                        <property name="drag_row_size">1</property>
                        <property name="editing">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="grid_line_color"></property>
                        <property name="grid_lines">1</property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label_bg"></property>
                        <property name="label_font"></property>
                        <property name="label_text"></property>
                        <property name="margin_height">0</property>
                        <property name="margin_width">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_path_subs_grid</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="row_label_horiz_alignment">wxALIGN_CENTER</property>
                        <property name="row_label_size">0</property>
                        <property name="row_label_values"></property>
                        <property name="row_label_vert_alignment">wxALIGN_CENTER</property>
                        <property name="row_sizes"></property>
                        <property name="rows">1</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="subclass">WX_GRID; widgets/wx_grid.h; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip">This is a read-only table which shows pertinent environment variables.</property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnSize">onSizeGrid</event>
                    </object>
                </object>
            </object>
        </object>
    </object>
</wxFormBuilder_Project>
