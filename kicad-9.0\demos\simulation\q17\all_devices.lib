*
.SUBCKT BS250P 3 4 5
*              D G S
M1 3 2 5 5 MBS250
RG 4 2 160
RL 3 5 1.2E8
C1 2 5 47E-12
C2 3 2 10E-12 
D1 3 5 DBS250
*
.MODEL MBS250 PMOS VTO=-3.193 RS=2.041 RD=0.697 IS=1E-15 KP=0.277
+CBD=105E-12 PB=1 LAMBDA=1.2E-2
.MODEL DBS250 D IS=2E-13 RS=0.309
.ENDS BS250P
*

*
*ZETEX  2N7000  Spice model    Last revision  3/5/00
*
.SUBCKT 2N7000_ZX 3 4 5
* Nodes        D G S
M1 3 2 5 5 MOD1
RG 4 2 343
RL 3 5 6E6
C1 2 5 23.5P
C2 3 2 4.5P 
D1 5 3 DIODE1
*
.MODEL MOD1 NMOS VTO=2.474 RS=1.68 RD=0.0 IS=1E-15 KP=0.296
+CBD=53.5P PB=1 LAMBDA=267E-6
.MODEL DIODE1 D IS=1.254E-13 N=1.0207 RS=0.222
.ENDS 2N7000_ZX
*

.model Q2SC2240 NPN(Is=1.41f Xti=3 Eg=1.11 Vaf=100 Bf=310 Ne=1.5 Ise=0
+               Ikf=70m Xtb=1.5 Br=.8893 Nc=2 Isc=0 Ikr=0 Rc=30 Cjc=6.878p
+               Mjc=.2725 Vjc=.75 Fc=.5 Cje=5p Mje=.3333 Vje=.75 Tr=10n
+               Tf=1.276n Itf=0 Vtf=0 Xtf=0)
*		TOSHIBA		90-01-29	creation

**********
*SRC=2SA970;QSA970;BJTs PNP;Amplifier;120 V .1A
.MODEL QSA970 PNP (IS=10.1F NF=1 BF=426 VAF=197 IKF=60M ISE=2.38P NE=2
+ BR=4 NR=1 VAR=20 IKR=90M RE=13.1 RB=52.6 RC=5.26 XTB=1.5
+ CJE=38.6P VJE=1.1 MJE=.5 CJC=12.4P VJC=.3 MJC=.3 TF=1.59N TR=1.1U)
*  120 Volt  .1 Amp  100 MHz  SiPNP  Transistor  07-28-1995
*QSA970, TOSHIBA 
**********

.SUBCKT IRF610_IR 1 2 3 
************************************** 
*      Model Generated by MODPEX     * 
*Copyright(c) Symmetry Design Systems* 
*         All Rights Reserved        * 
*    UNPUBLISHED LICENSED SOFTWARE   * 
*   Contains Proprietary Information * 
*      Which is The Property of      * 
*     SYMMETRY OR ITS LICENSORS      * 
*Commercial Use or Resale Restricted * 
*   by Symmetry License Agreement    * 
************************************** 
* Model generated on Oct 29, 97 
* MODEL FORMAT: SPICE3 
* Symmetry POWER MOS Model (Version 1.0) 
* External Node Designations 
* Node 1 -> Drain 
* Node 2 -> Gate 
* Node 3 -> Source
M1 9 7 8 8 MM L=100u W=100u 
* Default values used in MM: 
* The voltage-dependent capacitances are 
* not included. Other default values are: 
*   RS=0 RD=0 LD=0 CBD=0 CBS=0 CGBO=0
.MODEL MM NMOS LEVEL=1 IS=1e-32 
+VTO=3.94473 LAMBDA=0.00953957 KP=0.484056 
+CGSO=1.26059e-06 CGDO=1.00178e-11
RS 8 3 0.0001
D1 3 1 MD
.MODEL MD D IS=1.6866e-09 RS=0.0538695 N=1.49978 BV=200 
+IBV=0.00025 EG=1.2 XTI=4 TT=0 
+CJO=1.59879e-10 VJ=2.42435 M=0.605977 FC=0.5
RDS 3 1 1e+06
RD 9 1 1.14151
RG 2 7 5.34748
D2 4 5 MD1 
* Default values used in MD1: 
*   RS=0 EG=1.11 XTI=3.0 TT=0 
*   BV=infinite IBV=1mA
.MODEL MD1 D IS=1e-32 N=50 
+CJO=2.10468e-10 VJ=1.4522 M=0.87562 FC=1e-08
D3 0 5 MD2 
* Default values used in MD2: 
*   EG=1.11 XTI=3.0 TT=0 CJO=0 
*   BV=infinite IBV=1mA
.MODEL MD2 D IS=1e-10 N=0.4 RS=3e-06
RL 5 10 1
FI2 7 9 VFI2 -1
VFI2 4 0 0
EV16 10 0 9 7 1
CAP 11 10 4.00016e-10
FI1 7 9 VFI1 -1
VFI1 11 6 0
RCAP 6 10 1
D4 0 6 MD3 
* Default values used in MD3: 
*   EG=1.11 XTI=3.0 TT=0 CJO=0 
*   RS=0 BV=infinite IBV=1mA
.MODEL MD3 D IS=1e-10 N=0.4
.ENDS IRF610_IR



**********
*SRC=IRF9610S;IRF9610S;MOSFETs P;Power >100V;200V 2A 3ohm 
*SYM=POWMOSP
*PINOUT SMD-220
.SUBCKT IRF9610S 10 20 40
*     TERMINALS:  D  G  S
M1   1  2  3  3  DMOS L=1U W=1U
RD  10  1  1.42
RS  30  3  76M
RG  20  2  83.3
CGS  2  3  155P
EGD 12  0  1  2  1
VFB 14  0  0
FFB  1  2  VFB  1
CGD 13 14  193P
R1  13  0  1
D1  12 13  DLIM
DDG 15 14  DCGD
R2  12 15  1
D2  15  0  DLIM
DSD 10  3  DSUB
LS  30 40  7.5N
.MODEL DMOS PMOS (LEVEL=3 VMAX=417K THETA=58.1M ETA=2M VTO=-3.1 KP=0.865)
.MODEL DCGD D (CJO=193P VJ=0.6 M=0.68)
.MODEL DSUB D (IS=7.47N N=1.5 RS=2.81 BV=200 CJO=151P VJ=0.8 M=0.42 TT=240N)
.MODEL DLIM D (IS=100U)
.ENDS 
**********

* GENERIC FUNCTIONAL EQUIVALENT = 1N5245
* MANUFACTURER = SPRAGUE
* TYPE: DIODE
* SUBTYPE: VOLTAGE REG GP
* THIS IS A TEMPERATURE TRACKING MODEL CONSTRUCTED FROM MEASUREMENTS
* THE MODEL IS INTENDED FOR USE FROM -55 C TO 125 C.  NO RADIATION EFFECTS
* ARE INCLUDED.  SIMULATIONS USING THIS MODEL REPRESENT THE RESPONSES OF
* NOMINAL DEVICES AND SIMULATIONS ARE ACCURATE WITHIN THE LIMITS OF THE
* PRODUCT SPECIFICATION.
*** CAUTION: THE SIMULATED TRR RANGES FROM 73 TO 96% OF THE MEASURED TRR.
*            THIS COULD POTENTIALLY LEAD TO ERRORS IN CIRCUIT. SIMULATIONS IF
*            USED IN HIGH SPEED SWITCHING APPLICATIONS.
.SUBCKT D1N5245/TEMP     1 3
D1 1 3 DFOR
D2 3 2 DBLOCK
D3 3 1 DLEAK
IC 1 2         1.46
RC 2 1           10      TC =    7.62E-04   ,  -3.77E-08
*
.MODEL DBLOCK D(
+         IS = 1E-12
+         RS = 0
+          N = 0.6
+         TT = 0
+        CJO = 0
+         VJ = 1
+          M = .5
+         EG = .1
+        XTI = -3.86
+         KF = 0
+         AF = 1
+         FC = .5
+         BV = 9.9999E+13
+        IBV = .001
+ )
*
.MODEL DLEAK D(
+         IS = 5.000E-15
+         RS = 0
+          N = 43
+         TT = 0
+        CJO = 0
+         VJ = 1
+          M = .5
+         EG = 10.1202914
+        XTI = 654
+         KF = 0
+         AF = 1
+         FC = .5
+         BV = 9.9999E+13
+        IBV = .001
+ )
*
.MODEL DFOR     D       (
+         IS = 1.649357E-15
+         RS = 0.405147
+          N = 1.027365
+         TT = 2.54E-7
+        CJO = 1.478778E-10
+         VJ = 0.4204929
+          M = 0.3186104
+         EG = 1.11
+        XTI = 3
+         KF = 0
+         AF = 1
+         FC = 0.5
+         BV = 9.9999E+13
+        IBV = .001
+ )
.ENDS
*

.model D1N5248	D(Is=7.021f Rs=5.619 Ikf=0 N=1 Xti=3 Eg=1.11 Cjo=60p M=.4093
+		Vj=.75 Fc=.5 Isr=1.461n Nr=2 Bv=18 Ibv=23.333m Nbv=1.2074
+		Ibvl=215.7u Nbvl=.71348 Tbv1=888.89u)
*		Motorola 	pid=1N5248	case=DO-35
*		89-9-18 gjg
*		Vz = 18 @ 7mA, Zz = 37 @ 1mA, Zz = 11 @ 5mA, Zz = 7.9 @ 20mA


* GENERIC FUNCTIONAL EQUIVALENT = 1N5248
* TYPE: DIODE
* SUBTYPE: VOLTAGE REG GP
* THIS IS A TEMPERATURE TRACKING MODEL WHICH WAS CONSTRUCTED
* FROM PRODUCT SPECIFICATION LIMITS AND PREVIOUSLY EXTRACTED MODELS.
* THE  MODEL IS INTENDED FOR USE FROM -55 C TO 125 C.  NO RADIATION EFFECTS
* ARE INCLUDED.  SIMULATIONS USING THIS MODEL REPRESENT THE RESPONSES OF
* NOMINAL DEVICES AND SIMULATIONS ARE ACCURATE WITHIN THE LIMITS OF THE
* PRODUCT SPECIFICATION.
*
*** CAUTION: THE SIMULATED TRR RANGES FROM 28 TO 38% OF THE MEASURED TRR.
*            THIS COULD POTENTIALLY LEAD TO ERRORS IN CIRCUIT. SIMULATIONS IF
*            USED IN HIGH SPEED SWITCHING APPLICATIONS.
.SUBCKT D1N5248/TEMP      1 3
D1 1 3 DFOR
D2 3 2 DBLOCK
D3 3 1 DLEAK
IC 1 2         1.76
RC 2 1           10      TC =    7.93E-04   ,  -3.14E-08
*
.MODEL DBLOCK D(
+         IS = 1E-12
+         RS = 0
+          N = 0.716
+         TT = 0
+        CJO = 0
+         VJ = 1
+          M = .5
+         EG = .1
+        XTI = -3.86
+         KF = 0
+         AF = 1
+         FC = .5
+         BV = 9.9999E+13
+        IBV = .001
+ )
*
.MODEL DLEAK D(
+         IS = 1.000E-12
+         RS = 0
+          N = 103
+         TT = 0
+        CJO = 0
+         VJ = 1
+          M = .5
+         EG = 34.3
+        XTI = 309
+         KF = 0
+         AF = 1
+         FC = .5
+         BV = 9.9999E+13
+        IBV = .001
+ )
*
.MODEL DFOR     D       (
+         IS = 1.68868E-15
+         RS = 0.2636432
+          N = 1.0213594
+         TT = 2.9023E-7
+        CJO = 1.13597E-10
+         VJ = 0.6016557
+          M = 0.3406627
+         EG = 1.11
+        XTI = 3
+         KF = 0
+         AF = 1
+         FC = 0.5
+         BV = 1E5
+        IBV = .001
+ )
.ENDS
*

*** From file FQA36P15.lib
* PSpice Model Editor - Version 9.2

*
**************** Power Discrete MOSFET Electrical Circuit Model ******************
* Product Name: FQA36P15
* 150V P-Channel MOSFET and TO-3P
*---------------------------------------------------------------------------------
.SUBCKT FQA36P15 20 10 30
Rg 10 1  0.04
M1 2 1 3 3 DMOS  L=1u  W=1u
.MODEL DMOS PMOS (VTO={-3.6*{-0.00088*TEMP+1.022}} KP={19.5*{-0.00028*TEMP+1.007}}
+ THETA=0.0424  VMAX=1.5E5   LEVEL=3)
Cgs 1 3 2440p
Rd 20 4 0.06  TC=0.0085
Dds 4 3 DDS
.MODEL DDS D(BV={150*{0.00075*TEMP+0.98125}}  M=0.48   CJO=600p   VJ=0.61)
Dbody 20 3 DBODY
.MODEL DBODY D(IS=8.5E-12  N=1.0  RS=0.013  EG=1.19  TT=198n)
Ra 4 2  0.0152  TC=0.0085
Rs 3 5  0.0012
Ls 5 30 1n
M2 1 8 6 6 INTER
E2 8 6 4 1 2
.MODEL INTER PMOS (VTO=0 KP=10 LEVEL=1)
Cgdmax 7 4 3300p
Rcgd 7 4 1E7
Dgd  4 6 DGD
Rdgd 4 6 1E7
.MODEL DGD D(M=0.62 CJO=3300p VJ=0.52)
M3 7 9 1 1 INTER
E3 9 1 4 1 -2
.ENDS
*--------------------------------------------------------------------------------
* Creation : Nov.-24-2003
* Fairchild Semiconductor
*



*** From file FQA46N15.lib
*FQA46N15 150V N-CHANNEL DMOSFET ELECTRICAL PARAMETERS
*------------------------------------------------------------------------------------
.SUBCKT  FQA46N15   20  10  30
Rg     10    1    1
M1      2    1    3    3    DMOS    L=1u   W=1u
.MODEL DMOS NMOS (VTO={3.62*{-0.00097*TEMP+1.02425}}  KP={38.0*{-0.000095*TEMP+1.002375}}
+ THETA=0.056  VMAX=1.35E5  LEVEL=3)
Cgs     1    3    2400p
Rd      20   4    15m      TC=0.013
Dds     3    4    DDS
.MODEL     DDS    D(BV={150*{0.00896*TEMP+0.776}} M=0.5  CJO=420p  VJ=0.8)
Dbody   3    20   DBODY
.MODEL   DBODY    D(IS=9.2E-13   N=1.0   RS=7.8m   EG=1.07  TT=130n)
Ra      4    2    9.6m     TC=0.013
Rs      3    5    0.3m
Ls      5    30   0.55n
M2      1    8    6    6   INTER
E2      8    6    4    1   2
.MODEL   INTER    NMOS(VTO=0   KP=10   LEVEL=1)
Cgdmax  7    4    5385p
Rcgd    7    4    10meg
Dgd     6    4    DGD
Rdgd    4    6    10meg
.MODEL     DGD    D(M=0.85  CJO=5385p   VJ=0.5)
M3      7    9    1    1   INTER
E3      9    1    4    1   -2
.ENDS FQA46N15
*------------------------------------------------------------------------------------
*FAIRCHILD      CASE: TO-3P      PID: FQA46N15
*MAY-02-2002 CREATION

*** From file FQP3P20.lib
* FQP3P20 200V P-CHANNEL DMOSFET ELECTRICAL PARAMETERS
*------------------------------------------------------------------------------------
.SUBCKT  FQP3P20   20  10  30
Rg      10   1    1
M1      2    1    3    3    DMOS    L=1u   W=1u
.MODEL DMOS PMOS (VTO={-4.95*{-0.00095*TEMP+1.02375}} KP={1.15*{-0.00075*TEMP+1.01875}}
+ THETA=0.04  VMAX=2.7E5  LEVEL=3)
Cgs     1    3    182.5p
Rd      20   4    1350m      TC=0.01
Dds     4    3    DDS
.MODEL     DDS    D(BV={200*{0.0008*TEMP+0.98}}   M=0.5    CJO=37.5p   VJ=0.8)
Dbody   20   3    DBODY
.MODEL DBODY      D(IS=1.2E-14   N=1.07   RS=78.0m   EG=1.54   TT=100n)
Ra      4    2    412m      TC=0.01
Rs      3    5    41.2m
Ls      5   30    1.97n
M2      1    8    6    6   INTER
E2      8    6    4    1    2
.MODEL   INTER    PMOS(VTO=0  KP=10   LEVEL=1)
Cgdmax  7    4    266p
Rcgd    7    4    10meg
Dgd     4    6    DGD
Rdgd    4    6    10meg
.MODEL     DGD    D(M=0.7  CJO=266p   VJ=0.55)
M3      7    9    1    1   INTER
E3      9    1    4    1   -2
.ENDS FQP3P20
*------------------------------------------------------------------------------------
* FAIRCHILD      CASE: TO-220      PID: FQP3P20
* SEP-03-2001 CREATION


*** From file FQP3N30.lib
*
**************** Power Discrete MOSFET Electrical Circuit Model *****************
** Product Name: FQP3N10
** 100V N-Channel MOSFET and TO-220
** Model Type: BSIM3V3
**-------------------------------------------------------------------------------
.SUBCKT FQP3N30 2 1 3
*Nom Temp=25 deg C
Dbody 7 5    DbodyMOD
Dbreak 5 11  DbreakMOD
Ebreak 11 7 17 7 330
Lgate 1 9    1.125e-9
Ldrain 2 5   1.440e-9
Lsource 3 7  8.431e-10
RLgate 1 9   11.25
RLdrain 2 5  14.4
RLsource 3 7 8.43
Rgate 9 6    0.5
It 7 17      1
Rbreak 17 7  RbreakMOD 1
.MODEL RbreakMOD RES (TC1=1.08e-3 TC2=-1.02e-6)
.MODEL DbodyMOD D (IS=4.05e-13 N=1     RS=3.62e-2 TRS1=2.05e-3 TRS2=5.0e-7
+ CJO=2.45e-10     M=0.51      VJ=0.47 TT=3.02e-7 XTI=3       EG=1.12)
.MODEL DbreakMOD D (RS=100e-3 TRS1=1e-3 TRS2=1.0e-6)
Rdrain 5 16 RdrainMOD 1.65
.MODEL RdrainMOD RES (TC1=7.5e-3 TC2=1.39e-5)
M_BSIM3 16 6 7 7 Bsim3 W=0.45 L=2.0e-6 NRS=1
.MODEL  Bsim3 NMOS (LEVEL=7 VERSION=3.1 MOBMOD=3 CAPMOD=2 PARAMCHK=1 NQSMOD=0
+ TOX=1480e-10   XJ=1.4e-6    NCH=1.13e17
+ U0=700         VSAT=5.0e5   DROUT=1.0
+ DELTA=0.10     PSCBE2=0     RSH=5.09e-3
+ VTH0=4.30      VOFF=-0.1    NFACTOR=1.1
+ LINT=1.05e-7   DLC=1.05e-7  FC=0.5
+ CGSO=1.2e-15   CGSL=0       CGDO=8.0e-12
+ CGDL=3.91e-10  CJ=0         CF=0
+ CKAPPA=0.12    KT1=-1.88    KT2=0
+ UA1=-2.2e-9     NJ=10 )
.ENDS


*Typ RED GaAs LED: Vf=1.7V Vr=4V If=40mA trr=3uS
.MODEL LED1 D (IS=93.2P RS=42M N=3.73 BV=4 IBV=10U
+ CJO=2.97P VJ=.75 M=.333 TT=4.32U)

.model IRF610h VDMOS (Rg=20 Vto=4.30 Kp=0.5 Rs=35m Ksubthres=0.23 Mtriode=0.35 Rd=1 Lambda=3m Bex=-2.4 Vtotc=-6m Tksubthres1=4m Trs1=3.5m Trd1=5m Cgdmax=260p Cgdmin=10p a=0.35 Cgs=125p Cjo=120p m=0.3 VJ=0.75 IS=4n N=1.5 Eg=1.05 Rb=0.06 Trb1=2.5m Vds=200 Ron=1.5 Qg=8nC mfg=VishIH1907)

.model IRF9610 vdmos pchan VTO=-3.667 RS=0.47274 KP=0.813 RD=1.733 RG=10 mfg=International_Rectifier Vds=-200 CGDMAX=4.05E-10 CGDMIN=3.00p Cjo=3.06E-11 IS=6.17e-61 Rb=0.267 TT=1.762e-06 Cgs=1.53E-10 Ksubthres=0.1
* This one is definitely wrong, see Vto
*.model IRF9610h VDMOS (pchan Rg=6 Vto=***** Kp=0.35 Rs=68m Ksubthres=0.2 Mtriode=0.5 Rd=2 Lambda=4m Bex=-1 Vtotc=+2.5m Tksubthres1=4m Trs1=3m Trd1=9m Cgdmax=120p Cgdmin=15p a=0.26 Cgs=113p Cjo=207p m=0.4 VJ=2.5 IS=1.3f N=4.2 Eg=4.5 Rb=0.02 Trb1=1.3m Vds=-200 Ron=3 Qg=11nC mfg=VishIH1907)
* modified
.model IRF9610h VDMOS (pchan Rg=6 Vto=-3.76 Kp=0.35 Rs=68m Ksubthres=0.2 Mtriode=0.5 Rd=2 Lambda=4m Bex=-1 Vtotc=+2.5m Tksubthres1=4m Trs1=3m Trd1=9m Cgdmax=120p Cgdmin=15p a=0.26 Cgs=113p Cjo=207p m=0.4 VJ=2.5 IS=1.3f N=4.2 Eg=4.5 Rb=0.02 Trb1=1.3m Vds=-200 Ron=3 Qg=11nC mfg=VishIH1907)

.model IRF540 vdmos VTO=3.542 RS=0.03646 KP=35.149 RD=0.0291 RG=6 mfg=International_Rectifier Vds=100 CGDMAX=2.70n CGDMIN=4.00E-11 Cjo=4.76E-10 IS=1.32p Rb=0.01 TT=2.305e-07 Cgs=1.54E-09 Ksubthres=0.1

.model IRF9540 vdmos pchan VTO=-3.192 RS=0.05098 KP=13.966 RD=0.0985 RG=21.486 mfg=International_Rectifier Vds=-100 CGDMAX=2.00n CGDMIN=2.00E-11 Cjo=5.13E-10 IS=2.39e-27 Rb=0.0447 TT=1.465e-07 Cgs=1.27E-09 Ksubthres=0.1
