# KiCad Development Commands

## Windows System Commands
- `dir` - List directory contents  
- `type filename` - Display file contents
- `find "pattern" filename` - Search in files
- `findstr /s "pattern" *.cpp` - Recursive search in source files

## Build System Commands
```cmd
# Configure build with CMake
cmake -B build -S .

# Build the project  
cmake --build build

# Build specific target
cmake --build build --target target_name

# Clean build
cmake --build build --clean-first
```

## Code Quality Commands
```cmd
# Format code (if clang-format available)
clang-format -i filename.cpp

# Run static analysis (if available)
clang-tidy filename.cpp
```

## File Analysis Commands
```cmd
# Search for S-expression keywords
findstr /s "keyword_name" *.keywords

# Find generated lexer files in build directory
dir /s build\*lexer.h

# Search for class definitions
findstr /s "class.*LEXER" *.h

# Find CMake lexer generation rules
findstr /s "make_lexer" CMakeLists.txt
```

## Development Workflow
1. Edit .keywords files to add/remove S-expression tokens
2. Run CMake configure to regenerate lexer classes
3. Update parser code to handle new tokens
4. Build and test changes

## Important Directories
- `common/` - Shared keyword definitions and core libraries
- `eeschema/` - Schematic editor specific code
- `pcbnew/` - PCB editor specific code  
- `include/` - Header files including dsnlexer.h
- `cmake/` - Build system and code generation scripts