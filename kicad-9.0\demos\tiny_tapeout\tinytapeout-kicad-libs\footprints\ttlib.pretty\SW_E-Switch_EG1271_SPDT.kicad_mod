(footprint "SW_E-Switch_EG1271_SPDT" (version 20211014) (generator pcbnew)
  (layer "F.Cu")
  (tedit 5BB336EF)
  (descr "E-Switch sub miniature slide switch, EG series, DPDT, http://spec_sheets.e-switch.com/specs/P040047.pdf")
  (tags "switch DPDT")
  (attr through_hole)
  (fp_text reference "REF**" (at 2.5 -1.65) (layer "F.SilkS")
    (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 7460b0c1-0934-4568-a4ae-25582bce7a44)
  )
  (fp_text value "SW_E-Switch_EG1271_SPDT" (at 2.5 5) (layer "F.Fab")
    (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 3ee97298-0cde-4f1a-a1af-b7fbf2a9a42a)
  )
  (fp_text user "${REFERENCE}" (at 2.5 -1.65) (layer "F.Fab")
    (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 1aa56333-a5cc-4a52-9ae4-2efdb8c303a5)
  )
  (fp_line (start 0.97 -1.85) (end 1.53 -1.85) (layer "F.SilkS") (width 0.12) (tstamp 0d34a9f8-c25d-4908-b84a-1ae9f4fa94d8))
  (fp_line (start -4.35 1.85) (end -4.35 -1.85) (layer "F.SilkS") (width 0.12) (tstamp 16b478e5-481f-407e-a9bd-30cb8a9074f1))
  (fp_line (start -0.97 1.85) (end -1.53 1.85) (layer "F.SilkS") (width 0.12) (tstamp 1b5ea5b3-6b38-4e77-a509-d8595148c73c))
  (fp_line (start -3.47 1.85) (end -4.35 1.85) (layer "F.SilkS") (width 0.12) (tstamp 38327d0a-0ccb-450d-ae5d-714502913381))
  (fp_line (start -4.65 -1.15) (end -4.65 -2.15) (layer "F.SilkS") (width 0.12) (tstamp 43f2dfb2-1601-43a2-a473-5137853cac49))
  (fp_line (start -4.65 -2.15) (end -3.65 -2.15) (layer "F.SilkS") (width 0.12) (tstamp 4ff09ba1-4af5-4b72-85a0-d30402a210a8))
  (fp_line (start -4.35 -1.85) (end -3.5 -1.85) (layer "F.SilkS") (width 0.12) (tstamp 5a08101e-09f3-475f-a696-e671e7463ef8))
  (fp_line (start 4.35 -1.85) (end 4.35 1.85) (layer "F.SilkS") (width 0.12) (tstamp 7d37d57e-7232-4bb1-80bc-add16debbfad))
  (fp_line (start 4.35 1.85) (end 3.48 1.85) (layer "F.SilkS") (width 0.12) (tstamp 9f16694c-45ab-49a2-a505-7dd7fee68df1))
  (fp_line (start 1.52 1.85) (end 0.97 1.85) (layer "F.SilkS") (width 0.12) (tstamp cc3f9873-fbca-448a-abbb-a1856e42eecd))
  (fp_line (start -1.5 -1.85) (end -0.97 -1.85) (layer "F.SilkS") (width 0.12) (tstamp edaba539-de02-4c7e-afd9-86f8de22cfc6))
  (fp_line (start 3.47 -1.85) (end 4.35 -1.85) (layer "F.SilkS") (width 0.12) (tstamp f54b2524-a16d-4a51-bffe-69c870c861c9))
  (fp_line (start -4.5 -2.65) (end 4.5 -2.65) (layer "F.CrtYd") (width 0.05) (tstamp 0f0cf2cb-eecd-4087-ad78-fe7083af4e8c))
  (fp_line (start 4.5 -2.65) (end 4.5 2.65) (layer "F.CrtYd") (width 0.05) (tstamp 69c056a8-3ea3-4402-b89e-f6c2cda1f81a))
  (fp_line (start 4.5 2.65) (end -4.5 2.65) (layer "F.CrtYd") (width 0.05) (tstamp 901cd78f-d00c-408f-af78-5a4481f3fa46))
  (fp_line (start -4.5 2.65) (end -4.5 -2.65) (layer "F.CrtYd") (width 0.05) (tstamp f7676004-05b0-41fa-b850-deece60a8a69))
  (fp_line (start 2.1 1) (end -2.1 1) (layer "F.Fab") (width 0.1) (tstamp 0cf2dac7-b692-4e99-8129-5db826101615))
  (fp_line (start -1.75 -1) (end -1.75 1) (layer "F.Fab") (width 0.1) (tstamp 2e4da803-a4e6-4a76-8c81-c8e3ea69aea1))
  (fp_line (start 2.1 -1) (end 2.1 1) (layer "F.Fab") (width 0.1) (tstamp 3db9d02d-b189-4112-8acc-a5e8a81f7bc0))
  (fp_line (start -4.25 -1.75) (end 4.25 -1.75) (layer "F.Fab") (width 0.1) (tstamp 6aa8a09e-b64c-4c88-8afd-3176396f53a1))
  (fp_line (start -2.1 -1) (end -2.1 1) (layer "F.Fab") (width 0.1) (tstamp 7028c0b6-2b37-4e08-ad90-49d41f9d7107))
  (fp_line (start -1.05 -1) (end -1.05 1) (layer "F.Fab") (width 0.1) (tstamp 728ba70d-0a4f-4f90-9a28-948b6ef02592))
  (fp_line (start 4.25 -1.75) (end 4.25 1.75) (layer "F.Fab") (width 0.1) (tstamp 78719cfd-3276-4bcc-b10c-2ce704ac3f89))
  (fp_line (start 0 -1) (end 0 1) (layer "F.Fab") (width 0.1) (tstamp 79a9115b-5dec-4f5f-a2ee-abf4aa8e5b91))
  (fp_line (start 4.25 1.75) (end -4.25 1.75) (layer "F.Fab") (width 0.1) (tstamp 8e718024-03ac-46d1-b60d-4c87741dada5))
  (fp_line (start -1.4 -1) (end -1.4 1) (layer "F.Fab") (width 0.1) (tstamp 8fc00777-cef3-4096-a60e-f3994bbcb66d))
  (fp_line (start -4.25 -1.75) (end -4.25 1.75) (layer "F.Fab") (width 0.1) (tstamp 97a365ce-597e-46b0-bed7-1f355b237572))
  (fp_line (start -0.7 -1) (end -0.7 1) (layer "F.Fab") (width 0.1) (tstamp a7d97c7c-db86-469f-9a33-619a89f7fcb5))
  (fp_line (start -2.1 -1) (end 2.1 -1) (layer "F.Fab") (width 0.1) (tstamp bfec8b5d-e2f0-42fb-82a0-d1390da38f28))
  (fp_line (start -0.35 -1) (end -0.35 1) (layer "F.Fab") (width 0.1) (tstamp df45d5fe-e37d-4b6b-bf68-623c30b5456e))
  (pad "1" thru_hole rect (at -2.5 1.6) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 6116fb0a-5e15-4350-bef3-1814fb76181e))
  (pad "1" thru_hole rect (at -2.5 -1.6) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp b0e60c15-9701-4d88-b888-eec3be26e5a9))
  (pad "2" thru_hole circle (at 0 -1.6) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 7fdd192b-a412-4027-8d1c-001d5dd313eb))
  (pad "2" thru_hole circle (at 0 1.6) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp c388a1bd-45ce-4715-8c3d-52179e45d50d))
  (pad "3" thru_hole circle (at 2.5 -1.6) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 0bbd1a85-ed74-43bd-96ff-4d12dfd3f46b))
  (pad "3" thru_hole circle (at 2.5 1.6) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 374d92f1-fbf8-441d-925b-90c2ad4687ed))
  (model "${KICAD6_3DMODEL_DIR}/Button_Switch_THT.3dshapes/SW_E-Switch_EG1271_DPDT.wrl"
    (offset (xyz 0 0 0))
    (scale (xyz 1 1 1))
    (rotate (xyz 0 0 0))
  )
)
