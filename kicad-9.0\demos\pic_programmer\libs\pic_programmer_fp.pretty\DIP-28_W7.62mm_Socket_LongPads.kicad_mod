(footprint "DIP-28_W7.62mm_Socket_LongPads"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "28-lead though-hole mounted DIP package, row spacing 7.62 mm (300 mils), Socket, LongPads")
	(tags "THT DIP DIL PDIP 2.54mm 7.62mm 300mil Socket LongPads")
	(property "Reference" "REF**"
		(at 3.81 -2.33 0)
		(layer "F.SilkS")
		(uuid "21e44359-f760-4cc5-af9b-e92b56235853")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "SUPP28"
		(at 3.81 35.35 0)
		(layer "F.Fab")
		(uuid "ab31b923-e1ce-4d96-9bcd-4e342ef34ec2")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "d3687203-df2b-4646-a54e-f50a61be9541")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "cd604dfd-0366-4b98-93e4-297e5136bbf3")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -1.44 -1.39)
		(end -1.44 34.41)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "cb1272a6-86ba-4607-bd4a-2ccf9650598e")
	)
	(fp_line
		(start -1.44 34.41)
		(end 9.06 34.41)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "e71a4019-c791-4100-aec0-f474e6b146b0")
	)
	(fp_line
		(start 1.56 -1.33)
		(end 1.56 34.35)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "fb142d37-2202-4487-90df-468e8e965eec")
	)
	(fp_line
		(start 1.56 34.35)
		(end 6.06 34.35)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "14b71d1f-1b42-4d17-97f9-c00b16c871e5")
	)
	(fp_line
		(start 2.81 -1.33)
		(end 1.56 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "e8849710-116a-4a25-898a-b0e82d6b25f1")
	)
	(fp_line
		(start 6.06 -1.33)
		(end 4.81 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "df209a8e-59b8-4699-b8ae-33ec18be9864")
	)
	(fp_line
		(start 6.06 34.35)
		(end 6.06 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "ccfc65c1-5a80-4b16-b66d-9583d2caf697")
	)
	(fp_line
		(start 9.06 -1.39)
		(end -1.44 -1.39)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "0b148486-ffb3-4c37-bc09-5a3cbc420d65")
	)
	(fp_line
		(start 9.06 34.41)
		(end 9.06 -1.39)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "8c030139-0bd8-4e82-a9cb-72e96fd03c62")
	)
	(fp_arc
		(start 4.81 -1.33)
		(mid 3.81 -0.33)
		(end 2.81 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "c0e547fc-b14a-474f-ab16-7853e84ff5e3")
	)
	(fp_line
		(start -1.55 -1.6)
		(end -1.55 34.65)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "e48e392a-4cd1-4c00-9423-789cf91208d5")
	)
	(fp_line
		(start -1.55 34.65)
		(end 9.15 34.65)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "fbcfd4f5-3181-462b-ac5a-043620e620ad")
	)
	(fp_line
		(start 9.15 -1.6)
		(end -1.55 -1.6)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "59ca13c2-0001-48e6-bc3d-7bc509e85674")
	)
	(fp_line
		(start 9.15 34.65)
		(end 9.15 -1.6)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "18b6090c-0e30-41cc-8c7e-bb71de4ae99b")
	)
	(fp_line
		(start -1.27 -1.33)
		(end -1.27 34.35)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "50764812-28f4-4d8f-a662-8d0f32c7fbc3")
	)
	(fp_line
		(start -1.27 34.35)
		(end 8.89 34.35)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "85af02c5-de5d-4d3c-9f81-61a10657a2e0")
	)
	(fp_line
		(start 0.635 -0.27)
		(end 1.635 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "678b2b9e-cb2c-4bad-9461-1ed9eb33098d")
	)
	(fp_line
		(start 0.635 34.29)
		(end 0.635 -0.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "9d8b5c75-6a61-48c2-8b4b-027d2fa8313c")
	)
	(fp_line
		(start 1.635 -1.27)
		(end 6.985 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "c64891a2-bfab-40af-8d04-9f64cadcdfc7")
	)
	(fp_line
		(start 6.985 -1.27)
		(end 6.985 34.29)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "0372f79d-fd3f-4504-9149-5a9ad7ea6b1c")
	)
	(fp_line
		(start 6.985 34.29)
		(end 0.635 34.29)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "dfa2b1d3-468b-4fe0-ba65-21eb23204d1b")
	)
	(fp_line
		(start 8.89 -1.33)
		(end -1.27 -1.33)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "1a96293c-1df3-4106-98ac-bd54d61b676a")
	)
	(fp_line
		(start 8.89 34.35)
		(end 8.89 -1.33)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "0982c545-607b-4be5-abab-23a703d2d2cf")
	)
	(fp_text user "${REFERENCE}"
		(at 3.81 16.51 0)
		(layer "F.Fab")
		(uuid "03e58470-396c-4089-b09b-35805ae71a70")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "da04a126-278c-4fae-aecf-df978704f796")
	)
	(pad "2" thru_hole oval
		(at 0 2.54)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0652f255-ad11-4a64-a437-96115867ea40")
	)
	(pad "3" thru_hole oval
		(at 0 5.08)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "40dd1967-0926-48ae-b9fa-ff5d119a756a")
	)
	(pad "4" thru_hole oval
		(at 0 7.62)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "05b52a86-f73c-464c-a646-f706e2981324")
	)
	(pad "5" thru_hole oval
		(at 0 10.16)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "2cf7010f-99ab-4d13-8208-a8518d67d596")
	)
	(pad "6" thru_hole oval
		(at 0 12.7)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "cd759d2c-0141-4d54-9012-8d3a9def95a7")
	)
	(pad "7" thru_hole oval
		(at 0 15.24)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "374f3336-0ac5-4e83-8297-6914bbe2d644")
	)
	(pad "8" thru_hole oval
		(at 0 17.78)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "45664e52-8834-4d23-973a-0f32d5b9cb8d")
	)
	(pad "9" thru_hole oval
		(at 0 20.32)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "*************-4a0f-ba0b-0f258fbdbae5")
	)
	(pad "10" thru_hole oval
		(at 0 22.86)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f01de77f-8059-415a-ade0-528e2c83f359")
	)
	(pad "11" thru_hole oval
		(at 0 25.4)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "e6a72307-1b94-47fd-8468-061771ada97a")
	)
	(pad "12" thru_hole oval
		(at 0 27.94)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "86f3e5c0-5be7-49dc-a3df-45c05dc25741")
	)
	(pad "13" thru_hole oval
		(at 0 30.48)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "06cbabb0-b0ae-4fa9-b3d1-c8de129002b8")
	)
	(pad "14" thru_hole oval
		(at 0 33.02)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "1bdaa8de-15a6-4a46-9f06-4273c7ba796e")
	)
	(pad "15" thru_hole oval
		(at 7.62 33.02)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f6205444-56b3-4bab-8946-cc0960d258a7")
	)
	(pad "16" thru_hole oval
		(at 7.62 30.48)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "51d74885-ca2f-4911-96d4-31cdb0e45436")
	)
	(pad "17" thru_hole oval
		(at 7.62 27.94)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "32c42479-734e-43a8-8d8b-87ff8c1b87d2")
	)
	(pad "18" thru_hole oval
		(at 7.62 25.4)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "20efd829-033d-41b5-86c1-f7d9e7518a75")
	)
	(pad "19" thru_hole oval
		(at 7.62 22.86)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "3c97600d-26d6-41c7-9861-10246426f394")
	)
	(pad "20" thru_hole oval
		(at 7.62 20.32)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0f9c7fe1-b391-47f4-9436-3186a71e3233")
	)
	(pad "21" thru_hole oval
		(at 7.62 17.78)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c449f48b-3990-4c71-8f38-20a2f682f313")
	)
	(pad "22" thru_hole oval
		(at 7.62 15.24)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "85ecc0e3-26d4-4998-bf22-68edb559c085")
	)
	(pad "23" thru_hole oval
		(at 7.62 12.7)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "bbca8c59-38ff-44f9-875b-621f63c7c1d6")
	)
	(pad "24" thru_hole oval
		(at 7.62 10.16)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "d5392c57-a132-4308-88f7-7b816a5b0cc1")
	)
	(pad "25" thru_hole oval
		(at 7.62 7.62)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "628febe5-c682-41fb-b061-4e8211fb7690")
	)
	(pad "26" thru_hole oval
		(at 7.62 5.08)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "8944252b-32b1-4505-991c-6ffe9c13b494")
	)
	(pad "27" thru_hole oval
		(at 7.62 2.54)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "ed2465cc-731c-4426-a1ec-2eadbf9a4a4d")
	)
	(pad "28" thru_hole oval
		(at 7.62 0)
		(size 2.4 1.6)
		(drill 0.8)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "3b08845e-a044-4f97-a22c-750609c0c6ca")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/Package_DIP.3dshapes/DIP-28_W7.62mm_Socket.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
