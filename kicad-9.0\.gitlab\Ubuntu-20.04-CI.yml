##########################################################################
# Build KiCad on Ubuntu 20.04 and save the results
##########################################################################
.ubuntu20.04_build:
  stage: build
  tags:
    - kicad-ubuntu20.04
  image: registry.gitlab.com/kicad/kicad-ci/source_containers/master/ubuntu:20.04
  only:
    - branches@kicad/code/kicad
    - tags@kicad/code/kicad
  except:
    - schedules
  interruptible: false
  cache:
    key: "cache-ubuntu20.04-linux"
    paths:
      - ccache/
  before_script:
    # CCache Config
    - mkdir -p ccache
    - export CCACHE_BASEDIR=${PWD}
    - export CCACHE_DIR=${PWD}/ccache
  script:
    - mkdir -p build/linux
    - cd build/linux
    - cmake
      -G Ninja
      -DCMAKE_C_COMPILER_LAUNCHER=ccache
      -DCMAKE_CXX_COMPILER_LAUNCHER=ccache
      -DCMAKE_BUILD_TYPE=Release
      -DKICAD_STDLIB_LIGHT_DEBUG=ON
      -DKICAD_SCRIPTING_WXPYTHON=ON
      -DKICAD_USE_OCC=ON
      -DKICAD_BUILD_I18N=ON
      -DKICAD_BUILD_PNS_DEBUG_TOOL=ON
      ../../
    - ninja 2>&1 | tee compilation_log.txt
    - cd ../../
  artifacts:
    # Only save the artifacts that are needed for running the tests in the next stage
    # and the compilation log. The entire build directory is too large to save as an
    # artifact.
    expire_in: 2 hrs
    when: always
    paths:
      - build/linux/3d-viewer/
      - build/linux/pcbnew/pcbnew.py
      - build/linux/pcbnew/_pcbnew.kiface
      - build/linux/pcbnew/_pcbnew.so
      - build/linux/qa/
      - build/linux/compilation_log.txt
    exclude:
      - build/linux/**/*.o
      - build/linux/**/*.a

# Upload the compilation log in an easily downloadable form
.ubuntu20.04_report_build_warn:
  stage: report
  when: always
  only:
    - branches@kicad/code/kicad
    - tags@kicad/code/kicad
  except:
    - schedules
  needs:
    - job: ubuntu20.04_build
      artifacts: true
  script:
    - echo "Uploading compilation log"
    - cp build/linux/compilation_log.txt compilation_log.txt
  artifacts:
    expire_in: 1 year
    expose_as: 'Build log'
    name: "build_log.txt"
    paths:
      - compilation_log.txt

# Report on the metrics of the code
.ubuntu20.04_report_metrics:
  stage: report
  when: always
  only:
    - branches@kicad/code/kicad
    - tags@kicad/code/kicad
  except:
    - schedules
  needs:
    - job: ubuntu20.04_build
      artifacts: true
  script:
    - cat build/linux/compilation_log.txt | { grep "warning:" || test $? = 1; } | awk 'END{print "number_of_ubuntu_20.04_warnings "NR}' > metrics.txt
    - cat metrics.txt
  artifacts:
    reports:
      metrics: metrics.txt

##########################################################################
# Run the code unit tests.
##########################################################################
.ubuntu20.04_qa:
  extends:
    - .unit_test
  needs:
    - job: ubuntu20.04_build
      artifacts: true
  tags:
    - kicad-ubuntu20.04
  image: registry.gitlab.com/kicad/kicad-ci/source_containers/master/ubuntu:20.04
  only:
    - branches@kicad/code/kicad
    - tags@kicad/code/kicad
  except:
    - schedules
  parallel:
    matrix:
      # The name of the test without the qa_ prefix
      - TEST: [python, common, gerbview, pcbnew, eeschema, kimath, sexpr, kicad2step, spice]
