(kicad_symbol_lib (version 20201005) (generator kicad_symbol_editor)
  (symbol "v_i_sources-rescue:IAM-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "I" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "IAM-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "am(1 0 100k 1k 1n)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "IAM-Simulation_SPICE_0_0"
      (arc (start 0 0.508) (end -0.508 0.508) (radius (at -0.254 0.508) (length 0.254) (angles 0.1 180))
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "IAM-Simulation_SPICE_0_1"
      (arc (start -1.016 0) (end -1.524 0) (radius (at -1.27 0) (length 0.254) (angles 0.1 180))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start -1.016 0) (end -0.508 0) (radius (at -0.762 0) (length 0.254) (angles 180 -0.1))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 0 -0.508) (end 0.508 -0.508) (radius (at 0.254 -0.508) (length 0.254) (angles 180 -0.1))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 1.016 0) (end 0.508 0) (radius (at 0.762 0) (length 0.254) (angles 0.1 180))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 1.016 0) (end 1.524 0) (radius (at 1.27 0) (length 0.254) (angles 180 -0.1))
        (stroke (width 0)) (fill (type none))
      )
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
      (polyline
        (pts
          (xy -0.508 0.508)
          (xy -0.508 0)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 0 0.508)
          (xy 0 -0.508)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 0 1.27)
          (xy 0 2.286)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 0.508 -0.508)
          (xy 0.508 0)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -0.254 1.778)
          (xy 0 1.27)
          (xy 0.254 1.778)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "IAM-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:IDC-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "I" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "IDC-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "dc(1)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "IDC-Simulation_SPICE_0_1"
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
      (polyline
        (pts
          (xy 0 1.27)
          (xy 0 2.286)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -0.254 1.778)
          (xy 0 1.27)
          (xy 0.254 1.778)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "IDC-Simulation_SPICE_0_0"
      (polyline
        (pts
          (xy -1.27 0.254)
          (xy 1.27 0.254)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -0.762 -0.254)
          (xy -1.27 -0.254)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 0.254 -0.254)
          (xy -0.254 -0.254)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 1.27 -0.254)
          (xy 0.762 -0.254)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "IDC-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:IEXP-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "I" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "IEXP-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "exp(0 1 2n 30n 60n 40n)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "IEXP-Simulation_SPICE_0_0"
      (arc (start 0 0.762) (end -1.27 -0.762) (radius (at 0.6096 -1.0414) (length 1.905) (angles 108.7 171.5))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 0 0.762) (end 1.27 -0.762) (radius (at 1.5494 0.762) (length 1.5494) (angles 180 -100.4))
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -1.27 -0.762)
          (xy -1.778 -0.762)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 1.27 -0.762)
          (xy 1.778 -0.762)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "IEXP-Simulation_SPICE_0_1"
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
      (polyline
        (pts
          (xy 0 1.27)
          (xy 0 2.286)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -0.254 1.778)
          (xy 0 1.27)
          (xy 0.254 1.778)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "IEXP-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:IPULSE-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "I" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "IPULSE-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "pulse(0 1 2n 2n 2n 50n 100n)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "IPULSE-Simulation_SPICE_0_1"
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
      (polyline
        (pts
          (xy 0 1.27)
          (xy 0 2.286)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -0.254 1.778)
          (xy 0 1.27)
          (xy 0.254 1.778)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "IPULSE-Simulation_SPICE_0_0"
      (polyline
        (pts
          (xy -2.032 -0.762)
          (xy -1.397 -0.762)
          (xy -1.143 0.762)
          (xy -0.127 0.762)
          (xy 0.127 -0.762)
          (xy 1.143 -0.762)
          (xy 1.397 0.762)
          (xy 2.032 0.762)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "IPULSE-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:IPWL-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "I" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "IPWL-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "pwl(0 -1 50n -1 51n 0 97n 1 171n -1 200n -1)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "IPWL-Simulation_SPICE_0_1"
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
      (polyline
        (pts
          (xy 0 1.27)
          (xy 0 2.286)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -0.254 1.778)
          (xy 0 1.27)
          (xy 0.254 1.778)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "IPWL-Simulation_SPICE_0_0"
      (polyline
        (pts
          (xy -1.778 -1.016)
          (xy -0.762 1.016)
          (xy -0.254 0)
          (xy 0.762 0)
          (xy 1.27 1.27)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "IPWL-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:ISFFM-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "I" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "ISFFM-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "sffm(0 1 100k 5 1k)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "ISFFM-Simulation_SPICE_0_1"
      (arc (start -1.524 0.254) (end -2.032 0.254) (radius (at -1.778 0.254) (length 0.254) (angles 0.1 180))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start -1.524 -0.254) (end -1.016 -0.254) (radius (at -1.27 -0.254) (length 0.254) (angles 180 -0.1))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 0 0) (end -1.016 0) (radius (at -0.508 0) (length 0.508) (angles 0.1 180))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 0 0) (end 1.016 0) (radius (at 0.508 0) (length 0.508) (angles 180 -0.1))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 1.524 0.254) (end 1.016 0.254) (radius (at 1.27 0.254) (length 0.254) (angles 0.1 180))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 1.524 -0.254) (end 2.032 -0.254) (radius (at 1.778 -0.254) (length 0.254) (angles 180 -0.1))
        (stroke (width 0)) (fill (type none))
      )
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
      (polyline
        (pts
          (xy -2.032 0.254)
          (xy -2.032 0)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -1.524 -0.254)
          (xy -1.524 0.254)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -1.016 0)
          (xy -1.016 -0.254)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 0 1.27)
          (xy 0 2.286)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 1.016 0.254)
          (xy 1.016 0)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 1.524 0.254)
          (xy 1.524 -0.254)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 2.032 -0.254)
          (xy 2.032 0)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -0.254 1.778)
          (xy 0 1.27)
          (xy 0.254 1.778)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "ISFFM-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:ISIN-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "I" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "ISIN-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "sin(0 1 1k)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "ISIN-Simulation_SPICE_0_0"
      (arc (start 0 0) (end -1.27 0) (radius (at -0.635 0) (length 0.635) (angles 0.1 180))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 0 0) (end 1.27 0) (radius (at 0.635 0) (length 0.635) (angles 180 -0.1))
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "ISIN-Simulation_SPICE_0_1"
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
      (polyline
        (pts
          (xy 0 1.27)
          (xy 0 2.286)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -0.254 1.778)
          (xy 0 1.27)
          (xy 0.254 1.778)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "ISIN-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:ITRNOISE-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "I" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "ITRNOISE-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "trnoise(20n 0.5n 0 0)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "ITRNOISE-Simulation_SPICE_0_1"
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
      (polyline
        (pts
          (xy 0 1.27)
          (xy 0 2.286)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -0.254 1.778)
          (xy 0 1.27)
          (xy 0.254 1.778)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -2.032 -0.762)
          (xy -1.651 1.143)
          (xy -1.651 -0.254)
          (xy -1.524 0.508)
          (xy -1.397 -1.143)
          (xy -0.889 1.016)
          (xy -0.762 -0.762)
          (xy -0.508 0.254)
          (xy -0.381 -0.508)
          (xy -0.254 0.381)
          (xy -0.127 -0.889)
          (xy 0.381 1.397)
          (xy 0.508 -1.397)
          (xy 0.635 0.762)
          (xy 1.016 -0.381)
          (xy 1.27 1.397)
          (xy 1.524 -0.508)
          (xy 1.778 0.381)
          (xy 2.032 -0.889)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "ITRNOISE-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:ITRRANDOM-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "I" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "ITRRANDOM-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "trrandom(2 10m 0 1)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "ITRRANDOM-Simulation_SPICE_0_1"
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
      (polyline
        (pts
          (xy 0 1.27)
          (xy 0 2.286)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -0.254 1.778)
          (xy 0 1.27)
          (xy 0.254 1.778)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -2.032 -0.762)
          (xy -1.651 1.143)
          (xy -1.651 -0.254)
          (xy -1.524 0.508)
          (xy -1.397 -1.143)
          (xy -0.889 1.016)
          (xy -0.762 -0.762)
          (xy -0.508 0.254)
          (xy -0.381 -0.508)
          (xy -0.254 0.381)
          (xy -0.127 -0.889)
          (xy 0.381 1.397)
          (xy 0.508 -1.397)
          (xy 0.635 0.762)
          (xy 1.016 -0.381)
          (xy 1.27 1.397)
          (xy 1.524 -0.508)
          (xy 1.778 0.381)
          (xy 2.032 -0.889)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "ITRRANDOM-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:R-Device" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
    (property "Reference" "R" (id 0) (at 2.032 0 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "R-Device" (id 1) (at 0 0 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at -1.778 0 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "ki_fp_filters" "R_*" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "R-Device_0_1"
      (rectangle (start -1.016 -2.54) (end 1.016 2.54)
        (stroke (width 0.254)) (fill (type none))
      )
    )
    (symbol "R-Device_1_1"
      (pin passive line (at 0 3.81 270) (length 1.27)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -3.81 90) (length 1.27)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:VAM-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "V" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VAM-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "am(1 0 100k 1k 1n)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "VAM-Simulation_SPICE_0_0"
      (arc (start 0 0.508) (end -0.508 0.508) (radius (at -0.254 0.508) (length 0.254) (angles 0.1 180))
        (stroke (width 0)) (fill (type none))
      )
      (text "+" (at 0 1.905 0)
        (effects (font (size 1.27 1.27)))
      )
    )
    (symbol "VAM-Simulation_SPICE_0_1"
      (arc (start -1.016 0) (end -1.524 0) (radius (at -1.27 0) (length 0.254) (angles 0.1 180))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start -1.016 0) (end -0.508 0) (radius (at -0.762 0) (length 0.254) (angles 180 -0.1))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 0 -0.508) (end 0.508 -0.508) (radius (at 0.254 -0.508) (length 0.254) (angles 180 -0.1))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 1.016 0) (end 0.508 0) (radius (at 0.762 0) (length 0.254) (angles 0.1 180))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 1.016 0) (end 1.524 0) (radius (at 1.27 0) (length 0.254) (angles 180 -0.1))
        (stroke (width 0)) (fill (type none))
      )
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
      (polyline
        (pts
          (xy -0.508 0.508)
          (xy -0.508 0)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 0 0.508)
          (xy 0 -0.508)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 0.508 -0.508)
          (xy 0.508 0)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "VAM-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:VDC-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "V" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VDC-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "dc(1)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "VDC-Simulation_SPICE_0_1"
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
    )
    (symbol "VDC-Simulation_SPICE_0_0"
      (text "+" (at 0 1.905 0)
        (effects (font (size 1.27 1.27)))
      )
      (polyline
        (pts
          (xy -1.27 0.254)
          (xy 1.27 0.254)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -0.762 -0.254)
          (xy -1.27 -0.254)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 0.254 -0.254)
          (xy -0.254 -0.254)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 1.27 -0.254)
          (xy 0.762 -0.254)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "VDC-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:VEXP-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "V" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VEXP-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "exp(0 1 2n 30n 60n 40n)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "VEXP-Simulation_SPICE_0_0"
      (arc (start 0 0.762) (end -1.27 -0.762) (radius (at 0.6096 -1.0414) (length 1.905) (angles 108.7 171.5))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 0 0.762) (end 1.27 -0.762) (radius (at 1.5494 0.762) (length 1.5494) (angles 180 -100.4))
        (stroke (width 0)) (fill (type none))
      )
      (text "+" (at 0 1.905 0)
        (effects (font (size 1.27 1.27)))
      )
      (polyline
        (pts
          (xy -1.27 -0.762)
          (xy -1.778 -0.762)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 1.27 -0.762)
          (xy 1.778 -0.762)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "VEXP-Simulation_SPICE_0_1"
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
    )
    (symbol "VEXP-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:VPULSE-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "V" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VPULSE-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "pulse(0 1 2n 2n 2n 50n 100n)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "VPULSE-Simulation_SPICE_0_1"
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
    )
    (symbol "VPULSE-Simulation_SPICE_0_0"
      (text "+" (at 0 1.905 0)
        (effects (font (size 1.27 1.27)))
      )
      (polyline
        (pts
          (xy -2.032 -0.762)
          (xy -1.397 -0.762)
          (xy -1.143 0.762)
          (xy -0.127 0.762)
          (xy 0.127 -0.762)
          (xy 1.143 -0.762)
          (xy 1.397 0.762)
          (xy 2.032 0.762)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "VPULSE-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:VPWL-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "V" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VPWL-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "pwl(0 -1 50n -1 51n 0 97n 1 171n -1 200n -1)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "VPWL-Simulation_SPICE_0_1"
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
    )
    (symbol "VPWL-Simulation_SPICE_0_0"
      (text "+" (at 0 1.905 0)
        (effects (font (size 1.27 1.27)))
      )
      (polyline
        (pts
          (xy -1.778 -1.016)
          (xy -0.762 1.016)
          (xy -0.254 0)
          (xy 0.762 0)
          (xy 1.27 1.27)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "VPWL-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:VSFFM-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "V" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VSFFM-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "sffm(0 1 100k 5 1k)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "VSFFM-Simulation_SPICE_0_1"
      (arc (start -1.524 0.254) (end -2.032 0.254) (radius (at -1.778 0.254) (length 0.254) (angles 0.1 180))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start -1.524 -0.254) (end -1.016 -0.254) (radius (at -1.27 -0.254) (length 0.254) (angles 180 -0.1))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 0 0) (end -1.016 0) (radius (at -0.508 0) (length 0.508) (angles 0.1 180))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 0 0) (end 1.016 0) (radius (at 0.508 0) (length 0.508) (angles 180 -0.1))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 1.524 0.254) (end 1.016 0.254) (radius (at 1.27 0.254) (length 0.254) (angles 0.1 180))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 1.524 -0.254) (end 2.032 -0.254) (radius (at 1.778 -0.254) (length 0.254) (angles 180 -0.1))
        (stroke (width 0)) (fill (type none))
      )
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
      (polyline
        (pts
          (xy -2.032 0.254)
          (xy -2.032 0)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -1.524 -0.254)
          (xy -1.524 0.254)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy -1.016 0)
          (xy -1.016 -0.254)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 1.016 0.254)
          (xy 1.016 0)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 1.524 0.254)
          (xy 1.524 -0.254)
        )
        (stroke (width 0)) (fill (type none))
      )
      (polyline
        (pts
          (xy 2.032 -0.254)
          (xy 2.032 0)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "VSFFM-Simulation_SPICE_0_0"
      (text "+" (at 0 1.905 0)
        (effects (font (size 1.27 1.27)))
      )
    )
    (symbol "VSFFM-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:VSIN-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "V" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VSIN-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "sin(0 1 1k)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "VSIN-Simulation_SPICE_0_0"
      (arc (start 0 0) (end -1.27 0) (radius (at -0.635 0) (length 0.635) (angles 0.1 180))
        (stroke (width 0)) (fill (type none))
      )
      (arc (start 0 0) (end 1.27 0) (radius (at 0.635 0) (length 0.635) (angles 180 -0.1))
        (stroke (width 0)) (fill (type none))
      )
      (text "+" (at 0 1.905 0)
        (effects (font (size 1.27 1.27)))
      )
    )
    (symbol "VSIN-Simulation_SPICE_0_1"
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
    )
    (symbol "VSIN-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:VTRNOISE-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "V" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VTRNOISE-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "trnoise(20n 0.5n 0 0)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "VTRNOISE-Simulation_SPICE_0_1"
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
      (polyline
        (pts
          (xy -2.032 -0.762)
          (xy -1.651 1.143)
          (xy -1.651 -0.254)
          (xy -1.524 0.508)
          (xy -1.397 -1.143)
          (xy -0.889 1.016)
          (xy -0.762 -0.762)
          (xy -0.508 0.254)
          (xy -0.381 -0.508)
          (xy -0.254 0.381)
          (xy -0.127 -0.889)
          (xy 0.381 1.397)
          (xy 0.508 -1.397)
          (xy 0.635 0.762)
          (xy 1.016 -0.381)
          (xy 1.27 1.397)
          (xy 1.524 -0.508)
          (xy 1.778 0.381)
          (xy 2.032 -0.889)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "VTRNOISE-Simulation_SPICE_0_0"
      (text "+" (at 0 1.905 0)
        (effects (font (size 1.27 1.27)))
      )
    )
    (symbol "VTRNOISE-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "v_i_sources-rescue:VTRRANDOM-Simulation_SPICE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
    (property "Reference" "V" (id 0) (at 2.54 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VTRRANDOM-Simulation_SPICE" (id 1) (at 2.54 0 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "trrandom(2 10m 0 1)" (id 6) (at 2.54 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (symbol "VTRRANDOM-Simulation_SPICE_0_1"
      (circle (center 0 0) (radius 2.54) (stroke (width 0.254)) (fill (type background)))
      (polyline
        (pts
          (xy -2.032 -0.762)
          (xy -1.651 1.143)
          (xy -1.651 -0.254)
          (xy -1.524 0.508)
          (xy -1.397 -1.143)
          (xy -0.889 1.016)
          (xy -0.762 -0.762)
          (xy -0.508 0.254)
          (xy -0.381 -0.508)
          (xy -0.254 0.381)
          (xy -0.127 -0.889)
          (xy 0.381 1.397)
          (xy 0.508 -1.397)
          (xy 0.635 0.762)
          (xy 1.016 -0.381)
          (xy 1.27 1.397)
          (xy 1.524 -0.508)
          (xy 1.778 0.381)
          (xy 2.032 -0.889)
        )
        (stroke (width 0)) (fill (type none))
      )
    )
    (symbol "VTRRANDOM-Simulation_SPICE_0_0"
      (text "+" (at 0 1.905 0)
        (effects (font (size 1.27 1.27)))
      )
    )
    (symbol "VTRRANDOM-Simulation_SPICE_1_1"
      (pin passive line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
)
