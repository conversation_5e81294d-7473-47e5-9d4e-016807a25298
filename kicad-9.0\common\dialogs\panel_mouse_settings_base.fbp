<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<wxFormBuilder_Project>
  <FileVersion major="1" minor="17"/>
  <object class="Project" expanded="true">
    <property name="class_decoration">; </property>
    <property name="code_generation">C++</property>
    <property name="disconnect_events">1</property>
    <property name="disconnect_mode">source_name</property>
    <property name="disconnect_php_events">0</property>
    <property name="disconnect_python_events">0</property>
    <property name="embedded_files_path">res</property>
    <property name="encoding">UTF-8</property>
    <property name="event_generation">connect</property>
    <property name="file">panel_mouse_settings_base</property>
    <property name="first_id">1000</property>
    <property name="help_provider">none</property>
    <property name="image_path_wrapper_function_name"></property>
    <property name="indent_with_spaces"></property>
    <property name="internationalize">1</property>
    <property name="name">PANEL_MOUSE_SETTINGS_BASE</property>
    <property name="namespace"></property>
    <property name="path">.</property>
    <property name="precompiled_header"></property>
    <property name="relative_path">1</property>
    <property name="skip_lua_events">1</property>
    <property name="skip_php_events">1</property>
    <property name="skip_python_events">1</property>
    <property name="ui_table">UI</property>
    <property name="use_array_enum">0</property>
    <property name="use_enum">1</property>
    <property name="use_microsoft_bom">0</property>
    <object class="Panel" expanded="true">
      <property name="aui_managed">0</property>
      <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
      <property name="bg"></property>
      <property name="context_help"></property>
      <property name="context_menu">1</property>
      <property name="drag_accept_files">0</property>
      <property name="enabled">1</property>
      <property name="event_handler">impl_virtual</property>
      <property name="fg"></property>
      <property name="font"></property>
      <property name="hidden">0</property>
      <property name="id">wxID_ANY</property>
      <property name="maximum_size"></property>
      <property name="minimum_size"></property>
      <property name="name">PANEL_MOUSE_SETTINGS_BASE</property>
      <property name="pos"></property>
      <property name="size">-1,-1</property>
      <property name="subclass">RESETTABLE_PANEL; widgets/resettable_panel.h; Not forward_declare</property>
      <property name="tooltip"></property>
      <property name="two_step_creation">0</property>
      <property name="window_extra_style"></property>
      <property name="window_name"></property>
      <property name="window_style">wxTAB_TRAVERSAL</property>
      <object class="wxBoxSizer" expanded="true">
        <property name="minimum_size"></property>
        <property name="name">bSizer10</property>
        <property name="orient">wxVERTICAL</property>
        <property name="permission">none</property>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag"></property>
          <property name="proportion">1</property>
          <object class="wxBoxSizer" expanded="true">
            <property name="minimum_size"></property>
            <property name="name">bSizer1</property>
            <property name="orient">wxVERTICAL</property>
            <property name="permission">none</property>
            <object class="sizeritem" expanded="true">
              <property name="border">13</property>
              <property name="flag">wxTOP|wxRIGHT|wxLEFT</property>
              <property name="proportion">0</property>
              <object class="wxStaticText" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Pan and Zoom</property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_panZoomLabel</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <property name="wrap">-1</property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxBOTTOM</property>
              <property name="proportion">0</property>
              <object class="wxStaticLine" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_staticline1</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style">wxLI_HORIZONTAL</property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">10</property>
              <property name="flag">wxEXPAND|wxRIGHT</property>
              <property name="proportion">0</property>
              <object class="wxBoxSizer" expanded="true">
                <property name="minimum_size"></property>
                <property name="name">bSizer8</property>
                <property name="orient">wxVERTICAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND|wxTOP|wxRIGHT|wxLEFT</property>
                  <property name="proportion">1</property>
                  <object class="wxGridBagSizer" expanded="true">
                    <property name="empty_cell_size"></property>
                    <property name="flexible_direction">wxBOTH</property>
                    <property name="growablecols">0,1,2</property>
                    <property name="growablerows"></property>
                    <property name="hgap">0</property>
                    <property name="minimum_size"></property>
                    <property name="name">gbSizer1</property>
                    <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                    <property name="permission">none</property>
                    <property name="vgap">0</property>
                    <object class="gbsizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="colspan">1</property>
                      <property name="column">0</property>
                      <property name="flag">wxALL</property>
                      <property name="row">0</property>
                      <property name="rowspan">1</property>
                      <object class="wxCheckBox" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="checked">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Center and warp cursor on zoom</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_checkZoomCenter</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip">Center the cursor on screen when zooming.</property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="gbsizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="colspan">1</property>
                      <property name="column">1</property>
                      <property name="flag">wxEXPAND</property>
                      <property name="row">0</property>
                      <property name="rowspan">1</property>
                      <object class="spacer" expanded="false">
                        <property name="height">0</property>
                        <property name="permission">protected</property>
                        <property name="width">30</property>
                      </object>
                    </object>
                    <object class="gbsizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="colspan">1</property>
                      <property name="column">2</property>
                      <property name="flag">wxALL</property>
                      <property name="row">0</property>
                      <property name="rowspan">1</property>
                      <object class="wxCheckBox" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="checked">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Automatically pan while moving object</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_checkAutoPan</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip">When drawing a track or moving an item, pan when approaching the edge of the display.</property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="gbsizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="colspan">3</property>
                      <property name="column">0</property>
                      <property name="flag">wxBOTTOM|wxRIGHT|wxLEFT</property>
                      <property name="row">1</property>
                      <property name="rowspan">1</property>
                      <object class="wxCheckBox" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="checked">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Use zoom acceleration</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_checkZoomAcceleration</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip">Zoom faster when scrolling quickly</property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="gbsizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="colspan">1</property>
                      <property name="column">0</property>
                      <property name="flag">wxBOTTOM|wxEXPAND|wxTOP</property>
                      <property name="row">2</property>
                      <property name="rowspan">1</property>
                      <object class="wxBoxSizer" expanded="true">
                        <property name="minimum_size"></property>
                        <property name="name">m_zoomSizer</property>
                        <property name="orient">wxHORIZONTAL</property>
                        <property name="permission">protected</property>
                        <object class="sizeritem" expanded="false">
                          <property name="border">8</property>
                          <property name="flag">wxRIGHT|wxLEFT|wxALIGN_CENTER_VERTICAL</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticText" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Zoom speed:</property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_staticText1</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">0</property>
                          <property name="flag">wxEXPAND|wxLEFT|wxRIGHT|wxTOP</property>
                          <property name="proportion">0</property>
                          <object class="wxSlider" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">0</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="maxValue">10</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="minValue">1</property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size">120,-1</property>
                            <property name="moveable">1</property>
                            <property name="name">m_zoomSpeed</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style">wxSL_HORIZONTAL</property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip">How far to zoom in for each rotation of the mouse wheel</property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">5</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">10</property>
                          <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxRIGHT</property>
                          <property name="proportion">0</property>
                          <object class="wxCheckBox" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="checked">1</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Automatic</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_checkAutoZoomSpeed</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip">Pick the zoom speed automatically</property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                          </object>
                        </object>
                      </object>
                    </object>
                    <object class="gbsizeritem" expanded="true">
                      <property name="border">5</property>
                      <property name="colspan">1</property>
                      <property name="column">2</property>
                      <property name="flag">wxBOTTOM|wxEXPAND|wxTOP</property>
                      <property name="row">2</property>
                      <property name="rowspan">1</property>
                      <object class="wxBoxSizer" expanded="true">
                        <property name="minimum_size"></property>
                        <property name="name">m_panSizer</property>
                        <property name="orient">wxHORIZONTAL</property>
                        <property name="permission">protected</property>
                        <object class="sizeritem" expanded="false">
                          <property name="border">8</property>
                          <property name="flag">wxRIGHT|wxLEFT|wxALIGN_CENTER_VERTICAL</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticText" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Auto pan speed:</property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_staticText22</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">0</property>
                          <property name="flag">wxEXPAND|wxLEFT|wxRIGHT|wxTOP</property>
                          <property name="proportion">0</property>
                          <object class="wxSlider" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="maxValue">10</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="minValue">1</property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size">120,-1</property>
                            <property name="moveable">1</property>
                            <property name="name">m_autoPanSpeed</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style">wxSL_HORIZONTAL</property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip">How fast to pan when moving an object off the edge of the screen</property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">5</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                          </object>
                        </object>
                      </object>
                    </object>
                  </object>
                </object>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">0</property>
              <object class="spacer" expanded="true">
                <property name="height">15</property>
                <property name="permission">protected</property>
                <property name="width">0</property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">13</property>
              <property name="flag">wxEXPAND|wxTOP|wxRIGHT|wxLEFT</property>
              <property name="proportion">0</property>
              <object class="wxStaticText" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Drag Gestures</property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_dragLabel</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <property name="wrap">-1</property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxBOTTOM</property>
              <property name="proportion">0</property>
              <object class="wxStaticLine" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_staticline3</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style">wxLI_HORIZONTAL</property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxLEFT</property>
              <property name="proportion">0</property>
              <object class="wxBoxSizer" expanded="true">
                <property name="minimum_size"></property>
                <property name="name">bSizer9</property>
                <property name="orient">wxVERTICAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND</property>
                  <property name="proportion">0</property>
                  <object class="spacer" expanded="true">
                    <property name="height">5</property>
                    <property name="permission">protected</property>
                    <property name="width">0</property>
                  </object>
                </object>
                <object class="sizeritem" expanded="false">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND|wxALL</property>
                  <property name="proportion">1</property>
                  <object class="wxFlexGridSizer" expanded="false">
                    <property name="cols">3</property>
                    <property name="flexible_direction">wxHORIZONTAL</property>
                    <property name="growablecols">2</property>
                    <property name="growablerows"></property>
                    <property name="hgap">5</property>
                    <property name="minimum_size"></property>
                    <property name="name">fgSizer1</property>
                    <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                    <property name="permission">none</property>
                    <property name="rows">0</property>
                    <property name="vgap">5</property>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxBOTTOM|wxRIGHT|wxALIGN_CENTER_VERTICAL</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Left button drag:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_leftButtonDragLabel</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM|wxEXPAND|wxLEFT|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxChoice" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="choices">&quot;Draw selection rectangle&quot; &quot;Drag selected objects; otherwise draw selection rectangle&quot; &quot;Drag any object (selected or not)&quot;</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_choiceLeftButtonDrag</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="selection">0</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxEXPAND</property>
                      <property name="proportion">1</property>
                      <object class="spacer" expanded="false">
                        <property name="height">0</property>
                        <property name="permission">protected</property>
                        <property name="width">0</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Middle button drag:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticText3</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM|wxEXPAND|wxLEFT|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxChoice" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="choices">&quot;Pan&quot; &quot;Zoom&quot; &quot;None&quot;</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_choiceMiddleButtonDrag</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="selection">0</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxEXPAND</property>
                      <property name="proportion">1</property>
                      <object class="spacer" expanded="false">
                        <property name="height">0</property>
                        <property name="permission">protected</property>
                        <property name="width">0</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxStaticText" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Right button drag:</property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_staticText31</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <property name="wrap">-1</property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM|wxEXPAND|wxLEFT|wxRIGHT</property>
                      <property name="proportion">0</property>
                      <object class="wxChoice" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="choices">&quot;Pan&quot; &quot;Zoom&quot; &quot;None&quot;</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_choiceRightButtonDrag</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="selection">0</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxEXPAND</property>
                      <property name="proportion">1</property>
                      <object class="spacer" expanded="false">
                        <property name="height">0</property>
                        <property name="permission">protected</property>
                        <property name="width">0</property>
                      </object>
                    </object>
                  </object>
                </object>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">0</property>
              <object class="spacer" expanded="true">
                <property name="height">15</property>
                <property name="permission">protected</property>
                <property name="width">0</property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">13</property>
              <property name="flag">wxEXPAND|wxTOP|wxRIGHT|wxLEFT</property>
              <property name="proportion">0</property>
              <object class="wxStaticText" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Scroll Gestures</property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_scrollLabel</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <property name="wrap">-1</property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxBOTTOM</property>
              <property name="proportion">0</property>
              <object class="wxStaticLine" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_staticline2</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style">wxLI_HORIZONTAL</property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">10</property>
              <property name="flag">wxEXPAND|wxTOP|wxRIGHT</property>
              <property name="proportion">1</property>
              <object class="wxBoxSizer" expanded="true">
                <property name="minimum_size"></property>
                <property name="name">bMargins</property>
                <property name="orient">wxHORIZONTAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND|wxLEFT</property>
                  <property name="proportion">1</property>
                  <object class="wxBoxSizer" expanded="true">
                    <property name="minimum_size"></property>
                    <property name="name">bSizerLeft</property>
                    <property name="orient">wxVERTICAL</property>
                    <property name="permission">none</property>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxEXPAND</property>
                      <property name="proportion">0</property>
                      <object class="wxBoxSizer" expanded="false">
                        <property name="minimum_size"></property>
                        <property name="name">bSizer4</property>
                        <property name="orient">wxHORIZONTAL</property>
                        <property name="permission">none</property>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxRIGHT</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticText" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Vertical touchpad or scroll wheel movement:</property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_staticText21</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxRIGHT|wxLEFT</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticBitmap" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="bitmap"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_scrollWarning</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip">Only one action can be assigned to each column</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                          </object>
                        </object>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">24</property>
                      <property name="flag">wxRIGHT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxFlexGridSizer" expanded="false">
                        <property name="cols">6</property>
                        <property name="flexible_direction">wxBOTH</property>
                        <property name="growablecols">0</property>
                        <property name="growablerows"></property>
                        <property name="hgap">0</property>
                        <property name="minimum_size"></property>
                        <property name="name">fgSizer2</property>
                        <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                        <property name="permission">none</property>
                        <property name="rows">0</property>
                        <property name="vgap">8</property>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_RIGHT|wxTOP|wxRIGHT|wxLEFT</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticText" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_staticText19</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxTOP|wxALIGN_CENTER_VERTICAL|wxALIGN_CENTER_HORIZONTAL</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticText" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">--</property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size">-1,-1</property>
                            <property name="moveable">1</property>
                            <property name="name">m_staticText17</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxTOP|wxRIGHT|wxALIGN_CENTER_VERTICAL</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticText" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Ctrl</property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_lblCtrl</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxTOP|wxRIGHT|wxALIGN_CENTER_VERTICAL</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticText" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Shift</property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_staticText8</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxTOP|wxRIGHT|wxALIGN_CENTER_VERTICAL</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticText" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Alt</property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_lblAlt</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL|wxLEFT|wxRIGHT</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticText" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_staticText18</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticText" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Zoom:</property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_staticText10</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_VERTICAL|wxALIGN_CENTER_HORIZONTAL</property>
                          <property name="proportion">0</property>
                          <object class="wxRadioButton" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size">-1,-1</property>
                            <property name="moveable">1</property>
                            <property name="name">m_rbZoomNone</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style">wxRB_GROUP</property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">0</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <event name="OnRadioButton">OnScrollRadioButton</event>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</property>
                          <property name="proportion">0</property>
                          <object class="wxRadioButton" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_rbZoomCtrl</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">0</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <event name="OnRadioButton">OnScrollRadioButton</event>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxRIGHT|wxLEFT|wxALIGN_CENTER_VERTICAL</property>
                          <property name="proportion">0</property>
                          <object class="wxRadioButton" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_rbZoomShift</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">0</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <event name="OnRadioButton">OnScrollRadioButton</event>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxRIGHT|wxLEFT|wxALIGN_CENTER_VERTICAL</property>
                          <property name="proportion">0</property>
                          <object class="wxRadioButton" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_rbZoomAlt</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">0</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <event name="OnRadioButton">OnScrollRadioButton</event>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="true">
                          <property name="border">5</property>
                          <property name="flag">wxALL</property>
                          <property name="proportion">0</property>
                          <object class="wxCheckBox" expanded="true">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="checked">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Reverse</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_checkZoomReverse</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticText" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Pan up/down:</property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_staticText11</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</property>
                          <property name="proportion">0</property>
                          <object class="wxRadioButton" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_rbPanVNone</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style">wxRB_GROUP</property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">0</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <event name="OnRadioButton">OnScrollRadioButton</event>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</property>
                          <property name="proportion">0</property>
                          <object class="wxRadioButton" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_rbPanVCtrl</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">0</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <event name="OnRadioButton">OnScrollRadioButton</event>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL|wxRIGHT|wxLEFT</property>
                          <property name="proportion">0</property>
                          <object class="wxRadioButton" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_rbPanVShift</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">0</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <event name="OnRadioButton">OnScrollRadioButton</event>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL|wxRIGHT|wxLEFT</property>
                          <property name="proportion">0</property>
                          <object class="wxRadioButton" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_rbPanVAlt</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">0</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <event name="OnRadioButton">OnScrollRadioButton</event>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="true">
                          <property name="border">5</property>
                          <property name="flag">wxALL</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticText" expanded="true">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_staticText211</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT</property>
                          <property name="proportion">0</property>
                          <object class="wxStaticText" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Pan left/right:</property>
                            <property name="markup">0</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_staticText20</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <property name="wrap">-1</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</property>
                          <property name="proportion">0</property>
                          <object class="wxRadioButton" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_rbPanHNone</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style">wxRB_GROUP</property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">0</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <event name="OnRadioButton">OnScrollRadioButton</event>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</property>
                          <property name="proportion">0</property>
                          <object class="wxRadioButton" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_rbPanHCtrl</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">0</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <event name="OnRadioButton">OnScrollRadioButton</event>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxRIGHT|wxLEFT|wxALIGN_CENTER_VERTICAL</property>
                          <property name="proportion">0</property>
                          <object class="wxRadioButton" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_rbPanHShift</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">0</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <event name="OnRadioButton">OnScrollRadioButton</event>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxRIGHT|wxLEFT|wxALIGN_CENTER_VERTICAL</property>
                          <property name="proportion">0</property>
                          <object class="wxRadioButton" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label"></property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_rbPanHAlt</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="value">0</property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                            <event name="OnRadioButton">OnScrollRadioButton</event>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL|wxLEFT|wxRIGHT</property>
                          <property name="proportion">0</property>
                          <object class="wxCheckBox" expanded="false">
                            <property name="BottomDockable">1</property>
                            <property name="LeftDockable">1</property>
                            <property name="RightDockable">1</property>
                            <property name="TopDockable">1</property>
                            <property name="aui_layer"></property>
                            <property name="aui_name"></property>
                            <property name="aui_position"></property>
                            <property name="aui_row"></property>
                            <property name="best_size"></property>
                            <property name="bg"></property>
                            <property name="caption"></property>
                            <property name="caption_visible">1</property>
                            <property name="center_pane">0</property>
                            <property name="checked">0</property>
                            <property name="close_button">1</property>
                            <property name="context_help"></property>
                            <property name="context_menu">1</property>
                            <property name="default_pane">0</property>
                            <property name="dock">Dock</property>
                            <property name="dock_fixed">0</property>
                            <property name="docking">Left</property>
                            <property name="drag_accept_files">0</property>
                            <property name="enabled">1</property>
                            <property name="fg"></property>
                            <property name="floatable">1</property>
                            <property name="font"></property>
                            <property name="gripper">0</property>
                            <property name="hidden">0</property>
                            <property name="id">wxID_ANY</property>
                            <property name="label">Reverse</property>
                            <property name="max_size"></property>
                            <property name="maximize_button">0</property>
                            <property name="maximum_size"></property>
                            <property name="min_size"></property>
                            <property name="minimize_button">0</property>
                            <property name="minimum_size"></property>
                            <property name="moveable">1</property>
                            <property name="name">m_checkPanHReverse</property>
                            <property name="pane_border">1</property>
                            <property name="pane_position"></property>
                            <property name="pane_size"></property>
                            <property name="permission">protected</property>
                            <property name="pin_button">1</property>
                            <property name="pos"></property>
                            <property name="resize">Resizable</property>
                            <property name="show">1</property>
                            <property name="size"></property>
                            <property name="style"></property>
                            <property name="subclass">; ; forward_declare</property>
                            <property name="toolbar_pane">0</property>
                            <property name="tooltip"></property>
                            <property name="validator_data_type"></property>
                            <property name="validator_style">wxFILTER_NONE</property>
                            <property name="validator_type">wxDefaultValidator</property>
                            <property name="validator_variable"></property>
                            <property name="window_extra_style"></property>
                            <property name="window_name"></property>
                            <property name="window_style"></property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxEXPAND</property>
                          <property name="proportion">1</property>
                          <object class="spacer" expanded="false">
                            <property name="height">10</property>
                            <property name="permission">protected</property>
                            <property name="width">0</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxEXPAND</property>
                          <property name="proportion">1</property>
                          <object class="spacer" expanded="false">
                            <property name="height">0</property>
                            <property name="permission">protected</property>
                            <property name="width">42</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxEXPAND</property>
                          <property name="proportion">1</property>
                          <object class="spacer" expanded="false">
                            <property name="height">0</property>
                            <property name="permission">protected</property>
                            <property name="width">42</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxEXPAND</property>
                          <property name="proportion">1</property>
                          <object class="spacer" expanded="false">
                            <property name="height">0</property>
                            <property name="permission">protected</property>
                            <property name="width">42</property>
                          </object>
                        </object>
                        <object class="sizeritem" expanded="false">
                          <property name="border">5</property>
                          <property name="flag">wxEXPAND</property>
                          <property name="proportion">1</property>
                          <object class="spacer" expanded="false">
                            <property name="height">0</property>
                            <property name="permission">protected</property>
                            <property name="width">42</property>
                          </object>
                        </object>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxBOTTOM|wxRIGHT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxCheckBox" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="checked">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="default_pane">0</property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Pan left/right with horizontal movement</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_checkEnablePanH</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip">Pan the canvas left and right when scrolling left to right on the touchpad</property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                      </object>
                    </object>
                  </object>
                </object>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND|wxLEFT</property>
                  <property name="proportion">0</property>
                  <object class="wxBoxSizer" expanded="true">
                    <property name="minimum_size"></property>
                    <property name="name">bSizerRight</property>
                    <property name="orient">wxVERTICAL</property>
                    <property name="permission">none</property>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxEXPAND|wxBOTTOM|wxRIGHT|wxLEFT</property>
                      <property name="proportion">0</property>
                      <object class="wxButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="auth_needed">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="bitmap"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="current"></property>
                        <property name="default">0</property>
                        <property name="default_pane">0</property>
                        <property name="disabled"></property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="focus"></property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Reset to Mouse Defaults</property>
                        <property name="margins"></property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_mouseDefaults</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="position"></property>
                        <property name="pressed"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnButtonClick">onMouseDefaults</event>
                      </object>
                    </object>
                    <object class="sizeritem" expanded="false">
                      <property name="border">5</property>
                      <property name="flag">wxEXPAND|wxALL</property>
                      <property name="proportion">0</property>
                      <object class="wxButton" expanded="false">
                        <property name="BottomDockable">1</property>
                        <property name="LeftDockable">1</property>
                        <property name="RightDockable">1</property>
                        <property name="TopDockable">1</property>
                        <property name="aui_layer"></property>
                        <property name="aui_name"></property>
                        <property name="aui_position"></property>
                        <property name="aui_row"></property>
                        <property name="auth_needed">0</property>
                        <property name="best_size"></property>
                        <property name="bg"></property>
                        <property name="bitmap"></property>
                        <property name="caption"></property>
                        <property name="caption_visible">1</property>
                        <property name="center_pane">0</property>
                        <property name="close_button">1</property>
                        <property name="context_help"></property>
                        <property name="context_menu">1</property>
                        <property name="current"></property>
                        <property name="default">0</property>
                        <property name="default_pane">0</property>
                        <property name="disabled"></property>
                        <property name="dock">Dock</property>
                        <property name="dock_fixed">0</property>
                        <property name="docking">Left</property>
                        <property name="drag_accept_files">0</property>
                        <property name="enabled">1</property>
                        <property name="fg"></property>
                        <property name="floatable">1</property>
                        <property name="focus"></property>
                        <property name="font"></property>
                        <property name="gripper">0</property>
                        <property name="hidden">0</property>
                        <property name="id">wxID_ANY</property>
                        <property name="label">Reset to Trackpad Defaults</property>
                        <property name="margins"></property>
                        <property name="markup">0</property>
                        <property name="max_size"></property>
                        <property name="maximize_button">0</property>
                        <property name="maximum_size"></property>
                        <property name="min_size"></property>
                        <property name="minimize_button">0</property>
                        <property name="minimum_size"></property>
                        <property name="moveable">1</property>
                        <property name="name">m_trackpadDefaults</property>
                        <property name="pane_border">1</property>
                        <property name="pane_position"></property>
                        <property name="pane_size"></property>
                        <property name="permission">protected</property>
                        <property name="pin_button">1</property>
                        <property name="pos"></property>
                        <property name="position"></property>
                        <property name="pressed"></property>
                        <property name="resize">Resizable</property>
                        <property name="show">1</property>
                        <property name="size"></property>
                        <property name="style"></property>
                        <property name="subclass">; ; forward_declare</property>
                        <property name="toolbar_pane">0</property>
                        <property name="tooltip"></property>
                        <property name="validator_data_type"></property>
                        <property name="validator_style">wxFILTER_NONE</property>
                        <property name="validator_type">wxDefaultValidator</property>
                        <property name="validator_variable"></property>
                        <property name="window_extra_style"></property>
                        <property name="window_name"></property>
                        <property name="window_style"></property>
                        <event name="OnButtonClick">onTrackpadDefaults</event>
                      </object>
                    </object>
                  </object>
                </object>
              </object>
            </object>
          </object>
        </object>
      </object>
    </object>
  </object>
</wxFormBuilder_Project>
