(footprint "Altech_AK300_1x02_P5.00mm_45-Degree"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "Altech AK300 serie terminal block (Script generated with StandardBox.py) (http://www.altechcorp.com/PDFS/PCBMETRC.PDF)")
	(tags "Altech AK300 serie connector")
	(property "Reference" "REF**"
		(at -4.191 0.254 90)
		(layer "F.SilkS")
		(uuid "9900d616-51d7-4c0b-a112-d85006e263ed")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "CONN_2"
		(at 2.54 5.66 0)
		(layer "F.Fab")
		(uuid "dedbfb78-69d0-4d1d-997b-9fc288141106")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "de189590-b4d8-4a1f-a9f1-d694fba6d4c0")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "34a833ea-bdc5-45c2-92ad-84c0ffbbfafb")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -3 -6.5)
		(end 0 -6.5)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "b5c57c68-47ff-4c71-a416-d32ead94c0e1")
	)
	(fp_line
		(start -3 -3.5)
		(end -3 -6.5)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "076099bf-83fa-4d8e-9e73-b07d87e64ef6")
	)
	(fp_line
		(start -2.62 -6.12)
		(end -2.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "6bafdf96-d333-4ed9-ae19-7c9ded91686c")
	)
	(fp_line
		(start -2.62 -6.12)
		(end -2.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "a83fccbb-8842-4f77-ae45-ade9e35c0f4a")
	)
	(fp_line
		(start -2.62 -6.12)
		(end 7.62 -6.12)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "9bfa7016-600f-410d-8e5d-052e93f06eb2")
	)
	(fp_line
		(start -2.62 -6.12)
		(end 7.62 -6.12)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "da081664-29b9-4fce-a4bf-4d34384da696")
	)
	(fp_line
		(start -2.62 6.62)
		(end 7.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "1ed678c0-2263-4c93-926c-4e2a47a7a769")
	)
	(fp_line
		(start -2.62 6.62)
		(end 7.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "fff8189d-749e-4607-adb5-df88ced044f4")
	)
	(fp_line
		(start 7.62 -6.12)
		(end 7.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "9d3de8c6-a371-4d92-9a20-0fe041b8b648")
	)
	(fp_line
		(start 7.62 -6.12)
		(end 7.62 6.62)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "e38ac6e9-7a8f-4ac7-8948-b0c90ee444ab")
	)
	(fp_line
		(start -2.75 -6.25)
		(end -2.75 6.75)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "ac20f208-d763-4625-9354-1d812bda72b8")
	)
	(fp_line
		(start -2.75 -6.25)
		(end 7.75 -6.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "edf51ebf-f979-4ab6-a139-90a35ec055bd")
	)
	(fp_line
		(start -2.75 6.75)
		(end 7.75 6.75)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "cacca1b4-b46b-48d8-a58d-565c65672a22")
	)
	(fp_line
		(start 7.75 -6.25)
		(end 7.75 6.75)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "b7faa640-70ca-4d23-9cc7-fa51152de9e1")
	)
	(fp_line
		(start -2.5 -5.5)
		(end -2 -6)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "491d0cb6-bd97-4203-95b6-6843b5ef8aba")
	)
	(fp_line
		(start -2.5 6.5)
		(end -2.5 -5.5)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "c9b1550c-f150-4cf8-97c4-b572ede8c26f")
	)
	(fp_line
		(start -2 -6)
		(end 7.5 -6)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "ddc838ce-20bc-4ab8-9367-ee33a79045d6")
	)
	(fp_line
		(start 7.5 -6)
		(end 7.5 6.5)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "b668d2b1-1168-46f8-b25f-0a208b02840e")
	)
	(fp_line
		(start 7.5 6.5)
		(end -2.5 6.5)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "d1aaa0ad-1c75-4aad-9cfd-80de3cb7086a")
	)
	(fp_text user "${REFERENCE}"
		(at 2.54 3.2 0)
		(layer "F.Fab")
		(uuid "a4859599-eb37-4106-adf3-7398553775c2")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 3 3)
		(drill 1.5)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "496e189c-ccd9-48a1-9fb1-fddeef5cf61d")
	)
	(pad "2" thru_hole circle
		(at 5 0)
		(size 3 3)
		(drill 1.5)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "c86768fa-0ed7-483c-84e7-5682e61715f8")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/TerminalBlock_Altech.3dshapes/Altech_AK300_1x02_P5.00mm_45-Degree.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
