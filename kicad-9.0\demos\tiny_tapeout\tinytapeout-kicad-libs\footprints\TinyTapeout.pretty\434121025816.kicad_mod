(footprint "************"
	(version 20240108)
	(generator "pcbnew")
	(generator_version "8.0")
	(layer "F.Cu")
	(descr "<b>WS-TASV</B><BR>SMT Tact Switch 6.0x3.8 mm,2 pins")
	(property "Reference" "REF**"
		(at -0.2136 -3.4943 0)
		(layer "F.SilkS")
		(uuid "6595e974-f44b-4fb3-b7dc-ed46f2c50569")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "************"
		(at 1 3.1 0)
		(layer "F.Fab")
		(uuid "2e90bc8b-**************-9aa60fa4aae8")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Footprint" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "ca4e7db3-0465-403c-9e24-4f3255debf4c")
		(effects
			(font
				(size 1.27 1.27)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "f331b305-ca29-4097-8a8d-76b0a267c083")
		(effects
			(font
				(size 1.27 1.27)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "9d5a94d7-5b6d-4a40-8f56-3da3ec9ed771")
		(effects
			(font
				(size 1.27 1.27)
			)
		)
	)
	(attr smd)
	(fp_line
		(start -3.1 -2.05)
		(end 3.1 -2.05)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "7f7610ed-ed3a-439b-add9-995bb5320b89")
	)
	(fp_line
		(start -3.1 -1.1)
		(end -3.1 -2.05)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "c796b2a8-5c4e-465c-9e69-fd164ac72e73")
	)
	(fp_line
		(start -3.1 1.1)
		(end -3.1 2.05)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "94efc212-a05d-44a0-adc5-189cf6b7baae")
	)
	(fp_line
		(start -3.1 2.05)
		(end 3.1 2.05)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "7485d18a-34b5-4af8-89e5-99435b1783b3")
	)
	(fp_line
		(start 3.1 -2.05)
		(end 3.1 -1.1)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "2f43abb3-14f7-4da1-87dc-8a3e46a92ee9")
	)
	(fp_line
		(start 3.1 2.05)
		(end 3.1 1.1)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "8fbc7fe1-8c3d-423a-820d-21a31aa24d6f")
	)
	(fp_circle
		(center -2.6 -1.2)
		(end -2.5 -1.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(fill none)
		(layer "F.SilkS")
		(uuid "d6446b17-9515-412e-8e6a-91aad7af8282")
	)
	(fp_line
		(start -4.25 -2.25)
		(end -4.25 2.25)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "345741bc-2e78-4cac-8cab-80127e49154a")
	)
	(fp_line
		(start -4.25 2.25)
		(end 4.15 2.25)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "f20ccbab-79a0-4fac-ae3e-d188efe5c9fc")
	)
	(fp_line
		(start 4.15 -2.25)
		(end -4.25 -2.25)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "3e698762-e142-4261-99fe-41334b275a75")
	)
	(fp_line
		(start 4.15 2.25)
		(end 4.15 -2.25)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "2ad443b4-b3b7-4cf8-b5f0-41ef097c2197")
	)
	(fp_line
		(start -3 -1.95)
		(end -3 1.95)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "f61b51ab-555b-4c86-a6f1-2237be7d3b1d")
	)
	(fp_line
		(start -3 1.95)
		(end 3 1.95)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "58c0212a-7f09-4d2a-bc0b-4eeaee96f089")
	)
	(fp_line
		(start 3 -1.95)
		(end -3 -1.95)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "a4db9d88-2301-4ed7-b5e7-34c9ac64e539")
	)
	(fp_line
		(start 3 1.95)
		(end 3 -1.95)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "7d90f135-c5f4-463d-aba3-b8b48518f15b")
	)
	(fp_text user "${REFERENCE}"
		(at -2.3 0.5 0)
		(unlocked yes)
		(layer "F.Fab")
		(uuid "b15100bd-c94d-47a2-b79b-8255e0e2dffa")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
			(justify left bottom)
		)
	)
	(pad "1" smd rect
		(at -3.25 0)
		(size 1.5 1.4)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(solder_mask_margin 0.102)
		(uuid "db71d206-f146-4f61-b872-6c19c4a848c1")
	)
	(pad "2" smd rect
		(at 3.25 0)
		(size 1.5 1.4)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(solder_mask_margin 0.102)
		(uuid "fbc778fe-18cb-4cf4-8e9d-2625eea810d2")
	)
	(model "${KIPRJMOD}/tinytapeout-kicad-libs/3dmodels/************.step"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)