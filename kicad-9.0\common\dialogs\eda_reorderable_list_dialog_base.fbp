<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<wxFormBuilder_Project>
    <FileVersion major="1" minor="16" />
    <object class="Project" expanded="1">
        <property name="class_decoration"></property>
        <property name="code_generation">C++</property>
        <property name="disconnect_events">1</property>
        <property name="disconnect_mode">source_name</property>
        <property name="disconnect_php_events">0</property>
        <property name="disconnect_python_events">0</property>
        <property name="embedded_files_path">res</property>
        <property name="encoding">UTF-8</property>
        <property name="event_generation">connect</property>
        <property name="file">eda_reorderable_list_dialog_base</property>
        <property name="first_id">1000</property>
        <property name="help_provider">none</property>
        <property name="image_path_wrapper_function_name"></property>
        <property name="indent_with_spaces"></property>
        <property name="internationalize">1</property>
        <property name="name">eda_reorderable_list_dialog_base</property>
        <property name="namespace"></property>
        <property name="path">.</property>
        <property name="precompiled_header"></property>
        <property name="relative_path">1</property>
        <property name="skip_lua_events">1</property>
        <property name="skip_php_events">1</property>
        <property name="skip_python_events">1</property>
        <property name="ui_table">UI</property>
        <property name="use_array_enum">0</property>
        <property name="use_enum">0</property>
        <property name="use_microsoft_bom">0</property>
        <object class="Dialog" expanded="1">
            <property name="aui_managed">0</property>
            <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
            <property name="bg"></property>
            <property name="center">wxBOTH</property>
            <property name="context_help"></property>
            <property name="context_menu">1</property>
            <property name="enabled">1</property>
            <property name="event_handler">decl_pure_virtual</property>
            <property name="extra_style"></property>
            <property name="fg"></property>
            <property name="font"></property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="maximum_size"></property>
            <property name="minimum_size">320,220</property>
            <property name="name">EDA_REORDERABLE_LIST_DIALOG_BASE</property>
            <property name="pos"></property>
            <property name="size">580,260</property>
            <property name="style">wxDEFAULT_DIALOG_STYLE|wxRESIZE_BORDER</property>
            <property name="subclass">DIALOG_SHIM; dialog_shim.h</property>
            <property name="title"></property>
            <property name="tooltip"></property>
            <property name="two_step_creation">0</property>
            <property name="window_extra_style"></property>
            <property name="window_name"></property>
            <property name="window_style"></property>
            <object class="wxBoxSizer" expanded="1">
                <property name="minimum_size"></property>
                <property name="name">bSizerMain</property>
                <property name="orient">wxVERTICAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="1">
                    <property name="border">5</property>
                    <property name="flag">wxEXPAND</property>
                    <property name="proportion">1</property>
                    <object class="wxBoxSizer" expanded="1">
                        <property name="minimum_size"></property>
                        <property name="name">bTop</property>
                        <property name="orient">wxHORIZONTAL</property>
                        <property name="permission">none</property>
                        <object class="sizeritem" expanded="1">
                            <property name="border">10</property>
                            <property name="flag">wxEXPAND|wxTOP|wxRIGHT|wxLEFT</property>
                            <property name="proportion">1</property>
                            <object class="wxFlexGridSizer" expanded="1">
                                <property name="cols">3</property>
                                <property name="flexible_direction">wxBOTH</property>
                                <property name="growablecols">0,2</property>
                                <property name="growablerows">1</property>
                                <property name="hgap">0</property>
                                <property name="minimum_size">-1,-1</property>
                                <property name="name">fgSizer1</property>
                                <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                                <property name="permission">none</property>
                                <property name="rows">0</property>
                                <property name="vgap">3</property>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag"></property>
                                    <property name="proportion">0</property>
                                    <object class="wxStaticText" expanded="1">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="label">Available:</property>
                                        <property name="markup">0</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_availableListLabel</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style"></property>
                                        <property name="subclass">; ; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                        <property name="wrap">-1</property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND</property>
                                    <property name="proportion">1</property>
                                    <object class="spacer" expanded="1">
                                        <property name="height">0</property>
                                        <property name="permission">protected</property>
                                        <property name="width">0</property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag"></property>
                                    <property name="proportion">0</property>
                                    <object class="wxStaticText" expanded="1">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="label">Enabled:</property>
                                        <property name="markup">0</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_enabledListLabel</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style"></property>
                                        <property name="subclass"></property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                        <property name="wrap">-1</property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND</property>
                                    <property name="proportion">1</property>
                                    <object class="wxListCtrl" expanded="1">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size">140,20</property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_availableListBox</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style">wxLC_NO_HEADER|wxLC_REPORT|wxLC_SINGLE_SEL</property>
                                        <property name="subclass"></property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="validator_data_type"></property>
                                        <property name="validator_style">wxFILTER_NONE</property>
                                        <property name="validator_type">wxDefaultValidator</property>
                                        <property name="validator_variable"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style">wxBORDER_SIMPLE|wxVSCROLL</property>
                                        <event name="OnListItemSelected">onAvailableListItemSelected</event>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">10</property>
                                    <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT|wxLEFT</property>
                                    <property name="proportion">0</property>
                                    <object class="wxBoxSizer" expanded="1">
                                        <property name="minimum_size"></property>
                                        <property name="name">bMiddleButtons</property>
                                        <property name="orient">wxVERTICAL</property>
                                        <property name="permission">none</property>
                                        <object class="sizeritem" expanded="1">
                                            <property name="border">10</property>
                                            <property name="flag">wxBOTTOM</property>
                                            <property name="proportion">0</property>
                                            <object class="wxButton" expanded="1">
                                                <property name="BottomDockable">1</property>
                                                <property name="LeftDockable">1</property>
                                                <property name="RightDockable">1</property>
                                                <property name="TopDockable">1</property>
                                                <property name="aui_layer"></property>
                                                <property name="aui_name"></property>
                                                <property name="aui_position"></property>
                                                <property name="aui_row"></property>
                                                <property name="auth_needed">0</property>
                                                <property name="best_size"></property>
                                                <property name="bg"></property>
                                                <property name="bitmap"></property>
                                                <property name="caption"></property>
                                                <property name="caption_visible">1</property>
                                                <property name="center_pane">0</property>
                                                <property name="close_button">1</property>
                                                <property name="context_help"></property>
                                                <property name="context_menu">1</property>
                                                <property name="current"></property>
                                                <property name="default">0</property>
                                                <property name="default_pane">0</property>
                                                <property name="disabled"></property>
                                                <property name="dock">Dock</property>
                                                <property name="dock_fixed">0</property>
                                                <property name="docking">Left</property>
                                                <property name="enabled">1</property>
                                                <property name="fg"></property>
                                                <property name="floatable">1</property>
                                                <property name="focus"></property>
                                                <property name="font"></property>
                                                <property name="gripper">0</property>
                                                <property name="hidden">0</property>
                                                <property name="id">wxID_ANY</property>
                                                <property name="label">&gt;</property>
                                                <property name="margins">-1,-1</property>
                                                <property name="markup">0</property>
                                                <property name="max_size"></property>
                                                <property name="maximize_button">0</property>
                                                <property name="maximum_size"></property>
                                                <property name="min_size"></property>
                                                <property name="minimize_button">0</property>
                                                <property name="minimum_size"></property>
                                                <property name="moveable">1</property>
                                                <property name="name">m_btnAdd</property>
                                                <property name="pane_border">1</property>
                                                <property name="pane_position"></property>
                                                <property name="pane_size"></property>
                                                <property name="permission">protected</property>
                                                <property name="pin_button">1</property>
                                                <property name="pos"></property>
                                                <property name="position"></property>
                                                <property name="pressed"></property>
                                                <property name="resize">Resizable</property>
                                                <property name="show">1</property>
                                                <property name="size">24,24</property>
                                                <property name="style"></property>
                                                <property name="subclass">; ; forward_declare</property>
                                                <property name="toolbar_pane">0</property>
                                                <property name="tooltip"></property>
                                                <property name="validator_data_type"></property>
                                                <property name="validator_style">wxFILTER_NONE</property>
                                                <property name="validator_type">wxDefaultValidator</property>
                                                <property name="validator_variable"></property>
                                                <property name="window_extra_style"></property>
                                                <property name="window_name"></property>
                                                <property name="window_style"></property>
                                                <event name="OnButtonClick">onAddItem</event>
                                            </object>
                                        </object>
                                        <object class="sizeritem" expanded="1">
                                            <property name="border">20</property>
                                            <property name="flag">wxBOTTOM|wxALIGN_CENTER_HORIZONTAL</property>
                                            <property name="proportion">0</property>
                                            <object class="wxButton" expanded="1">
                                                <property name="BottomDockable">1</property>
                                                <property name="LeftDockable">1</property>
                                                <property name="RightDockable">1</property>
                                                <property name="TopDockable">1</property>
                                                <property name="aui_layer"></property>
                                                <property name="aui_name"></property>
                                                <property name="aui_position"></property>
                                                <property name="aui_row"></property>
                                                <property name="auth_needed">0</property>
                                                <property name="best_size"></property>
                                                <property name="bg"></property>
                                                <property name="bitmap"></property>
                                                <property name="caption"></property>
                                                <property name="caption_visible">1</property>
                                                <property name="center_pane">0</property>
                                                <property name="close_button">1</property>
                                                <property name="context_help"></property>
                                                <property name="context_menu">1</property>
                                                <property name="current"></property>
                                                <property name="default">0</property>
                                                <property name="default_pane">0</property>
                                                <property name="disabled"></property>
                                                <property name="dock">Dock</property>
                                                <property name="dock_fixed">0</property>
                                                <property name="docking">Left</property>
                                                <property name="enabled">1</property>
                                                <property name="fg"></property>
                                                <property name="floatable">1</property>
                                                <property name="focus"></property>
                                                <property name="font"></property>
                                                <property name="gripper">0</property>
                                                <property name="hidden">0</property>
                                                <property name="id">wxID_ANY</property>
                                                <property name="label">&lt;</property>
                                                <property name="margins"></property>
                                                <property name="markup">0</property>
                                                <property name="max_size"></property>
                                                <property name="maximize_button">0</property>
                                                <property name="maximum_size"></property>
                                                <property name="min_size"></property>
                                                <property name="minimize_button">0</property>
                                                <property name="minimum_size"></property>
                                                <property name="moveable">1</property>
                                                <property name="name">m_btnRemove</property>
                                                <property name="pane_border">1</property>
                                                <property name="pane_position"></property>
                                                <property name="pane_size"></property>
                                                <property name="permission">protected</property>
                                                <property name="pin_button">1</property>
                                                <property name="pos"></property>
                                                <property name="position"></property>
                                                <property name="pressed"></property>
                                                <property name="resize">Resizable</property>
                                                <property name="show">1</property>
                                                <property name="size">24,24</property>
                                                <property name="style"></property>
                                                <property name="subclass">; ; forward_declare</property>
                                                <property name="toolbar_pane">0</property>
                                                <property name="tooltip"></property>
                                                <property name="validator_data_type"></property>
                                                <property name="validator_style">wxFILTER_NONE</property>
                                                <property name="validator_type">wxDefaultValidator</property>
                                                <property name="validator_variable"></property>
                                                <property name="window_extra_style"></property>
                                                <property name="window_name"></property>
                                                <property name="window_style"></property>
                                                <event name="OnButtonClick">onRemoveItem</event>
                                            </object>
                                        </object>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND</property>
                                    <property name="proportion">3</property>
                                    <object class="wxListCtrl" expanded="1">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size">140,20</property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_enabledListBox</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style">wxLC_NO_HEADER|wxLC_REPORT|wxLC_SINGLE_SEL</property>
                                        <property name="subclass"></property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="validator_data_type"></property>
                                        <property name="validator_style">wxFILTER_NONE</property>
                                        <property name="validator_type">wxDefaultValidator</property>
                                        <property name="validator_variable"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style">wxBORDER_SIMPLE|wxVSCROLL</property>
                                        <event name="OnListItemSelected">onEnabledListItemSelected</event>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND</property>
                                    <property name="proportion">1</property>
                                    <object class="spacer" expanded="1">
                                        <property name="height">0</property>
                                        <property name="permission">protected</property>
                                        <property name="width">0</property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND</property>
                                    <property name="proportion">1</property>
                                    <object class="spacer" expanded="1">
                                        <property name="height">0</property>
                                        <property name="permission">protected</property>
                                        <property name="width">0</property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND</property>
                                    <property name="proportion">0</property>
                                    <object class="wxBoxSizer" expanded="1">
                                        <property name="minimum_size"></property>
                                        <property name="name">bSizer4</property>
                                        <property name="orient">wxHORIZONTAL</property>
                                        <property name="permission">none</property>
                                        <object class="sizeritem" expanded="1">
                                            <property name="border">5</property>
                                            <property name="flag"></property>
                                            <property name="proportion">0</property>
                                            <object class="wxBitmapButton" expanded="1">
                                                <property name="BottomDockable">1</property>
                                                <property name="LeftDockable">1</property>
                                                <property name="RightDockable">1</property>
                                                <property name="TopDockable">1</property>
                                                <property name="aui_layer"></property>
                                                <property name="aui_name"></property>
                                                <property name="aui_position"></property>
                                                <property name="aui_row"></property>
                                                <property name="auth_needed">0</property>
                                                <property name="best_size"></property>
                                                <property name="bg"></property>
                                                <property name="bitmap"></property>
                                                <property name="caption"></property>
                                                <property name="caption_visible">1</property>
                                                <property name="center_pane">0</property>
                                                <property name="close_button">1</property>
                                                <property name="context_help"></property>
                                                <property name="context_menu">1</property>
                                                <property name="current"></property>
                                                <property name="default">0</property>
                                                <property name="default_pane">0</property>
                                                <property name="disabled"></property>
                                                <property name="dock">Dock</property>
                                                <property name="dock_fixed">0</property>
                                                <property name="docking">Left</property>
                                                <property name="enabled">1</property>
                                                <property name="fg"></property>
                                                <property name="floatable">1</property>
                                                <property name="focus"></property>
                                                <property name="font"></property>
                                                <property name="gripper">0</property>
                                                <property name="hidden">0</property>
                                                <property name="id">wxID_ANY</property>
                                                <property name="label">Up</property>
                                                <property name="margins"></property>
                                                <property name="markup">0</property>
                                                <property name="max_size"></property>
                                                <property name="maximize_button">0</property>
                                                <property name="maximum_size"></property>
                                                <property name="min_size"></property>
                                                <property name="minimize_button">0</property>
                                                <property name="minimum_size"></property>
                                                <property name="moveable">1</property>
                                                <property name="name">m_btnUp</property>
                                                <property name="pane_border">1</property>
                                                <property name="pane_position"></property>
                                                <property name="pane_size"></property>
                                                <property name="permission">protected</property>
                                                <property name="pin_button">1</property>
                                                <property name="pos"></property>
                                                <property name="position"></property>
                                                <property name="pressed"></property>
                                                <property name="resize">Resizable</property>
                                                <property name="show">1</property>
                                                <property name="size"></property>
                                                <property name="style"></property>
                                                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                                                <property name="toolbar_pane">0</property>
                                                <property name="tooltip">Move up</property>
                                                <property name="validator_data_type"></property>
                                                <property name="validator_style">wxFILTER_NONE</property>
                                                <property name="validator_type">wxDefaultValidator</property>
                                                <property name="validator_variable"></property>
                                                <property name="window_extra_style"></property>
                                                <property name="window_name"></property>
                                                <property name="window_style"></property>
                                                <event name="OnButtonClick">onMoveUp</event>
                                            </object>
                                        </object>
                                        <object class="sizeritem" expanded="1">
                                            <property name="border">5</property>
                                            <property name="flag">wxLEFT</property>
                                            <property name="proportion">0</property>
                                            <object class="wxBitmapButton" expanded="1">
                                                <property name="BottomDockable">1</property>
                                                <property name="LeftDockable">1</property>
                                                <property name="RightDockable">1</property>
                                                <property name="TopDockable">1</property>
                                                <property name="aui_layer"></property>
                                                <property name="aui_name"></property>
                                                <property name="aui_position"></property>
                                                <property name="aui_row"></property>
                                                <property name="auth_needed">0</property>
                                                <property name="best_size"></property>
                                                <property name="bg"></property>
                                                <property name="bitmap"></property>
                                                <property name="caption"></property>
                                                <property name="caption_visible">1</property>
                                                <property name="center_pane">0</property>
                                                <property name="close_button">1</property>
                                                <property name="context_help"></property>
                                                <property name="context_menu">1</property>
                                                <property name="current"></property>
                                                <property name="default">0</property>
                                                <property name="default_pane">0</property>
                                                <property name="disabled"></property>
                                                <property name="dock">Dock</property>
                                                <property name="dock_fixed">0</property>
                                                <property name="docking">Left</property>
                                                <property name="enabled">1</property>
                                                <property name="fg"></property>
                                                <property name="floatable">1</property>
                                                <property name="focus"></property>
                                                <property name="font"></property>
                                                <property name="gripper">0</property>
                                                <property name="hidden">0</property>
                                                <property name="id">wxID_ANY</property>
                                                <property name="label">Down</property>
                                                <property name="margins"></property>
                                                <property name="markup">0</property>
                                                <property name="max_size"></property>
                                                <property name="maximize_button">0</property>
                                                <property name="maximum_size"></property>
                                                <property name="min_size"></property>
                                                <property name="minimize_button">0</property>
                                                <property name="minimum_size"></property>
                                                <property name="moveable">1</property>
                                                <property name="name">m_btnDown</property>
                                                <property name="pane_border">1</property>
                                                <property name="pane_position"></property>
                                                <property name="pane_size"></property>
                                                <property name="permission">protected</property>
                                                <property name="pin_button">1</property>
                                                <property name="pos"></property>
                                                <property name="position"></property>
                                                <property name="pressed"></property>
                                                <property name="resize">Resizable</property>
                                                <property name="show">1</property>
                                                <property name="size"></property>
                                                <property name="style"></property>
                                                <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                                                <property name="toolbar_pane">0</property>
                                                <property name="tooltip">Move down</property>
                                                <property name="validator_data_type"></property>
                                                <property name="validator_style">wxFILTER_NONE</property>
                                                <property name="validator_type">wxDefaultValidator</property>
                                                <property name="validator_variable"></property>
                                                <property name="window_extra_style"></property>
                                                <property name="window_name"></property>
                                                <property name="window_style"></property>
                                                <event name="OnButtonClick">onMoveDown</event>
                                            </object>
                                        </object>
                                    </object>
                                </object>
                            </object>
                        </object>
                    </object>
                </object>
                <object class="sizeritem" expanded="1">
                    <property name="border">5</property>
                    <property name="flag">wxEXPAND</property>
                    <property name="proportion">0</property>
                    <object class="wxBoxSizer" expanded="1">
                        <property name="minimum_size"></property>
                        <property name="name">m_ButtonsSizer</property>
                        <property name="orient">wxHORIZONTAL</property>
                        <property name="permission">public</property>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxALL</property>
                            <property name="proportion">1</property>
                            <object class="wxStdDialogButtonSizer" expanded="1">
                                <property name="Apply">0</property>
                                <property name="Cancel">1</property>
                                <property name="ContextHelp">0</property>
                                <property name="Help">0</property>
                                <property name="No">0</property>
                                <property name="OK">1</property>
                                <property name="Save">0</property>
                                <property name="Yes">0</property>
                                <property name="minimum_size"></property>
                                <property name="name">m_sdbSizer</property>
                                <property name="permission">protected</property>
                            </object>
                        </object>
                    </object>
                </object>
            </object>
        </object>
    </object>
</wxFormBuilder_Project>
