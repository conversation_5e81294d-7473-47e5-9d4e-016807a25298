(kicad_sch
	(version 20250114)
	(generator "eeschema")
	(generator_version "9.0")
	(uuid "6fbe5289-75f5-4763-8b1c-e93c284ae160")
	(paper "A4")
	(title_block
		(title "RoyalBlue54L")
		(date "2025-01-25")
		(rev "0.0.1")
		(company "Lord's Boards")
	)
	(lib_symbols
		(symbol "Device:C_Small"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.254)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "C"
				(at 0.254 1.778 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "C_Small"
				(at 0.254 -2.032 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Unpolarized capacitor, small symbol"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "capacitor cap"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "C_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "C_Small_0_1"
				(polyline
					(pts
						(xy -1.524 0.508) (xy 1.524 0.508)
					)
					(stroke
						(width 0.3048)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.524 -0.508) (xy 1.524 -0.508)
					)
					(stroke
						(width 0.3302)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "C_Small_1_1"
				(pin passive line
					(at 0 2.54 270)
					(length 2.032)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -2.54 90)
					(length 2.032)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Device:LED_ARGB"
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "D"
				(at 0 9.398 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "LED_ARGB"
				(at 0 -8.89 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 -1.27 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 -1.27 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "RGB LED, anode/red/green/blue"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "LED RGB diode"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "LED* LED_SMD:* LED_THT:*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "LED_ARGB_0_0"
				(text "R"
					(at -1.905 3.81 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(text "G"
					(at -1.905 -1.27 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(text "B"
					(at -1.905 -6.35 0)
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(symbol "LED_ARGB_0_1"
				(polyline
					(pts
						(xy -1.27 6.35) (xy -1.27 3.81)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 6.35) (xy -1.27 3.81) (xy -1.27 3.81)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 5.08) (xy -2.54 5.08)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 5.08) (xy 1.27 5.08)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 1.27) (xy -1.27 -1.27)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 1.27) (xy -1.27 -1.27) (xy -1.27 -1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 0) (xy -2.54 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -3.81) (xy -1.27 -6.35)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -5.08) (xy -2.54 -5.08)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -5.08) (xy 1.27 -5.08)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.016 6.35) (xy 0.508 7.874) (xy -0.254 7.874) (xy 0.508 7.874) (xy 0.508 7.112)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.016 1.27) (xy 0.508 2.794) (xy -0.254 2.794) (xy 0.508 2.794) (xy 0.508 2.032)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.016 -3.81) (xy 0.508 -2.286) (xy -0.254 -2.286) (xy 0.508 -2.286) (xy 0.508 -3.048)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 6.35) (xy 1.524 7.874) (xy 0.762 7.874) (xy 1.524 7.874) (xy 1.524 7.112)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 1.27) (xy 1.524 2.794) (xy 0.762 2.794) (xy 1.524 2.794) (xy 1.524 2.032)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 -3.81) (xy 1.524 -2.286) (xy 0.762 -2.286) (xy 1.524 -2.286) (xy 1.524 -3.048)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 6.35) (xy 1.27 3.81) (xy -1.27 5.08) (xy 1.27 6.35)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 1.27 6.35)
					(end 1.27 6.35)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 1.27 3.81)
					(end 1.27 6.35)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 1.27) (xy 1.27 -1.27) (xy -1.27 0) (xy 1.27 1.27)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 1.27 1.27)
					(end 1.27 1.27)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 0) (xy -1.27 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 0) (xy 2.54 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 1.27 -1.27)
					(end 1.27 1.27)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -3.81) (xy 1.27 -6.35) (xy -1.27 -5.08) (xy 1.27 -3.81)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -5.08) (xy 2.032 -5.08) (xy 2.032 5.08) (xy 1.27 5.08)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 2.032 0)
					(radius 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 2.794 8.382)
					(end -2.794 -7.62)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(symbol "LED_ARGB_1_1"
				(pin passive line
					(at -5.08 5.08 0)
					(length 2.54)
					(name "RK"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 0 0)
					(length 2.54)
					(name "GK"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -5.08 -5.08 0)
					(length 2.54)
					(name "BK"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 0 180)
					(length 2.54)
					(name "A"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Device:L_Small"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.254)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "L"
				(at 0.762 1.016 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "L_Small"
				(at 0.762 -1.016 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Inductor, small symbol"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "inductor choke coil reactor magnetic"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Choke_* *Coil* Inductor_* L_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "L_Small_0_1"
				(arc
					(start 0 2.032)
					(mid 0.5058 1.524)
					(end 0 1.016)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 0 1.016)
					(mid 0.5058 0.508)
					(end 0 0)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 0 0)
					(mid 0.5058 -0.508)
					(end 0 -1.016)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 0 -1.016)
					(mid 0.5058 -1.524)
					(end 0 -2.032)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "L_Small_1_1"
				(pin passive line
					(at 0 2.54 270)
					(length 0.508)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -2.54 90)
					(length 0.508)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Device:R_Small_US"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.254)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "R"
				(at 0.762 0.508 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "R_Small_US"
				(at 0.762 -1.016 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Resistor, small US symbol"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "r resistor"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "R_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "R_Small_US_1_1"
				(polyline
					(pts
						(xy 0 1.524) (xy 1.016 1.143) (xy 0 0.762) (xy -1.016 0.381) (xy 0 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 1.016 -0.381) (xy 0 -0.762) (xy -1.016 -1.143) (xy 0 -1.524)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(pin passive line
					(at 0 2.54 270)
					(length 1.016)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -2.54 90)
					(length 1.016)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Graphic:Logo_Open_Hardware_Small"
			(exclude_from_sim no)
			(in_bom no)
			(on_board no)
			(property "Reference" "#SYM"
				(at 0 6.985 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "Logo_Open_Hardware_Small"
				(at 0 -5.715 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Open Hardware logo, small"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Enable" "0"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "Logo"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Logo_Open_Hardware_Small_0_1"
				(polyline
					(pts
						(xy 3.3528 -4.3434) (xy 3.302 -4.318) (xy 3.175 -4.2418) (xy 2.9972 -4.1148) (xy 2.7686 -3.9624)
						(xy 2.54 -3.81) (xy 2.3622 -3.7084) (xy 2.2352 -3.6068) (xy 2.1844 -3.5814) (xy 2.159 -3.6068)
						(xy 2.0574 -3.6576) (xy 1.905 -3.7338) (xy 1.8034 -3.7846) (xy 1.6764 -3.8354) (xy 1.6002 -3.8354)
						(xy 1.6002 -3.8354) (xy 1.5494 -3.7338) (xy 1.4732 -3.5306) (xy 1.3462 -3.302) (xy 1.2446 -3.0226)
						(xy 1.1176 -2.7178) (xy 0.9652 -2.413) (xy 0.8636 -2.1082) (xy 0.7366 -1.8288) (xy 0.6604 -1.6256)
						(xy 0.6096 -1.4732) (xy 0.5842 -1.397) (xy 0.5842 -1.397) (xy 0.6604 -1.3208) (xy 0.7874 -1.2446)
						(xy 1.0414 -1.016) (xy 1.2954 -0.6858) (xy 1.4478 -0.3302) (xy 1.524 0.0762) (xy 1.4732 0.4572)
						(xy 1.3208 0.8128) (xy 1.0668 1.143) (xy 0.762 1.3716) (xy 0.4064 1.524) (xy 0 1.5748) (xy -0.381 1.5494)
						(xy -0.7366 1.397) (xy -1.0668 1.143) (xy -1.2192 0.9906) (xy -1.397 0.6604) (xy -1.524 0.3048)
						(xy -1.524 0.2286) (xy -1.4986 -0.1778) (xy -1.397 -0.5334) (xy -1.1938 -0.8636) (xy -0.9144 -1.143)
						(xy -0.8636 -1.1684) (xy -0.7366 -1.27) (xy -0.635 -1.3462) (xy -0.5842 -1.397) (xy -1.0668 -2.5908)
						(xy -1.143 -2.794) (xy -1.2954 -3.1242) (xy -1.397 -3.4036) (xy -1.4986 -3.6322) (xy -1.5748 -3.7846)
						(xy -1.6002 -3.8354) (xy -1.6002 -3.8354) (xy -1.651 -3.8354) (xy -1.7272 -3.81) (xy -1.905 -3.7338)
						(xy -2.0066 -3.683) (xy -2.1336 -3.6068) (xy -2.2098 -3.5814) (xy -2.2606 -3.6068) (xy -2.3622 -3.683)
						(xy -2.54 -3.81) (xy -2.7686 -3.9624) (xy -2.9718 -4.0894) (xy -3.1496 -4.2164) (xy -3.302 -4.318)
						(xy -3.3528 -4.3434) (xy -3.3782 -4.3434) (xy -3.429 -4.318) (xy -3.5306 -4.2164) (xy -3.7084 -4.064)
						(xy -3.937 -3.8354) (xy -3.9624 -3.81) (xy -4.1656 -3.6068) (xy -4.318 -3.4544) (xy -4.4196 -3.3274)
						(xy -4.445 -3.2766) (xy -4.445 -3.2766) (xy -4.4196 -3.2258) (xy -4.318 -3.0734) (xy -4.2164 -2.8956)
						(xy -4.064 -2.667) (xy -3.6576 -2.0828) (xy -3.8862 -1.5494) (xy -3.937 -1.3716) (xy -4.0386 -1.1684)
						(xy -4.0894 -1.0414) (xy -4.1148 -0.9652) (xy -4.191 -0.9398) (xy -4.318 -0.9144) (xy -4.5466 -0.8636)
						(xy -4.8006 -0.8128) (xy -5.0546 -0.7874) (xy -5.2578 -0.7366) (xy -5.4356 -0.7112) (xy -5.5118 -0.6858)
						(xy -5.5118 -0.6858) (xy -5.5372 -0.635) (xy -5.5372 -0.5588) (xy -5.5372 -0.4318) (xy -5.5626 -0.2286)
						(xy -5.5626 0.0762) (xy -5.5626 0.127) (xy -5.5372 0.4064) (xy -5.5372 0.635) (xy -5.5372 0.762)
						(xy -5.5372 0.8382) (xy -5.5372 0.8382) (xy -5.461 0.8382) (xy -5.3086 0.889) (xy -5.08 0.9144)
						(xy -4.826 0.9652) (xy -4.8006 0.9906) (xy -4.5466 1.0414) (xy -4.318 1.0668) (xy -4.1656 1.1176)
						(xy -4.0894 1.143) (xy -4.0894 1.143) (xy -4.0386 1.2446) (xy -3.9624 1.4224) (xy -3.8608 1.6256)
						(xy -3.7846 1.8288) (xy -3.7084 2.0066) (xy -3.6576 2.159) (xy -3.6322 2.2098) (xy -3.6322 2.2098)
						(xy -3.683 2.286) (xy -3.7592 2.413) (xy -3.8862 2.5908) (xy -4.064 2.8194) (xy -4.064 2.8448)
						(xy -4.2164 3.0734) (xy -4.3434 3.2512) (xy -4.4196 3.3782) (xy -4.445 3.4544) (xy -4.445 3.4544)
						(xy -4.3942 3.5052) (xy -4.2926 3.6322) (xy -4.1148 3.81) (xy -3.937 4.0132) (xy -3.8608 4.064)
						(xy -3.6576 4.2926) (xy -3.5052 4.4196) (xy -3.4036 4.4958) (xy -3.3528 4.5212) (xy -3.3528 4.5212)
						(xy -3.302 4.4704) (xy -3.1496 4.3688) (xy -2.9718 4.2418) (xy -2.7432 4.0894) (xy -2.7178 4.0894)
						(xy -2.4892 3.937) (xy -2.3114 3.81) (xy -2.1844 3.7084) (xy -2.1336 3.683) (xy -2.1082 3.683)
						(xy -2.032 3.7084) (xy -1.8542 3.7592) (xy -1.6764 3.8354) (xy -1.4732 3.937) (xy -1.27 4.0132)
						(xy -1.143 4.064) (xy -1.0668 4.1148) (xy -1.0668 4.1148) (xy -1.0414 4.191) (xy -1.016 4.3434)
						(xy -0.9652 4.572) (xy -0.9144 4.8514) (xy -0.889 4.9022) (xy -0.8382 5.1562) (xy -0.8128 5.3848)
						(xy -0.7874 5.5372) (xy -0.762 5.588) (xy -0.7112 5.6134) (xy -0.5842 5.6134) (xy -0.4064 5.6134)
						(xy -0.1524 5.6134) (xy 0.0762 5.6134) (xy 0.3302 5.6134) (xy 0.5334 5.6134) (xy 0.6858 5.588)
						(xy 0.7366 5.588) (xy 0.7366 5.588) (xy 0.762 5.5118) (xy 0.8128 5.334) (xy 0.8382 5.1054) (xy 0.9144 4.826)
						(xy 0.9144 4.7752) (xy 0.9652 4.5212) (xy 1.016 4.2926) (xy 1.0414 4.1402) (xy 1.0668 4.0894)
						(xy 1.0668 4.0894) (xy 1.1938 4.0386) (xy 1.3716 3.9624) (xy 1.5748 3.8608) (xy 2.0828 3.6576)
						(xy 2.7178 4.0894) (xy 2.7686 4.1402) (xy 2.9972 4.2926) (xy 3.175 4.4196) (xy 3.302 4.4958) (xy 3.3782 4.5212)
						(xy 3.3782 4.5212) (xy 3.429 4.4704) (xy 3.556 4.3434) (xy 3.7338 4.191) (xy 3.9116 3.9878) (xy 4.064 3.8354)
						(xy 4.2418 3.6576) (xy 4.3434 3.556) (xy 4.4196 3.4798) (xy 4.4196 3.429) (xy 4.4196 3.4036) (xy 4.3942 3.3274)
						(xy 4.2926 3.2004) (xy 4.1656 2.9972) (xy 4.0132 2.794) (xy 3.8862 2.5908) (xy 3.7592 2.3876)
						(xy 3.6576 2.2352) (xy 3.6322 2.159) (xy 3.6322 2.1336) (xy 3.683 2.0066) (xy 3.7592 1.8288) (xy 3.8608 1.6002)
						(xy 4.064 1.1176) (xy 4.3942 1.0414) (xy 4.5974 1.016) (xy 4.8768 0.9652) (xy 5.1308 0.9144) (xy 5.5372 0.8382)
						(xy 5.5626 -0.6604) (xy 5.4864 -0.6858) (xy 5.4356 -0.6858) (xy 5.2832 -0.7366) (xy 5.0546 -0.762)
						(xy 4.8006 -0.8128) (xy 4.5974 -0.8636) (xy 4.3688 -0.9144) (xy 4.2164 -0.9398) (xy 4.1402 -0.9398)
						(xy 4.1148 -0.9652) (xy 4.064 -1.0668) (xy 3.9878 -1.2446) (xy 3.9116 -1.4478) (xy 3.81 -1.651)
						(xy 3.7338 -1.8542) (xy 3.683 -2.0066) (xy 3.6576 -2.0828) (xy 3.683 -2.1336) (xy 3.7846 -2.2606)
						(xy 3.8862 -2.4638) (xy 4.0386 -2.667) (xy 4.191 -2.8956) (xy 4.318 -3.0734) (xy 4.3942 -3.2004)
						(xy 4.445 -3.2766) (xy 4.4196 -3.3274) (xy 4.3434 -3.429) (xy 4.1656 -3.5814) (xy 3.937 -3.8354)
						(xy 3.8862 -3.8608) (xy 3.683 -4.064) (xy 3.5306 -4.2164) (xy 3.4036 -4.318) (xy 3.3528 -4.3434)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Jumper:SolderJumper_2_Bridged"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim yes)
			(in_bom no)
			(on_board yes)
			(property "Reference" "JP"
				(at 0 2.032 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "SolderJumper_2_Bridged"
				(at 0 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Solder Jumper, 2-pole, closed/bridged"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "solder jumper SPST"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "SolderJumper*Bridged*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "SolderJumper_2_Bridged_0_1"
				(rectangle
					(start -0.508 0.508)
					(end 0.508 -0.508)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -0.254 1.016) (xy -0.254 -1.016)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -0.254 -1.016)
					(mid -1.2656 0)
					(end -0.254 1.016)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -0.254 -1.016)
					(mid -1.2656 0)
					(end -0.254 1.016)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(arc
					(start 0.254 1.016)
					(mid 1.2656 0)
					(end 0.254 -1.016)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start 0.254 1.016)
					(mid 1.2656 0)
					(end 0.254 -1.016)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0.254 1.016) (xy 0.254 -1.016)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "SolderJumper_2_Bridged_1_1"
				(pin passive line
					(at -3.81 0 0)
					(length 2.54)
					(name "A"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 3.81 0 180)
					(length 2.54)
					(name "B"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "LordsBoards-Graphic:LordsBoardsLogo"
			(exclude_from_sim no)
			(in_bom no)
			(on_board no)
			(property "Reference" "#SYM"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "LordsBoardsLogo_1_0"
				(polyline
					(pts
						(xy -5.5268 4.7992) (xy -5.473 4.7941) (xy -5.4197 4.7851) (xy -5.3671 4.7724) (xy -5.3154 4.7559)
						(xy -5.2647 4.7357) (xy -5.2152 4.7117) (xy -5.1671 4.6841) (xy -5.1206 4.6528) (xy -5.0759 4.6179)
						(xy -2.4774 2.4374) (xy -2.4704 2.4318) (xy -2.4632 2.4265) (xy -2.4558 2.4216) (xy -2.4483 2.4171)
						(xy -2.4406 2.413) (xy -2.4328 2.4092) (xy -2.4249 2.4058) (xy -2.4169 2.4028) (xy -2.4089 2.4001)
						(xy -2.4007 2.3978) (xy -2.3924 2.3959) (xy -2.3841 2.3943) (xy -2.3758 2.3931) (xy -2.3674 2.3923)
						(xy -2.359 2.3918) (xy -2.3506 2.3918) (xy -2.3422 2.392) (xy -2.3338 2.3927) (xy -2.3255 2.3937)
						(xy -2.3172 2.3951) (xy -2.3089 2.3968) (xy -2.3007 2.3989) (xy -2.2926 2.4014) (xy -2.2846 2.4042)
						(xy -2.2766 2.4074) (xy -2.2688 2.411) (xy -2.2611 2.4149) (xy -2.2536 2.4192) (xy -2.2462 2.4239)
						(xy -2.239 2.4289) (xy -2.2319 2.4343) (xy -2.225 2.4401) (xy -0.5157 3.9374) (xy -0.488 3.9606)
						(xy -0.4595 3.9822) (xy -0.4303 4.0024) (xy -0.4003 4.021) (xy -0.3697 4.0382) (xy -0.3385 4.0539)
						(xy -0.3068 4.0681) (xy -0.2745 4.0808) (xy -0.2418 4.092) (xy -0.2088 4.1018) (xy -0.1754 4.11)
						(xy -0.1418 4.1168) (xy -0.1079 4.122) (xy -0.0739 4.1258) (xy -0.0397 4.1281) (xy -0.0055 4.1288)
						(xy 0.0287 4.1281) (xy 0.0628 4.1259) (xy 0.0969 4.1223) (xy 0.1307 4.1171) (xy 0.1644 4.1104)
						(xy 0.1978 4.1023) (xy 0.2309 4.0926) (xy 0.2636 4.0815) (xy 0.2958 4.0688) (xy 0.3276 4.0547)
						(xy 0.3589 4.0391) (xy 0.3895 4.022) (xy 0.4195 4.0034) (xy 0.4488 3.9833) (xy 0.4774 3.9617)
						(xy 0.5051 3.9387) (xy 2.2245 2.4392) (xy 2.2314 2.4335) (xy 2.2385 2.4281) (xy 2.2457 2.4231)
						(xy 2.2531 2.4184) (xy 2.2607 2.4141) (xy 2.2684 2.4102) (xy 2.2762 2.4066) (xy 2.2841 2.4034)
						(xy 2.2921 2.4006) (xy 2.3003 2.3982) (xy 2.3085 2.3961) (xy 2.3167 2.3943) (xy 2.325 2.393) (xy 2.3334 2.392)
						(xy 2.3417 2.3914) (xy 2.3501 2.3911) (xy 2.3585 2.3912) (xy 2.3669 2.3917) (xy 2.3753 2.3925)
						(xy 2.3836 2.3937) (xy 2.3919 2.3953) (xy 2.4001 2.3972) (xy 2.4082 2.3995) (xy 2.4163 2.4022)
						(xy 2.4243 2.4052) (xy 2.4322 2.4087) (xy 2.4399 2.4124) (xy 2.4475 2.4166) (xy 2.455 2.4211)
						(xy 2.4624 2.4259) (xy 2.4695 2.4312) (xy 2.4765 2.4368) (xy 5.0759 4.6179) (xy 5.1206 4.6528)
						(xy 5.1671 4.6841) (xy 5.2152 4.7117) (xy 5.2647 4.7356) (xy 5.3154 4.7558) (xy 5.3671 4.7723)
						(xy 5.4197 4.7851) (xy 5.473 4.794) (xy 5.5268 4.7992) (xy 5.5809 4.8005) (xy 5.6351 4.798) (xy 5.6893 4.7916)
						(xy 5.7433 4.7813) (xy 5.7969 4.7671) (xy 5.8499 4.7489) (xy 5.9021 4.7268) (xy 5.9527 4.701)
						(xy 6.0007 4.6721) (xy 6.046 4.6402) (xy 6.0886 4.6054) (xy 6.1284 4.568) (xy 6.1652 4.5281) (xy 6.199 4.4858)
						(xy 6.2296 4.4413) (xy 6.257 4.3947) (xy 6.2811 4.3462) (xy 6.3017 4.296) (xy 6.3189 4.2441) (xy 6.3323 4.1908)
						(xy 6.3421 4.1362) (xy 6.348 4.0805) (xy 6.35 4.0238) (xy 6.35 -4.0327) (xy 6.349 -4.0725) (xy 6.346 -4.1118)
						(xy 6.341 -4.1506) (xy 6.3342 -4.1888) (xy 6.3255 -4.2263) (xy 6.3151 -4.2631) (xy 6.3029 -4.2991)
						(xy 6.289 -4.3343) (xy 6.2734 -4.3686) (xy 6.2563 -4.4021) (xy 6.2376 -4.4345) (xy 6.2174 -4.466)
						(xy 6.1957 -4.4964) (xy 6.1727 -4.5257) (xy 6.1483 -4.5539) (xy 6.1226 -4.5808) (xy 6.0956 -4.6065)
						(xy 6.0675 -4.6309) (xy 6.0382 -4.654) (xy 6.0077 -4.6756) (xy 5.9763 -4.6958) (xy 5.9438 -4.7145)
						(xy 5.9104 -4.7316) (xy 5.876 -4.7472) (xy 5.8408 -4.7611) (xy 5.8048 -4.7733) (xy 5.768 -4.7838)
						(xy 5.7305 -4.7924) (xy 5.6924 -4.7993) (xy 5.6536 -4.8042) (xy 5.6143 -4.8072) (xy 5.5744 -4.8082)
						(xy -5.5744 -4.8082) (xy -5.6143 -4.8072) (xy -5.6536 -4.8042) (xy -5.6924 -4.7993) (xy -5.7306 -4.7924)
						(xy -5.7681 -4.7838) (xy -5.8048 -4.7733) (xy -5.8409 -4.7611) (xy -5.8761 -4.7472) (xy -5.9104 -4.7316)
						(xy -5.9438 -4.7145) (xy -5.9763 -4.6958) (xy -6.0078 -4.6756) (xy -6.0382 -4.654) (xy -6.0675 -4.6309)
						(xy -6.0957 -4.6065) (xy -6.1226 -4.5808) (xy -6.1483 -4.5539) (xy -6.1727 -4.5257) (xy -6.1957 -4.4964)
						(xy -6.2174 -4.466) (xy -6.2376 -4.4345) (xy -6.2563 -4.4021) (xy -6.2734 -4.3686) (xy -6.289 -4.3343)
						(xy -6.3029 -4.2991) (xy -6.3151 -4.2631) (xy -6.3255 -4.2263) (xy -6.3342 -4.1888) (xy -6.341 -4.1506)
						(xy -6.346 -4.1118) (xy -6.349 -4.0725) (xy -6.35 -4.0327) (xy -6.35 1.5556) (xy -5.7683 1.5556)
						(xy -5.7683 -4.0327) (xy -5.7681 -4.0426) (xy -5.7673 -4.0525) (xy -5.7661 -4.0622) (xy -5.7644 -4.0717)
						(xy -5.7622 -4.0811) (xy -5.7596 -4.0903) (xy -5.7566 -4.0993) (xy -5.7531 -4.1081) (xy -5.7492 -4.1167)
						(xy -5.7449 -4.1251) (xy -5.7403 -4.1332) (xy -5.7352 -4.1411) (xy -5.7298 -4.1487) (xy -5.7241 -4.156)
						(xy -5.718 -4.163) (xy -5.7115 -4.1698) (xy -5.7048 -4.1762) (xy -5.6978 -4.1823) (xy -5.6905 -4.188)
						(xy -5.6829 -4.1934) (xy -5.675 -4.1985) (xy -5.6669 -4.2032) (xy -5.6585 -4.2074) (xy -5.6499 -4.2113)
						(xy -5.6411 -4.2148) (xy -5.6321 -4.2178) (xy -5.6229 -4.2204) (xy -5.6135 -4.2226) (xy -5.604 -4.2243)
						(xy -5.5943 -4.2255) (xy -5.5844 -4.2263) (xy -5.5744 -4.2265) (xy 5.5744 -4.2265) (xy 5.5844 -4.2263)
						(xy 5.5942 -4.2255) (xy 5.6039 -4.2243) (xy 5.6135 -4.2226) (xy 5.6229 -4.2204) (xy 5.6321 -4.2178)
						(xy 5.6411 -4.2148) (xy 5.6499 -4.2113) (xy 5.6585 -4.2074) (xy 5.6668 -4.2032) (xy 5.6749 -4.1985)
						(xy 5.6828 -4.1934) (xy 5.6904 -4.188) (xy 5.6977 -4.1823) (xy 5.7048 -4.1762) (xy 5.7115 -4.1698)
						(xy 5.7179 -4.163) (xy 5.724 -4.156) (xy 5.7298 -4.1487) (xy 5.7352 -4.1411) (xy 5.7402 -4.1332)
						(xy 5.7449 -4.1251) (xy 5.7492 -4.1167) (xy 5.7531 -4.1081) (xy 5.7565 -4.0993) (xy 5.7596 -4.0903)
						(xy 5.7622 -4.0811) (xy 5.7644 -4.0717) (xy 5.7661 -4.0622) (xy 5.7673 -4.0525) (xy 5.7681 -4.0426)
						(xy 5.7683 -4.0327) (xy 5.7683 1.5556) (xy 5.768 1.5681) (xy 5.7671 1.5804) (xy 5.7655 1.5925)
						(xy 5.7634 1.6045) (xy 5.7607 1.6162) (xy 5.7574 1.6277) (xy 5.7536 1.6389) (xy 5.7493 1.6499)
						(xy 5.7444 1.6607) (xy 5.7391 1.6711) (xy 5.7332 1.6813) (xy 5.7269 1.6911) (xy 5.7201 1.7006)
						(xy 5.713 1.7098) (xy 5.7053 1.7186) (xy 5.6973 1.727) (xy 5.6889 1.735) (xy 5.6801 1.7426) (xy 5.6709 1.7498)
						(xy 5.6614 1.7566) (xy 5.6516 1.7629) (xy 5.6414 1.7687) (xy 5.631 1.7741) (xy 5.6203 1.7789)
						(xy 5.6093 1.7833) (xy 5.598 1.7871) (xy 5.5865 1.7904) (xy 5.5748 1.7931) (xy 5.5628 1.7952)
						(xy 5.5507 1.7968) (xy 5.5384 1.7977) (xy 5.526 1.798) (xy -5.526 1.798) (xy -5.5384 1.7977) (xy -5.5507 1.7968)
						(xy -5.5629 1.7952) (xy -5.5748 1.7931) (xy -5.5865 1.7904) (xy -5.598 1.7871) (xy -5.6093 1.7833)
						(xy -5.6203 1.7789) (xy -5.631 1.7741) (xy -5.6415 1.7687) (xy -5.6516 1.7629) (xy -5.6615 1.7566)
						(xy -5.671 1.7498) (xy -5.6801 1.7426) (xy -5.6889 1.735) (xy -5.6973 1.727) (xy -5.7054 1.7186)
						(xy -5.713 1.7098) (xy -5.7202 1.7006) (xy -5.7269 1.6911) (xy -5.7332 1.6813) (xy -5.7391 1.6711)
						(xy -5.7444 1.6607) (xy -5.7493 1.6499) (xy -5.7536 1.6389) (xy -5.7574 1.6277) (xy -5.7607 1.6162)
						(xy -5.7634 1.6045) (xy -5.7655 1.5925) (xy -5.7671 1.5804) (xy -5.768 1.5681) (xy -5.7683 1.5556)
						(xy -6.35 1.5556) (xy -6.35 4.0238) (xy -6.3495 4.0523) (xy -6.348 4.0805) (xy -6.3455 4.1085)
						(xy -6.3421 4.1362) (xy -6.3377 4.1637) (xy -6.3323 4.1908) (xy -6.3261 4.2176) (xy -6.3189 4.2441)
						(xy -6.3108 4.2702) (xy -6.3018 4.296) (xy -6.2919 4.3213) (xy -6.2811 4.3462) (xy -6.2695 4.3707)
						(xy -6.2571 4.3947) (xy -6.2438 4.4182) (xy -6.2297 4.4413) (xy -6.2148 4.4638) (xy -6.199 4.4858)
						(xy -6.1825 4.5072) (xy -6.1653 4.5281) (xy -6.1472 4.5484) (xy -6.1285 4.568) (xy -6.1089 4.587)
						(xy -6.0887 4.6054) (xy -6.0677 4.6231) (xy -6.0461 4.6402) (xy -6.0238 4.6565) (xy -6.0007 4.6721)
						(xy -5.9771 4.6869) (xy -5.9527 4.701) (xy -5.9278 4.7143) (xy -5.9022 4.7268) (xy -5.85 4.7489)
						(xy -5.797 4.7671) (xy -5.7434 4.7813) (xy -5.6894 4.7916) (xy -5.6352 4.798) (xy -5.5809 4.8006)
						(xy -5.5268 4.7992)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -4.5733 1.0153) (xy -4.5659 1.0147) (xy -4.5586 1.0138) (xy -4.5515 1.0125) (xy -4.5444 1.0109)
						(xy -4.5375 1.0089) (xy -4.5308 1.0067) (xy -4.5242 1.0041) (xy -4.5177 1.0011) (xy -4.5115 0.9979)
						(xy -4.5054 0.9944) (xy -4.4995 0.9906) (xy -4.4938 0.9866) (xy -4.4883 0.9823) (xy -4.483 0.9777)
						(xy -4.4779 0.9729) (xy -4.4731 0.9678) (xy -4.4686 0.9626) (xy -4.4642 0.9571) (xy -4.4602 0.9514)
						(xy -4.4564 0.9455) (xy -4.4529 0.9394) (xy -4.4497 0.9331) (xy -4.4468 0.9267) (xy -4.4442 0.9201)
						(xy -4.4419 0.9133) (xy -4.4399 0.9064) (xy -4.4383 0.8994) (xy -4.437 0.8922) (xy -4.4361 0.8849)
						(xy -4.4355 0.8776) (xy -4.4353 0.8701) (xy -4.4353 0.3369) (xy -4.4355 0.3294) (xy -4.4361 0.322)
						(xy -4.437 0.3147) (xy -4.4383 0.3076) (xy -4.4399 0.3005) (xy -4.4419 0.2936) (xy -4.4442 0.2869)
						(xy -4.4468 0.2803) (xy -4.4497 0.2738) (xy -4.4529 0.2676) (xy -4.4564 0.2615) (xy -4.4602 0.2556)
						(xy -4.4642 0.2499) (xy -4.4686 0.2444) (xy -4.4731 0.2391) (xy -4.4779 0.2341) (xy -4.483 0.2292)
						(xy -4.4883 0.2247) (xy -4.4938 0.2203) (xy -4.4995 0.2163) (xy -4.5054 0.2125) (xy -4.5115 0.209)
						(xy -4.5177 0.2058) (xy -4.5242 0.2029) (xy -4.5308 0.2003) (xy -4.5375 0.198) (xy -4.5444 0.196)
						(xy -4.5515 0.1944) (xy -4.5586 0.1931) (xy -4.5659 0.1922) (xy -4.5733 0.1916) (xy -4.5808 0.1914)
						(xy -5.114 0.1914) (xy -5.1214 0.1916) (xy -5.1288 0.1922) (xy -5.1361 0.1931) (xy -5.1433 0.1944)
						(xy -5.1503 0.196) (xy -5.1572 0.198) (xy -5.1639 0.2003) (xy -5.1705 0.2029) (xy -5.177 0.2058)
						(xy -5.1833 0.209) (xy -5.1893 0.2125) (xy -5.1952 0.2163) (xy -5.2009 0.2203) (xy -5.2064 0.2247)
						(xy -5.2117 0.2292) (xy -5.2168 0.2341) (xy -5.2216 0.2391) (xy -5.2262 0.2444) (xy -5.2305 0.2499)
						(xy -5.2345 0.2556) (xy -5.2383 0.2615) (xy -5.2418 0.2676) (xy -5.245 0.2738) (xy -5.2479 0.2803)
						(xy -5.2506 0.2869) (xy -5.2528 0.2936) (xy -5.2548 0.3005) (xy -5.2564 0.3076) (xy -5.2577 0.3147)
						(xy -5.2586 0.322) (xy -5.2592 0.3294) (xy -5.2594 0.3369) (xy -5.2594 0.8701) (xy -5.2592 0.8776)
						(xy -5.2586 0.8849) (xy -5.2577 0.8922) (xy -5.2564 0.8994) (xy -5.2548 0.9064) (xy -5.2528 0.9133)
						(xy -5.2506 0.9201) (xy -5.2479 0.9267) (xy -5.245 0.9331) (xy -5.2418 0.9394) (xy -5.2383 0.9455)
						(xy -5.2345 0.9514) (xy -5.2305 0.9571) (xy -5.2262 0.9626) (xy -5.2216 0.9678) (xy -5.2168 0.9729)
						(xy -5.2117 0.9777) (xy -5.2064 0.9823) (xy -5.2009 0.9866) (xy -5.1952 0.9906) (xy -5.1893 0.9944)
						(xy -5.1833 0.9979) (xy -5.177 1.0011) (xy -5.1705 1.0041) (xy -5.1639 1.0067) (xy -5.1572 1.0089)
						(xy -5.1503 1.0109) (xy -5.1433 1.0125) (xy -5.1361 1.0138) (xy -5.1288 1.0147) (xy -5.1214 1.0153)
						(xy -5.114 1.0155) (xy -4.5808 1.0155) (xy -4.5733 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -4.5733 -0.1965) (xy -4.5659 -0.1971) (xy -4.5586 -0.198) (xy -4.5515 -0.1993) (xy -4.5444 -0.2009)
						(xy -4.5375 -0.2029) (xy -4.5308 -0.2052) (xy -4.5242 -0.2078) (xy -4.5177 -0.2107) (xy -4.5115 -0.2139)
						(xy -4.5054 -0.2174) (xy -4.4995 -0.2212) (xy -4.4938 -0.2253) (xy -4.4883 -0.2296) (xy -4.483 -0.2341)
						(xy -4.4779 -0.239) (xy -4.4731 -0.244) (xy -4.4686 -0.2493) (xy -4.4642 -0.2548) (xy -4.4602 -0.2605)
						(xy -4.4564 -0.2664) (xy -4.4529 -0.2725) (xy -4.4497 -0.2787) (xy -4.4468 -0.2852) (xy -4.4442 -0.2918)
						(xy -4.4419 -0.2985) (xy -4.4399 -0.3054) (xy -4.4383 -0.3125) (xy -4.437 -0.3196) (xy -4.4361 -0.3269)
						(xy -4.4355 -0.3343) (xy -4.4353 -0.3418) (xy -4.4353 -0.875) (xy -4.4355 -0.8825) (xy -4.4361 -0.8898)
						(xy -4.437 -0.8971) (xy -4.4383 -0.9043) (xy -4.4399 -0.9113) (xy -4.4419 -0.9182) (xy -4.4442 -0.925)
						(xy -4.4468 -0.9316) (xy -4.4497 -0.938) (xy -4.4529 -0.9443) (xy -4.4564 -0.9504) (xy -4.4602 -0.9563)
						(xy -4.4642 -0.962) (xy -4.4686 -0.9675) (xy -4.4731 -0.9727) (xy -4.4779 -0.9778) (xy -4.483 -0.9826)
						(xy -4.4883 -0.9872) (xy -4.4938 -0.9915) (xy -4.4995 -0.9956) (xy -4.5054 -0.9993) (xy -4.5115 -1.0028)
						(xy -4.5177 -1.006) (xy -4.5242 -1.009) (xy -4.5308 -1.0116) (xy -4.5375 -1.0139) (xy -4.5444 -1.0158)
						(xy -4.5515 -1.0174) (xy -4.5586 -1.0187) (xy -4.5659 -1.0196) (xy -4.5733 -1.0202) (xy -4.5808 -1.0204)
						(xy -5.114 -1.0204) (xy -5.1214 -1.0202) (xy -5.1288 -1.0196) (xy -5.1361 -1.0187) (xy -5.1433 -1.0174)
						(xy -5.1503 -1.0158) (xy -5.1572 -1.0139) (xy -5.1639 -1.0116) (xy -5.1705 -1.009) (xy -5.177 -1.006)
						(xy -5.1833 -1.0028) (xy -5.1893 -0.9993) (xy -5.1952 -0.9956) (xy -5.2009 -0.9915) (xy -5.2064 -0.9872)
						(xy -5.2117 -0.9826) (xy -5.2168 -0.9778) (xy -5.2216 -0.9727) (xy -5.2262 -0.9675) (xy -5.2305 -0.962)
						(xy -5.2345 -0.9563) (xy -5.2383 -0.9504) (xy -5.2418 -0.9443) (xy -5.245 -0.938) (xy -5.2479 -0.9316)
						(xy -5.2506 -0.925) (xy -5.2528 -0.9182) (xy -5.2548 -0.9113) (xy -5.2564 -0.9043) (xy -5.2577 -0.8971)
						(xy -5.2586 -0.8898) (xy -5.2592 -0.8825) (xy -5.2594 -0.875) (xy -5.2594 -0.3418) (xy -5.2592 -0.3343)
						(xy -5.2586 -0.3269) (xy -5.2577 -0.3196) (xy -5.2564 -0.3125) (xy -5.2548 -0.3054) (xy -5.2528 -0.2985)
						(xy -5.2506 -0.2918) (xy -5.2479 -0.2852) (xy -5.245 -0.2787) (xy -5.2418 -0.2725) (xy -5.2383 -0.2664)
						(xy -5.2345 -0.2605) (xy -5.2305 -0.2548) (xy -5.2262 -0.2493) (xy -5.2216 -0.244) (xy -5.2168 -0.239)
						(xy -5.2117 -0.2341) (xy -5.2064 -0.2296) (xy -5.2009 -0.2253) (xy -5.1952 -0.2212) (xy -5.1893 -0.2174)
						(xy -5.1833 -0.2139) (xy -5.177 -0.2107) (xy -5.1705 -0.2078) (xy -5.1639 -0.2052) (xy -5.1572 -0.2029)
						(xy -5.1503 -0.2009) (xy -5.1433 -0.1993) (xy -5.1361 -0.198) (xy -5.1288 -0.1971) (xy -5.1214 -0.1965)
						(xy -5.114 -0.1964) (xy -4.5808 -0.1964) (xy -4.5733 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -4.5733 -2.6029) (xy -4.5659 -2.6035) (xy -4.5586 -2.6044) (xy -4.5515 -2.6057) (xy -4.5444 -2.6073)
						(xy -4.5375 -2.6093) (xy -4.5308 -2.6115) (xy -4.5242 -2.6141) (xy -4.5177 -2.6171) (xy -4.5115 -2.6203)
						(xy -4.5054 -2.6238) (xy -4.4995 -2.6276) (xy -4.4938 -2.6316) (xy -4.4883 -2.6359) (xy -4.483 -2.6405)
						(xy -4.4779 -2.6453) (xy -4.4731 -2.6504) (xy -4.4686 -2.6556) (xy -4.4642 -2.6611) (xy -4.4602 -2.6668)
						(xy -4.4564 -2.6727) (xy -4.4529 -2.6788) (xy -4.4497 -2.6851) (xy -4.4468 -2.6915) (xy -4.4442 -2.6981)
						(xy -4.4419 -2.7049) (xy -4.4399 -2.7118) (xy -4.4383 -2.7188) (xy -4.437 -2.726) (xy -4.4361 -2.7333)
						(xy -4.4355 -2.7406) (xy -4.4353 -2.7481) (xy -4.4353 -3.2813) (xy -4.4355 -3.2888) (xy -4.4361 -3.2962)
						(xy -4.437 -3.3035) (xy -4.4383 -3.3106) (xy -4.4399 -3.3177) (xy -4.4419 -3.3246) (xy -4.4442 -3.3313)
						(xy -4.4468 -3.3379) (xy -4.4497 -3.3444) (xy -4.4529 -3.3506) (xy -4.4564 -3.3567) (xy -4.4602 -3.3626)
						(xy -4.4642 -3.3683) (xy -4.4686 -3.3738) (xy -4.4731 -3.3791) (xy -4.4779 -3.3841) (xy -4.483 -3.389)
						(xy -4.4883 -3.3935) (xy -4.4938 -3.3979) (xy -4.4995 -3.4019) (xy -4.5054 -3.4057) (xy -4.5115 -3.4092)
						(xy -4.5177 -3.4124) (xy -4.5242 -3.4153) (xy -4.5308 -3.4179) (xy -4.5375 -3.4202) (xy -4.5444 -3.4222)
						(xy -4.5515 -3.4238) (xy -4.5586 -3.4251) (xy -4.5659 -3.426) (xy -4.5733 -3.4266) (xy -4.5808 -3.4268)
						(xy -5.114 -3.4268) (xy -5.1214 -3.4266) (xy -5.1288 -3.426) (xy -5.1361 -3.4251) (xy -5.1433 -3.4238)
						(xy -5.1503 -3.4222) (xy -5.1572 -3.4202) (xy -5.1639 -3.4179) (xy -5.1705 -3.4153) (xy -5.177 -3.4124)
						(xy -5.1833 -3.4092) (xy -5.1893 -3.4057) (xy -5.1952 -3.4019) (xy -5.2009 -3.3979) (xy -5.2064 -3.3935)
						(xy -5.2117 -3.389) (xy -5.2168 -3.3841) (xy -5.2216 -3.3791) (xy -5.2262 -3.3738) (xy -5.2305 -3.3683)
						(xy -5.2345 -3.3626) (xy -5.2383 -3.3567) (xy -5.2418 -3.3506) (xy -5.245 -3.3444) (xy -5.2479 -3.3379)
						(xy -5.2506 -3.3313) (xy -5.2528 -3.3246) (xy -5.2548 -3.3177) (xy -5.2564 -3.3106) (xy -5.2577 -3.3035)
						(xy -5.2586 -3.2962) (xy -5.2592 -3.2888) (xy -5.2594 -3.2813) (xy -5.2594 -2.7481) (xy -5.2592 -2.7406)
						(xy -5.2586 -2.7333) (xy -5.2577 -2.726) (xy -5.2564 -2.7188) (xy -5.2548 -2.7118) (xy -5.2528 -2.7049)
						(xy -5.2506 -2.6981) (xy -5.2479 -2.6915) (xy -5.245 -2.6851) (xy -5.2418 -2.6788) (xy -5.2383 -2.6727)
						(xy -5.2345 -2.6668) (xy -5.2305 -2.6611) (xy -5.2262 -2.6556) (xy -5.2216 -2.6504) (xy -5.2168 -2.6453)
						(xy -5.2117 -2.6405) (xy -5.2064 -2.6359) (xy -5.2009 -2.6316) (xy -5.1952 -2.6276) (xy -5.1893 -2.6238)
						(xy -5.1833 -2.6203) (xy -5.177 -2.6171) (xy -5.1705 -2.6141) (xy -5.1639 -2.6115) (xy -5.1572 -2.6093)
						(xy -5.1503 -2.6073) (xy -5.1433 -2.6057) (xy -5.1361 -2.6044) (xy -5.1288 -2.6035) (xy -5.1214 -2.6029)
						(xy -5.114 -2.6027) (xy -4.5808 -2.6027) (xy -4.5733 -2.6029)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -4.5733 -2.6202) (xy -4.5659 -2.6208) (xy -4.5586 -2.6217) (xy -4.5515 -2.623) (xy -4.5444 -2.6246)
						(xy -4.5375 -2.6266) (xy -4.5308 -2.6288) (xy -4.5242 -2.6314) (xy -4.5177 -2.6344) (xy -4.5115 -2.6376)
						(xy -4.5054 -2.6411) (xy -4.4995 -2.6449) (xy -4.4938 -2.6489) (xy -4.4883 -2.6532) (xy -4.483 -2.6578)
						(xy -4.4779 -2.6626) (xy -4.4731 -2.6677) (xy -4.4686 -2.673) (xy -4.4642 -2.6784) (xy -4.4602 -2.6841)
						(xy -4.4564 -2.69) (xy -4.4529 -2.6961) (xy -4.4497 -2.7024) (xy -4.4468 -2.7088) (xy -4.4442 -2.7155)
						(xy -4.4419 -2.7222) (xy -4.4399 -2.7291) (xy -4.4383 -2.7361) (xy -4.437 -2.7433) (xy -4.4361 -2.7506)
						(xy -4.4355 -2.758) (xy -4.4353 -2.7654) (xy -4.4353 -3.2986) (xy -4.4355 -3.3061) (xy -4.4361 -3.3135)
						(xy -4.437 -3.3208) (xy -4.4383 -3.3279) (xy -4.4399 -3.335) (xy -4.4419 -3.3419) (xy -4.4442 -3.3486)
						(xy -4.4468 -3.3552) (xy -4.4497 -3.3617) (xy -4.4529 -3.3679) (xy -4.4564 -3.374) (xy -4.4602 -3.3799)
						(xy -4.4642 -3.3856) (xy -4.4686 -3.3911) (xy -4.4731 -3.3964) (xy -4.4779 -3.4015) (xy -4.483 -3.4063)
						(xy -4.4883 -3.4108) (xy -4.4938 -3.4152) (xy -4.4995 -3.4192) (xy -4.5054 -3.423) (xy -4.5115 -3.4265)
						(xy -4.5177 -3.4297) (xy -4.5242 -3.4326) (xy -4.5308 -3.4352) (xy -4.5375 -3.4375) (xy -4.5444 -3.4395)
						(xy -4.5515 -3.4411) (xy -4.5586 -3.4424) (xy -4.5659 -3.4433) (xy -4.5733 -3.4439) (xy -4.5808 -3.4441)
						(xy -5.114 -3.4441) (xy -5.1214 -3.4439) (xy -5.1288 -3.4433) (xy -5.1361 -3.4424) (xy -5.1433 -3.4411)
						(xy -5.1503 -3.4395) (xy -5.1572 -3.4375) (xy -5.1639 -3.4352) (xy -5.1705 -3.4326) (xy -5.177 -3.4297)
						(xy -5.1833 -3.4265) (xy -5.1893 -3.423) (xy -5.1952 -3.4192) (xy -5.2009 -3.4152) (xy -5.2064 -3.4108)
						(xy -5.2117 -3.4063) (xy -5.2168 -3.4015) (xy -5.2216 -3.3964) (xy -5.2262 -3.3911) (xy -5.2305 -3.3856)
						(xy -5.2345 -3.3799) (xy -5.2383 -3.374) (xy -5.2418 -3.3679) (xy -5.245 -3.3617) (xy -5.2479 -3.3552)
						(xy -5.2506 -3.3486) (xy -5.2528 -3.3419) (xy -5.2548 -3.335) (xy -5.2564 -3.3279) (xy -5.2577 -3.3208)
						(xy -5.2586 -3.3135) (xy -5.2592 -3.3061) (xy -5.2594 -3.2986) (xy -5.2594 -2.7654) (xy -5.2592 -2.758)
						(xy -5.2586 -2.7506) (xy -5.2577 -2.7433) (xy -5.2564 -2.7361) (xy -5.2548 -2.7291) (xy -5.2528 -2.7222)
						(xy -5.2506 -2.7155) (xy -5.2479 -2.7088) (xy -5.245 -2.7024) (xy -5.2418 -2.6961) (xy -5.2383 -2.69)
						(xy -5.2345 -2.6841) (xy -5.2305 -2.6784) (xy -5.2262 -2.673) (xy -5.2216 -2.6677) (xy -5.2168 -2.6626)
						(xy -5.2117 -2.6578) (xy -5.2064 -2.6532) (xy -5.2009 -2.6489) (xy -5.1952 -2.6449) (xy -5.1893 -2.6411)
						(xy -5.1833 -2.6376) (xy -5.177 -2.6344) (xy -5.1705 -2.6314) (xy -5.1639 -2.6288) (xy -5.1572 -2.6266)
						(xy -5.1503 -2.6246) (xy -5.1433 -2.623) (xy -5.1361 -2.6217) (xy -5.1288 -2.6208) (xy -5.1214 -2.6202)
						(xy -5.114 -2.62) (xy -4.5808 -2.62) (xy -4.5733 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3668 -1.4084) (xy -3.3594 -1.4089) (xy -3.3521 -1.4099) (xy -3.345 -1.4111) (xy -3.3379 -1.4128)
						(xy -3.331 -1.4147) (xy -3.3243 -1.417) (xy -3.3177 -1.4196) (xy -3.3112 -1.4225) (xy -3.305 -1.4257)
						(xy -3.2989 -1.4292) (xy -3.293 -1.433) (xy -3.2873 -1.4371) (xy -3.2818 -1.4414) (xy -3.2765 -1.446)
						(xy -3.2714 -1.4508) (xy -3.2666 -1.4558) (xy -3.2621 -1.4611) (xy -3.2577 -1.4666) (xy -3.2537 -1.4723)
						(xy -3.2499 -1.4782) (xy -3.2464 -1.4843) (xy -3.2432 -1.4906) (xy -3.2403 -1.497) (xy -3.2377 -1.5036)
						(xy -3.2354 -1.5104) (xy -3.2334 -1.5173) (xy -3.2318 -1.5243) (xy -3.2305 -1.5315) (xy -3.2296 -1.5387)
						(xy -3.229 -1.5461) (xy -3.2288 -1.5536) (xy -3.2288 -2.0868) (xy -3.229 -2.0943) (xy -3.2296 -2.1017)
						(xy -3.2305 -2.1089) (xy -3.2318 -2.1161) (xy -3.2334 -2.1231) (xy -3.2354 -2.13) (xy -3.2377 -2.1368)
						(xy -3.2403 -2.1434) (xy -3.2432 -2.1498) (xy -3.2464 -2.1561) (xy -3.2499 -2.1622) (xy -3.2537 -2.1681)
						(xy -3.2577 -2.1738) (xy -3.2621 -2.1793) (xy -3.2666 -2.1846) (xy -3.2714 -2.1896) (xy -3.2765 -2.1944)
						(xy -3.2818 -2.199) (xy -3.2873 -2.2033) (xy -3.293 -2.2074) (xy -3.2989 -2.2112) (xy -3.305 -2.2147)
						(xy -3.3112 -2.2179) (xy -3.3177 -2.2208) (xy -3.3243 -2.2234) (xy -3.331 -2.2257) (xy -3.3379 -2.2276)
						(xy -3.345 -2.2293) (xy -3.3521 -2.2306) (xy -3.3594 -2.2315) (xy -3.3668 -2.232) (xy -3.3743 -2.2322)
						(xy -3.4549 -2.2322) (xy -3.9075 -2.2322) (xy -4.5861 -2.2322) (xy -5.0226 -2.2322) (xy -5.1193 -2.2322)
						(xy -5.1268 -2.232) (xy -5.1342 -2.2315) (xy -5.1414 -2.2306) (xy -5.1486 -2.2293) (xy -5.1556 -2.2276)
						(xy -5.1625 -2.2257) (xy -5.1693 -2.2234) (xy -5.1759 -2.2208) (xy -5.1823 -2.2179) (xy -5.1886 -2.2147)
						(xy -5.1947 -2.2112) (xy -5.2006 -2.2074) (xy -5.2063 -2.2033) (xy -5.2118 -2.199) (xy -5.2171 -2.1944)
						(xy -5.2221 -2.1896) (xy -5.2269 -2.1846) (xy -5.2315 -2.1793) (xy -5.2358 -2.1738) (xy -5.2399 -2.1681)
						(xy -5.2437 -2.1622) (xy -5.2472 -2.1561) (xy -5.2504 -2.1498) (xy -5.2533 -2.1434) (xy -5.2559 -2.1368)
						(xy -5.2582 -2.13) (xy -5.2601 -2.1231) (xy -5.2618 -2.1161) (xy -5.263 -2.1089) (xy -5.264 -2.1017)
						(xy -5.2645 -2.0943) (xy -5.2647 -2.0868) (xy -5.2647 -1.5536) (xy -5.2645 -1.5461) (xy -5.264 -1.5387)
						(xy -5.263 -1.5315) (xy -5.2618 -1.5243) (xy -5.2601 -1.5173) (xy -5.2582 -1.5104) (xy -5.2559 -1.5036)
						(xy -5.2533 -1.497) (xy -5.2504 -1.4906) (xy -5.2472 -1.4843) (xy -5.2437 -1.4782) (xy -5.2399 -1.4723)
						(xy -5.2358 -1.4666) (xy -5.2315 -1.4611) (xy -5.2269 -1.4558) (xy -5.2221 -1.4508) (xy -5.2171 -1.446)
						(xy -5.2118 -1.4414) (xy -5.2063 -1.4371) (xy -5.2006 -1.433) (xy -5.1947 -1.4292) (xy -5.1886 -1.4257)
						(xy -5.1823 -1.4225) (xy -5.1759 -1.4196) (xy -5.1693 -1.417) (xy -5.1625 -1.4147) (xy -5.1556 -1.4128)
						(xy -5.1486 -1.4111) (xy -5.1414 -1.4099) (xy -5.1342 -1.4089) (xy -5.1268 -1.4084) (xy -5.1193 -1.4082)
						(xy -5.0226 -1.4082) (xy -4.5861 -1.4082) (xy -3.9075 -1.4082) (xy -3.4549 -1.4082) (xy -3.3743 -1.4082)
						(xy -3.3668 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3561 1.0153) (xy -3.3487 1.0147) (xy -3.3415 1.0138) (xy -3.3343 1.0125) (xy -3.3273 1.0109)
						(xy -3.3204 1.0089) (xy -3.3136 1.0067) (xy -3.307 1.0041) (xy -3.3006 1.0011) (xy -3.2943 0.9979)
						(xy -3.2882 0.9944) (xy -3.2823 0.9906) (xy -3.2766 0.9866) (xy -3.2711 0.9823) (xy -3.2658 0.9777)
						(xy -3.2608 0.9729) (xy -3.256 0.9678) (xy -3.2514 0.9626) (xy -3.2471 0.9571) (xy -3.243 0.9514)
						(xy -3.2392 0.9455) (xy -3.2357 0.9394) (xy -3.2325 0.9331) (xy -3.2296 0.9267) (xy -3.227 0.9201)
						(xy -3.2247 0.9133) (xy -3.2228 0.9064) (xy -3.2211 0.8994) (xy -3.2199 0.8922) (xy -3.2189 0.8849)
						(xy -3.2184 0.8776) (xy -3.2182 0.8701) (xy -3.2182 0.3369) (xy -3.2184 0.3294) (xy -3.2189 0.322)
						(xy -3.2199 0.3147) (xy -3.2211 0.3076) (xy -3.2228 0.3005) (xy -3.2247 0.2936) (xy -3.227 0.2869)
						(xy -3.2296 0.2803) (xy -3.2325 0.2738) (xy -3.2357 0.2676) (xy -3.2392 0.2615) (xy -3.243 0.2556)
						(xy -3.2471 0.2499) (xy -3.2514 0.2444) (xy -3.256 0.2391) (xy -3.2608 0.2341) (xy -3.2658 0.2292)
						(xy -3.2711 0.2247) (xy -3.2766 0.2203) (xy -3.2823 0.2163) (xy -3.2882 0.2125) (xy -3.2943 0.209)
						(xy -3.3006 0.2058) (xy -3.307 0.2029) (xy -3.3136 0.2003) (xy -3.3204 0.198) (xy -3.3273 0.196)
						(xy -3.3343 0.1944) (xy -3.3415 0.1931) (xy -3.3487 0.1922) (xy -3.3561 0.1916) (xy -3.3636 0.1914)
						(xy -3.8968 0.1914) (xy -3.9043 0.1916) (xy -3.9117 0.1922) (xy -3.9189 0.1931) (xy -3.9261 0.1944)
						(xy -3.9331 0.196) (xy -3.94 0.198) (xy -3.9468 0.2003) (xy -3.9534 0.2029) (xy -3.9598 0.2058)
						(xy -3.9661 0.209) (xy -3.9722 0.2125) (xy -3.9781 0.2163) (xy -3.9838 0.2203) (xy -3.9893 0.2247)
						(xy -3.9946 0.2292) (xy -3.9996 0.2341) (xy -4.0044 0.2391) (xy -4.009 0.2444) (xy -4.0133 0.2499)
						(xy -4.0174 0.2556) (xy -4.0212 0.2615) (xy -4.0247 0.2676) (xy -4.0279 0.2738) (xy -4.0308 0.2803)
						(xy -4.0334 0.2869) (xy -4.0357 0.2936) (xy -4.0376 0.3005) (xy -4.0393 0.3076) (xy -4.0405 0.3147)
						(xy -4.0415 0.322) (xy -4.042 0.3294) (xy -4.0422 0.3369) (xy -4.0422 0.8701) (xy -4.042 0.8776)
						(xy -4.0415 0.8849) (xy -4.0405 0.8922) (xy -4.0393 0.8994) (xy -4.0376 0.9064) (xy -4.0357 0.9133)
						(xy -4.0334 0.9201) (xy -4.0308 0.9267) (xy -4.0279 0.9331) (xy -4.0247 0.9394) (xy -4.0212 0.9455)
						(xy -4.0174 0.9514) (xy -4.0133 0.9571) (xy -4.009 0.9626) (xy -4.0044 0.9678) (xy -3.9996 0.9729)
						(xy -3.9946 0.9777) (xy -3.9893 0.9823) (xy -3.9838 0.9866) (xy -3.9781 0.9906) (xy -3.9722 0.9944)
						(xy -3.9661 0.9979) (xy -3.9598 1.0011) (xy -3.9534 1.0041) (xy -3.9468 1.0067) (xy -3.94 1.0089)
						(xy -3.9331 1.0109) (xy -3.9261 1.0125) (xy -3.9189 1.0138) (xy -3.9117 1.0147) (xy -3.9043 1.0153)
						(xy -3.8968 1.0155) (xy -3.3636 1.0155) (xy -3.3561 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3561 -0.1965) (xy -3.3487 -0.1971) (xy -3.3415 -0.198) (xy -3.3343 -0.1993) (xy -3.3273 -0.2009)
						(xy -3.3204 -0.2029) (xy -3.3136 -0.2052) (xy -3.307 -0.2078) (xy -3.3006 -0.2107) (xy -3.2943 -0.2139)
						(xy -3.2882 -0.2174) (xy -3.2823 -0.2212) (xy -3.2766 -0.2253) (xy -3.2711 -0.2296) (xy -3.2658 -0.2341)
						(xy -3.2608 -0.239) (xy -3.256 -0.244) (xy -3.2514 -0.2493) (xy -3.2471 -0.2548) (xy -3.243 -0.2605)
						(xy -3.2392 -0.2664) (xy -3.2357 -0.2725) (xy -3.2325 -0.2787) (xy -3.2296 -0.2852) (xy -3.227 -0.2918)
						(xy -3.2247 -0.2985) (xy -3.2228 -0.3054) (xy -3.2211 -0.3125) (xy -3.2199 -0.3196) (xy -3.2189 -0.3269)
						(xy -3.2184 -0.3343) (xy -3.2182 -0.3418) (xy -3.2182 -0.875) (xy -3.2184 -0.8825) (xy -3.2189 -0.8898)
						(xy -3.2199 -0.8971) (xy -3.2211 -0.9043) (xy -3.2228 -0.9113) (xy -3.2247 -0.9182) (xy -3.227 -0.925)
						(xy -3.2296 -0.9316) (xy -3.2325 -0.938) (xy -3.2357 -0.9443) (xy -3.2392 -0.9504) (xy -3.243 -0.9563)
						(xy -3.2471 -0.962) (xy -3.2514 -0.9675) (xy -3.256 -0.9727) (xy -3.2608 -0.9778) (xy -3.2658 -0.9826)
						(xy -3.2711 -0.9872) (xy -3.2766 -0.9915) (xy -3.2823 -0.9956) (xy -3.2882 -0.9993) (xy -3.2943 -1.0028)
						(xy -3.3006 -1.006) (xy -3.307 -1.009) (xy -3.3136 -1.0116) (xy -3.3204 -1.0139) (xy -3.3273 -1.0158)
						(xy -3.3343 -1.0174) (xy -3.3415 -1.0187) (xy -3.3487 -1.0196) (xy -3.3561 -1.0202) (xy -3.3636 -1.0204)
						(xy -3.8968 -1.0204) (xy -3.9043 -1.0202) (xy -3.9117 -1.0196) (xy -3.9189 -1.0187) (xy -3.9261 -1.0174)
						(xy -3.9331 -1.0158) (xy -3.94 -1.0139) (xy -3.9468 -1.0116) (xy -3.9534 -1.009) (xy -3.9598 -1.006)
						(xy -3.9661 -1.0028) (xy -3.9722 -0.9993) (xy -3.9781 -0.9956) (xy -3.9838 -0.9915) (xy -3.9893 -0.9872)
						(xy -3.9946 -0.9826) (xy -3.9996 -0.9778) (xy -4.0044 -0.9727) (xy -4.009 -0.9675) (xy -4.0133 -0.962)
						(xy -4.0174 -0.9563) (xy -4.0212 -0.9504) (xy -4.0247 -0.9443) (xy -4.0279 -0.938) (xy -4.0308 -0.9316)
						(xy -4.0334 -0.925) (xy -4.0357 -0.9182) (xy -4.0376 -0.9113) (xy -4.0393 -0.9043) (xy -4.0405 -0.8971)
						(xy -4.0415 -0.8898) (xy -4.042 -0.8825) (xy -4.0422 -0.875) (xy -4.0422 -0.3418) (xy -4.042 -0.3343)
						(xy -4.0415 -0.3269) (xy -4.0405 -0.3196) (xy -4.0393 -0.3125) (xy -4.0376 -0.3054) (xy -4.0357 -0.2985)
						(xy -4.0334 -0.2918) (xy -4.0308 -0.2852) (xy -4.0279 -0.2787) (xy -4.0247 -0.2725) (xy -4.0212 -0.2664)
						(xy -4.0174 -0.2605) (xy -4.0133 -0.2548) (xy -4.009 -0.2493) (xy -4.0044 -0.244) (xy -3.9996 -0.239)
						(xy -3.9946 -0.2341) (xy -3.9893 -0.2296) (xy -3.9838 -0.2253) (xy -3.9781 -0.2212) (xy -3.9722 -0.2174)
						(xy -3.9661 -0.2139) (xy -3.9598 -0.2107) (xy -3.9534 -0.2078) (xy -3.9468 -0.2052) (xy -3.94 -0.2029)
						(xy -3.9331 -0.2009) (xy -3.9261 -0.1993) (xy -3.9189 -0.198) (xy -3.9117 -0.1971) (xy -3.9043 -0.1965)
						(xy -3.8968 -0.1964) (xy -3.3636 -0.1964) (xy -3.3561 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3561 -2.6029) (xy -3.3487 -2.6035) (xy -3.3415 -2.6044) (xy -3.3343 -2.6057) (xy -3.3273 -2.6073)
						(xy -3.3204 -2.6093) (xy -3.3136 -2.6115) (xy -3.307 -2.6141) (xy -3.3006 -2.6171) (xy -3.2943 -2.6203)
						(xy -3.2882 -2.6238) (xy -3.2823 -2.6276) (xy -3.2766 -2.6316) (xy -3.2711 -2.6359) (xy -3.2658 -2.6405)
						(xy -3.2608 -2.6453) (xy -3.256 -2.6504) (xy -3.2514 -2.6556) (xy -3.2471 -2.6611) (xy -3.243 -2.6668)
						(xy -3.2392 -2.6727) (xy -3.2357 -2.6788) (xy -3.2325 -2.6851) (xy -3.2296 -2.6915) (xy -3.227 -2.6981)
						(xy -3.2247 -2.7049) (xy -3.2228 -2.7118) (xy -3.2211 -2.7188) (xy -3.2199 -2.726) (xy -3.2189 -2.7333)
						(xy -3.2184 -2.7406) (xy -3.2182 -2.7481) (xy -3.2182 -3.2813) (xy -3.2184 -3.2888) (xy -3.2189 -3.2962)
						(xy -3.2199 -3.3035) (xy -3.2211 -3.3106) (xy -3.2228 -3.3177) (xy -3.2247 -3.3246) (xy -3.227 -3.3313)
						(xy -3.2296 -3.3379) (xy -3.2325 -3.3444) (xy -3.2357 -3.3506) (xy -3.2392 -3.3567) (xy -3.243 -3.3626)
						(xy -3.2471 -3.3683) (xy -3.2514 -3.3738) (xy -3.256 -3.3791) (xy -3.2608 -3.3841) (xy -3.2658 -3.389)
						(xy -3.2711 -3.3935) (xy -3.2766 -3.3979) (xy -3.2823 -3.4019) (xy -3.2882 -3.4057) (xy -3.2943 -3.4092)
						(xy -3.3006 -3.4124) (xy -3.307 -3.4153) (xy -3.3136 -3.4179) (xy -3.3204 -3.4202) (xy -3.3273 -3.4222)
						(xy -3.3343 -3.4238) (xy -3.3415 -3.4251) (xy -3.3487 -3.426) (xy -3.3561 -3.4266) (xy -3.3636 -3.4268)
						(xy -3.8968 -3.4268) (xy -3.9043 -3.4266) (xy -3.9117 -3.426) (xy -3.9189 -3.4251) (xy -3.9261 -3.4238)
						(xy -3.9331 -3.4222) (xy -3.94 -3.4202) (xy -3.9468 -3.4179) (xy -3.9534 -3.4153) (xy -3.9598 -3.4124)
						(xy -3.9661 -3.4092) (xy -3.9722 -3.4057) (xy -3.9781 -3.4019) (xy -3.9838 -3.3979) (xy -3.9893 -3.3935)
						(xy -3.9946 -3.389) (xy -3.9996 -3.3841) (xy -4.0044 -3.3791) (xy -4.009 -3.3738) (xy -4.0133 -3.3683)
						(xy -4.0174 -3.3626) (xy -4.0212 -3.3567) (xy -4.0247 -3.3506) (xy -4.0279 -3.3444) (xy -4.0308 -3.3379)
						(xy -4.0334 -3.3313) (xy -4.0357 -3.3246) (xy -4.0376 -3.3177) (xy -4.0393 -3.3106) (xy -4.0405 -3.3035)
						(xy -4.0415 -3.2962) (xy -4.042 -3.2888) (xy -4.0422 -3.2813) (xy -4.0422 -2.7481) (xy -4.042 -2.7406)
						(xy -4.0415 -2.7333) (xy -4.0405 -2.726) (xy -4.0393 -2.7188) (xy -4.0376 -2.7118) (xy -4.0357 -2.7049)
						(xy -4.0334 -2.6981) (xy -4.0308 -2.6915) (xy -4.0279 -2.6851) (xy -4.0247 -2.6788) (xy -4.0212 -2.6727)
						(xy -4.0174 -2.6668) (xy -4.0133 -2.6611) (xy -4.009 -2.6556) (xy -4.0044 -2.6504) (xy -3.9996 -2.6453)
						(xy -3.9946 -2.6405) (xy -3.9893 -2.6359) (xy -3.9838 -2.6316) (xy -3.9781 -2.6276) (xy -3.9722 -2.6238)
						(xy -3.9661 -2.6203) (xy -3.9598 -2.6171) (xy -3.9534 -2.6141) (xy -3.9468 -2.6115) (xy -3.94 -2.6093)
						(xy -3.9331 -2.6073) (xy -3.9261 -2.6057) (xy -3.9189 -2.6044) (xy -3.9117 -2.6035) (xy -3.9043 -2.6029)
						(xy -3.8968 -2.6027) (xy -3.3636 -2.6027) (xy -3.3561 -2.6029)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -3.3561 -2.6202) (xy -3.3487 -2.6208) (xy -3.3415 -2.6217) (xy -3.3343 -2.623) (xy -3.3273 -2.6246)
						(xy -3.3204 -2.6266) (xy -3.3136 -2.6288) (xy -3.307 -2.6314) (xy -3.3006 -2.6344) (xy -3.2943 -2.6376)
						(xy -3.2882 -2.6411) (xy -3.2823 -2.6449) (xy -3.2766 -2.6489) (xy -3.2711 -2.6532) (xy -3.2658 -2.6578)
						(xy -3.2608 -2.6626) (xy -3.256 -2.6677) (xy -3.2514 -2.673) (xy -3.2471 -2.6784) (xy -3.243 -2.6841)
						(xy -3.2392 -2.69) (xy -3.2357 -2.6961) (xy -3.2325 -2.7024) (xy -3.2296 -2.7088) (xy -3.227 -2.7155)
						(xy -3.2247 -2.7222) (xy -3.2228 -2.7291) (xy -3.2211 -2.7361) (xy -3.2199 -2.7433) (xy -3.2189 -2.7506)
						(xy -3.2184 -2.758) (xy -3.2182 -2.7654) (xy -3.2182 -3.2986) (xy -3.2184 -3.3061) (xy -3.2189 -3.3135)
						(xy -3.2199 -3.3208) (xy -3.2211 -3.3279) (xy -3.2228 -3.335) (xy -3.2247 -3.3419) (xy -3.227 -3.3486)
						(xy -3.2296 -3.3552) (xy -3.2325 -3.3617) (xy -3.2357 -3.3679) (xy -3.2392 -3.374) (xy -3.243 -3.3799)
						(xy -3.2471 -3.3856) (xy -3.2514 -3.3911) (xy -3.256 -3.3964) (xy -3.2608 -3.4015) (xy -3.2658 -3.4063)
						(xy -3.2711 -3.4108) (xy -3.2766 -3.4152) (xy -3.2823 -3.4192) (xy -3.2882 -3.423) (xy -3.2943 -3.4265)
						(xy -3.3006 -3.4297) (xy -3.307 -3.4326) (xy -3.3136 -3.4352) (xy -3.3204 -3.4375) (xy -3.3273 -3.4395)
						(xy -3.3343 -3.4411) (xy -3.3415 -3.4424) (xy -3.3487 -3.4433) (xy -3.3561 -3.4439) (xy -3.3636 -3.4441)
						(xy -3.8968 -3.4441) (xy -3.9043 -3.4439) (xy -3.9117 -3.4433) (xy -3.9189 -3.4424) (xy -3.9261 -3.4411)
						(xy -3.9331 -3.4395) (xy -3.94 -3.4375) (xy -3.9468 -3.4352) (xy -3.9534 -3.4326) (xy -3.9598 -3.4297)
						(xy -3.9661 -3.4265) (xy -3.9722 -3.423) (xy -3.9781 -3.4192) (xy -3.9838 -3.4152) (xy -3.9893 -3.4108)
						(xy -3.9946 -3.4063) (xy -3.9996 -3.4015) (xy -4.0044 -3.3964) (xy -4.009 -3.3911) (xy -4.0133 -3.3856)
						(xy -4.0174 -3.3799) (xy -4.0212 -3.374) (xy -4.0247 -3.3679) (xy -4.0279 -3.3617) (xy -4.0308 -3.3552)
						(xy -4.0334 -3.3486) (xy -4.0357 -3.3419) (xy -4.0376 -3.335) (xy -4.0393 -3.3279) (xy -4.0405 -3.3208)
						(xy -4.0415 -3.3135) (xy -4.042 -3.3061) (xy -4.0422 -3.2986) (xy -4.0422 -2.7654) (xy -4.042 -2.758)
						(xy -4.0415 -2.7506) (xy -4.0405 -2.7433) (xy -4.0393 -2.7361) (xy -4.0376 -2.7291) (xy -4.0357 -2.7222)
						(xy -4.0334 -2.7155) (xy -4.0308 -2.7088) (xy -4.0279 -2.7024) (xy -4.0247 -2.6961) (xy -4.0212 -2.69)
						(xy -4.0174 -2.6841) (xy -4.0133 -2.6784) (xy -4.009 -2.673) (xy -4.0044 -2.6677) (xy -3.9996 -2.6626)
						(xy -3.9946 -2.6578) (xy -3.9893 -2.6532) (xy -3.9838 -2.6489) (xy -3.9781 -2.6449) (xy -3.9722 -2.6411)
						(xy -3.9661 -2.6376) (xy -3.9598 -2.6344) (xy -3.9534 -2.6314) (xy -3.9468 -2.6288) (xy -3.94 -2.6266)
						(xy -3.9331 -2.6246) (xy -3.9261 -2.623) (xy -3.9189 -2.6217) (xy -3.9117 -2.6208) (xy -3.9043 -2.6202)
						(xy -3.8968 -2.62) (xy -3.3636 -2.62) (xy -3.3561 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -2.1496 1.0153) (xy -2.1422 1.0147) (xy -2.135 1.0138) (xy -2.1278 1.0125) (xy -2.1208 1.0109)
						(xy -2.1139 1.0089) (xy -2.1071 1.0067) (xy -2.1005 1.0041) (xy -2.0941 1.0011) (xy -2.0878 0.9979)
						(xy -2.0817 0.9944) (xy -2.0758 0.9906) (xy -2.0701 0.9866) (xy -2.0646 0.9823) (xy -2.0593 0.9777)
						(xy -2.0543 0.9729) (xy -2.0495 0.9678) (xy -2.0449 0.9626) (xy -2.0406 0.9571) (xy -2.0365 0.9514)
						(xy -2.0327 0.9455) (xy -2.0292 0.9394) (xy -2.026 0.9331) (xy -2.0231 0.9267) (xy -2.0205 0.9201)
						(xy -2.0182 0.9133) (xy -2.0163 0.9064) (xy -2.0146 0.8994) (xy -2.0133 0.8922) (xy -2.0124 0.8849)
						(xy -2.0119 0.8776) (xy -2.0117 0.8701) (xy -2.0117 0.3369) (xy -2.0119 0.3294) (xy -2.0124 0.322)
						(xy -2.0133 0.3147) (xy -2.0146 0.3076) (xy -2.0163 0.3005) (xy -2.0182 0.2936) (xy -2.0205 0.2869)
						(xy -2.0231 0.2803) (xy -2.026 0.2738) (xy -2.0292 0.2676) (xy -2.0327 0.2615) (xy -2.0365 0.2556)
						(xy -2.0406 0.2499) (xy -2.0449 0.2444) (xy -2.0495 0.2391) (xy -2.0543 0.2341) (xy -2.0593 0.2292)
						(xy -2.0646 0.2247) (xy -2.0701 0.2203) (xy -2.0758 0.2163) (xy -2.0817 0.2125) (xy -2.0878 0.209)
						(xy -2.0941 0.2058) (xy -2.1005 0.2029) (xy -2.1071 0.2003) (xy -2.1139 0.198) (xy -2.1208 0.196)
						(xy -2.1278 0.1944) (xy -2.135 0.1931) (xy -2.1422 0.1922) (xy -2.1496 0.1916) (xy -2.1571 0.1914)
						(xy -2.6903 0.1914) (xy -2.6978 0.1916) (xy -2.7052 0.1922) (xy -2.7124 0.1931) (xy -2.7196 0.1944)
						(xy -2.7266 0.196) (xy -2.7335 0.198) (xy -2.7403 0.2003) (xy -2.7469 0.2029) (xy -2.7533 0.2058)
						(xy -2.7596 0.209) (xy -2.7657 0.2125) (xy -2.7716 0.2163) (xy -2.7773 0.2203) (xy -2.7828 0.2247)
						(xy -2.7881 0.2292) (xy -2.7931 0.2341) (xy -2.7979 0.2391) (xy -2.8025 0.2444) (xy -2.8068 0.2499)
						(xy -2.8109 0.2556) (xy -2.8147 0.2615) (xy -2.8182 0.2676) (xy -2.8214 0.2738) (xy -2.8243 0.2803)
						(xy -2.8269 0.2869) (xy -2.8292 0.2936) (xy -2.8311 0.3005) (xy -2.8328 0.3076) (xy -2.834 0.3147)
						(xy -2.835 0.322) (xy -2.8355 0.3294) (xy -2.8357 0.3369) (xy -2.8357 0.8701) (xy -2.8355 0.8776)
						(xy -2.835 0.8849) (xy -2.834 0.8922) (xy -2.8328 0.8994) (xy -2.8311 0.9064) (xy -2.8292 0.9133)
						(xy -2.8269 0.9201) (xy -2.8243 0.9267) (xy -2.8214 0.9331) (xy -2.8182 0.9394) (xy -2.8147 0.9455)
						(xy -2.8109 0.9514) (xy -2.8068 0.9571) (xy -2.8025 0.9626) (xy -2.7979 0.9678) (xy -2.7931 0.9729)
						(xy -2.7881 0.9777) (xy -2.7828 0.9823) (xy -2.7773 0.9866) (xy -2.7716 0.9906) (xy -2.7657 0.9944)
						(xy -2.7596 0.9979) (xy -2.7533 1.0011) (xy -2.7469 1.0041) (xy -2.7403 1.0067) (xy -2.7335 1.0089)
						(xy -2.7266 1.0109) (xy -2.7196 1.0125) (xy -2.7124 1.0138) (xy -2.7052 1.0147) (xy -2.6978 1.0153)
						(xy -2.6903 1.0155) (xy -2.1571 1.0155) (xy -2.1496 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -2.1496 -0.1965) (xy -2.1422 -0.1971) (xy -2.135 -0.198) (xy -2.1278 -0.1993) (xy -2.1208 -0.2009)
						(xy -2.1139 -0.2029) (xy -2.1071 -0.2052) (xy -2.1005 -0.2078) (xy -2.0941 -0.2107) (xy -2.0878 -0.2139)
						(xy -2.0817 -0.2174) (xy -2.0758 -0.2212) (xy -2.0701 -0.2253) (xy -2.0646 -0.2296) (xy -2.0593 -0.2341)
						(xy -2.0543 -0.239) (xy -2.0495 -0.244) (xy -2.0449 -0.2493) (xy -2.0406 -0.2548) (xy -2.0365 -0.2605)
						(xy -2.0327 -0.2664) (xy -2.0292 -0.2725) (xy -2.026 -0.2787) (xy -2.0231 -0.2852) (xy -2.0205 -0.2918)
						(xy -2.0182 -0.2985) (xy -2.0163 -0.3054) (xy -2.0146 -0.3125) (xy -2.0133 -0.3196) (xy -2.0124 -0.3269)
						(xy -2.0119 -0.3343) (xy -2.0117 -0.3418) (xy -2.0117 -0.875) (xy -2.0119 -0.8825) (xy -2.0124 -0.8898)
						(xy -2.0133 -0.8971) (xy -2.0146 -0.9043) (xy -2.0163 -0.9113) (xy -2.0182 -0.9182) (xy -2.0205 -0.925)
						(xy -2.0231 -0.9316) (xy -2.026 -0.938) (xy -2.0292 -0.9443) (xy -2.0327 -0.9504) (xy -2.0365 -0.9563)
						(xy -2.0406 -0.962) (xy -2.0449 -0.9675) (xy -2.0495 -0.9727) (xy -2.0543 -0.9778) (xy -2.0593 -0.9826)
						(xy -2.0646 -0.9872) (xy -2.0701 -0.9915) (xy -2.0758 -0.9956) (xy -2.0817 -0.9993) (xy -2.0878 -1.0028)
						(xy -2.0941 -1.006) (xy -2.1005 -1.009) (xy -2.1071 -1.0116) (xy -2.1139 -1.0139) (xy -2.1208 -1.0158)
						(xy -2.1278 -1.0174) (xy -2.135 -1.0187) (xy -2.1422 -1.0196) (xy -2.1496 -1.0202) (xy -2.1571 -1.0204)
						(xy -2.6903 -1.0204) (xy -2.6978 -1.0202) (xy -2.7052 -1.0196) (xy -2.7124 -1.0187) (xy -2.7196 -1.0174)
						(xy -2.7266 -1.0158) (xy -2.7335 -1.0139) (xy -2.7403 -1.0116) (xy -2.7469 -1.009) (xy -2.7533 -1.006)
						(xy -2.7596 -1.0028) (xy -2.7657 -0.9993) (xy -2.7716 -0.9956) (xy -2.7773 -0.9915) (xy -2.7828 -0.9872)
						(xy -2.7881 -0.9826) (xy -2.7931 -0.9778) (xy -2.7979 -0.9727) (xy -2.8025 -0.9675) (xy -2.8068 -0.962)
						(xy -2.8109 -0.9563) (xy -2.8147 -0.9504) (xy -2.8182 -0.9443) (xy -2.8214 -0.938) (xy -2.8243 -0.9316)
						(xy -2.8269 -0.925) (xy -2.8292 -0.9182) (xy -2.8311 -0.9113) (xy -2.8328 -0.9043) (xy -2.834 -0.8971)
						(xy -2.835 -0.8898) (xy -2.8355 -0.8825) (xy -2.8357 -0.875) (xy -2.8357 -0.3418) (xy -2.8355 -0.3343)
						(xy -2.835 -0.3269) (xy -2.834 -0.3196) (xy -2.8328 -0.3125) (xy -2.8311 -0.3054) (xy -2.8292 -0.2985)
						(xy -2.8269 -0.2918) (xy -2.8243 -0.2852) (xy -2.8214 -0.2787) (xy -2.8182 -0.2725) (xy -2.8147 -0.2664)
						(xy -2.8109 -0.2605) (xy -2.8068 -0.2548) (xy -2.8025 -0.2493) (xy -2.7979 -0.244) (xy -2.7931 -0.239)
						(xy -2.7881 -0.2341) (xy -2.7828 -0.2296) (xy -2.7773 -0.2253) (xy -2.7716 -0.2212) (xy -2.7657 -0.2174)
						(xy -2.7596 -0.2139) (xy -2.7533 -0.2107) (xy -2.7469 -0.2078) (xy -2.7403 -0.2052) (xy -2.7335 -0.2029)
						(xy -2.7266 -0.2009) (xy -2.7196 -0.1993) (xy -2.7124 -0.198) (xy -2.7052 -0.1971) (xy -2.6978 -0.1965)
						(xy -2.6903 -0.1964) (xy -2.1571 -0.1964) (xy -2.1496 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -2.1496 -1.4084) (xy -2.1422 -1.4089) (xy -2.135 -1.4099) (xy -2.1278 -1.4111) (xy -2.1208 -1.4128)
						(xy -2.1139 -1.4147) (xy -2.1071 -1.417) (xy -2.1005 -1.4196) (xy -2.0941 -1.4225) (xy -2.0878 -1.4257)
						(xy -2.0817 -1.4292) (xy -2.0758 -1.433) (xy -2.0701 -1.4371) (xy -2.0646 -1.4414) (xy -2.0593 -1.446)
						(xy -2.0543 -1.4508) (xy -2.0495 -1.4558) (xy -2.0449 -1.4611) (xy -2.0406 -1.4666) (xy -2.0365 -1.4723)
						(xy -2.0327 -1.4782) (xy -2.0292 -1.4843) (xy -2.026 -1.4906) (xy -2.0231 -1.497) (xy -2.0205 -1.5036)
						(xy -2.0182 -1.5104) (xy -2.0163 -1.5173) (xy -2.0146 -1.5243) (xy -2.0133 -1.5315) (xy -2.0124 -1.5387)
						(xy -2.0119 -1.5461) (xy -2.0117 -1.5536) (xy -2.0117 -2.0868) (xy -2.0119 -2.0943) (xy -2.0124 -2.1017)
						(xy -2.0133 -2.1089) (xy -2.0146 -2.1161) (xy -2.0163 -2.1231) (xy -2.0182 -2.13) (xy -2.0205 -2.1368)
						(xy -2.0231 -2.1434) (xy -2.026 -2.1498) (xy -2.0292 -2.1561) (xy -2.0327 -2.1622) (xy -2.0365 -2.1681)
						(xy -2.0406 -2.1738) (xy -2.0449 -2.1793) (xy -2.0495 -2.1846) (xy -2.0543 -2.1896) (xy -2.0593 -2.1944)
						(xy -2.0646 -2.199) (xy -2.0701 -2.2033) (xy -2.0758 -2.2074) (xy -2.0817 -2.2112) (xy -2.0878 -2.2147)
						(xy -2.0941 -2.2179) (xy -2.1005 -2.2208) (xy -2.1071 -2.2234) (xy -2.1139 -2.2257) (xy -2.1208 -2.2276)
						(xy -2.1278 -2.2293) (xy -2.135 -2.2306) (xy -2.1422 -2.2315) (xy -2.1496 -2.232) (xy -2.1571 -2.2322)
						(xy -2.6903 -2.2322) (xy -2.6978 -2.232) (xy -2.7052 -2.2315) (xy -2.7124 -2.2306) (xy -2.7196 -2.2293)
						(xy -2.7266 -2.2276) (xy -2.7335 -2.2257) (xy -2.7403 -2.2234) (xy -2.7469 -2.2208) (xy -2.7533 -2.2179)
						(xy -2.7596 -2.2147) (xy -2.7657 -2.2112) (xy -2.7716 -2.2074) (xy -2.7773 -2.2033) (xy -2.7828 -2.199)
						(xy -2.7881 -2.1944) (xy -2.7931 -2.1896) (xy -2.7979 -2.1846) (xy -2.8025 -2.1793) (xy -2.8068 -2.1738)
						(xy -2.8109 -2.1681) (xy -2.8147 -2.1622) (xy -2.8182 -2.1561) (xy -2.8214 -2.1498) (xy -2.8243 -2.1434)
						(xy -2.8269 -2.1368) (xy -2.8292 -2.13) (xy -2.8311 -2.1231) (xy -2.8328 -2.1161) (xy -2.834 -2.1089)
						(xy -2.835 -2.1017) (xy -2.8355 -2.0943) (xy -2.8357 -2.0868) (xy -2.8357 -1.5536) (xy -2.8355 -1.5461)
						(xy -2.835 -1.5387) (xy -2.834 -1.5315) (xy -2.8328 -1.5243) (xy -2.8311 -1.5173) (xy -2.8292 -1.5104)
						(xy -2.8269 -1.5036) (xy -2.8243 -1.497) (xy -2.8214 -1.4906) (xy -2.8182 -1.4843) (xy -2.8147 -1.4782)
						(xy -2.8109 -1.4723) (xy -2.8068 -1.4666) (xy -2.8025 -1.4611) (xy -2.7979 -1.4558) (xy -2.7931 -1.4508)
						(xy -2.7881 -1.446) (xy -2.7828 -1.4414) (xy -2.7773 -1.4371) (xy -2.7716 -1.433) (xy -2.7657 -1.4292)
						(xy -2.7596 -1.4257) (xy -2.7533 -1.4225) (xy -2.7469 -1.4196) (xy -2.7403 -1.417) (xy -2.7335 -1.4147)
						(xy -2.7266 -1.4128) (xy -2.7196 -1.4111) (xy -2.7124 -1.4099) (xy -2.7052 -1.4089) (xy -2.6978 -1.4084)
						(xy -2.6903 -1.4082) (xy -2.1571 -1.4082) (xy -2.1496 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -0.9378 1.0153) (xy -0.9304 1.0147) (xy -0.9231 1.0138) (xy -0.916 1.0125) (xy -0.9089 1.0109)
						(xy -0.902 1.0089) (xy -0.8953 1.0067) (xy -0.8887 1.0041) (xy -0.8822 1.0011) (xy -0.876 0.9979)
						(xy -0.8699 0.9944) (xy -0.864 0.9906) (xy -0.8583 0.9866) (xy -0.8528 0.9823) (xy -0.8475 0.9777)
						(xy -0.8424 0.9729) (xy -0.8376 0.9678) (xy -0.8331 0.9626) (xy -0.8287 0.9571) (xy -0.8247 0.9514)
						(xy -0.8209 0.9455) (xy -0.8174 0.9394) (xy -0.8142 0.9331) (xy -0.8113 0.9267) (xy -0.8087 0.9201)
						(xy -0.8064 0.9133) (xy -0.8044 0.9064) (xy -0.8028 0.8994) (xy -0.8015 0.8922) (xy -0.8006 0.8849)
						(xy -0.8 0.8776) (xy -0.7998 0.8701) (xy -0.7998 0.3369) (xy -0.8 0.3294) (xy -0.8006 0.322) (xy -0.8015 0.3147)
						(xy -0.8028 0.3076) (xy -0.8044 0.3005) (xy -0.8064 0.2936) (xy -0.8087 0.2869) (xy -0.8113 0.2803)
						(xy -0.8142 0.2738) (xy -0.8174 0.2676) (xy -0.8209 0.2615) (xy -0.8247 0.2556) (xy -0.8287 0.2499)
						(xy -0.8331 0.2444) (xy -0.8376 0.2391) (xy -0.8424 0.2341) (xy -0.8475 0.2292) (xy -0.8528 0.2247)
						(xy -0.8583 0.2203) (xy -0.864 0.2163) (xy -0.8699 0.2125) (xy -0.876 0.209) (xy -0.8822 0.2058)
						(xy -0.8887 0.2029) (xy -0.8953 0.2003) (xy -0.902 0.198) (xy -0.9089 0.196) (xy -0.916 0.1944)
						(xy -0.9231 0.1931) (xy -0.9304 0.1922) (xy -0.9378 0.1916) (xy -0.9453 0.1914) (xy -1.4785 0.1914)
						(xy -1.4859 0.1916) (xy -1.4933 0.1922) (xy -1.5006 0.1931) (xy -1.5078 0.1944) (xy -1.5148 0.196)
						(xy -1.5217 0.198) (xy -1.5284 0.2003) (xy -1.5351 0.2029) (xy -1.5415 0.2058) (xy -1.5478 0.209)
						(xy -1.5539 0.2125) (xy -1.5598 0.2163) (xy -1.5655 0.2203) (xy -1.5709 0.2247) (xy -1.5762 0.2292)
						(xy -1.5813 0.2341) (xy -1.5861 0.2391) (xy -1.5907 0.2444) (xy -1.595 0.2499) (xy -1.599 0.2556)
						(xy -1.6028 0.2615) (xy -1.6063 0.2676) (xy -1.6095 0.2738) (xy -1.6125 0.2803) (xy -1.6151 0.2869)
						(xy -1.6173 0.2936) (xy -1.6193 0.3005) (xy -1.6209 0.3076) (xy -1.6222 0.3147) (xy -1.6231 0.322)
						(xy -1.6237 0.3294) (xy -1.6239 0.3369) (xy -1.6239 0.8701) (xy -1.6237 0.8776) (xy -1.6231 0.8849)
						(xy -1.6222 0.8922) (xy -1.6209 0.8994) (xy -1.6193 0.9064) (xy -1.6173 0.9133) (xy -1.6151 0.9201)
						(xy -1.6125 0.9267) (xy -1.6095 0.9331) (xy -1.6063 0.9394) (xy -1.6028 0.9455) (xy -1.599 0.9514)
						(xy -1.595 0.9571) (xy -1.5907 0.9626) (xy -1.5861 0.9678) (xy -1.5813 0.9729) (xy -1.5762 0.9777)
						(xy -1.5709 0.9823) (xy -1.5655 0.9866) (xy -1.5598 0.9906) (xy -1.5539 0.9944) (xy -1.5478 0.9979)
						(xy -1.5415 1.0011) (xy -1.5351 1.0041) (xy -1.5284 1.0067) (xy -1.5217 1.0089) (xy -1.5148 1.0109)
						(xy -1.5078 1.0125) (xy -1.5006 1.0138) (xy -1.4933 1.0147) (xy -1.4859 1.0153) (xy -1.4785 1.0155)
						(xy -0.9453 1.0155) (xy -0.9378 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -0.9378 -0.1965) (xy -0.9304 -0.1971) (xy -0.9231 -0.198) (xy -0.916 -0.1993) (xy -0.9089 -0.2009)
						(xy -0.902 -0.2029) (xy -0.8953 -0.2052) (xy -0.8887 -0.2078) (xy -0.8822 -0.2107) (xy -0.876 -0.2139)
						(xy -0.8699 -0.2174) (xy -0.864 -0.2212) (xy -0.8583 -0.2253) (xy -0.8528 -0.2296) (xy -0.8475 -0.2341)
						(xy -0.8424 -0.239) (xy -0.8376 -0.244) (xy -0.8331 -0.2493) (xy -0.8287 -0.2548) (xy -0.8247 -0.2605)
						(xy -0.8209 -0.2664) (xy -0.8174 -0.2725) (xy -0.8142 -0.2787) (xy -0.8113 -0.2852) (xy -0.8087 -0.2918)
						(xy -0.8064 -0.2985) (xy -0.8044 -0.3054) (xy -0.8028 -0.3125) (xy -0.8015 -0.3196) (xy -0.8006 -0.3269)
						(xy -0.8 -0.3343) (xy -0.7998 -0.3418) (xy -0.7998 -0.875) (xy -0.8 -0.8825) (xy -0.8006 -0.8898)
						(xy -0.8015 -0.8971) (xy -0.8028 -0.9043) (xy -0.8044 -0.9113) (xy -0.8064 -0.9182) (xy -0.8087 -0.925)
						(xy -0.8113 -0.9316) (xy -0.8142 -0.938) (xy -0.8174 -0.9443) (xy -0.8209 -0.9504) (xy -0.8247 -0.9563)
						(xy -0.8287 -0.962) (xy -0.8331 -0.9675) (xy -0.8376 -0.9727) (xy -0.8424 -0.9778) (xy -0.8475 -0.9826)
						(xy -0.8528 -0.9872) (xy -0.8583 -0.9915) (xy -0.864 -0.9956) (xy -0.8699 -0.9993) (xy -0.876 -1.0028)
						(xy -0.8822 -1.006) (xy -0.8887 -1.009) (xy -0.8953 -1.0116) (xy -0.902 -1.0139) (xy -0.9089 -1.0158)
						(xy -0.916 -1.0174) (xy -0.9231 -1.0187) (xy -0.9304 -1.0196) (xy -0.9378 -1.0202) (xy -0.9453 -1.0204)
						(xy -1.4785 -1.0204) (xy -1.4859 -1.0202) (xy -1.4933 -1.0196) (xy -1.5006 -1.0187) (xy -1.5078 -1.0174)
						(xy -1.5148 -1.0158) (xy -1.5217 -1.0139) (xy -1.5284 -1.0116) (xy -1.5351 -1.009) (xy -1.5415 -1.006)
						(xy -1.5478 -1.0028) (xy -1.5539 -0.9993) (xy -1.5598 -0.9956) (xy -1.5655 -0.9915) (xy -1.5709 -0.9872)
						(xy -1.5762 -0.9826) (xy -1.5813 -0.9778) (xy -1.5861 -0.9727) (xy -1.5907 -0.9675) (xy -1.595 -0.962)
						(xy -1.599 -0.9563) (xy -1.6028 -0.9504) (xy -1.6063 -0.9443) (xy -1.6095 -0.938) (xy -1.6125 -0.9316)
						(xy -1.6151 -0.925) (xy -1.6173 -0.9182) (xy -1.6193 -0.9113) (xy -1.6209 -0.9043) (xy -1.6222 -0.8971)
						(xy -1.6231 -0.8898) (xy -1.6237 -0.8825) (xy -1.6239 -0.875) (xy -1.6239 -0.3418) (xy -1.6237 -0.3343)
						(xy -1.6231 -0.3269) (xy -1.6222 -0.3196) (xy -1.6209 -0.3125) (xy -1.6193 -0.3054) (xy -1.6173 -0.2985)
						(xy -1.6151 -0.2918) (xy -1.6125 -0.2852) (xy -1.6095 -0.2787) (xy -1.6063 -0.2725) (xy -1.6028 -0.2664)
						(xy -1.599 -0.2605) (xy -1.595 -0.2548) (xy -1.5907 -0.2493) (xy -1.5861 -0.244) (xy -1.5813 -0.239)
						(xy -1.5762 -0.2341) (xy -1.5709 -0.2296) (xy -1.5655 -0.2253) (xy -1.5598 -0.2212) (xy -1.5539 -0.2174)
						(xy -1.5478 -0.2139) (xy -1.5415 -0.2107) (xy -1.5351 -0.2078) (xy -1.5284 -0.2052) (xy -1.5217 -0.2029)
						(xy -1.5148 -0.2009) (xy -1.5078 -0.1993) (xy -1.5006 -0.198) (xy -1.4933 -0.1971) (xy -1.4859 -0.1965)
						(xy -1.4785 -0.1964) (xy -0.9453 -0.1964) (xy -0.9378 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy -0.9378 -1.4084) (xy -0.9304 -1.4089) (xy -0.9231 -1.4099) (xy -0.916 -1.4111) (xy -0.9089 -1.4128)
						(xy -0.902 -1.4147) (xy -0.8953 -1.417) (xy -0.8887 -1.4196) (xy -0.8822 -1.4225) (xy -0.876 -1.4257)
						(xy -0.8699 -1.4292) (xy -0.864 -1.433) (xy -0.8583 -1.4371) (xy -0.8528 -1.4414) (xy -0.8475 -1.446)
						(xy -0.8424 -1.4508) (xy -0.8376 -1.4558) (xy -0.8331 -1.4611) (xy -0.8287 -1.4666) (xy -0.8247 -1.4723)
						(xy -0.8209 -1.4782) (xy -0.8174 -1.4843) (xy -0.8142 -1.4906) (xy -0.8113 -1.497) (xy -0.8087 -1.5036)
						(xy -0.8064 -1.5104) (xy -0.8044 -1.5173) (xy -0.8028 -1.5243) (xy -0.8015 -1.5315) (xy -0.8006 -1.5387)
						(xy -0.8 -1.5461) (xy -0.7998 -1.5536) (xy -0.7998 -2.0868) (xy -0.8 -2.0943) (xy -0.8006 -2.1017)
						(xy -0.8015 -2.1089) (xy -0.8028 -2.1161) (xy -0.8044 -2.1231) (xy -0.8064 -2.13) (xy -0.8087 -2.1368)
						(xy -0.8113 -2.1434) (xy -0.8142 -2.1498) (xy -0.8174 -2.1561) (xy -0.8209 -2.1622) (xy -0.8247 -2.1681)
						(xy -0.8287 -2.1738) (xy -0.8331 -2.1793) (xy -0.8376 -2.1846) (xy -0.8424 -2.1896) (xy -0.8475 -2.1944)
						(xy -0.8528 -2.199) (xy -0.8583 -2.2033) (xy -0.864 -2.2074) (xy -0.8699 -2.2112) (xy -0.876 -2.2147)
						(xy -0.8822 -2.2179) (xy -0.8887 -2.2208) (xy -0.8953 -2.2234) (xy -0.902 -2.2257) (xy -0.9089 -2.2276)
						(xy -0.916 -2.2293) (xy -0.9231 -2.2306) (xy -0.9304 -2.2315) (xy -0.9378 -2.232) (xy -0.9453 -2.2322)
						(xy -1.4785 -2.2322) (xy -1.4859 -2.232) (xy -1.4933 -2.2315) (xy -1.5006 -2.2306) (xy -1.5078 -2.2293)
						(xy -1.5148 -2.2276) (xy -1.5217 -2.2257) (xy -1.5284 -2.2234) (xy -1.5351 -2.2208) (xy -1.5415 -2.2179)
						(xy -1.5478 -2.2147) (xy -1.5539 -2.2112) (xy -1.5598 -2.2074) (xy -1.5655 -2.2033) (xy -1.5709 -2.199)
						(xy -1.5762 -2.1944) (xy -1.5813 -2.1896) (xy -1.5861 -2.1846) (xy -1.5907 -2.1793) (xy -1.595 -2.1738)
						(xy -1.599 -2.1681) (xy -1.6028 -2.1622) (xy -1.6063 -2.1561) (xy -1.6095 -2.1498) (xy -1.6125 -2.1434)
						(xy -1.6151 -2.1368) (xy -1.6173 -2.13) (xy -1.6193 -2.1231) (xy -1.6209 -2.1161) (xy -1.6222 -2.1089)
						(xy -1.6231 -2.1017) (xy -1.6237 -2.0943) (xy -1.6239 -2.0868) (xy -1.6239 -1.5536) (xy -1.6237 -1.5461)
						(xy -1.6231 -1.5387) (xy -1.6222 -1.5315) (xy -1.6209 -1.5243) (xy -1.6193 -1.5173) (xy -1.6173 -1.5104)
						(xy -1.6151 -1.5036) (xy -1.6125 -1.497) (xy -1.6095 -1.4906) (xy -1.6063 -1.4843) (xy -1.6028 -1.4782)
						(xy -1.599 -1.4723) (xy -1.595 -1.4666) (xy -1.5907 -1.4611) (xy -1.5861 -1.4558) (xy -1.5813 -1.4508)
						(xy -1.5762 -1.446) (xy -1.5709 -1.4414) (xy -1.5655 -1.4371) (xy -1.5598 -1.433) (xy -1.5539 -1.4292)
						(xy -1.5478 -1.4257) (xy -1.5415 -1.4225) (xy -1.5351 -1.4196) (xy -1.5284 -1.417) (xy -1.5217 -1.4147)
						(xy -1.5148 -1.4128) (xy -1.5078 -1.4111) (xy -1.5006 -1.4099) (xy -1.4933 -1.4089) (xy -1.4859 -1.4084)
						(xy -1.4785 -1.4082) (xy -0.9453 -1.4082) (xy -0.9378 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0.2687 1.0153) (xy 0.2761 1.0147) (xy 0.2834 1.0138) (xy 0.2905 1.0125) (xy 0.2976 1.0109)
						(xy 0.3045 1.0089) (xy 0.3112 1.0067) (xy 0.3178 1.0041) (xy 0.3243 1.0011) (xy 0.3305 0.9979)
						(xy 0.3366 0.9944) (xy 0.3425 0.9906) (xy 0.3482 0.9866) (xy 0.3537 0.9823) (xy 0.359 0.9777)
						(xy 0.3641 0.9729) (xy 0.3689 0.9678) (xy 0.3734 0.9626) (xy 0.3778 0.9571) (xy 0.3818 0.9514)
						(xy 0.3856 0.9455) (xy 0.3891 0.9394) (xy 0.3923 0.9331) (xy 0.3952 0.9267) (xy 0.3978 0.9201)
						(xy 0.4001 0.9133) (xy 0.4021 0.9064) (xy 0.4037 0.8994) (xy 0.405 0.8922) (xy 0.4059 0.8849)
						(xy 0.4065 0.8776) (xy 0.4067 0.8701) (xy 0.4067 0.3369) (xy 0.4065 0.3294) (xy 0.4059 0.322)
						(xy 0.405 0.3147) (xy 0.4037 0.3076) (xy 0.4021 0.3005) (xy 0.4001 0.2936) (xy 0.3978 0.2869)
						(xy 0.3952 0.2803) (xy 0.3923 0.2738) (xy 0.3891 0.2676) (xy 0.3856 0.2615) (xy 0.3818 0.2556)
						(xy 0.3778 0.2499) (xy 0.3734 0.2444) (xy 0.3689 0.2391) (xy 0.3641 0.2341) (xy 0.359 0.2292)
						(xy 0.3537 0.2247) (xy 0.3482 0.2203) (xy 0.3425 0.2163) (xy 0.3366 0.2125) (xy 0.3305 0.209)
						(xy 0.3243 0.2058) (xy 0.3178 0.2029) (xy 0.3112 0.2003) (xy 0.3045 0.198) (xy 0.2976 0.196) (xy 0.2905 0.1944)
						(xy 0.2834 0.1931) (xy 0.2761 0.1922) (xy 0.2687 0.1916) (xy 0.2612 0.1914) (xy -0.272 0.1914)
						(xy -0.2794 0.1916) (xy -0.2868 0.1922) (xy -0.2941 0.1931) (xy -0.3013 0.1944) (xy -0.3083 0.196)
						(xy -0.3152 0.198) (xy -0.3219 0.2003) (xy -0.3286 0.2029) (xy -0.335 0.2058) (xy -0.3413 0.209)
						(xy -0.3474 0.2125) (xy -0.3533 0.2163) (xy -0.359 0.2203) (xy -0.3644 0.2247) (xy -0.3697 0.2292)
						(xy -0.3748 0.2341) (xy -0.3796 0.2391) (xy -0.3842 0.2444) (xy -0.3885 0.2499) (xy -0.3925 0.2556)
						(xy -0.3963 0.2615) (xy -0.3998 0.2676) (xy -0.403 0.2738) (xy -0.406 0.2803) (xy -0.4086 0.2869)
						(xy -0.4108 0.2936) (xy -0.4128 0.3005) (xy -0.4144 0.3076) (xy -0.4157 0.3147) (xy -0.4166 0.322)
						(xy -0.4172 0.3294) (xy -0.4174 0.3369) (xy -0.4174 0.8701) (xy -0.4172 0.8776) (xy -0.4166 0.8849)
						(xy -0.4157 0.8922) (xy -0.4144 0.8994) (xy -0.4128 0.9064) (xy -0.4108 0.9133) (xy -0.4086 0.9201)
						(xy -0.406 0.9267) (xy -0.403 0.9331) (xy -0.3998 0.9394) (xy -0.3963 0.9455) (xy -0.3925 0.9514)
						(xy -0.3885 0.9571) (xy -0.3842 0.9626) (xy -0.3796 0.9678) (xy -0.3748 0.9729) (xy -0.3697 0.9777)
						(xy -0.3644 0.9823) (xy -0.359 0.9866) (xy -0.3533 0.9906) (xy -0.3474 0.9944) (xy -0.3413 0.9979)
						(xy -0.335 1.0011) (xy -0.3286 1.0041) (xy -0.3219 1.0067) (xy -0.3152 1.0089) (xy -0.3083 1.0109)
						(xy -0.3013 1.0125) (xy -0.2941 1.0138) (xy -0.2868 1.0147) (xy -0.2794 1.0153) (xy -0.272 1.0155)
						(xy 0.2612 1.0155) (xy 0.2687 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0.2687 -0.1965) (xy 0.2761 -0.1971) (xy 0.2834 -0.198) (xy 0.2905 -0.1993) (xy 0.2976 -0.2009)
						(xy 0.3045 -0.2029) (xy 0.3112 -0.2052) (xy 0.3178 -0.2078) (xy 0.3243 -0.2107) (xy 0.3305 -0.2139)
						(xy 0.3366 -0.2174) (xy 0.3425 -0.2212) (xy 0.3482 -0.2253) (xy 0.3537 -0.2296) (xy 0.359 -0.2341)
						(xy 0.3641 -0.239) (xy 0.3689 -0.244) (xy 0.3734 -0.2493) (xy 0.3778 -0.2548) (xy 0.3818 -0.2605)
						(xy 0.3856 -0.2664) (xy 0.3891 -0.2725) (xy 0.3923 -0.2787) (xy 0.3952 -0.2852) (xy 0.3978 -0.2918)
						(xy 0.4001 -0.2985) (xy 0.4021 -0.3054) (xy 0.4037 -0.3125) (xy 0.405 -0.3196) (xy 0.4059 -0.3269)
						(xy 0.4065 -0.3343) (xy 0.4067 -0.3418) (xy 0.4067 -0.875) (xy 0.4065 -0.8825) (xy 0.4059 -0.8898)
						(xy 0.405 -0.8971) (xy 0.4037 -0.9043) (xy 0.4021 -0.9113) (xy 0.4001 -0.9182) (xy 0.3978 -0.925)
						(xy 0.3952 -0.9316) (xy 0.3923 -0.938) (xy 0.3891 -0.9443) (xy 0.3856 -0.9504) (xy 0.3818 -0.9563)
						(xy 0.3778 -0.962) (xy 0.3734 -0.9675) (xy 0.3689 -0.9727) (xy 0.3641 -0.9778) (xy 0.359 -0.9826)
						(xy 0.3537 -0.9872) (xy 0.3482 -0.9915) (xy 0.3425 -0.9956) (xy 0.3366 -0.9993) (xy 0.3305 -1.0028)
						(xy 0.3243 -1.006) (xy 0.3178 -1.009) (xy 0.3112 -1.0116) (xy 0.3045 -1.0139) (xy 0.2976 -1.0158)
						(xy 0.2905 -1.0174) (xy 0.2834 -1.0187) (xy 0.2761 -1.0196) (xy 0.2687 -1.0202) (xy 0.2612 -1.0204)
						(xy -0.272 -1.0204) (xy -0.2794 -1.0202) (xy -0.2868 -1.0196) (xy -0.2941 -1.0187) (xy -0.3013 -1.0174)
						(xy -0.3083 -1.0158) (xy -0.3152 -1.0139) (xy -0.3219 -1.0116) (xy -0.3286 -1.009) (xy -0.335 -1.006)
						(xy -0.3413 -1.0028) (xy -0.3474 -0.9993) (xy -0.3533 -0.9956) (xy -0.359 -0.9915) (xy -0.3644 -0.9872)
						(xy -0.3697 -0.9826) (xy -0.3748 -0.9778) (xy -0.3796 -0.9727) (xy -0.3842 -0.9675) (xy -0.3885 -0.962)
						(xy -0.3925 -0.9563) (xy -0.3963 -0.9504) (xy -0.3998 -0.9443) (xy -0.403 -0.938) (xy -0.406 -0.9316)
						(xy -0.4086 -0.925) (xy -0.4108 -0.9182) (xy -0.4128 -0.9113) (xy -0.4144 -0.9043) (xy -0.4157 -0.8971)
						(xy -0.4166 -0.8898) (xy -0.4172 -0.8825) (xy -0.4174 -0.875) (xy -0.4174 -0.3418) (xy -0.4172 -0.3343)
						(xy -0.4166 -0.3269) (xy -0.4157 -0.3196) (xy -0.4144 -0.3125) (xy -0.4128 -0.3054) (xy -0.4108 -0.2985)
						(xy -0.4086 -0.2918) (xy -0.406 -0.2852) (xy -0.403 -0.2787) (xy -0.3998 -0.2725) (xy -0.3963 -0.2664)
						(xy -0.3925 -0.2605) (xy -0.3885 -0.2548) (xy -0.3842 -0.2493) (xy -0.3796 -0.244) (xy -0.3748 -0.239)
						(xy -0.3697 -0.2341) (xy -0.3644 -0.2296) (xy -0.359 -0.2253) (xy -0.3533 -0.2212) (xy -0.3474 -0.2174)
						(xy -0.3413 -0.2139) (xy -0.335 -0.2107) (xy -0.3286 -0.2078) (xy -0.3219 -0.2052) (xy -0.3152 -0.2029)
						(xy -0.3083 -0.2009) (xy -0.3013 -0.1993) (xy -0.2941 -0.198) (xy -0.2868 -0.1971) (xy -0.2794 -0.1965)
						(xy -0.272 -0.1964) (xy 0.2612 -0.1964) (xy 0.2687 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 0.2687 -1.4084) (xy 0.2761 -1.4089) (xy 0.2834 -1.4099) (xy 0.2905 -1.4111) (xy 0.2976 -1.4128)
						(xy 0.3045 -1.4147) (xy 0.3112 -1.417) (xy 0.3178 -1.4196) (xy 0.3243 -1.4225) (xy 0.3305 -1.4257)
						(xy 0.3366 -1.4292) (xy 0.3425 -1.433) (xy 0.3482 -1.4371) (xy 0.3537 -1.4414) (xy 0.359 -1.446)
						(xy 0.3641 -1.4508) (xy 0.3689 -1.4558) (xy 0.3734 -1.4611) (xy 0.3778 -1.4666) (xy 0.3818 -1.4723)
						(xy 0.3856 -1.4782) (xy 0.3891 -1.4843) (xy 0.3923 -1.4906) (xy 0.3952 -1.497) (xy 0.3978 -1.5036)
						(xy 0.4001 -1.5104) (xy 0.4021 -1.5173) (xy 0.4037 -1.5243) (xy 0.405 -1.5315) (xy 0.4059 -1.5387)
						(xy 0.4065 -1.5461) (xy 0.4067 -1.5536) (xy 0.4067 -2.0868) (xy 0.4065 -2.0943) (xy 0.4059 -2.1017)
						(xy 0.405 -2.1089) (xy 0.4037 -2.1161) (xy 0.4021 -2.1231) (xy 0.4001 -2.13) (xy 0.3978 -2.1368)
						(xy 0.3952 -2.1434) (xy 0.3923 -2.1498) (xy 0.3891 -2.1561) (xy 0.3856 -2.1622) (xy 0.3818 -2.1681)
						(xy 0.3778 -2.1738) (xy 0.3734 -2.1793) (xy 0.3689 -2.1846) (xy 0.3641 -2.1896) (xy 0.359 -2.1944)
						(xy 0.3537 -2.199) (xy 0.3482 -2.2033) (xy 0.3425 -2.2074) (xy 0.3366 -2.2112) (xy 0.3305 -2.2147)
						(xy 0.3243 -2.2179) (xy 0.3178 -2.2208) (xy 0.3112 -2.2234) (xy 0.3045 -2.2257) (xy 0.2976 -2.2276)
						(xy 0.2905 -2.2293) (xy 0.2834 -2.2306) (xy 0.2761 -2.2315) (xy 0.2687 -2.232) (xy 0.2612 -2.2322)
						(xy -0.272 -2.2322) (xy -0.2794 -2.232) (xy -0.2868 -2.2315) (xy -0.2941 -2.2306) (xy -0.3013 -2.2293)
						(xy -0.3083 -2.2276) (xy -0.3152 -2.2257) (xy -0.3219 -2.2234) (xy -0.3286 -2.2208) (xy -0.335 -2.2179)
						(xy -0.3413 -2.2147) (xy -0.3474 -2.2112) (xy -0.3533 -2.2074) (xy -0.359 -2.2033) (xy -0.3644 -2.199)
						(xy -0.3697 -2.1944) (xy -0.3748 -2.1896) (xy -0.3796 -2.1846) (xy -0.3842 -2.1793) (xy -0.3885 -2.1738)
						(xy -0.3925 -2.1681) (xy -0.3963 -2.1622) (xy -0.3998 -2.1561) (xy -0.403 -2.1498) (xy -0.406 -2.1434)
						(xy -0.4086 -2.1368) (xy -0.4108 -2.13) (xy -0.4128 -2.1231) (xy -0.4144 -2.1161) (xy -0.4157 -2.1089)
						(xy -0.4166 -2.1017) (xy -0.4172 -2.0943) (xy -0.4174 -2.0868) (xy -0.4174 -1.5536) (xy -0.4172 -1.5461)
						(xy -0.4166 -1.5387) (xy -0.4157 -1.5315) (xy -0.4144 -1.5243) (xy -0.4128 -1.5173) (xy -0.4108 -1.5104)
						(xy -0.4086 -1.5036) (xy -0.406 -1.497) (xy -0.403 -1.4906) (xy -0.3998 -1.4843) (xy -0.3963 -1.4782)
						(xy -0.3925 -1.4723) (xy -0.3885 -1.4666) (xy -0.3842 -1.4611) (xy -0.3796 -1.4558) (xy -0.3748 -1.4508)
						(xy -0.3697 -1.446) (xy -0.3644 -1.4414) (xy -0.359 -1.4371) (xy -0.3533 -1.433) (xy -0.3474 -1.4292)
						(xy -0.3413 -1.4257) (xy -0.335 -1.4225) (xy -0.3286 -1.4196) (xy -0.3219 -1.417) (xy -0.3152 -1.4147)
						(xy -0.3083 -1.4128) (xy -0.3013 -1.4111) (xy -0.2941 -1.4099) (xy -0.2868 -1.4089) (xy -0.2794 -1.4084)
						(xy -0.272 -1.4082) (xy 0.2612 -1.4082) (xy 0.2687 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 1.4859 1.0153) (xy 1.4933 1.0147) (xy 1.5005 1.0138) (xy 1.5077 1.0125) (xy 1.5147 1.0109)
						(xy 1.5216 1.0089) (xy 1.5284 1.0067) (xy 1.535 1.0041) (xy 1.5414 1.0011) (xy 1.5477 0.9979)
						(xy 1.5538 0.9944) (xy 1.5597 0.9906) (xy 1.5654 0.9866) (xy 1.5709 0.9823) (xy 1.5762 0.9777)
						(xy 1.5812 0.9729) (xy 1.586 0.9678) (xy 1.5906 0.9626) (xy 1.5949 0.9571) (xy 1.599 0.9514) (xy 1.6028 0.9455)
						(xy 1.6063 0.9394) (xy 1.6095 0.9331) (xy 1.6124 0.9267) (xy 1.615 0.9201) (xy 1.6173 0.9133)
						(xy 1.6192 0.9064) (xy 1.6209 0.8994) (xy 1.6221 0.8922) (xy 1.6231 0.8849) (xy 1.6236 0.8776)
						(xy 1.6238 0.8701) (xy 1.6238 0.3369) (xy 1.6236 0.3294) (xy 1.6231 0.322) (xy 1.6221 0.3147)
						(xy 1.6209 0.3076) (xy 1.6192 0.3005) (xy 1.6173 0.2936) (xy 1.615 0.2869) (xy 1.6124 0.2803)
						(xy 1.6095 0.2738) (xy 1.6063 0.2676) (xy 1.6028 0.2615) (xy 1.599 0.2556) (xy 1.5949 0.2499)
						(xy 1.5906 0.2444) (xy 1.586 0.2391) (xy 1.5812 0.2341) (xy 1.5762 0.2292) (xy 1.5709 0.2247)
						(xy 1.5654 0.2203) (xy 1.5597 0.2163) (xy 1.5538 0.2125) (xy 1.5477 0.209) (xy 1.5414 0.2058)
						(xy 1.535 0.2029) (xy 1.5284 0.2003) (xy 1.5216 0.198) (xy 1.5147 0.196) (xy 1.5077 0.1944) (xy 1.5005 0.1931)
						(xy 1.4933 0.1922) (xy 1.4859 0.1916) (xy 1.4784 0.1914) (xy 0.9452 0.1914) (xy 0.9377 0.1916)
						(xy 0.9303 0.1922) (xy 0.9231 0.1931) (xy 0.9159 0.1944) (xy 0.9089 0.196) (xy 0.902 0.198) (xy 0.8952 0.2003)
						(xy 0.8886 0.2029) (xy 0.8822 0.2058) (xy 0.8759 0.209) (xy 0.8698 0.2125) (xy 0.8639 0.2163)
						(xy 0.8582 0.2203) (xy 0.8527 0.2247) (xy 0.8474 0.2292) (xy 0.8424 0.2341) (xy 0.8376 0.2391)
						(xy 0.833 0.2444) (xy 0.8287 0.2499) (xy 0.8246 0.2556) (xy 0.8208 0.2615) (xy 0.8173 0.2676)
						(xy 0.8141 0.2738) (xy 0.8112 0.2803) (xy 0.8086 0.2869) (xy 0.8063 0.2936) (xy 0.8044 0.3005)
						(xy 0.8027 0.3076) (xy 0.8015 0.3147) (xy 0.8005 0.322) (xy 0.8 0.3294) (xy 0.7998 0.3369) (xy 0.7998 0.8701)
						(xy 0.8 0.8776) (xy 0.8005 0.8849) (xy 0.8015 0.8922) (xy 0.8027 0.8994) (xy 0.8044 0.9064) (xy 0.8063 0.9133)
						(xy 0.8086 0.9201) (xy 0.8112 0.9267) (xy 0.8141 0.9331) (xy 0.8173 0.9394) (xy 0.8208 0.9455)
						(xy 0.8246 0.9514) (xy 0.8287 0.9571) (xy 0.833 0.9626) (xy 0.8376 0.9678) (xy 0.8424 0.9729)
						(xy 0.8474 0.9777) (xy 0.8527 0.9823) (xy 0.8582 0.9866) (xy 0.8639 0.9906) (xy 0.8698 0.9944)
						(xy 0.8759 0.9979) (xy 0.8822 1.0011) (xy 0.8886 1.0041) (xy 0.8952 1.0067) (xy 0.902 1.0089)
						(xy 0.9089 1.0109) (xy 0.9159 1.0125) (xy 0.9231 1.0138) (xy 0.9303 1.0147) (xy 0.9377 1.0153)
						(xy 0.9452 1.0155) (xy 1.4784 1.0155) (xy 1.4859 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 1.4859 -0.1965) (xy 1.4933 -0.1971) (xy 1.5005 -0.198) (xy 1.5077 -0.1993) (xy 1.5147 -0.2009)
						(xy 1.5216 -0.2029) (xy 1.5284 -0.2052) (xy 1.535 -0.2078) (xy 1.5414 -0.2107) (xy 1.5477 -0.2139)
						(xy 1.5538 -0.2174) (xy 1.5597 -0.2212) (xy 1.5654 -0.2253) (xy 1.5709 -0.2296) (xy 1.5762 -0.2341)
						(xy 1.5812 -0.239) (xy 1.586 -0.244) (xy 1.5906 -0.2493) (xy 1.5949 -0.2548) (xy 1.599 -0.2605)
						(xy 1.6028 -0.2664) (xy 1.6063 -0.2725) (xy 1.6095 -0.2787) (xy 1.6124 -0.2852) (xy 1.615 -0.2918)
						(xy 1.6173 -0.2985) (xy 1.6192 -0.3054) (xy 1.6209 -0.3125) (xy 1.6221 -0.3196) (xy 1.6231 -0.3269)
						(xy 1.6236 -0.3343) (xy 1.6238 -0.3418) (xy 1.6238 -0.875) (xy 1.6236 -0.8825) (xy 1.6231 -0.8898)
						(xy 1.6221 -0.8971) (xy 1.6209 -0.9043) (xy 1.6192 -0.9113) (xy 1.6173 -0.9182) (xy 1.615 -0.925)
						(xy 1.6124 -0.9316) (xy 1.6095 -0.938) (xy 1.6063 -0.9443) (xy 1.6028 -0.9504) (xy 1.599 -0.9563)
						(xy 1.5949 -0.962) (xy 1.5906 -0.9675) (xy 1.586 -0.9727) (xy 1.5812 -0.9778) (xy 1.5762 -0.9826)
						(xy 1.5709 -0.9872) (xy 1.5654 -0.9915) (xy 1.5597 -0.9956) (xy 1.5538 -0.9993) (xy 1.5477 -1.0028)
						(xy 1.5414 -1.006) (xy 1.535 -1.009) (xy 1.5284 -1.0116) (xy 1.5216 -1.0139) (xy 1.5147 -1.0158)
						(xy 1.5077 -1.0174) (xy 1.5005 -1.0187) (xy 1.4933 -1.0196) (xy 1.4859 -1.0202) (xy 1.4784 -1.0204)
						(xy 0.9452 -1.0204) (xy 0.9377 -1.0202) (xy 0.9303 -1.0196) (xy 0.9231 -1.0187) (xy 0.9159 -1.0174)
						(xy 0.9089 -1.0158) (xy 0.902 -1.0139) (xy 0.8952 -1.0116) (xy 0.8886 -1.009) (xy 0.8822 -1.006)
						(xy 0.8759 -1.0028) (xy 0.8698 -0.9993) (xy 0.8639 -0.9956) (xy 0.8582 -0.9915) (xy 0.8527 -0.9872)
						(xy 0.8474 -0.9826) (xy 0.8424 -0.9778) (xy 0.8376 -0.9727) (xy 0.833 -0.9675) (xy 0.8287 -0.962)
						(xy 0.8246 -0.9563) (xy 0.8208 -0.9504) (xy 0.8173 -0.9443) (xy 0.8141 -0.938) (xy 0.8112 -0.9316)
						(xy 0.8086 -0.925) (xy 0.8063 -0.9182) (xy 0.8044 -0.9113) (xy 0.8027 -0.9043) (xy 0.8015 -0.8971)
						(xy 0.8005 -0.8898) (xy 0.8 -0.8825) (xy 0.7998 -0.875) (xy 0.7998 -0.3418) (xy 0.8 -0.3343) (xy 0.8005 -0.3269)
						(xy 0.8015 -0.3196) (xy 0.8027 -0.3125) (xy 0.8044 -0.3054) (xy 0.8063 -0.2985) (xy 0.8086 -0.2918)
						(xy 0.8112 -0.2852) (xy 0.8141 -0.2787) (xy 0.8173 -0.2725) (xy 0.8208 -0.2664) (xy 0.8246 -0.2605)
						(xy 0.8287 -0.2548) (xy 0.833 -0.2493) (xy 0.8376 -0.244) (xy 0.8424 -0.239) (xy 0.8474 -0.2341)
						(xy 0.8527 -0.2296) (xy 0.8582 -0.2253) (xy 0.8639 -0.2212) (xy 0.8698 -0.2174) (xy 0.8759 -0.2139)
						(xy 0.8822 -0.2107) (xy 0.8886 -0.2078) (xy 0.8952 -0.2052) (xy 0.902 -0.2029) (xy 0.9089 -0.2009)
						(xy 0.9159 -0.1993) (xy 0.9231 -0.198) (xy 0.9303 -0.1971) (xy 0.9377 -0.1965) (xy 0.9452 -0.1964)
						(xy 1.4784 -0.1964) (xy 1.4859 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 1.4859 -1.4084) (xy 1.4933 -1.4089) (xy 1.5005 -1.4099) (xy 1.5077 -1.4111) (xy 1.5147 -1.4128)
						(xy 1.5216 -1.4147) (xy 1.5284 -1.417) (xy 1.535 -1.4196) (xy 1.5414 -1.4225) (xy 1.5477 -1.4257)
						(xy 1.5538 -1.4292) (xy 1.5597 -1.433) (xy 1.5654 -1.4371) (xy 1.5709 -1.4414) (xy 1.5762 -1.446)
						(xy 1.5812 -1.4508) (xy 1.586 -1.4558) (xy 1.5906 -1.4611) (xy 1.5949 -1.4666) (xy 1.599 -1.4723)
						(xy 1.6028 -1.4782) (xy 1.6063 -1.4843) (xy 1.6095 -1.4906) (xy 1.6124 -1.497) (xy 1.615 -1.5036)
						(xy 1.6173 -1.5104) (xy 1.6192 -1.5173) (xy 1.6209 -1.5243) (xy 1.6221 -1.5315) (xy 1.6231 -1.5387)
						(xy 1.6236 -1.5461) (xy 1.6238 -1.5536) (xy 1.6238 -2.0868) (xy 1.6236 -2.0943) (xy 1.6231 -2.1017)
						(xy 1.6221 -2.1089) (xy 1.6209 -2.1161) (xy 1.6192 -2.1231) (xy 1.6173 -2.13) (xy 1.615 -2.1368)
						(xy 1.6124 -2.1434) (xy 1.6095 -2.1498) (xy 1.6063 -2.1561) (xy 1.6028 -2.1622) (xy 1.599 -2.1681)
						(xy 1.5949 -2.1738) (xy 1.5906 -2.1793) (xy 1.586 -2.1846) (xy 1.5812 -2.1896) (xy 1.5762 -2.1944)
						(xy 1.5709 -2.199) (xy 1.5654 -2.2033) (xy 1.5597 -2.2074) (xy 1.5538 -2.2112) (xy 1.5477 -2.2147)
						(xy 1.5414 -2.2179) (xy 1.535 -2.2208) (xy 1.5284 -2.2234) (xy 1.5216 -2.2257) (xy 1.5147 -2.2276)
						(xy 1.5077 -2.2293) (xy 1.5005 -2.2306) (xy 1.4933 -2.2315) (xy 1.4859 -2.232) (xy 1.4784 -2.2322)
						(xy 0.9452 -2.2322) (xy 0.9377 -2.232) (xy 0.9303 -2.2315) (xy 0.9231 -2.2306) (xy 0.9159 -2.2293)
						(xy 0.9089 -2.2276) (xy 0.902 -2.2257) (xy 0.8952 -2.2234) (xy 0.8886 -2.2208) (xy 0.8822 -2.2179)
						(xy 0.8759 -2.2147) (xy 0.8698 -2.2112) (xy 0.8639 -2.2074) (xy 0.8582 -2.2033) (xy 0.8527 -2.199)
						(xy 0.8474 -2.1944) (xy 0.8424 -2.1896) (xy 0.8376 -2.1846) (xy 0.833 -2.1793) (xy 0.8287 -2.1738)
						(xy 0.8246 -2.1681) (xy 0.8208 -2.1622) (xy 0.8173 -2.1561) (xy 0.8141 -2.1498) (xy 0.8112 -2.1434)
						(xy 0.8086 -2.1368) (xy 0.8063 -2.13) (xy 0.8044 -2.1231) (xy 0.8027 -2.1161) (xy 0.8015 -2.1089)
						(xy 0.8005 -2.1017) (xy 0.8 -2.0943) (xy 0.7998 -2.0868) (xy 0.7998 -1.5536) (xy 0.8 -1.5461)
						(xy 0.8005 -1.5387) (xy 0.8015 -1.5315) (xy 0.8027 -1.5243) (xy 0.8044 -1.5173) (xy 0.8063 -1.5104)
						(xy 0.8086 -1.5036) (xy 0.8112 -1.497) (xy 0.8141 -1.4906) (xy 0.8173 -1.4843) (xy 0.8208 -1.4782)
						(xy 0.8246 -1.4723) (xy 0.8287 -1.4666) (xy 0.833 -1.4611) (xy 0.8376 -1.4558) (xy 0.8424 -1.4508)
						(xy 0.8474 -1.446) (xy 0.8527 -1.4414) (xy 0.8582 -1.4371) (xy 0.8639 -1.433) (xy 0.8698 -1.4292)
						(xy 0.8759 -1.4257) (xy 0.8822 -1.4225) (xy 0.8886 -1.4196) (xy 0.8952 -1.417) (xy 0.902 -1.4147)
						(xy 0.9089 -1.4128) (xy 0.9159 -1.4111) (xy 0.9231 -1.4099) (xy 0.9303 -1.4089) (xy 0.9377 -1.4084)
						(xy 0.9452 -1.4082) (xy 1.4784 -1.4082) (xy 1.4859 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6549 -2.6202) (xy 2.6643 -2.6208) (xy 2.6735 -2.6217) (xy 2.6826 -2.623) (xy 2.6916 -2.6246)
						(xy 2.7004 -2.6266) (xy 2.7089 -2.6288) (xy 2.7174 -2.6314) (xy 2.7255 -2.6344) (xy 2.7335 -2.6376)
						(xy 2.7413 -2.6411) (xy 2.7488 -2.6449) (xy 2.756 -2.6489) (xy 2.763 -2.6532) (xy 2.7697 -2.6578)
						(xy 2.7761 -2.6626) (xy 2.7823 -2.6677) (xy 2.7881 -2.673) (xy 2.7936 -2.6784) (xy 2.7987 -2.6841)
						(xy 2.8035 -2.69) (xy 2.808 -2.6961) (xy 2.8121 -2.7024) (xy 2.8158 -2.7088) (xy 2.8191 -2.7155)
						(xy 2.822 -2.7222) (xy 2.8245 -2.7291) (xy 2.8266 -2.7361) (xy 2.8282 -2.7433) (xy 2.8294 -2.7506)
						(xy 2.8301 -2.758) (xy 2.8303 -2.7654) (xy 2.8303 -3.2986) (xy 2.8301 -3.3061) (xy 2.8294 -3.3135)
						(xy 2.8282 -3.3208) (xy 2.8266 -3.3279) (xy 2.8245 -3.335) (xy 2.822 -3.3419) (xy 2.8191 -3.3486)
						(xy 2.8158 -3.3552) (xy 2.8121 -3.3617) (xy 2.808 -3.3679) (xy 2.8035 -3.374) (xy 2.7987 -3.3799)
						(xy 2.7936 -3.3856) (xy 2.7881 -3.3911) (xy 2.7823 -3.3964) (xy 2.7761 -3.4015) (xy 2.763 -3.4108)
						(xy 2.7488 -3.4192) (xy 2.7335 -3.4265) (xy 2.7174 -3.4326) (xy 2.7004 -3.4375) (xy 2.6826 -3.4411)
						(xy 2.6643 -3.4433) (xy 2.6549 -3.4439) (xy 2.6453 -3.4441) (xy 2.299 -3.4441) (xy 1.9671 -3.4441)
						(xy 1.1106 -3.4441) (xy 0.4323 -3.4441) (xy -0.4378 -3.4441) (xy -1.116 -3.4441) (xy -1.9725 -3.4441)
						(xy -2.3044 -3.4441) (xy -2.6508 -3.4441) (xy -2.6603 -3.4439) (xy -2.6697 -3.4433) (xy -2.6789 -3.4424)
						(xy -2.688 -3.4411) (xy -2.697 -3.4395) (xy -2.7058 -3.4375) (xy -2.7144 -3.4352) (xy -2.7228 -3.4326)
						(xy -2.731 -3.4297) (xy -2.7389 -3.4265) (xy -2.7467 -3.423) (xy -2.7542 -3.4192) (xy -2.7614 -3.4152)
						(xy -2.7684 -3.4108) (xy -2.7751 -3.4063) (xy -2.7816 -3.4015) (xy -2.7877 -3.3964) (xy -2.7935 -3.3911)
						(xy -2.799 -3.3856) (xy -2.8041 -3.3799) (xy -2.809 -3.374) (xy -2.8134 -3.3679) (xy -2.8175 -3.3617)
						(xy -2.8212 -3.3552) (xy -2.8245 -3.3486) (xy -2.8274 -3.3419) (xy -2.8299 -3.335) (xy -2.832 -3.3279)
						(xy -2.8336 -3.3208) (xy -2.8348 -3.3135) (xy -2.8355 -3.3061) (xy -2.8357 -3.2986) (xy -2.8357 -2.7654)
						(xy -2.8355 -2.758) (xy -2.8348 -2.7506) (xy -2.8336 -2.7433) (xy -2.832 -2.7361) (xy -2.8299 -2.7291)
						(xy -2.8274 -2.7222) (xy -2.8245 -2.7155) (xy -2.8212 -2.7088) (xy -2.8175 -2.7024) (xy -2.8134 -2.6961)
						(xy -2.809 -2.69) (xy -2.8041 -2.6841) (xy -2.799 -2.6784) (xy -2.7935 -2.673) (xy -2.7877 -2.6677)
						(xy -2.7816 -2.6626) (xy -2.7684 -2.6532) (xy -2.7542 -2.6449) (xy -2.7389 -2.6376) (xy -2.7228 -2.6314)
						(xy -2.7058 -2.6266) (xy -2.688 -2.623) (xy -2.6697 -2.6208) (xy -2.6603 -2.6202) (xy -2.6508 -2.62)
						(xy -2.3044 -2.62) (xy -1.9725 -2.62) (xy -1.116 -2.62) (xy -0.4378 -2.62) (xy 0.4323 -2.62) (xy 1.1106 -2.62)
						(xy 1.9671 -2.62) (xy 2.299 -2.62) (xy 2.6453 -2.62) (xy 2.6549 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6924 1.0326) (xy 2.6998 1.032) (xy 2.707 1.0311) (xy 2.7142 1.0298) (xy 2.7212 1.0282) (xy 2.7281 1.0262)
						(xy 2.7349 1.024) (xy 2.7415 1.0214) (xy 2.7479 1.0184) (xy 2.7542 1.0152) (xy 2.7603 1.0117)
						(xy 2.7662 1.0079) (xy 2.7719 1.0039) (xy 2.7774 0.9996) (xy 2.7827 0.995) (xy 2.7877 0.9902)
						(xy 2.7925 0.9851) (xy 2.7971 0.9798) (xy 2.8014 0.9744) (xy 2.8055 0.9687) (xy 2.8093 0.9628)
						(xy 2.8128 0.9567) (xy 2.816 0.9504) (xy 2.8189 0.944) (xy 2.8215 0.9373) (xy 2.8238 0.9306) (xy 2.8257 0.9237)
						(xy 2.8274 0.9167) (xy 2.8286 0.9095) (xy 2.8296 0.9022) (xy 2.8301 0.8948) (xy 2.8303 0.8874)
						(xy 2.8303 0.3542) (xy 2.8301 0.3467) (xy 2.8296 0.3393) (xy 2.8286 0.332) (xy 2.8274 0.3249)
						(xy 2.8257 0.3178) (xy 2.8238 0.3109) (xy 2.8215 0.3042) (xy 2.8189 0.2976) (xy 2.816 0.2911)
						(xy 2.8128 0.2849) (xy 2.8093 0.2788) (xy 2.8055 0.2729) (xy 2.8014 0.2672) (xy 2.7971 0.2617)
						(xy 2.7925 0.2564) (xy 2.7877 0.2513) (xy 2.7827 0.2465) (xy 2.7774 0.242) (xy 2.7719 0.2376)
						(xy 2.7662 0.2336) (xy 2.7603 0.2298) (xy 2.7542 0.2263) (xy 2.7479 0.2231) (xy 2.7415 0.2202)
						(xy 2.7349 0.2176) (xy 2.7281 0.2153) (xy 2.7212 0.2133) (xy 2.7142 0.2117) (xy 2.707 0.2104)
						(xy 2.6998 0.2095) (xy 2.6924 0.2089) (xy 2.6849 0.2087) (xy 2.1517 0.2087) (xy 2.1442 0.2089)
						(xy 2.1368 0.2095) (xy 2.1296 0.2104) (xy 2.1224 0.2117) (xy 2.1154 0.2133) (xy 2.1085 0.2153)
						(xy 2.1017 0.2176) (xy 2.0951 0.2202) (xy 2.0887 0.2231) (xy 2.0824 0.2263) (xy 2.0763 0.2298)
						(xy 2.0704 0.2336) (xy 2.0647 0.2376) (xy 2.0592 0.242) (xy 2.0539 0.2465) (xy 2.0489 0.2513)
						(xy 2.0441 0.2564) (xy 2.0395 0.2617) (xy 2.0352 0.2672) (xy 2.0311 0.2729) (xy 2.0273 0.2788)
						(xy 2.0238 0.2849) (xy 2.0206 0.2911) (xy 2.0177 0.2976) (xy 2.0151 0.3042) (xy 2.0128 0.3109)
						(xy 2.0109 0.3178) (xy 2.0092 0.3249) (xy 2.008 0.332) (xy 2.007 0.3393) (xy 2.0065 0.3467) (xy 2.0063 0.3542)
						(xy 2.0063 0.8874) (xy 2.0065 0.8948) (xy 2.007 0.9022) (xy 2.008 0.9095) (xy 2.0092 0.9167) (xy 2.0109 0.9237)
						(xy 2.0128 0.9306) (xy 2.0151 0.9373) (xy 2.0177 0.944) (xy 2.0206 0.9504) (xy 2.0238 0.9567)
						(xy 2.0273 0.9628) (xy 2.0311 0.9687) (xy 2.0352 0.9744) (xy 2.0395 0.9798) (xy 2.0441 0.9851)
						(xy 2.0489 0.9902) (xy 2.0539 0.995) (xy 2.0592 0.9996) (xy 2.0647 1.0039) (xy 2.0704 1.0079)
						(xy 2.0763 1.0117) (xy 2.0824 1.0152) (xy 2.0887 1.0184) (xy 2.0951 1.0214) (xy 2.1017 1.024)
						(xy 2.1085 1.0262) (xy 2.1154 1.0282) (xy 2.1224 1.0298) (xy 2.1296 1.0311) (xy 2.1368 1.032)
						(xy 2.1442 1.0326) (xy 2.1517 1.0328) (xy 2.6849 1.0328) (xy 2.6924 1.0326)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6924 1.0153) (xy 2.6998 1.0147) (xy 2.707 1.0138) (xy 2.7142 1.0125) (xy 2.7212 1.0109)
						(xy 2.7281 1.0089) (xy 2.7349 1.0067) (xy 2.7415 1.0041) (xy 2.7479 1.0011) (xy 2.7542 0.9979)
						(xy 2.7603 0.9944) (xy 2.7662 0.9906) (xy 2.7719 0.9866) (xy 2.7774 0.9823) (xy 2.7827 0.9777)
						(xy 2.7877 0.9729) (xy 2.7925 0.9678) (xy 2.7971 0.9626) (xy 2.8014 0.9571) (xy 2.8055 0.9514)
						(xy 2.8093 0.9455) (xy 2.8128 0.9394) (xy 2.816 0.9331) (xy 2.8189 0.9267) (xy 2.8215 0.9201)
						(xy 2.8238 0.9133) (xy 2.8257 0.9064) (xy 2.8274 0.8994) (xy 2.8286 0.8922) (xy 2.8296 0.8849)
						(xy 2.8301 0.8776) (xy 2.8303 0.8701) (xy 2.8303 0.3369) (xy 2.8301 0.3294) (xy 2.8296 0.322)
						(xy 2.8286 0.3147) (xy 2.8274 0.3076) (xy 2.8257 0.3005) (xy 2.8238 0.2936) (xy 2.8215 0.2869)
						(xy 2.8189 0.2803) (xy 2.816 0.2738) (xy 2.8128 0.2676) (xy 2.8093 0.2615) (xy 2.8055 0.2556)
						(xy 2.8014 0.2499) (xy 2.7971 0.2444) (xy 2.7925 0.2391) (xy 2.7877 0.2341) (xy 2.7827 0.2292)
						(xy 2.7774 0.2247) (xy 2.7719 0.2203) (xy 2.7662 0.2163) (xy 2.7603 0.2125) (xy 2.7542 0.209)
						(xy 2.7479 0.2058) (xy 2.7415 0.2029) (xy 2.7349 0.2003) (xy 2.7281 0.198) (xy 2.7212 0.196) (xy 2.7142 0.1944)
						(xy 2.707 0.1931) (xy 2.6998 0.1922) (xy 2.6924 0.1916) (xy 2.6849 0.1914) (xy 2.1517 0.1914)
						(xy 2.1442 0.1916) (xy 2.1368 0.1922) (xy 2.1296 0.1931) (xy 2.1224 0.1944) (xy 2.1154 0.196)
						(xy 2.1085 0.198) (xy 2.1017 0.2003) (xy 2.0951 0.2029) (xy 2.0887 0.2058) (xy 2.0824 0.209) (xy 2.0763 0.2125)
						(xy 2.0704 0.2163) (xy 2.0647 0.2203) (xy 2.0592 0.2247) (xy 2.0539 0.2292) (xy 2.0489 0.2341)
						(xy 2.0441 0.2391) (xy 2.0395 0.2444) (xy 2.0352 0.2499) (xy 2.0311 0.2556) (xy 2.0273 0.2615)
						(xy 2.0238 0.2676) (xy 2.0206 0.2738) (xy 2.0177 0.2803) (xy 2.0151 0.2869) (xy 2.0128 0.2936)
						(xy 2.0109 0.3005) (xy 2.0092 0.3076) (xy 2.008 0.3147) (xy 2.007 0.322) (xy 2.0065 0.3294) (xy 2.0063 0.3369)
						(xy 2.0063 0.8701) (xy 2.0065 0.8776) (xy 2.007 0.8849) (xy 2.008 0.8922) (xy 2.0092 0.8994) (xy 2.0109 0.9064)
						(xy 2.0128 0.9133) (xy 2.0151 0.9201) (xy 2.0177 0.9267) (xy 2.0206 0.9331) (xy 2.0238 0.9394)
						(xy 2.0273 0.9455) (xy 2.0311 0.9514) (xy 2.0352 0.9571) (xy 2.0395 0.9626) (xy 2.0441 0.9678)
						(xy 2.0489 0.9729) (xy 2.0539 0.9777) (xy 2.0592 0.9823) (xy 2.0647 0.9866) (xy 2.0704 0.9906)
						(xy 2.0763 0.9944) (xy 2.0824 0.9979) (xy 2.0887 1.0011) (xy 2.0951 1.0041) (xy 2.1017 1.0067)
						(xy 2.1085 1.0089) (xy 2.1154 1.0109) (xy 2.1224 1.0125) (xy 2.1296 1.0138) (xy 2.1368 1.0147)
						(xy 2.1442 1.0153) (xy 2.1517 1.0155) (xy 2.6849 1.0155) (xy 2.6924 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6924 -0.1965) (xy 2.6998 -0.1971) (xy 2.707 -0.198) (xy 2.7142 -0.1993) (xy 2.7212 -0.2009)
						(xy 2.7281 -0.2029) (xy 2.7349 -0.2052) (xy 2.7415 -0.2078) (xy 2.7479 -0.2107) (xy 2.7542 -0.2139)
						(xy 2.7603 -0.2174) (xy 2.7662 -0.2212) (xy 2.7719 -0.2253) (xy 2.7774 -0.2296) (xy 2.7827 -0.2341)
						(xy 2.7877 -0.239) (xy 2.7925 -0.244) (xy 2.7971 -0.2493) (xy 2.8014 -0.2548) (xy 2.8055 -0.2605)
						(xy 2.8093 -0.2664) (xy 2.8128 -0.2725) (xy 2.816 -0.2787) (xy 2.8189 -0.2852) (xy 2.8215 -0.2918)
						(xy 2.8238 -0.2985) (xy 2.8257 -0.3054) (xy 2.8274 -0.3125) (xy 2.8286 -0.3196) (xy 2.8296 -0.3269)
						(xy 2.8301 -0.3343) (xy 2.8303 -0.3418) (xy 2.8303 -0.875) (xy 2.8301 -0.8825) (xy 2.8296 -0.8898)
						(xy 2.8286 -0.8971) (xy 2.8274 -0.9043) (xy 2.8257 -0.9113) (xy 2.8238 -0.9182) (xy 2.8215 -0.925)
						(xy 2.8189 -0.9316) (xy 2.816 -0.938) (xy 2.8128 -0.9443) (xy 2.8093 -0.9504) (xy 2.8055 -0.9563)
						(xy 2.8014 -0.962) (xy 2.7971 -0.9675) (xy 2.7925 -0.9727) (xy 2.7877 -0.9778) (xy 2.7827 -0.9826)
						(xy 2.7774 -0.9872) (xy 2.7719 -0.9915) (xy 2.7662 -0.9956) (xy 2.7603 -0.9993) (xy 2.7542 -1.0028)
						(xy 2.7479 -1.006) (xy 2.7415 -1.009) (xy 2.7349 -1.0116) (xy 2.7281 -1.0139) (xy 2.7212 -1.0158)
						(xy 2.7142 -1.0174) (xy 2.707 -1.0187) (xy 2.6998 -1.0196) (xy 2.6924 -1.0202) (xy 2.6849 -1.0204)
						(xy 2.1517 -1.0204) (xy 2.1442 -1.0202) (xy 2.1368 -1.0196) (xy 2.1296 -1.0187) (xy 2.1224 -1.0174)
						(xy 2.1154 -1.0158) (xy 2.1085 -1.0139) (xy 2.1017 -1.0116) (xy 2.0951 -1.009) (xy 2.0887 -1.006)
						(xy 2.0824 -1.0028) (xy 2.0763 -0.9993) (xy 2.0704 -0.9956) (xy 2.0647 -0.9915) (xy 2.0592 -0.9872)
						(xy 2.0539 -0.9826) (xy 2.0489 -0.9778) (xy 2.0441 -0.9727) (xy 2.0395 -0.9675) (xy 2.0352 -0.962)
						(xy 2.0311 -0.9563) (xy 2.0273 -0.9504) (xy 2.0238 -0.9443) (xy 2.0206 -0.938) (xy 2.0177 -0.9316)
						(xy 2.0151 -0.925) (xy 2.0128 -0.9182) (xy 2.0109 -0.9113) (xy 2.0092 -0.9043) (xy 2.008 -0.8971)
						(xy 2.007 -0.8898) (xy 2.0065 -0.8825) (xy 2.0063 -0.875) (xy 2.0063 -0.3418) (xy 2.0065 -0.3343)
						(xy 2.007 -0.3269) (xy 2.008 -0.3196) (xy 2.0092 -0.3125) (xy 2.0109 -0.3054) (xy 2.0128 -0.2985)
						(xy 2.0151 -0.2918) (xy 2.0177 -0.2852) (xy 2.0206 -0.2787) (xy 2.0238 -0.2725) (xy 2.0273 -0.2664)
						(xy 2.0311 -0.2605) (xy 2.0352 -0.2548) (xy 2.0395 -0.2493) (xy 2.0441 -0.244) (xy 2.0489 -0.239)
						(xy 2.0539 -0.2341) (xy 2.0592 -0.2296) (xy 2.0647 -0.2253) (xy 2.0704 -0.2212) (xy 2.0763 -0.2174)
						(xy 2.0824 -0.2139) (xy 2.0887 -0.2107) (xy 2.0951 -0.2078) (xy 2.1017 -0.2052) (xy 2.1085 -0.2029)
						(xy 2.1154 -0.2009) (xy 2.1224 -0.1993) (xy 2.1296 -0.198) (xy 2.1368 -0.1971) (xy 2.1442 -0.1965)
						(xy 2.1517 -0.1964) (xy 2.6849 -0.1964) (xy 2.6924 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 2.6924 -1.4084) (xy 2.6998 -1.4089) (xy 2.707 -1.4099) (xy 2.7142 -1.4111) (xy 2.7212 -1.4128)
						(xy 2.7281 -1.4147) (xy 2.7349 -1.417) (xy 2.7415 -1.4196) (xy 2.7479 -1.4225) (xy 2.7542 -1.4257)
						(xy 2.7603 -1.4292) (xy 2.7662 -1.433) (xy 2.7719 -1.4371) (xy 2.7774 -1.4414) (xy 2.7827 -1.446)
						(xy 2.7877 -1.4508) (xy 2.7925 -1.4558) (xy 2.7971 -1.4611) (xy 2.8014 -1.4666) (xy 2.8055 -1.4723)
						(xy 2.8093 -1.4782) (xy 2.8128 -1.4843) (xy 2.816 -1.4906) (xy 2.8189 -1.497) (xy 2.8215 -1.5036)
						(xy 2.8238 -1.5104) (xy 2.8257 -1.5173) (xy 2.8274 -1.5243) (xy 2.8286 -1.5315) (xy 2.8296 -1.5387)
						(xy 2.8301 -1.5461) (xy 2.8303 -1.5536) (xy 2.8303 -2.0868) (xy 2.8301 -2.0943) (xy 2.8296 -2.1017)
						(xy 2.8286 -2.1089) (xy 2.8274 -2.1161) (xy 2.8257 -2.1231) (xy 2.8238 -2.13) (xy 2.8215 -2.1368)
						(xy 2.8189 -2.1434) (xy 2.816 -2.1498) (xy 2.8128 -2.1561) (xy 2.8093 -2.1622) (xy 2.8055 -2.1681)
						(xy 2.8014 -2.1738) (xy 2.7971 -2.1793) (xy 2.7925 -2.1846) (xy 2.7877 -2.1896) (xy 2.7827 -2.1944)
						(xy 2.7774 -2.199) (xy 2.7719 -2.2033) (xy 2.7662 -2.2074) (xy 2.7603 -2.2112) (xy 2.7542 -2.2147)
						(xy 2.7479 -2.2179) (xy 2.7415 -2.2208) (xy 2.7349 -2.2234) (xy 2.7281 -2.2257) (xy 2.7212 -2.2276)
						(xy 2.7142 -2.2293) (xy 2.707 -2.2306) (xy 2.6998 -2.2315) (xy 2.6924 -2.232) (xy 2.6849 -2.2322)
						(xy 2.1517 -2.2322) (xy 2.1442 -2.232) (xy 2.1368 -2.2315) (xy 2.1296 -2.2306) (xy 2.1224 -2.2293)
						(xy 2.1154 -2.2276) (xy 2.1085 -2.2257) (xy 2.1017 -2.2234) (xy 2.0951 -2.2208) (xy 2.0887 -2.2179)
						(xy 2.0824 -2.2147) (xy 2.0763 -2.2112) (xy 2.0704 -2.2074) (xy 2.0647 -2.2033) (xy 2.0592 -2.199)
						(xy 2.0539 -2.1944) (xy 2.0489 -2.1896) (xy 2.0441 -2.1846) (xy 2.0395 -2.1793) (xy 2.0352 -2.1738)
						(xy 2.0311 -2.1681) (xy 2.0273 -2.1622) (xy 2.0238 -2.1561) (xy 2.0206 -2.1498) (xy 2.0177 -2.1434)
						(xy 2.0151 -2.1368) (xy 2.0128 -2.13) (xy 2.0109 -2.1231) (xy 2.0092 -2.1161) (xy 2.008 -2.1089)
						(xy 2.007 -2.1017) (xy 2.0065 -2.0943) (xy 2.0063 -2.0868) (xy 2.0063 -1.5536) (xy 2.0065 -1.5461)
						(xy 2.007 -1.5387) (xy 2.008 -1.5315) (xy 2.0092 -1.5243) (xy 2.0109 -1.5173) (xy 2.0128 -1.5104)
						(xy 2.0151 -1.5036) (xy 2.0177 -1.497) (xy 2.0206 -1.4906) (xy 2.0238 -1.4843) (xy 2.0273 -1.4782)
						(xy 2.0311 -1.4723) (xy 2.0352 -1.4666) (xy 2.0395 -1.4611) (xy 2.0441 -1.4558) (xy 2.0489 -1.4508)
						(xy 2.0539 -1.446) (xy 2.0592 -1.4414) (xy 2.0647 -1.4371) (xy 2.0704 -1.433) (xy 2.0763 -1.4292)
						(xy 2.0824 -1.4257) (xy 2.0887 -1.4225) (xy 2.0951 -1.4196) (xy 2.1017 -1.417) (xy 2.1085 -1.4147)
						(xy 2.1154 -1.4128) (xy 2.1224 -1.4111) (xy 2.1296 -1.4099) (xy 2.1368 -1.4089) (xy 2.1442 -1.4084)
						(xy 2.1517 -1.4082) (xy 2.6849 -1.4082) (xy 2.6924 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 3.9042 -0.1965) (xy 3.9116 -0.1971) (xy 3.9189 -0.198) (xy 3.926 -0.1993) (xy 3.9331 -0.2009)
						(xy 3.94 -0.2029) (xy 3.9467 -0.2052) (xy 3.9533 -0.2078) (xy 3.9598 -0.2107) (xy 3.966 -0.2139)
						(xy 3.9721 -0.2174) (xy 3.978 -0.2212) (xy 3.9837 -0.2253) (xy 3.9892 -0.2296) (xy 3.9945 -0.2341)
						(xy 3.9995 -0.239) (xy 4.0044 -0.244) (xy 4.0089 -0.2493) (xy 4.0133 -0.2548) (xy 4.0173 -0.2605)
						(xy 4.0211 -0.2664) (xy 4.0246 -0.2725) (xy 4.0278 -0.2787) (xy 4.0307 -0.2852) (xy 4.0333 -0.2918)
						(xy 4.0356 -0.2985) (xy 4.0376 -0.3054) (xy 4.0392 -0.3125) (xy 4.0405 -0.3196) (xy 4.0414 -0.3269)
						(xy 4.042 -0.3343) (xy 4.0422 -0.3418) (xy 4.0422 -0.875) (xy 4.042 -0.8825) (xy 4.0414 -0.8898)
						(xy 4.0405 -0.8971) (xy 4.0392 -0.9043) (xy 4.0376 -0.9113) (xy 4.0356 -0.9182) (xy 4.0333 -0.925)
						(xy 4.0307 -0.9316) (xy 4.0278 -0.938) (xy 4.0246 -0.9443) (xy 4.0211 -0.9504) (xy 4.0173 -0.9563)
						(xy 4.0133 -0.962) (xy 4.0089 -0.9675) (xy 4.0044 -0.9727) (xy 3.9995 -0.9778) (xy 3.9945 -0.9826)
						(xy 3.9892 -0.9872) (xy 3.9837 -0.9915) (xy 3.978 -0.9956) (xy 3.9721 -0.9993) (xy 3.966 -1.0028)
						(xy 3.9598 -1.006) (xy 3.9533 -1.009) (xy 3.9467 -1.0116) (xy 3.94 -1.0139) (xy 3.9331 -1.0158)
						(xy 3.926 -1.0174) (xy 3.9189 -1.0187) (xy 3.9116 -1.0196) (xy 3.9042 -1.0202) (xy 3.8967 -1.0204)
						(xy 3.3635 -1.0204) (xy 3.3561 -1.0202) (xy 3.3487 -1.0196) (xy 3.3414 -1.0187) (xy 3.3342 -1.0174)
						(xy 3.3272 -1.0158) (xy 3.3203 -1.0139) (xy 3.3135 -1.0116) (xy 3.3069 -1.009) (xy 3.3005 -1.006)
						(xy 3.2942 -1.0028) (xy 3.2881 -0.9993) (xy 3.2822 -0.9956) (xy 3.2765 -0.9915) (xy 3.271 -0.9872)
						(xy 3.2658 -0.9826) (xy 3.2607 -0.9778) (xy 3.2559 -0.9727) (xy 3.2513 -0.9675) (xy 3.247 -0.962)
						(xy 3.243 -0.9563) (xy 3.2392 -0.9504) (xy 3.2357 -0.9443) (xy 3.2325 -0.938) (xy 3.2295 -0.9316)
						(xy 3.2269 -0.925) (xy 3.2247 -0.9182) (xy 3.2227 -0.9113) (xy 3.2211 -0.9043) (xy 3.2198 -0.8971)
						(xy 3.2189 -0.8898) (xy 3.2183 -0.8825) (xy 3.2181 -0.875) (xy 3.2181 -0.3418) (xy 3.2183 -0.3343)
						(xy 3.2189 -0.3269) (xy 3.2198 -0.3196) (xy 3.2211 -0.3125) (xy 3.2227 -0.3054) (xy 3.2247 -0.2985)
						(xy 3.2269 -0.2918) (xy 3.2295 -0.2852) (xy 3.2325 -0.2787) (xy 3.2357 -0.2725) (xy 3.2392 -0.2664)
						(xy 3.243 -0.2605) (xy 3.247 -0.2548) (xy 3.2513 -0.2493) (xy 3.2559 -0.244) (xy 3.2607 -0.239)
						(xy 3.2658 -0.2341) (xy 3.271 -0.2296) (xy 3.2765 -0.2253) (xy 3.2822 -0.2212) (xy 3.2881 -0.2174)
						(xy 3.2942 -0.2139) (xy 3.3005 -0.2107) (xy 3.3069 -0.2078) (xy 3.3135 -0.2052) (xy 3.3203 -0.2029)
						(xy 3.3272 -0.2009) (xy 3.3342 -0.1993) (xy 3.3414 -0.198) (xy 3.3487 -0.1971) (xy 3.3561 -0.1965)
						(xy 3.3635 -0.1964) (xy 3.8967 -0.1964) (xy 3.9042 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 3.9042 -2.6029) (xy 3.9116 -2.6035) (xy 3.9189 -2.6044) (xy 3.926 -2.6057) (xy 3.9331 -2.6073)
						(xy 3.94 -2.6093) (xy 3.9467 -2.6115) (xy 3.9533 -2.6141) (xy 3.9598 -2.6171) (xy 3.966 -2.6203)
						(xy 3.9721 -2.6238) (xy 3.978 -2.6276) (xy 3.9837 -2.6316) (xy 3.9892 -2.6359) (xy 3.9945 -2.6405)
						(xy 3.9995 -2.6453) (xy 4.0044 -2.6504) (xy 4.0089 -2.6556) (xy 4.0133 -2.6611) (xy 4.0173 -2.6668)
						(xy 4.0211 -2.6727) (xy 4.0246 -2.6788) (xy 4.0278 -2.6851) (xy 4.0307 -2.6915) (xy 4.0333 -2.6981)
						(xy 4.0356 -2.7049) (xy 4.0376 -2.7118) (xy 4.0392 -2.7188) (xy 4.0405 -2.726) (xy 4.0414 -2.7333)
						(xy 4.042 -2.7406) (xy 4.0422 -2.7481) (xy 4.0422 -3.2813) (xy 4.042 -3.2888) (xy 4.0414 -3.2962)
						(xy 4.0405 -3.3035) (xy 4.0392 -3.3106) (xy 4.0376 -3.3177) (xy 4.0356 -3.3246) (xy 4.0333 -3.3313)
						(xy 4.0307 -3.3379) (xy 4.0278 -3.3444) (xy 4.0246 -3.3506) (xy 4.0211 -3.3567) (xy 4.0173 -3.3626)
						(xy 4.0133 -3.3683) (xy 4.0089 -3.3738) (xy 4.0044 -3.3791) (xy 3.9995 -3.3841) (xy 3.9945 -3.389)
						(xy 3.9892 -3.3935) (xy 3.9837 -3.3979) (xy 3.978 -3.4019) (xy 3.9721 -3.4057) (xy 3.966 -3.4092)
						(xy 3.9598 -3.4124) (xy 3.9533 -3.4153) (xy 3.9467 -3.4179) (xy 3.94 -3.4202) (xy 3.9331 -3.4222)
						(xy 3.926 -3.4238) (xy 3.9189 -3.4251) (xy 3.9116 -3.426) (xy 3.9042 -3.4266) (xy 3.8967 -3.4268)
						(xy 3.3635 -3.4268) (xy 3.3561 -3.4266) (xy 3.3487 -3.426) (xy 3.3414 -3.4251) (xy 3.3342 -3.4238)
						(xy 3.3272 -3.4222) (xy 3.3203 -3.4202) (xy 3.3135 -3.4179) (xy 3.3069 -3.4153) (xy 3.3005 -3.4124)
						(xy 3.2942 -3.4092) (xy 3.2881 -3.4057) (xy 3.2822 -3.4019) (xy 3.2765 -3.3979) (xy 3.271 -3.3935)
						(xy 3.2658 -3.389) (xy 3.2607 -3.3841) (xy 3.2559 -3.3791) (xy 3.2513 -3.3738) (xy 3.247 -3.3683)
						(xy 3.243 -3.3626) (xy 3.2392 -3.3567) (xy 3.2357 -3.3506) (xy 3.2325 -3.3444) (xy 3.2295 -3.3379)
						(xy 3.2269 -3.3313) (xy 3.2247 -3.3246) (xy 3.2227 -3.3177) (xy 3.2211 -3.3106) (xy 3.2198 -3.3035)
						(xy 3.2189 -3.2962) (xy 3.2183 -3.2888) (xy 3.2181 -3.2813) (xy 3.2181 -2.7481) (xy 3.2183 -2.7406)
						(xy 3.2189 -2.7333) (xy 3.2198 -2.726) (xy 3.2211 -2.7188) (xy 3.2227 -2.7118) (xy 3.2247 -2.7049)
						(xy 3.2269 -2.6981) (xy 3.2295 -2.6915) (xy 3.2325 -2.6851) (xy 3.2357 -2.6788) (xy 3.2392 -2.6727)
						(xy 3.243 -2.6668) (xy 3.247 -2.6611) (xy 3.2513 -2.6556) (xy 3.2559 -2.6504) (xy 3.2607 -2.6453)
						(xy 3.2658 -2.6405) (xy 3.271 -2.6359) (xy 3.2765 -2.6316) (xy 3.2822 -2.6276) (xy 3.2881 -2.6238)
						(xy 3.2942 -2.6203) (xy 3.3005 -2.6171) (xy 3.3069 -2.6141) (xy 3.3135 -2.6115) (xy 3.3203 -2.6093)
						(xy 3.3272 -2.6073) (xy 3.3342 -2.6057) (xy 3.3414 -2.6044) (xy 3.3487 -2.6035) (xy 3.3561 -2.6029)
						(xy 3.3635 -2.6027) (xy 3.8967 -2.6027) (xy 3.9042 -2.6029)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 3.9042 -2.6202) (xy 3.9116 -2.6208) (xy 3.9189 -2.6217) (xy 3.926 -2.623) (xy 3.9331 -2.6246)
						(xy 3.94 -2.6266) (xy 3.9467 -2.6288) (xy 3.9533 -2.6314) (xy 3.9598 -2.6344) (xy 3.966 -2.6376)
						(xy 3.9721 -2.6411) (xy 3.978 -2.6449) (xy 3.9837 -2.6489) (xy 3.9892 -2.6532) (xy 3.9945 -2.6578)
						(xy 3.9995 -2.6626) (xy 4.0044 -2.6677) (xy 4.0089 -2.673) (xy 4.0133 -2.6784) (xy 4.0173 -2.6841)
						(xy 4.0211 -2.69) (xy 4.0246 -2.6961) (xy 4.0278 -2.7024) (xy 4.0307 -2.7088) (xy 4.0333 -2.7155)
						(xy 4.0356 -2.7222) (xy 4.0376 -2.7291) (xy 4.0392 -2.7361) (xy 4.0405 -2.7433) (xy 4.0414 -2.7506)
						(xy 4.042 -2.758) (xy 4.0422 -2.7654) (xy 4.0422 -3.2986) (xy 4.042 -3.3061) (xy 4.0414 -3.3135)
						(xy 4.0405 -3.3208) (xy 4.0392 -3.3279) (xy 4.0376 -3.335) (xy 4.0356 -3.3419) (xy 4.0333 -3.3486)
						(xy 4.0307 -3.3552) (xy 4.0278 -3.3617) (xy 4.0246 -3.3679) (xy 4.0211 -3.374) (xy 4.0173 -3.3799)
						(xy 4.0133 -3.3856) (xy 4.0089 -3.3911) (xy 4.0044 -3.3964) (xy 3.9995 -3.4015) (xy 3.9945 -3.4063)
						(xy 3.9892 -3.4108) (xy 3.9837 -3.4152) (xy 3.978 -3.4192) (xy 3.9721 -3.423) (xy 3.966 -3.4265)
						(xy 3.9598 -3.4297) (xy 3.9533 -3.4326) (xy 3.9467 -3.4352) (xy 3.94 -3.4375) (xy 3.9331 -3.4395)
						(xy 3.926 -3.4411) (xy 3.9189 -3.4424) (xy 3.9116 -3.4433) (xy 3.9042 -3.4439) (xy 3.8967 -3.4441)
						(xy 3.3635 -3.4441) (xy 3.3561 -3.4439) (xy 3.3487 -3.4433) (xy 3.3414 -3.4424) (xy 3.3342 -3.4411)
						(xy 3.3272 -3.4395) (xy 3.3203 -3.4375) (xy 3.3135 -3.4352) (xy 3.3069 -3.4326) (xy 3.3005 -3.4297)
						(xy 3.2942 -3.4265) (xy 3.2881 -3.423) (xy 3.2822 -3.4192) (xy 3.2765 -3.4152) (xy 3.271 -3.4108)
						(xy 3.2658 -3.4063) (xy 3.2607 -3.4015) (xy 3.2559 -3.3964) (xy 3.2513 -3.3911) (xy 3.247 -3.3856)
						(xy 3.243 -3.3799) (xy 3.2392 -3.374) (xy 3.2357 -3.3679) (xy 3.2325 -3.3617) (xy 3.2295 -3.3552)
						(xy 3.2269 -3.3486) (xy 3.2247 -3.3419) (xy 3.2227 -3.335) (xy 3.2211 -3.3279) (xy 3.2198 -3.3208)
						(xy 3.2189 -3.3135) (xy 3.2183 -3.3061) (xy 3.2181 -3.2986) (xy 3.2181 -2.7654) (xy 3.2183 -2.758)
						(xy 3.2189 -2.7506) (xy 3.2198 -2.7433) (xy 3.2211 -2.7361) (xy 3.2227 -2.7291) (xy 3.2247 -2.7222)
						(xy 3.2269 -2.7155) (xy 3.2295 -2.7088) (xy 3.2325 -2.7024) (xy 3.2357 -2.6961) (xy 3.2392 -2.69)
						(xy 3.243 -2.6841) (xy 3.247 -2.6784) (xy 3.2513 -2.673) (xy 3.2559 -2.6677) (xy 3.2607 -2.6626)
						(xy 3.2658 -2.6578) (xy 3.271 -2.6532) (xy 3.2765 -2.6489) (xy 3.2822 -2.6449) (xy 3.2881 -2.6411)
						(xy 3.2942 -2.6376) (xy 3.3005 -2.6344) (xy 3.3069 -2.6314) (xy 3.3135 -2.6288) (xy 3.3203 -2.6266)
						(xy 3.3272 -2.6246) (xy 3.3342 -2.623) (xy 3.3414 -2.6217) (xy 3.3487 -2.6208) (xy 3.3561 -2.6202)
						(xy 3.3635 -2.62) (xy 3.8967 -2.62) (xy 3.9042 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 1.0153) (xy 5.1288 1.0147) (xy 5.136 1.0138) (xy 5.1432 1.0125) (xy 5.1502 1.0109)
						(xy 5.1571 1.0089) (xy 5.1639 1.0067) (xy 5.1705 1.004) (xy 5.1769 1.0011) (xy 5.1832 0.9979)
						(xy 5.1893 0.9944) (xy 5.1952 0.9906) (xy 5.2009 0.9866) (xy 5.2064 0.9823) (xy 5.2117 0.9777)
						(xy 5.2167 0.9729) (xy 5.2215 0.9678) (xy 5.2261 0.9625) (xy 5.2304 0.9571) (xy 5.2345 0.9513)
						(xy 5.2383 0.9454) (xy 5.2418 0.9394) (xy 5.245 0.9331) (xy 5.2479 0.9266) (xy 5.2505 0.92) (xy 5.2528 0.9133)
						(xy 5.2547 0.9064) (xy 5.2564 0.8994) (xy 5.2576 0.8922) (xy 5.2586 0.8849) (xy 5.2591 0.8775)
						(xy 5.2593 0.8701) (xy 5.2593 0.3369) (xy 5.2591 0.3294) (xy 5.2586 0.322) (xy 5.2576 0.3147)
						(xy 5.2564 0.3076) (xy 5.2547 0.3005) (xy 5.2528 0.2936) (xy 5.2505 0.2869) (xy 5.2479 0.2803)
						(xy 5.245 0.2738) (xy 5.2418 0.2676) (xy 5.2383 0.2615) (xy 5.2345 0.2556) (xy 5.2304 0.2499)
						(xy 5.2261 0.2444) (xy 5.2215 0.2391) (xy 5.2167 0.234) (xy 5.2117 0.2292) (xy 5.2064 0.2247)
						(xy 5.2009 0.2203) (xy 5.1952 0.2163) (xy 5.1893 0.2125) (xy 5.1832 0.209) (xy 5.1769 0.2058)
						(xy 5.1705 0.2029) (xy 5.1639 0.2003) (xy 5.1571 0.198) (xy 5.1502 0.196) (xy 5.1432 0.1944) (xy 5.136 0.1931)
						(xy 5.1288 0.1922) (xy 5.1214 0.1916) (xy 5.1139 0.1914) (xy 5.0226 0.1914) (xy 4.5807 0.1914)
						(xy 3.8967 0.1914) (xy 3.4549 0.1914) (xy 3.3635 0.1914) (xy 3.3561 0.1916) (xy 3.3487 0.1922)
						(xy 3.3414 0.1931) (xy 3.3342 0.1944) (xy 3.3272 0.196) (xy 3.3203 0.198) (xy 3.3135 0.2003) (xy 3.3069 0.2029)
						(xy 3.3005 0.2058) (xy 3.2942 0.209) (xy 3.2881 0.2125) (xy 3.2822 0.2163) (xy 3.2765 0.2203)
						(xy 3.271 0.2247) (xy 3.2658 0.2292) (xy 3.2607 0.234) (xy 3.2559 0.2391) (xy 3.2513 0.2444) (xy 3.247 0.2499)
						(xy 3.243 0.2556) (xy 3.2392 0.2615) (xy 3.2357 0.2676) (xy 3.2325 0.2738) (xy 3.2295 0.2803)
						(xy 3.2269 0.2869) (xy 3.2247 0.2936) (xy 3.2227 0.3005) (xy 3.2211 0.3076) (xy 3.2198 0.3147)
						(xy 3.2189 0.322) (xy 3.2183 0.3294) (xy 3.2181 0.3369) (xy 3.2181 0.8701) (xy 3.2183 0.8775)
						(xy 3.2189 0.8849) (xy 3.2198 0.8922) (xy 3.2211 0.8994) (xy 3.2227 0.9064) (xy 3.2247 0.9133)
						(xy 3.2269 0.92) (xy 3.2295 0.9266) (xy 3.2325 0.9331) (xy 3.2357 0.9394) (xy 3.2392 0.9454) (xy 3.243 0.9513)
						(xy 3.247 0.9571) (xy 3.2513 0.9625) (xy 3.2559 0.9678) (xy 3.2607 0.9729) (xy 3.2658 0.9777)
						(xy 3.271 0.9823) (xy 3.2765 0.9866) (xy 3.2822 0.9906) (xy 3.2881 0.9944) (xy 3.2942 0.9979)
						(xy 3.3005 1.0011) (xy 3.3069 1.004) (xy 3.3135 1.0067) (xy 3.3203 1.0089) (xy 3.3272 1.0109)
						(xy 3.3342 1.0125) (xy 3.3414 1.0138) (xy 3.3487 1.0147) (xy 3.3561 1.0153) (xy 3.3635 1.0155)
						(xy 3.4549 1.0155) (xy 3.8967 1.0155) (xy 4.5807 1.0155) (xy 5.0226 1.0155) (xy 5.1139 1.0155)
						(xy 5.1214 1.0153)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 -0.1965) (xy 5.1288 -0.1971) (xy 5.136 -0.198) (xy 5.1432 -0.1993) (xy 5.1502 -0.2009)
						(xy 5.1571 -0.2029) (xy 5.1639 -0.2052) (xy 5.1705 -0.2078) (xy 5.1769 -0.2107) (xy 5.1832 -0.2139)
						(xy 5.1893 -0.2174) (xy 5.1952 -0.2212) (xy 5.2009 -0.2253) (xy 5.2064 -0.2296) (xy 5.2117 -0.2341)
						(xy 5.2167 -0.239) (xy 5.2215 -0.244) (xy 5.2261 -0.2493) (xy 5.2304 -0.2548) (xy 5.2345 -0.2605)
						(xy 5.2383 -0.2664) (xy 5.2418 -0.2725) (xy 5.245 -0.2787) (xy 5.2479 -0.2852) (xy 5.2505 -0.2918)
						(xy 5.2528 -0.2985) (xy 5.2547 -0.3054) (xy 5.2564 -0.3125) (xy 5.2576 -0.3196) (xy 5.2586 -0.3269)
						(xy 5.2591 -0.3343) (xy 5.2593 -0.3418) (xy 5.2593 -0.875) (xy 5.2591 -0.8825) (xy 5.2586 -0.8898)
						(xy 5.2576 -0.8971) (xy 5.2564 -0.9043) (xy 5.2547 -0.9113) (xy 5.2528 -0.9182) (xy 5.2505 -0.925)
						(xy 5.2479 -0.9316) (xy 5.245 -0.938) (xy 5.2418 -0.9443) (xy 5.2383 -0.9504) (xy 5.2345 -0.9563)
						(xy 5.2304 -0.962) (xy 5.2261 -0.9675) (xy 5.2215 -0.9727) (xy 5.2167 -0.9778) (xy 5.2117 -0.9826)
						(xy 5.2064 -0.9872) (xy 5.2009 -0.9915) (xy 5.1952 -0.9956) (xy 5.1893 -0.9993) (xy 5.1832 -1.0028)
						(xy 5.1769 -1.006) (xy 5.1705 -1.009) (xy 5.1639 -1.0116) (xy 5.1571 -1.0139) (xy 5.1502 -1.0158)
						(xy 5.1432 -1.0174) (xy 5.136 -1.0187) (xy 5.1288 -1.0196) (xy 5.1214 -1.0202) (xy 5.1139 -1.0204)
						(xy 4.5807 -1.0204) (xy 4.5732 -1.0202) (xy 4.5658 -1.0196) (xy 4.5586 -1.0187) (xy 4.5514 -1.0174)
						(xy 4.5444 -1.0158) (xy 4.5375 -1.0139) (xy 4.5307 -1.0116) (xy 4.5241 -1.009) (xy 4.5177 -1.006)
						(xy 4.5114 -1.0028) (xy 4.5053 -0.9993) (xy 4.4994 -0.9956) (xy 4.4937 -0.9915) (xy 4.4882 -0.9872)
						(xy 4.4829 -0.9826) (xy 4.4779 -0.9778) (xy 4.4731 -0.9727) (xy 4.4685 -0.9675) (xy 4.4642 -0.962)
						(xy 4.4601 -0.9563) (xy 4.4563 -0.9504) (xy 4.4528 -0.9443) (xy 4.4496 -0.938) (xy 4.4467 -0.9316)
						(xy 4.4441 -0.925) (xy 4.4418 -0.9182) (xy 4.4399 -0.9113) (xy 4.4382 -0.9043) (xy 4.437 -0.8971)
						(xy 4.436 -0.8898) (xy 4.4355 -0.8825) (xy 4.4353 -0.875) (xy 4.4353 -0.3418) (xy 4.4355 -0.3343)
						(xy 4.436 -0.3269) (xy 4.437 -0.3196) (xy 4.4382 -0.3125) (xy 4.4399 -0.3054) (xy 4.4418 -0.2985)
						(xy 4.4441 -0.2918) (xy 4.4467 -0.2852) (xy 4.4496 -0.2787) (xy 4.4528 -0.2725) (xy 4.4563 -0.2664)
						(xy 4.4601 -0.2605) (xy 4.4642 -0.2548) (xy 4.4685 -0.2493) (xy 4.4731 -0.244) (xy 4.4779 -0.239)
						(xy 4.4829 -0.2341) (xy 4.4882 -0.2296) (xy 4.4937 -0.2253) (xy 4.4994 -0.2212) (xy 4.5053 -0.2174)
						(xy 4.5114 -0.2139) (xy 4.5177 -0.2107) (xy 4.5241 -0.2078) (xy 4.5307 -0.2052) (xy 4.5375 -0.2029)
						(xy 4.5444 -0.2009) (xy 4.5514 -0.1993) (xy 4.5586 -0.198) (xy 4.5658 -0.1971) (xy 4.5732 -0.1965)
						(xy 4.5807 -0.1964) (xy 5.1139 -0.1964) (xy 5.1214 -0.1965)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 -1.4084) (xy 5.1288 -1.4089) (xy 5.136 -1.4099) (xy 5.1432 -1.4111) (xy 5.1502 -1.4128)
						(xy 5.1571 -1.4147) (xy 5.1639 -1.417) (xy 5.1705 -1.4196) (xy 5.1769 -1.4225) (xy 5.1832 -1.4257)
						(xy 5.1893 -1.4292) (xy 5.1952 -1.433) (xy 5.2009 -1.4371) (xy 5.2064 -1.4414) (xy 5.2117 -1.446)
						(xy 5.2167 -1.4508) (xy 5.2215 -1.4558) (xy 5.2261 -1.4611) (xy 5.2304 -1.4666) (xy 5.2345 -1.4723)
						(xy 5.2383 -1.4782) (xy 5.2418 -1.4843) (xy 5.245 -1.4906) (xy 5.2479 -1.497) (xy 5.2505 -1.5036)
						(xy 5.2528 -1.5104) (xy 5.2547 -1.5173) (xy 5.2564 -1.5243) (xy 5.2576 -1.5315) (xy 5.2586 -1.5387)
						(xy 5.2591 -1.5461) (xy 5.2593 -1.5536) (xy 5.2593 -2.0868) (xy 5.2591 -2.0943) (xy 5.2586 -2.1017)
						(xy 5.2576 -2.1089) (xy 5.2564 -2.1161) (xy 5.2547 -2.1231) (xy 5.2528 -2.13) (xy 5.2505 -2.1368)
						(xy 5.2479 -2.1434) (xy 5.245 -2.1498) (xy 5.2418 -2.1561) (xy 5.2383 -2.1622) (xy 5.2345 -2.1681)
						(xy 5.2304 -2.1738) (xy 5.2261 -2.1793) (xy 5.2215 -2.1846) (xy 5.2167 -2.1896) (xy 5.2117 -2.1944)
						(xy 5.2064 -2.199) (xy 5.2009 -2.2033) (xy 5.1952 -2.2074) (xy 5.1893 -2.2112) (xy 5.1832 -2.2147)
						(xy 5.1769 -2.2179) (xy 5.1705 -2.2208) (xy 5.1639 -2.2234) (xy 5.1571 -2.2257) (xy 5.1502 -2.2276)
						(xy 5.1432 -2.2293) (xy 5.136 -2.2306) (xy 5.1288 -2.2315) (xy 5.1214 -2.232) (xy 5.1139 -2.2322)
						(xy 5.0226 -2.2322) (xy 4.5807 -2.2322) (xy 3.8967 -2.2322) (xy 3.4549 -2.2322) (xy 3.3635 -2.2322)
						(xy 3.3561 -2.232) (xy 3.3487 -2.2315) (xy 3.3414 -2.2306) (xy 3.3342 -2.2293) (xy 3.3272 -2.2276)
						(xy 3.3203 -2.2257) (xy 3.3135 -2.2234) (xy 3.3069 -2.2208) (xy 3.3005 -2.2179) (xy 3.2942 -2.2147)
						(xy 3.2881 -2.2112) (xy 3.2822 -2.2074) (xy 3.2765 -2.2033) (xy 3.271 -2.199) (xy 3.2658 -2.1944)
						(xy 3.2607 -2.1896) (xy 3.2559 -2.1846) (xy 3.2513 -2.1793) (xy 3.247 -2.1738) (xy 3.243 -2.1681)
						(xy 3.2392 -2.1622) (xy 3.2357 -2.1561) (xy 3.2325 -2.1498) (xy 3.2295 -2.1434) (xy 3.2269 -2.1368)
						(xy 3.2247 -2.13) (xy 3.2227 -2.1231) (xy 3.2211 -2.1161) (xy 3.2198 -2.1089) (xy 3.2189 -2.1017)
						(xy 3.2183 -2.0943) (xy 3.2181 -2.0868) (xy 3.2181 -1.5536) (xy 3.2183 -1.5461) (xy 3.2189 -1.5387)
						(xy 3.2198 -1.5315) (xy 3.2211 -1.5243) (xy 3.2227 -1.5173) (xy 3.2247 -1.5104) (xy 3.2269 -1.5036)
						(xy 3.2295 -1.497) (xy 3.2325 -1.4906) (xy 3.2357 -1.4843) (xy 3.2392 -1.4782) (xy 3.243 -1.4723)
						(xy 3.247 -1.4666) (xy 3.2513 -1.4611) (xy 3.2559 -1.4558) (xy 3.2607 -1.4508) (xy 3.2658 -1.446)
						(xy 3.271 -1.4414) (xy 3.2765 -1.4371) (xy 3.2822 -1.433) (xy 3.2881 -1.4292) (xy 3.2942 -1.4257)
						(xy 3.3005 -1.4225) (xy 3.3069 -1.4196) (xy 3.3135 -1.417) (xy 3.3203 -1.4147) (xy 3.3272 -1.4128)
						(xy 3.3342 -1.4111) (xy 3.3414 -1.4099) (xy 3.3487 -1.4089) (xy 3.3561 -1.4084) (xy 3.3635 -1.4082)
						(xy 3.4549 -1.4082) (xy 3.8967 -1.4082) (xy 4.5807 -1.4082) (xy 5.0226 -1.4082) (xy 5.1139 -1.4082)
						(xy 5.1214 -1.4084)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 -2.6029) (xy 5.1288 -2.6035) (xy 5.136 -2.6044) (xy 5.1432 -2.6057) (xy 5.1502 -2.6073)
						(xy 5.1571 -2.6093) (xy 5.1639 -2.6115) (xy 5.1705 -2.6141) (xy 5.1769 -2.6171) (xy 5.1832 -2.6203)
						(xy 5.1893 -2.6238) (xy 5.1952 -2.6276) (xy 5.2009 -2.6316) (xy 5.2064 -2.6359) (xy 5.2117 -2.6405)
						(xy 5.2167 -2.6453) (xy 5.2215 -2.6504) (xy 5.2261 -2.6556) (xy 5.2304 -2.6611) (xy 5.2345 -2.6668)
						(xy 5.2383 -2.6727) (xy 5.2418 -2.6788) (xy 5.245 -2.6851) (xy 5.2479 -2.6915) (xy 5.2505 -2.6981)
						(xy 5.2528 -2.7049) (xy 5.2547 -2.7118) (xy 5.2564 -2.7188) (xy 5.2576 -2.726) (xy 5.2586 -2.7333)
						(xy 5.2591 -2.7406) (xy 5.2593 -2.7481) (xy 5.2593 -3.2813) (xy 5.2591 -3.2888) (xy 5.2586 -3.2962)
						(xy 5.2576 -3.3035) (xy 5.2564 -3.3106) (xy 5.2547 -3.3177) (xy 5.2528 -3.3246) (xy 5.2505 -3.3313)
						(xy 5.2479 -3.3379) (xy 5.245 -3.3444) (xy 5.2418 -3.3506) (xy 5.2383 -3.3567) (xy 5.2345 -3.3626)
						(xy 5.2304 -3.3683) (xy 5.2261 -3.3738) (xy 5.2215 -3.3791) (xy 5.2167 -3.3841) (xy 5.2117 -3.389)
						(xy 5.2064 -3.3935) (xy 5.2009 -3.3979) (xy 5.1952 -3.4019) (xy 5.1893 -3.4057) (xy 5.1832 -3.4092)
						(xy 5.1769 -3.4124) (xy 5.1705 -3.4153) (xy 5.1639 -3.4179) (xy 5.1571 -3.4202) (xy 5.1502 -3.4222)
						(xy 5.1432 -3.4238) (xy 5.136 -3.4251) (xy 5.1288 -3.426) (xy 5.1214 -3.4266) (xy 5.1139 -3.4268)
						(xy 4.5807 -3.4268) (xy 4.5732 -3.4266) (xy 4.5658 -3.426) (xy 4.5586 -3.4251) (xy 4.5514 -3.4238)
						(xy 4.5444 -3.4222) (xy 4.5375 -3.4202) (xy 4.5307 -3.4179) (xy 4.5241 -3.4153) (xy 4.5177 -3.4124)
						(xy 4.5114 -3.4092) (xy 4.5053 -3.4057) (xy 4.4994 -3.4019) (xy 4.4937 -3.3979) (xy 4.4882 -3.3935)
						(xy 4.4829 -3.389) (xy 4.4779 -3.3841) (xy 4.4731 -3.3791) (xy 4.4685 -3.3738) (xy 4.4642 -3.3683)
						(xy 4.4601 -3.3626) (xy 4.4563 -3.3567) (xy 4.4528 -3.3506) (xy 4.4496 -3.3444) (xy 4.4467 -3.3379)
						(xy 4.4441 -3.3313) (xy 4.4418 -3.3246) (xy 4.4399 -3.3177) (xy 4.4382 -3.3106) (xy 4.437 -3.3035)
						(xy 4.436 -3.2962) (xy 4.4355 -3.2888) (xy 4.4353 -3.2813) (xy 4.4353 -2.7481) (xy 4.4355 -2.7406)
						(xy 4.436 -2.7333) (xy 4.437 -2.726) (xy 4.4382 -2.7188) (xy 4.4399 -2.7118) (xy 4.4418 -2.7049)
						(xy 4.4441 -2.6981) (xy 4.4467 -2.6915) (xy 4.4496 -2.6851) (xy 4.4528 -2.6788) (xy 4.4563 -2.6727)
						(xy 4.4601 -2.6668) (xy 4.4642 -2.6611) (xy 4.4685 -2.6556) (xy 4.4731 -2.6504) (xy 4.4779 -2.6453)
						(xy 4.4829 -2.6405) (xy 4.4882 -2.6359) (xy 4.4937 -2.6316) (xy 4.4994 -2.6276) (xy 4.5053 -2.6238)
						(xy 4.5114 -2.6203) (xy 4.5177 -2.6171) (xy 4.5241 -2.6141) (xy 4.5307 -2.6115) (xy 4.5375 -2.6093)
						(xy 4.5444 -2.6073) (xy 4.5514 -2.6057) (xy 4.5586 -2.6044) (xy 4.5658 -2.6035) (xy 4.5732 -2.6029)
						(xy 4.5807 -2.6027) (xy 5.1139 -2.6027) (xy 5.1214 -2.6029)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
				(polyline
					(pts
						(xy 5.1214 -2.6202) (xy 5.1288 -2.6208) (xy 5.136 -2.6217) (xy 5.1432 -2.623) (xy 5.1502 -2.6246)
						(xy 5.1571 -2.6266) (xy 5.1639 -2.6288) (xy 5.1705 -2.6314) (xy 5.1769 -2.6344) (xy 5.1832 -2.6376)
						(xy 5.1893 -2.6411) (xy 5.1952 -2.6449) (xy 5.2009 -2.6489) (xy 5.2064 -2.6532) (xy 5.2117 -2.6578)
						(xy 5.2167 -2.6626) (xy 5.2215 -2.6677) (xy 5.2261 -2.673) (xy 5.2304 -2.6784) (xy 5.2345 -2.6841)
						(xy 5.2383 -2.69) (xy 5.2418 -2.6961) (xy 5.245 -2.7024) (xy 5.2479 -2.7088) (xy 5.2505 -2.7155)
						(xy 5.2528 -2.7222) (xy 5.2547 -2.7291) (xy 5.2564 -2.7361) (xy 5.2576 -2.7433) (xy 5.2586 -2.7506)
						(xy 5.2591 -2.758) (xy 5.2593 -2.7654) (xy 5.2593 -3.2986) (xy 5.2591 -3.3061) (xy 5.2586 -3.3135)
						(xy 5.2576 -3.3208) (xy 5.2564 -3.3279) (xy 5.2547 -3.335) (xy 5.2528 -3.3419) (xy 5.2505 -3.3486)
						(xy 5.2479 -3.3552) (xy 5.245 -3.3617) (xy 5.2418 -3.3679) (xy 5.2383 -3.374) (xy 5.2345 -3.3799)
						(xy 5.2304 -3.3856) (xy 5.2261 -3.3911) (xy 5.2215 -3.3964) (xy 5.2167 -3.4015) (xy 5.2117 -3.4063)
						(xy 5.2064 -3.4108) (xy 5.2009 -3.4152) (xy 5.1952 -3.4192) (xy 5.1893 -3.423) (xy 5.1832 -3.4265)
						(xy 5.1769 -3.4297) (xy 5.1705 -3.4326) (xy 5.1639 -3.4352) (xy 5.1571 -3.4375) (xy 5.1502 -3.4395)
						(xy 5.1432 -3.4411) (xy 5.136 -3.4424) (xy 5.1288 -3.4433) (xy 5.1214 -3.4439) (xy 5.1139 -3.4441)
						(xy 4.5807 -3.4441) (xy 4.5732 -3.4439) (xy 4.5658 -3.4433) (xy 4.5586 -3.4424) (xy 4.5514 -3.4411)
						(xy 4.5444 -3.4395) (xy 4.5375 -3.4375) (xy 4.5307 -3.4352) (xy 4.5241 -3.4326) (xy 4.5177 -3.4297)
						(xy 4.5114 -3.4265) (xy 4.5053 -3.423) (xy 4.4994 -3.4192) (xy 4.4937 -3.4152) (xy 4.4882 -3.4108)
						(xy 4.4829 -3.4063) (xy 4.4779 -3.4015) (xy 4.4731 -3.3964) (xy 4.4685 -3.3911) (xy 4.4642 -3.3856)
						(xy 4.4601 -3.3799) (xy 4.4563 -3.374) (xy 4.4528 -3.3679) (xy 4.4496 -3.3617) (xy 4.4467 -3.3552)
						(xy 4.4441 -3.3486) (xy 4.4418 -3.3419) (xy 4.4399 -3.335) (xy 4.4382 -3.3279) (xy 4.437 -3.3208)
						(xy 4.436 -3.3135) (xy 4.4355 -3.3061) (xy 4.4353 -3.2986) (xy 4.4353 -2.7654) (xy 4.4355 -2.758)
						(xy 4.436 -2.7506) (xy 4.437 -2.7433) (xy 4.4382 -2.7361) (xy 4.4399 -2.7291) (xy 4.4418 -2.7222)
						(xy 4.4441 -2.7155) (xy 4.4467 -2.7088) (xy 4.4496 -2.7024) (xy 4.4528 -2.6961) (xy 4.4563 -2.69)
						(xy 4.4601 -2.6841) (xy 4.4642 -2.6784) (xy 4.4685 -2.673) (xy 4.4731 -2.6677) (xy 4.4779 -2.6626)
						(xy 4.4829 -2.6578) (xy 4.4882 -2.6532) (xy 4.4937 -2.6489) (xy 4.4994 -2.6449) (xy 4.5053 -2.6411)
						(xy 4.5114 -2.6376) (xy 4.5177 -2.6344) (xy 4.5241 -2.6314) (xy 4.5307 -2.6288) (xy 4.5375 -2.6266)
						(xy 4.5444 -2.6246) (xy 4.5514 -2.623) (xy 4.5586 -2.6217) (xy 4.5658 -2.6208) (xy 4.5732 -2.6202)
						(xy 4.5807 -2.62) (xy 5.1139 -2.62) (xy 5.1214 -2.6202)
					)
					(stroke
						(width -0.0001)
						(type solid)
					)
					(fill
						(type outline)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "nordic-lib-kicad-npm:nPM1300-QEXX"
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at 0 0 0)
				(do_not_autoplace)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "nPM1300-QEXX"
				(at 0 -2.54 0)
				(do_not_autoplace)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" "Package_DFN_QFN:QFN-32-1EP_5x5mm_P0.5mm_EP3.6x3.6mm_ThermalVias"
				(at 0 53.34 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "https://docs.nordicsemi.com/bundle/ps_npm1300"
				(at 0 55.88 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "PMIC, LED Driver, Battery Charger, QFN-32"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "PMIC Regulator LED Driver Battery Charger"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "QFN*32*1EP*5x5mm*P0.5mm*EP3.6x3.6mm*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "nPM1300-QEXX_0_0"
				(pin power_in line
					(at -35.56 33.02 0)
					(length 5.08)
					(name "VBUS"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "21"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -35.56 20.32 0)
					(length 5.08)
					(name "CC1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "23"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -35.56 17.78 0)
					(length 5.08)
					(name "CC2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "24"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin open_collector line
					(at -35.56 12.7 0)
					(length 5.08)
					(name "LED0"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "25"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin open_collector line
					(at -35.56 10.16 0)
					(length 5.08)
					(name "LED1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "26"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin open_collector line
					(at -35.56 7.62 0)
					(length 5.08)
					(name "LED2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "27"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -35.56 2.54 0)
					(length 5.08)
					(name "GPIO0"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -35.56 0 0)
					(length 5.08)
					(name "GPIO1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -35.56 -2.54 0)
					(length 5.08)
					(name "GPIO2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -35.56 -5.08 0)
					(length 5.08)
					(name "GPIO3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "10"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -35.56 -7.62 0)
					(length 5.08)
					(name "GPIO4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "11"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -35.56 -12.7 0)
					(length 5.08)
					(name "SDA"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "13"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin bidirectional line
					(at -35.56 -15.24 0)
					(length 5.08)
					(name "SCL"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "14"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -35.56 -20.32 0)
					(length 5.08)
					(name "VSET1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "17"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -35.56 -22.86 0)
					(length 5.08)
					(name "VSET2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "16"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at -35.56 -27.94 0)
					(length 5.08)
					(name "SHPHLD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "15"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at -2.54 40.64 270)
					(length 5.08)
					(name "VDDIO"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "12"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 0 40.64 270)
					(length 5.08)
					(name "PVDD"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 0 -40.64 90)
					(length 5.08)
					(name "AVSS"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "33"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_out line
					(at 2.54 40.64 270)
					(length 5.08)
					(name "VSYS"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "20"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_out line
					(at 35.56 33.02 180)
					(length 5.08)
					(name "VBUSOUT"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "22"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_out line
					(at 35.56 17.78 180)
					(length 5.08)
					(name "VOUT1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 35.56 12.7 180)
					(length 5.08)
					(name "SW1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_out line
					(at 35.56 5.08 180)
					(length 5.08)
					(name "VOUT2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "32"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 35.56 0 180)
					(length 5.08)
					(name "SW2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin output line
					(at 35.56 -10.16 180)
					(length 5.08)
					(name "LSOUT1/VOUTLDO1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "29"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at 35.56 -15.24 180)
					(length 5.08)
					(name "LSIN2/VINLDO2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "30"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin output line
					(at 35.56 -17.78 180)
					(length 5.08)
					(name "LSOUT2/VOUTLDO2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "31"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 35.56 -30.48 180)
					(length 5.08)
					(name "VBAT"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "19"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 35.56 -33.02 180)
					(length 5.08)
					(name "NTC"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "18"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "nPM1300-QEXX_1_0"
				(pin power_in line
					(at 35.56 10.16 180)
					(length 5.08)
					(name "PVSS1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin power_in line
					(at 35.56 -2.54 180)
					(length 5.08)
					(name "PVSS2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at 35.56 -7.62 180)
					(length 5.08)
					(name "LSIN1/VINLDO1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "28"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "nPM1300-QEXX_1_1"
				(rectangle
					(start -30.48 35.56)
					(end 30.48 -35.56)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type background)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:+BATT"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "+BATT"
				(at 0 3.556 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"+BATT\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power battery"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "+BATT_0_1"
				(polyline
					(pts
						(xy -0.762 1.27) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 2.54) (xy 0.762 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "+BATT_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:GND"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "GND"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "GND_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "GND_1_1"
				(pin power_in line
					(at 0 0 270)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:VBUS"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "VBUS"
				(at 0 3.556 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"VBUS\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VBUS_0_1"
				(polyline
					(pts
						(xy -0.762 1.27) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 2.54) (xy 0.762 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "VBUS_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:VDD"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "VDD"
				(at 0 3.556 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"VDD\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VDD_0_1"
				(polyline
					(pts
						(xy -0.762 1.27) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 2.54) (xy 0.762 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "VDD_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
	)
	(junction
		(at 184.15 109.22)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "23ce1e29-98b8-40a3-b8a5-b82ea7b26250")
	)
	(junction
		(at 168.91 52.07)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "389a1345-7c1e-454f-bd79-685d78121f95")
	)
	(junction
		(at 179.07 52.07)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "4284af87-175e-4e84-9d43-cc93c4996bd7")
	)
	(junction
		(at 214.63 99.06)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "46c88b4c-ff1d-4077-b9f3-fa409cf6d373")
	)
	(junction
		(at 194.31 129.54)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "4aa5d27c-2dfd-4863-90d9-9b8d5828a5ee")
	)
	(junction
		(at 194.31 134.62)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "4e55eda9-3ea1-4ee9-a47a-d09b03b4f03e")
	)
	(junction
		(at 146.05 46.99)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "603c5409-35b7-4441-b446-3e136f676a41")
	)
	(junction
		(at 203.2 93.98)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "657d1502-95dc-48f9-9664-eb88e7bc5160")
	)
	(junction
		(at 158.75 46.99)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "6e3da4c0-7344-4ee4-8ed4-b77ae4b5ffd2")
	)
	(junction
		(at 179.07 46.99)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "9af90d59-2603-49a0-bf30-f94d10af999d")
	)
	(junction
		(at 194.31 66.04)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "a41ea912-806b-44c3-aa52-c4ca8e3e4510")
	)
	(junction
		(at 168.91 46.99)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "b29dc78a-b3e4-4f1e-90d3-182d3dccd2d2")
	)
	(junction
		(at 214.63 93.98)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "ce4300b3-ec24-4fa5-b0d7-8b07dd002338")
	)
	(junction
		(at 184.15 114.3)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "db492518-c161-4847-a9d2-c18b464c46f0")
	)
	(junction
		(at 151.13 58.42)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "e0deb824-c74e-4e5b-86bf-1f095ef3b5c5")
	)
	(junction
		(at 184.15 116.84)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "f2588493-6c77-46be-aa04-698aa4aaf1c9")
	)
	(no_connect
		(at 184.15 86.36)
		(uuid "0c565ea1-ca24-4fd6-95d2-13b3c9e69c6f")
	)
	(no_connect
		(at 113.03 101.6)
		(uuid "691ed4bb-5329-47c9-80eb-d017cf7c2966")
	)
	(no_connect
		(at 113.03 106.68)
		(uuid "9832a1d1-732d-4747-8ff6-08e6e370948c")
	)
	(no_connect
		(at 113.03 104.14)
		(uuid "9d5fda61-6218-49c1-9272-d06576add9b0")
	)
	(bus_entry
		(at 254 26.67)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0198e40a-4abe-4a22-a880-f720cc00c71e")
	)
	(bus_entry
		(at 254 29.21)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "38a6e0d9-05bb-4b5b-9c6e-57920f76296d")
	)
	(bus_entry
		(at 254 36.83)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "3cb84673-ea9e-4761-866e-528f24bf76bf")
	)
	(bus_entry
		(at 254 31.75)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "8d9c69a8-46e3-4c66-befd-61599b965fe4")
	)
	(bus_entry
		(at 254 41.91)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ccd48c70-0618-4317-bb6e-87825d279767")
	)
	(bus_entry
		(at 254 39.37)
		(size 2.54 -2.54)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "db108d7c-c952-43d1-94dc-a6260240637c")
	)
	(wire
		(pts
			(xy 190.5 86.36) (xy 187.96 86.36)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "029adc35-2c90-4870-b7ca-bddf4c3bb2c8")
	)
	(bus
		(pts
			(xy 256.54 39.37) (xy 256.54 36.83)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "03b2e208-c960-4c40-ad15-d7f881146dbf")
	)
	(wire
		(pts
			(xy 184.15 106.68) (xy 184.15 109.22)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "076a5ca6-ea0b-4d74-8b05-da949aa63e40")
	)
	(wire
		(pts
			(xy 254 29.21) (xy 252.73 29.21)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "15325889-1fa1-4db5-8234-279460a715cb")
	)
	(wire
		(pts
			(xy 254 41.91) (xy 252.73 41.91)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1eab8988-5538-4686-8e2b-2e2480a31f64")
	)
	(wire
		(pts
			(xy 168.91 46.99) (xy 179.07 46.99)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1f279db3-75cf-4c74-b3c1-f62fb73f7a45")
	)
	(wire
		(pts
			(xy 220.98 93.98) (xy 214.63 93.98)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "21d3e9da-197d-4761-a6fd-446c0befbd03")
	)
	(wire
		(pts
			(xy 194.31 66.04) (xy 184.15 66.04)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "265b069a-5730-4e33-b076-1e3be3eac064")
	)
	(wire
		(pts
			(xy 187.96 88.9) (xy 184.15 88.9)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2973a50a-51e8-4351-a879-03e27e338831")
	)
	(wire
		(pts
			(xy 113.03 83.82) (xy 113.03 86.36)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2afe467f-0c10-4ade-9f9f-6e6d455bb62e")
	)
	(wire
		(pts
			(xy 187.96 86.36) (xy 187.96 88.9)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2f3d6553-b9a4-4a47-8c8f-ea80513b1861")
	)
	(wire
		(pts
			(xy 151.13 46.99) (xy 158.75 46.99)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "38547644-78d3-43c2-bc4f-a68fb118ea93")
	)
	(wire
		(pts
			(xy 100.33 121.92) (xy 113.03 121.92)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "479e0deb-d28f-4b50-8544-4bb61abc9111")
	)
	(bus
		(pts
			(xy 256.54 26.67) (xy 256.54 29.21)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4a09b729-9599-49b7-884a-87ee929aa7c2")
	)
	(wire
		(pts
			(xy 101.6 66.04) (xy 113.03 66.04)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4fb582c7-777e-42b8-b791-82cdfbd24342")
	)
	(wire
		(pts
			(xy 184.15 132.08) (xy 184.15 134.62)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "50f0a2dc-14e4-48cb-98af-857030a69e00")
	)
	(wire
		(pts
			(xy 99.06 93.98) (xy 113.03 93.98)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "51ecafef-8977-4143-856c-4fae65640d45")
	)
	(wire
		(pts
			(xy 113.03 93.98) (xy 113.03 91.44)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5a0e6091-29ce-443a-bcc5-584a28de2eb4")
	)
	(wire
		(pts
			(xy 184.15 114.3) (xy 184.15 116.84)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5cd29a03-93fd-40d8-8209-30a47c707704")
	)
	(wire
		(pts
			(xy 184.15 109.22) (xy 184.15 114.3)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "61801fea-1a47-457d-abc4-65e68bb2b53e")
	)
	(wire
		(pts
			(xy 168.91 52.07) (xy 179.07 52.07)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "64fe3061-542b-43d1-ab39-09401d8590f3")
	)
	(wire
		(pts
			(xy 151.13 46.99) (xy 151.13 58.42)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "70976c10-8fe6-47ef-b22b-8d4f6fcb7671")
	)
	(wire
		(pts
			(xy 234.95 93.98) (xy 228.6 93.98)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "70ccde2d-9c17-4b46-9773-6ae5bd34e11b")
	)
	(wire
		(pts
			(xy 254 36.83) (xy 252.73 36.83)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "758f4148-ccf7-4ab4-be9d-0eea296955c8")
	)
	(wire
		(pts
			(xy 254 39.37) (xy 252.73 39.37)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "7e75e8ea-4b59-43de-8bf2-d6f09a957940")
	)
	(wire
		(pts
			(xy 254 26.67) (xy 252.73 26.67)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "81d1bb0f-d9bb-4bd7-adab-da18657a3500")
	)
	(wire
		(pts
			(xy 146.05 46.99) (xy 146.05 58.42)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "827f7bce-3613-47ba-afa3-d817ec0722f2")
	)
	(wire
		(pts
			(xy 158.75 52.07) (xy 168.91 52.07)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "86268aaf-1808-4010-8bf8-14bb03aba1a3")
	)
	(wire
		(pts
			(xy 146.05 46.99) (xy 140.97 46.99)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "93e4ffd4-73f9-4aa8-89bc-c5975d67c2c7")
	)
	(wire
		(pts
			(xy 158.75 46.99) (xy 168.91 46.99)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a6fadff1-f958-4980-a7ce-53b0799eee64")
	)
	(wire
		(pts
			(xy 254 31.75) (xy 252.73 31.75)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a736d8b2-9e2e-4a95-a989-f86af19c9a4c")
	)
	(wire
		(pts
			(xy 184.15 134.62) (xy 194.31 134.62)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "aa36ea11-e185-4321-b4d1-4404b9d4da59")
	)
	(wire
		(pts
			(xy 210.82 101.6) (xy 210.82 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ae83084f-68c9-4b41-80fc-a5211bab0095")
	)
	(wire
		(pts
			(xy 194.31 66.04) (xy 194.31 67.31)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "aebd64e5-7228-41df-9429-0ac4b052c637")
	)
	(wire
		(pts
			(xy 184.15 129.54) (xy 194.31 129.54)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b071585f-9b71-49e7-878c-9911619d41a5")
	)
	(wire
		(pts
			(xy 184.15 101.6) (xy 210.82 101.6)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b2c18393-df9d-4fad-b8c2-9d4bce770138")
	)
	(bus
		(pts
			(xy 256.54 24.13) (xy 256.54 26.67)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b67283f6-b4c3-4b0c-8f62-31977c6e3cb9")
	)
	(wire
		(pts
			(xy 210.82 99.06) (xy 214.63 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "bce572e9-6190-40ce-b3e4-6b14e7598855")
	)
	(wire
		(pts
			(xy 99.06 83.82) (xy 113.03 83.82)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ca25cef7-fbe2-4223-9538-f89da0c13ece")
	)
	(wire
		(pts
			(xy 88.9 119.38) (xy 113.03 119.38)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d6b6a39b-b4e5-482b-aab7-f4b5e50992ab")
	)
	(wire
		(pts
			(xy 184.15 93.98) (xy 203.2 93.98)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "db522382-e2d1-403a-8d43-ee3897893018")
	)
	(wire
		(pts
			(xy 203.2 93.98) (xy 214.63 93.98)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e5f075eb-32da-49b6-be97-643d784649e7")
	)
	(wire
		(pts
			(xy 99.06 88.9) (xy 113.03 88.9)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e7a7ab01-bca2-41e9-ab25-8105dbd95753")
	)
	(wire
		(pts
			(xy 184.15 99.06) (xy 203.2 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "ef994083-55f9-4186-8b9c-9369b152d152")
	)
	(bus
		(pts
			(xy 256.54 36.83) (xy 256.54 34.29)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f6625e4c-308c-4080-b0bd-63f18743c5a4")
	)
	(wire
		(pts
			(xy 151.13 58.42) (xy 148.59 58.42)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f8a2bda1-99c8-45d3-887b-f48d957baa1b")
	)
	(label "VSYS"
		(at 184.15 81.28 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "0947f6d2-e286-44f4-abdb-38f1db7e26bb")
	)
	(label "NPM1300.INT"
		(at 252.73 41.91 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "198815af-e14c-4588-a3f1-d911df81130d")
	)
	(label "NPM1300.SCL"
		(at 113.03 114.3 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "1a0dd48c-**************-2384f38bf0f6")
	)
	(label "NPM1300.SDA"
		(at 113.03 111.76 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "1cc6e438-ce58-46cb-bc0a-b961f337deca")
	)
	(label "USB.CC1"
		(at 252.73 29.21 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "25c882ef-02dc-435b-b014-162f62f780e2")
	)
	(label "USB.VBUS"
		(at 101.6 66.04 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "2a8e8fc6-4e1f-47c0-abd3-544b14644b0f")
	)
	(label "USB.CC2"
		(at 113.03 81.28 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "41a1065b-ff6a-4858-b14d-472526904f0f")
	)
	(label "VSYS"
		(at 151.13 46.99 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "5fb83af3-ebc2-4b49-907c-e539f86a843d")
	)
	(label "NPM1300.SDA"
		(at 252.73 36.83 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "65cca1b4-e034-48c0-b7ce-70b9e206bd39")
	)
	(label "USB.VBUS"
		(at 252.73 26.67 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "6ed6347a-c657-462c-b339-16c2a59d31e7")
	)
	(label "NPM1300.SCL"
		(at 252.73 39.37 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "717333d9-e805-42bd-bb07-f92ced647cca")
	)
	(label "NPM1300.INT"
		(at 113.03 96.52 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "7d6bc70b-fd6b-4fb3-9307-4807a02ec3a8")
	)
	(label "USB.CC1"
		(at 113.03 78.74 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "bdaaa43a-827b-4e08-b21f-d2f7904591e7")
	)
	(label "USB.CC2"
		(at 252.73 31.75 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "cf4ab313-dee7-4305-908e-7f6d738377ba")
	)
	(label "VSYS"
		(at 88.9 88.9 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right bottom)
		)
		(uuid "fc67fdbc-2085-454f-857c-a8741b515058")
	)
	(hierarchical_label "USB{VBUS, CC1, CC2}"
		(shape input)
		(at 256.54 24.13 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "0d441ce0-dfe4-4e3f-9e5c-b38c4488fdd7")
	)
	(hierarchical_label "ENABLE"
		(shape input)
		(at 113.03 99.06 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "40306335-f2e6-4df9-9602-b23d12e297b4")
	)
	(hierarchical_label "SHPLD"
		(shape input)
		(at 113.03 127 180)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "506d54cd-2ba2-47f9-ab5e-e27b23165002")
	)
	(hierarchical_label "NPM1300{SCL, SDA, INT}"
		(shape input)
		(at 256.54 34.29 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "f74db474-47d0-4227-90de-7ae68913ab20")
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 194.31 132.08 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "002b7315-8988-4ee2-ae3d-30d178a1468c")
		(property "Reference" "C11"
			(at 196.85 130.8162 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "2.2uF"
			(at 196.85 133.3562 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0603_1608Metric"
			(at 194.31 132.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 194.31 132.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 194.31 132.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C23630"
			(at 194.31 132.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "45ce8801-c676-4130-9ba2-d110624dbdee")
		)
		(pin "2"
			(uuid "b0045d4b-fe54-45d2-8ae1-f0e8a62161b9")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "C11")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VBUS")
		(at 194.31 66.04 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "08a04004-6015-4dc6-b014-669308e88c02")
		(property "Reference" "#PWR041"
			(at 194.31 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VBUS"
			(at 194.31 60.96 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 194.31 66.04 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 194.31 66.04 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VBUS\""
			(at 194.31 66.04 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "63565d86-b381-44d1-b10e-19ebb2e67378")
		)
		(instances
			(project ""
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR041")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:L_Small")
		(at 203.2 96.52 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "1019e531-ce12-421f-ada7-04ec0c4a3a8f")
		(property "Reference" "L1"
			(at 204.47 95.2499 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "2.2uH"
			(at 204.47 97.7899 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Inductor_SMD:L_0603_1608Metric"
			(at 203.2 96.52 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 203.2 96.52 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Inductor, small symbol"
			(at 203.2 96.52 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C337910"
			(at 203.2 96.52 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "bc9d203a-0c7e-475c-a079-3fcfc23dd939")
		)
		(pin "1"
			(uuid "454f5a73-a9c6-4135-8c31-425dce2dcd7d")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "L1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 194.31 134.62 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "20f097d2-86e9-4b15-8c8e-22f50fb9589b")
		(property "Reference" "#PWR033"
			(at 194.31 140.97 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 194.31 139.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 194.31 134.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 194.31 134.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 194.31 134.62 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "86ee7251-33b9-43ac-9f64-acea346d8ca8")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR033")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Graphic:Logo_Open_Hardware_Small")
		(at 278.13 172.72 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom no)
		(on_board no)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "281f9912-a861-41c5-beee-c7f893371e05")
		(property "Reference" "#SYM4"
			(at 278.13 165.735 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "Logo_Open_Hardware_Small"
			(at 278.13 178.435 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 278.13 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 278.13 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 278.13 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#SYM4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VDD")
		(at 234.95 93.98 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "28f710ab-0b59-445e-b9e6-f7317e8f157b")
		(property "Reference" "#PWR021"
			(at 234.95 97.79 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VDD"
			(at 234.95 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 234.95 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 234.95 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VDD\""
			(at 234.95 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "0b772ea6-d5dd-45c9-993e-45fbae29aec6")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR021")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 179.07 52.07 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "2f39c33b-3ba7-4fb3-a783-a7aa42b48f76")
		(property "Reference" "#PWR017"
			(at 179.07 58.42 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 179.07 57.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 179.07 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 179.07 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 179.07 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "8d096010-8b1e-454e-b7c7-c9f9a6eff698")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR017")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 101.6 68.58 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "494fa412-5ea7-4e84-95d6-f01d13dabe10")
		(property "Reference" "C5"
			(at 99.06 67.3162 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "1uF"
			(at 99.06 69.8562 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0603_1608Metric"
			(at 101.6 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 101.6 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 101.6 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C15849"
			(at 101.6 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "de91c931-bc15-492e-8f50-fb1f4c88380b")
		)
		(pin "2"
			(uuid "ee00d126-dd2b-4389-87ab-3885761e3a13")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "C5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 184.15 116.84 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "5d4796c5-581e-4e62-8d8a-d07f43b132a1")
		(property "Reference" "#PWR030"
			(at 184.15 123.19 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 184.15 121.92 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 184.15 116.84 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 184.15 116.84 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 184.15 116.84 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "ceeceffc-8425-4e37-8f1d-b290fa45f071")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR030")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "LordsBoards-Graphic:LordsBoardsLogo")
		(at 185.42 172.72 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom no)
		(on_board no)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "66151128-5b68-45f8-8cc2-d6d72c6c77d0")
		(property "Reference" "#SYM3"
			(at 185.42 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "~"
			(at 185.42 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 185.42 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 185.42 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 185.42 172.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#SYM3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VBUS")
		(at 179.07 46.99 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "6c494467-b231-4010-aac6-a32292331859")
		(property "Reference" "#PWR053"
			(at 179.07 50.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VSYS"
			(at 179.07 41.91 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 179.07 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 179.07 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VBUS\""
			(at 179.07 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "22faac76-cca5-4da5-955c-a5fd2cc36b07")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR053")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 214.63 96.52 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "7a91e346-0d48-41bd-bc53-0810db001bbe")
		(property "Reference" "C8"
			(at 217.17 95.2562 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "10uF"
			(at 217.17 97.7962 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0603_1608Metric"
			(at 214.63 96.52 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 214.63 96.52 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 214.63 96.52 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C96446"
			(at 214.63 96.52 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b71e8ee5-02a1-4052-9d69-d78ad2c3778c")
		)
		(pin "2"
			(uuid "57869202-8a7a-4f1c-822f-8024a264d41a")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "C8")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 214.63 99.06 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "7cd8da8a-b8eb-4e84-a77e-e3a6f42a7338")
		(property "Reference" "#PWR022"
			(at 214.63 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 214.63 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 214.63 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 214.63 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 214.63 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "11a7435f-6759-45c9-a2d7-acb661ece161")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR022")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:LED_ARGB")
		(at 93.98 88.9 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "8dd7bfd3-c341-4c78-8554-a732fd8363fa")
		(property "Reference" "D3"
			(at 93.98 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "LED_ARGB"
			(at 93.98 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "LED_SMD:LED_Wurth_150044M155260"
			(at 93.98 90.17 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 93.98 90.17 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "RGB LED, anode/red/green/blue"
			(at 93.98 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C404280"
			(at 93.98 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "f1a3bdd8-3c82-4d35-ba24-80aec6bd8bc3")
		)
		(pin "2"
			(uuid "9bc278d3-2972-4071-a729-bd9a735a4d50")
		)
		(pin "3"
			(uuid "f54136dc-812f-4fc3-b241-30eb40e751fa")
		)
		(pin "4"
			(uuid "596c3b6b-f191-4db6-9eef-572959abe334")
		)
		(instances
			(project ""
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "D3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 168.91 49.53 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "8e833882-89ac-4e52-a273-5a2d07c5626d")
		(property "Reference" "C3"
			(at 171.45 48.2662 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "10uF"
			(at 171.45 50.8062 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0603_1608Metric"
			(at 168.91 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 168.91 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 168.91 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C96446"
			(at 168.91 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "a24df6d1-11ed-4d9b-8192-4c4274ea28f5")
		)
		(pin "2"
			(uuid "ae7a9fc3-e174-403a-b6be-c36c0142c923")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "C3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 101.6 71.12 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "921d6704-1028-4af9-b945-87f3b016a592")
		(property "Reference" "#PWR018"
			(at 101.6 77.47 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 101.6 76.2 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 101.6 71.12 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 101.6 71.12 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 101.6 71.12 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "db1c8f35-2d71-4459-a6d8-b82ded6fb0b1")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR018")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 88.9 119.38 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "9440c911-0865-486f-b422-cee989e8cc2d")
		(property "Reference" "#PWR031"
			(at 88.9 125.73 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 88.9 124.46 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 88.9 119.38 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 88.9 119.38 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 88.9 119.38 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "516c92de-4de8-4698-b80b-03b93d37c08b")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR031")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 179.07 49.53 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "9720a1d0-3266-4bac-9c2b-4023ea1ca012")
		(property "Reference" "C4"
			(at 181.61 48.2662 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "10uF"
			(at 181.61 50.8062 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0603_1608Metric"
			(at 179.07 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 179.07 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 179.07 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C96446"
			(at 179.07 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "a61089e5-2a74-4025-bdeb-102b6046fc69")
		)
		(pin "2"
			(uuid "5f3a0828-76e1-48ae-9c42-af1bfb1c0f12")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "C4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:+BATT")
		(at 194.31 129.54 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a05991b8-b8a3-406d-8df9-93621badde22")
		(property "Reference" "#PWR035"
			(at 194.31 133.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+BATT"
			(at 194.31 124.46 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 194.31 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 194.31 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"+BATT\""
			(at 194.31 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "578524fb-2001-41c1-b7d2-e5ce653c2daf")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR035")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 194.31 69.85 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "b0be1fc9-71be-4847-ac6a-7788b9466804")
		(property "Reference" "C6"
			(at 196.85 68.5862 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "1uF"
			(at 196.85 71.1262 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0603_1608Metric"
			(at 194.31 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 194.31 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 194.31 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C15849"
			(at 194.31 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "30a83199-c521-44df-a95c-d4d695194e58")
		)
		(pin "2"
			(uuid "4d65e1b2-6f01-44e3-854a-a66d55a61146")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "C6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 158.75 49.53 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "bfcc6c51-f5a8-438b-acf8-093cf5a52440")
		(property "Reference" "C2"
			(at 161.29 48.2662 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "10uF"
			(at 161.29 50.8062 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0603_1608Metric"
			(at 158.75 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 158.75 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 158.75 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C96446"
			(at 158.75 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "c609e526-f097-49d9-9551-edfdce665c5a")
		)
		(pin "2"
			(uuid "a5c75b84-86c5-4e05-bb4e-10c7c168b50b")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "C2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 140.97 52.07 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "c216b80a-02e4-43c4-af37-9564aaa73724")
		(property "Reference" "#PWR016"
			(at 140.97 58.42 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 140.97 57.15 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 140.97 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 140.97 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 140.97 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "a3416b4d-af4d-49b4-ab82-21bb6b2b5f9e")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR016")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 148.59 139.7 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "c2346cb2-ad79-437a-b333-b20dff36b150")
		(property "Reference" "#PWR034"
			(at 148.59 146.05 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 148.59 144.78 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 148.59 139.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 148.59 139.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 148.59 139.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "116f78c9-7cc5-4776-a44a-800680967ba2")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR034")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "nordic-lib-kicad-npm:nPM1300-QEXX")
		(at 148.59 99.06 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "c5ab4340-54f4-44fe-a595-1ad823512311")
		(property "Reference" "U2"
			(at 148.59 99.06 0)
			(do_not_autoplace yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "nPM1300-QEXX"
			(at 148.59 101.6 0)
			(do_not_autoplace yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Package_DFN_QFN:QFN-32-1EP_5x5mm_P0.5mm_EP3.6x3.6mm_ThermalVias"
			(at 148.59 45.72 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "https://docs.nordicsemi.com/bundle/ps_npm1300"
			(at 148.59 43.18 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "PMIC, LED Driver, Battery Charger, QFN-32"
			(at 148.59 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "********"
			(at 148.59 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "14"
			(uuid "f08ce449-99e5-4e73-9522-31485d720b3f")
		)
		(pin "29"
			(uuid "52fd1184-daea-4329-8364-456767b72c04")
		)
		(pin "8"
			(uuid "6a22aa35-b4af-43a5-a617-4fa4584491a6")
		)
		(pin "19"
			(uuid "16899ae8-a843-4409-8b8f-fbdd1bcd2032")
		)
		(pin "20"
			(uuid "5e3739b8-0c3b-4501-a5d0-e868cb253102")
		)
		(pin "1"
			(uuid "af7b07f8-f790-4fb1-8943-0f686b09a6b7")
		)
		(pin "9"
			(uuid "fb996d7c-48fc-4d14-9578-5988cb740224")
		)
		(pin "33"
			(uuid "f9798299-aa4b-4117-af5d-fd2ff4ec0df5")
		)
		(pin "27"
			(uuid "c6f80806-bf66-45f5-8786-8c675b9ce9ed")
		)
		(pin "30"
			(uuid "141ff870-1195-4bd5-b903-a0c6bdf85c88")
		)
		(pin "4"
			(uuid "b24a014b-6d00-4411-9012-ba581f237627")
		)
		(pin "26"
			(uuid "78bc0655-cb5e-4352-8546-e370682426ca")
		)
		(pin "17"
			(uuid "68746d35-1416-47e7-87e9-849e6c2279dc")
		)
		(pin "24"
			(uuid "3d052af6-b6d3-4102-a986-330a2a3ac592")
		)
		(pin "32"
			(uuid "4e00509d-21ee-4a70-bf9e-bb598f70d10b")
		)
		(pin "25"
			(uuid "b429a4b7-cc59-4b12-a055-fb4f3f4d9732")
		)
		(pin "7"
			(uuid "bd242526-54b5-4a6d-b279-85298bf426c6")
		)
		(pin "10"
			(uuid "9a2d9857-d4e7-4782-9254-2ee4c239dbec")
		)
		(pin "22"
			(uuid "7f26435a-0957-4e2c-b287-fee111eb17f5")
		)
		(pin "21"
			(uuid "8fa6e51e-e780-4ffb-b79b-1b6fdbf0423f")
		)
		(pin "5"
			(uuid "e559e716-f3b2-4c40-b723-15f5520cc472")
		)
		(pin "31"
			(uuid "06d226ab-43af-492c-9e4b-a802841388d9")
		)
		(pin "18"
			(uuid "10d91a13-b7e3-463d-9d0c-e892245cd688")
		)
		(pin "2"
			(uuid "3949241f-17d5-4a72-bdc6-8311c5b72636")
		)
		(pin "28"
			(uuid "43ce19bb-6507-47bc-b526-d3db8a421e2b")
		)
		(pin "6"
			(uuid "c4c2c394-8114-427b-ad80-73fcabbfcea0")
		)
		(pin "3"
			(uuid "9f06edfd-de58-4631-9db8-a3ffae5aaf7d")
		)
		(pin "12"
			(uuid "8cc4757e-acea-4b5c-b1f1-b5f996358140")
		)
		(pin "13"
			(uuid "e5ad3c38-1e71-45ea-bc2c-1616d4520412")
		)
		(pin "15"
			(uuid "0aefeaa1-6665-41dc-8275-604d74820069")
		)
		(pin "23"
			(uuid "f1b8c687-c3aa-4c72-b9df-9ca18ffb1a25")
		)
		(pin "16"
			(uuid "815697d6-7374-4afd-aed8-4a3b70eca615")
		)
		(pin "11"
			(uuid "1f7eeaef-3708-4c2e-8567-35790ad2fc92")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "U2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C_Small")
		(at 140.97 49.53 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "c8e8bba0-79ed-43f9-8c49-654bbd93d4e5")
		(property "Reference" "C1"
			(at 138.43 48.2662 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "100nF"
			(at 138.43 50.8062 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0402_1005Metric"
			(at 140.97 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 140.97 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Unpolarized capacitor, small symbol"
			(at 140.97 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C307331"
			(at 140.97 49.53 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "7d017a93-07cc-48ff-b6c0-cee15000ed17")
		)
		(pin "2"
			(uuid "e8726e74-389b-4232-8ed9-79a481486a60")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "C1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VDD")
		(at 146.05 46.99 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "d3be2580-c387-42e6-b11a-e94848336059")
		(property "Reference" "#PWR015"
			(at 146.05 50.8 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VDD"
			(at 146.05 41.91 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 146.05 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 146.05 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VDD\""
			(at 146.05 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "c2345d3e-650d-45d6-8d15-b6b285ca192c")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR015")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Jumper:SolderJumper_2_Bridged")
		(at 224.79 93.98 0)
		(unit 1)
		(exclude_from_sim yes)
		(in_bom no)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "d41cf84c-a86c-4f10-a6f3-60bfcca61f4c")
		(property "Reference" "JP1"
			(at 224.79 87.63 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "SolderJumper_2_Bridged"
			(at 224.79 90.17 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Footprint" "Jumper:SolderJumper-2_P1.3mm_Bridged_RoundedPad1.0x1.5mm"
			(at 224.79 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 224.79 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Solder Jumper, 2-pole, closed/bridged"
			(at 224.79 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "8c054d7e-48d3-4feb-ba6e-3e641b849b34")
		)
		(pin "1"
			(uuid "f3996ed5-9c3a-406c-9fba-0bd7298837d5")
		)
		(instances
			(project ""
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "JP1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R_Small_US")
		(at 100.33 124.46 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "d56309dc-d750-4e7e-847f-38cd9352ec17")
		(property "Reference" "R4"
			(at 97.79 123.1899 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "330kR"
			(at 97.79 125.7299 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 100.33 124.46 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 100.33 124.46 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Resistor, small US symbol"
			(at 100.33 124.46 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "LCSC" "C25778"
			(at 100.33 124.46 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "e4845884-74c6-44cd-86d0-************")
		)
		(pin "1"
			(uuid "1b7f81a3-bb41-4530-a48a-0e7d158a90c6")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "R4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 190.5 86.36 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "e4cbba04-00fd-49fb-9350-55cfc0ceafe2")
		(property "Reference" "#PWR020"
			(at 190.5 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 190.5 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 190.5 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 190.5 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 190.5 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "bdf60580-f548-4e32-843f-f87eceb9b74f")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR020")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 194.31 72.39 0)
		(mirror y)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "ebb12955-b6af-4623-9d31-2c839e464680")
		(property "Reference" "#PWR019"
			(at 194.31 78.74 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 194.31 77.47 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 194.31 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 194.31 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 194.31 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "d57d3da0-1ae1-4a9f-935f-711f6ba1b1d7")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR019")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 100.33 127 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "f6b85cc2-3df9-45f7-a717-e1334aab51a6")
		(property "Reference" "#PWR032"
			(at 100.33 133.35 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 100.33 132.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 100.33 127 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 100.33 127 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 100.33 127 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "7c7f6795-6c0b-4423-99d4-58a419ef8ad3")
		)
		(instances
			(project "RoyalBlue54L-Feather"
				(path "/ba76ff89-c129-45ae-a3c9-97538370856c/79d06a01-15ae-4276-9cac-debde3b18a91"
					(reference "#PWR032")
					(unit 1)
				)
			)
		)
	)
)
