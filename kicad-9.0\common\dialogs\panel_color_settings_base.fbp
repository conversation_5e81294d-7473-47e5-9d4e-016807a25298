<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<wxFormBuilder_Project>
  <FileVersion major="1" minor="18"/>
  <object class="Project" expanded="true">
    <property name="code_generation">C++</property>
    <property name="cpp_class_decoration">; </property>
    <property name="cpp_disconnect_events">1</property>
    <property name="cpp_event_generation">connect</property>
    <property name="cpp_help_provider">none</property>
    <property name="cpp_namespace"></property>
    <property name="cpp_precompiled_header"></property>
    <property name="cpp_use_array_enum">0</property>
    <property name="cpp_use_enum">1</property>
    <property name="embedded_files_path">res</property>
    <property name="encoding">UTF-8</property>
    <property name="file">panel_color_settings_base</property>
    <property name="first_id">1000</property>
    <property name="internationalize">1</property>
    <property name="lua_skip_events">1</property>
    <property name="lua_ui_table">UI</property>
    <property name="name">PanelColorSettings</property>
    <property name="path">.</property>
    <property name="php_disconnect_events">0</property>
    <property name="php_disconnect_mode">source_name</property>
    <property name="php_skip_events">1</property>
    <property name="python_disconnect_events">0</property>
    <property name="python_disconnect_mode">source_name</property>
    <property name="python_image_path_wrapper_function_name"></property>
    <property name="python_indent_with_spaces"></property>
    <property name="python_skip_events">1</property>
    <property name="relative_path">1</property>
    <property name="use_microsoft_bom">0</property>
    <property name="use_native_eol">0</property>
    <object class="Panel" expanded="true">
      <property name="aui_managed">0</property>
      <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
      <property name="bg"></property>
      <property name="context_help"></property>
      <property name="context_menu">1</property>
      <property name="drag_accept_files">0</property>
      <property name="enabled">1</property>
      <property name="event_handler">impl_virtual</property>
      <property name="fg"></property>
      <property name="font"></property>
      <property name="hidden">0</property>
      <property name="id">wxID_ANY</property>
      <property name="maximum_size"></property>
      <property name="minimum_size"></property>
      <property name="name">PANEL_COLOR_SETTINGS_BASE</property>
      <property name="pos"></property>
      <property name="size">-1,-1</property>
      <property name="subclass">RESETTABLE_PANEL; widgets/resettable_panel.h; Not forward_declare</property>
      <property name="tooltip"></property>
      <property name="two_step_creation">0</property>
      <property name="window_extra_style"></property>
      <property name="window_name"></property>
      <property name="window_style">wxTAB_TRAVERSAL</property>
      <event name="OnSize">OnSize</event>
      <object class="wxBoxSizer" expanded="true">
        <property name="minimum_size"></property>
        <property name="name">m_mainSizer</property>
        <property name="orient">wxVERTICAL</property>
        <property name="permission">protected</property>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxEXPAND|wxALL</property>
          <property name="proportion">0</property>
          <object class="wxBoxSizer" expanded="true">
            <property name="minimum_size"></property>
            <property name="name">bControlSizer</property>
            <property name="orient">wxHORIZONTAL</property>
            <property name="permission">none</property>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT|wxRIGHT</property>
              <property name="proportion">0</property>
              <object class="wxStaticText" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Theme:</property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_staticText9</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <property name="wrap">-1</property>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALIGN_CENTER_VERTICAL|wxBOTTOM|wxRIGHT|wxTOP</property>
              <property name="proportion">0</property>
              <object class="wxChoice" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="choices"></property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size">150,-1</property>
                <property name="moveable">1</property>
                <property name="name">m_cbTheme</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="selection">0</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnChoice">OnThemeChanged</event>
                <event name="OnLeftDown">OnLeftDownTheme</event>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">1</property>
              <object class="spacer" expanded="false">
                <property name="height">0</property>
                <property name="permission">protected</property>
                <property name="width">0</property>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
              <property name="proportion">0</property>
              <object class="wxCheckBox" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="checked">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Override individual item colors</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_optOverrideColors</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">public</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip">Show all items in their default color even if they have specific colors set in their properties.</property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnCheckBox">OnOverrideItemColorsClicked</event>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">1</property>
              <object class="spacer" expanded="false">
                <property name="height">0</property>
                <property name="permission">protected</property>
                <property name="width">0</property>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT</property>
              <property name="proportion">0</property>
              <object class="wxButton" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Open Theme Folder</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_btnOpenFolder</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip">Open the folder containing color themes</property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">OnBtnOpenThemeFolderClicked</event>
              </object>
            </object>
          </object>
        </object>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxEXPAND</property>
          <property name="proportion">1</property>
          <object class="wxPanel" expanded="true">
            <property name="BottomDockable">1</property>
            <property name="LeftDockable">1</property>
            <property name="RightDockable">1</property>
            <property name="TopDockable">1</property>
            <property name="aui_layer">0</property>
            <property name="aui_name"></property>
            <property name="aui_position">0</property>
            <property name="aui_row">0</property>
            <property name="best_size"></property>
            <property name="bg"></property>
            <property name="caption"></property>
            <property name="caption_visible">1</property>
            <property name="center_pane">0</property>
            <property name="close_button">1</property>
            <property name="context_help"></property>
            <property name="context_menu">1</property>
            <property name="default_pane">0</property>
            <property name="dock">Dock</property>
            <property name="dock_fixed">0</property>
            <property name="docking">Left</property>
            <property name="drag_accept_files">0</property>
            <property name="enabled">1</property>
            <property name="fg"></property>
            <property name="floatable">1</property>
            <property name="font"></property>
            <property name="gripper">0</property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="max_size"></property>
            <property name="maximize_button">0</property>
            <property name="maximum_size"></property>
            <property name="min_size"></property>
            <property name="minimize_button">0</property>
            <property name="minimum_size"></property>
            <property name="moveable">1</property>
            <property name="name">m_panel1</property>
            <property name="pane_border">1</property>
            <property name="pane_position"></property>
            <property name="pane_size"></property>
            <property name="permission">protected</property>
            <property name="pin_button">1</property>
            <property name="pos"></property>
            <property name="resize">Resizable</property>
            <property name="show">1</property>
            <property name="size"></property>
            <property name="subclass">WX_PANEL; widgets/wx_panel.h; forward_declare</property>
            <property name="toolbar_pane">0</property>
            <property name="tooltip"></property>
            <property name="window_extra_style"></property>
            <property name="window_name"></property>
            <property name="window_style">wxTAB_TRAVERSAL</property>
            <object class="wxBoxSizer" expanded="true">
              <property name="minimum_size"></property>
              <property name="name">m_colorsMainSizer</property>
              <property name="orient">wxHORIZONTAL</property>
              <property name="permission">protected</property>
              <object class="sizeritem" expanded="true">
                <property name="border">5</property>
                <property name="flag">wxEXPAND|wxLEFT|wxRIGHT</property>
                <property name="proportion">0</property>
                <object class="wxScrolledWindow" expanded="true">
                  <property name="BottomDockable">1</property>
                  <property name="LeftDockable">1</property>
                  <property name="RightDockable">1</property>
                  <property name="TopDockable">1</property>
                  <property name="aui_layer">0</property>
                  <property name="aui_name"></property>
                  <property name="aui_position">0</property>
                  <property name="aui_row">0</property>
                  <property name="best_size"></property>
                  <property name="bg">wxSYS_COLOUR_WINDOW</property>
                  <property name="caption"></property>
                  <property name="caption_visible">1</property>
                  <property name="center_pane">0</property>
                  <property name="close_button">1</property>
                  <property name="context_help"></property>
                  <property name="context_menu">1</property>
                  <property name="default_pane">0</property>
                  <property name="dock">Dock</property>
                  <property name="dock_fixed">0</property>
                  <property name="docking">Left</property>
                  <property name="drag_accept_files">0</property>
                  <property name="enabled">1</property>
                  <property name="fg"></property>
                  <property name="floatable">1</property>
                  <property name="font"></property>
                  <property name="gripper">0</property>
                  <property name="hidden">0</property>
                  <property name="id">wxID_ANY</property>
                  <property name="max_size"></property>
                  <property name="maximize_button">0</property>
                  <property name="maximum_size"></property>
                  <property name="min_size"></property>
                  <property name="minimize_button">0</property>
                  <property name="minimum_size">240,240</property>
                  <property name="moveable">1</property>
                  <property name="name">m_colorsListWindow</property>
                  <property name="pane_border">1</property>
                  <property name="pane_position"></property>
                  <property name="pane_size"></property>
                  <property name="permission">protected</property>
                  <property name="pin_button">1</property>
                  <property name="pos"></property>
                  <property name="resize">Resizable</property>
                  <property name="scroll_rate_x">5</property>
                  <property name="scroll_rate_y">5</property>
                  <property name="show">1</property>
                  <property name="size"></property>
                  <property name="subclass">; ; forward_declare</property>
                  <property name="toolbar_pane">0</property>
                  <property name="tooltip"></property>
                  <property name="window_extra_style"></property>
                  <property name="window_name"></property>
                  <property name="window_style">wxVSCROLL</property>
                  <object class="wxFlexGridSizer" expanded="false">
                    <property name="cols">2</property>
                    <property name="flexible_direction">wxHORIZONTAL</property>
                    <property name="growablecols">0</property>
                    <property name="growablerows"></property>
                    <property name="hgap">0</property>
                    <property name="minimum_size">100,-1</property>
                    <property name="name">m_colorsGridSizer</property>
                    <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_NONE</property>
                    <property name="permission">protected</property>
                    <property name="rows">0</property>
                    <property name="vgap">0</property>
                  </object>
                </object>
              </object>
              <object class="sizeritem" expanded="false">
                <property name="border">5</property>
                <property name="flag">wxEXPAND|wxRIGHT</property>
                <property name="proportion">1</property>
                <object class="wxBoxSizer" expanded="false">
                  <property name="minimum_size"></property>
                  <property name="name">m_previewPanelSizer</property>
                  <property name="orient">wxVERTICAL</property>
                  <property name="permission">protected</property>
                </object>
              </object>
            </object>
          </object>
        </object>
      </object>
    </object>
  </object>
</wxFormBuilder_Project>
