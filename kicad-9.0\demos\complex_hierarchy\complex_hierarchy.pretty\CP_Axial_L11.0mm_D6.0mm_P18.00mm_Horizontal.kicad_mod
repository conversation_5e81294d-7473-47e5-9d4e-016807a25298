(footprint "CP_Axial_L11.0mm_D6.0mm_P18.00mm_Horizontal"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "CP, Axial series, Axial, Horizontal, pin pitch=18mm, , length*diameter=11*6mm^2, Electrolytic Capacitor")
	(tags "CP Axial series Axial Horizontal pin pitch 18mm  length 11mm diameter 6mm Electrolytic Capacitor")
	(property "Reference" "REF**"
		(at 9 -4.12 180)
		(layer "F.SilkS")
		(uuid "447e67f6-48e1-4653-b2cb-526a20e38e16")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "47uF/63V"
		(at 9 4.12 180)
		(layer "F.Fab")
		(uuid "43ad10ea-28da-4b80-9d6e-510939fd14e9")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "1cf720c3-4a9e-4f16-b39e-105f322352c7")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "e373e6ca-b8a6-462d-ad85-2474c87441c8")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start 1.28 -2.6)
		(end 3.08 -2.6)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f2d0c758-5c09-4d89-8f83-bd6c0e374f0a")
	)
	(fp_line
		(start 1.44 0)
		(end 3.38 0)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "0d6580dd-ea22-4aba-9e64-0182a1b2af7a")
	)
	(fp_line
		(start 2.18 -3.5)
		(end 2.18 -1.7)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "c740d0d2-**************-3d3629727e14")
	)
	(fp_line
		(start 3.38 -3.12)
		(end 3.38 3.12)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "d85221fb-807b-41ef-b6ee-5d5f640e0fc0")
	)
	(fp_line
		(start 3.38 -3.12)
		(end 5.18 -3.12)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "7fbd0be2-4684-45d9-b22b-03ea2c6874d2")
	)
	(fp_line
		(start 3.38 3.12)
		(end 5.18 3.12)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "6742ae1c-5179-4335-93b2-ff4bd9d124ed")
	)
	(fp_line
		(start 5.18 -3.12)
		(end 6.08 -2.22)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "51115de7-682d-4528-95d1-5206c3184963")
	)
	(fp_line
		(start 5.18 3.12)
		(end 6.08 2.22)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "a2c474c9-82f6-4ee0-a2f0-218cf3303bb6")
	)
	(fp_line
		(start 6.08 -2.22)
		(end 6.98 -3.12)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "9d18cb21-f081-4424-825f-8ccdb83ec2c8")
	)
	(fp_line
		(start 6.08 2.22)
		(end 6.98 3.12)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "ea6f59a5-dcdb-41eb-9895-e4fc9a5207ac")
	)
	(fp_line
		(start 6.98 -3.12)
		(end 14.62 -3.12)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f3f044dc-f4e9-440e-ae23-c332ccdc6581")
	)
	(fp_line
		(start 6.98 3.12)
		(end 14.62 3.12)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "89315ae0-ed58-4e3a-912d-e59c7820c8ac")
	)
	(fp_line
		(start 14.62 -3.12)
		(end 14.62 3.12)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "5a1b1b3b-c83e-4fac-a4b2-bc594ed0ca99")
	)
	(fp_line
		(start 16.56 0)
		(end 14.62 0)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "abe5cd55-a5bd-4b44-8323-4a51d1b44af7")
	)
	(fp_line
		(start -1.45 -3.25)
		(end -1.45 3.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "c4529fc9-1084-4173-b039-015565caacb2")
	)
	(fp_line
		(start -1.45 3.25)
		(end 19.45 3.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "76a1b140-bc52-4d64-9114-012c45580a81")
	)
	(fp_line
		(start 19.45 -3.25)
		(end -1.45 -3.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "78c69141-4bdc-4d9c-9283-17b6bc065d7e")
	)
	(fp_line
		(start 19.45 3.25)
		(end 19.45 -3.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "7ef3f83a-feba-4438-95d3-a817c9ef0cee")
	)
	(fp_line
		(start 0 0)
		(end 3.5 0)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "db2fe5ab-a906-4062-a53e-709c9d90f77f")
	)
	(fp_line
		(start 3.5 -3)
		(end 3.5 3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "1355141b-8665-4dd3-a653-3858f6873f75")
	)
	(fp_line
		(start 3.5 -3)
		(end 5.18 -3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "735b2f29-6f10-48d1-ad06-77eb31c564eb")
	)
	(fp_line
		(start 3.5 3)
		(end 5.18 3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "de896255-d9b8-47c1-998a-e03db9c7eebb")
	)
	(fp_line
		(start 5.18 -3)
		(end 6.08 -2.1)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "68591d81-9f9c-41c9-917e-35ab3741bc1f")
	)
	(fp_line
		(start 5.18 3)
		(end 6.08 2.1)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "1e6fe62a-9e78-4e83-b8ea-4f0c00ad6f4a")
	)
	(fp_line
		(start 5.2 0)
		(end 7 0)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "a4ed0c59-253b-4603-b77b-7acf798101b6")
	)
	(fp_line
		(start 6.08 -2.1)
		(end 6.98 -3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "1dec1523-7825-4db9-ba89-45d9e92ed117")
	)
	(fp_line
		(start 6.08 2.1)
		(end 6.98 3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "cdfc3e81-64a8-4e53-906c-c3d5b87b862c")
	)
	(fp_line
		(start 6.1 -0.9)
		(end 6.1 0.9)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "2343ab35-3011-4c69-90bc-a5a6544ffb9f")
	)
	(fp_line
		(start 6.98 -3)
		(end 14.5 -3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "4bf240c5-834a-480b-abb4-55b4a951f11a")
	)
	(fp_line
		(start 6.98 3)
		(end 14.5 3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "db05c876-6763-4735-bfa7-04303068c5f7")
	)
	(fp_line
		(start 14.5 -3)
		(end 14.5 3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "7c5d5e0e-598b-47a1-a0eb-2bad5ce869d6")
	)
	(fp_line
		(start 18 0)
		(end 14.5 0)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "48f3a1fc-815f-41da-9239-e2fc4125494d")
	)
	(fp_text user "${REFERENCE}"
		(at 9 0 180)
		(layer "F.Fab")
		(uuid "93fcb93d-**************-9a9b2c8cf667")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 2.4 2.4)
		(drill 1.2)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "5551237b-cb22-4342-905f-590566bcabeb")
	)
	(pad "2" thru_hole oval
		(at 18 0)
		(size 2.4 2.4)
		(drill 1.2)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "499203c4-ab0e-4598-98f9-1dd7b5b64a59")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/Capacitor_THT.3dshapes/CP_Axial_L11.0mm_D6.0mm_P18.00mm_Horizontal.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
