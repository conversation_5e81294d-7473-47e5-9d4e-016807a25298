# Copyright 2017 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Intentionally very minimal, so that Crashpad can build in-tree in a variety of
# other projects, unrelated to the variables that are set in those projects'
# BUILDCONFIG.gn. Do not add more variables here. Instead, make them available
# in build/crashpad_buildconfig.gni if they must be globally available.

if (target_os == "") {
  target_os = host_os
}

if (current_os == "") {
  current_os = target_os
}

if (target_cpu == "") {
  target_cpu = host_cpu
}

if (current_cpu == "") {
  current_cpu = target_cpu
}

import("//build/crashpad_buildconfig.gni")

if (crashpad_is_standalone) {
  _mini_chromium_dir = "//third_party/mini_chromium/mini_chromium"
} else if (crashpad_is_external) {
  _mini_chromium_dir = "//../../mini_chromium/mini_chromium"
}

if (current_os == "win") {
  set_default_toolchain(
      "$_mini_chromium_dir/build/config:msvc_toolchain_$current_cpu")
} else {
  set_default_toolchain("$_mini_chromium_dir/build/config:gcc_like_toolchain")
}

declare_args() {
  # When true, enables the debug configuration, with additional run-time checks
  # and logging. When false, enables the release configuration, with additional
  # optimizations.
  is_debug = false

  # When true, build all code with -fsanitize=fuzzer, and enable various
  # *_fuzzer targets.
  crashpad_use_libfuzzer = false
}

_default_configs = [
  "$_mini_chromium_dir/build/config:default",
  "$_mini_chromium_dir/build/config:Wexit_time_destructors",
  "$_mini_chromium_dir/build/config:Wimplicit_fallthrough",
]

if (crashpad_use_libfuzzer) {
  _default_configs += [ "//build/config:crashpad_fuzzer_flags" ]
}

_default_executable_configs = _default_configs + [
                                "$_mini_chromium_dir/build/config:executable",
                                "$_mini_chromium_dir/build/config:win_console",
                              ]

set_defaults("source_set") {
  configs = _default_configs
}

set_defaults("static_library") {
  configs = _default_configs
}

set_defaults("executable") {
  configs = _default_executable_configs
}

set_defaults("loadable_module") {
  configs = _default_configs
}

set_defaults("shared_library") {
  configs = _default_configs
}

set_defaults("test") {
  configs = _default_executable_configs
}
