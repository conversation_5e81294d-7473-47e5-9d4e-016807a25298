<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<wxFormBuilder_Project>
    <FileVersion major="1" minor="16" />
    <object class="Project" expanded="1">
        <property name="class_decoration"></property>
        <property name="code_generation">C++</property>
        <property name="disconnect_events">1</property>
        <property name="disconnect_mode">source_name</property>
        <property name="disconnect_php_events">0</property>
        <property name="disconnect_python_events">0</property>
        <property name="embedded_files_path">res</property>
        <property name="encoding">UTF-8</property>
        <property name="event_generation">connect</property>
        <property name="file">panel_3D_display_options_base</property>
        <property name="first_id">1000</property>
        <property name="help_provider">none</property>
        <property name="image_path_wrapper_function_name"></property>
        <property name="indent_with_spaces"></property>
        <property name="internationalize">1</property>
        <property name="name">panel_3D_display_options_base</property>
        <property name="namespace"></property>
        <property name="path">.</property>
        <property name="precompiled_header"></property>
        <property name="relative_path">1</property>
        <property name="skip_lua_events">1</property>
        <property name="skip_php_events">1</property>
        <property name="skip_python_events">1</property>
        <property name="ui_table">UI</property>
        <property name="use_array_enum">0</property>
        <property name="use_enum">0</property>
        <property name="use_microsoft_bom">0</property>
        <object class="Panel" expanded="1">
            <property name="aui_managed">0</property>
            <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
            <property name="bg"></property>
            <property name="context_help"></property>
            <property name="context_menu">1</property>
            <property name="enabled">1</property>
            <property name="event_handler">impl_virtual</property>
            <property name="fg"></property>
            <property name="font"></property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="maximum_size"></property>
            <property name="minimum_size"></property>
            <property name="name">PANEL_3D_DISPLAY_OPTIONS_BASE</property>
            <property name="pos"></property>
            <property name="size">-1,-1</property>
            <property name="subclass">RESETTABLE_PANEL; widgets/resettable_panel.h; forward_declare</property>
            <property name="tooltip"></property>
            <property name="two_step_creation">0</property>
            <property name="window_extra_style"></property>
            <property name="window_name"></property>
            <property name="window_style">wxTAB_TRAVERSAL</property>
            <object class="wxBoxSizer" expanded="1">
                <property name="minimum_size"></property>
                <property name="name">bSizerMain</property>
                <property name="orient">wxHORIZONTAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="1">
                    <property name="border">5</property>
                    <property name="flag"></property>
                    <property name="proportion">1</property>
                    <object class="wxBoxSizer" expanded="1">
                        <property name="minimum_size"></property>
                        <property name="name">bSizer7</property>
                        <property name="orient">wxVERTICAL</property>
                        <property name="permission">none</property>
                        <object class="sizeritem" expanded="1">
                            <property name="border">20</property>
                            <property name="flag">wxEXPAND|wxRIGHT</property>
                            <property name="proportion">0</property>
                            <object class="wxBoxSizer" expanded="1">
                                <property name="minimum_size"></property>
                                <property name="name">bSizeLeft</property>
                                <property name="orient">wxVERTICAL</property>
                                <property name="permission">none</property>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">13</property>
                                    <property name="flag">wxTOP|wxRIGHT|wxLEFT|wxEXPAND</property>
                                    <property name="proportion">0</property>
                                    <object class="wxStaticText" expanded="1">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="label">Render Options</property>
                                        <property name="markup">0</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_renderOptionsLabel</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style"></property>
                                        <property name="subclass">; ; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                        <property name="wrap">-1</property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND|wxBOTTOM</property>
                                    <property name="proportion">0</property>
                                    <object class="wxStaticLine" expanded="1">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_staticline4</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style">wxLI_HORIZONTAL</property>
                                        <property name="subclass">; ; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND|wxTOP|wxLEFT</property>
                                    <property name="proportion">1</property>
                                    <object class="wxFlexGridSizer" expanded="1">
                                        <property name="cols">1</property>
                                        <property name="flexible_direction">wxBOTH</property>
                                        <property name="growablecols"></property>
                                        <property name="growablerows"></property>
                                        <property name="hgap">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="name">fgSizer2</property>
                                        <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                                        <property name="permission">none</property>
                                        <property name="rows">0</property>
                                        <property name="vgap">5</property>
                                        <object class="sizeritem" expanded="0">
                                            <property name="border">5</property>
                                            <property name="flag">wxLEFT</property>
                                            <property name="proportion">0</property>
                                            <object class="wxCheckBox" expanded="0">
                                                <property name="BottomDockable">1</property>
                                                <property name="LeftDockable">1</property>
                                                <property name="RightDockable">1</property>
                                                <property name="TopDockable">1</property>
                                                <property name="aui_layer"></property>
                                                <property name="aui_name"></property>
                                                <property name="aui_position"></property>
                                                <property name="aui_row"></property>
                                                <property name="best_size"></property>
                                                <property name="bg"></property>
                                                <property name="caption"></property>
                                                <property name="caption_visible">1</property>
                                                <property name="center_pane">0</property>
                                                <property name="checked">0</property>
                                                <property name="close_button">1</property>
                                                <property name="context_help"></property>
                                                <property name="context_menu">1</property>
                                                <property name="default_pane">0</property>
                                                <property name="dock">Dock</property>
                                                <property name="dock_fixed">0</property>
                                                <property name="docking">Left</property>
                                                <property name="enabled">1</property>
                                                <property name="fg"></property>
                                                <property name="floatable">1</property>
                                                <property name="font"></property>
                                                <property name="gripper">0</property>
                                                <property name="hidden">0</property>
                                                <property name="id">wxID_ANY</property>
                                                <property name="label">Clip silkscreen at via annuli</property>
                                                <property name="max_size"></property>
                                                <property name="maximize_button">0</property>
                                                <property name="maximum_size"></property>
                                                <property name="min_size"></property>
                                                <property name="minimize_button">0</property>
                                                <property name="minimum_size"></property>
                                                <property name="moveable">1</property>
                                                <property name="name">m_checkBoxClipSilkOnViaAnnulus</property>
                                                <property name="pane_border">1</property>
                                                <property name="pane_position"></property>
                                                <property name="pane_size"></property>
                                                <property name="permission">protected</property>
                                                <property name="pin_button">1</property>
                                                <property name="pos"></property>
                                                <property name="resize">Resizable</property>
                                                <property name="show">1</property>
                                                <property name="size"></property>
                                                <property name="style"></property>
                                                <property name="subclass"></property>
                                                <property name="toolbar_pane">0</property>
                                                <property name="tooltip"></property>
                                                <property name="validator_data_type"></property>
                                                <property name="validator_style">wxFILTER_NONE</property>
                                                <property name="validator_type">wxDefaultValidator</property>
                                                <property name="validator_variable"></property>
                                                <property name="window_extra_style"></property>
                                                <property name="window_name"></property>
                                                <property name="window_style"></property>
                                            </object>
                                        </object>
                                        <object class="sizeritem" expanded="0">
                                            <property name="border">5</property>
                                            <property name="flag">wxLEFT</property>
                                            <property name="proportion">0</property>
                                            <object class="wxCheckBox" expanded="0">
                                                <property name="BottomDockable">1</property>
                                                <property name="LeftDockable">1</property>
                                                <property name="RightDockable">1</property>
                                                <property name="TopDockable">1</property>
                                                <property name="aui_layer"></property>
                                                <property name="aui_name"></property>
                                                <property name="aui_position"></property>
                                                <property name="aui_row"></property>
                                                <property name="best_size"></property>
                                                <property name="bg"></property>
                                                <property name="caption"></property>
                                                <property name="caption_visible">1</property>
                                                <property name="center_pane">0</property>
                                                <property name="checked">0</property>
                                                <property name="close_button">1</property>
                                                <property name="context_help"></property>
                                                <property name="context_menu">1</property>
                                                <property name="default_pane">0</property>
                                                <property name="dock">Dock</property>
                                                <property name="dock_fixed">0</property>
                                                <property name="docking">Left</property>
                                                <property name="enabled">1</property>
                                                <property name="fg"></property>
                                                <property name="floatable">1</property>
                                                <property name="font"></property>
                                                <property name="gripper">0</property>
                                                <property name="hidden">0</property>
                                                <property name="id">wxID_ANY</property>
                                                <property name="label">Clip silkscreen at solder mask edges</property>
                                                <property name="max_size"></property>
                                                <property name="maximize_button">0</property>
                                                <property name="maximum_size"></property>
                                                <property name="min_size"></property>
                                                <property name="minimize_button">0</property>
                                                <property name="minimum_size"></property>
                                                <property name="moveable">1</property>
                                                <property name="name">m_checkBoxSubtractMaskFromSilk</property>
                                                <property name="pane_border">1</property>
                                                <property name="pane_position"></property>
                                                <property name="pane_size"></property>
                                                <property name="permission">protected</property>
                                                <property name="pin_button">1</property>
                                                <property name="pos"></property>
                                                <property name="resize">Resizable</property>
                                                <property name="show">1</property>
                                                <property name="size"></property>
                                                <property name="style"></property>
                                                <property name="subclass"></property>
                                                <property name="toolbar_pane">0</property>
                                                <property name="tooltip"></property>
                                                <property name="validator_data_type"></property>
                                                <property name="validator_style">wxFILTER_NONE</property>
                                                <property name="validator_type">wxDefaultValidator</property>
                                                <property name="validator_variable"></property>
                                                <property name="window_extra_style"></property>
                                                <property name="window_name"></property>
                                                <property name="window_style"></property>
                                            </object>
                                        </object>
                                        <object class="sizeritem" expanded="0">
                                            <property name="border">5</property>
                                            <property name="flag">wxRIGHT|wxLEFT</property>
                                            <property name="proportion">0</property>
                                            <object class="wxCheckBox" expanded="0">
                                                <property name="BottomDockable">1</property>
                                                <property name="LeftDockable">1</property>
                                                <property name="RightDockable">1</property>
                                                <property name="TopDockable">1</property>
                                                <property name="aui_layer"></property>
                                                <property name="aui_name"></property>
                                                <property name="aui_position"></property>
                                                <property name="aui_row"></property>
                                                <property name="best_size"></property>
                                                <property name="bg"></property>
                                                <property name="caption"></property>
                                                <property name="caption_visible">1</property>
                                                <property name="center_pane">0</property>
                                                <property name="checked">0</property>
                                                <property name="close_button">1</property>
                                                <property name="context_help"></property>
                                                <property name="context_menu">1</property>
                                                <property name="default_pane">0</property>
                                                <property name="dock">Dock</property>
                                                <property name="dock_fixed">0</property>
                                                <property name="docking">Left</property>
                                                <property name="enabled">1</property>
                                                <property name="fg"></property>
                                                <property name="floatable">1</property>
                                                <property name="font"></property>
                                                <property name="gripper">0</property>
                                                <property name="hidden">0</property>
                                                <property name="id">wxID_ANY</property>
                                                <property name="label">Show filled areas in zones</property>
                                                <property name="max_size"></property>
                                                <property name="maximize_button">0</property>
                                                <property name="maximum_size"></property>
                                                <property name="min_size"></property>
                                                <property name="minimize_button">0</property>
                                                <property name="minimum_size"></property>
                                                <property name="moveable">1</property>
                                                <property name="name">m_checkBoxAreas</property>
                                                <property name="pane_border">1</property>
                                                <property name="pane_position"></property>
                                                <property name="pane_size"></property>
                                                <property name="permission">protected</property>
                                                <property name="pin_button">1</property>
                                                <property name="pos"></property>
                                                <property name="resize">Resizable</property>
                                                <property name="show">1</property>
                                                <property name="size"></property>
                                                <property name="style"></property>
                                                <property name="subclass"></property>
                                                <property name="toolbar_pane">0</property>
                                                <property name="tooltip"></property>
                                                <property name="validator_data_type"></property>
                                                <property name="validator_style">wxFILTER_NONE</property>
                                                <property name="validator_type">wxDefaultValidator</property>
                                                <property name="validator_variable"></property>
                                                <property name="window_extra_style"></property>
                                                <property name="window_name"></property>
                                                <property name="window_style"></property>
                                            </object>
                                        </object>
                                        <object class="sizeritem" expanded="0">
                                            <property name="border">5</property>
                                            <property name="flag">wxRIGHT|wxLEFT</property>
                                            <property name="proportion">0</property>
                                            <object class="wxCheckBox" expanded="0">
                                                <property name="BottomDockable">1</property>
                                                <property name="LeftDockable">1</property>
                                                <property name="RightDockable">1</property>
                                                <property name="TopDockable">1</property>
                                                <property name="aui_layer"></property>
                                                <property name="aui_name"></property>
                                                <property name="aui_position"></property>
                                                <property name="aui_row"></property>
                                                <property name="best_size"></property>
                                                <property name="bg"></property>
                                                <property name="caption"></property>
                                                <property name="caption_visible">1</property>
                                                <property name="center_pane">0</property>
                                                <property name="checked">0</property>
                                                <property name="close_button">1</property>
                                                <property name="context_help"></property>
                                                <property name="context_menu">1</property>
                                                <property name="default_pane">0</property>
                                                <property name="dock">Dock</property>
                                                <property name="dock_fixed">0</property>
                                                <property name="docking">Left</property>
                                                <property name="enabled">1</property>
                                                <property name="fg"></property>
                                                <property name="floatable">1</property>
                                                <property name="font"></property>
                                                <property name="gripper">0</property>
                                                <property name="hidden">0</property>
                                                <property name="id">wxID_ANY</property>
                                                <property name="label">Use bare copper color for unplated copper (slow)</property>
                                                <property name="max_size"></property>
                                                <property name="maximize_button">0</property>
                                                <property name="maximum_size"></property>
                                                <property name="min_size"></property>
                                                <property name="minimize_button">0</property>
                                                <property name="minimum_size"></property>
                                                <property name="moveable">1</property>
                                                <property name="name">m_checkBoxRenderPlatedPadsAsPlated</property>
                                                <property name="pane_border">1</property>
                                                <property name="pane_position"></property>
                                                <property name="pane_size"></property>
                                                <property name="permission">protected</property>
                                                <property name="pin_button">1</property>
                                                <property name="pos"></property>
                                                <property name="resize">Resizable</property>
                                                <property name="show">1</property>
                                                <property name="size"></property>
                                                <property name="style"></property>
                                                <property name="subclass">; ; forward_declare</property>
                                                <property name="toolbar_pane">0</property>
                                                <property name="tooltip">Use different colors for plated and unplated copper. (Slow)</property>
                                                <property name="validator_data_type"></property>
                                                <property name="validator_style">wxFILTER_NONE</property>
                                                <property name="validator_type">wxDefaultValidator</property>
                                                <property name="validator_variable"></property>
                                                <property name="window_extra_style"></property>
                                                <property name="window_name"></property>
                                                <property name="window_style"></property>
                                            </object>
                                        </object>
                                        <object class="sizeritem" expanded="1">
                                            <property name="border">5</property>
                                            <property name="flag">wxEXPAND</property>
                                            <property name="proportion">1</property>
                                            <object class="wxBoxSizer" expanded="1">
                                                <property name="minimum_size"></property>
                                                <property name="name">bSizerMaterials</property>
                                                <property name="orient">wxHORIZONTAL</property>
                                                <property name="permission">none</property>
                                                <object class="sizeritem" expanded="1">
                                                    <property name="border">5</property>
                                                    <property name="flag">wxALIGN_CENTER_VERTICAL|wxTOP|wxBOTTOM|wxLEFT</property>
                                                    <property name="proportion">0</property>
                                                    <object class="wxStaticText" expanded="1">
                                                        <property name="BottomDockable">1</property>
                                                        <property name="LeftDockable">1</property>
                                                        <property name="RightDockable">1</property>
                                                        <property name="TopDockable">1</property>
                                                        <property name="aui_layer"></property>
                                                        <property name="aui_name"></property>
                                                        <property name="aui_position"></property>
                                                        <property name="aui_row"></property>
                                                        <property name="best_size"></property>
                                                        <property name="bg"></property>
                                                        <property name="caption"></property>
                                                        <property name="caption_visible">1</property>
                                                        <property name="center_pane">0</property>
                                                        <property name="close_button">1</property>
                                                        <property name="context_help"></property>
                                                        <property name="context_menu">1</property>
                                                        <property name="default_pane">0</property>
                                                        <property name="dock">Dock</property>
                                                        <property name="dock_fixed">0</property>
                                                        <property name="docking">Left</property>
                                                        <property name="enabled">1</property>
                                                        <property name="fg"></property>
                                                        <property name="floatable">1</property>
                                                        <property name="font"></property>
                                                        <property name="gripper">0</property>
                                                        <property name="hidden">0</property>
                                                        <property name="id">wxID_ANY</property>
                                                        <property name="label">Material properties:</property>
                                                        <property name="markup">0</property>
                                                        <property name="max_size"></property>
                                                        <property name="maximize_button">0</property>
                                                        <property name="maximum_size"></property>
                                                        <property name="min_size"></property>
                                                        <property name="minimize_button">0</property>
                                                        <property name="minimum_size"></property>
                                                        <property name="moveable">1</property>
                                                        <property name="name">m_materialPropertiesLabel</property>
                                                        <property name="pane_border">1</property>
                                                        <property name="pane_position"></property>
                                                        <property name="pane_size"></property>
                                                        <property name="permission">protected</property>
                                                        <property name="pin_button">1</property>
                                                        <property name="pos"></property>
                                                        <property name="resize">Resizable</property>
                                                        <property name="show">1</property>
                                                        <property name="size"></property>
                                                        <property name="style"></property>
                                                        <property name="subclass">; ; forward_declare</property>
                                                        <property name="toolbar_pane">0</property>
                                                        <property name="tooltip"></property>
                                                        <property name="window_extra_style"></property>
                                                        <property name="window_name"></property>
                                                        <property name="window_style"></property>
                                                        <property name="wrap">-1</property>
                                                    </object>
                                                </object>
                                                <object class="sizeritem" expanded="1">
                                                    <property name="border">5</property>
                                                    <property name="flag">wxALL|wxALIGN_CENTER_VERTICAL</property>
                                                    <property name="proportion">0</property>
                                                    <object class="wxChoice" expanded="1">
                                                        <property name="BottomDockable">1</property>
                                                        <property name="LeftDockable">1</property>
                                                        <property name="RightDockable">1</property>
                                                        <property name="TopDockable">1</property>
                                                        <property name="aui_layer"></property>
                                                        <property name="aui_name"></property>
                                                        <property name="aui_position"></property>
                                                        <property name="aui_row"></property>
                                                        <property name="best_size"></property>
                                                        <property name="bg"></property>
                                                        <property name="caption"></property>
                                                        <property name="caption_visible">1</property>
                                                        <property name="center_pane">0</property>
                                                        <property name="choices">&quot;Realistic&quot; &quot;Solid colors&quot; &quot;CAD colors&quot;</property>
                                                        <property name="close_button">1</property>
                                                        <property name="context_help"></property>
                                                        <property name="context_menu">1</property>
                                                        <property name="default_pane">0</property>
                                                        <property name="dock">Dock</property>
                                                        <property name="dock_fixed">0</property>
                                                        <property name="docking">Left</property>
                                                        <property name="enabled">1</property>
                                                        <property name="fg"></property>
                                                        <property name="floatable">1</property>
                                                        <property name="font"></property>
                                                        <property name="gripper">0</property>
                                                        <property name="hidden">0</property>
                                                        <property name="id">wxID_ANY</property>
                                                        <property name="max_size"></property>
                                                        <property name="maximize_button">0</property>
                                                        <property name="maximum_size"></property>
                                                        <property name="min_size"></property>
                                                        <property name="minimize_button">0</property>
                                                        <property name="minimum_size"></property>
                                                        <property name="moveable">1</property>
                                                        <property name="name">m_materialProperties</property>
                                                        <property name="pane_border">1</property>
                                                        <property name="pane_position"></property>
                                                        <property name="pane_size"></property>
                                                        <property name="permission">protected</property>
                                                        <property name="pin_button">1</property>
                                                        <property name="pos"></property>
                                                        <property name="resize">Resizable</property>
                                                        <property name="selection">0</property>
                                                        <property name="show">1</property>
                                                        <property name="size"></property>
                                                        <property name="style"></property>
                                                        <property name="subclass">; ; forward_declare</property>
                                                        <property name="toolbar_pane">0</property>
                                                        <property name="tooltip"></property>
                                                        <property name="validator_data_type"></property>
                                                        <property name="validator_style">wxFILTER_NONE</property>
                                                        <property name="validator_type">wxDefaultValidator</property>
                                                        <property name="validator_variable"></property>
                                                        <property name="window_extra_style"></property>
                                                        <property name="window_name"></property>
                                                        <property name="window_style"></property>
                                                    </object>
                                                </object>
                                            </object>
                                        </object>
                                    </object>
                                </object>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">15</property>
                            <property name="flag">wxEXPAND|wxTOP</property>
                            <property name="proportion">0</property>
                            <object class="wxBoxSizer" expanded="1">
                                <property name="minimum_size"></property>
                                <property name="name">bSizerRight</property>
                                <property name="orient">wxVERTICAL</property>
                                <property name="permission">none</property>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">13</property>
                                    <property name="flag">wxTOP|wxRIGHT|wxLEFT</property>
                                    <property name="proportion">0</property>
                                    <object class="wxStaticText" expanded="1">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="label">Camera Options</property>
                                        <property name="markup">0</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_cameraOptionsLabel</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style"></property>
                                        <property name="subclass">; ; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                        <property name="wrap">-1</property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND|wxBOTTOM</property>
                                    <property name="proportion">0</property>
                                    <object class="wxStaticLine" expanded="1">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_staticline5</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style">wxLI_HORIZONTAL</property>
                                        <property name="subclass">; ; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxTOP|wxLEFT</property>
                                    <property name="proportion">0</property>
                                    <object class="wxBoxSizer" expanded="1">
                                        <property name="minimum_size"></property>
                                        <property name="name">bSizerRotAngle</property>
                                        <property name="orient">wxHORIZONTAL</property>
                                        <property name="permission">none</property>
                                        <object class="sizeritem" expanded="0">
                                            <property name="border">5</property>
                                            <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT|wxLEFT</property>
                                            <property name="proportion">0</property>
                                            <object class="wxStaticText" expanded="0">
                                                <property name="BottomDockable">1</property>
                                                <property name="LeftDockable">1</property>
                                                <property name="RightDockable">1</property>
                                                <property name="TopDockable">1</property>
                                                <property name="aui_layer"></property>
                                                <property name="aui_name"></property>
                                                <property name="aui_position"></property>
                                                <property name="aui_row"></property>
                                                <property name="best_size"></property>
                                                <property name="bg"></property>
                                                <property name="caption"></property>
                                                <property name="caption_visible">1</property>
                                                <property name="center_pane">0</property>
                                                <property name="close_button">1</property>
                                                <property name="context_help"></property>
                                                <property name="context_menu">1</property>
                                                <property name="default_pane">0</property>
                                                <property name="dock">Dock</property>
                                                <property name="dock_fixed">0</property>
                                                <property name="docking">Left</property>
                                                <property name="enabled">1</property>
                                                <property name="fg"></property>
                                                <property name="floatable">1</property>
                                                <property name="font"></property>
                                                <property name="gripper">0</property>
                                                <property name="hidden">0</property>
                                                <property name="id">wxID_ANY</property>
                                                <property name="label">Rotation increment:</property>
                                                <property name="markup">0</property>
                                                <property name="max_size"></property>
                                                <property name="maximize_button">0</property>
                                                <property name="maximum_size"></property>
                                                <property name="min_size"></property>
                                                <property name="minimize_button">0</property>
                                                <property name="minimum_size"></property>
                                                <property name="moveable">1</property>
                                                <property name="name">m_staticTextRotAngle</property>
                                                <property name="pane_border">1</property>
                                                <property name="pane_position"></property>
                                                <property name="pane_size"></property>
                                                <property name="permission">protected</property>
                                                <property name="pin_button">1</property>
                                                <property name="pos"></property>
                                                <property name="resize">Resizable</property>
                                                <property name="show">1</property>
                                                <property name="size"></property>
                                                <property name="style"></property>
                                                <property name="subclass">; ; forward_declare</property>
                                                <property name="toolbar_pane">0</property>
                                                <property name="tooltip"></property>
                                                <property name="window_extra_style"></property>
                                                <property name="window_name"></property>
                                                <property name="window_style"></property>
                                                <property name="wrap">-1</property>
                                            </object>
                                        </object>
                                        <object class="sizeritem" expanded="0">
                                            <property name="border">5</property>
                                            <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT|wxLEFT</property>
                                            <property name="proportion">0</property>
                                            <object class="wxSpinCtrlDouble" expanded="0">
                                                <property name="BottomDockable">1</property>
                                                <property name="LeftDockable">1</property>
                                                <property name="RightDockable">1</property>
                                                <property name="TopDockable">1</property>
                                                <property name="aui_layer"></property>
                                                <property name="aui_name"></property>
                                                <property name="aui_position"></property>
                                                <property name="aui_row"></property>
                                                <property name="best_size"></property>
                                                <property name="bg"></property>
                                                <property name="caption"></property>
                                                <property name="caption_visible">1</property>
                                                <property name="center_pane">0</property>
                                                <property name="close_button">1</property>
                                                <property name="context_help"></property>
                                                <property name="context_menu">1</property>
                                                <property name="default_pane">0</property>
                                                <property name="digits">0</property>
                                                <property name="dock">Dock</property>
                                                <property name="dock_fixed">0</property>
                                                <property name="docking">Left</property>
                                                <property name="enabled">1</property>
                                                <property name="fg"></property>
                                                <property name="floatable">1</property>
                                                <property name="font"></property>
                                                <property name="gripper">0</property>
                                                <property name="hidden">0</property>
                                                <property name="id">wxID_ANY</property>
                                                <property name="inc">1</property>
                                                <property name="initial">10</property>
                                                <property name="max">359</property>
                                                <property name="max_size"></property>
                                                <property name="maximize_button">0</property>
                                                <property name="maximum_size"></property>
                                                <property name="min">0</property>
                                                <property name="min_size"></property>
                                                <property name="minimize_button">0</property>
                                                <property name="minimum_size"></property>
                                                <property name="moveable">1</property>
                                                <property name="name">m_spinCtrlRotationAngle</property>
                                                <property name="pane_border">1</property>
                                                <property name="pane_position"></property>
                                                <property name="pane_size"></property>
                                                <property name="permission">protected</property>
                                                <property name="pin_button">1</property>
                                                <property name="pos"></property>
                                                <property name="resize">Resizable</property>
                                                <property name="show">1</property>
                                                <property name="size"></property>
                                                <property name="style">wxSP_ARROW_KEYS</property>
                                                <property name="subclass">; ; forward_declare</property>
                                                <property name="toolbar_pane">0</property>
                                                <property name="tooltip"></property>
                                                <property name="value"></property>
                                                <property name="window_extra_style"></property>
                                                <property name="window_name"></property>
                                                <property name="window_style"></property>
                                            </object>
                                        </object>
                                        <object class="sizeritem" expanded="0">
                                            <property name="border">5</property>
                                            <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT</property>
                                            <property name="proportion">0</property>
                                            <object class="wxStaticText" expanded="0">
                                                <property name="BottomDockable">1</property>
                                                <property name="LeftDockable">1</property>
                                                <property name="RightDockable">1</property>
                                                <property name="TopDockable">1</property>
                                                <property name="aui_layer"></property>
                                                <property name="aui_name"></property>
                                                <property name="aui_position"></property>
                                                <property name="aui_row"></property>
                                                <property name="best_size"></property>
                                                <property name="bg"></property>
                                                <property name="caption"></property>
                                                <property name="caption_visible">1</property>
                                                <property name="center_pane">0</property>
                                                <property name="close_button">1</property>
                                                <property name="context_help"></property>
                                                <property name="context_menu">1</property>
                                                <property name="default_pane">0</property>
                                                <property name="dock">Dock</property>
                                                <property name="dock_fixed">0</property>
                                                <property name="docking">Left</property>
                                                <property name="enabled">1</property>
                                                <property name="fg"></property>
                                                <property name="floatable">1</property>
                                                <property name="font"></property>
                                                <property name="gripper">0</property>
                                                <property name="hidden">0</property>
                                                <property name="id">wxID_ANY</property>
                                                <property name="label">deg</property>
                                                <property name="markup">0</property>
                                                <property name="max_size"></property>
                                                <property name="maximize_button">0</property>
                                                <property name="maximum_size"></property>
                                                <property name="min_size"></property>
                                                <property name="minimize_button">0</property>
                                                <property name="minimum_size"></property>
                                                <property name="moveable">1</property>
                                                <property name="name">m_staticTextRotAngleUnits</property>
                                                <property name="pane_border">1</property>
                                                <property name="pane_position"></property>
                                                <property name="pane_size"></property>
                                                <property name="permission">protected</property>
                                                <property name="pin_button">1</property>
                                                <property name="pos"></property>
                                                <property name="resize">Resizable</property>
                                                <property name="show">1</property>
                                                <property name="size"></property>
                                                <property name="style"></property>
                                                <property name="subclass">; ; forward_declare</property>
                                                <property name="toolbar_pane">0</property>
                                                <property name="tooltip"></property>
                                                <property name="window_extra_style"></property>
                                                <property name="window_name"></property>
                                                <property name="window_style"></property>
                                                <property name="wrap">-1</property>
                                            </object>
                                        </object>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND|wxLEFT</property>
                                    <property name="proportion">0</property>
                                    <object class="wxBoxSizer" expanded="1">
                                        <property name="minimum_size"></property>
                                        <property name="name">bSizerSlider</property>
                                        <property name="orient">wxHORIZONTAL</property>
                                        <property name="permission">none</property>
                                        <object class="sizeritem" expanded="0">
                                            <property name="border">5</property>
                                            <property name="flag">wxALIGN_CENTER_VERTICAL|wxRIGHT|wxLEFT</property>
                                            <property name="proportion">0</property>
                                            <object class="wxCheckBox" expanded="0">
                                                <property name="BottomDockable">1</property>
                                                <property name="LeftDockable">1</property>
                                                <property name="RightDockable">1</property>
                                                <property name="TopDockable">1</property>
                                                <property name="aui_layer"></property>
                                                <property name="aui_name"></property>
                                                <property name="aui_position"></property>
                                                <property name="aui_row"></property>
                                                <property name="best_size"></property>
                                                <property name="bg"></property>
                                                <property name="caption"></property>
                                                <property name="caption_visible">1</property>
                                                <property name="center_pane">0</property>
                                                <property name="checked">1</property>
                                                <property name="close_button">1</property>
                                                <property name="context_help"></property>
                                                <property name="context_menu">1</property>
                                                <property name="default_pane">0</property>
                                                <property name="dock">Dock</property>
                                                <property name="dock_fixed">0</property>
                                                <property name="docking">Left</property>
                                                <property name="enabled">1</property>
                                                <property name="fg"></property>
                                                <property name="floatable">1</property>
                                                <property name="font"></property>
                                                <property name="gripper">0</property>
                                                <property name="hidden">0</property>
                                                <property name="id">wxID_ANY</property>
                                                <property name="label">Redraw while moving</property>
                                                <property name="max_size"></property>
                                                <property name="maximize_button">0</property>
                                                <property name="maximum_size"></property>
                                                <property name="min_size"></property>
                                                <property name="minimize_button">0</property>
                                                <property name="minimum_size"></property>
                                                <property name="moveable">1</property>
                                                <property name="name">m_checkBoxEnableAnimation</property>
                                                <property name="pane_border">1</property>
                                                <property name="pane_position"></property>
                                                <property name="pane_size"></property>
                                                <property name="permission">protected</property>
                                                <property name="pin_button">1</property>
                                                <property name="pos"></property>
                                                <property name="resize">Resizable</property>
                                                <property name="show">1</property>
                                                <property name="size"></property>
                                                <property name="style"></property>
                                                <property name="subclass"></property>
                                                <property name="toolbar_pane">0</property>
                                                <property name="tooltip"></property>
                                                <property name="validator_data_type"></property>
                                                <property name="validator_style">wxFILTER_NONE</property>
                                                <property name="validator_type">wxDefaultValidator</property>
                                                <property name="validator_variable"></property>
                                                <property name="window_extra_style"></property>
                                                <property name="window_name"></property>
                                                <property name="window_style"></property>
                                                <event name="OnCheckBox">OnCheckEnableAnimation</event>
                                            </object>
                                        </object>
                                        <object class="sizeritem" expanded="0">
                                            <property name="border">15</property>
                                            <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
                                            <property name="proportion">0</property>
                                            <object class="wxStaticText" expanded="0">
                                                <property name="BottomDockable">1</property>
                                                <property name="LeftDockable">1</property>
                                                <property name="RightDockable">1</property>
                                                <property name="TopDockable">1</property>
                                                <property name="aui_layer"></property>
                                                <property name="aui_name"></property>
                                                <property name="aui_position"></property>
                                                <property name="aui_row"></property>
                                                <property name="best_size"></property>
                                                <property name="bg"></property>
                                                <property name="caption"></property>
                                                <property name="caption_visible">1</property>
                                                <property name="center_pane">0</property>
                                                <property name="close_button">1</property>
                                                <property name="context_help"></property>
                                                <property name="context_menu">1</property>
                                                <property name="default_pane">0</property>
                                                <property name="dock">Dock</property>
                                                <property name="dock_fixed">0</property>
                                                <property name="docking">Left</property>
                                                <property name="enabled">1</property>
                                                <property name="fg"></property>
                                                <property name="floatable">1</property>
                                                <property name="font"></property>
                                                <property name="gripper">0</property>
                                                <property name="hidden">0</property>
                                                <property name="id">wxID_ANY</property>
                                                <property name="label">Redraw speed:</property>
                                                <property name="markup">0</property>
                                                <property name="max_size"></property>
                                                <property name="maximize_button">0</property>
                                                <property name="maximum_size"></property>
                                                <property name="min_size"></property>
                                                <property name="minimize_button">0</property>
                                                <property name="minimum_size"></property>
                                                <property name="moveable">1</property>
                                                <property name="name">m_staticAnimationSpeed</property>
                                                <property name="pane_border">1</property>
                                                <property name="pane_position"></property>
                                                <property name="pane_size"></property>
                                                <property name="permission">protected</property>
                                                <property name="pin_button">1</property>
                                                <property name="pos"></property>
                                                <property name="resize">Resizable</property>
                                                <property name="show">1</property>
                                                <property name="size"></property>
                                                <property name="style"></property>
                                                <property name="subclass">; ; forward_declare</property>
                                                <property name="toolbar_pane">0</property>
                                                <property name="tooltip"></property>
                                                <property name="window_extra_style"></property>
                                                <property name="window_name"></property>
                                                <property name="window_style"></property>
                                                <property name="wrap">-1</property>
                                            </object>
                                        </object>
                                        <object class="sizeritem" expanded="0">
                                            <property name="border">5</property>
                                            <property name="flag">wxEXPAND|wxRIGHT|wxLEFT</property>
                                            <property name="proportion">0</property>
                                            <object class="wxSlider" expanded="0">
                                                <property name="BottomDockable">1</property>
                                                <property name="LeftDockable">1</property>
                                                <property name="RightDockable">1</property>
                                                <property name="TopDockable">1</property>
                                                <property name="aui_layer"></property>
                                                <property name="aui_name"></property>
                                                <property name="aui_position"></property>
                                                <property name="aui_row"></property>
                                                <property name="best_size"></property>
                                                <property name="bg"></property>
                                                <property name="caption"></property>
                                                <property name="caption_visible">1</property>
                                                <property name="center_pane">0</property>
                                                <property name="close_button">1</property>
                                                <property name="context_help"></property>
                                                <property name="context_menu">1</property>
                                                <property name="default_pane">0</property>
                                                <property name="dock">Dock</property>
                                                <property name="dock_fixed">0</property>
                                                <property name="docking">Left</property>
                                                <property name="enabled">1</property>
                                                <property name="fg"></property>
                                                <property name="floatable">1</property>
                                                <property name="font"></property>
                                                <property name="gripper">0</property>
                                                <property name="hidden">0</property>
                                                <property name="id">wxID_ANY</property>
                                                <property name="maxValue">5</property>
                                                <property name="max_size"></property>
                                                <property name="maximize_button">0</property>
                                                <property name="maximum_size"></property>
                                                <property name="minValue">1</property>
                                                <property name="min_size"></property>
                                                <property name="minimize_button">0</property>
                                                <property name="minimum_size">-1,-1</property>
                                                <property name="moveable">1</property>
                                                <property name="name">m_sliderAnimationSpeed</property>
                                                <property name="pane_border">1</property>
                                                <property name="pane_position"></property>
                                                <property name="pane_size"></property>
                                                <property name="permission">protected</property>
                                                <property name="pin_button">1</property>
                                                <property name="pos"></property>
                                                <property name="resize">Resizable</property>
                                                <property name="show">1</property>
                                                <property name="size"></property>
                                                <property name="style">wxSL_AUTOTICKS|wxSL_HORIZONTAL|wxSL_LABELS</property>
                                                <property name="subclass">; ; forward_declare</property>
                                                <property name="toolbar_pane">0</property>
                                                <property name="tooltip"></property>
                                                <property name="validator_data_type"></property>
                                                <property name="validator_style">wxFILTER_NONE</property>
                                                <property name="validator_type">wxDefaultValidator</property>
                                                <property name="validator_variable"></property>
                                                <property name="value">3</property>
                                                <property name="window_extra_style"></property>
                                                <property name="window_name"></property>
                                                <property name="window_style"></property>
                                            </object>
                                        </object>
                                    </object>
                                </object>
                            </object>
                        </object>
                    </object>
                </object>
            </object>
        </object>
    </object>
</wxFormBuilder_Project>
