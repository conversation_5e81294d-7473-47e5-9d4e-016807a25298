<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<wxFormBuilder_Project>
  <FileVersion major="1" minor="17"/>
  <object class="Project" expanded="true">
    <property name="class_decoration"></property>
    <property name="code_generation">C++</property>
    <property name="disconnect_events">1</property>
    <property name="disconnect_mode">source_name</property>
    <property name="disconnect_php_events">0</property>
    <property name="disconnect_python_events">0</property>
    <property name="embedded_files_path">res</property>
    <property name="encoding">UTF-8</property>
    <property name="event_generation">connect</property>
    <property name="file">dialog_configure_paths_base</property>
    <property name="first_id">1000</property>
    <property name="help_provider">none</property>
    <property name="image_path_wrapper_function_name"></property>
    <property name="indent_with_spaces"></property>
    <property name="internationalize">1</property>
    <property name="name">DIALOG_CONFIGURE_PATHS_BASE</property>
    <property name="namespace"></property>
    <property name="path">.</property>
    <property name="precompiled_header"></property>
    <property name="relative_path">1</property>
    <property name="skip_lua_events">1</property>
    <property name="skip_php_events">1</property>
    <property name="skip_python_events">1</property>
    <property name="ui_table">UI</property>
    <property name="use_array_enum">0</property>
    <property name="use_enum">0</property>
    <property name="use_microsoft_bom">0</property>
    <object class="Dialog" expanded="true">
      <property name="aui_managed">0</property>
      <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
      <property name="bg"></property>
      <property name="center">wxBOTH</property>
      <property name="context_help"></property>
      <property name="context_menu">1</property>
      <property name="drag_accept_files">0</property>
      <property name="enabled">1</property>
      <property name="event_handler">impl_virtual</property>
      <property name="extra_style"></property>
      <property name="fg"></property>
      <property name="font"></property>
      <property name="hidden">0</property>
      <property name="id">wxID_ANY</property>
      <property name="maximum_size"></property>
      <property name="minimum_size">-1,-1</property>
      <property name="name">DIALOG_CONFIGURE_PATHS_BASE</property>
      <property name="pos"></property>
      <property name="size">-1,-1</property>
      <property name="style">wxDEFAULT_DIALOG_STYLE|wxRESIZE_BORDER</property>
      <property name="subclass">DIALOG_SHIM; dialog_shim.h</property>
      <property name="title">Configure Paths</property>
      <property name="tooltip"></property>
      <property name="two_step_creation">0</property>
      <property name="window_extra_style"></property>
      <property name="window_name"></property>
      <property name="window_style"></property>
      <event name="OnUpdateUI">OnUpdateUI</event>
      <object class="wxBoxSizer" expanded="true">
        <property name="minimum_size"></property>
        <property name="name">bSizerMain</property>
        <property name="orient">wxVERTICAL</property>
        <property name="permission">none</property>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxALL|wxEXPAND</property>
          <property name="proportion">1</property>
          <object class="wxStaticBoxSizer" expanded="true">
            <property name="id">wxID_ANY</property>
            <property name="label">Environment Variables</property>
            <property name="minimum_size"></property>
            <property name="name">sbEnvVars</property>
            <property name="orient">wxVERTICAL</property>
            <property name="parent">1</property>
            <property name="permission">none</property>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxALL|wxEXPAND</property>
              <property name="proportion">1</property>
              <object class="wxGrid" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="autosize_cols">0</property>
                <property name="autosize_rows">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="cell_bg"></property>
                <property name="cell_font"></property>
                <property name="cell_horiz_alignment">wxALIGN_LEFT</property>
                <property name="cell_text"></property>
                <property name="cell_vert_alignment">wxALIGN_CENTER</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="col_label_horiz_alignment">wxALIGN_CENTER</property>
                <property name="col_label_size">wxGRID_AUTOSIZE</property>
                <property name="col_label_values">&quot;Name&quot; &quot;Path&quot;</property>
                <property name="col_label_vert_alignment">wxALIGN_CENTER</property>
                <property name="cols">2</property>
                <property name="column_sizes">150,454</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="drag_col_move">0</property>
                <property name="drag_col_size">1</property>
                <property name="drag_grid_size">0</property>
                <property name="drag_row_size">1</property>
                <property name="editing">1</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="grid_line_color"></property>
                <property name="grid_lines">1</property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label_bg"></property>
                <property name="label_font"></property>
                <property name="label_text"></property>
                <property name="margin_height">0</property>
                <property name="margin_width">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size">604,170</property>
                <property name="moveable">1</property>
                <property name="name">m_EnvVars</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="row_label_horiz_alignment">wxALIGN_CENTER</property>
                <property name="row_label_size">0</property>
                <property name="row_label_values"></property>
                <property name="row_label_vert_alignment">wxALIGN_CENTER</property>
                <property name="row_sizes"></property>
                <property name="rows">1</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="subclass">WX_GRID; widgets/wx_grid.h; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">0</property>
              <object class="wxBoxSizer" expanded="true">
                <property name="minimum_size"></property>
                <property name="name">bSizerEnvVarBtns</property>
                <property name="orient">wxHORIZONTAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxBOTTOM|wxLEFT</property>
                  <property name="proportion">0</property>
                  <object class="wxBitmapButton" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer"></property>
                    <property name="aui_name"></property>
                    <property name="aui_position"></property>
                    <property name="aui_row"></property>
                    <property name="auth_needed">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="bitmap"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="current"></property>
                    <property name="default">0</property>
                    <property name="default_pane">0</property>
                    <property name="disabled"></property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="focus"></property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="label">Add Environment Variable</property>
                    <property name="margins"></property>
                    <property name="markup">0</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size">-1,-1</property>
                    <property name="moveable">1</property>
                    <property name="name">m_btnAddEnvVar</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="position"></property>
                    <property name="pressed"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="validator_data_type"></property>
                    <property name="validator_style">wxFILTER_NONE</property>
                    <property name="validator_type">wxDefaultValidator</property>
                    <property name="validator_variable"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <event name="OnButtonClick">OnAddEnvVar</event>
                  </object>
                </object>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND|wxRIGHT|wxLEFT</property>
                  <property name="proportion">0</property>
                  <object class="spacer" expanded="true">
                    <property name="height">0</property>
                    <property name="permission">protected</property>
                    <property name="width">0</property>
                  </object>
                </object>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxBOTTOM</property>
                  <property name="proportion">0</property>
                  <object class="wxBitmapButton" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer"></property>
                    <property name="aui_name"></property>
                    <property name="aui_position"></property>
                    <property name="aui_row"></property>
                    <property name="auth_needed">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="bitmap"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="current"></property>
                    <property name="default">0</property>
                    <property name="default_pane">0</property>
                    <property name="disabled"></property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="focus"></property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="label">Delete Environment Variable</property>
                    <property name="margins"></property>
                    <property name="markup">0</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size">-1,-1</property>
                    <property name="moveable">1</property>
                    <property name="name">m_btnDeleteEnvVar</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="position"></property>
                    <property name="pressed"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass">STD_BITMAP_BUTTON; widgets/std_bitmap_button.h; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="validator_data_type"></property>
                    <property name="validator_style">wxFILTER_NONE</property>
                    <property name="validator_type">wxDefaultValidator</property>
                    <property name="validator_variable"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <event name="OnButtonClick">OnRemoveEnvVar</event>
                  </object>
                </object>
              </object>
            </object>
          </object>
        </object>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxALL|wxEXPAND</property>
          <property name="proportion">0</property>
          <object class="wxStdDialogButtonSizer" expanded="true">
            <property name="Apply">0</property>
            <property name="Cancel">1</property>
            <property name="ContextHelp">0</property>
            <property name="Help">1</property>
            <property name="No">0</property>
            <property name="OK">1</property>
            <property name="Save">0</property>
            <property name="Yes">0</property>
            <property name="minimum_size"></property>
            <property name="name">m_sdbSizer</property>
            <property name="permission">protected</property>
            <event name="OnHelpButtonClick">OnHelp</event>
          </object>
        </object>
      </object>
    </object>
  </object>
</wxFormBuilder_Project>
