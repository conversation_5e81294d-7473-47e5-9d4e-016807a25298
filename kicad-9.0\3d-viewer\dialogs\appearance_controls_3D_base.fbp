<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<wxFormBuilder_Project>
  <FileVersion major="1" minor="18"/>
  <object class="Project" expanded="true">
    <property name="code_generation">C++</property>
    <property name="cpp_class_decoration">; </property>
    <property name="cpp_disconnect_events">1</property>
    <property name="cpp_event_generation">connect</property>
    <property name="cpp_help_provider">none</property>
    <property name="cpp_namespace"></property>
    <property name="cpp_precompiled_header"></property>
    <property name="cpp_use_array_enum">0</property>
    <property name="cpp_use_enum">0</property>
    <property name="embedded_files_path">res</property>
    <property name="encoding">UTF-8</property>
    <property name="file">appearance_controls_3D_base</property>
    <property name="first_id">1000</property>
    <property name="internationalize">1</property>
    <property name="lua_skip_events">1</property>
    <property name="lua_ui_table">UI</property>
    <property name="name">Appearance Panel 3D</property>
    <property name="path">.</property>
    <property name="php_disconnect_events">0</property>
    <property name="php_disconnect_mode">source_name</property>
    <property name="php_skip_events">1</property>
    <property name="python_disconnect_events">0</property>
    <property name="python_disconnect_mode">source_name</property>
    <property name="python_image_path_wrapper_function_name"></property>
    <property name="python_indent_with_spaces"></property>
    <property name="python_skip_events">1</property>
    <property name="relative_path">1</property>
    <property name="use_microsoft_bom">0</property>
    <property name="use_native_eol">0</property>
    <object class="Panel" expanded="true">
      <property name="aui_managed">0</property>
      <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
      <property name="bg"></property>
      <property name="context_help"></property>
      <property name="context_menu">1</property>
      <property name="drag_accept_files">0</property>
      <property name="enabled">1</property>
      <property name="event_handler">impl_virtual</property>
      <property name="fg"></property>
      <property name="font"></property>
      <property name="hidden">0</property>
      <property name="id">wxID_ANY</property>
      <property name="maximum_size"></property>
      <property name="minimum_size">210,360</property>
      <property name="name">APPEARANCE_CONTROLS_3D_BASE</property>
      <property name="pos"></property>
      <property name="size">-1,-1</property>
      <property name="subclass">WX_PANEL; widgets/wx_panel.h; forward_declare</property>
      <property name="tooltip"></property>
      <property name="two_step_creation">0</property>
      <property name="window_extra_style"></property>
      <property name="window_name"></property>
      <property name="window_style">wxTAB_TRAVERSAL</property>
      <event name="OnSetFocus">OnSetFocus</event>
      <event name="OnSize">OnSize</event>
      <object class="wxBoxSizer" expanded="true">
        <property name="minimum_size"></property>
        <property name="name">m_sizerOuter</property>
        <property name="orient">wxVERTICAL</property>
        <property name="permission">protected</property>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxEXPAND</property>
          <property name="proportion">1</property>
          <object class="wxPanel" expanded="true">
            <property name="BottomDockable">1</property>
            <property name="LeftDockable">1</property>
            <property name="RightDockable">1</property>
            <property name="TopDockable">1</property>
            <property name="aui_layer">0</property>
            <property name="aui_name"></property>
            <property name="aui_position">0</property>
            <property name="aui_row">0</property>
            <property name="best_size"></property>
            <property name="bg"></property>
            <property name="caption"></property>
            <property name="caption_visible">1</property>
            <property name="center_pane">0</property>
            <property name="close_button">1</property>
            <property name="context_help"></property>
            <property name="context_menu">1</property>
            <property name="default_pane">0</property>
            <property name="dock">Dock</property>
            <property name="dock_fixed">0</property>
            <property name="docking">Left</property>
            <property name="drag_accept_files">0</property>
            <property name="enabled">1</property>
            <property name="fg"></property>
            <property name="floatable">1</property>
            <property name="font"></property>
            <property name="gripper">0</property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="max_size"></property>
            <property name="maximize_button">0</property>
            <property name="maximum_size"></property>
            <property name="min_size"></property>
            <property name="minimize_button">0</property>
            <property name="minimum_size"></property>
            <property name="moveable">1</property>
            <property name="name">m_panelLayers</property>
            <property name="pane_border">1</property>
            <property name="pane_position"></property>
            <property name="pane_size"></property>
            <property name="permission">protected</property>
            <property name="pin_button">1</property>
            <property name="pos"></property>
            <property name="resize">Resizable</property>
            <property name="show">1</property>
            <property name="size"></property>
            <property name="subclass">; ; forward_declare</property>
            <property name="toolbar_pane">0</property>
            <property name="tooltip"></property>
            <property name="window_extra_style"></property>
            <property name="window_name"></property>
            <property name="window_style">wxTAB_TRAVERSAL</property>
            <event name="OnSetFocus">OnSetFocus</event>
            <object class="wxBoxSizer" expanded="true">
              <property name="minimum_size"></property>
              <property name="name">m_panelLayersSizer</property>
              <property name="orient">wxVERTICAL</property>
              <property name="permission">protected</property>
              <object class="sizeritem" expanded="true">
                <property name="border">5</property>
                <property name="flag">wxEXPAND|wxLEFT|wxRIGHT|wxTOP</property>
                <property name="proportion">1</property>
                <object class="CustomControl" expanded="true">
                  <property name="BottomDockable">1</property>
                  <property name="LeftDockable">1</property>
                  <property name="RightDockable">1</property>
                  <property name="TopDockable">1</property>
                  <property name="aui_layer">0</property>
                  <property name="aui_name"></property>
                  <property name="aui_position">0</property>
                  <property name="aui_row">0</property>
                  <property name="best_size"></property>
                  <property name="bg"></property>
                  <property name="caption"></property>
                  <property name="caption_visible">1</property>
                  <property name="center_pane">0</property>
                  <property name="class">wxScrolledCanvas</property>
                  <property name="close_button">1</property>
                  <property name="construction"></property>
                  <property name="context_help"></property>
                  <property name="context_menu">1</property>
                  <property name="declaration"></property>
                  <property name="default_pane">0</property>
                  <property name="dock">Dock</property>
                  <property name="dock_fixed">0</property>
                  <property name="docking">Left</property>
                  <property name="drag_accept_files">0</property>
                  <property name="enabled">1</property>
                  <property name="fg"></property>
                  <property name="floatable">1</property>
                  <property name="font"></property>
                  <property name="gripper">0</property>
                  <property name="hidden">0</property>
                  <property name="id">wxID_ANY</property>
                  <property name="include">#include &lt;wx/scrolwin.h&gt;&#x0A;#include &lt;wx/grid.h&gt; // needed for MSVC to see wxScrolledCanvas indirectly exported</property>
                  <property name="max_size"></property>
                  <property name="maximize_button">0</property>
                  <property name="maximum_size"></property>
                  <property name="min_size"></property>
                  <property name="minimize_button">0</property>
                  <property name="minimum_size"></property>
                  <property name="moveable">1</property>
                  <property name="name">m_windowLayers</property>
                  <property name="pane_border">1</property>
                  <property name="pane_position"></property>
                  <property name="pane_size"></property>
                  <property name="permission">protected</property>
                  <property name="pin_button">1</property>
                  <property name="pos"></property>
                  <property name="resize">Resizable</property>
                  <property name="settings"></property>
                  <property name="show">1</property>
                  <property name="size"></property>
                  <property name="subclass">; ; forward_declare</property>
                  <property name="toolbar_pane">0</property>
                  <property name="tooltip"></property>
                  <property name="window_extra_style"></property>
                  <property name="window_name"></property>
                  <property name="window_style">wxVSCROLL</property>
                </object>
              </object>
            </object>
          </object>
        </object>
        <object class="sizeritem" expanded="true">
          <property name="border">2</property>
          <property name="flag">wxEXPAND|wxTOP|wxBOTTOM</property>
          <property name="proportion">0</property>
          <object class="wxBoxSizer" expanded="true">
            <property name="minimum_size"></property>
            <property name="name">bBottomMargin</property>
            <property name="orient">wxVERTICAL</property>
            <property name="permission">none</property>
            <object class="sizeritem" expanded="true">
              <property name="border">3</property>
              <property name="flag">wxBOTTOM|wxEXPAND|wxLEFT|wxRIGHT</property>
              <property name="proportion">0</property>
              <object class="wxStaticLine" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer">0</property>
                <property name="aui_name"></property>
                <property name="aui_position">0</property>
                <property name="aui_row">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_staticline1</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style">wxLI_HORIZONTAL</property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxTOP|wxRIGHT|wxLEFT</property>
              <property name="proportion">0</property>
              <object class="wxBoxSizer" expanded="true">
                <property name="minimum_size"></property>
                <property name="name">bPresets</property>
                <property name="orient">wxVERTICAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="false">
                  <property name="border">5</property>
                  <property name="flag"></property>
                  <property name="proportion">1</property>
                  <object class="wxStaticText" expanded="false">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="label">Presets (Ctrl+Tab):</property>
                    <property name="markup">0</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_presetsLabel</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass">; ; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <property name="wrap">-1</property>
                  </object>
                </object>
                <object class="sizeritem" expanded="false">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND|wxTOP</property>
                  <property name="proportion">0</property>
                  <object class="wxChoice" expanded="false">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="choices">&quot;Follow PCB Editor&quot; &quot;Follow PCB Plot Settings&quot;</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_cbLayerPresets</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="selection">1</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass">; ; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="validator_data_type"></property>
                    <property name="validator_style">wxFILTER_NONE</property>
                    <property name="validator_type">wxDefaultValidator</property>
                    <property name="validator_variable"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <event name="OnChoice">onLayerPresetChanged</event>
                  </object>
                </object>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">0</property>
              <object class="spacer" expanded="false">
                <property name="height">2</property>
                <property name="permission">protected</property>
                <property name="width">0</property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxBOTTOM|wxRIGHT|wxLEFT</property>
              <property name="proportion">1</property>
              <object class="wxBoxSizer" expanded="true">
                <property name="minimum_size"></property>
                <property name="name">bViewports</property>
                <property name="orient">wxVERTICAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="false">
                  <property name="border">5</property>
                  <property name="flag">wxTOP</property>
                  <property name="proportion">1</property>
                  <object class="wxStaticText" expanded="false">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="label">Viewports (Alt+Tab):</property>
                    <property name="markup">0</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_viewportsLabel</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass">; ; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <property name="wrap">-1</property>
                  </object>
                </object>
                <object class="sizeritem" expanded="false">
                  <property name="border">5</property>
                  <property name="flag">wxEXPAND|wxTOP</property>
                  <property name="proportion">0</property>
                  <object class="wxChoice" expanded="false">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer">0</property>
                    <property name="aui_name"></property>
                    <property name="aui_position">0</property>
                    <property name="aui_row">0</property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="choices">&quot;(unsaved)&quot;</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_cbViewports</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="selection">1</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass">; ; forward_declare</property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="validator_data_type"></property>
                    <property name="validator_style">wxFILTER_NONE</property>
                    <property name="validator_type">wxDefaultValidator</property>
                    <property name="validator_variable"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <event name="OnChoice">onViewportChanged</event>
                    <event name="OnUpdateUI">onUpdateViewportsCb</event>
                  </object>
                </object>
              </object>
            </object>
          </object>
        </object>
      </object>
    </object>
  </object>
</wxFormBuilder_Project>
