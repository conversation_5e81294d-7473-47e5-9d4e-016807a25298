(kicad_pcb
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(layers
		(0 "F.Cu" signal "top_cu")
		(2 "B.Cu" signal "bottom_cu")
		(9 "F.<PERSON>hes" user "F.Adhesive")
		(11 "B<PERSON>Adhes" user "B.Adhesive")
		(13 "F.Paste" user)
		(15 "B.Paste" user)
		(5 "F.SilkS" user "F.Silkscreen")
		(7 "B.SilkS" user "B.Silkscreen")
		(1 "F.Mask" user)
		(3 "B.Mask" user)
		(17 "Dwgs.User" user "User.Drawings")
		(19 "Cmts.User" user "User.Comments")
		(21 "Eco1.User" user "User.Eco1")
		(23 "Eco2.User" user "User.Eco2")
		(25 "Edge.Cuts" user)
		(27 "Margin" user)
		(31 "F.CrtYd" user "F.Courtyard")
		(29 "B.CrtYd" user "B.Courtyard")
		(35 "F.Fab" user)
		(33 "B.Fab" user)
	)
	(setup
		(stackup
			(layer "F.SilkS"
				(type "Top Silk Screen")
				(color "White")
			)
			(layer "F.Paste"
				(type "Top Solder Paste")
			)
			(layer "F.Mask"
				(type "Top Solder Mask")
				(color "Green")
				(thickness 0.01)
			)
			(layer "F.Cu"
				(type "copper")
				(thickness 0.035)
			)
			(layer "dielectric 1"
				(type "core")
				(thickness 1.51)
				(material "FR4")
				(epsilon_r 4.5)
				(loss_tangent 0.02)
			)
			(layer "B.Cu"
				(type "copper")
				(thickness 0.035)
			)
			(layer "B.Mask"
				(type "Bottom Solder Mask")
				(color "Green")
				(thickness 0.01)
			)
			(layer "B.Paste"
				(type "Bottom Solder Paste")
			)
			(layer "B.SilkS"
				(type "Bottom Silk Screen")
				(color "White")
			)
			(copper_finish "None")
			(dielectric_constraints no)
		)
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(tenting front back)
		(pcbplotparams
			(layerselection 0x00000000_00000000_00000000_000000af)
			(plot_on_all_layers_selection 0x00000000_00000000_00000000_00000000)
			(disableapertmacros no)
			(usegerberextensions no)
			(usegerberattributes yes)
			(usegerberadvancedattributes yes)
			(creategerberjobfile yes)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 6)
			(plotframeref no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(pdf_metadata yes)
			(pdf_single_document no)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plot_black_and_white yes)
			(plotinvisibletext no)
			(sketchpadsonfab no)
			(plotpadnumbers no)
			(hidednponfab no)
			(sketchdnponfab yes)
			(crossoutdnponfab yes)
			(subtractmaskfromsilk no)
			(outputformat 1)
			(mirror no)
			(drillshape 0)
			(scaleselection 1)
			(outputdirectory "")
		)
	)
	(net 0 "")
	(net 1 "GND")
	(net 2 "Net-(P3-P1)")
	(net 3 "Net-(P2-P1)")
	(net 4 "Net-(U1A-K)")
	(net 5 "Net-(P1-PM)")
	(net 6 "Net-(P4-P1)")
	(net 7 "Net-(P4-PM)")
	(net 8 "unconnected-(P5-Pad1)")
	(net 9 "unconnected-(P6-Pad1)")
	(net 10 "unconnected-(P7-Pad1)")
	(net 11 "unconnected-(P8-Pad1)")
	(net 12 "Net-(U1A-G)")
	(net 13 "Net-(U1B-K)")
	(footprint "Footprints:CP_Radial_D10.0mm_P5.00mm"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a581e5")
		(at 141.605 99.695 90)
		(descr "CP, Radial series, Radial, pin pitch=5.00mm, , diameter=10mm, Electrolytic Capacitor")
		(tags "CP Radial series Radial pin pitch 5.00mm  diameter 10mm Electrolytic Capacitor")
		(property "Reference" "C1"
			(at 4.953 -5.969 90)
			(layer "F.SilkS")
			(uuid "2ed7cdd6-55fa-4b64-a7b4-6eaa6e55c99e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "10uF"
			(at 2.5 6.25 90)
			(layer "F.Fab")
			(uuid "a20f5cd0-8d48-4f88-a2bb-d592eafab848")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 90)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "26a16992-e30a-4e2c-b833-51e69d69f9c7")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 90)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "c4b8e836-f9b5-4e4b-85c0-be21493a083d")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "CP_*")
		(path "/00000000-0000-0000-0000-00004549f4be")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 2.58 -5.08)
			(end 2.58 5.08)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "55b08714-eb91-400c-aa94-e59c060e08e1")
		)
		(fp_line
			(start 2.54 -5.08)
			(end 2.54 5.08)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9c591fdd-f5d1-4cdd-87ba-16936e35afa7")
		)
		(fp_line
			(start 2.5 -5.08)
			(end 2.5 5.08)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6e8751db-9383-424c-b487-d8f34c2375fb")
		)
		(fp_line
			(start 2.62 -5.079)
			(end 2.62 5.079)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "38cd2244-a220-45e1-8b9b-af148f5bed04")
		)
		(fp_line
			(start 2.66 -5.078)
			(end 2.66 5.078)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2249db18-f593-404a-a9f3-a4984087d530")
		)
		(fp_line
			(start 2.7 -5.077)
			(end 2.7 5.077)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c228462b-ebac-487c-b091-d34f61abea2f")
		)
		(fp_line
			(start 2.74 -5.075)
			(end 2.74 5.075)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8e3e328d-e26a-4105-9b65-8d756c8826a7")
		)
		(fp_line
			(start 2.78 -5.073)
			(end 2.78 5.073)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7e2ee580-9fc9-4a0d-baaf-8c9ae11784d7")
		)
		(fp_line
			(start 2.82 -5.07)
			(end 2.82 5.07)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5b8f4e81-9c75-4840-977e-7f963060ce1d")
		)
		(fp_line
			(start 2.86 -5.068)
			(end 2.86 5.068)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "60de52e9-a8d4-4701-b834-5ac0aa5fe94a")
		)
		(fp_line
			(start 2.9 -5.065)
			(end 2.9 5.065)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3800ae91-ef9d-4d8b-b547-fafc258dbfc4")
		)
		(fp_line
			(start 2.94 -5.062)
			(end 2.94 5.062)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "30ab86dc-a343-4213-9652-316285c63432")
		)
		(fp_line
			(start 2.98 -5.058)
			(end 2.98 5.058)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b19e51fc-3399-4717-a676-7a7cf1845bc2")
		)
		(fp_line
			(start 3.02 -5.054)
			(end 3.02 5.054)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "66ba5d19-b49f-46ae-a63d-c080b5ad963b")
		)
		(fp_line
			(start 3.06 -5.05)
			(end 3.06 5.05)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "261872f5-d50c-459c-8d00-faab9a3511a5")
		)
		(fp_line
			(start 3.1 -5.045)
			(end 3.1 5.045)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "31523875-3ae3-4bdd-88ff-408603644fd9")
		)
		(fp_line
			(start 3.14 -5.04)
			(end 3.14 5.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "dd76adf4-**************-16909f6abf3d")
		)
		(fp_line
			(start 3.18 -5.035)
			(end 3.18 5.035)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2d6d765a-35be-44df-9383-a5b088c77529")
		)
		(fp_line
			(start 3.221 -5.03)
			(end 3.221 5.03)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "851deebf-20bc-434e-a913-2f014cb6e459")
		)
		(fp_line
			(start 3.261 -5.024)
			(end 3.261 5.024)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2c547837-d167-488c-935f-eec31fa200ac")
		)
		(fp_line
			(start 3.301 -5.018)
			(end 3.301 5.018)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3ee14423-a230-47bf-8556-74a2b4242494")
		)
		(fp_line
			(start 3.341 -5.011)
			(end 3.341 5.011)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b1dc7797-0f5b-407a-a0d0-791043f0b9ee")
		)
		(fp_line
			(start 3.381 -5.004)
			(end 3.381 5.004)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ce018632-5649-422b-84c5-c7fa80c28cb1")
		)
		(fp_line
			(start 3.421 -4.997)
			(end 3.421 4.997)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7d124f47-3308-4584-806e-3a3e55798ed8")
		)
		(fp_line
			(start 3.461 -4.99)
			(end 3.461 4.99)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d22748c6-9a6d-403c-9cf7-c93889f7101a")
		)
		(fp_line
			(start 3.501 -4.982)
			(end 3.501 4.982)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f8b3e83f-1cde-4765-bdc5-e4afbb9259cb")
		)
		(fp_line
			(start 3.541 -4.974)
			(end 3.541 4.974)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c21ecb30-eaee-4c54-b65e-6456d8220949")
		)
		(fp_line
			(start 3.581 -4.965)
			(end 3.581 4.965)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f5c2a1d7-80ac-4a9c-b794-51134ff08c48")
		)
		(fp_line
			(start 3.621 -4.956)
			(end 3.621 4.956)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "dc0b697e-d655-4a2b-8f76-c1600e3ce579")
		)
		(fp_line
			(start 3.661 -4.947)
			(end 3.661 4.947)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e25bde07-3c9d-4b93-ad4e-a5ae73ca091a")
		)
		(fp_line
			(start 3.701 -4.938)
			(end 3.701 4.938)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "785b5cfc-75a5-4a0a-9289-443942c198f7")
		)
		(fp_line
			(start 3.741 -4.928)
			(end 3.741 4.928)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ce3b9b36-afaa-46ff-8c1d-f0fef1699ba3")
		)
		(fp_line
			(start 3.781 -4.918)
			(end 3.781 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a144e201-24b6-4868-89b4-cdbbffbc4a5e")
		)
		(fp_line
			(start 3.821 -4.907)
			(end 3.821 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a1ad0f99-ece9-4256-9f06-3ad1252d9f38")
		)
		(fp_line
			(start 3.861 -4.897)
			(end 3.861 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a2ebe67c-37d3-47e6-bd2b-f3ebdce5f567")
		)
		(fp_line
			(start 3.901 -4.885)
			(end 3.901 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "971b1b27-004f-44f6-9cf2-a6e26d310bf3")
		)
		(fp_line
			(start 3.941 -4.874)
			(end 3.941 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4f866b74-effe-4812-950e-93fa37327dc1")
		)
		(fp_line
			(start 3.981 -4.862)
			(end 3.981 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "76749fd2-eb02-4275-8177-383b5b11d180")
		)
		(fp_line
			(start 4.021 -4.85)
			(end 4.021 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7af5e210-0444-49bf-9bf1-e1d02d27f2b9")
		)
		(fp_line
			(start 4.061 -4.837)
			(end 4.061 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ee662c36-b7a7-42c3-956b-692086b4dd77")
		)
		(fp_line
			(start 4.101 -4.824)
			(end 4.101 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9eb30ed1-4f6a-4fdd-a4bb-45a3b6e1d876")
		)
		(fp_line
			(start 4.141 -4.811)
			(end 4.141 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6a3e1ae3-553a-49ac-867c-722fc637e4d1")
		)
		(fp_line
			(start 4.181 -4.797)
			(end 4.181 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c06d17ab-835e-4c57-b5e2-b0296777d90a")
		)
		(fp_line
			(start 4.221 -4.783)
			(end 4.221 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5eaec3ad-d3b6-4d07-ac9c-f5970857f719")
		)
		(fp_line
			(start 4.261 -4.768)
			(end 4.261 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a85aed79-9a59-45d6-b0ab-e763eb6a712a")
		)
		(fp_line
			(start 4.301 -4.754)
			(end 4.301 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8ef54465-ed2b-413f-adcc-be84d81496bd")
		)
		(fp_line
			(start 4.341 -4.738)
			(end 4.341 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4699d94f-fc30-4a89-8a2b-37ae5c4d4897")
		)
		(fp_line
			(start 4.381 -4.723)
			(end 4.381 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "80af0f1c-7095-493f-9751-3a0a38b33fba")
		)
		(fp_line
			(start 4.421 -4.707)
			(end 4.421 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6468c98b-d37f-4864-8b94-51fa3e6920a3")
		)
		(fp_line
			(start 4.461 -4.69)
			(end 4.461 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6ae8e797-b9c4-4517-a114-12b6e9fbc832")
		)
		(fp_line
			(start 4.501 -4.674)
			(end 4.501 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2402a19f-f67a-4177-83b7-89a745b44e3f")
		)
		(fp_line
			(start 4.541 -4.657)
			(end 4.541 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9b847667-5998-4ace-a1a6-94c90d640891")
		)
		(fp_line
			(start 4.581 -4.639)
			(end 4.581 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "aa9040c1-45a1-42d7-9eda-97f92f4ec710")
		)
		(fp_line
			(start 4.621 -4.621)
			(end 4.621 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ff1fb7a9-d975-4f49-83e2-8b3fdeb6b578")
		)
		(fp_line
			(start 4.661 -4.603)
			(end 4.661 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "59d41193-1cf0-476e-8741-d6aafee78842")
		)
		(fp_line
			(start 4.701 -4.584)
			(end 4.701 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ac6cdc8a-7c13-45d9-87cb-0046dc9092d0")
		)
		(fp_line
			(start 4.741 -4.564)
			(end 4.741 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fc131c3f-a58b-4985-ab7c-50e5a7bac2b9")
		)
		(fp_line
			(start 4.781 -4.545)
			(end 4.781 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c042fdcc-c270-445d-adef-a3f037a76236")
		)
		(fp_line
			(start 4.821 -4.525)
			(end 4.821 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "474bd11f-b629-468c-b63e-f0751f42cb17")
		)
		(fp_line
			(start 4.861 -4.504)
			(end 4.861 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b2ff7f90-0524-47a9-a3eb-0ad0ec9cf2a2")
		)
		(fp_line
			(start 4.901 -4.483)
			(end 4.901 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3b6e4190-f29f-4b27-aea3-b616b85720b9")
		)
		(fp_line
			(start 4.941 -4.462)
			(end 4.941 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "842ab2fb-73ab-44be-977b-36be393b6e8d")
		)
		(fp_line
			(start 4.981 -4.44)
			(end 4.981 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c59ab418-1447-4a21-b3e2-91a6bb432f7e")
		)
		(fp_line
			(start 5.021 -4.417)
			(end 5.021 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "275584ea-9b1d-47a0-bebf-a75872e0bb06")
		)
		(fp_line
			(start 5.061 -4.395)
			(end 5.061 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f6c081ad-50fd-45b1-82fb-abab63320636")
		)
		(fp_line
			(start 5.101 -4.371)
			(end 5.101 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4b7c5fb0-6084-4d45-b6ff-c5c3441d61c5")
		)
		(fp_line
			(start 5.141 -4.347)
			(end 5.141 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "273799d7-7749-477f-8aff-468f4a05dfb1")
		)
		(fp_line
			(start 5.181 -4.323)
			(end 5.181 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "59a91828-5d41-429e-9860-333ca0af4c80")
		)
		(fp_line
			(start 5.221 -4.298)
			(end 5.221 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d772c873-3c4b-4cd7-8ff4-dd5fd4f98c8d")
		)
		(fp_line
			(start 5.261 -4.273)
			(end 5.261 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "435e0688-f58b-4ab2-b20b-9632fe850075")
		)
		(fp_line
			(start 5.301 -4.247)
			(end 5.301 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1dea4f94-fcd8-49e4-a2c0-42d15d35478c")
		)
		(fp_line
			(start 5.341 -4.221)
			(end 5.341 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6888fcf9-1cd4-443b-ad71-eab5eb92b31b")
		)
		(fp_line
			(start 5.381 -4.194)
			(end 5.381 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cca86b3c-db1f-4244-b4f0-54f04f48cc88")
		)
		(fp_line
			(start 5.421 -4.166)
			(end 5.421 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e10d67c1-4a3e-48ae-82e7-650002bffb70")
		)
		(fp_line
			(start 5.461 -4.138)
			(end 5.461 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "eb41e621-b7aa-4c83-9e37-10b3c47c3899")
		)
		(fp_line
			(start 5.501 -4.11)
			(end 5.501 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c29e5a6b-51e0-4824-b408-293922d54c07")
		)
		(fp_line
			(start 5.541 -4.08)
			(end 5.541 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ef57577c-6531-423e-aa80-0440265de4e7")
		)
		(fp_line
			(start 5.581 -4.05)
			(end 5.581 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6791a5e5-ea5f-48e1-bcdf-d88e220ef076")
		)
		(fp_line
			(start 5.621 -4.02)
			(end 5.621 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "42d5577a-4e4c-49c4-b0d1-e56796e39fc6")
		)
		(fp_line
			(start 5.661 -3.989)
			(end 5.661 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1522f5ec-0ccd-4408-a241-241cc0694371")
		)
		(fp_line
			(start 5.701 -3.957)
			(end 5.701 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "178ee254-835d-4cf2-a98c-c746fd4ca9fa")
		)
		(fp_line
			(start 5.741 -3.925)
			(end 5.741 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c688f5a6-7416-4f54-9665-c5ecc7a59203")
		)
		(fp_line
			(start 5.781 -3.892)
			(end 5.781 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ba731555-ad8e-4a29-b05d-9eb114998383")
		)
		(fp_line
			(start 5.821 -3.858)
			(end 5.821 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ad53771c-f9f3-4f65-8975-fa18e458b2df")
		)
		(fp_line
			(start 5.861 -3.824)
			(end 5.861 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2fa9e197-6edf-4a2e-bc5a-bd2f8b4aa016")
		)
		(fp_line
			(start 5.901 -3.789)
			(end 5.901 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5378684b-b516-4384-8bd1-026bd664e579")
		)
		(fp_line
			(start 5.941 -3.753)
			(end 5.941 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "99fe57d6-7718-4080-b3a8-366869d40045")
		)
		(fp_line
			(start 5.981 -3.716)
			(end 5.981 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "19f59725-5121-46e3-bb8b-dbccf189e7d3")
		)
		(fp_line
			(start 6.021 -3.679)
			(end 6.021 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b398f84f-6b1b-4a31-a552-548a16eb5c42")
		)
		(fp_line
			(start 6.061 -3.64)
			(end 6.061 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "928a1005-52bd-4ccb-8a39-77e87e1e689c")
		)
		(fp_line
			(start 6.101 -3.601)
			(end 6.101 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1fa2bfdc-b55d-46f0-90f8-6097174039fb")
		)
		(fp_line
			(start 6.141 -3.561)
			(end 6.141 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4dd69efe-4eff-4009-b11e-d40919740154")
		)
		(fp_line
			(start 6.181 -3.52)
			(end 6.181 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4da38523-752b-4cc0-9e22-976fa0eaf5ca")
		)
		(fp_line
			(start 6.221 -3.478)
			(end 6.221 -1.241)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bd258515-86f6-4230-b1f2-6181e6985c1f")
		)
		(fp_line
			(start 6.261 -3.436)
			(end 6.261 3.436)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "70229a35-5b60-4987-a896-ae4132682121")
		)
		(fp_line
			(start 6.301 -3.392)
			(end 6.301 3.392)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "86fb8875-eaba-4830-acd6-06eb0bbf2bd0")
		)
		(fp_line
			(start -2.479646 -3.375)
			(end -2.479646 -2.375)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "105b1d88-3d78-4554-8e42-eb9d9c967991")
		)
		(fp_line
			(start 6.341 -3.347)
			(end 6.341 3.347)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b5d2f440-6c89-483f-8c24-6518ddaf7ccf")
		)
		(fp_line
			(start 6.381 -3.301)
			(end 6.381 3.301)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "79a835d1-4a7a-47fd-9801-511118ed18fe")
		)
		(fp_line
			(start 6.421 -3.254)
			(end 6.421 3.254)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "210a94f3-0b2e-42cf-8171-ea783b98e63d")
		)
		(fp_line
			(start 6.461 -3.206)
			(end 6.461 3.206)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1dd0c56a-9e88-4e5d-9aa4-e60912d2097a")
		)
		(fp_line
			(start 6.501 -3.156)
			(end 6.501 3.156)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6be8e002-b050-4fe8-9804-4f6dfaa2aa52")
		)
		(fp_line
			(start 6.541 -3.106)
			(end 6.541 3.106)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d5b8da94-ecd8-4812-9ce8-6146f9257a25")
		)
		(fp_line
			(start 6.581 -3.054)
			(end 6.581 3.054)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d10ea470-dd1e-41b7-bc09-d52bbff76f74")
		)
		(fp_line
			(start 6.621 -3)
			(end 6.621 3)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "*************-4892-8df0-feb05a4bc67a")
		)
		(fp_line
			(start 6.661 -2.945)
			(end 6.661 2.945)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "814cf802-f0f9-4582-9203-36d15e528c58")
		)
		(fp_line
			(start 6.701 -2.889)
			(end 6.701 2.889)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "521a7d0d-e6ba-4cb8-966e-ddac5496c70d")
		)
		(fp_line
			(start -2.979646 -2.875)
			(end -1.979646 -2.875)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "323ffad9-f9ea-4acb-a4e3-53d65e59c610")
		)
		(fp_line
			(start 6.741 -2.83)
			(end 6.741 2.83)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "aa9facc5-e2c0-43a0-9478-8ed56a975396")
		)
		(fp_line
			(start 6.781 -2.77)
			(end 6.781 2.77)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "94270788-7b39-4d9f-be30-b031da9ab7a1")
		)
		(fp_line
			(start 6.821 -2.709)
			(end 6.821 2.709)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2d5dafc9-f7dd-417c-b507-dc7f5abd88e5")
		)
		(fp_line
			(start 6.861 -2.645)
			(end 6.861 2.645)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1fe5d73d-0eb5-45f3-ab7d-659febcb4317")
		)
		(fp_line
			(start 6.901 -2.579)
			(end 6.901 2.579)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "435b8656-5eb4-4fb1-8c8d-752380ba3eef")
		)
		(fp_line
			(start 6.941 -2.51)
			(end 6.941 2.51)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "55e3f72f-0bb9-4815-ae03-3c8ac8347dd0")
		)
		(fp_line
			(start 6.981 -2.439)
			(end 6.981 2.439)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "84c91dc1-aae7-41ba-ae34-c76604ec4ece")
		)
		(fp_line
			(start 7.021 -2.365)
			(end 7.021 2.365)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "327cb97e-78ac-4122-9492-5b39701216e9")
		)
		(fp_line
			(start 7.061 -2.289)
			(end 7.061 2.289)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9df5c628-6058-4a3b-b337-d4b99c3a9f06")
		)
		(fp_line
			(start 7.101 -2.209)
			(end 7.101 2.209)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0a0b043e-a397-4352-a67e-a7e75a024f01")
		)
		(fp_line
			(start 7.141 -2.125)
			(end 7.141 2.125)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "40b870bf-c3f0-43db-b156-78b6804ebb92")
		)
		(fp_line
			(start 7.181 -2.037)
			(end 7.181 2.037)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2f3ef5b9-334e-4673-9f5c-b4ea0db75f6c")
		)
		(fp_line
			(start 7.221 -1.944)
			(end 7.221 1.944)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e99d2873-068f-4718-8601-73775282e094")
		)
		(fp_line
			(start 7.261 -1.846)
			(end 7.261 1.846)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "42df8cf7-0b74-42a9-9b91-54324c0dadd4")
		)
		(fp_line
			(start 7.301 -1.742)
			(end 7.301 1.742)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "74fc3726-f2fa-46d5-af2f-4fbc802cbe72")
		)
		(fp_line
			(start 7.341 -1.63)
			(end 7.341 1.63)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "152ff210-9b04-4fd1-8ed3-1885b4504c64")
		)
		(fp_line
			(start 7.381 -1.51)
			(end 7.381 1.51)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "047dad6d-e2fa-4ccd-958d-855fc0c1e773")
		)
		(fp_line
			(start 7.421 -1.378)
			(end 7.421 1.378)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6ba0b1c8-7faa-4d3c-908a-633bef77c13b")
		)
		(fp_line
			(start 7.461 -1.23)
			(end 7.461 1.23)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ec2f3fcb-253e-497e-8436-0ef24642cd51")
		)
		(fp_line
			(start 7.501 -1.062)
			(end 7.501 1.062)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "501d5e4e-103d-4821-8961-f37dc15fd97b")
		)
		(fp_line
			(start 7.541 -0.862)
			(end 7.541 0.862)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8450fbfe-74f3-4be1-8b4b-cda71cd493c9")
		)
		(fp_line
			(start 7.581 -0.599)
			(end 7.581 0.599)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c1fa18ce-8144-49a3-87ef-dfbbb1b16f2f")
		)
		(fp_line
			(start 6.221 1.241)
			(end 6.221 3.478)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0b570316-fa03-4735-9438-9b0dc0b298d6")
		)
		(fp_line
			(start 6.181 1.241)
			(end 6.181 3.52)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "409f7e67-1908-4793-a165-98b2485de76d")
		)
		(fp_line
			(start 6.141 1.241)
			(end 6.141 3.561)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f779b274-dd8e-4a63-bfa1-73a74337d9e0")
		)
		(fp_line
			(start 6.101 1.241)
			(end 6.101 3.601)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a822ed9b-8738-4094-af8b-a27762cfa2d1")
		)
		(fp_line
			(start 6.061 1.241)
			(end 6.061 3.64)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "17250405-92fb-4af7-a8b2-9c459560ce91")
		)
		(fp_line
			(start 6.021 1.241)
			(end 6.021 3.679)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "48392ea4-4995-4bc1-9dc5-aa0a506b2c35")
		)
		(fp_line
			(start 5.981 1.241)
			(end 5.981 3.716)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a02e9dfc-307f-4a38-94ab-a132a38b0330")
		)
		(fp_line
			(start 5.941 1.241)
			(end 5.941 3.753)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ff4ee673-ffd3-48d6-9d2b-29227c5a6fe3")
		)
		(fp_line
			(start 5.901 1.241)
			(end 5.901 3.789)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7eed6362-28e2-416c-b5a4-688489c7622e")
		)
		(fp_line
			(start 5.861 1.241)
			(end 5.861 3.824)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "94679353-b445-42da-9330-096b4295271f")
		)
		(fp_line
			(start 5.821 1.241)
			(end 5.821 3.858)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d8e8b41f-f6a3-44e9-b971-c28433551e26")
		)
		(fp_line
			(start 5.781 1.241)
			(end 5.781 3.892)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e0245ee0-c8dd-4b55-aae4-599f98a41470")
		)
		(fp_line
			(start 5.741 1.241)
			(end 5.741 3.925)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4248534e-15f6-4f67-9a9e-d56165968f79")
		)
		(fp_line
			(start 5.701 1.241)
			(end 5.701 3.957)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "dc170f6e-4b20-4950-ad3a-bfdefef9e944")
		)
		(fp_line
			(start 5.661 1.241)
			(end 5.661 3.989)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3319f3b1-b914-4e5a-abef-84c7ea06a559")
		)
		(fp_line
			(start 5.621 1.241)
			(end 5.621 4.02)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "18f07cc4-3166-4cb0-9079-c9bf1afe6da1")
		)
		(fp_line
			(start 5.581 1.241)
			(end 5.581 4.05)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a68c11da-7e7d-4e85-9e75-ea5c57e4abf8")
		)
		(fp_line
			(start 5.541 1.241)
			(end 5.541 4.08)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "704236e8-4bb7-4741-82f4-e1d1b73c8832")
		)
		(fp_line
			(start 5.501 1.241)
			(end 5.501 4.11)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c2c28464-f529-4e83-9b18-3be57b8f70bc")
		)
		(fp_line
			(start 5.461 1.241)
			(end 5.461 4.138)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7eb69030-cf2b-4c2a-aa3f-15cf4744cd14")
		)
		(fp_line
			(start 5.421 1.241)
			(end 5.421 4.166)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "821d1b31-3d12-4f51-b10a-4f6edf5ac980")
		)
		(fp_line
			(start 5.381 1.241)
			(end 5.381 4.194)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "08d93cc2-7c43-4b08-a3d2-7d03cadf0353")
		)
		(fp_line
			(start 5.341 1.241)
			(end 5.341 4.221)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f5c28a1e-9d18-424e-a886-696927db6051")
		)
		(fp_line
			(start 5.301 1.241)
			(end 5.301 4.247)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d3aec13a-567d-4f26-855b-150886676a28")
		)
		(fp_line
			(start 5.261 1.241)
			(end 5.261 4.273)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2f6141b8-fa56-481b-b6c2-dd566e756366")
		)
		(fp_line
			(start 5.221 1.241)
			(end 5.221 4.298)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3f90187b-698d-41a3-a0c9-2214536b1f6d")
		)
		(fp_line
			(start 5.181 1.241)
			(end 5.181 4.323)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f82beff1-e345-4a36-8ac5-2cb57ac695f9")
		)
		(fp_line
			(start 5.141 1.241)
			(end 5.141 4.347)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7351aa88-b949-47d8-908c-cdee4584157d")
		)
		(fp_line
			(start 5.101 1.241)
			(end 5.101 4.371)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c3a2493e-e7c0-476b-911c-bb50817026ae")
		)
		(fp_line
			(start 5.061 1.241)
			(end 5.061 4.395)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "03934ec4-fd81-4b75-8f83-89d73777630a")
		)
		(fp_line
			(start 5.021 1.241)
			(end 5.021 4.417)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "28208e90-01a4-4a53-9315-0e4e60941307")
		)
		(fp_line
			(start 4.981 1.241)
			(end 4.981 4.44)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9ad4c9ac-cee1-40d3-a7f2-59466c47bf7d")
		)
		(fp_line
			(start 4.941 1.241)
			(end 4.941 4.462)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "41688e30-5d33-4a1c-b91b-15c6616113e2")
		)
		(fp_line
			(start 4.901 1.241)
			(end 4.901 4.483)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d9b3ffec-872d-4210-a998-7d0b26ebcc3f")
		)
		(fp_line
			(start 4.861 1.241)
			(end 4.861 4.504)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "853b95d5-dec9-493e-928b-eb1b2aa0ce24")
		)
		(fp_line
			(start 4.821 1.241)
			(end 4.821 4.525)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0ef814dd-f17a-4478-be10-be71e49d8d2b")
		)
		(fp_line
			(start 4.781 1.241)
			(end 4.781 4.545)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bc35d45b-2a28-488e-a48f-110ef69399ac")
		)
		(fp_line
			(start 4.741 1.241)
			(end 4.741 4.564)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "619f43bd-0b9f-4889-b7ee-2ec7f4613b55")
		)
		(fp_line
			(start 4.701 1.241)
			(end 4.701 4.584)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5ffa5068-0df9-4d88-82b1-f9d70d610a87")
		)
		(fp_line
			(start 4.661 1.241)
			(end 4.661 4.603)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a84fcd19-e9c4-4b36-96c2-7fd9d1d4fa98")
		)
		(fp_line
			(start 4.621 1.241)
			(end 4.621 4.621)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5f6a5568-4ad7-4f9f-a618-5adaa44bbda4")
		)
		(fp_line
			(start 4.581 1.241)
			(end 4.581 4.639)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0d192248-211c-4059-99b4-3ff94031149e")
		)
		(fp_line
			(start 4.541 1.241)
			(end 4.541 4.657)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "79b5d8b2-618b-431c-b006-2b61025f37c2")
		)
		(fp_line
			(start 4.501 1.241)
			(end 4.501 4.674)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "315d277d-e449-4b48-a0ef-8f5d1be1a237")
		)
		(fp_line
			(start 4.461 1.241)
			(end 4.461 4.69)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6b14c1f3-83d1-4850-85e9-f592c167f02f")
		)
		(fp_line
			(start 4.421 1.241)
			(end 4.421 4.707)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3732f9f5-e076-4e6c-b62f-9f40610d418e")
		)
		(fp_line
			(start 4.381 1.241)
			(end 4.381 4.723)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5f614a8d-4783-4278-a0a5-6dbfa1043f5e")
		)
		(fp_line
			(start 4.341 1.241)
			(end 4.341 4.738)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6897130f-36d6-492f-82f2-bcae53f59a84")
		)
		(fp_line
			(start 4.301 1.241)
			(end 4.301 4.754)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1304d367-690d-4e09-a4df-6e1437c845e4")
		)
		(fp_line
			(start 4.261 1.241)
			(end 4.261 4.768)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fe0ac277-56e5-40e3-8c12-76ff5c246f3b")
		)
		(fp_line
			(start 4.221 1.241)
			(end 4.221 4.783)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6a553ff7-3305-463a-b1e4-a7128204ea97")
		)
		(fp_line
			(start 4.181 1.241)
			(end 4.181 4.797)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "dabfb0b3-bce8-4104-939a-ff2db120fb1e")
		)
		(fp_line
			(start 4.141 1.241)
			(end 4.141 4.811)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "56acaeb0-47f1-479e-8d35-01db94e0faae")
		)
		(fp_line
			(start 4.101 1.241)
			(end 4.101 4.824)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "037ee17d-0590-449b-bbce-77c1705182f6")
		)
		(fp_line
			(start 4.061 1.241)
			(end 4.061 4.837)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "49ca1546-1824-430f-bc34-78838c0a312d")
		)
		(fp_line
			(start 4.021 1.241)
			(end 4.021 4.85)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7d84c7c8-64b9-4a9e-b7c7-e722f0587950")
		)
		(fp_line
			(start 3.981 1.241)
			(end 3.981 4.862)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "aad5b5d4-09fc-4831-aa82-f029688bada7")
		)
		(fp_line
			(start 3.941 1.241)
			(end 3.941 4.874)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "745c7bcb-4e60-48c1-ab11-cd1a72fc5477")
		)
		(fp_line
			(start 3.901 1.241)
			(end 3.901 4.885)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6c9b8bcc-2af1-4bb5-b9af-ab5af2afcdc6")
		)
		(fp_line
			(start 3.861 1.241)
			(end 3.861 4.897)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e665f4e6-87f0-4e11-9f55-f2abeda5a481")
		)
		(fp_line
			(start 3.821 1.241)
			(end 3.821 4.907)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fa558647-fdc3-405c-9a90-f245b77e829d")
		)
		(fp_line
			(start 3.781 1.241)
			(end 3.781 4.918)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3096ec7e-f7a4-476f-86e1-8623e3c018c6")
		)
		(fp_circle
			(center 2.5 0)
			(end 7.62 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(fill no)
			(layer "F.SilkS")
			(uuid "258ecd5d-ba52-4323-a896-a66a2aadf72e")
		)
		(fp_circle
			(center 2.5 0)
			(end 7.75 0)
			(stroke
				(width 0.05)
				(type solid)
			)
			(fill no)
			(layer "F.CrtYd")
			(uuid "4fd5af4b-4245-4f95-90e1-8dc724dc4e4c")
		)
		(fp_line
			(start -1.288861 -2.6875)
			(end -1.288861 -1.6875)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "645a2082-1a9a-4e2f-a8af-38e05b33ded1")
		)
		(fp_line
			(start -1.788861 -2.1875)
			(end -0.788861 -2.1875)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5b4531fa-7c33-4ff3-9e87-6f9331da321a")
		)
		(fp_circle
			(center 2.5 0)
			(end 7.5 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(fill no)
			(layer "F.Fab")
			(uuid "bdde8555-0701-4481-85f3-e3e4c405ff73")
		)
		(fp_text user "${REFERENCE}"
			(at 2.5 0 90)
			(layer "F.Fab")
			(uuid "aa145f57-2953-40f4-8ffd-75557c481946")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0 90)
			(size 2 2)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 2 "Net-(P3-P1)")
			(pintype "passive")
			(uuid "aeded7e3-b278-4188-a018-5517ed785333")
		)
		(pad "2" thru_hole circle
			(at 5 0 90)
			(size 2 2)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "GND")
			(pintype "passive")
			(uuid "ddeafcc4-fac1-48aa-a9d5-a388bf01a059")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Capacitor_THT.3dshapes/CP_Radial_D10.0mm_P5.00mm.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:C_Disc_D4.7mm_W2.5mm_P5.00mm"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a581ea")
		(at 137.16 125.095 90)
		(descr "C, Disc series, Radial, pin pitch=5.00mm, , diameter*width=4.7*2.5mm^2, Capacitor, http://www.vishay.com/docs/45233/krseries.pdf")
		(tags "C Disc series Radial pin pitch 5.00mm  diameter 4.7mm width 2.5mm Capacitor")
		(property "Reference" "C2"
			(at 2.5 -2.5 90)
			(layer "F.SilkS")
			(uuid "35ce4d94-5d87-4b69-b7ef-c5dc3648837e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "680nF"
			(at 2.5 2.5 90)
			(layer "F.Fab")
			(uuid "ad9a6130-2fe8-4f8d-a7c9-6af3a1b3662f")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 90)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "7acf57a5-ff22-4848-8250-5c3755951b9b")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 90)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "5554f48a-50b8-4319-9992-88a8ae23c924")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "C? C_????_* C_???? SMD*_c Capacitor*")
		(path "/00000000-0000-0000-0000-00004549f3be")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 4.97 -1.37)
			(end 4.97 -1.055)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "538b7753-5aa1-497b-a67d-a5d68a09bdae")
		)
		(fp_line
			(start 0.03 -1.37)
			(end 4.97 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e8bbeb14-3d9f-4b9d-b48d-7a409351c917")
		)
		(fp_line
			(start 0.03 -1.37)
			(end 0.03 -1.055)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4c842c54-6037-42d6-a34f-d01e6de24cc6")
		)
		(fp_line
			(start 4.97 1.055)
			(end 4.97 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8b150ed6-a531-40c4-8a47-675e4ecfb3a4")
		)
		(fp_line
			(start 0.03 1.055)
			(end 0.03 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2f0aefe9-a16f-4773-9936-deff229bfd64")
		)
		(fp_line
			(start 0.03 1.37)
			(end 4.97 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b12ed8a2-3a06-4f0d-a23a-fd6955b96413")
		)
		(fp_line
			(start 6.05 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "00040d5a-4677-46ab-8b1e-60b5c75cf028")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "387df14d-70fe-4141-a9f5-aff9d01fef82")
		)
		(fp_line
			(start 6.05 1.5)
			(end 6.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "9bf4ebb7-394a-4819-9b02-0c46edc4aada")
		)
		(fp_line
			(start -1.05 1.5)
			(end 6.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "07f64759-a837-4753-9c21-1c1b8f123889")
		)
		(fp_line
			(start 4.85 -1.25)
			(end 0.15 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "aebabbe2-ab4f-4049-8483-f9cec2223177")
		)
		(fp_line
			(start 0.15 -1.25)
			(end 0.15 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "3a69d3bc-0889-4f5c-8697-3a52dd39e2a0")
		)
		(fp_line
			(start 4.85 1.25)
			(end 4.85 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "23ab2612-74bf-4a89-8643-e3e99a6a2dfa")
		)
		(fp_line
			(start 0.15 1.25)
			(end 4.85 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2fd11e17-c05e-4f62-8076-2d4106f99124")
		)
		(fp_text user "${REFERENCE}"
			(at 2.5 0 90)
			(layer "F.Fab")
			(uuid "fcc437fe-2f7b-4d43-b021-c94db6b35bbd")
			(effects
				(font
					(size 0.94 0.94)
					(thickness 0.141)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 90)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 3 "Net-(P2-P1)")
			(pintype "passive")
			(uuid "87622d23-7d7a-471d-bcd4-c13eacfb9e91")
		)
		(pad "2" thru_hole circle
			(at 5 0 90)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 4 "Net-(U1A-K)")
			(pintype "passive")
			(uuid "434f4a07-999c-4c64-a829-a4342b3a5aca")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Capacitor_THT.3dshapes/C_Disc_D4.7mm_W2.5mm_P5.00mm.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:MountingHole_3.2mm_M3_DIN965_Pad"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a58203")
		(at 125.095 93.98)
		(descr "Mounting Hole 3.2mm, M3, DIN965")
		(tags "mounting hole 3.2mm m3 din965")
		(property "Reference" "P5"
			(at 4.505 -1.28 0)
			(layer "F.SilkS")
			(uuid "258a0f6c-51a1-49fa-8e62-f92667f4ad68")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "MOUNTING_HOLE"
			(at 2.905 2.52 0)
			(layer "F.Fab")
			(uuid "e5a2195f-6a99-4cf9-9991-e80a29632f05")
			(effects
				(font
					(size 0.8 0.8)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "90f87de6-f907-483e-aeff-26aa555d3485")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "dd42ef63-7144-436d-9e7d-849817f87959")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054a5890a")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole exclude_from_pos_files exclude_from_bom)
		(fp_circle
			(center 0 0)
			(end 2.8 0)
			(stroke
				(width 0.15)
				(type solid)
			)
			(fill no)
			(layer "Cmts.User")
			(uuid "4969424e-fc1f-420e-9c32-9913477f1434")
		)
		(fp_circle
			(center 0 0)
			(end 3.05 0)
			(stroke
				(width 0.05)
				(type solid)
			)
			(fill no)
			(layer "F.CrtYd")
			(uuid "0e3e3107-3fbd-4db1-933c-bad046ea7045")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "719866c2-b4e9-4609-8cae-19889eca2737")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0)
			(size 5.6 5.6)
			(drill 3.2)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 8 "unconnected-(P5-Pad1)")
			(pinfunction "1")
			(pintype "passive+no_connect")
			(uuid "344b68cd-a8b0-4964-89b9-749801da5c3e")
		)
		(embedded_fonts no)
	)
	(footprint "Footprints:MountingHole_3.2mm_M3_DIN965_Pad"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a58207")
		(at 169.545 93.98)
		(descr "Mounting Hole 3.2mm, M3, DIN965")
		(tags "mounting hole 3.2mm m3 din965")
		(property "Reference" "P6"
			(at -3.945 -1.98 180)
			(layer "F.SilkS")
			(uuid "0cd3e423-45e5-4d4b-9231-bf0559877b8c")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "MOUNTING_HOLE"
			(at -2.745 2.52 0)
			(layer "F.Fab")
			(uuid "8fd9406c-3be1-4c9c-8645-6f7a4592302b")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "665d47c9-04bd-4b24-9cfb-0bccfb482c15")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "2d54038b-2a23-4d77-a56c-cfc3d09847fe")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054a58c65")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole exclude_from_pos_files exclude_from_bom)
		(fp_circle
			(center 0 0)
			(end 2.8 0)
			(stroke
				(width 0.15)
				(type solid)
			)
			(fill no)
			(layer "Cmts.User")
			(uuid "357f6710-7915-4de2-bce2-ddad7ffd83a6")
		)
		(fp_circle
			(center 0 0)
			(end 3.05 0)
			(stroke
				(width 0.05)
				(type solid)
			)
			(fill no)
			(layer "F.CrtYd")
			(uuid "e73e9944-25e6-443b-aa58-5e7111a9542b")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "eede304f-9d7f-42b6-9a6b-171546617f40")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0)
			(size 5.6 5.6)
			(drill 3.2)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 9 "unconnected-(P6-Pad1)")
			(pinfunction "1")
			(pintype "passive+no_connect")
			(uuid "e71faccf-d887-486c-a426-242f3bc2bebe")
		)
		(embedded_fonts no)
	)
	(footprint "Footprints:MountingHole_3.2mm_M3_DIN965_Pad"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a5820b")
		(at 169.545 132.715)
		(descr "Mounting Hole 3.2mm, M3, DIN965")
		(tags "mounting hole 3.2mm m3 din965")
		(property "Reference" "P7"
			(at 0 -3.8 0)
			(layer "F.SilkS")
			(uuid "35d2cf6e-4ccd-41f4-b863-a785fa3b5f1b")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "MOUNTING_HOLE"
			(at -6.245 2.285 0)
			(layer "F.Fab")
			(uuid "62dff599-d8f4-4019-8e69-10509cc778a6")
			(effects
				(font
					(size 0.8 0.8)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "76591c0a-9a89-4ec9-9622-bbea5adb6fef")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "1f8f9820-6eb9-43ef-9e95-f320ec1ba21c")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054a58c8a")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole exclude_from_pos_files exclude_from_bom)
		(fp_circle
			(center 0 0)
			(end 2.8 0)
			(stroke
				(width 0.15)
				(type solid)
			)
			(fill no)
			(layer "Cmts.User")
			(uuid "3a7e4919-9df1-48d6-a9f6-22fe705104ba")
		)
		(fp_circle
			(center 0 0)
			(end 3.05 0)
			(stroke
				(width 0.05)
				(type solid)
			)
			(fill no)
			(layer "F.CrtYd")
			(uuid "68947465-20a9-441a-92c6-1c4a40ed9311")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "6a2b7bfe-ad3f-4374-bc35-16860f3ba495")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0)
			(size 5.6 5.6)
			(drill 3.2)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 10 "unconnected-(P7-Pad1)")
			(pinfunction "1")
			(pintype "passive+no_connect")
			(uuid "f4ac570c-5d1b-4481-8a1c-82f0cf4dcaaa")
		)
		(embedded_fonts no)
	)
	(footprint "Footprints:R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a58213")
		(at 136.271 107.95 -90)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=7.62mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 7.62mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R1"
			(at 0.381 -2.37 90)
			(layer "F.SilkS")
			(uuid "e6adfdaa-13df-4a89-b733-417ab9ece03b")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "1.5K"
			(at 3.81 2.37 90)
			(layer "F.Fab")
			(uuid "cfb9774b-1b6e-4641-944a-c7ab0bcfb96b")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "5d53f0ae-45f0-4458-b09a-25f7f513605b")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "9f13b920-047d-424d-92b4-c9aaa275e2da")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R_* Resistor_*")
		(path "/00000000-0000-0000-0000-00004549f38a")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 0.54 1.37)
			(end 7.08 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5abb106e-7486-4ae8-80ed-7f45281d4964")
		)
		(fp_line
			(start 7.08 1.37)
			(end 7.08 1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9c76a430-7bab-4f07-a041-ebed1164db24")
		)
		(fp_line
			(start 0.54 1.04)
			(end 0.54 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "383e4e85-2abe-4fb5-97ed-ff99350e9e6f")
		)
		(fp_line
			(start 0.54 -1.04)
			(end 0.54 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d649c987-2d66-48f3-a968-d06641478e3d")
		)
		(fp_line
			(start 0.54 -1.37)
			(end 7.08 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "14ea1350-14aa-485c-bf23-f90cdbdbc322")
		)
		(fp_line
			(start 7.08 -1.37)
			(end 7.08 -1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0ee55101-68ab-46a7-9eb1-fcc3213c8e2d")
		)
		(fp_line
			(start -1.05 1.5)
			(end 8.67 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "a16e7456-9eae-442f-96bf-ce608543e3e7")
		)
		(fp_line
			(start 8.67 1.5)
			(end 8.67 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "5f3b38f8-bdbf-4494-887d-7b3df8dd7e40")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b41bce1c-fb52-43bd-b2db-9bfd9a61bcc9")
		)
		(fp_line
			(start 8.67 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "fb726cbd-ea07-4ab1-8011-ae36c7448ded")
		)
		(fp_line
			(start 0.66 1.25)
			(end 6.96 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "fd9a9384-80fd-4444-800e-e19f218ee312")
		)
		(fp_line
			(start 6.96 1.25)
			(end 6.96 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "16dcd1b7-4b25-45a0-8b6e-8e3ce89d73f6")
		)
		(fp_line
			(start 0 0)
			(end 0.66 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f658f8bf-7893-4ff9-8692-a617dd7a9519")
		)
		(fp_line
			(start 7.62 0)
			(end 6.96 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a8dcd0dc-1ebc-4dc5-ad17-5ef07f6bce70")
		)
		(fp_line
			(start 0.66 -1.25)
			(end 0.66 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "3d01ccaf-20ae-4e02-840a-3e4af43274c4")
		)
		(fp_line
			(start 6.96 -1.25)
			(end 0.66 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "3a0cc8ab-94c1-44cd-88c0-3b4ddfe2444e")
		)
		(fp_text user "${REFERENCE}"
			(at 3.81 0 90)
			(layer "F.Fab")
			(uuid "3208ebf7-d8ae-4d3b-b318-5eb4a757981f")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 12 "Net-(U1A-G)")
			(pintype "passive")
			(uuid "feb2f18d-24ad-4008-aa5c-b444c01530ed")
		)
		(pad "2" thru_hole oval
			(at 7.62 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 4 "Net-(U1A-K)")
			(pintype "passive")
			(uuid "58fbc74c-6eca-4aaa-9941-3e7756811e38")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a58218")
		(at 156.21 95.885 180)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=7.62mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 7.62mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R2"
			(at 3.81 -2.37 0)
			(layer "F.SilkS")
			(uuid "c02361e4-7093-4efa-8319-f70ab5189b75")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "1.5K"
			(at 3.81 2.37 0)
			(layer "F.Fab")
			(uuid "1cf2890e-9645-4a6e-abc0-06c7ed40ddcd")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "e8f1e870-333c-4cc4-bc39-d187dc710950")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "8452e19b-79ff-40a9-88bc-fece0700176d")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R_* Resistor_*")
		(path "/00000000-0000-0000-0000-00004549f39d")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 7.08 1.37)
			(end 7.08 1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "22444b91-a0b7-4fef-b4bc-d65212f7a63f")
		)
		(fp_line
			(start 7.08 -1.37)
			(end 7.08 -1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bfcf14bf-ee87-42fa-b8f6-ecef869bfe3a")
		)
		(fp_line
			(start 0.54 1.37)
			(end 7.08 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6bdbaa7e-f6d5-487d-9dca-4313501e3acf")
		)
		(fp_line
			(start 0.54 1.04)
			(end 0.54 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b45bf438-96dc-4067-93f4-df4a75763c3a")
		)
		(fp_line
			(start 0.54 -1.04)
			(end 0.54 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e83771a7-2d55-4dea-a0ea-1219519252ba")
		)
		(fp_line
			(start 0.54 -1.37)
			(end 7.08 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "56c437bc-14e0-4b13-b36c-dfca1dc7e172")
		)
		(fp_line
			(start 8.67 1.5)
			(end 8.67 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3a84a103-3cfe-42c6-a99e-433ee64dec68")
		)
		(fp_line
			(start 8.67 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "caba309e-14a1-4238-8019-74915ce5b3c9")
		)
		(fp_line
			(start -1.05 1.5)
			(end 8.67 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "4d5e6e17-2d23-4ef6-9953-c51fdc229031")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "2fcbaf45-0096-48f5-81d4-b1afe92dcc50")
		)
		(fp_line
			(start 7.62 0)
			(end 6.96 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "70a4ed21-df38-44fd-a165-ce6eb7543bee")
		)
		(fp_line
			(start 6.96 1.25)
			(end 6.96 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5433f84f-5381-43d4-a9dc-b6f5b8aace07")
		)
		(fp_line
			(start 6.96 -1.25)
			(end 0.66 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "799715ab-b0aa-4df6-8403-99a24ed6c743")
		)
		(fp_line
			(start 0.66 1.25)
			(end 6.96 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "4b1ee1cc-6189-4229-a80a-202324f1de76")
		)
		(fp_line
			(start 0.66 -1.25)
			(end 0.66 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6ccf757b-b6c4-45ed-aecb-69259953b0cb")
		)
		(fp_line
			(start 0 0)
			(end 0.66 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b97b5e36-1246-4bf5-b482-485b1726dcfb")
		)
		(fp_text user "${REFERENCE}"
			(at 3.81 0 0)
			(layer "F.Fab")
			(uuid "785707d2-e95e-476f-9b1e-2150760026e7")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "Net-(U1B-K)")
			(pintype "passive")
			(uuid "66512dbb-55f5-4672-8f38-a2db5bbb6679")
		)
		(pad "2" thru_hole oval
			(at 7.62 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "GND")
			(pintype "passive")
			(uuid "d5df32dc-6c81-4b69-bdcb-79b78c9cb647")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054a58222")
		(at 164.465 117.475 -90)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=7.62mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 7.62mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R4"
			(at 3.81 -2.37 90)
			(layer "F.SilkS")
			(uuid "a58670b1-d425-4d40-a23b-1d786f354ad3")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "47K"
			(at 3.81 2.37 90)
			(layer "F.Fab")
			(uuid "a50e1a94-5e26-4087-9c8c-01814e3faf65")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "deb59616-7e16-4232-ad7a-13ec21f0b986")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "50303c61-2f83-4b86-bf1a-c11d152d0733")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R_* Resistor_*")
		(path "/00000000-0000-0000-0000-00004549f3a2")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 0.54 1.37)
			(end 7.08 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0118e2c0-ae00-44a0-bbac-2d0ccfb9de58")
		)
		(fp_line
			(start 7.08 1.37)
			(end 7.08 1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1170d6c8-0848-4a8a-b60a-b83c346ebcef")
		)
		(fp_line
			(start 0.54 1.04)
			(end 0.54 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "da17759c-cdc8-4884-a5f5-35c4495a51ad")
		)
		(fp_line
			(start 0.54 -1.04)
			(end 0.54 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b30fc593-4c80-4a1c-8e14-648884a5865e")
		)
		(fp_line
			(start 0.54 -1.37)
			(end 7.08 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "28535c1c-98f2-4a8e-8809-f7a339e0d0c4")
		)
		(fp_line
			(start 7.08 -1.37)
			(end 7.08 -1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "86b63ac8-2f90-417d-a21c-9fa24319063e")
		)
		(fp_line
			(start -1.05 1.5)
			(end 8.67 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "651faf4c-03c0-4c21-9c22-3ff71d714c8f")
		)
		(fp_line
			(start 8.67 1.5)
			(end 8.67 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3c34811a-571d-4795-b9d4-dd78e174a8dc")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "043bb72b-1686-4d64-a8d5-800e390beb5a")
		)
		(fp_line
			(start 8.67 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "30bc49c5-c7fc-4528-a957-a1a023854cf9")
		)
		(fp_line
			(start 0.66 1.25)
			(end 6.96 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0e18fa34-3b92-4b59-a590-044c90bfc067")
		)
		(fp_line
			(start 6.96 1.25)
			(end 6.96 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "bf67eef9-837f-4631-beca-1d09daea9ead")
		)
		(fp_line
			(start 0 0)
			(end 0.66 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0a203c9c-0b39-4701-bb07-309982c737fc")
		)
		(fp_line
			(start 7.62 0)
			(end 6.96 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "7e9cb72b-4d24-4b95-9d0b-675f79c70ae8")
		)
		(fp_line
			(start 0.66 -1.25)
			(end 0.66 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d9c68f54-a333-47d1-98ea-333a85fefa15")
		)
		(fp_line
			(start 6.96 -1.25)
			(end 0.66 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "bf6372b9-425f-45f2-8620-77950accd504")
		)
		(fp_text user "${REFERENCE}"
			(at 3.81 0 90)
			(layer "F.Fab")
			(uuid "4ba6a88f-997d-4afc-829b-be1e3451cbc1")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 5 "Net-(P1-PM)")
			(pintype "passive")
			(uuid "3cceafde-73f6-4420-81bc-b45ba5ef5d9d")
		)
		(pad "2" thru_hole oval
			(at 7.62 0 270)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "GND")
			(pintype "passive")
			(uuid "237d5d44-fd22-4dab-93dd-3f87c398aeaf")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:Altech_AK300_1x02_P5.00mm_45-Degree"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a332ee4")
		(at 128.27 112.776 -90)
		(descr "Altech AK300 serie terminal block (Script generated with StandardBox.py) (http://www.altechcorp.com/PDFS/PCBMETRC.PDF)")
		(tags "Altech AK300 serie connector")
		(property "Reference" "P2"
			(at 8.724 1.67 0)
			(layer "F.SilkS")
			(uuid "1de5f3f5-99a1-4c66-a1f1-5c7e8c131e68")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "OUT"
			(at 2.5 7.5 270)
			(layer "F.Fab")
			(uuid "65f929aa-69b9-4d98-a044-f5bcba4f1fa7")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "2ff25d9a-c6df-4e12-933a-d8bbf93f2b7c")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "8a27a22b-9885-4c38-a8da-9fd109a5c08f")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-00004549f46c")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole)
		(fp_line
			(start -2.62 6.62)
			(end 7.62 6.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0ffb4796-1c11-4a4f-aaf7-7f426a9371ed")
		)
		(fp_line
			(start -3 -3.5)
			(end -3 -6.5)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bbea8c4a-d2b3-438b-8f3c-081a0d357876")
		)
		(fp_line
			(start -2.62 -6.12)
			(end -2.62 6.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "85d31338-74bd-476c-88b1-98b2c8ece68b")
		)
		(fp_line
			(start -2.62 -6.12)
			(end 7.62 -6.12)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "da92b19b-c307-4291-b51d-159aa67a572e")
		)
		(fp_line
			(start 7.62 -6.12)
			(end 7.62 6.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6035b8eb-6009-42e2-b4a6-48a28b4e6cc1")
		)
		(fp_line
			(start -3 -6.5)
			(end 0 -6.5)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "72a5a806-b79f-42c5-9542-03d4b8dc1e17")
		)
		(fp_line
			(start -2.75 6.75)
			(end 7.75 6.75)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "020e54af-0e2a-491f-8fb2-4646e7f782e2")
		)
		(fp_line
			(start -2.75 -6.25)
			(end -2.75 6.75)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "35d36650-28d1-44a0-a318-0c4152589c88")
		)
		(fp_line
			(start -2.75 -6.25)
			(end 7.75 -6.25)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "5c892d4d-8c84-41d0-8c82-710df6a32fc8")
		)
		(fp_line
			(start 7.75 -6.25)
			(end 7.75 6.75)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "80273cdd-26aa-4284-a472-0b613a4d3bbb")
		)
		(fp_line
			(start -2.5 6.5)
			(end -2.5 -5.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6683ddf7-c59e-4884-918e-a7678701e634")
		)
		(fp_line
			(start 7.5 6.5)
			(end -2.5 6.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f37af704-e3a8-4636-b203-e562d3dadd20")
		)
		(fp_line
			(start -2.5 -5.5)
			(end -2 -6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b4572d41-9c16-438d-a5a8-38cbd910ce86")
		)
		(fp_line
			(start -2 -6)
			(end 7.5 -6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c70206a9-2340-49ac-8398-87f9d3d173f7")
		)
		(fp_line
			(start 7.5 -6)
			(end 7.5 6.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "04f4d0dc-5d6e-4c61-9b81-f0b15d839afa")
		)
		(fp_text user "${REFERENCE}"
			(at 2.7 2.75 270)
			(layer "F.Fab")
			(uuid "70fa3a97-6353-42cf-b946-cc7c0c8d02fe")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0 270)
			(size 3 3)
			(drill 1.5)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 3 "Net-(P2-P1)")
			(pinfunction "P1")
			(pintype "passive")
			(uuid "013d680a-efce-4eb6-a226-1b7a2fef9c33")
		)
		(pad "2" thru_hole circle
			(at 5 0 270)
			(size 3 3)
			(drill 1.5)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "GND")
			(pinfunction "PM")
			(pintype "passive")
			(uuid "e99f56de-529e-492f-a24a-0cba9f0cc86a")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/TerminalBlock_Altech.3dshapes/Altech_AK300_1x02_P5.00mm_45-Degree.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:Altech_AK300_1x02_P5.00mm_45-Degree"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a332f0f")
		(at 128.27 100.711 -90)
		(descr "Altech AK300 serie terminal block (Script generated with StandardBox.py) (http://www.altechcorp.com/PDFS/PCBMETRC.PDF)")
		(tags "Altech AK300 serie connector")
		(property "Reference" "P3"
			(at 2.489 -7.43 270)
			(layer "F.SilkS")
			(uuid "02e5a7b1-7d8c-489f-8433-9aadf895de6d")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "POWER"
			(at 2.5 7.5 270)
			(layer "F.Fab")
			(uuid "7e89ba4e-23c0-43b1-abd9-965acfa17cb1")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "611a3613-09ac-41b5-af5f-c133123796e0")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "87af6020-b6e8-4c49-9e31-067539e7a660")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-00004549f4a5")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole)
		(fp_line
			(start -2.62 6.62)
			(end 7.62 6.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e2ce49ff-2c6c-4f41-b735-54130cdb29c2")
		)
		(fp_line
			(start -3 -3.5)
			(end -3 -6.5)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8bd50d0b-27ea-4daa-b26c-4502569e5cb9")
		)
		(fp_line
			(start -2.62 -6.12)
			(end -2.62 6.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a731da27-757d-4fb9-9ff2-97441e864e99")
		)
		(fp_line
			(start -2.62 -6.12)
			(end 7.62 -6.12)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9648e9d9-122c-4ad6-bf64-ef21cef0ed8a")
		)
		(fp_line
			(start 7.62 -6.12)
			(end 7.62 6.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6223e13c-150e-4eda-9a61-d49aacb3fda8")
		)
		(fp_line
			(start -3 -6.5)
			(end 0 -6.5)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b97b5f1b-4a0f-4a67-908e-1d6ed2235a67")
		)
		(fp_line
			(start -2.75 6.75)
			(end 7.75 6.75)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "95636f05-76ed-40e7-971e-8be4cbd55644")
		)
		(fp_line
			(start -2.75 -6.25)
			(end -2.75 6.75)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "18228733-77cb-4f54-93b9-9dbdf536e329")
		)
		(fp_line
			(start -2.75 -6.25)
			(end 7.75 -6.25)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "e8156356-2780-4525-98fd-f7db32bd1bb6")
		)
		(fp_line
			(start 7.75 -6.25)
			(end 7.75 6.75)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "af235725-085e-4fc3-bb8d-859ee4d20ccd")
		)
		(fp_line
			(start -2.5 6.5)
			(end -2.5 -5.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "8f685bd1-6f12-4d45-81e9-6f4ecc2a1d5b")
		)
		(fp_line
			(start 7.5 6.5)
			(end -2.5 6.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "7f4e6e52-edd3-459e-9d35-5a5fe2111e35")
		)
		(fp_line
			(start -2.5 -5.5)
			(end -2 -6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "90368aaf-2e7f-4c7a-9531-a93b7c3512cc")
		)
		(fp_line
			(start -2 -6)
			(end 7.5 -6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "98f89488-2b27-4157-9d99-0474bdd712a7")
		)
		(fp_line
			(start 7.5 -6)
			(end 7.5 6.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ac8faa98-6671-459a-8d7c-8dc6e1c331ff")
		)
		(fp_text user "${REFERENCE}"
			(at 2.7 2.75 270)
			(layer "F.Fab")
			(uuid "41b13464-8e72-4def-a331-5d9bb51ba6f9")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0 270)
			(size 3 3)
			(drill 1.5)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 2 "Net-(P3-P1)")
			(pinfunction "P1")
			(pintype "passive")
			(uuid "808e4f1b-ce1e-4bfb-b9bd-15d1ce3526c9")
		)
		(pad "2" thru_hole circle
			(at 5 0 270)
			(size 3 3)
			(drill 1.5)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "GND")
			(pinfunction "PM")
			(pintype "passive")
			(uuid "3fdadd90-878d-4ae5-b745-a995af1fd466")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/TerminalBlock_Altech.3dshapes/Altech_AK300_1x02_P5.00mm_45-Degree.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:Altech_AK300_1x02_P5.00mm_45-Degree"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a332f3a")
		(at 145.542 131.191)
		(descr "Altech AK300 serie terminal block (Script generated with StandardBox.py) (http://www.altechcorp.com/PDFS/PCBMETRC.PDF)")
		(tags "Altech AK300 serie connector")
		(property "Reference" "P4"
			(at -4.342 -1.291 0)
			(layer "F.SilkS")
			(uuid "91406c33-85d6-4050-9303-40b5c1b5f60b")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "CONN_2"
			(at 2.5 7.5 0)
			(layer "F.Fab")
			(uuid "7dd79e7c-8386-43cb-a2ff-3ff01661162a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "df529e3d-ccd9-44a7-858b-47c0134ec743")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "11c6c3c4-64c2-43a8-bfd6-2c198ed8c7a1")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-0000456a8acc")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole)
		(fp_line
			(start -3 -6.5)
			(end 0 -6.5)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cd985cf9-a0aa-4986-a4d3-f1bfce21f217")
		)
		(fp_line
			(start -3 -3.5)
			(end -3 -6.5)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4e3dd076-a421-406d-ba88-5697bbb75e46")
		)
		(fp_line
			(start -2.62 -6.12)
			(end -2.62 6.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a4d1deb0-9296-4d9f-8b45-b343befbe2e3")
		)
		(fp_line
			(start -2.62 -6.12)
			(end 7.62 -6.12)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "93598b18-c998-4372-9788-2f8d4ef554be")
		)
		(fp_line
			(start -2.62 6.62)
			(end 7.62 6.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d4458632-e639-44be-9358-72631de2a01d")
		)
		(fp_line
			(start 7.62 -6.12)
			(end 7.62 6.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fa4e4619-2310-496a-9c63-4fb385cf27a0")
		)
		(fp_line
			(start -2.75 -6.25)
			(end -2.75 6.75)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "4c6eccd5-595c-4ceb-8380-52442af8459a")
		)
		(fp_line
			(start -2.75 -6.25)
			(end 7.75 -6.25)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "cba14841-a452-4c9b-9507-5b7451565bd1")
		)
		(fp_line
			(start -2.75 6.75)
			(end 7.75 6.75)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "1f32d7a7-10e6-4534-ab64-06f920bb3c81")
		)
		(fp_line
			(start 7.75 -6.25)
			(end 7.75 6.75)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "734cae64-189e-4c92-b71a-465824a483af")
		)
		(fp_line
			(start -2.5 -5.5)
			(end -2 -6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "88daabc2-7baa-469e-885c-84076515ba9d")
		)
		(fp_line
			(start -2.5 6.5)
			(end -2.5 -5.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6bcf8031-d505-40be-8432-a37959232a88")
		)
		(fp_line
			(start -2 -6)
			(end 7.5 -6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "206fb420-11fd-4175-ab91-6280d5bb344f")
		)
		(fp_line
			(start 7.5 -6)
			(end 7.5 6.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "98b43065-d84b-44b1-b696-04d7fa6e01b3")
		)
		(fp_line
			(start 7.5 6.5)
			(end -2.5 6.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5f86e13c-e7ea-4464-9023-f90e566ad23f")
		)
		(fp_text user "${REFERENCE}"
			(at 2.7 2.75 0)
			(layer "F.Fab")
			(uuid "c7ef4da4-c27f-45c8-922f-f35e61e9b9af")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0)
			(size 3 3)
			(drill 1.5)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 6 "Net-(P4-P1)")
			(pinfunction "P1")
			(pintype "passive")
			(uuid "fcd5fa4c-c38c-40c8-8c89-1d3c7bb39912")
		)
		(pad "2" thru_hole circle
			(at 5 0)
			(size 3 3)
			(drill 1.5)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 7 "Net-(P4-PM)")
			(pinfunction "PM")
			(pintype "passive")
			(uuid "c8a3dfdc-1f64-457c-968b-294e9ed86941")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/TerminalBlock_Altech.3dshapes/Altech_AK300_1x02_P5.00mm_45-Degree.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a33370f")
		(at 133.985 125.095 180)
		(descr "Resistor, Axial_DIN0207 series, Axial, Horizontal, pin pitch=7.62mm, 0.25W = 1/4W, length*diameter=6.3*2.5mm^2, http://cdn-reichelt.de/documents/datenblatt/B400/1_4W%23YAG.pdf")
		(tags "Resistor Axial_DIN0207 series Axial Horizontal pin pitch 7.62mm 0.25W = 1/4W length 6.3mm diameter 2.5mm")
		(property "Reference" "R3"
			(at 3.81 -2.37 0)
			(layer "F.SilkS")
			(uuid "591a2eee-2875-409c-9699-e38786a0b3ef")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "100K"
			(at 3.81 2.37 0)
			(layer "F.Fab")
			(uuid "bf466a44-a47a-47bf-9bf4-4ec230443781")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "0f644dfb-a7ff-4d47-88de-6ae0524d8719")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "99946438-3b8d-4862-a10a-972d5870f7aa")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R_* Resistor_*")
		(path "/00000000-0000-0000-0000-00004549f3ad")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole)
		(fp_line
			(start 7.08 1.37)
			(end 7.08 1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8b8bedf0-844c-489d-a93b-81443259ce0c")
		)
		(fp_line
			(start 7.08 -1.37)
			(end 7.08 -1.04)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "96d4d1d3-bf12-4d28-8cf1-3b7c16797ff0")
		)
		(fp_line
			(start 0.54 1.37)
			(end 7.08 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "60ed77fd-ded4-468d-8cfa-48053ed9ae49")
		)
		(fp_line
			(start 0.54 1.04)
			(end 0.54 1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "07ef2c0c-4626-4e80-ae05-834ef85a45b4")
		)
		(fp_line
			(start 0.54 -1.04)
			(end 0.54 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "53db9c37-17b8-4367-8c97-0d8aea121fdd")
		)
		(fp_line
			(start 0.54 -1.37)
			(end 7.08 -1.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "12e29739-3a80-4975-98e5-6238be3dc02f")
		)
		(fp_line
			(start 8.67 1.5)
			(end 8.67 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "d5d296dc-cfe7-4736-b9e0-9ad186b8e447")
		)
		(fp_line
			(start 8.67 -1.5)
			(end -1.05 -1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "d8ca0f82-97c2-41fb-b693-b33dc6875437")
		)
		(fp_line
			(start -1.05 1.5)
			(end 8.67 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "33012263-96e0-445b-b02c-28f56bde8650")
		)
		(fp_line
			(start -1.05 -1.5)
			(end -1.05 1.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b782aadb-6915-4c99-839d-3d4a4d86bbe4")
		)
		(fp_line
			(start 7.62 0)
			(end 6.96 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "43aa7b18-09de-4f40-bc0e-86350383a69d")
		)
		(fp_line
			(start 6.96 1.25)
			(end 6.96 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "67ddc8a2-cdc3-4709-8b2f-1a7615bf9e27")
		)
		(fp_line
			(start 6.96 -1.25)
			(end 0.66 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f99ae0bf-293b-46ee-9e27-a02ecdec5629")
		)
		(fp_line
			(start 0.66 1.25)
			(end 6.96 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0fad34fb-fe30-4480-bc56-296f2afb7371")
		)
		(fp_line
			(start 0.66 -1.25)
			(end 0.66 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "1e5afe06-a631-41ba-955f-14e879810920")
		)
		(fp_line
			(start 0 0)
			(end 0.66 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2cae6772-d558-4741-9d76-de8bc0f95555")
		)
		(fp_text user "${REFERENCE}"
			(at 3.81 0 0)
			(layer "F.Fab")
			(uuid "6b826615-57ff-490a-a30f-a6749c4293f9")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 3 "Net-(P2-P1)")
			(pintype "passive")
			(uuid "61e29ef0-01c0-4612-860d-df6b334f1798")
		)
		(pad "2" thru_hole oval
			(at 7.62 0 180)
			(size 1.6 1.6)
			(drill 0.8)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "GND")
			(pintype "passive")
			(uuid "587d6891-33ea-463a-b75d-744f879246c9")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_THT.3dshapes/R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:Valve_ECC-83-1"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005a334ccc")
		(at 149.225 113.665)
		(descr "Valve ECC-83-1 round pins")
		(tags "Valve ECC-83-1 round pins")
		(property "Reference" "U1"
			(at 12.7 -1.397 0)
			(layer "F.SilkS")
			(uuid "193bb3e4-9a9e-4fde-9c68-cbe480b4f1b0")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "ECC83"
			(at -3.45 6.68 0)
			(layer "F.Fab")
			(uuid "66700218-3bd9-42b5-b01a-7b2716a5d3b1")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "a1ec01f8-b968-4956-b610-68cbf8f2bd01")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "c723bbc2-8381-4500-a1a3-fe8a66c54524")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000048b4f266")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole)
		(fp_circle
			(center 0 0.05)
			(end 10.16 1.32)
			(stroke
				(width 0.12)
				(type solid)
			)
			(fill no)
			(layer "F.SilkS")
			(uuid "3740888a-cee5-487c-9aeb-a26dbd05cbd3")
		)
		(fp_circle
			(center 0 0.05)
			(end 0 -10.55)
			(stroke
				(width 0.05)
				(type solid)
			)
			(fill no)
			(layer "F.CrtYd")
			(uuid "71f6f59e-fef5-4293-ad74-357de6313efc")
		)
		(fp_circle
			(center 0 0.05)
			(end 0 -10.1)
			(stroke
				(width 0.1)
				(type solid)
			)
			(fill no)
			(layer "F.Fab")
			(uuid "9b7c5890-6d2f-413b-8eb2-527fe4da88ff")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0.05 0)
			(layer "F.Fab")
			(uuid "eb442c0e-1edb-4bdb-98b8-ece48445b24e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 3.45 4.8)
			(size 2.03 2.03)
			(drill 1.02)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 12 "Net-(U1A-G)")
			(pinfunction "A")
			(pintype "passive")
			(uuid "beb4c69f-bce5-4736-bb41-d9523aa79796")
		)
		(pad "2" thru_hole circle
			(at 5.6 1.87)
			(size 2.03 2.03)
			(drill 1.02)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 5 "Net-(P1-PM)")
			(pinfunction "G")
			(pintype "input")
			(uuid "7b5a3ca9-d484-4629-8665-1188b5e86489")
		)
		(pad "3" thru_hole circle
			(at 5.6 -1.78)
			(size 2.03 2.03)
			(drill 1.02)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 13 "Net-(U1B-K)")
			(pinfunction "K")
			(pintype "passive")
			(uuid "81d31795-db6a-4081-9936-04fde2386907")
		)
		(pad "4" thru_hole circle
			(at 3.45 -4.71)
			(size 2.03 2.03)
			(drill 1.02)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 7 "Net-(P4-PM)")
			(pinfunction "F1")
			(pintype "input")
			(uuid "136be1b9-9ca0-4c4a-839d-a587728a5bc9")
		)
		(pad "5" thru_hole circle
			(at 0 -5.85)
			(size 2.03 2.03)
			(drill 1.02)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 7 "Net-(P4-PM)")
			(pinfunction "F1")
			(pintype "input")
			(uuid "61e9c53c-6235-4dad-a1fc-75828cb63335")
		)
		(pad "6" thru_hole circle
			(at -3.46 -4.71)
			(size 2.03 2.03)
			(drill 1.02)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 2 "Net-(P3-P1)")
			(pinfunction "A")
			(pintype "passive")
			(uuid "e8fa3875-2076-4de2-b1d6-9c3fb76b7ace")
		)
		(pad "7" thru_hole circle
			(at -5.61 -1.78)
			(size 2.03 2.03)
			(drill 1.02)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 12 "Net-(U1A-G)")
			(pinfunction "G")
			(pintype "input")
			(uuid "ec6f9db9-8766-4ee9-b242-67cc63ed2949")
		)
		(pad "8" thru_hole circle
			(at -5.61 1.83)
			(size 2.03 2.03)
			(drill 1.02)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 4 "Net-(U1A-K)")
			(pinfunction "K")
			(pintype "passive")
			(uuid "93d4f75f-1985-434b-9e98-bd382358b589")
		)
		(pad "9" thru_hole circle
			(at -3.46 4.8)
			(size 2.03 2.03)
			(drill 1.02)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 6 "Net-(P4-P1)")
			(pinfunction "F2")
			(pintype "input")
			(uuid "16b5f9b7-8f48-404c-82f4-861d94de7c41")
		)
		(embedded_fonts no)
		(model "${KIPRJMOD}/3d_shapes/ecc83.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:Altech_AK300_1x02_P5.00mm_45-Degree"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005d888bc3")
		(at 166.37 105.41 90)
		(descr "Altech AK300 serie terminal block (Script generated with StandardBox.py) (http://www.altechcorp.com/PDFS/PCBMETRC.PDF)")
		(tags "Altech AK300 serie connector")
		(property "Reference" "P1"
			(at 0 -7.2 90)
			(layer "F.SilkS")
			(uuid "651bc4eb-6aa9-415b-ac04-9edaac9128c9")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "IN"
			(at 2.5 7.5 90)
			(layer "F.Fab")
			(uuid "be2fe93d-8435-4a4b-b759-a6cef067cfae")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 90)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "792b5cdc-afd1-4ca7-a81f-be8f3edef4e5")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 90)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "1b52c54f-cdb4-46e6-84a1-4865eede5836")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-00004549f464")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole)
		(fp_line
			(start -3 -6.5)
			(end 0 -6.5)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "794ed367-24a2-4bed-bed9-321c7e61af82")
		)
		(fp_line
			(start 7.62 -6.12)
			(end 7.62 6.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "dae3c5c1-b442-47d6-b83c-f364b2026d9b")
		)
		(fp_line
			(start -2.62 -6.12)
			(end 7.62 -6.12)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f59aee27-0529-4c3a-b0b9-78236f75b405")
		)
		(fp_line
			(start -2.62 -6.12)
			(end -2.62 6.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b7ac5d07-2c13-46a6-826a-237155fd402c")
		)
		(fp_line
			(start -3 -3.5)
			(end -3 -6.5)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bb9f2a6e-caf2-4d43-b080-64204d848b60")
		)
		(fp_line
			(start -2.62 6.62)
			(end 7.62 6.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2c911fdc-1e76-4322-9429-bfa6d44c9a4d")
		)
		(fp_line
			(start 7.75 -6.25)
			(end 7.75 6.75)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "d8a01a70-8f02-4c7a-b643-25abb6e4dfee")
		)
		(fp_line
			(start -2.75 -6.25)
			(end 7.75 -6.25)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "0b1b9812-a376-45a4-b07d-995032b4f6d0")
		)
		(fp_line
			(start -2.75 -6.25)
			(end -2.75 6.75)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "35921df2-7535-4aa4-835a-1c1dbf176e09")
		)
		(fp_line
			(start -2.75 6.75)
			(end 7.75 6.75)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "f24e1aee-5393-49a9-80f4-fab5d7e6f23e")
		)
		(fp_line
			(start 7.5 -6)
			(end 7.5 6.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "09d7a72c-1ea3-43e4-85b7-df922191e0b9")
		)
		(fp_line
			(start -2 -6)
			(end 7.5 -6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "830633f1-ee61-4bf9-a48b-307935404f40")
		)
		(fp_line
			(start -2.5 -5.5)
			(end -2 -6)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b5c49f35-bf0e-4f3d-a74c-8dcefd39e426")
		)
		(fp_line
			(start 7.5 6.5)
			(end -2.5 6.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "8e90972c-71fd-436e-8b71-1eca86ffedc4")
		)
		(fp_line
			(start -2.5 6.5)
			(end -2.5 -5.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c3b8dc38-0f2e-4993-b43b-89a571994c9a")
		)
		(fp_text user "${REFERENCE}"
			(at 2.7 2.75 90)
			(layer "F.Fab")
			(uuid "481ae0dc-92d2-480d-8c1f-a91c6d746058")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0 90)
			(size 3 3)
			(drill 1.5)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "GND")
			(pinfunction "P1")
			(pintype "passive")
			(uuid "da86310b-0262-4c39-9590-c439cb4ea431")
		)
		(pad "2" thru_hole circle
			(at 5 0 90)
			(size 3 3)
			(drill 1.5)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 5 "Net-(P1-PM)")
			(pinfunction "PM")
			(pintype "passive")
			(uuid "db9237f9-114e-4120-b2d2-4152d767eb57")
		)
		(embedded_fonts no)
		(model "${KICAD6_3DMODEL_DIR}/TerminalBlock_Altech.3dshapes/Altech_AK300_1x02_P5.00mm_45-Degree.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Footprints:MountingHole_3.2mm_M3_DIN965_Pad"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005d88946b")
		(at 125.095 132.715)
		(descr "Mounting Hole 3.2mm, M3, DIN965")
		(tags "mounting hole 3.2mm m3 din965")
		(property "Reference" "P8"
			(at 0 -3.8 0)
			(layer "F.SilkS")
			(uuid "391f0409-3b13-444d-a77f-878ea79c9456")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "MOUNTING_HOLE"
			(at 0 3.8 0)
			(layer "F.Fab")
			(uuid "c16ac3a3-a20c-4dac-bf57-8cf3f7406b23")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "d00689b9-2e6d-428b-815a-0a69055e499b")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "1a6634ee-6f26-41b7-8b17-a60e2dd73457")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054a58ca3")
		(sheetname "Racine")
		(sheetfile "ecc83-pp.kicad_sch")
		(attr through_hole exclude_from_pos_files exclude_from_bom)
		(fp_circle
			(center 0 0)
			(end 2.8 0)
			(stroke
				(width 0.15)
				(type solid)
			)
			(fill no)
			(layer "Cmts.User")
			(uuid "bc27e896-5029-4ce8-ae9d-c614ceb74a4f")
		)
		(fp_circle
			(center 0 0)
			(end 3.05 0)
			(stroke
				(width 0.05)
				(type solid)
			)
			(fill no)
			(layer "F.CrtYd")
			(uuid "764a218d-e2f8-42fb-9305-9696f145cb5d")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "646f2bdf-77f1-4fdf-96fa-8f7fb84b6236")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0)
			(size 5.6 5.6)
			(drill 3.2)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 11 "unconnected-(P8-Pad1)")
			(pinfunction "1")
			(pintype "passive+no_connect")
			(uuid "21304fa0-8617-428a-9dfd-966010a238e6")
		)
		(embedded_fonts no)
	)
	(gr_line
		(start 173.355 90.17)
		(end 173.355 136.525)
		(stroke
			(width 0.127)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "00000000-0000-0000-0000-00005a3345b3")
	)
	(gr_line
		(start 121.285 90.17)
		(end 121.285 136.525)
		(stroke
			(width 0.127)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "00000000-0000-0000-0000-00005d88943e")
	)
	(gr_line
		(start 173.355 90.17)
		(end 121.285 90.17)
		(stroke
			(width 0.127)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "258201f7-c476-442a-b854-de67eac27cf4")
	)
	(gr_line
		(start 121.285 136.525)
		(end 173.355 136.525)
		(stroke
			(width 0.127)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "9b19456e-3ad1-4dde-92c4-23451559e1ba")
	)
	(segment
		(start 139.573 99.695)
		(end 141.605 99.695)
		(width 0.8)
		(layer "B.Cu")
		(net 2)
		(uuid "1d6285fd-2d49-4956-932f-458079ff628a")
	)
	(segment
		(start 139.446 99.822)
		(end 139.573 99.695)
		(width 0.8)
		(layer "B.Cu")
		(net 2)
		(uuid "32199045-7b83-44fa-a0ee-e9986ff68da8")
	)
	(segment
		(start 144.78 99.695)
		(end 141.605 99.695)
		(width 0.8)
		(layer "B.Cu")
		(net 2)
		(uuid "391ae493-c92e-47da-8afe-abe1020ef4d2")
	)
	(segment
		(start 139.446 99.822)
		(end 129.159 99.822)
		(width 0.8)
		(layer "B.Cu")
		(net 2)
		(uuid "5bc7790b-95ff-4279-ae23-ddd41e4d5261")
	)
	(segment
		(start 145.7706 100.6856)
		(end 144.78 99.695)
		(width 0.8)
		(layer "B.Cu")
		(net 2)
		(uuid "7ae751b0-ad79-4045-b2be-442fd2252a45")
	)
	(segment
		(start 145.7706 108.91012)
		(end 145.7706 100.6856)
		(width 0.8)
		(layer "B.Cu")
		(net 2)
		(uuid "cc1b45ba-83d2-4ac1-873b-99326f78e367")
	)
	(segment
		(start 129.159 99.822)
		(end 128.27 100.711)
		(width 0.8)
		(layer "B.Cu")
		(net 2)
		(uuid "e4754195-9def-428c-bfb0-dba618ea5ccc")
	)
	(segment
		(start 133.858 123.825)
		(end 133.858 122.837172)
		(width 0.8)
		(layer "B.Cu")
		(net 3)
		(uuid "1763dda4-5bc1-49f5-b62b-d2f2293118d7")
	)
	(segment
		(start 133.858 124.968)
		(end 133.858 123.825)
		(width 0.8)
		(layer "B.Cu")
		(net 3)
		(uuid "29dac00a-0a8a-4e42-a43b-e84a153871b0")
	)
	(segment
		(start 133.858 122.837172)
		(end 132.08 121.059172)
		(width 0.8)
		(layer "B.Cu")
		(net 3)
		(uuid "5c569a56-2bd2-4a50-b31f-e50325b77ed1")
	)
	(segment
		(start 133.985 125.095)
		(end 133.858 124.968)
		(width 0.8)
		(layer "B.Cu")
		(net 3)
		(uuid "76df2c56-63e6-4960-a62a-03363fa814a8")
	)
	(segment
		(start 132.08 114.4224)
		(end 130.4336 112.776)
		(width 0.8)
		(layer "B.Cu")
		(net 3)
		(uuid "ab72afcc-896d-4a27-99b8-f0afcc85c497")
	)
	(segment
		(start 132.08 121.059172)
		(end 132.08 114.4224)
		(width 0.8)
		(layer "B.Cu")
		(net 3)
		(uuid "db089e40-ef96-4245-8f30-af808e45320f")
	)
	(segment
		(start 130.4336 112.776)
		(end 128.27 112.776)
		(width 0.8)
		(layer "B.Cu")
		(net 3)
		(uuid "e2fa26aa-1341-4367-94b4-4635866f1aeb")
	)
	(segment
		(start 137.16 125.095)
		(end 133.985 125.095)
		(width 0.8)
		(layer "B.Cu")
		(net 3)
		(uuid "e7c50475-4975-4e6b-90c0-66c74a4edd88")
	)
	(segment
		(start 136.271 115.57)
		(end 137.16 115.57)
		(width 0.8)
		(layer "B.Cu")
		(net 4)
		(uuid "85fe529e-0f31-4426-90fb-f0a251407089")
	)
	(segment
		(start 137.16 120.015)
		(end 137.16 115.57)
		(width 0.8)
		(layer "B.Cu")
		(net 4)
		(uuid "a8f7b422-101c-4965-915d-e5dd69e04c9f")
	)
	(segment
		(start 137.16 115.57)
		(end 143.49984 115.57)
		(width 0.8)
		(layer "B.Cu")
		(net 4)
		(uuid "c766a778-05a9-412d-80dc-9c3dc4557624")
	)
	(segment
		(start 143.49984 115.57)
		(end 143.61668 115.45316)
		(width 0.8)
		(layer "B.Cu")
		(net 4)
		(uuid "fd79da36-99c5-4013-b635-9ee1f5541e8c")
	)
	(segment
		(start 161.417 106.506)
		(end 161.417 111.4806)
		(width 0.8)
		(layer "B.Cu")
		(net 5)
		(uuid "14d8d88a-7f5a-4442-9306-7d36ecae1603")
	)
	(segment
		(start 157.4038 115.4938)
		(end 159.385 117.475)
		(width 0.8)
		(layer "B.Cu")
		(net 5)
		(uuid "247123a1-f7a3-47f5-95eb-67c3c61f9616")
	)
	(segment
		(start 161.417 111.4806)
		(end 157.4038 115.4938)
		(width 0.8)
		(layer "B.Cu")
		(net 5)
		(uuid "27c677f8-c961-4ca4-b0eb-0a5fd50f3b54")
	)
	(segment
		(start 164.592 100.283)
		(end 163.292001 101.582999)
		(width 0.8)
		(layer "B.Cu")
		(net 5)
		(uuid "345a4445-9bef-46fa-a228-c99e5e00c755")
	)
	(segment
		(start 162.450001 101.582999)
		(end 161.417 102.616)
		(width 0.8)
		(layer "B.Cu")
		(net 5)
		(uuid "3f0c0146-d2ef-4af6-b385-82c21c3c247a")
	)
	(segment
		(start 154.83332 115.4938)
		(end 157.4038 115.4938)
		(width 0.8)
		(layer "B.Cu")
		(net 5)
		(uuid "4b87cc06-41eb-4015-97dc-8e2b90d662c6")
	)
	(segment
		(start 159.385 117.475)
		(end 164.465 117.475)
		(width 0.8)
		(layer "B.Cu")
		(net 5)
		(uuid "635b5e92-eb27-4e08-a95d-168b98661cc4")
	)
	(segment
		(start 161.417 102.616)
		(end 161.417 106.506)
		(width 0.8)
		(layer "B.Cu")
		(net 5)
		(uuid "82c1ea56-324d-4125-9d56-54e209c9a9fe")
	)
	(segment
		(start 163.292001 101.582999)
		(end 162.450001 101.582999)
		(width 0.8)
		(layer "B.Cu")
		(net 5)
		(uuid "ef7c3498-8917-4f90-a7ac-024f512d9637")
	)
	(segment
		(start 145.7706 127.298923)
		(end 145.7706 126.492)
		(width 0.8)
		(layer "B.Cu")
		(net 6)
		(uuid "1fbbe805-b3f8-4ea1-bf81-0d63bdf234ed")
	)
	(segment
		(start 145.7706 118.41988)
		(end 145.7706 124.587)
		(width 0.8)
		(layer "B.Cu")
		(net 6)
		(uuid "25ec7cce-8cfb-4c3d-ad23-5e56fb3b633a")
	)
	(segment
		(start 145.368 129.54)
		(end 145.368 127.701523)
		(width 0.8)
		(layer "B.Cu")
		(net 6)
		(uuid "49ad258e-6d3c-4907-a4a5-b281af550842")
	)
	(segment
		(start 145.368 127.701523)
		(end 145.7706 127.298923)
		(width 0.8)
		(layer "B.Cu")
		(net 6)
		(uuid "51dc66a4-9084-4133-a999-eec42fb3b535")
	)
	(segment
		(start 145.7706 124.587)
		(end 145.7706 126.492)
		(width 0.8)
		(layer "B.Cu")
		(net 6)
		(uuid "ea04ecd2-2a6e-4212-931b-3bff06bcd845")
	)
	(segment
		(start 149.225 107.7722)
		(end 151.54148 107.7722)
		(width 0.8)
		(layer "B.Cu")
		(net 7)
		(uuid "1cc68d87-1722-4832-bf18-2cd2a9784e1b")
	)
	(segment
		(start 149.86 111.125)
		(end 149.86 124.841)
		(width 0.8)
		(layer "B.Cu")
		(net 7)
		(uuid "3a80fbe2-0d9d-477a-bd78-21fb6bee3a8f")
	)
	(segment
		(start 149.225 110.49)
		(end 149.86 111.125)
		(width 0.8)
		(layer "B.Cu")
		(net 7)
		(uuid "5db575f5-9a7d-410e-acd8-e38875f1d705")
	)
	(segment
		(start 150.542 131.191)
		(end 150.542 125.523)
		(width 0.8)
		(layer "B.Cu")
		(net 7)
		(uuid "6803d821-0e79-4908-9c15-0a44a45ccd45")
	)
	(segment
		(start 151.54148 107.7722)
		(end 152.6794 108.91012)
		(width 0.8)
		(layer "B.Cu")
		(net 7)
		(uuid "9d2e5597-7ad7-44fc-95cb-48ac71f23103")
	)
	(segment
		(start 149.225 107.7722)
		(end 149.225 110.49)
		(width 0.8)
		(layer "B.Cu")
		(net 7)
		(uuid "c1eb496b-3bd3-4711-b96b-db9fe95dfe1f")
	)
	(segment
		(start 150.542 125.523)
		(end 149.86 124.841)
		(width 0.8)
		(layer "B.Cu")
		(net 7)
		(uuid "e08953d7-ea3c-4e15-85f8-f785fdb6ec3b")
	)
	(segment
		(start 136.525 121.92)
		(end 139.065 124.46)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "04a58d1d-cb04-44a5-8ce1-8153b1fad396")
	)
	(segment
		(start 133.985 109.22)
		(end 133.985 116.205)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "0a72f525-0d1b-4f34-ba8b-901e6ea242fc")
	)
	(segment
		(start 152.250834 134.055312)
		(end 154.94 131.366146)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "10bd612f-b54f-41dc-aced-b8515b59a488")
	)
	(segment
		(start 135.89 107.95)
		(end 135.255 107.95)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "4e5e9938-57c9-4bb2-970b-cc43e5bd53e9")
	)
	(segment
		(start 135.509 117.729)
		(end 135.509 121.031)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "5c81feef-8ec5-4bc1-ae1b-5599c38f7110")
	)
	(segment
		(start 154.94 120.68048)
		(end 152.6794 118.41988)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "5d1258a4-1077-4c6c-9ccf-40f02d473dcf")
	)
	(segment
		(start 136.398 121.92)
		(end 136.525 121.92)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "681e3661-1a5f-4900-8a55-ef1da47f12da")
	)
	(segment
		(start 143.61668 111.8362)
		(end 141.6812 111.8362)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "68e3d464-ca76-4e1c-805a-6c3a1a6e955e")
	)
	(segment
		(start 135.255 107.95)
		(end 133.985 109.22)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "78379560-5dbe-4fc6-9630-87c5563746cd")
	)
	(segment
		(start 135.89 107.95)
		(end 137.795 107.95)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "7f5d6d5e-9240-4f26-9164-bf04cb23d38d")
	)
	(segment
		(start 139.065 124.46)
		(end 139.065 132.240853)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "887389ab-9ae7-4ff8-90d2-f94110ed397a")
	)
	(segment
		(start 139.065 132.240853)
		(end 140.879459 134.055312)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "8b8dbf7c-c5ca-4c53-8d7d-d407a486fd9c")
	)
	(segment
		(start 154.94 131.366146)
		(end 154.94 120.68048)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "be88cb55-7bbb-4725-92cb-340cac9671c5")
	)
	(segment
		(start 135.509 121.031)
		(end 136.398 121.92)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "cf694a67-043c-48f2-bff0-6fe15c14f4f1")
	)
	(segment
		(start 140.879459 134.055312)
		(end 152.250834 134.055312)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "f399e874-5b8e-44b7-ae3c-3853d4d7d6a4")
	)
	(segment
		(start 133.985 116.205)
		(end 135.509 117.729)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "f64f10da-e04f-4197-858e-631549c213c1")
	)
	(segment
		(start 141.6812 111.8362)
		(end 137.795 107.95)
		(width 0.8)
		(layer "B.Cu")
		(net 12)
		(uuid "fc238a17-323f-4bf7-b628-df5cf98728b2")
	)
	(segment
		(start 156.21 110.5)
		(end 154.825 111.885)
		(width 0.8)
		(layer "B.Cu")
		(net 13)
		(uuid "68e5151f-158a-448d-b289-866542b41d38")
	)
	(segment
		(start 156.21 95.885)
		(end 156.21 110.5)
		(width 0.8)
		(layer "B.Cu")
		(net 13)
		(uuid "d7a2ea41-4fea-4c56-89ef-27fec710c0ad")
	)
	(zone
		(net 1)
		(net_name "GND")
		(layer "B.Cu")
		(uuid "00000000-0000-0000-0000-00004eed97a2")
		(hatch edge 0.508)
		(connect_pads
			(clearance 0.635)
		)
		(min_thickness 0.381)
		(filled_areas_thickness no)
		(fill yes
			(thermal_gap 0.254)
			(thermal_bridge_width 0.50038)
		)
		(polygon
			(pts
				(xy 172.085 135.89) (xy 172.085 91.313) (xy 122.555 91.44) (xy 122.555 135.89)
			)
		)
		(filled_polygon
			(layer "B.Cu")
			(pts
				(xy 166.963652 91.344896) (xy 167.029723 91.397309) (xy 167.066509 91.473199) (xy 167.066725 91.557534)
				(xy 167.030329 91.633611) (xy 167.026302 91.638515) (xy 166.974827 91.699116) (xy 166.806002 91.897873)
				(xy 166.596944 92.206211) (xy 166.422443 92.535355) (xy 166.284558 92.881422) (xy 166.284557 92.881423)
				(xy 166.284556 92.881428) (xy 166.184892 93.240384) (xy 166.124623 93.608012) (xy 166.104454 93.98)
				(xy 166.124623 94.351988) (xy 166.184892 94.719616) (xy 166.236786 94.90652) (xy 166.284557 95.078576)
				(xy 166.284558 95.078577) (xy 166.419518 95.417304) (xy 166.422445 95.424648) (xy 166.596943 95.753787)
				(xy 166.806005 96.06213) (xy 167.047179 96.346062) (xy 167.047184 96.346067) (xy 167.047186 96.346069)
				(xy 167.317638 96.602254) (xy 167.317639 96.602255) (xy 167.61421 96.827702) (xy 167.614212 96.827703)
				(xy 167.933413 97.019761) (xy 167.933417 97.019763) (xy 167.933419 97.019764) (xy 168.271523 97.176187)
				(xy 168.624556 97.295138) (xy 168.988382 97.375222) (xy 169.358733 97.4155) (xy 169.358736 97.4155)
				(xy 169.731264 97.4155) (xy 169.731267 97.4155) (xy 170.101618 97.375222) (xy 170.465444 97.295138)
				(xy 170.818477 97.176187) (xy 171.156581 97.019764) (xy 171.47579 96.827702) (xy 171.772362 96.602254)
				(xy 171.772377 96.602239) (xy 171.772816 96.601868) (xy 171.772985 96.601779) (xy 171.776449 96.599147)
				(xy 171.776901 96.599742) (xy 171.847629 96.56294) (xy 171.931924 96.560328) (xy 172.009005 96.594548)
				(xy 172.063605 96.658823) (xy 172.084909 96.740424) (xy 172.085 96.746294) (xy 172.085 129.948705)
				(xy 172.066234 130.030926) (xy 172.013651 130.096862) (xy 171.937668 130.133454) (xy 171.853332 130.133454)
				(xy 171.777349 130.096862) (xy 171.772835 130.093147) (xy 171.772371 130.092753) (xy 171.77236 130.092745)
				(xy 171.47579 129.867298) (xy 171.475791 129.867298) (xy 171.475789 129.867297) (xy 171.475787 129.867296)
				(xy 171.156586 129.675238) (xy 170.818475 129.518812) (xy 170.818472 129.518811) (xy 170.465448 129.399863)
				(xy 170.465445 129.399862) (xy 170.297746 129.362949) (xy 170.101618 129.319778) (xy 170.101613 129.319777)
				(xy 170.101611 129.319777) (xy 169.73127 129.2795) (xy 169.731267 129.2795) (xy 169.358733 129.2795)
				(xy 169.358729 129.2795) (xy 168.988388 129.319777) (xy 168.988384 129.319777) (xy 168.988382 129.319778)
				(xy 168.835048 129.353529) (xy 168.624554 129.399862) (xy 168.624551 129.399863) (xy 168.271527 129.518811)
				(xy 168.271524 129.518812) (xy 167.933413 129.675238) (xy 167.614212 129.867296) (xy 167.61421 129.867297)
				(xy 167.317639 130.092744) (xy 167.317638 130.092745) (xy 167.047186 130.34893) (xy 166.806002 130.632873)
				(xy 166.596944 130.941211) (xy 166.422443 131.270355) (xy 166.284558 131.616422) (xy 166.284557 131.616423)
				(xy 166.24349 131.764334) (xy 166.184892 131.975384) (xy 166.124623 132.343012) (xy 166.104454 132.715)
				(xy 166.120949 133.019235) (xy 166.124623 133.086985) (xy 166.184892 133.454615) (xy 166.284557 133.813576)
				(xy 166.284558 133.813577) (xy 166.422443 134.159644) (xy 166.596944 134.488788) (xy 166.801893 134.791066)
				(xy 166.806005 134.79713) (xy 167.047179 135.081062) (xy 167.317638 135.337254) (xy 167.597029 135.549641)
				(xy 167.651126 135.614338) (xy 167.671796 135.696101) (xy 167.654943 135.778736) (xy 167.603905 135.845875)
				(xy 167.528792 135.88422) (xy 167.482347 135.89) (xy 127.157653 135.89) (xy 127.075432 135.871234)
				(xy 127.009496 135.818651) (xy 126.972904 135.742668) (xy 126.972904 135.658332) (xy 127.009496 135.582349)
				(xy 127.042969 135.549642) (xy 127.322362 135.337254) (xy 127.592821 135.081062) (xy 127.833995 134.79713)
				(xy 128.043057 134.488787) (xy 128.217555 134.159648) (xy 128.355444 133.813572) (xy 128.455108 133.454616)
				(xy 128.515377 133.086988) (xy 128.535546 132.715) (xy 128.515377 132.343012) (xy 128.455108 131.975384)
				(xy 128.355444 131.616428) (xy 128.217555 131.270352) (xy 128.043057 130.941213) (xy 127.833995 130.63287)
				(xy 127.592821 130.348938) (xy 127.581512 130.338226) (xy 127.322361 130.092745) (xy 127.32236 130.092744)
				(xy 127.025789 129.867297) (xy 127.025787 129.867296) (xy 126.706586 129.675238) (xy 126.368475 129.518812)
				(xy 126.368472 129.518811) (xy 126.015448 129.399863) (xy 126.015445 129.399862) (xy 125.847746 129.362949)
				(xy 125.651618 129.319778) (xy 125.651613 129.319777) (xy 125.651611 129.319777) (xy 125.28127 129.2795)
				(xy 125.281267 129.2795) (xy 124.908733 129.2795) (xy 124.908729 129.2795) (xy 124.538388 129.319777)
				(xy 124.538384 129.319777) (xy 124.538382 129.319778) (xy 124.385048 129.353529) (xy 124.174554 129.399862)
				(xy 124.174551 129.399863) (xy 123.821527 129.518811) (xy 123.821524 129.518812) (xy 123.483413 129.675238)
				(xy 123.164212 129.867296) (xy 123.16421 129.867297) (xy 122.867628 130.092753) (xy 122.867165 130.093147)
				(xy 122.866992 130.093236) (xy 122.863551 130.095853) (xy 122.8631 130.09526) (xy 122.792346 130.132065)
				(xy 122.708051 130.134667) (xy 122.630975 130.100436) (xy 122.576384 130.036153) (xy 122.55509 129.95455)
				(xy 122.555 129.948705) (xy 122.555 125.34519) (xy 125.339466 125.34519) (xy 125.386514 125.500288)
				(xy 125.386519 125.5003) (xy 125.484392 125.683404) (xy 125.484395 125.68341) (xy 125.6161 125.843894)
				(xy 125.616105 125.843899) (xy 125.776589 125.975604) (xy 125.776595 125.975607) (xy 125.959699 126.07348)
				(xy 125.959711 126.073485) (xy 126.114809 126.120533) (xy 126.11481 126.120532) (xy 126.11481 125.410496)
				(xy 126.126955 125.422641) (xy 126.239852 125.480165) (xy 126.333519 125.495) (xy 126.396481 125.495)
				(xy 126.490148 125.480165) (xy 126.603045 125.422641) (xy 126.61519 125.410496) (xy 126.61519 126.120533)
				(xy 126.770288 126.073485) (xy 126.7703 126.07348) (xy 126.953404 125.975607) (xy 126.95341 125.975604)
				(xy 127.113894 125.843899) (xy 127.113899 125.843894) (xy 127.245604 125.68341) (xy 127.245607 125.683404)
				(xy 127.34348 125.5003) (xy 127.343485 125.500288) (xy 127.390533 125.34519) (xy 126.680496 125.34519)
				(xy 126.692641 125.333045) (xy 126.750165 125.220148) (xy 126.769986 125.095) (xy 126.750165 124.969852)
				(xy 126.692641 124.856955) (xy 126.680496 124.84481) (xy 127.390533 124.84481) (xy 127.390533 124.844809)
				(xy 127.343485 124.689711) (xy 127.34348 124.689699) (xy 127.245607 124.506595) (xy 127.245604 124.506589)
				(xy 127.113899 124.346105) (xy 127.113894 124.3461) (xy 126.95341 124.214395) (xy 126.953404 124.214392)
				(xy 126.770302 124.11652) (xy 126.61519 124.069466) (xy 126.61519 124.779504) (xy 126.603045 124.767359)
				(xy 126.490148 124.709835) (xy 126.396481 124.695) (xy 126.333519 124.695) (xy 126.239852 124.709835)
				(xy 126.126955 124.767359) (xy 126.11481 124.779504) (xy 126.11481 124.069466) (xy 125.959697 124.11652)
				(xy 125.776595 124.214392) (xy 125.776589 124.214395) (xy 125.616105 124.3461) (xy 125.6161 124.346105)
				(xy 125.484395 124.506589) (xy 125.484392 124.506595) (xy 125.386519 124.689699) (xy 125.386514 124.689711)
				(xy 125.339466 124.844809) (xy 125.339467 124.84481) (xy 126.049504 124.84481) (xy 126.037359 124.856955)
				(xy 125.979835 124.969852) (xy 125.960014 125.095) (xy 125.979835 125.220148) (xy 126.037359 125.333045)
				(xy 126.049504 125.34519) (xy 125.339466 125.34519) (xy 122.555 125.34519) (xy 122.555 117.776001)
				(xy 126.511082 117.776001) (xy 126.530728 118.038152) (xy 126.530728 118.038157) (xy 126.589226 118.29445)
				(xy 126.685269 118.539164) (xy 126.816715 118.766835) (xy 126.864908 118.827267) (xy 127.592722 118.099453)
				(xy 127.616059 118.153553) (xy 127.720756 118.294185) (xy 127.855062 118.406882) (xy 127.946844 118.452976)
				(xy 127.218113 119.181707) (xy 127.39054 119.299267) (xy 127.390545 119.29927) (xy 127.627395 119.41333)
				(xy 127.87861 119.490819) (xy 128.138549 119.529999) (xy 128.13856 119.53) (xy 128.40144 119.53)
				(xy 128.40145 119.529999) (xy 128.661389 119.490819) (xy 128.912604 119.41333) (xy 129.149454 119.29927)
				(xy 129.149459 119.299267) (xy 129.321885 119.181708) (xy 128.595747 118.45557) (xy 128.608891 118.450787)
				(xy 128.755373 118.354445) (xy 128.875688 118.226918) (xy 128.948545 118.100723) (xy 129.675089 118.827266)
				(xy 129.723284 118.766835) (xy 129.85473 118.539164) (xy 129.950773 118.29445) (xy 130.009271 118.038157)
				(xy 130.009271 118.038152) (xy 130.028918 117.776001) (xy 130.028918 117.775998) (xy 130.009271 117.513847)
				(xy 130.009271 117.513842) (xy 129.950773 117.257549) (xy 129.85473 117.012835) (xy 129.723285 116.785166)
				(xy 129.723284 116.785164) (xy 129.67509 116.724731) (xy 128.947276 117.452544) (xy 128.923941 117.398447)
				(xy 128.819244 117.257815) (xy 128.684938 117.145118) (xy 128.593154 117.099022) (xy 129.321886 116.37029)
				(xy 129.149464 116.252735) (xy 129.14945 116.252727) (xy 128.91261 116.13867) (xy 128.661389 116.06118)
				(xy 128.40145 116.022) (xy 128.138549 116.022) (xy 127.87861 116.06118) (xy 127.627389 116.13867)
				(xy 127.390544 116.25273) (xy 127.390537 116.252734) (xy 127.218112 116.37029) (xy 127.944252 117.096429)
				(xy 127.931109 117.101213) (xy 127.784627 117.197555) (xy 127.664312 117.325082) (xy 127.591453 117.451276)
				(xy 126.864908 116.724731) (xy 126.816714 116.785165) (xy 126.816713 116.785167) (xy 126.685269 117.012835)
				(xy 126.589226 117.257549) (xy 126.530728 117.513842) (xy 126.530728 117.513847) (xy 126.511082 117.775998)
				(xy 126.511082 117.776001) (xy 122.555 117.776001) (xy 122.555 111.211817) (xy 126.1345 111.211817)
				(xy 126.134501 114.34018) (xy 126.137334 114.376197) (xy 126.137333 114.376197) (xy 126.137334 114.376203)
				(xy 126.137335 114.376204) (xy 126.182131 114.530393) (xy 126.263865 114.668598) (xy 126.377402 114.782135)
				(xy 126.377404 114.782136) (xy 126.377403 114.782136) (xy 126.466413 114.834776) (xy 126.515607 114.863869)
				(xy 126.669796 114.908665) (xy 126.700918 114.911114) (xy 126.705817 114.9115) (xy 126.705818 114.911499)
				(xy 126.705819 114.9115) (xy 129.83418 114.911499) (xy 129.870204 114.908665) (xy 130.024393 114.863869)
				(xy 130.162598 114.782135) (xy 130.276135 114.668598) (xy 130.357869 114.530393) (xy 130.357868 114.530393)
				(xy 130.363938 114.520131) (xy 130.365094 114.520815) (xy 130.407445 114.463058) (xy 130.481885 114.42342)
				(xy 130.566151 114.420005) (xy 130.643554 114.453491) (xy 130.665495 114.472313) (xy 130.988997 114.795815)
				(xy 131.033866 114.867224) (xy 131.0445 114.929812) (xy 131.0445 121.003651) (xy 131.044043 121.012945)
				(xy 131.03949 121.059168) (xy 131.043582 121.100725) (xy 131.043583 121.100729) (xy 131.0445 121.110037)
				(xy 131.0445 121.11004) (xy 131.052168 121.187892) (xy 131.059483 121.262166) (xy 131.083678 121.341924)
				(xy 131.110147 121.429184) (xy 131.118694 121.457358) (xy 131.214848 121.637249) (xy 131.252702 121.683374)
				(xy 131.267164 121.700996) (xy 131.344243 121.794919) (xy 131.344246 121.794921) (xy 131.344248 121.794924)
				(xy 131.380166 121.8244) (xy 131.387047 121.830637) (xy 132.766997 123.210587) (xy 132.811866 123.281996)
				(xy 132.8225 123.344584) (xy 132.8225 124.193358) (xy 132.803734 124.275579) (xy 132.791645 124.297)
				(xy 132.759564 124.346105) (xy 132.718186 124.409439) (xy 132.622628 124.627289) (xy 132.622623 124.627304)
				(xy 132.564225 124.857913) (xy 132.564225 124.857915) (xy 132.54458 125.095) (xy 132.564224 125.332079)
				(xy 132.564225 125.332086) (xy 132.622623 125.562695) (xy 132.622628 125.56271) (xy 132.718185 125.78056)
				(xy 132.718188 125.780564) (xy 132.848306 125.979724) (xy 132.848308 125.979726) (xy 132.84831 125.979729)
				(xy 133.009424 126.154745) (xy 133.00943 126.154751) (xy 133.115499 126.237308) (xy 133.197166 126.300872)
				(xy 133.406386 126.414096) (xy 133.40639 126.414098) (xy 133.631398 126.491344) (xy 133.866046 126.530499)
				(xy 133.866047 126.5305) (xy 133.866051 126.5305) (xy 134.103953 126.5305) (xy 134.103953 126.530499)
				(xy 134.338602 126.491344) (xy 134.56361 126.414098) (xy 134.772835 126.300871) (xy 134.94039 126.170458)
				(xy 135.0168 126.134766) (xy 135.056783 126.1305) (xy 136.088217 126.1305) (xy 136.170438 126.149266)
				(xy 136.20461 126.170458) (xy 136.372165 126.300871) (xy 136.58139 126.414098) (xy 136.806398 126.491344)
				(xy 137.041046 126.530499) (xy 137.041047 126.5305) (xy 137.041051 126.5305) (xy 137.278953 126.5305)
				(xy 137.278953 126.530499) (xy 137.513602 126.491344) (xy 137.73861 126.414098) (xy 137.749805 126.408039)
				(xy 137.831046 126.38541) (xy 137.914062 126.40027) (xy 137.982409 126.449678) (xy 138.02255 126.523848)
				(xy 138.0295 126.574698) (xy 138.0295 132.185332) (xy 138.029043 132.194626) (xy 138.02449 132.240849)
				(xy 138.028582 132.282406) (xy 138.028583 132.28241) (xy 138.0295 132.291718) (xy 138.0295 132.291721)
				(xy 138.034552 132.343012) (xy 138.044483 132.44385) (xy 138.084723 132.5765) (xy 138.103693 132.639036)
				(xy 138.103696 132.639044) (xy 138.199845 132.818926) (xy 138.199848 132.818931) (xy 138.231867 132.857946)
				(xy 138.269474 132.90377) (xy 138.329248 132.976605) (xy 138.365166 133.006081) (xy 138.372047 133.012318)
				(xy 140.10799 134.748263) (xy 140.114239 134.755157) (xy 140.143702 134.791059) (xy 140.143707 134.791064)
				(xy 140.181465 134.822051) (xy 140.18148 134.822064) (xy 140.301382 134.920464) (xy 140.301386 134.920467)
				(xy 140.48127 135.016617) (xy 140.481272 135.016617) (xy 140.481273 135.016618) (xy 140.676463 135.075828)
				(xy 140.676466 135.075829) (xy 140.879455 135.095823) (xy 140.879459 135.095823) (xy 140.879461 135.095823)
				(xy 140.925694 135.091269) (xy 140.934987 135.090812) (xy 152.195316 135.090812) (xy 152.204609 135.091269)
				(xy 152.250832 135.095822) (xy 152.250834 135.095822) (xy 152.250835 135.095822) (xy 152.25698 135.095216)
				(xy 152.301699 135.090812) (xy 152.301702 135.090812) (xy 152.453827 135.075829) (xy 152.453829 135.075828)
				(xy 152.453831 135.075828) (xy 152.649018 135.016619) (xy 152.649022 135.016617) (xy 152.828911 134.920464)
				(xy 152.913751 134.850837) (xy 152.986586 134.791064) (xy 153.016057 134.755151) (xy 153.0223 134.748263)
				(xy 155.632954 132.137608) (xy 155.63984 132.131369) (xy 155.675752 132.101898) (xy 155.735525 132.029063)
				(xy 155.805152 131.944223) (xy 155.901306 131.764332) (xy 155.917951 131.70946) (xy 155.960517 131.569139)
				(xy 155.9755 131.417014) (xy 155.9755 131.417011) (xy 155.98051 131.366146) (xy 155.975957 131.319919)
				(xy 155.9755 131.310625) (xy 155.9755 125.34519) (xy 163.439466 125.34519) (xy 163.486514 125.500288)
				(xy 163.486519 125.5003) (xy 163.584392 125.683404) (xy 163.584395 125.68341) (xy 163.7161 125.843894)
				(xy 163.716105 125.843899) (xy 163.876589 125.975604) (xy 163.876595 125.975607) (xy 164.059699 126.07348)
				(xy 164.059711 126.073485) (xy 164.214809 126.120533) (xy 164.21481 126.120532) (xy 164.21481 125.410496)
				(xy 164.226955 125.422641) (xy 164.339852 125.480165) (xy 164.433519 125.495) (xy 164.496481 125.495)
				(xy 164.590148 125.480165) (xy 164.703045 125.422641) (xy 164.71519 125.410496) (xy 164.71519 126.120533)
				(xy 164.870288 126.073485) (xy 164.8703 126.07348) (xy 165.053404 125.975607) (xy 165.05341 125.975604)
				(xy 165.213894 125.843899) (xy 165.213899 125.843894) (xy 165.345604 125.68341) (xy 165.345607 125.683404)
				(xy 165.44348 125.5003) (xy 165.443485 125.500288) (xy 165.490533 125.34519) (xy 164.780496 125.34519)
				(xy 164.792641 125.333045) (xy 164.850165 125.220148) (xy 164.869986 125.095) (xy 164.850165 124.969852)
				(xy 164.792641 124.856955) (xy 164.780496 124.84481) (xy 165.490533 124.84481) (xy 165.490533 124.844809)
				(xy 165.443485 124.689711) (xy 165.44348 124.689699) (xy 165.345607 124.506595) (xy 165.345604 124.506589)
				(xy 165.213899 124.346105) (xy 165.213894 124.3461) (xy 165.05341 124.214395) (xy 165.053404 124.214392)
				(xy 164.870302 124.11652) (xy 164.71519 124.069466) (xy 164.71519 124.779504) (xy 164.703045 124.767359)
				(xy 164.590148 124.709835) (xy 164.496481 124.695) (xy 164.433519 124.695) (xy 164.339852 124.709835)
				(xy 164.226955 124.767359) (xy 164.21481 124.779504) (xy 164.21481 124.069466) (xy 164.059697 124.11652)
				(xy 163.876595 124.214392) (xy 163.876589 124.214395) (xy 163.716105 124.3461) (xy 163.7161 124.346105)
				(xy 163.584395 124.506589) (xy 163.584392 124.506595) (xy 163.486519 124.689699) (xy 163.486514 124.689711)
				(xy 163.439466 124.844809) (xy 163.439467 124.84481) (xy 164.149504 124.84481) (xy 164.137359 124.856955)
				(xy 164.079835 124.969852) (xy 164.060014 125.095) (xy 164.079835 125.220148) (xy 164.137359 125.333045)
				(xy 164.149504 125.34519) (xy 163.439466 125.34519) (xy 155.9755 125.34519) (xy 155.9755 120.735998)
				(xy 155.975957 120.726705) (xy 155.97999 120.685757) (xy 155.98051 120.68048) (xy 155.9755 120.629612)
				(xy 155.960517 120.477487) (xy 155.960517 120.477486) (xy 155.901309 120.282302) (xy 155.901307 120.282299)
				(xy 155.901306 120.282294) (xy 155.861765 120.208318) (xy 155.839064 120.165847) (xy 155.805155 120.102408)
				(xy 155.805155 120.102407) (xy 155.799076 120.095) (xy 155.729716 120.010484) (xy 155.675757 119.944733)
				(xy 155.675746 119.944722) (xy 155.63984 119.915256) (xy 155.632946 119.909007) (xy 154.382041 118.658103)
				(xy 154.337172 118.586694) (xy 154.327122 118.509237) (xy 154.330604 118.465) (xy 154.310221 118.206006)
				(xy 154.249573 117.95339) (xy 154.150154 117.713372) (xy 154.150151 117.713367) (xy 154.150149 117.713363)
				(xy 154.014417 117.491868) (xy 154.014416 117.491867) (xy 154.014412 117.49186) (xy 153.858117 117.308862)
				(xy 153.818989 117.234153) (xy 153.816151 117.149866) (xy 153.850165 117.072694) (xy 153.914294 117.017922)
				(xy 153.995837 116.996399) (xy 154.074729 117.010716) (xy 154.31339 117.109573) (xy 154.566006 117.170221)
				(xy 154.825 117.190604) (xy 155.083994 117.170221) (xy 155.33661 117.109573) (xy 155.576628 117.010154)
				(xy 155.79814 116.874412) (xy 155.995689 116.705689) (xy 156.045948 116.646843) (xy 156.089605 116.595729)
				(xy 156.157274 116.545396) (xy 156.233701 116.5293) (xy 156.896388 116.5293) (xy 156.978609 116.548066)
				(xy 157.030385 116.584803) (xy 158.613527 118.167946) (xy 158.619776 118.17484) (xy 158.64924 118.210743)
				(xy 158.649243 118.210746) (xy 158.649248 118.210752) (xy 158.68876 118.243178) (xy 158.806923 118.340152)
				(xy 158.931766 118.406882) (xy 158.956083 118.41988) (xy 158.986814 118.436306) (xy 159.182002 118.495516)
				(xy 159.182004 118.495516) (xy 159.182007 118.495517) (xy 159.286595 118.505818) (xy 159.384998 118.515511)
				(xy 159.385 118.515511) (xy 159.385002 118.515511) (xy 159.431233 118.510957) (xy 159.440527 118.5105)
				(xy 163.393217 118.5105) (xy 163.475438 118.529266) (xy 163.50961 118.550458) (xy 163.677165 118.680871)
				(xy 163.88639 118.794098) (xy 164.111398 118.871344) (xy 164.346046 118.910499) (xy 164.346047 118.9105)
				(xy 164.346051 118.9105) (xy 164.583953 118.9105) (xy 164.583953 118.910499) (xy 164.818602 118.871344)
				(xy 165.04361 118.794098) (xy 165.252835 118.680871) (xy 165.44057 118.534751) (xy 165.601694 118.359724)
				(xy 165.731812 118.160564) (xy 165.734888 118.153553) (xy 165.770036 118.07342) (xy 165.827374 117.942704)
				(xy 165.885775 117.712085) (xy 165.90542 117.475) (xy 165.885775 117.237915) (xy 165.827374 117.007296)
				(xy 165.822594 116.996399) (xy 165.731814 116.789439) (xy 165.677097 116.705689) (xy 165.601694 116.590276)
				(xy 165.60169 116.590271) (xy 165.601689 116.59027) (xy 165.440575 116.415254) (xy 165.440573 116.415252)
				(xy 165.44057 116.415249) (xy 165.275313 116.286624) (xy 165.252833 116.269127) (xy 165.043613 116.155903)
				(xy 164.818612 116.078659) (xy 164.818606 116.078657) (xy 164.818602 116.078656) (xy 164.818598 116.078655)
				(xy 164.818593 116.078654) (xy 164.583954 116.0395) (xy 164.583949 116.0395) (xy 164.346051 116.0395)
				(xy 164.346046 116.0395) (xy 164.111406 116.078654) (xy 164.111387 116.078659) (xy 163.886386 116.155903)
				(xy 163.677166 116.269128) (xy 163.50961 116.399542) (xy 163.4332 116.435234) (xy 163.393217 116.4395)
				(xy 159.892412 116.4395) (xy 159.810191 116.420734) (xy 159.758415 116.383997) (xy 159.002214 115.627796)
				(xy 158.957345 115.556387) (xy 158.947903 115.472582) (xy 158.975757 115.392979) (xy 159.002206 115.359811)
				(xy 162.109954 112.252062) (xy 162.11684 112.245823) (xy 162.152752 112.216352) (xy 162.212525 112.143517)
				(xy 162.282152 112.058677) (xy 162.378306 111.878786) (xy 162.397749 111.814693) (xy 162.437516 111.683597)
				(xy 162.437516 111.683595) (xy 162.437517 111.683593) (xy 162.448688 111.570176) (xy 162.457511 111.4806)
				(xy 162.452957 111.434365) (xy 162.4525 111.425072) (xy 162.4525 105.159809) (xy 164.616 105.159809)
				(xy 164.616001 105.15981) (xy 165.662013 105.15981) (xy 165.626366 105.278877) (xy 165.616172 105.453906)
				(xy 165.646616 105.626567) (xy 165.66112 105.66019) (xy 164.616002 105.66019) (xy 164.616001 105.660191)
				(xy 164.616001 106.935011) (xy 164.630738 107.009107) (xy 164.686874 107.093121) (xy 164.686878 107.093125)
				(xy 164.770893 107.149263) (xy 164.844978 107.163999) (xy 166.11981 107.163999) (xy 166.11981 106.12148)
				(xy 166.282338 106.16) (xy 166.413684 106.16) (xy 166.544139 106.144752) (xy 166.62019 106.117071)
				(xy 166.62019 107.163998) (xy 166.620191 107.163999) (xy 167.895012 107.163999) (xy 167.969107 107.149261)
				(xy 168.053121 107.093125) (xy 168.053125 107.093121) (xy 168.109263 107.009106) (xy 168.123999 106.935021)
				(xy 168.124 106.935011) (xy 168.124 105.660191) (xy 168.123999 105.66019) (xy 167.077987 105.66019)
				(xy 167.113634 105.541123) (xy 167.123828 105.366094) (xy 167.093384 105.193433) (xy 167.07888 105.15981)
				(xy 168.123998 105.15981) (xy 168.123999 105.159809) (xy 168.123999 103.884988) (xy 168.109261 103.810892)
				(xy 168.053125 103.726878) (xy 168.053121 103.726874) (xy 167.969106 103.670736) (xy 167.895021 103.656)
				(xy 166.620191 103.656) (xy 166.62019 103.656001) (xy 166.62019 104.698519) (xy 166.457662 104.66)
				(xy 166.326316 104.66) (xy 166.195861 104.675248) (xy 166.11981 104.702928) (xy 166.11981 103.656)
				(xy 164.844988 103.656) (xy 164.770892 103.670738) (xy 164.686878 103.726874) (xy 164.686874 103.726878)
				(xy 164.630736 103.810893) (xy 164.616 103.884978) (xy 164.616 105.159809) (xy 162.4525 105.159809)
				(xy 162.4525 103.123412) (xy 162.471266 103.041191) (xy 162.508003 102.989415) (xy 162.823416 102.674002)
				(xy 162.894825 102.629133) (xy 162.957413 102.618499) (xy 163.236483 102.618499) (xy 163.245776 102.618956)
				(xy 163.291999 102.623509) (xy 163.292001 102.623509) (xy 163.292002 102.623509) (xy 163.298147 102.622903)
				(xy 163.342866 102.618499) (xy 163.342869 102.618499) (xy 163.494994 102.603516) (xy 163.494996 102.603515)
				(xy 163.494998 102.603515) (xy 163.690178 102.544308) (xy 163.690179 102.544307) (xy 163.690187 102.544305)
				(xy 163.743082 102.516031) (xy 163.762375 102.50572) (xy 163.870072 102.448154) (xy 163.870073 102.448154)
				(xy 163.870074 102.448153) (xy 163.870078 102.448151) (xy 163.937839 102.39254) (xy 163.988242 102.351177)
				(xy 164.027745 102.318758) (xy 164.027747 102.318755) (xy 164.027753 102.318751) (xy 164.057224 102.282838)
				(xy 164.063463 102.275953) (xy 164.507676 101.83174) (xy 164.579083 101.786873) (xy 164.662888 101.777431)
				(xy 164.742491 101.805285) (xy 164.780165 101.836396) (xy 164.909002 101.974347) (xy 164.909007 101.974351)
				(xy 164.909011 101.974355) (xy 165.135617 102.158713) (xy 165.13562 102.158715) (xy 165.385221 102.310501)
				(xy 165.385226 102.310504) (xy 165.385235 102.310509) (xy 165.385243 102.310512) (xy 165.385246 102.310514)
				(xy 165.653176 102.426893) (xy 165.653189 102.426898) (xy 165.653194 102.4269) (xy 165.934504 102.50572)
				(xy 166.223928 102.5455) (xy 166.223936 102.5455) (xy 166.516064 102.5455) (xy 166.516072 102.5455)
				(xy 166.805496 102.50572) (xy 167.086806 102.4269) (xy 167.086815 102.426895) (xy 167.086823 102.426893)
				(xy 167.335797 102.318748) (xy 167.354765 102.310509) (xy 167.604378 102.158716) (xy 167.830998 101.974347)
				(xy 168.030402 101.760837) (xy 168.198876 101.522164) (xy 168.333282 101.262774) (xy 168.431115 100.987497)
				(xy 168.471074 100.795204) (xy 168.490551 100.701476) (xy 168.490552 100.701466) (xy 168.490553 100.701463)
				(xy 168.51049 100.41) (xy 168.490553 100.118537) (xy 168.490551 100.118531) (xy 168.490551 100.118523)
				(xy 168.449375 99.920376) (xy 168.431115 99.832503) (xy 168.333282 99.557226) (xy 168.198876 99.297836)
				(xy 168.030402 99.059163) (xy 167.830998 98.845653) (xy 167.830992 98.845648) (xy 167.830988 98.845644)
				(xy 167.604382 98.661286) (xy 167.604379 98.661284) (xy 167.354778 98.509498) (xy 167.354753 98.509485)
				(xy 167.086823 98.393106) (xy 167.08681 98.393101) (xy 167.033932 98.378285) (xy 166.805496 98.31428)
				(xy 166.805492 98.314279) (xy 166.805487 98.314278) (xy 166.516073 98.2745) (xy 166.516072 98.2745)
				(xy 166.223928 98.2745) (xy 166.223926 98.2745) (xy 165.934512 98.314278) (xy 165.934505 98.314279)
				(xy 165.934504 98.31428) (xy 165.828757 98.343909) (xy 165.653189 98.393101) (xy 165.653176 98.393106)
				(xy 165.385246 98.509485) (xy 165.385221 98.509498) (xy 165.13562 98.661284) (xy 165.135617 98.661286)
				(xy 164.909011 98.845644) (xy 164.909003 98.845652) (xy 164.709597 99.059163) (xy 164.629292 99.17293)
				(xy 164.566545 99.229279) (xy 164.493053 99.252235) (xy 164.389006 99.262483) (xy 164.389002 99.262484)
				(xy 164.193815 99.321692) (xy 164.013923 99.417848) (xy 164.013922 99.417849) (xy 163.89577 99.514813)
				(xy 163.895761 99.514821) (xy 162.918586 100.491996) (xy 162.847177 100.536865) (xy 162.784589 100.547499)
				(xy 162.505519 100.547499) (xy 162.496226 100.547042) (xy 162.450003 100.542489) (xy 162.450001 100.542489)
				(xy 162.43812 100.543659) (xy 162.40377 100.547042) (xy 162.403766 100.547042) (xy 162.399136 100.547499)
				(xy 162.399133 100.547499) (xy 162.30179 100.557086) (xy 162.247003 100.562482) (xy 162.051814 100.621692)
				(xy 161.871927 100.717844) (xy 161.808357 100.770015) (xy 161.714253 100.847242) (xy 161.714246 100.84725)
				(xy 161.684777 100.883158) (xy 161.67853 100.890051) (xy 160.724055 101.844524) (xy 160.717163 101.850771)
				(xy 160.681254 101.880241) (xy 160.681242 101.880253) (xy 160.648823 101.919759) (xy 160.551844 102.037927)
				(xy 160.520538 102.096499) (xy 160.455695 102.217812) (xy 160.45569 102.217822) (xy 160.396483 102.413002)
				(xy 160.383434 102.545501) (xy 160.37649 102.615997) (xy 160.37649 102.616001) (xy 160.381043 102.662225)
				(xy 160.3815 102.671518) (xy 160.3815 110.973188) (xy 160.362734 111.055409) (xy 160.325997 111.107185)
				(xy 157.030385 114.402797) (xy 156.958976 114.447666) (xy 156.896388 114.4583) (xy 156.163324 114.4583)
				(xy 156.081103 114.439534) (xy 156.019226 114.391869) (xy 155.995694 114.364316) (xy 155.995691 114.364313)
				(xy 155.995689 114.364311) (xy 155.79814 114.195588) (xy 155.798138 114.195586) (xy 155.798136 114.195585)
				(xy 155.798131 114.195582) (xy 155.576636 114.05985) (xy 155.576624 114.059844) (xy 155.336609 113.960426)
				(xy 155.083991 113.899778) (xy 155.083992 113.899778) (xy 155.07303 113.898916) (xy 154.992535 113.873757)
				(xy 154.930927 113.816164) (xy 154.900409 113.737543) (xy 154.907026 113.653468) (xy 154.949466 113.580589)
				(xy 155.019324 113.533342) (xy 155.07303 113.521084) (xy 155.076842 113.520783) (xy 155.083994 113.520221)
				(xy 155.33661 113.459573) (xy 155.576628 113.360154) (xy 155.79814 113.224412) (xy 155.995689 113.055689)
				(xy 156.164412 112.85814) (xy 156.300154 112.636628) (xy 156.399573 112.39661) (xy 156.460221 112.143994)
				(xy 156.480604 111.885) (xy 156.473509 111.794851) (xy 156.485767 111.711413) (xy 156.528426 111.64599)
				(xy 156.902954 111.271462) (xy 156.90984 111.265223) (xy 156.914419 111.261465) (xy 156.945752 111.235752)
				(xy 157.024366 111.13996) (xy 157.075152 111.078077) (xy 157.171306 110.898186) (xy 157.171307 110.898183)
				(xy 157.175792 110.883401) (xy 157.230515 110.702999) (xy 157.230514 110.702999) (xy 157.230517 110.702993)
				(xy 157.2455 110.550868) (xy 157.250511 110.5) (xy 157.249791 110.492694) (xy 157.245957 110.453765)
				(xy 157.2455 110.444472) (xy 157.2455 96.953592) (xy 157.264266 96.871371) (xy 157.295579 96.825248)
				(xy 157.346694 96.769724) (xy 157.476812 96.570564) (xy 157.572374 96.352704) (xy 157.630775 96.122085)
				(xy 157.65042 95.885) (xy 157.630775 95.647915) (xy 157.572374 95.417296) (xy 157.51943 95.296595)
				(xy 157.476814 95.199439) (xy 157.463905 95.17968) (xy 157.346694 95.000276) (xy 157.34669 95.000271)
				(xy 157.346689 95.00027) (xy 157.185575 94.825254) (xy 157.185573 94.825252) (xy 157.18557 94.825249)
				(xy 157.049853 94.719616) (xy 156.997833 94.679127) (xy 156.788613 94.565903) (xy 156.563612 94.488659)
				(xy 156.563606 94.488657) (xy 156.563602 94.488656) (xy 156.563598 94.488655) (xy 156.563593 94.488654)
				(xy 156.328954 94.4495) (xy 156.328949 94.4495) (xy 156.091051 94.4495) (xy 156.091046 94.4495)
				(xy 155.856406 94.488654) (xy 155.856387 94.488659) (xy 155.631386 94.565903) (xy 155.422166 94.679127)
				(xy 155.234436 94.825244) (xy 155.234424 94.825254) (xy 155.07331 95.00027) (xy 154.943185 95.199439)
				(xy 154.847628 95.417289) (xy 154.847623 95.417304) (xy 154.789225 95.647913) (xy 154.789224 95.64792)
				(xy 154.76958 95.885) (xy 154.789224 96.122079) (xy 154.789225 96.122086) (xy 154.847623 96.352695)
				(xy 154.847628 96.35271) (xy 154.943185 96.57056) (xy 154.984567 96.633899) (xy 155.073306 96.769724)
				(xy 155.124419 96.825247) (xy 155.166299 96.898447) (xy 155.1745 96.953592) (xy 155.1745 109.992588)
				(xy 155.155734 110.074809) (xy 155.118997 110.126583) (xy 155.064009 110.181572) (xy 154.992602 110.22644)
				(xy 154.915148 110.23649) (xy 154.825 110.229396) (xy 154.566007 110.249778) (xy 154.31339 110.310426)
				(xy 154.074732 110.409282) (xy 153.991588 110.423409) (xy 153.910549 110.400062) (xy 153.847665 110.343865)
				(xy 153.815391 110.265949) (xy 153.82012 110.181746) (xy 153.858114 110.11114) (xy 154.014412 109.92814)
				(xy 154.150154 109.706628) (xy 154.249573 109.46661) (xy 154.310221 109.213994) (xy 154.330604 108.955)
				(xy 154.310221 108.696006) (xy 154.249573 108.44339) (xy 154.150154 108.203372) (xy 154.150151 108.203367)
				(xy 154.150149 108.203363) (xy 154.014417 107.981868) (xy 154.014414 107.981863) (xy 154.014413 107.981862)
				(xy 154.014412 107.98186) (xy 153.845689 107.784311) (xy 153.64814 107.615588) (xy 153.648138 107.615586)
				(xy 153.648136 107.615585) (xy 153.648131 107.615582) (xy 153.426636 107.47985) (xy 153.426624 107.479844)
				(xy 153.186609 107.380426) (xy 152.933991 107.319778) (xy 152.933993 107.319778) (xy 152.695382 107.301)
				(xy 152.675 107.299396) (xy 152.674999 107.299396) (xy 152.630538 107.302895) (xy 152.547098 107.290637)
				(xy 152.481674 107.247976) (xy 152.312945 107.079247) (xy 152.306708 107.072366) (xy 152.277232 107.036448)
				(xy 152.277229 107.036446) (xy 152.277227 107.036443) (xy 152.146895 106.929484) (xy 152.119562 106.907051)
				(xy 152.119558 106.907048) (xy 152.119557 106.907048) (xy 152.057817 106.874047) (xy 151.939664 106.810892)
				(xy 151.744477 106.751683) (xy 151.689251 106.746244) (xy 151.592348 106.7367) (xy 151.592345 106.7367)
				(xy 151.583037 106.735783) (xy 151.583033 106.735782) (xy 151.55513 106.733034) (xy 151.54148 106.73169)
				(xy 151.541478 106.73169) (xy 151.541476 106.73169) (xy 151.495255 106.736243) (xy 151.485962 106.7367)
				(xy 150.561957 106.7367) (xy 150.479736 106.717934) (xy 150.417863 106.670273) (xy 150.395689 106.644311)
				(xy 150.19814 106.475588) (xy 150.198138 106.475586) (xy 150.198136 106.475585) (xy 150.198131 106.475582)
				(xy 149.976636 106.33985) (xy 149.976624 106.339844) (xy 149.736609 106.240426) (xy 149.483991 106.179778)
				(xy 149.483993 106.179778) (xy 149.225 106.159396) (xy 148.966007 106.179778) (xy 148.71339 106.240426)
				(xy 148.473375 106.339844) (xy 148.473363 106.33985) (xy 148.251868 106.475582) (xy 148.251863 106.475585)
				(xy 148.054311 106.644311) (xy 147.885585 106.841863) (xy 147.885582 106.841868) (xy 147.74985 107.063363)
				(xy 147.749844 107.063375) (xy 147.650426 107.30339) (xy 147.589778 107.556007) (xy 147.569396 107.815)
				(xy 147.589779 108.074) (xy 147.59024 108.076909) (xy 147.590133 108.078494) (xy 147.590363 108.081416)
				(xy 147.589933 108.081449) (xy 147.584566 108.161053) (xy 147.542945 108.234403) (xy 147.47362 108.28243)
				(xy 147.390323 108.295622) (xy 147.309551 108.271366) (xy 147.247302 108.214466) (xy 147.241497 108.205564)
				(xy 147.214221 108.161053) (xy 147.104412 107.98186) (xy 146.935689 107.784311) (xy 146.872528 107.730366)
				(xy 146.822195 107.662697) (xy 146.8061 107.58627) (xy 146.8061 100.741118) (xy 146.806557 100.731825)
				(xy 146.81111 100.6856) (xy 146.8061 100.634732) (xy 146.797508 100.547499) (xy 146.791117 100.482606)
				(xy 146.731906 100.287414) (xy 146.695972 100.220186) (xy 146.641639 100.118537) (xy 146.635752 100.107523)
				(xy 146.538778 99.98936) (xy 146.506352 99.949848) (xy 146.506346 99.949843) (xy 146.506343 99.94984)
				(xy 146.47044 99.920376) (xy 146.463546 99.914127) (xy 145.551465 99.002047) (xy 145.545228 98.995166)
				(xy 145.515752 98.959248) (xy 145.442917 98.899474) (xy 145.377336 98.845653) (xy 145.358078 98.829848)
				(xy 145.358076 98.829847) (xy 145.178186 98.733693) (xy 144.982997 98.674483) (xy 144.927771 98.669044)
				(xy 144.830868 98.6595) (xy 144.830865 98.6595) (xy 144.821557 98.658583) (xy 144.821553 98.658582)
				(xy 144.79365 98.655834) (xy 144.78 98.65449) (xy 144.779998 98.65449) (xy 144.779996 98.65449)
				(xy 144.733775 98.659043) (xy 144.724482 98.6595) (xy 143.398743 98.6595) (xy 143.316522 98.640734)
				(xy 143.250586 98.588151) (xy 143.216769 98.522872) (xy 143.192869 98.440607) (xy 143.111135 98.302402)
				(xy 142.997598 98.188865) (xy 142.997595 98.188863) (xy 142.997596 98.188863) (xy 142.859395 98.107132)
				(xy 142.859394 98.107131) (xy 142.859393 98.107131) (xy 142.705204 98.062335) (xy 142.693196 98.06139)
				(xy 142.669183 98.0595) (xy 142.669181 98.0595) (xy 140.54082 98.059501) (xy 140.504802 98.062334)
				(xy 140.504799 98.062334) (xy 140.504796 98.062335) (xy 140.350607 98.107131) (xy 140.350604 98.107132)
				(xy 140.212404 98.188863) (xy 140.098863 98.302404) (xy 140.017131 98.440606) (xy 139.993231 98.52287)
				(xy 139.952271 98.59659) (xy 139.883381 98.645238) (xy 139.811256 98.6595) (xy 139.628518 98.6595)
				(xy 139.619225 98.659043) (xy 139.573002 98.65449) (xy 139.573 98.65449) (xy 139.561119 98.65566)
				(xy 139.526769 98.659043) (xy 139.526765 98.659043) (xy 139.522135 98.6595) (xy 139.522132 98.6595)
				(xy 139.424789 98.669087) (xy 139.370002 98.674483) (xy 139.174815 98.733692) (xy 139.17481 98.733695)
				(xy 139.117882 98.764124) (xy 139.036524 98.786332) (xy 139.028553 98.7865) (xy 130.322726 98.7865)
				(xy 130.240505 98.767734) (xy 130.188732 98.730999) (xy 130.162598 98.704865) (xy 130.162595 98.704863)
				(xy 130.162596 98.704863) (xy 130.024395 98.623132) (xy 130.024394 98.623131) (xy 130.024393 98.623131)
				(xy 129.870204 98.578335) (xy 129.858196 98.57739) (xy 129.834183 98.5755) (xy 129.834181 98.5755)
				(xy 126.70582 98.575501) (xy 126.669802 98.578334) (xy 126.669799 98.578334) (xy 126.669796 98.578335)
				(xy 126.515607 98.623131) (xy 126.515604 98.623132) (xy 126.377404 98.704863) (xy 126.263863 98.818404)
				(xy 126.182132 98.956604) (xy 126.182131 98.956606) (xy 126.182131 98.956607) (xy 126.152336 99.059163)
				(xy 126.137335 99.110796) (xy 126.1345 99.146817) (xy 126.134501 102.27518) (xy 126.137334 102.311197)
				(xy 126.137333 102.311197) (xy 126.137334 102.311203) (xy 126.137335 102.311204) (xy 126.170947 102.426898)
				(xy 126.182132 102.465395) (xy 126.263816 102.603516) (xy 126.263865 102.603598) (xy 126.377402 102.717135)
				(xy 126.377404 102.717136) (xy 126.377403 102.717136) (xy 126.466413 102.769776) (xy 126.515607 102.798869)
				(xy 126.669796 102.843665) (xy 126.700918 102.846114) (xy 126.705817 102.8465) (xy 126.705818 102.846499)
				(xy 126.705819 102.8465) (xy 129.83418 102.846499) (xy 129.870204 102.843665) (xy 130.024393 102.798869)
				(xy 130.162598 102.717135) (xy 130.276135 102.603598) (xy 130.357869 102.465393) (xy 130.402665 102.311204)
				(xy 130.4055 102.275181) (xy 130.4055 101.047) (xy 130.424266 100.964779) (xy 130.476849 100.898843)
				(xy 130.552832 100.862251) (xy 130.595 100.8575) (xy 139.390482 100.8575) (xy 139.399775 100.857957)
				(xy 139.445998 100.86251) (xy 139.446 100.86251) (xy 139.446001 100.86251) (xy 139.452146 100.861904)
				(xy 139.496865 100.8575) (xy 139.496868 100.8575) (xy 139.648993 100.842517) (xy 139.775291 100.804204)
				(xy 139.859417 100.798296) (xy 139.937778 100.829473) (xy 139.994852 100.891562) (xy 140.012272 100.93267)
				(xy 140.017131 100.949393) (xy 140.017132 100.949395) (xy 140.039665 100.987497) (xy 140.098865 101.087598)
				(xy 140.212402 101.201135) (xy 140.212404 101.201136) (xy 140.212403 101.201136) (xy 140.301413 101.253776)
				(xy 140.350607 101.282869) (xy 140.504796 101.327665) (xy 140.535918 101.330114) (xy 140.540817 101.3305)
				(xy 140.540818 101.330499) (xy 140.540819 101.3305) (xy 142.66918 101.330499) (xy 142.705204 101.327665)
				(xy 142.859393 101.282869) (xy 142.997598 101.201135) (xy 143.111135 101.087598) (xy 143.192869 100.949393)
				(xy 143.216769 100.86713) (xy 143.257729 100.79341) (xy 143.326619 100.744762) (xy 143.398744 100.7305)
				(xy 144.272588 100.7305) (xy 144.354809 100.749266) (xy 144.406585 100.786003) (xy 144.679597 101.059015)
				(xy 144.724466 101.130424) (xy 144.7351 101.193012) (xy 144.7351 107.576704) (xy 144.716334 107.658925)
				(xy 144.668672 107.720799) (xy 144.594309 107.784312) (xy 144.594308 107.784312) (xy 144.42559 107.981857)
				(xy 144.425582 107.981868) (xy 144.28985 108.203363) (xy 144.289844 108.203375) (xy 144.190426 108.44339)
				(xy 144.129778 108.696007) (xy 144.109396 108.955) (xy 144.129778 109.213992) (xy 144.190426 109.466609)
				(xy 144.289844 109.706624) (xy 144.28985 109.706636) (xy 144.425582 109.928131) (xy 144.425588 109.92814)
				(xy 144.42559 109.928142) (xy 144.581882 110.111137) (xy 144.62101 110.185846) (xy 144.623848 110.270133)
				(xy 144.589834 110.347305) (xy 144.525705 110.402077) (xy 144.444162 110.4236) (xy 144.365266 110.409282)
				(xy 144.3169 110.389248) (xy 144.12661 110.310427) (xy 144.059924 110.294417) (xy 143.873991 110.249778)
				(xy 143.873993 110.249778) (xy 143.615 110.229396) (xy 143.356007 110.249778) (xy 143.10339 110.310426)
				(xy 142.863375 110.409844) (xy 142.863363 110.40985) (xy 142.641868 110.545582) (xy 142.641863 110.545585)
				(xy 142.444305 110.714316) (xy 142.427265 110.734269) (xy 142.359597 110.784603) (xy 142.283167 110.8007)
				(xy 142.188612 110.8007) (xy 142.106391 110.781934) (xy 142.054615 110.745197) (xy 138.566465 107.257047)
				(xy 138.560226 107.250163) (xy 138.530752 107.214248) (xy 138.457917 107.154474) (xy 138.457916 107.154473)
				(xy 138.373078 107.084848) (xy 138.373076 107.084847) (xy 138.193186 106.988693) (xy 137.997997 106.929483)
				(xy 137.942771 106.924044) (xy 137.845868 106.9145) (xy 137.845865 106.9145) (xy 137.836557 106.913583)
				(xy 137.836553 106.913582) (xy 137.80865 106.910834) (xy 137.795 106.90949) (xy 137.794998 106.90949)
				(xy 137.794996 106.90949) (xy 137.748775 106.914043) (xy 137.739482 106.9145) (xy 137.342783 106.9145)
				(xy 137.260562 106.895734) (xy 137.22639 106.874542) (xy 137.144612 106.810892) (xy 137.058835 106.744129)
				(xy 136.980683 106.701835) (xy 136.849613 106.630903) (xy 136.624612 106.553659) (xy 136.624606 106.553657)
				(xy 136.624602 106.553656) (xy 136.624598 106.553655) (xy 136.624593 106.553654) (xy 136.389954 106.5145)
				(xy 136.389949 106.5145) (xy 136.152051 106.5145) (xy 136.152046 106.5145) (xy 135.917406 106.553654)
				(xy 135.917387 106.553659) (xy 135.692386 106.630903) (xy 135.483166 106.744128) (xy 135.316246 106.874047)
				(xy 135.239836 106.909739) (xy 135.218424 106.913092) (xy 135.213435 106.913584) (xy 135.208771 106.914043)
				(xy 135.208767 106.914043) (xy 135.204135 106.9145) (xy 135.204132 106.9145) (xy 135.106789 106.924087)
				(xy 135.052002 106.929483) (xy 134.856813 106.988693) (xy 134.676928 107.084844) (xy 134.676923 107.084847)
				(xy 134.592082 107.154474) (xy 134.51925 107.214246) (xy 134.519239 107.214257) (xy 134.489771 107.250163)
				(xy 134.483524 107.257055) (xy 133.292055 108.448524) (xy 133.285163 108.454771) (xy 133.249254 108.484241)
				(xy 133.249241 108.484254) (xy 133.216822 108.523759) (xy 133.11985 108.641919) (xy 133.119846 108.641925)
				(xy 133.117539 108.646243) (xy 133.023694 108.821812) (xy 133.02369 108.821822) (xy 132.964483 109.017002)
				(xy 132.95095 109.154413) (xy 132.94449 109.219996) (xy 132.94449 109.220001) (xy 132.949043 109.266225)
				(xy 132.9495 109.275518) (xy 132.9495 113.369988) (xy 132.930734 113.452209) (xy 132.878151 113.518145)
				(xy 132.802168 113.554737) (xy 132.717832 113.554737) (xy 132.641849 113.518145) (xy 132.626003 113.503985)
				(xy 131.205065 112.083047) (xy 131.198828 112.076166) (xy 131.169352 112.040248) (xy 131.096517 111.980474)
				(xy 131.096516 111.980473) (xy 131.011678 111.910848) (xy 131.011676 111.910847) (xy 130.831786 111.814693)
				(xy 130.636593 111.755482) (xy 130.576424 111.749556) (xy 130.496438 111.722821) (xy 130.435974 111.664028)
				(xy 130.407006 111.584824) (xy 130.405499 111.560969) (xy 130.405499 111.21182) (xy 130.402665 111.175802)
				(xy 130.402666 111.175802) (xy 130.402665 111.175799) (xy 130.402665 111.175796) (xy 130.357869 111.021607)
				(xy 130.276135 110.883402) (xy 130.162598 110.769865) (xy 130.162595 110.769863) (xy 130.162596 110.769863)
				(xy 130.024395 110.688132) (xy 130.024394 110.688131) (xy 130.024393 110.688131) (xy 129.870204 110.643335)
				(xy 129.858196 110.64239) (xy 129.834183 110.6405) (xy 129.834181 110.6405) (xy 126.70582 110.640501)
				(xy 126.669802 110.643334) (xy 126.669799 110.643334) (xy 126.669796 110.643335) (xy 126.515607 110.688131)
				(xy 126.515604 110.688132) (xy 126.377404 110.769863) (xy 126.263863 110.883404) (xy 126.182132 111.021604)
				(xy 126.182131 111.021606) (xy 126.182131 111.021607) (xy 126.172311 111.055409) (xy 126.137335 111.175796)
				(xy 126.1345 111.211817) (xy 122.555 111.211817) (xy 122.555 105.711001) (xy 126.511082 105.711001)
				(xy 126.530728 105.973152) (xy 126.530728 105.973157) (xy 126.589226 106.22945) (xy 126.685269 106.474164)
				(xy 126.816715 106.701835) (xy 126.864908 106.762267) (xy 127.592722 106.034453) (xy 127.616059 106.088553)
				(xy 127.720756 106.229185) (xy 127.855062 106.341882) (xy 127.946844 106.387976) (xy 127.218113 107.116707)
				(xy 127.39054 107.234267) (xy 127.390545 107.23427) (xy 127.627395 107.34833) (xy 127.87861 107.425819)
				(xy 128.138549 107.464999) (xy 128.13856 107.465) (xy 128.40144 107.465) (xy 128.40145 107.464999)
				(xy 128.661389 107.425819) (xy 128.912604 107.34833) (xy 129.149454 107.23427) (xy 129.149459 107.234267)
				(xy 129.321885 107.116708) (xy 128.595747 106.39057) (xy 128.608891 106.385787) (xy 128.755373 106.289445)
				(xy 128.875688 106.161918) (xy 128.948545 106.035723) (xy 129.675089 106.762266) (xy 129.723284 106.701835)
				(xy 129.85473 106.474164) (xy 129.950773 106.22945) (xy 130.009271 105.973157) (xy 130.009271 105.973152)
				(xy 130.028918 105.711001) (xy 130.028918 105.710998) (xy 130.009271 105.448847) (xy 130.009271 105.448842)
				(xy 129.950773 105.192549) (xy 129.85473 104.947835) (xy 129.723285 104.720166) (xy 129.723284 104.720164)
				(xy 129.67509 104.659731) (xy 128.947276 105.387544) (xy 128.923941 105.333447) (xy 128.819244 105.192815)
				(xy 128.684938 105.080118) (xy 128.593154 105.034022) (xy 129.321886 104.30529) (xy 129.149464 104.187735)
				(xy 129.14945 104.187727) (xy 128.91261 104.07367) (xy 128.661389 103.99618) (xy 128.40145 103.957)
				(xy 128.138549 103.957) (xy 127.87861 103.99618) (xy 127.627389 104.07367) (xy 127.390544 104.18773)
				(xy 127.390537 104.187734) (xy 127.218112 104.30529) (xy 127.944252 105.031429) (xy 127.931109 105.036213)
				(xy 127.784627 105.132555) (xy 127.664312 105.260082) (xy 127.591453 105.386276) (xy 126.864908 104.659731)
				(xy 126.816714 104.720165) (xy 126.816713 104.720167) (xy 126.685269 104.947835) (xy 126.589226 105.192549)
				(xy 126.530728 105.448842) (xy 126.530728 105.448847) (xy 126.511082 105.710998) (xy 126.511082 105.711001)
				(xy 122.555 105.711001) (xy 122.555 96.746294) (xy 122.573766 96.664073) (xy 122.626349 96.598137)
				(xy 122.702332 96.561545) (xy 122.786668 96.561545) (xy 122.862651 96.598137) (xy 122.867184 96.601868)
				(xy 122.86763 96.602247) (xy 122.867638 96.602254) (xy 123.087941 96.769724) (xy 123.16421 96.827702)
				(xy 123.164212 96.827703) (xy 123.483413 97.019761) (xy 123.483417 97.019763) (xy 123.483419 97.019764)
				(xy 123.821523 97.176187) (xy 124.174556 97.295138) (xy 124.538382 97.375222) (xy 124.908733 97.4155)
				(xy 124.908736 97.4155) (xy 125.281264 97.4155) (xy 125.281267 97.4155) (xy 125.651618 97.375222)
				(xy 126.015444 97.295138) (xy 126.368477 97.176187) (xy 126.706581 97.019764) (xy 127.02579 96.827702)
				(xy 127.322362 96.602254) (xy 127.592821 96.346062) (xy 127.771937 96.13519) (xy 147.564466 96.13519)
				(xy 147.611514 96.290288) (xy 147.611519 96.2903) (xy 147.709392 96.473404) (xy 147.709395 96.47341)
				(xy 147.8411 96.633894) (xy 147.841105 96.633899) (xy 148.001589 96.765604) (xy 148.001595 96.765607)
				(xy 148.184699 96.86348) (xy 148.184711 96.863485) (xy 148.339809 96.910533) (xy 148.33981 96.910532)
				(xy 148.33981 96.200496) (xy 148.351955 96.212641) (xy 148.464852 96.270165) (xy 148.558519 96.285)
				(xy 148.621481 96.285) (xy 148.715148 96.270165) (xy 148.828045 96.212641) (xy 148.84019 96.200496)
				(xy 148.84019 96.910533) (xy 148.995288 96.863485) (xy 148.9953 96.86348) (xy 149.178404 96.765607)
				(xy 149.17841 96.765604) (xy 149.338894 96.633899) (xy 149.338899 96.633894) (xy 149.470604 96.47341)
				(xy 149.470607 96.473404) (xy 149.56848 96.2903) (xy 149.568485 96.290288) (xy 149.615533 96.13519)
				(xy 148.905496 96.13519) (xy 148.917641 96.123045) (xy 148.975165 96.010148) (xy 148.994986 95.885)
				(xy 148.975165 95.759852) (xy 148.917641 95.646955) (xy 148.905496 95.63481) (xy 149.615533 95.63481)
				(xy 149.615533 95.634809) (xy 149.568485 95.479711) (xy 149.56848 95.479699) (xy 149.470607 95.296595)
				(xy 149.470604 95.296589) (xy 149.338899 95.136105) (xy 149.338894 95.1361) (xy 149.17841 95.004395)
				(xy 149.178404 95.004392) (xy 148.995302 94.90652) (xy 148.84019 94.859466) (xy 148.84019 95.569504)
				(xy 148.828045 95.557359) (xy 148.715148 95.499835) (xy 148.621481 95.485) (xy 148.558519 95.485)
				(xy 148.464852 95.499835) (xy 148.351955 95.557359) (xy 148.33981 95.569504) (xy 148.33981 94.859466)
				(xy 148.184697 94.90652) (xy 148.001595 95.004392) (xy 148.001589 95.004395) (xy 147.841105 95.1361)
				(xy 147.8411 95.136105) (xy 147.709395 95.296589) (xy 147.709392 95.296595) (xy 147.611519 95.479699)
				(xy 147.611514 95.479711) (xy 147.564466 95.634809) (xy 147.564467 95.63481) (xy 148.274504 95.63481)
				(xy 148.262359 95.646955) (xy 148.204835 95.759852) (xy 148.185014 95.885) (xy 148.204835 96.010148)
				(xy 148.262359 96.123045) (xy 148.274504 96.13519) (xy 147.564466 96.13519) (xy 127.771937 96.13519)
				(xy 127.833995 96.06213) (xy 128.043057 95.753787) (xy 128.217555 95.424648) (xy 128.355444 95.078572)
				(xy 128.455108 94.719616) (xy 128.459144 94.695) (xy 140.34621 94.695) (xy 140.365333 94.913583)
				(xy 140.365334 94.913588) (xy 140.422122 95.125527) (xy 140.422124 95.125535) (xy 140.514852 95.324388)
				(xy 140.514855 95.324393) (xy 140.558891 95.387284) (xy 141.121861 94.824314) (xy 141.145507 94.904844)
				(xy 141.223239 95.025798) (xy 141.3319 95.119952) (xy 141.462685 95.17968) (xy 141.4727 95.18112)
				(xy 140.912714 95.741107) (xy 140.975599 95.78514) (xy 140.975599 95.785141) (xy 141.174469 95.877876)
				(xy 141.174472 95.877877) (xy 141.386411 95.934665) (xy 141.386416 95.934666) (xy 141.605 95.953789)
				(xy 141.823583 95.934666) (xy 141.823588 95.934665) (xy 142.035527 95.877877) (xy 142.03553 95.877876)
				(xy 142.234394 95.785143) (xy 142.234401 95.78514) (xy 142.297284 95.741107) (xy 141.737297 95.18112)
				(xy 141.747315 95.17968) (xy 141.8781 95.119952) (xy 141.986761 95.025798) (xy 142.064493 94.904844)
				(xy 142.088138 94.824315) (xy 142.651107 95.387284) (xy 142.69514 95.324401) (xy 142.695143 95.324394)
				(xy 142.787876 95.12553) (xy 142.787877 95.125527) (xy 142.844665 94.913588) (xy 142.844666 94.913583)
				(xy 142.863789 94.695) (xy 142.844666 94.476416) (xy 142.844665 94.476411) (xy 142.787877 94.264472)
				(xy 142.787876 94.264469) (xy 142.69514 94.065599) (xy 142.651107 94.002714) (xy 142.088137 94.565683)
				(xy 142.064493 94.485156) (xy 141.986761 94.364202) (xy 141.8781 94.270048) (xy 141.747315 94.21032)
				(xy 141.737297 94.208879) (xy 142.297284 93.648891) (xy 142.234393 93.604855) (xy 142.234388 93.604852)
				(xy 142.035535 93.512124) (xy 142.035527 93.512122) (xy 141.823588 93.455334) (xy 141.823583 93.455333)
				(xy 141.605 93.43621) (xy 141.386416 93.455333) (xy 141.386411 93.455334) (xy 141.174472 93.512122)
				(xy 141.174464 93.512124) (xy 140.975613 93.604851) (xy 140.975606 93.604855) (xy 140.912714 93.648891)
				(xy 141.472702 94.208879) (xy 141.462685 94.21032) (xy 141.3319 94.270048) (xy 141.223239 94.364202)
				(xy 141.145507 94.485156) (xy 141.121861 94.565684) (xy 140.558891 94.002714) (xy 140.514855 94.065606)
				(xy 140.514851 94.065613) (xy 140.422124 94.264464) (xy 140.422122 94.264472) (xy 140.365334 94.476411)
				(xy 140.365333 94.476416) (xy 140.34621 94.695) (xy 128.459144 94.695) (xy 128.515377 94.351988)
				(xy 128.535546 93.98) (xy 128.515377 93.608012) (xy 128.455108 93.240384) (xy 128.355444 92.881428)
				(xy 128.217555 92.535352) (xy 128.043057 92.206213) (xy 127.833995 91.89787) (xy 127.698723 91.738615)
				(xy 127.6598 91.663804) (xy 127.65719 91.579509) (xy 127.691412 91.502429) (xy 127.75569 91.447832)
				(xy 127.83729 91.42653) (xy 127.842536 91.426442) (xy 166.88139 91.326342)
			)
		)
	)
	(embedded_fonts no)
)
