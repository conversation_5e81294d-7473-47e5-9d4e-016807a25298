///////////////////////////////////////////////////////////////////////////
// C++ code generated with wxFormBuilder (version 4.2.1-0-g80c4cb6)
// http://www.wxformbuilder.org/
//
// PLEASE DO *NOT* EDIT THIS FILE!
///////////////////////////////////////////////////////////////////////////

#pragma once

#include <wx/artprov.h>
#include <wx/xrc/xmlres.h>
#include <wx/intl.h>
class WX_PANEL;

#include "widgets/resettable_panel.h"
#include <wx/string.h>
#include <wx/stattext.h>
#include <wx/gdicmn.h>
#include <wx/font.h>
#include <wx/colour.h>
#include <wx/settings.h>
#include <wx/choice.h>
#include <wx/checkbox.h>
#include <wx/button.h>
#include <wx/bitmap.h>
#include <wx/image.h>
#include <wx/icon.h>
#include <wx/sizer.h>
#include <wx/scrolwin.h>
#include <wx/panel.h>

///////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
/// Class PANEL_COLOR_SETTINGS_BASE
///////////////////////////////////////////////////////////////////////////////
class PANEL_COLOR_SETTINGS_BASE : public RESETTABLE_PANEL
{
	private:

	protected:
		wxBoxSizer* m_mainSizer;
		wxStaticText* m_staticText9;
		wxChoice* m_cbTheme;
		wxButton* m_btnOpenFolder;
		WX_PANEL* m_panel1;
		wxBoxSizer* m_colorsMainSizer;
		wxScrolledWindow* m_colorsListWindow;
		wxFlexGridSizer* m_colorsGridSizer;
		wxBoxSizer* m_previewPanelSizer;

		// Virtual event handlers, override them in your derived class
		virtual void OnSize( wxSizeEvent& event ) { event.Skip(); }
		virtual void OnThemeChanged( wxCommandEvent& event ) { event.Skip(); }
		virtual void OnLeftDownTheme( wxMouseEvent& event ) { event.Skip(); }
		virtual void OnOverrideItemColorsClicked( wxCommandEvent& event ) { event.Skip(); }
		virtual void OnBtnOpenThemeFolderClicked( wxCommandEvent& event ) { event.Skip(); }


	public:
		wxCheckBox* m_optOverrideColors;

		PANEL_COLOR_SETTINGS_BASE( wxWindow* parent, wxWindowID id = wxID_ANY, const wxPoint& pos = wxDefaultPosition, const wxSize& size = wxSize( -1,-1 ), long style = wxTAB_TRAVERSAL, const wxString& name = wxEmptyString );

		~PANEL_COLOR_SETTINGS_BASE();

};

