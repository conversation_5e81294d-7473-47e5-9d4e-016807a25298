(footprint "DSUB-9_Female_Horizontal_P2.77x2.84mm_EdgePinOffset7.70mm_Housed_MountingHolesOffset9.12mm"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "9-pin D-Sub connector, horizontal/angled (90 deg), THT-mount, female, pitch 2.77x2.84mm, pin-PCB-offset 7.7mm, distance of mounting holes 25mm, distance of mounting holes to PCB edge 9.12mm, see https://disti-assets.s3.amazonaws.com/tonar/files/datasheets/16730.pdf")
	(tags "9-pin D-Sub connector horizontal angled 90deg THT female pitch 2.77x2.84mm pin-PCB-offset 7.7mm mounting-holes-distance 25mm mounting-hole-offset 9.12mm")
	(property "Reference" "REF**"
		(at -22.479 2.159 270)
		(layer "F.SilkS")
		(uuid "6f35b8cf-813b-4e1c-a7ff-186a34d641d0")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "DB9-FEMAL"
		(at -5.54 18.61 180)
		(layer "F.Fab")
		(uuid "866810d4-aaf3-43bc-b313-72ad2be9b827")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "9da9badf-38be-4aca-a3a0-8e28d68c036f")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "7dd9eb86-f0df-497a-afda-e68e04c4048e")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -21.025 -1.86)
		(end 9.945 -1.86)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "7860ac2a-f96a-42ee-9278-fa321297f2a7")
	)
	(fp_line
		(start -21.025 10.48)
		(end -21.025 -1.86)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "d606bef8-097c-43dd-88f2-350dba548a21")
	)
	(fp_line
		(start -0.25 -2.754338)
		(end 0.25 -2.754338)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f3db5fe9-92c7-42e8-a1f7-bf3021e79cda")
	)
	(fp_line
		(start 0 -2.321325)
		(end -0.25 -2.754338)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "d12efbe5-3204-419c-95f3-553a6fc4b969")
	)
	(fp_line
		(start 0.25 -2.754338)
		(end 0 -2.321325)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "7e3a2a74-4877-4b1e-a5fa-aa5cda947023")
	)
	(fp_line
		(start 9.945 -1.86)
		(end 9.945 10.48)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "045d63f8-825c-43ec-bf5b-98e2dc1286e1")
	)
	(fp_line
		(start -21.47 -2.31)
		(end -21.47 17.61)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "3b5d7fb8-aecc-46b2-b044-bd5ed26a3509")
	)
	(fp_line
		(start -21.47 17.61)
		(end 10.39 17.61)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "e5068d79-eb84-42b1-81de-e4730e12808f")
	)
	(fp_line
		(start 10.39 -2.31)
		(end -21.47 -2.31)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "e0f1bbdf-5eb2-4e94-9474-0bb742bbc790")
	)
	(fp_line
		(start 10.39 17.61)
		(end 10.39 -2.31)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "f9455d39-55c3-449d-97a2-550e26362232")
	)
	(fp_line
		(start -20.965 -1.8)
		(end -20.965 10.54)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "34e2eab8-1659-4a08-a906-0baf3e1215e7")
	)
	(fp_line
		(start -20.965 10.54)
		(end -20.965 10.94)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "11c29ddb-e713-4ebd-a8e6-cf5f00ef7e4e")
	)
	(fp_line
		(start -20.965 10.54)
		(end 9.885 10.54)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "b7d74848-45f6-48a4-b00f-16ae191669b8")
	)
	(fp_line
		(start -20.965 10.94)
		(end 9.885 10.94)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "cdfc7db7-eced-4283-b236-3be99d6c396e")
	)
	(fp_line
		(start -20.54 10.94)
		(end -20.54 15.94)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "649a37c1-3e8e-4ac6-abcb-244833d8c893")
	)
	(fp_line
		(start -20.54 15.94)
		(end -15.54 15.94)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "48ac5103-875d-48d3-b144-0b466cf70e48")
	)
	(fp_line
		(start -19.64 10.54)
		(end -19.64 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "a3b25399-0eae-443f-8106-647f0390033c")
	)
	(fp_line
		(start -16.44 10.54)
		(end -16.44 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "52e19381-1d5e-4e3c-854f-f1403c99c4e6")
	)
	(fp_line
		(start -15.54 10.94)
		(end -20.54 10.94)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "6aeba948-8715-46ea-beac-1d120e02f2f9")
	)
	(fp_line
		(start -15.54 15.94)
		(end -15.54 10.94)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "b9c9e919-5ff1-497b-96f0-bdd89b9c365a")
	)
	(fp_line
		(start -13.69 10.94)
		(end -13.69 17.11)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "3e7ebc27-98a9-4b50-af5e-d674c3ca4cdb")
	)
	(fp_line
		(start -13.69 17.11)
		(end 2.61 17.11)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "47a23315-1c7c-4c06-950f-230d8e53cda6")
	)
	(fp_line
		(start 2.61 10.94)
		(end -13.69 10.94)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "b0095465-edfa-481f-8843-d34d867108a3")
	)
	(fp_line
		(start 2.61 17.11)
		(end 2.61 10.94)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "1d69b992-dcd8-46ed-a51a-f4d18b8cf18d")
	)
	(fp_line
		(start 4.46 10.94)
		(end 4.46 15.94)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "d4eb7333-992e-4695-b95a-79912b3fb37a")
	)
	(fp_line
		(start 4.46 15.94)
		(end 9.46 15.94)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "dfeb62de-8a03-4555-a2f4-69d821c17953")
	)
	(fp_line
		(start 5.36 10.54)
		(end 5.36 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "f8c6f659-dd74-48af-b103-c32a3d7b2ae2")
	)
	(fp_line
		(start 8.56 10.54)
		(end 8.56 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "fdb971f5-1fbd-4967-b7b3-0ff124f66f09")
	)
	(fp_line
		(start 9.46 10.94)
		(end 4.46 10.94)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "670b87ce-9fd7-4f5d-976d-ebecd2cb7ed8")
	)
	(fp_line
		(start 9.46 15.94)
		(end 9.46 10.94)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "7893d3ce-4f89-48e6-9717-b34879628f8c")
	)
	(fp_line
		(start 9.885 -1.8)
		(end -20.965 -1.8)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "b1a3d700-d3a7-46cf-80db-6a73e54f4c28")
	)
	(fp_line
		(start 9.885 10.54)
		(end -20.965 10.54)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "be21ace4-**************-0ee10faacc31")
	)
	(fp_line
		(start 9.885 10.54)
		(end 9.885 -1.8)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "7c143021-d3ab-43fb-895b-0138afa658a3")
	)
	(fp_line
		(start 9.885 10.94)
		(end 9.885 10.54)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "db88f18e-a07f-4b00-8b56-ee88cd8014fe")
	)
	(fp_arc
		(start -19.64 1.42)
		(mid -18.04 -0.18)
		(end -16.44 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "5e2d850d-56a1-4bf2-a812-b7488b26faed")
	)
	(fp_arc
		(start 5.36 1.42)
		(mid 6.96 -0.18)
		(end 8.56 1.42)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "8cec417e-2fc0-4050-a41d-9d5ff3456597")
	)
	(fp_text user "${REFERENCE}"
		(at -5.54 14.025 180)
		(layer "F.Fab")
		(uuid "b5bf1b10-1d64-42e9-94fa-4754dac24bc5")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "0" thru_hole circle
		(at -18.04 1.42)
		(size 4 4)
		(drill 3.2)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "8b76f9be-9887-4284-8f5f-6822901faddc")
	)
	(pad "0" thru_hole circle
		(at 6.96 1.42)
		(size 4 4)
		(drill 3.2)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "5cc6bc9e-9563-4432-ac60-cfed67639fe4")
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "1a48daba-513b-48de-81a8-b9aca94555af")
	)
	(pad "2" thru_hole circle
		(at -2.77 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "1d247939-7960-4535-a53b-6c9e6e1b886e")
	)
	(pad "3" thru_hole circle
		(at -5.54 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "d7c37da6-763e-4de5-b3e5-108598830c2b")
	)
	(pad "4" thru_hole circle
		(at -8.31 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "7a6c7a7c-f67d-4af9-a461-13866f02d0eb")
	)
	(pad "5" thru_hole circle
		(at -11.08 0)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "a3f0c226-0a9b-435b-b585-86ccca2a3617")
	)
	(pad "6" thru_hole circle
		(at -1.385 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "7155ce05-9f4e-4751-9834-261a1190deaf")
	)
	(pad "7" thru_hole circle
		(at -4.155 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "d879037c-3f93-41c4-a80c-93541fc91d1b")
	)
	(pad "8" thru_hole circle
		(at -6.925 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "ae607bf2-b454-476a-971a-677bda3c0350")
	)
	(pad "9" thru_hole circle
		(at -9.695 2.84)
		(size 1.6 1.6)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(teardrops
			(best_length_ratio 0.5)
			(max_length 1)
			(best_width_ratio 1)
			(max_width 2)
			(curved_edges no)
			(filter_ratio 0.9)
			(enabled yes)
			(allow_two_segments yes)
			(prefer_zone_connections yes)
		)
		(uuid "221def29-2e82-434a-8410-456bcfd9b6c7")
	)
	(embedded_fonts no)
	(model "${KICAD8_3DMODEL_DIR}/Connector_Dsub.3dshapes/DSUB-9_Female_Horizontal_P2.77x2.84mm_EdgePinOffset7.70mm_Housed_MountingHolesOffset9.12mm.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
