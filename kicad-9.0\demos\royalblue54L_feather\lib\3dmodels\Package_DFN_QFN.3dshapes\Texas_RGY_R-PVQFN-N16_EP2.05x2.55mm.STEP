ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */
/* OPTION: using custom renumber hook */

FILE_DESCRIPTION(
/* description */ ('STEP AP242',
'CAx-IF Rec.Pracs.---Representation and Presentation of Product Manufa
cturing Information (PMI)---4.0---2014-10-13',
'CAx-IF Rec.Pracs.---3D Tessellated Geometry---0.4---2014-09-14','2;1'),

/* implementation_level */ '2;1');

FILE_NAME(
/* name */ '673c10afa43bb33766d6df44',
/* time_stamp */ '2024-11-19T04:14:39Z',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v20',
/* originating_system */ 'ONSHAPE BY PTC INC, 1.189',
/* authorisation */ '  ');

FILE_SCHEMA (('AP242_MANAGED_MODEL_BASED_3D_ENGINEERING_MIM_LF { 1 0 10303 442 1 1 4 }'));
ENDSEC;

DATA;
#10=SHAPE_REPRESENTATION_RELATIONSHIP('','',#1714,#11);
#11=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#1712),#2766);
#12=CYLINDRICAL_SURFACE('',#1734,0.00012);
#13=CYLINDRICAL_SURFACE('',#1740,0.00012);
#14=CYLINDRICAL_SURFACE('',#1746,0.00012);
#15=CYLINDRICAL_SURFACE('',#1752,0.00012);
#16=CYLINDRICAL_SURFACE('',#1758,0.00012);
#17=CYLINDRICAL_SURFACE('',#1764,0.00012);
#18=CYLINDRICAL_SURFACE('',#1770,0.00012);
#19=CYLINDRICAL_SURFACE('',#1776,0.00012);
#20=CYLINDRICAL_SURFACE('',#1782,0.00012);
#21=CYLINDRICAL_SURFACE('',#1788,0.00012);
#22=CYLINDRICAL_SURFACE('',#1800,0.00012);
#23=CYLINDRICAL_SURFACE('',#1808,0.00012);
#24=CYLINDRICAL_SURFACE('',#1812,0.00012);
#25=CYLINDRICAL_SURFACE('',#1819,0.00012);
#26=CYLINDRICAL_SURFACE('',#1824,0.00012);
#27=CYLINDRICAL_SURFACE('',#1831,0.00012);
#28=CYLINDRICAL_SURFACE('',#1840,0.000125);
#29=CIRCLE('',#1717,0.00012);
#30=CIRCLE('',#1718,0.00012);
#31=CIRCLE('',#1719,0.00012);
#32=CIRCLE('',#1720,0.00012);
#33=CIRCLE('',#1721,0.00012);
#34=CIRCLE('',#1722,0.00012);
#35=CIRCLE('',#1723,0.00012);
#36=CIRCLE('',#1724,0.00012);
#37=CIRCLE('',#1725,0.00012);
#38=CIRCLE('',#1726,0.00012);
#39=CIRCLE('',#1727,0.00012);
#40=CIRCLE('',#1728,0.00012);
#41=CIRCLE('',#1729,0.00012);
#42=CIRCLE('',#1730,0.00012);
#43=CIRCLE('',#1731,0.00012);
#44=CIRCLE('',#1732,0.00012);
#45=CIRCLE('',#1735,0.00012);
#46=CIRCLE('',#1741,0.00012);
#47=CIRCLE('',#1747,0.00012);
#48=CIRCLE('',#1753,0.00012);
#49=CIRCLE('',#1759,0.00012);
#50=CIRCLE('',#1765,0.00012);
#51=CIRCLE('',#1771,0.00012);
#52=CIRCLE('',#1777,0.00012);
#53=CIRCLE('',#1783,0.00012);
#54=CIRCLE('',#1789,0.00012);
#55=CIRCLE('',#1798,0.000125);
#56=CIRCLE('',#1801,0.00012);
#57=CIRCLE('',#1809,0.00012);
#58=CIRCLE('',#1813,0.00012);
#59=CIRCLE('',#1820,0.00012);
#60=CIRCLE('',#1825,0.00012);
#61=CIRCLE('',#1832,0.00012);
#62=CIRCLE('',#1841,0.000125);
#63=ORIENTED_EDGE('',*,*,#523,.F.);
#64=ORIENTED_EDGE('',*,*,#524,.T.);
#65=ORIENTED_EDGE('',*,*,#525,.T.);
#66=ORIENTED_EDGE('',*,*,#526,.F.);
#67=ORIENTED_EDGE('',*,*,#527,.F.);
#68=ORIENTED_EDGE('',*,*,#528,.T.);
#69=ORIENTED_EDGE('',*,*,#529,.T.);
#70=ORIENTED_EDGE('',*,*,#530,.F.);
#71=ORIENTED_EDGE('',*,*,#531,.F.);
#72=ORIENTED_EDGE('',*,*,#532,.T.);
#73=ORIENTED_EDGE('',*,*,#533,.T.);
#74=ORIENTED_EDGE('',*,*,#534,.F.);
#75=ORIENTED_EDGE('',*,*,#535,.T.);
#76=ORIENTED_EDGE('',*,*,#536,.F.);
#77=ORIENTED_EDGE('',*,*,#537,.T.);
#78=ORIENTED_EDGE('',*,*,#538,.T.);
#79=ORIENTED_EDGE('',*,*,#539,.T.);
#80=ORIENTED_EDGE('',*,*,#540,.F.);
#81=ORIENTED_EDGE('',*,*,#541,.F.);
#82=ORIENTED_EDGE('',*,*,#542,.F.);
#83=ORIENTED_EDGE('',*,*,#543,.F.);
#84=ORIENTED_EDGE('',*,*,#544,.T.);
#85=ORIENTED_EDGE('',*,*,#545,.F.);
#86=ORIENTED_EDGE('',*,*,#546,.F.);
#87=ORIENTED_EDGE('',*,*,#547,.F.);
#88=ORIENTED_EDGE('',*,*,#548,.T.);
#89=ORIENTED_EDGE('',*,*,#549,.F.);
#90=ORIENTED_EDGE('',*,*,#550,.F.);
#91=ORIENTED_EDGE('',*,*,#551,.F.);
#92=ORIENTED_EDGE('',*,*,#552,.T.);
#93=ORIENTED_EDGE('',*,*,#553,.F.);
#94=ORIENTED_EDGE('',*,*,#554,.F.);
#95=ORIENTED_EDGE('',*,*,#555,.F.);
#96=ORIENTED_EDGE('',*,*,#556,.T.);
#97=ORIENTED_EDGE('',*,*,#557,.F.);
#98=ORIENTED_EDGE('',*,*,#558,.F.);
#99=ORIENTED_EDGE('',*,*,#559,.F.);
#100=ORIENTED_EDGE('',*,*,#560,.T.);
#101=ORIENTED_EDGE('',*,*,#561,.F.);
#102=ORIENTED_EDGE('',*,*,#562,.F.);
#103=ORIENTED_EDGE('',*,*,#563,.F.);
#104=ORIENTED_EDGE('',*,*,#564,.T.);
#105=ORIENTED_EDGE('',*,*,#565,.F.);
#106=ORIENTED_EDGE('',*,*,#566,.T.);
#107=ORIENTED_EDGE('',*,*,#567,.T.);
#108=ORIENTED_EDGE('',*,*,#568,.F.);
#109=ORIENTED_EDGE('',*,*,#569,.T.);
#110=ORIENTED_EDGE('',*,*,#570,.T.);
#111=ORIENTED_EDGE('',*,*,#571,.T.);
#112=ORIENTED_EDGE('',*,*,#572,.F.);
#113=ORIENTED_EDGE('',*,*,#573,.T.);
#114=ORIENTED_EDGE('',*,*,#574,.T.);
#115=ORIENTED_EDGE('',*,*,#575,.T.);
#116=ORIENTED_EDGE('',*,*,#576,.F.);
#117=ORIENTED_EDGE('',*,*,#577,.T.);
#118=ORIENTED_EDGE('',*,*,#578,.T.);
#119=ORIENTED_EDGE('',*,*,#579,.T.);
#120=ORIENTED_EDGE('',*,*,#580,.F.);
#121=ORIENTED_EDGE('',*,*,#581,.T.);
#122=ORIENTED_EDGE('',*,*,#582,.T.);
#123=ORIENTED_EDGE('',*,*,#583,.T.);
#124=ORIENTED_EDGE('',*,*,#584,.F.);
#125=ORIENTED_EDGE('',*,*,#585,.T.);
#126=ORIENTED_EDGE('',*,*,#586,.T.);
#127=ORIENTED_EDGE('',*,*,#587,.F.);
#128=ORIENTED_EDGE('',*,*,#588,.T.);
#129=ORIENTED_EDGE('',*,*,#589,.T.);
#130=ORIENTED_EDGE('',*,*,#590,.F.);
#131=ORIENTED_EDGE('',*,*,#591,.F.);
#132=ORIENTED_EDGE('',*,*,#592,.T.);
#133=ORIENTED_EDGE('',*,*,#593,.T.);
#134=ORIENTED_EDGE('',*,*,#594,.F.);
#135=ORIENTED_EDGE('',*,*,#536,.T.);
#136=ORIENTED_EDGE('',*,*,#595,.F.);
#137=ORIENTED_EDGE('',*,*,#596,.F.);
#138=ORIENTED_EDGE('',*,*,#597,.T.);
#139=ORIENTED_EDGE('',*,*,#537,.F.);
#140=ORIENTED_EDGE('',*,*,#597,.F.);
#141=ORIENTED_EDGE('',*,*,#598,.T.);
#142=ORIENTED_EDGE('',*,*,#599,.T.);
#143=ORIENTED_EDGE('',*,*,#538,.F.);
#144=ORIENTED_EDGE('',*,*,#599,.F.);
#145=ORIENTED_EDGE('',*,*,#600,.T.);
#146=ORIENTED_EDGE('',*,*,#601,.T.);
#147=ORIENTED_EDGE('',*,*,#602,.T.);
#148=ORIENTED_EDGE('',*,*,#601,.F.);
#149=ORIENTED_EDGE('',*,*,#603,.F.);
#150=ORIENTED_EDGE('',*,*,#595,.T.);
#151=ORIENTED_EDGE('',*,*,#596,.T.);
#152=ORIENTED_EDGE('',*,*,#603,.T.);
#153=ORIENTED_EDGE('',*,*,#600,.F.);
#154=ORIENTED_EDGE('',*,*,#598,.F.);
#155=ORIENTED_EDGE('',*,*,#584,.T.);
#156=ORIENTED_EDGE('',*,*,#604,.F.);
#157=ORIENTED_EDGE('',*,*,#605,.F.);
#158=ORIENTED_EDGE('',*,*,#606,.T.);
#159=ORIENTED_EDGE('',*,*,#585,.F.);
#160=ORIENTED_EDGE('',*,*,#606,.F.);
#161=ORIENTED_EDGE('',*,*,#607,.T.);
#162=ORIENTED_EDGE('',*,*,#608,.T.);
#163=ORIENTED_EDGE('',*,*,#586,.F.);
#164=ORIENTED_EDGE('',*,*,#608,.F.);
#165=ORIENTED_EDGE('',*,*,#609,.T.);
#166=ORIENTED_EDGE('',*,*,#610,.T.);
#167=ORIENTED_EDGE('',*,*,#611,.T.);
#168=ORIENTED_EDGE('',*,*,#610,.F.);
#169=ORIENTED_EDGE('',*,*,#612,.F.);
#170=ORIENTED_EDGE('',*,*,#604,.T.);
#171=ORIENTED_EDGE('',*,*,#605,.T.);
#172=ORIENTED_EDGE('',*,*,#612,.T.);
#173=ORIENTED_EDGE('',*,*,#609,.F.);
#174=ORIENTED_EDGE('',*,*,#607,.F.);
#175=ORIENTED_EDGE('',*,*,#580,.T.);
#176=ORIENTED_EDGE('',*,*,#613,.F.);
#177=ORIENTED_EDGE('',*,*,#614,.F.);
#178=ORIENTED_EDGE('',*,*,#615,.T.);
#179=ORIENTED_EDGE('',*,*,#581,.F.);
#180=ORIENTED_EDGE('',*,*,#615,.F.);
#181=ORIENTED_EDGE('',*,*,#616,.T.);
#182=ORIENTED_EDGE('',*,*,#617,.T.);
#183=ORIENTED_EDGE('',*,*,#582,.F.);
#184=ORIENTED_EDGE('',*,*,#617,.F.);
#185=ORIENTED_EDGE('',*,*,#618,.T.);
#186=ORIENTED_EDGE('',*,*,#619,.T.);
#187=ORIENTED_EDGE('',*,*,#620,.T.);
#188=ORIENTED_EDGE('',*,*,#619,.F.);
#189=ORIENTED_EDGE('',*,*,#621,.F.);
#190=ORIENTED_EDGE('',*,*,#613,.T.);
#191=ORIENTED_EDGE('',*,*,#614,.T.);
#192=ORIENTED_EDGE('',*,*,#621,.T.);
#193=ORIENTED_EDGE('',*,*,#618,.F.);
#194=ORIENTED_EDGE('',*,*,#616,.F.);
#195=ORIENTED_EDGE('',*,*,#576,.T.);
#196=ORIENTED_EDGE('',*,*,#622,.F.);
#197=ORIENTED_EDGE('',*,*,#623,.F.);
#198=ORIENTED_EDGE('',*,*,#624,.T.);
#199=ORIENTED_EDGE('',*,*,#577,.F.);
#200=ORIENTED_EDGE('',*,*,#624,.F.);
#201=ORIENTED_EDGE('',*,*,#625,.T.);
#202=ORIENTED_EDGE('',*,*,#626,.T.);
#203=ORIENTED_EDGE('',*,*,#578,.F.);
#204=ORIENTED_EDGE('',*,*,#626,.F.);
#205=ORIENTED_EDGE('',*,*,#627,.T.);
#206=ORIENTED_EDGE('',*,*,#628,.T.);
#207=ORIENTED_EDGE('',*,*,#629,.T.);
#208=ORIENTED_EDGE('',*,*,#628,.F.);
#209=ORIENTED_EDGE('',*,*,#630,.F.);
#210=ORIENTED_EDGE('',*,*,#622,.T.);
#211=ORIENTED_EDGE('',*,*,#623,.T.);
#212=ORIENTED_EDGE('',*,*,#630,.T.);
#213=ORIENTED_EDGE('',*,*,#627,.F.);
#214=ORIENTED_EDGE('',*,*,#625,.F.);
#215=ORIENTED_EDGE('',*,*,#572,.T.);
#216=ORIENTED_EDGE('',*,*,#631,.F.);
#217=ORIENTED_EDGE('',*,*,#632,.F.);
#218=ORIENTED_EDGE('',*,*,#633,.T.);
#219=ORIENTED_EDGE('',*,*,#573,.F.);
#220=ORIENTED_EDGE('',*,*,#633,.F.);
#221=ORIENTED_EDGE('',*,*,#634,.T.);
#222=ORIENTED_EDGE('',*,*,#635,.T.);
#223=ORIENTED_EDGE('',*,*,#574,.F.);
#224=ORIENTED_EDGE('',*,*,#635,.F.);
#225=ORIENTED_EDGE('',*,*,#636,.T.);
#226=ORIENTED_EDGE('',*,*,#637,.T.);
#227=ORIENTED_EDGE('',*,*,#638,.T.);
#228=ORIENTED_EDGE('',*,*,#637,.F.);
#229=ORIENTED_EDGE('',*,*,#639,.F.);
#230=ORIENTED_EDGE('',*,*,#631,.T.);
#231=ORIENTED_EDGE('',*,*,#632,.T.);
#232=ORIENTED_EDGE('',*,*,#639,.T.);
#233=ORIENTED_EDGE('',*,*,#636,.F.);
#234=ORIENTED_EDGE('',*,*,#634,.F.);
#235=ORIENTED_EDGE('',*,*,#544,.F.);
#236=ORIENTED_EDGE('',*,*,#640,.F.);
#237=ORIENTED_EDGE('',*,*,#641,.T.);
#238=ORIENTED_EDGE('',*,*,#642,.T.);
#239=ORIENTED_EDGE('',*,*,#543,.T.);
#240=ORIENTED_EDGE('',*,*,#643,.F.);
#241=ORIENTED_EDGE('',*,*,#644,.F.);
#242=ORIENTED_EDGE('',*,*,#640,.T.);
#243=ORIENTED_EDGE('',*,*,#542,.T.);
#244=ORIENTED_EDGE('',*,*,#645,.F.);
#245=ORIENTED_EDGE('',*,*,#646,.F.);
#246=ORIENTED_EDGE('',*,*,#643,.T.);
#247=ORIENTED_EDGE('',*,*,#647,.F.);
#248=ORIENTED_EDGE('',*,*,#642,.F.);
#249=ORIENTED_EDGE('',*,*,#648,.T.);
#250=ORIENTED_EDGE('',*,*,#645,.T.);
#251=ORIENTED_EDGE('',*,*,#641,.F.);
#252=ORIENTED_EDGE('',*,*,#644,.T.);
#253=ORIENTED_EDGE('',*,*,#646,.T.);
#254=ORIENTED_EDGE('',*,*,#648,.F.);
#255=ORIENTED_EDGE('',*,*,#548,.F.);
#256=ORIENTED_EDGE('',*,*,#649,.F.);
#257=ORIENTED_EDGE('',*,*,#650,.T.);
#258=ORIENTED_EDGE('',*,*,#651,.T.);
#259=ORIENTED_EDGE('',*,*,#547,.T.);
#260=ORIENTED_EDGE('',*,*,#652,.F.);
#261=ORIENTED_EDGE('',*,*,#653,.F.);
#262=ORIENTED_EDGE('',*,*,#649,.T.);
#263=ORIENTED_EDGE('',*,*,#546,.T.);
#264=ORIENTED_EDGE('',*,*,#654,.F.);
#265=ORIENTED_EDGE('',*,*,#655,.F.);
#266=ORIENTED_EDGE('',*,*,#652,.T.);
#267=ORIENTED_EDGE('',*,*,#656,.F.);
#268=ORIENTED_EDGE('',*,*,#651,.F.);
#269=ORIENTED_EDGE('',*,*,#657,.T.);
#270=ORIENTED_EDGE('',*,*,#654,.T.);
#271=ORIENTED_EDGE('',*,*,#650,.F.);
#272=ORIENTED_EDGE('',*,*,#653,.T.);
#273=ORIENTED_EDGE('',*,*,#655,.T.);
#274=ORIENTED_EDGE('',*,*,#657,.F.);
#275=ORIENTED_EDGE('',*,*,#552,.F.);
#276=ORIENTED_EDGE('',*,*,#658,.F.);
#277=ORIENTED_EDGE('',*,*,#659,.T.);
#278=ORIENTED_EDGE('',*,*,#660,.T.);
#279=ORIENTED_EDGE('',*,*,#551,.T.);
#280=ORIENTED_EDGE('',*,*,#661,.F.);
#281=ORIENTED_EDGE('',*,*,#662,.F.);
#282=ORIENTED_EDGE('',*,*,#658,.T.);
#283=ORIENTED_EDGE('',*,*,#550,.T.);
#284=ORIENTED_EDGE('',*,*,#663,.F.);
#285=ORIENTED_EDGE('',*,*,#664,.F.);
#286=ORIENTED_EDGE('',*,*,#661,.T.);
#287=ORIENTED_EDGE('',*,*,#665,.F.);
#288=ORIENTED_EDGE('',*,*,#660,.F.);
#289=ORIENTED_EDGE('',*,*,#666,.T.);
#290=ORIENTED_EDGE('',*,*,#663,.T.);
#291=ORIENTED_EDGE('',*,*,#659,.F.);
#292=ORIENTED_EDGE('',*,*,#662,.T.);
#293=ORIENTED_EDGE('',*,*,#664,.T.);
#294=ORIENTED_EDGE('',*,*,#666,.F.);
#295=ORIENTED_EDGE('',*,*,#556,.F.);
#296=ORIENTED_EDGE('',*,*,#667,.F.);
#297=ORIENTED_EDGE('',*,*,#668,.T.);
#298=ORIENTED_EDGE('',*,*,#669,.T.);
#299=ORIENTED_EDGE('',*,*,#555,.T.);
#300=ORIENTED_EDGE('',*,*,#670,.F.);
#301=ORIENTED_EDGE('',*,*,#671,.F.);
#302=ORIENTED_EDGE('',*,*,#667,.T.);
#303=ORIENTED_EDGE('',*,*,#554,.T.);
#304=ORIENTED_EDGE('',*,*,#672,.F.);
#305=ORIENTED_EDGE('',*,*,#673,.F.);
#306=ORIENTED_EDGE('',*,*,#670,.T.);
#307=ORIENTED_EDGE('',*,*,#674,.F.);
#308=ORIENTED_EDGE('',*,*,#669,.F.);
#309=ORIENTED_EDGE('',*,*,#675,.T.);
#310=ORIENTED_EDGE('',*,*,#672,.T.);
#311=ORIENTED_EDGE('',*,*,#668,.F.);
#312=ORIENTED_EDGE('',*,*,#671,.T.);
#313=ORIENTED_EDGE('',*,*,#673,.T.);
#314=ORIENTED_EDGE('',*,*,#675,.F.);
#315=ORIENTED_EDGE('',*,*,#560,.F.);
#316=ORIENTED_EDGE('',*,*,#676,.F.);
#317=ORIENTED_EDGE('',*,*,#677,.T.);
#318=ORIENTED_EDGE('',*,*,#678,.T.);
#319=ORIENTED_EDGE('',*,*,#559,.T.);
#320=ORIENTED_EDGE('',*,*,#679,.F.);
#321=ORIENTED_EDGE('',*,*,#680,.F.);
#322=ORIENTED_EDGE('',*,*,#676,.T.);
#323=ORIENTED_EDGE('',*,*,#558,.T.);
#324=ORIENTED_EDGE('',*,*,#681,.F.);
#325=ORIENTED_EDGE('',*,*,#682,.F.);
#326=ORIENTED_EDGE('',*,*,#679,.T.);
#327=ORIENTED_EDGE('',*,*,#683,.F.);
#328=ORIENTED_EDGE('',*,*,#678,.F.);
#329=ORIENTED_EDGE('',*,*,#684,.T.);
#330=ORIENTED_EDGE('',*,*,#681,.T.);
#331=ORIENTED_EDGE('',*,*,#677,.F.);
#332=ORIENTED_EDGE('',*,*,#680,.T.);
#333=ORIENTED_EDGE('',*,*,#682,.T.);
#334=ORIENTED_EDGE('',*,*,#684,.F.);
#335=ORIENTED_EDGE('',*,*,#566,.F.);
#336=ORIENTED_EDGE('',*,*,#685,.F.);
#337=ORIENTED_EDGE('',*,*,#686,.T.);
#338=ORIENTED_EDGE('',*,*,#687,.T.);
#339=ORIENTED_EDGE('',*,*,#545,.T.);
#340=ORIENTED_EDGE('',*,*,#647,.T.);
#341=ORIENTED_EDGE('',*,*,#541,.T.);
#342=ORIENTED_EDGE('',*,*,#688,.F.);
#343=ORIENTED_EDGE('',*,*,#689,.F.);
#344=ORIENTED_EDGE('',*,*,#685,.T.);
#345=ORIENTED_EDGE('',*,*,#565,.T.);
#346=ORIENTED_EDGE('',*,*,#690,.T.);
#347=ORIENTED_EDGE('',*,*,#561,.T.);
#348=ORIENTED_EDGE('',*,*,#683,.T.);
#349=ORIENTED_EDGE('',*,*,#557,.T.);
#350=ORIENTED_EDGE('',*,*,#674,.T.);
#351=ORIENTED_EDGE('',*,*,#553,.T.);
#352=ORIENTED_EDGE('',*,*,#665,.T.);
#353=ORIENTED_EDGE('',*,*,#549,.T.);
#354=ORIENTED_EDGE('',*,*,#656,.T.);
#355=ORIENTED_EDGE('',*,*,#540,.T.);
#356=ORIENTED_EDGE('',*,*,#691,.F.);
#357=ORIENTED_EDGE('',*,*,#692,.F.);
#358=ORIENTED_EDGE('',*,*,#688,.T.);
#359=ORIENTED_EDGE('',*,*,#535,.F.);
#360=ORIENTED_EDGE('',*,*,#611,.F.);
#361=ORIENTED_EDGE('',*,*,#583,.F.);
#362=ORIENTED_EDGE('',*,*,#620,.F.);
#363=ORIENTED_EDGE('',*,*,#579,.F.);
#364=ORIENTED_EDGE('',*,*,#629,.F.);
#365=ORIENTED_EDGE('',*,*,#575,.F.);
#366=ORIENTED_EDGE('',*,*,#638,.F.);
#367=ORIENTED_EDGE('',*,*,#571,.F.);
#368=ORIENTED_EDGE('',*,*,#693,.F.);
#369=ORIENTED_EDGE('',*,*,#567,.F.);
#370=ORIENTED_EDGE('',*,*,#687,.F.);
#371=ORIENTED_EDGE('',*,*,#694,.T.);
#372=ORIENTED_EDGE('',*,*,#691,.T.);
#373=ORIENTED_EDGE('',*,*,#539,.F.);
#374=ORIENTED_EDGE('',*,*,#602,.F.);
#375=ORIENTED_EDGE('',*,*,#695,.F.);
#376=ORIENTED_EDGE('',*,*,#686,.F.);
#377=ORIENTED_EDGE('',*,*,#689,.T.);
#378=ORIENTED_EDGE('',*,*,#692,.T.);
#379=ORIENTED_EDGE('',*,*,#694,.F.);
#380=ORIENTED_EDGE('',*,*,#564,.F.);
#381=ORIENTED_EDGE('',*,*,#696,.F.);
#382=ORIENTED_EDGE('',*,*,#697,.T.);
#383=ORIENTED_EDGE('',*,*,#698,.T.);
#384=ORIENTED_EDGE('',*,*,#563,.T.);
#385=ORIENTED_EDGE('',*,*,#699,.F.);
#386=ORIENTED_EDGE('',*,*,#700,.F.);
#387=ORIENTED_EDGE('',*,*,#696,.T.);
#388=ORIENTED_EDGE('',*,*,#562,.T.);
#389=ORIENTED_EDGE('',*,*,#701,.F.);
#390=ORIENTED_EDGE('',*,*,#702,.F.);
#391=ORIENTED_EDGE('',*,*,#699,.T.);
#392=ORIENTED_EDGE('',*,*,#690,.F.);
#393=ORIENTED_EDGE('',*,*,#698,.F.);
#394=ORIENTED_EDGE('',*,*,#703,.T.);
#395=ORIENTED_EDGE('',*,*,#701,.T.);
#396=ORIENTED_EDGE('',*,*,#697,.F.);
#397=ORIENTED_EDGE('',*,*,#700,.T.);
#398=ORIENTED_EDGE('',*,*,#702,.T.);
#399=ORIENTED_EDGE('',*,*,#703,.F.);
#400=ORIENTED_EDGE('',*,*,#568,.T.);
#401=ORIENTED_EDGE('',*,*,#704,.F.);
#402=ORIENTED_EDGE('',*,*,#705,.F.);
#403=ORIENTED_EDGE('',*,*,#706,.T.);
#404=ORIENTED_EDGE('',*,*,#693,.T.);
#405=ORIENTED_EDGE('',*,*,#707,.F.);
#406=ORIENTED_EDGE('',*,*,#708,.F.);
#407=ORIENTED_EDGE('',*,*,#704,.T.);
#408=ORIENTED_EDGE('',*,*,#570,.F.);
#409=ORIENTED_EDGE('',*,*,#709,.F.);
#410=ORIENTED_EDGE('',*,*,#710,.T.);
#411=ORIENTED_EDGE('',*,*,#707,.T.);
#412=ORIENTED_EDGE('',*,*,#569,.F.);
#413=ORIENTED_EDGE('',*,*,#706,.F.);
#414=ORIENTED_EDGE('',*,*,#711,.T.);
#415=ORIENTED_EDGE('',*,*,#709,.T.);
#416=ORIENTED_EDGE('',*,*,#705,.T.);
#417=ORIENTED_EDGE('',*,*,#708,.T.);
#418=ORIENTED_EDGE('',*,*,#710,.F.);
#419=ORIENTED_EDGE('',*,*,#711,.F.);
#420=ORIENTED_EDGE('',*,*,#531,.T.);
#421=ORIENTED_EDGE('',*,*,#712,.F.);
#422=ORIENTED_EDGE('',*,*,#713,.F.);
#423=ORIENTED_EDGE('',*,*,#714,.T.);
#424=ORIENTED_EDGE('',*,*,#534,.T.);
#425=ORIENTED_EDGE('',*,*,#715,.F.);
#426=ORIENTED_EDGE('',*,*,#716,.F.);
#427=ORIENTED_EDGE('',*,*,#712,.T.);
#428=ORIENTED_EDGE('',*,*,#533,.F.);
#429=ORIENTED_EDGE('',*,*,#717,.F.);
#430=ORIENTED_EDGE('',*,*,#718,.T.);
#431=ORIENTED_EDGE('',*,*,#715,.T.);
#432=ORIENTED_EDGE('',*,*,#532,.F.);
#433=ORIENTED_EDGE('',*,*,#714,.F.);
#434=ORIENTED_EDGE('',*,*,#719,.T.);
#435=ORIENTED_EDGE('',*,*,#717,.T.);
#436=ORIENTED_EDGE('',*,*,#713,.T.);
#437=ORIENTED_EDGE('',*,*,#716,.T.);
#438=ORIENTED_EDGE('',*,*,#718,.F.);
#439=ORIENTED_EDGE('',*,*,#719,.F.);
#440=ORIENTED_EDGE('',*,*,#587,.T.);
#441=ORIENTED_EDGE('',*,*,#720,.F.);
#442=ORIENTED_EDGE('',*,*,#721,.F.);
#443=ORIENTED_EDGE('',*,*,#722,.T.);
#444=ORIENTED_EDGE('',*,*,#590,.T.);
#445=ORIENTED_EDGE('',*,*,#723,.F.);
#446=ORIENTED_EDGE('',*,*,#724,.F.);
#447=ORIENTED_EDGE('',*,*,#720,.T.);
#448=ORIENTED_EDGE('',*,*,#589,.F.);
#449=ORIENTED_EDGE('',*,*,#725,.F.);
#450=ORIENTED_EDGE('',*,*,#726,.T.);
#451=ORIENTED_EDGE('',*,*,#723,.T.);
#452=ORIENTED_EDGE('',*,*,#588,.F.);
#453=ORIENTED_EDGE('',*,*,#722,.F.);
#454=ORIENTED_EDGE('',*,*,#727,.T.);
#455=ORIENTED_EDGE('',*,*,#725,.T.);
#456=ORIENTED_EDGE('',*,*,#721,.T.);
#457=ORIENTED_EDGE('',*,*,#724,.T.);
#458=ORIENTED_EDGE('',*,*,#726,.F.);
#459=ORIENTED_EDGE('',*,*,#727,.F.);
#460=ORIENTED_EDGE('',*,*,#527,.T.);
#461=ORIENTED_EDGE('',*,*,#728,.F.);
#462=ORIENTED_EDGE('',*,*,#729,.F.);
#463=ORIENTED_EDGE('',*,*,#730,.T.);
#464=ORIENTED_EDGE('',*,*,#530,.T.);
#465=ORIENTED_EDGE('',*,*,#731,.F.);
#466=ORIENTED_EDGE('',*,*,#732,.F.);
#467=ORIENTED_EDGE('',*,*,#728,.T.);
#468=ORIENTED_EDGE('',*,*,#529,.F.);
#469=ORIENTED_EDGE('',*,*,#733,.F.);
#470=ORIENTED_EDGE('',*,*,#734,.T.);
#471=ORIENTED_EDGE('',*,*,#731,.T.);
#472=ORIENTED_EDGE('',*,*,#528,.F.);
#473=ORIENTED_EDGE('',*,*,#730,.F.);
#474=ORIENTED_EDGE('',*,*,#735,.T.);
#475=ORIENTED_EDGE('',*,*,#733,.T.);
#476=ORIENTED_EDGE('',*,*,#729,.T.);
#477=ORIENTED_EDGE('',*,*,#732,.T.);
#478=ORIENTED_EDGE('',*,*,#734,.F.);
#479=ORIENTED_EDGE('',*,*,#735,.F.);
#480=ORIENTED_EDGE('',*,*,#591,.T.);
#481=ORIENTED_EDGE('',*,*,#736,.F.);
#482=ORIENTED_EDGE('',*,*,#737,.F.);
#483=ORIENTED_EDGE('',*,*,#738,.T.);
#484=ORIENTED_EDGE('',*,*,#594,.T.);
#485=ORIENTED_EDGE('',*,*,#739,.F.);
#486=ORIENTED_EDGE('',*,*,#740,.F.);
#487=ORIENTED_EDGE('',*,*,#736,.T.);
#488=ORIENTED_EDGE('',*,*,#593,.F.);
#489=ORIENTED_EDGE('',*,*,#741,.F.);
#490=ORIENTED_EDGE('',*,*,#742,.T.);
#491=ORIENTED_EDGE('',*,*,#739,.T.);
#492=ORIENTED_EDGE('',*,*,#592,.F.);
#493=ORIENTED_EDGE('',*,*,#738,.F.);
#494=ORIENTED_EDGE('',*,*,#743,.T.);
#495=ORIENTED_EDGE('',*,*,#741,.T.);
#496=ORIENTED_EDGE('',*,*,#737,.T.);
#497=ORIENTED_EDGE('',*,*,#740,.T.);
#498=ORIENTED_EDGE('',*,*,#742,.F.);
#499=ORIENTED_EDGE('',*,*,#743,.F.);
#500=ORIENTED_EDGE('',*,*,#524,.F.);
#501=ORIENTED_EDGE('',*,*,#744,.F.);
#502=ORIENTED_EDGE('',*,*,#745,.T.);
#503=ORIENTED_EDGE('',*,*,#746,.T.);
#504=ORIENTED_EDGE('',*,*,#523,.T.);
#505=ORIENTED_EDGE('',*,*,#747,.F.);
#506=ORIENTED_EDGE('',*,*,#748,.F.);
#507=ORIENTED_EDGE('',*,*,#744,.T.);
#508=ORIENTED_EDGE('',*,*,#526,.T.);
#509=ORIENTED_EDGE('',*,*,#749,.F.);
#510=ORIENTED_EDGE('',*,*,#750,.F.);
#511=ORIENTED_EDGE('',*,*,#747,.T.);
#512=ORIENTED_EDGE('',*,*,#525,.F.);
#513=ORIENTED_EDGE('',*,*,#746,.F.);
#514=ORIENTED_EDGE('',*,*,#751,.T.);
#515=ORIENTED_EDGE('',*,*,#749,.T.);
#516=ORIENTED_EDGE('',*,*,#745,.F.);
#517=ORIENTED_EDGE('',*,*,#748,.T.);
#518=ORIENTED_EDGE('',*,*,#750,.T.);
#519=ORIENTED_EDGE('',*,*,#751,.F.);
#520=ORIENTED_EDGE('',*,*,#752,.F.);
#521=ORIENTED_EDGE('',*,*,#695,.T.);
#522=ORIENTED_EDGE('',*,*,#752,.T.);
#523=EDGE_CURVE('',#753,#754,#899,.T.);
#524=EDGE_CURVE('',#753,#755,#900,.T.);
#525=EDGE_CURVE('',#755,#756,#901,.T.);
#526=EDGE_CURVE('',#754,#756,#902,.T.);
#527=EDGE_CURVE('',#757,#758,#903,.T.);
#528=EDGE_CURVE('',#757,#759,#904,.T.);
#529=EDGE_CURVE('',#759,#760,#905,.T.);
#530=EDGE_CURVE('',#758,#760,#29,.T.);
#531=EDGE_CURVE('',#761,#762,#906,.T.);
#532=EDGE_CURVE('',#761,#763,#907,.T.);
#533=EDGE_CURVE('',#763,#764,#908,.T.);
#534=EDGE_CURVE('',#762,#764,#30,.T.);
#535=EDGE_CURVE('',#765,#766,#909,.T.);
#536=EDGE_CURVE('',#767,#766,#910,.T.);
#537=EDGE_CURVE('',#767,#768,#31,.T.);
#538=EDGE_CURVE('',#768,#769,#911,.T.);
#539=EDGE_CURVE('',#769,#770,#912,.T.);
#540=EDGE_CURVE('',#771,#770,#913,.T.);
#541=EDGE_CURVE('',#772,#771,#914,.T.);
#542=EDGE_CURVE('',#773,#772,#915,.T.);
#543=EDGE_CURVE('',#774,#773,#32,.T.);
#544=EDGE_CURVE('',#774,#775,#916,.T.);
#545=EDGE_CURVE('',#776,#775,#917,.T.);
#546=EDGE_CURVE('',#777,#776,#918,.T.);
#547=EDGE_CURVE('',#778,#777,#33,.T.);
#548=EDGE_CURVE('',#778,#779,#919,.T.);
#549=EDGE_CURVE('',#780,#779,#920,.T.);
#550=EDGE_CURVE('',#781,#780,#921,.T.);
#551=EDGE_CURVE('',#782,#781,#34,.T.);
#552=EDGE_CURVE('',#782,#783,#922,.T.);
#553=EDGE_CURVE('',#784,#783,#923,.T.);
#554=EDGE_CURVE('',#785,#784,#924,.T.);
#555=EDGE_CURVE('',#786,#785,#35,.T.);
#556=EDGE_CURVE('',#786,#787,#925,.T.);
#557=EDGE_CURVE('',#788,#787,#926,.T.);
#558=EDGE_CURVE('',#789,#788,#927,.T.);
#559=EDGE_CURVE('',#790,#789,#36,.T.);
#560=EDGE_CURVE('',#790,#791,#928,.T.);
#561=EDGE_CURVE('',#792,#791,#929,.T.);
#562=EDGE_CURVE('',#793,#792,#930,.T.);
#563=EDGE_CURVE('',#794,#793,#37,.T.);
#564=EDGE_CURVE('',#794,#795,#931,.T.);
#565=EDGE_CURVE('',#796,#795,#932,.T.);
#566=EDGE_CURVE('',#796,#797,#933,.T.);
#567=EDGE_CURVE('',#797,#798,#934,.T.);
#568=EDGE_CURVE('',#799,#798,#935,.T.);
#569=EDGE_CURVE('',#799,#800,#38,.T.);
#570=EDGE_CURVE('',#800,#801,#936,.T.);
#571=EDGE_CURVE('',#801,#802,#937,.T.);
#572=EDGE_CURVE('',#803,#802,#938,.T.);
#573=EDGE_CURVE('',#803,#804,#39,.T.);
#574=EDGE_CURVE('',#804,#805,#939,.T.);
#575=EDGE_CURVE('',#805,#806,#940,.T.);
#576=EDGE_CURVE('',#807,#806,#941,.T.);
#577=EDGE_CURVE('',#807,#808,#40,.T.);
#578=EDGE_CURVE('',#808,#809,#942,.T.);
#579=EDGE_CURVE('',#809,#810,#943,.T.);
#580=EDGE_CURVE('',#811,#810,#944,.T.);
#581=EDGE_CURVE('',#811,#812,#41,.T.);
#582=EDGE_CURVE('',#812,#813,#945,.T.);
#583=EDGE_CURVE('',#813,#814,#946,.T.);
#584=EDGE_CURVE('',#815,#814,#947,.T.);
#585=EDGE_CURVE('',#815,#816,#42,.T.);
#586=EDGE_CURVE('',#816,#765,#948,.T.);
#587=EDGE_CURVE('',#817,#818,#949,.T.);
#588=EDGE_CURVE('',#817,#819,#950,.T.);
#589=EDGE_CURVE('',#819,#820,#43,.T.);
#590=EDGE_CURVE('',#818,#820,#951,.T.);
#591=EDGE_CURVE('',#821,#822,#952,.T.);
#592=EDGE_CURVE('',#821,#823,#953,.T.);
#593=EDGE_CURVE('',#823,#824,#44,.T.);
#594=EDGE_CURVE('',#822,#824,#954,.T.);
#595=EDGE_CURVE('',#825,#766,#955,.T.);
#596=EDGE_CURVE('',#826,#825,#956,.T.);
#597=EDGE_CURVE('',#826,#767,#957,.T.);
#598=EDGE_CURVE('',#826,#827,#45,.T.);
#599=EDGE_CURVE('',#827,#768,#958,.T.);
#600=EDGE_CURVE('',#827,#828,#959,.T.);
#601=EDGE_CURVE('',#828,#769,#960,.T.);
#602=EDGE_CURVE('',#766,#769,#961,.T.);
#603=EDGE_CURVE('',#825,#828,#962,.T.);
#604=EDGE_CURVE('',#829,#814,#963,.T.);
#605=EDGE_CURVE('',#830,#829,#964,.T.);
#606=EDGE_CURVE('',#830,#815,#965,.T.);
#607=EDGE_CURVE('',#830,#831,#46,.T.);
#608=EDGE_CURVE('',#831,#816,#966,.T.);
#609=EDGE_CURVE('',#831,#832,#967,.T.);
#610=EDGE_CURVE('',#832,#765,#968,.T.);
#611=EDGE_CURVE('',#814,#765,#969,.T.);
#612=EDGE_CURVE('',#829,#832,#970,.T.);
#613=EDGE_CURVE('',#833,#810,#971,.T.);
#614=EDGE_CURVE('',#834,#833,#972,.T.);
#615=EDGE_CURVE('',#834,#811,#973,.T.);
#616=EDGE_CURVE('',#834,#835,#47,.T.);
#617=EDGE_CURVE('',#835,#812,#974,.T.);
#618=EDGE_CURVE('',#835,#836,#975,.T.);
#619=EDGE_CURVE('',#836,#813,#976,.T.);
#620=EDGE_CURVE('',#810,#813,#977,.T.);
#621=EDGE_CURVE('',#833,#836,#978,.T.);
#622=EDGE_CURVE('',#837,#806,#979,.T.);
#623=EDGE_CURVE('',#838,#837,#980,.T.);
#624=EDGE_CURVE('',#838,#807,#981,.T.);
#625=EDGE_CURVE('',#838,#839,#48,.T.);
#626=EDGE_CURVE('',#839,#808,#982,.T.);
#627=EDGE_CURVE('',#839,#840,#983,.T.);
#628=EDGE_CURVE('',#840,#809,#984,.T.);
#629=EDGE_CURVE('',#806,#809,#985,.T.);
#630=EDGE_CURVE('',#837,#840,#986,.T.);
#631=EDGE_CURVE('',#841,#802,#987,.T.);
#632=EDGE_CURVE('',#842,#841,#988,.T.);
#633=EDGE_CURVE('',#842,#803,#989,.T.);
#634=EDGE_CURVE('',#842,#843,#49,.T.);
#635=EDGE_CURVE('',#843,#804,#990,.T.);
#636=EDGE_CURVE('',#843,#844,#991,.T.);
#637=EDGE_CURVE('',#844,#805,#992,.T.);
#638=EDGE_CURVE('',#802,#805,#993,.T.);
#639=EDGE_CURVE('',#841,#844,#994,.T.);
#640=EDGE_CURVE('',#845,#774,#995,.T.);
#641=EDGE_CURVE('',#845,#846,#996,.T.);
#642=EDGE_CURVE('',#846,#775,#997,.T.);
#643=EDGE_CURVE('',#847,#773,#998,.T.);
#644=EDGE_CURVE('',#845,#847,#50,.T.);
#645=EDGE_CURVE('',#848,#772,#999,.T.);
#646=EDGE_CURVE('',#847,#848,#1000,.T.);
#647=EDGE_CURVE('',#775,#772,#1001,.T.);
#648=EDGE_CURVE('',#846,#848,#1002,.T.);
#649=EDGE_CURVE('',#849,#778,#1003,.T.);
#650=EDGE_CURVE('',#849,#850,#1004,.T.);
#651=EDGE_CURVE('',#850,#779,#1005,.T.);
#652=EDGE_CURVE('',#851,#777,#1006,.T.);
#653=EDGE_CURVE('',#849,#851,#51,.T.);
#654=EDGE_CURVE('',#852,#776,#1007,.T.);
#655=EDGE_CURVE('',#851,#852,#1008,.T.);
#656=EDGE_CURVE('',#779,#776,#1009,.T.);
#657=EDGE_CURVE('',#850,#852,#1010,.T.);
#658=EDGE_CURVE('',#853,#782,#1011,.T.);
#659=EDGE_CURVE('',#853,#854,#1012,.T.);
#660=EDGE_CURVE('',#854,#783,#1013,.T.);
#661=EDGE_CURVE('',#855,#781,#1014,.T.);
#662=EDGE_CURVE('',#853,#855,#52,.T.);
#663=EDGE_CURVE('',#856,#780,#1015,.T.);
#664=EDGE_CURVE('',#855,#856,#1016,.T.);
#665=EDGE_CURVE('',#783,#780,#1017,.T.);
#666=EDGE_CURVE('',#854,#856,#1018,.T.);
#667=EDGE_CURVE('',#857,#786,#1019,.T.);
#668=EDGE_CURVE('',#857,#858,#1020,.T.);
#669=EDGE_CURVE('',#858,#787,#1021,.T.);
#670=EDGE_CURVE('',#859,#785,#1022,.T.);
#671=EDGE_CURVE('',#857,#859,#53,.T.);
#672=EDGE_CURVE('',#860,#784,#1023,.T.);
#673=EDGE_CURVE('',#859,#860,#1024,.T.);
#674=EDGE_CURVE('',#787,#784,#1025,.T.);
#675=EDGE_CURVE('',#858,#860,#1026,.T.);
#676=EDGE_CURVE('',#861,#790,#1027,.T.);
#677=EDGE_CURVE('',#861,#862,#1028,.T.);
#678=EDGE_CURVE('',#862,#791,#1029,.T.);
#679=EDGE_CURVE('',#863,#789,#1030,.T.);
#680=EDGE_CURVE('',#861,#863,#54,.T.);
#681=EDGE_CURVE('',#864,#788,#1031,.T.);
#682=EDGE_CURVE('',#863,#864,#1032,.T.);
#683=EDGE_CURVE('',#791,#788,#1033,.T.);
#684=EDGE_CURVE('',#862,#864,#1034,.T.);
#685=EDGE_CURVE('',#865,#796,#1035,.T.);
#686=EDGE_CURVE('',#865,#866,#1036,.T.);
#687=EDGE_CURVE('',#866,#797,#1037,.T.);
#688=EDGE_CURVE('',#867,#771,#1038,.T.);
#689=EDGE_CURVE('',#865,#867,#1039,.T.);
#690=EDGE_CURVE('',#795,#792,#1040,.T.);
#691=EDGE_CURVE('',#868,#770,#1041,.T.);
#692=EDGE_CURVE('',#867,#868,#1042,.T.);
#693=EDGE_CURVE('',#798,#801,#1043,.T.);
#694=EDGE_CURVE('',#866,#868,#1044,.T.);
#695=EDGE_CURVE('',#869,#869,#55,.T.);
#696=EDGE_CURVE('',#870,#794,#1045,.T.);
#697=EDGE_CURVE('',#870,#871,#1046,.T.);
#698=EDGE_CURVE('',#871,#795,#1047,.T.);
#699=EDGE_CURVE('',#872,#793,#1048,.T.);
#700=EDGE_CURVE('',#870,#872,#56,.T.);
#701=EDGE_CURVE('',#873,#792,#1049,.T.);
#702=EDGE_CURVE('',#872,#873,#1050,.T.);
#703=EDGE_CURVE('',#871,#873,#1051,.T.);
#704=EDGE_CURVE('',#874,#798,#1052,.T.);
#705=EDGE_CURVE('',#875,#874,#1053,.T.);
#706=EDGE_CURVE('',#875,#799,#1054,.T.);
#707=EDGE_CURVE('',#876,#801,#1055,.T.);
#708=EDGE_CURVE('',#874,#876,#1056,.T.);
#709=EDGE_CURVE('',#877,#800,#1057,.T.);
#710=EDGE_CURVE('',#877,#876,#1058,.T.);
#711=EDGE_CURVE('',#875,#877,#57,.T.);
#712=EDGE_CURVE('',#878,#762,#1059,.T.);
#713=EDGE_CURVE('',#879,#878,#1060,.T.);
#714=EDGE_CURVE('',#879,#761,#1061,.T.);
#715=EDGE_CURVE('',#880,#764,#1062,.T.);
#716=EDGE_CURVE('',#878,#880,#58,.T.);
#717=EDGE_CURVE('',#881,#763,#1063,.T.);
#718=EDGE_CURVE('',#881,#880,#1064,.T.);
#719=EDGE_CURVE('',#879,#881,#1065,.T.);
#720=EDGE_CURVE('',#882,#818,#1066,.T.);
#721=EDGE_CURVE('',#883,#882,#1067,.T.);
#722=EDGE_CURVE('',#883,#817,#1068,.T.);
#723=EDGE_CURVE('',#884,#820,#1069,.T.);
#724=EDGE_CURVE('',#882,#884,#1070,.T.);
#725=EDGE_CURVE('',#885,#819,#1071,.T.);
#726=EDGE_CURVE('',#885,#884,#59,.T.);
#727=EDGE_CURVE('',#883,#885,#1072,.T.);
#728=EDGE_CURVE('',#886,#758,#1073,.T.);
#729=EDGE_CURVE('',#887,#886,#1074,.T.);
#730=EDGE_CURVE('',#887,#757,#1075,.T.);
#731=EDGE_CURVE('',#888,#760,#1076,.T.);
#732=EDGE_CURVE('',#886,#888,#60,.T.);
#733=EDGE_CURVE('',#889,#759,#1077,.T.);
#734=EDGE_CURVE('',#889,#888,#1078,.T.);
#735=EDGE_CURVE('',#887,#889,#1079,.T.);
#736=EDGE_CURVE('',#890,#822,#1080,.T.);
#737=EDGE_CURVE('',#891,#890,#1081,.T.);
#738=EDGE_CURVE('',#891,#821,#1082,.T.);
#739=EDGE_CURVE('',#892,#824,#1083,.T.);
#740=EDGE_CURVE('',#890,#892,#1084,.T.);
#741=EDGE_CURVE('',#893,#823,#1085,.T.);
#742=EDGE_CURVE('',#893,#892,#61,.T.);
#743=EDGE_CURVE('',#891,#893,#1086,.T.);
#744=EDGE_CURVE('',#894,#753,#1087,.T.);
#745=EDGE_CURVE('',#894,#895,#1088,.T.);
#746=EDGE_CURVE('',#895,#755,#1089,.T.);
#747=EDGE_CURVE('',#896,#754,#1090,.T.);
#748=EDGE_CURVE('',#894,#896,#1091,.T.);
#749=EDGE_CURVE('',#897,#756,#1092,.T.);
#750=EDGE_CURVE('',#896,#897,#1093,.T.);
#751=EDGE_CURVE('',#895,#897,#1094,.T.);
#752=EDGE_CURVE('',#898,#898,#62,.T.);
#753=VERTEX_POINT('',#2298);
#754=VERTEX_POINT('',#2299);
#755=VERTEX_POINT('',#2301);
#756=VERTEX_POINT('',#2303);
#757=VERTEX_POINT('',#2306);
#758=VERTEX_POINT('',#2307);
#759=VERTEX_POINT('',#2309);
#760=VERTEX_POINT('',#2311);
#761=VERTEX_POINT('',#2314);
#762=VERTEX_POINT('',#2315);
#763=VERTEX_POINT('',#2317);
#764=VERTEX_POINT('',#2319);
#765=VERTEX_POINT('',#2322);
#766=VERTEX_POINT('',#2323);
#767=VERTEX_POINT('',#2325);
#768=VERTEX_POINT('',#2327);
#769=VERTEX_POINT('',#2329);
#770=VERTEX_POINT('',#2331);
#771=VERTEX_POINT('',#2333);
#772=VERTEX_POINT('',#2335);
#773=VERTEX_POINT('',#2337);
#774=VERTEX_POINT('',#2339);
#775=VERTEX_POINT('',#2341);
#776=VERTEX_POINT('',#2343);
#777=VERTEX_POINT('',#2345);
#778=VERTEX_POINT('',#2347);
#779=VERTEX_POINT('',#2349);
#780=VERTEX_POINT('',#2351);
#781=VERTEX_POINT('',#2353);
#782=VERTEX_POINT('',#2355);
#783=VERTEX_POINT('',#2357);
#784=VERTEX_POINT('',#2359);
#785=VERTEX_POINT('',#2361);
#786=VERTEX_POINT('',#2363);
#787=VERTEX_POINT('',#2365);
#788=VERTEX_POINT('',#2367);
#789=VERTEX_POINT('',#2369);
#790=VERTEX_POINT('',#2371);
#791=VERTEX_POINT('',#2373);
#792=VERTEX_POINT('',#2375);
#793=VERTEX_POINT('',#2377);
#794=VERTEX_POINT('',#2379);
#795=VERTEX_POINT('',#2381);
#796=VERTEX_POINT('',#2383);
#797=VERTEX_POINT('',#2385);
#798=VERTEX_POINT('',#2387);
#799=VERTEX_POINT('',#2389);
#800=VERTEX_POINT('',#2391);
#801=VERTEX_POINT('',#2393);
#802=VERTEX_POINT('',#2395);
#803=VERTEX_POINT('',#2397);
#804=VERTEX_POINT('',#2399);
#805=VERTEX_POINT('',#2401);
#806=VERTEX_POINT('',#2403);
#807=VERTEX_POINT('',#2405);
#808=VERTEX_POINT('',#2407);
#809=VERTEX_POINT('',#2409);
#810=VERTEX_POINT('',#2411);
#811=VERTEX_POINT('',#2413);
#812=VERTEX_POINT('',#2415);
#813=VERTEX_POINT('',#2417);
#814=VERTEX_POINT('',#2419);
#815=VERTEX_POINT('',#2421);
#816=VERTEX_POINT('',#2423);
#817=VERTEX_POINT('',#2426);
#818=VERTEX_POINT('',#2427);
#819=VERTEX_POINT('',#2429);
#820=VERTEX_POINT('',#2431);
#821=VERTEX_POINT('',#2434);
#822=VERTEX_POINT('',#2435);
#823=VERTEX_POINT('',#2437);
#824=VERTEX_POINT('',#2439);
#825=VERTEX_POINT('',#2443);
#826=VERTEX_POINT('',#2445);
#827=VERTEX_POINT('',#2449);
#828=VERTEX_POINT('',#2453);
#829=VERTEX_POINT('',#2461);
#830=VERTEX_POINT('',#2463);
#831=VERTEX_POINT('',#2467);
#832=VERTEX_POINT('',#2471);
#833=VERTEX_POINT('',#2479);
#834=VERTEX_POINT('',#2481);
#835=VERTEX_POINT('',#2485);
#836=VERTEX_POINT('',#2489);
#837=VERTEX_POINT('',#2497);
#838=VERTEX_POINT('',#2499);
#839=VERTEX_POINT('',#2503);
#840=VERTEX_POINT('',#2507);
#841=VERTEX_POINT('',#2515);
#842=VERTEX_POINT('',#2517);
#843=VERTEX_POINT('',#2521);
#844=VERTEX_POINT('',#2525);
#845=VERTEX_POINT('',#2533);
#846=VERTEX_POINT('',#2535);
#847=VERTEX_POINT('',#2539);
#848=VERTEX_POINT('',#2543);
#849=VERTEX_POINT('',#2551);
#850=VERTEX_POINT('',#2553);
#851=VERTEX_POINT('',#2557);
#852=VERTEX_POINT('',#2561);
#853=VERTEX_POINT('',#2569);
#854=VERTEX_POINT('',#2571);
#855=VERTEX_POINT('',#2575);
#856=VERTEX_POINT('',#2579);
#857=VERTEX_POINT('',#2587);
#858=VERTEX_POINT('',#2589);
#859=VERTEX_POINT('',#2593);
#860=VERTEX_POINT('',#2597);
#861=VERTEX_POINT('',#2605);
#862=VERTEX_POINT('',#2607);
#863=VERTEX_POINT('',#2611);
#864=VERTEX_POINT('',#2615);
#865=VERTEX_POINT('',#2623);
#866=VERTEX_POINT('',#2625);
#867=VERTEX_POINT('',#2629);
#868=VERTEX_POINT('',#2634);
#869=VERTEX_POINT('',#2641);
#870=VERTEX_POINT('',#2644);
#871=VERTEX_POINT('',#2646);
#872=VERTEX_POINT('',#2650);
#873=VERTEX_POINT('',#2654);
#874=VERTEX_POINT('',#2661);
#875=VERTEX_POINT('',#2663);
#876=VERTEX_POINT('',#2667);
#877=VERTEX_POINT('',#2671);
#878=VERTEX_POINT('',#2678);
#879=VERTEX_POINT('',#2680);
#880=VERTEX_POINT('',#2684);
#881=VERTEX_POINT('',#2688);
#882=VERTEX_POINT('',#2695);
#883=VERTEX_POINT('',#2697);
#884=VERTEX_POINT('',#2701);
#885=VERTEX_POINT('',#2705);
#886=VERTEX_POINT('',#2712);
#887=VERTEX_POINT('',#2714);
#888=VERTEX_POINT('',#2718);
#889=VERTEX_POINT('',#2722);
#890=VERTEX_POINT('',#2729);
#891=VERTEX_POINT('',#2731);
#892=VERTEX_POINT('',#2735);
#893=VERTEX_POINT('',#2739);
#894=VERTEX_POINT('',#2746);
#895=VERTEX_POINT('',#2748);
#896=VERTEX_POINT('',#2752);
#897=VERTEX_POINT('',#2756);
#898=VERTEX_POINT('',#2763);
#899=LINE('',#2297,#1095);
#900=LINE('',#2300,#1096);
#901=LINE('',#2302,#1097);
#902=LINE('',#2304,#1098);
#903=LINE('',#2305,#1099);
#904=LINE('',#2308,#1100);
#905=LINE('',#2310,#1101);
#906=LINE('',#2313,#1102);
#907=LINE('',#2316,#1103);
#908=LINE('',#2318,#1104);
#909=LINE('',#2321,#1105);
#910=LINE('',#2324,#1106);
#911=LINE('',#2328,#1107);
#912=LINE('',#2330,#1108);
#913=LINE('',#2332,#1109);
#914=LINE('',#2334,#1110);
#915=LINE('',#2336,#1111);
#916=LINE('',#2340,#1112);
#917=LINE('',#2342,#1113);
#918=LINE('',#2344,#1114);
#919=LINE('',#2348,#1115);
#920=LINE('',#2350,#1116);
#921=LINE('',#2352,#1117);
#922=LINE('',#2356,#1118);
#923=LINE('',#2358,#1119);
#924=LINE('',#2360,#1120);
#925=LINE('',#2364,#1121);
#926=LINE('',#2366,#1122);
#927=LINE('',#2368,#1123);
#928=LINE('',#2372,#1124);
#929=LINE('',#2374,#1125);
#930=LINE('',#2376,#1126);
#931=LINE('',#2380,#1127);
#932=LINE('',#2382,#1128);
#933=LINE('',#2384,#1129);
#934=LINE('',#2386,#1130);
#935=LINE('',#2388,#1131);
#936=LINE('',#2392,#1132);
#937=LINE('',#2394,#1133);
#938=LINE('',#2396,#1134);
#939=LINE('',#2400,#1135);
#940=LINE('',#2402,#1136);
#941=LINE('',#2404,#1137);
#942=LINE('',#2408,#1138);
#943=LINE('',#2410,#1139);
#944=LINE('',#2412,#1140);
#945=LINE('',#2416,#1141);
#946=LINE('',#2418,#1142);
#947=LINE('',#2420,#1143);
#948=LINE('',#2424,#1144);
#949=LINE('',#2425,#1145);
#950=LINE('',#2428,#1146);
#951=LINE('',#2432,#1147);
#952=LINE('',#2433,#1148);
#953=LINE('',#2436,#1149);
#954=LINE('',#2440,#1150);
#955=LINE('',#2442,#1151);
#956=LINE('',#2444,#1152);
#957=LINE('',#2446,#1153);
#958=LINE('',#2450,#1154);
#959=LINE('',#2452,#1155);
#960=LINE('',#2454,#1156);
#961=LINE('',#2456,#1157);
#962=LINE('',#2457,#1158);
#963=LINE('',#2460,#1159);
#964=LINE('',#2462,#1160);
#965=LINE('',#2464,#1161);
#966=LINE('',#2468,#1162);
#967=LINE('',#2470,#1163);
#968=LINE('',#2472,#1164);
#969=LINE('',#2474,#1165);
#970=LINE('',#2475,#1166);
#971=LINE('',#2478,#1167);
#972=LINE('',#2480,#1168);
#973=LINE('',#2482,#1169);
#974=LINE('',#2486,#1170);
#975=LINE('',#2488,#1171);
#976=LINE('',#2490,#1172);
#977=LINE('',#2492,#1173);
#978=LINE('',#2493,#1174);
#979=LINE('',#2496,#1175);
#980=LINE('',#2498,#1176);
#981=LINE('',#2500,#1177);
#982=LINE('',#2504,#1178);
#983=LINE('',#2506,#1179);
#984=LINE('',#2508,#1180);
#985=LINE('',#2510,#1181);
#986=LINE('',#2511,#1182);
#987=LINE('',#2514,#1183);
#988=LINE('',#2516,#1184);
#989=LINE('',#2518,#1185);
#990=LINE('',#2522,#1186);
#991=LINE('',#2524,#1187);
#992=LINE('',#2526,#1188);
#993=LINE('',#2528,#1189);
#994=LINE('',#2529,#1190);
#995=LINE('',#2532,#1191);
#996=LINE('',#2534,#1192);
#997=LINE('',#2536,#1193);
#998=LINE('',#2538,#1194);
#999=LINE('',#2542,#1195);
#1000=LINE('',#2544,#1196);
#1001=LINE('',#2546,#1197);
#1002=LINE('',#2547,#1198);
#1003=LINE('',#2550,#1199);
#1004=LINE('',#2552,#1200);
#1005=LINE('',#2554,#1201);
#1006=LINE('',#2556,#1202);
#1007=LINE('',#2560,#1203);
#1008=LINE('',#2562,#1204);
#1009=LINE('',#2564,#1205);
#1010=LINE('',#2565,#1206);
#1011=LINE('',#2568,#1207);
#1012=LINE('',#2570,#1208);
#1013=LINE('',#2572,#1209);
#1014=LINE('',#2574,#1210);
#1015=LINE('',#2578,#1211);
#1016=LINE('',#2580,#1212);
#1017=LINE('',#2582,#1213);
#1018=LINE('',#2583,#1214);
#1019=LINE('',#2586,#1215);
#1020=LINE('',#2588,#1216);
#1021=LINE('',#2590,#1217);
#1022=LINE('',#2592,#1218);
#1023=LINE('',#2596,#1219);
#1024=LINE('',#2598,#1220);
#1025=LINE('',#2600,#1221);
#1026=LINE('',#2601,#1222);
#1027=LINE('',#2604,#1223);
#1028=LINE('',#2606,#1224);
#1029=LINE('',#2608,#1225);
#1030=LINE('',#2610,#1226);
#1031=LINE('',#2614,#1227);
#1032=LINE('',#2616,#1228);
#1033=LINE('',#2618,#1229);
#1034=LINE('',#2619,#1230);
#1035=LINE('',#2622,#1231);
#1036=LINE('',#2624,#1232);
#1037=LINE('',#2626,#1233);
#1038=LINE('',#2628,#1234);
#1039=LINE('',#2630,#1235);
#1040=LINE('',#2631,#1236);
#1041=LINE('',#2633,#1237);
#1042=LINE('',#2635,#1238);
#1043=LINE('',#2637,#1239);
#1044=LINE('',#2638,#1240);
#1045=LINE('',#2643,#1241);
#1046=LINE('',#2645,#1242);
#1047=LINE('',#2647,#1243);
#1048=LINE('',#2649,#1244);
#1049=LINE('',#2653,#1245);
#1050=LINE('',#2655,#1246);
#1051=LINE('',#2657,#1247);
#1052=LINE('',#2660,#1248);
#1053=LINE('',#2662,#1249);
#1054=LINE('',#2664,#1250);
#1055=LINE('',#2666,#1251);
#1056=LINE('',#2668,#1252);
#1057=LINE('',#2670,#1253);
#1058=LINE('',#2672,#1254);
#1059=LINE('',#2677,#1255);
#1060=LINE('',#2679,#1256);
#1061=LINE('',#2681,#1257);
#1062=LINE('',#2683,#1258);
#1063=LINE('',#2687,#1259);
#1064=LINE('',#2689,#1260);
#1065=LINE('',#2691,#1261);
#1066=LINE('',#2694,#1262);
#1067=LINE('',#2696,#1263);
#1068=LINE('',#2698,#1264);
#1069=LINE('',#2700,#1265);
#1070=LINE('',#2702,#1266);
#1071=LINE('',#2704,#1267);
#1072=LINE('',#2708,#1268);
#1073=LINE('',#2711,#1269);
#1074=LINE('',#2713,#1270);
#1075=LINE('',#2715,#1271);
#1076=LINE('',#2717,#1272);
#1077=LINE('',#2721,#1273);
#1078=LINE('',#2723,#1274);
#1079=LINE('',#2725,#1275);
#1080=LINE('',#2728,#1276);
#1081=LINE('',#2730,#1277);
#1082=LINE('',#2732,#1278);
#1083=LINE('',#2734,#1279);
#1084=LINE('',#2736,#1280);
#1085=LINE('',#2738,#1281);
#1086=LINE('',#2742,#1282);
#1087=LINE('',#2745,#1283);
#1088=LINE('',#2747,#1284);
#1089=LINE('',#2749,#1285);
#1090=LINE('',#2751,#1286);
#1091=LINE('',#2753,#1287);
#1092=LINE('',#2755,#1288);
#1093=LINE('',#2757,#1289);
#1094=LINE('',#2759,#1290);
#1095=VECTOR('',#1847,1.);
#1096=VECTOR('',#1848,1.);
#1097=VECTOR('',#1849,1.);
#1098=VECTOR('',#1850,1.);
#1099=VECTOR('',#1851,1.);
#1100=VECTOR('',#1852,1.);
#1101=VECTOR('',#1853,1.);
#1102=VECTOR('',#1856,1.);
#1103=VECTOR('',#1857,1.);
#1104=VECTOR('',#1858,1.);
#1105=VECTOR('',#1861,1.);
#1106=VECTOR('',#1862,1.);
#1107=VECTOR('',#1865,1.);
#1108=VECTOR('',#1866,1.);
#1109=VECTOR('',#1867,1.);
#1110=VECTOR('',#1868,1.);
#1111=VECTOR('',#1869,1.);
#1112=VECTOR('',#1872,1.);
#1113=VECTOR('',#1873,1.);
#1114=VECTOR('',#1874,1.);
#1115=VECTOR('',#1877,1.);
#1116=VECTOR('',#1878,1.);
#1117=VECTOR('',#1879,1.);
#1118=VECTOR('',#1882,1.);
#1119=VECTOR('',#1883,1.);
#1120=VECTOR('',#1884,1.);
#1121=VECTOR('',#1887,1.);
#1122=VECTOR('',#1888,1.);
#1123=VECTOR('',#1889,1.);
#1124=VECTOR('',#1892,1.);
#1125=VECTOR('',#1893,1.);
#1126=VECTOR('',#1894,1.);
#1127=VECTOR('',#1897,1.);
#1128=VECTOR('',#1898,1.);
#1129=VECTOR('',#1899,1.);
#1130=VECTOR('',#1900,1.);
#1131=VECTOR('',#1901,1.);
#1132=VECTOR('',#1904,1.);
#1133=VECTOR('',#1905,1.);
#1134=VECTOR('',#1906,1.);
#1135=VECTOR('',#1909,1.);
#1136=VECTOR('',#1910,1.);
#1137=VECTOR('',#1911,1.);
#1138=VECTOR('',#1914,1.);
#1139=VECTOR('',#1915,1.);
#1140=VECTOR('',#1916,1.);
#1141=VECTOR('',#1919,1.);
#1142=VECTOR('',#1920,1.);
#1143=VECTOR('',#1921,1.);
#1144=VECTOR('',#1924,1.);
#1145=VECTOR('',#1925,1.);
#1146=VECTOR('',#1926,1.);
#1147=VECTOR('',#1929,1.);
#1148=VECTOR('',#1930,1.);
#1149=VECTOR('',#1931,1.);
#1150=VECTOR('',#1934,1.);
#1151=VECTOR('',#1937,1.);
#1152=VECTOR('',#1938,1.);
#1153=VECTOR('',#1939,1.);
#1154=VECTOR('',#1944,1.);
#1155=VECTOR('',#1947,1.);
#1156=VECTOR('',#1948,1.);
#1157=VECTOR('',#1951,1.);
#1158=VECTOR('',#1952,1.);
#1159=VECTOR('',#1957,1.);
#1160=VECTOR('',#1958,1.);
#1161=VECTOR('',#1959,1.);
#1162=VECTOR('',#1964,1.);
#1163=VECTOR('',#1967,1.);
#1164=VECTOR('',#1968,1.);
#1165=VECTOR('',#1971,1.);
#1166=VECTOR('',#1972,1.);
#1167=VECTOR('',#1977,1.);
#1168=VECTOR('',#1978,1.);
#1169=VECTOR('',#1979,1.);
#1170=VECTOR('',#1984,1.);
#1171=VECTOR('',#1987,1.);
#1172=VECTOR('',#1988,1.);
#1173=VECTOR('',#1991,1.);
#1174=VECTOR('',#1992,1.);
#1175=VECTOR('',#1997,1.);
#1176=VECTOR('',#1998,1.);
#1177=VECTOR('',#1999,1.);
#1178=VECTOR('',#2004,1.);
#1179=VECTOR('',#2007,1.);
#1180=VECTOR('',#2008,1.);
#1181=VECTOR('',#2011,1.);
#1182=VECTOR('',#2012,1.);
#1183=VECTOR('',#2017,1.);
#1184=VECTOR('',#2018,1.);
#1185=VECTOR('',#2019,1.);
#1186=VECTOR('',#2024,1.);
#1187=VECTOR('',#2027,1.);
#1188=VECTOR('',#2028,1.);
#1189=VECTOR('',#2031,1.);
#1190=VECTOR('',#2032,1.);
#1191=VECTOR('',#2037,1.);
#1192=VECTOR('',#2038,1.);
#1193=VECTOR('',#2039,1.);
#1194=VECTOR('',#2042,1.);
#1195=VECTOR('',#2047,1.);
#1196=VECTOR('',#2048,1.);
#1197=VECTOR('',#2051,1.);
#1198=VECTOR('',#2052,1.);
#1199=VECTOR('',#2057,1.);
#1200=VECTOR('',#2058,1.);
#1201=VECTOR('',#2059,1.);
#1202=VECTOR('',#2062,1.);
#1203=VECTOR('',#2067,1.);
#1204=VECTOR('',#2068,1.);
#1205=VECTOR('',#2071,1.);
#1206=VECTOR('',#2072,1.);
#1207=VECTOR('',#2077,1.);
#1208=VECTOR('',#2078,1.);
#1209=VECTOR('',#2079,1.);
#1210=VECTOR('',#2082,1.);
#1211=VECTOR('',#2087,1.);
#1212=VECTOR('',#2088,1.);
#1213=VECTOR('',#2091,1.);
#1214=VECTOR('',#2092,1.);
#1215=VECTOR('',#2097,1.);
#1216=VECTOR('',#2098,1.);
#1217=VECTOR('',#2099,1.);
#1218=VECTOR('',#2102,1.);
#1219=VECTOR('',#2107,1.);
#1220=VECTOR('',#2108,1.);
#1221=VECTOR('',#2111,1.);
#1222=VECTOR('',#2112,1.);
#1223=VECTOR('',#2117,1.);
#1224=VECTOR('',#2118,1.);
#1225=VECTOR('',#2119,1.);
#1226=VECTOR('',#2122,1.);
#1227=VECTOR('',#2127,1.);
#1228=VECTOR('',#2128,1.);
#1229=VECTOR('',#2131,1.);
#1230=VECTOR('',#2132,1.);
#1231=VECTOR('',#2137,1.);
#1232=VECTOR('',#2138,1.);
#1233=VECTOR('',#2139,1.);
#1234=VECTOR('',#2142,1.);
#1235=VECTOR('',#2143,1.);
#1236=VECTOR('',#2144,1.);
#1237=VECTOR('',#2147,1.);
#1238=VECTOR('',#2148,1.);
#1239=VECTOR('',#2151,1.);
#1240=VECTOR('',#2152,1.);
#1241=VECTOR('',#2159,1.);
#1242=VECTOR('',#2160,1.);
#1243=VECTOR('',#2161,1.);
#1244=VECTOR('',#2164,1.);
#1245=VECTOR('',#2169,1.);
#1246=VECTOR('',#2170,1.);
#1247=VECTOR('',#2173,1.);
#1248=VECTOR('',#2178,1.);
#1249=VECTOR('',#2179,1.);
#1250=VECTOR('',#2180,1.);
#1251=VECTOR('',#2183,1.);
#1252=VECTOR('',#2184,1.);
#1253=VECTOR('',#2187,1.);
#1254=VECTOR('',#2188,1.);
#1255=VECTOR('',#2197,1.);
#1256=VECTOR('',#2198,1.);
#1257=VECTOR('',#2199,1.);
#1258=VECTOR('',#2202,1.);
#1259=VECTOR('',#2207,1.);
#1260=VECTOR('',#2208,1.);
#1261=VECTOR('',#2211,1.);
#1262=VECTOR('',#2216,1.);
#1263=VECTOR('',#2217,1.);
#1264=VECTOR('',#2218,1.);
#1265=VECTOR('',#2221,1.);
#1266=VECTOR('',#2222,1.);
#1267=VECTOR('',#2225,1.);
#1268=VECTOR('',#2230,1.);
#1269=VECTOR('',#2235,1.);
#1270=VECTOR('',#2236,1.);
#1271=VECTOR('',#2237,1.);
#1272=VECTOR('',#2240,1.);
#1273=VECTOR('',#2245,1.);
#1274=VECTOR('',#2246,1.);
#1275=VECTOR('',#2249,1.);
#1276=VECTOR('',#2254,1.);
#1277=VECTOR('',#2255,1.);
#1278=VECTOR('',#2256,1.);
#1279=VECTOR('',#2259,1.);
#1280=VECTOR('',#2260,1.);
#1281=VECTOR('',#2263,1.);
#1282=VECTOR('',#2268,1.);
#1283=VECTOR('',#2273,1.);
#1284=VECTOR('',#2274,1.);
#1285=VECTOR('',#2275,1.);
#1286=VECTOR('',#2278,1.);
#1287=VECTOR('',#2279,1.);
#1288=VECTOR('',#2282,1.);
#1289=VECTOR('',#2283,1.);
#1290=VECTOR('',#2286,1.);
#1291=EDGE_LOOP('',(#63,#64,#65,#66));
#1292=EDGE_LOOP('',(#67,#68,#69,#70));
#1293=EDGE_LOOP('',(#71,#72,#73,#74));
#1294=EDGE_LOOP('',(#75,#76,#77,#78,#79,#80,#81,#82,#83,#84,#85,#86,#87,
#88,#89,#90,#91,#92,#93,#94,#95,#96,#97,#98,#99,#100,#101,#102,#103,#104,
#105,#106,#107,#108,#109,#110,#111,#112,#113,#114,#115,#116,#117,#118,#119,
#120,#121,#122,#123,#124,#125,#126));
#1295=EDGE_LOOP('',(#127,#128,#129,#130));
#1296=EDGE_LOOP('',(#131,#132,#133,#134));
#1297=EDGE_LOOP('',(#135,#136,#137,#138));
#1298=EDGE_LOOP('',(#139,#140,#141,#142));
#1299=EDGE_LOOP('',(#143,#144,#145,#146));
#1300=EDGE_LOOP('',(#147,#148,#149,#150));
#1301=EDGE_LOOP('',(#151,#152,#153,#154));
#1302=EDGE_LOOP('',(#155,#156,#157,#158));
#1303=EDGE_LOOP('',(#159,#160,#161,#162));
#1304=EDGE_LOOP('',(#163,#164,#165,#166));
#1305=EDGE_LOOP('',(#167,#168,#169,#170));
#1306=EDGE_LOOP('',(#171,#172,#173,#174));
#1307=EDGE_LOOP('',(#175,#176,#177,#178));
#1308=EDGE_LOOP('',(#179,#180,#181,#182));
#1309=EDGE_LOOP('',(#183,#184,#185,#186));
#1310=EDGE_LOOP('',(#187,#188,#189,#190));
#1311=EDGE_LOOP('',(#191,#192,#193,#194));
#1312=EDGE_LOOP('',(#195,#196,#197,#198));
#1313=EDGE_LOOP('',(#199,#200,#201,#202));
#1314=EDGE_LOOP('',(#203,#204,#205,#206));
#1315=EDGE_LOOP('',(#207,#208,#209,#210));
#1316=EDGE_LOOP('',(#211,#212,#213,#214));
#1317=EDGE_LOOP('',(#215,#216,#217,#218));
#1318=EDGE_LOOP('',(#219,#220,#221,#222));
#1319=EDGE_LOOP('',(#223,#224,#225,#226));
#1320=EDGE_LOOP('',(#227,#228,#229,#230));
#1321=EDGE_LOOP('',(#231,#232,#233,#234));
#1322=EDGE_LOOP('',(#235,#236,#237,#238));
#1323=EDGE_LOOP('',(#239,#240,#241,#242));
#1324=EDGE_LOOP('',(#243,#244,#245,#246));
#1325=EDGE_LOOP('',(#247,#248,#249,#250));
#1326=EDGE_LOOP('',(#251,#252,#253,#254));
#1327=EDGE_LOOP('',(#255,#256,#257,#258));
#1328=EDGE_LOOP('',(#259,#260,#261,#262));
#1329=EDGE_LOOP('',(#263,#264,#265,#266));
#1330=EDGE_LOOP('',(#267,#268,#269,#270));
#1331=EDGE_LOOP('',(#271,#272,#273,#274));
#1332=EDGE_LOOP('',(#275,#276,#277,#278));
#1333=EDGE_LOOP('',(#279,#280,#281,#282));
#1334=EDGE_LOOP('',(#283,#284,#285,#286));
#1335=EDGE_LOOP('',(#287,#288,#289,#290));
#1336=EDGE_LOOP('',(#291,#292,#293,#294));
#1337=EDGE_LOOP('',(#295,#296,#297,#298));
#1338=EDGE_LOOP('',(#299,#300,#301,#302));
#1339=EDGE_LOOP('',(#303,#304,#305,#306));
#1340=EDGE_LOOP('',(#307,#308,#309,#310));
#1341=EDGE_LOOP('',(#311,#312,#313,#314));
#1342=EDGE_LOOP('',(#315,#316,#317,#318));
#1343=EDGE_LOOP('',(#319,#320,#321,#322));
#1344=EDGE_LOOP('',(#323,#324,#325,#326));
#1345=EDGE_LOOP('',(#327,#328,#329,#330));
#1346=EDGE_LOOP('',(#331,#332,#333,#334));
#1347=EDGE_LOOP('',(#335,#336,#337,#338));
#1348=EDGE_LOOP('',(#339,#340,#341,#342,#343,#344,#345,#346,#347,#348,#349,
#350,#351,#352,#353,#354));
#1349=EDGE_LOOP('',(#355,#356,#357,#358));
#1350=EDGE_LOOP('',(#359,#360,#361,#362,#363,#364,#365,#366,#367,#368,#369,
#370,#371,#372,#373,#374));
#1351=EDGE_LOOP('',(#375));
#1352=EDGE_LOOP('',(#376,#377,#378,#379));
#1353=EDGE_LOOP('',(#380,#381,#382,#383));
#1354=EDGE_LOOP('',(#384,#385,#386,#387));
#1355=EDGE_LOOP('',(#388,#389,#390,#391));
#1356=EDGE_LOOP('',(#392,#393,#394,#395));
#1357=EDGE_LOOP('',(#396,#397,#398,#399));
#1358=EDGE_LOOP('',(#400,#401,#402,#403));
#1359=EDGE_LOOP('',(#404,#405,#406,#407));
#1360=EDGE_LOOP('',(#408,#409,#410,#411));
#1361=EDGE_LOOP('',(#412,#413,#414,#415));
#1362=EDGE_LOOP('',(#416,#417,#418,#419));
#1363=EDGE_LOOP('',(#420,#421,#422,#423));
#1364=EDGE_LOOP('',(#424,#425,#426,#427));
#1365=EDGE_LOOP('',(#428,#429,#430,#431));
#1366=EDGE_LOOP('',(#432,#433,#434,#435));
#1367=EDGE_LOOP('',(#436,#437,#438,#439));
#1368=EDGE_LOOP('',(#440,#441,#442,#443));
#1369=EDGE_LOOP('',(#444,#445,#446,#447));
#1370=EDGE_LOOP('',(#448,#449,#450,#451));
#1371=EDGE_LOOP('',(#452,#453,#454,#455));
#1372=EDGE_LOOP('',(#456,#457,#458,#459));
#1373=EDGE_LOOP('',(#460,#461,#462,#463));
#1374=EDGE_LOOP('',(#464,#465,#466,#467));
#1375=EDGE_LOOP('',(#468,#469,#470,#471));
#1376=EDGE_LOOP('',(#472,#473,#474,#475));
#1377=EDGE_LOOP('',(#476,#477,#478,#479));
#1378=EDGE_LOOP('',(#480,#481,#482,#483));
#1379=EDGE_LOOP('',(#484,#485,#486,#487));
#1380=EDGE_LOOP('',(#488,#489,#490,#491));
#1381=EDGE_LOOP('',(#492,#493,#494,#495));
#1382=EDGE_LOOP('',(#496,#497,#498,#499));
#1383=EDGE_LOOP('',(#500,#501,#502,#503));
#1384=EDGE_LOOP('',(#504,#505,#506,#507));
#1385=EDGE_LOOP('',(#508,#509,#510,#511));
#1386=EDGE_LOOP('',(#512,#513,#514,#515));
#1387=EDGE_LOOP('',(#516,#517,#518,#519));
#1388=EDGE_LOOP('',(#520));
#1389=EDGE_LOOP('',(#521));
#1390=EDGE_LOOP('',(#522));
#1391=FACE_BOUND('',#1291,.T.);
#1392=FACE_BOUND('',#1292,.T.);
#1393=FACE_BOUND('',#1293,.T.);
#1394=FACE_BOUND('',#1294,.T.);
#1395=FACE_BOUND('',#1295,.T.);
#1396=FACE_BOUND('',#1296,.T.);
#1397=FACE_BOUND('',#1297,.T.);
#1398=FACE_BOUND('',#1298,.T.);
#1399=FACE_BOUND('',#1299,.T.);
#1400=FACE_BOUND('',#1300,.T.);
#1401=FACE_BOUND('',#1301,.T.);
#1402=FACE_BOUND('',#1302,.T.);
#1403=FACE_BOUND('',#1303,.T.);
#1404=FACE_BOUND('',#1304,.T.);
#1405=FACE_BOUND('',#1305,.T.);
#1406=FACE_BOUND('',#1306,.T.);
#1407=FACE_BOUND('',#1307,.T.);
#1408=FACE_BOUND('',#1308,.T.);
#1409=FACE_BOUND('',#1309,.T.);
#1410=FACE_BOUND('',#1310,.T.);
#1411=FACE_BOUND('',#1311,.T.);
#1412=FACE_BOUND('',#1312,.T.);
#1413=FACE_BOUND('',#1313,.T.);
#1414=FACE_BOUND('',#1314,.T.);
#1415=FACE_BOUND('',#1315,.T.);
#1416=FACE_BOUND('',#1316,.T.);
#1417=FACE_BOUND('',#1317,.T.);
#1418=FACE_BOUND('',#1318,.T.);
#1419=FACE_BOUND('',#1319,.T.);
#1420=FACE_BOUND('',#1320,.T.);
#1421=FACE_BOUND('',#1321,.T.);
#1422=FACE_BOUND('',#1322,.T.);
#1423=FACE_BOUND('',#1323,.T.);
#1424=FACE_BOUND('',#1324,.T.);
#1425=FACE_BOUND('',#1325,.T.);
#1426=FACE_BOUND('',#1326,.T.);
#1427=FACE_BOUND('',#1327,.T.);
#1428=FACE_BOUND('',#1328,.T.);
#1429=FACE_BOUND('',#1329,.T.);
#1430=FACE_BOUND('',#1330,.T.);
#1431=FACE_BOUND('',#1331,.T.);
#1432=FACE_BOUND('',#1332,.T.);
#1433=FACE_BOUND('',#1333,.T.);
#1434=FACE_BOUND('',#1334,.T.);
#1435=FACE_BOUND('',#1335,.T.);
#1436=FACE_BOUND('',#1336,.T.);
#1437=FACE_BOUND('',#1337,.T.);
#1438=FACE_BOUND('',#1338,.T.);
#1439=FACE_BOUND('',#1339,.T.);
#1440=FACE_BOUND('',#1340,.T.);
#1441=FACE_BOUND('',#1341,.T.);
#1442=FACE_BOUND('',#1342,.T.);
#1443=FACE_BOUND('',#1343,.T.);
#1444=FACE_BOUND('',#1344,.T.);
#1445=FACE_BOUND('',#1345,.T.);
#1446=FACE_BOUND('',#1346,.T.);
#1447=FACE_BOUND('',#1347,.T.);
#1448=FACE_BOUND('',#1348,.T.);
#1449=FACE_BOUND('',#1349,.T.);
#1450=FACE_BOUND('',#1350,.T.);
#1451=FACE_BOUND('',#1351,.T.);
#1452=FACE_BOUND('',#1352,.T.);
#1453=FACE_BOUND('',#1353,.T.);
#1454=FACE_BOUND('',#1354,.T.);
#1455=FACE_BOUND('',#1355,.T.);
#1456=FACE_BOUND('',#1356,.T.);
#1457=FACE_BOUND('',#1357,.T.);
#1458=FACE_BOUND('',#1358,.T.);
#1459=FACE_BOUND('',#1359,.T.);
#1460=FACE_BOUND('',#1360,.T.);
#1461=FACE_BOUND('',#1361,.T.);
#1462=FACE_BOUND('',#1362,.T.);
#1463=FACE_BOUND('',#1363,.T.);
#1464=FACE_BOUND('',#1364,.T.);
#1465=FACE_BOUND('',#1365,.T.);
#1466=FACE_BOUND('',#1366,.T.);
#1467=FACE_BOUND('',#1367,.T.);
#1468=FACE_BOUND('',#1368,.T.);
#1469=FACE_BOUND('',#1369,.T.);
#1470=FACE_BOUND('',#1370,.T.);
#1471=FACE_BOUND('',#1371,.T.);
#1472=FACE_BOUND('',#1372,.T.);
#1473=FACE_BOUND('',#1373,.T.);
#1474=FACE_BOUND('',#1374,.T.);
#1475=FACE_BOUND('',#1375,.T.);
#1476=FACE_BOUND('',#1376,.T.);
#1477=FACE_BOUND('',#1377,.T.);
#1478=FACE_BOUND('',#1378,.T.);
#1479=FACE_BOUND('',#1379,.T.);
#1480=FACE_BOUND('',#1380,.T.);
#1481=FACE_BOUND('',#1381,.T.);
#1482=FACE_BOUND('',#1382,.T.);
#1483=FACE_BOUND('',#1383,.T.);
#1484=FACE_BOUND('',#1384,.T.);
#1485=FACE_BOUND('',#1385,.T.);
#1486=FACE_BOUND('',#1386,.T.);
#1487=FACE_BOUND('',#1387,.T.);
#1488=FACE_BOUND('',#1388,.T.);
#1489=FACE_BOUND('',#1389,.T.);
#1490=FACE_BOUND('',#1390,.T.);
#1491=PLANE('',#1716);
#1492=PLANE('',#1733);
#1493=PLANE('',#1736);
#1494=PLANE('',#1737);
#1495=PLANE('',#1738);
#1496=PLANE('',#1739);
#1497=PLANE('',#1742);
#1498=PLANE('',#1743);
#1499=PLANE('',#1744);
#1500=PLANE('',#1745);
#1501=PLANE('',#1748);
#1502=PLANE('',#1749);
#1503=PLANE('',#1750);
#1504=PLANE('',#1751);
#1505=PLANE('',#1754);
#1506=PLANE('',#1755);
#1507=PLANE('',#1756);
#1508=PLANE('',#1757);
#1509=PLANE('',#1760);
#1510=PLANE('',#1761);
#1511=PLANE('',#1762);
#1512=PLANE('',#1763);
#1513=PLANE('',#1766);
#1514=PLANE('',#1767);
#1515=PLANE('',#1768);
#1516=PLANE('',#1769);
#1517=PLANE('',#1772);
#1518=PLANE('',#1773);
#1519=PLANE('',#1774);
#1520=PLANE('',#1775);
#1521=PLANE('',#1778);
#1522=PLANE('',#1779);
#1523=PLANE('',#1780);
#1524=PLANE('',#1781);
#1525=PLANE('',#1784);
#1526=PLANE('',#1785);
#1527=PLANE('',#1786);
#1528=PLANE('',#1787);
#1529=PLANE('',#1790);
#1530=PLANE('',#1791);
#1531=PLANE('',#1792);
#1532=PLANE('',#1793);
#1533=PLANE('',#1794);
#1534=PLANE('',#1795);
#1535=PLANE('',#1796);
#1536=PLANE('',#1797);
#1537=PLANE('',#1799);
#1538=PLANE('',#1802);
#1539=PLANE('',#1803);
#1540=PLANE('',#1804);
#1541=PLANE('',#1805);
#1542=PLANE('',#1806);
#1543=PLANE('',#1807);
#1544=PLANE('',#1810);
#1545=PLANE('',#1811);
#1546=PLANE('',#1814);
#1547=PLANE('',#1815);
#1548=PLANE('',#1816);
#1549=PLANE('',#1817);
#1550=PLANE('',#1818);
#1551=PLANE('',#1821);
#1552=PLANE('',#1822);
#1553=PLANE('',#1823);
#1554=PLANE('',#1826);
#1555=PLANE('',#1827);
#1556=PLANE('',#1828);
#1557=PLANE('',#1829);
#1558=PLANE('',#1830);
#1559=PLANE('',#1833);
#1560=PLANE('',#1834);
#1561=PLANE('',#1835);
#1562=PLANE('',#1836);
#1563=PLANE('',#1837);
#1564=PLANE('',#1838);
#1565=PLANE('',#1839);
#1566=PLANE('',#1842);
#1567=OVER_RIDING_STYLED_ITEM('',(#1669),#1573,#1667);
#1568=OVER_RIDING_STYLED_ITEM('',(#1670),#1624,#1667);
#1569=OVER_RIDING_STYLED_ITEM('',(#1671),#1625,#1667);
#1570=OVER_RIDING_STYLED_ITEM('',(#1672),#1626,#1667);
#1571=OVER_RIDING_STYLED_ITEM('',(#1673),#1627,#1667);
#1572=OVER_RIDING_STYLED_ITEM('',(#1674),#1628,#1667);
#1573=ADVANCED_FACE('',(#1391,#1392,#1393,#1394,#1395,#1396),#1491,.F.);
#1574=ADVANCED_FACE('',(#1397),#1492,.F.);
#1575=ADVANCED_FACE('',(#1398),#12,.T.);
#1576=ADVANCED_FACE('',(#1399),#1493,.T.);
#1577=ADVANCED_FACE('',(#1400),#1494,.F.);
#1578=ADVANCED_FACE('',(#1401),#1495,.T.);
#1579=ADVANCED_FACE('',(#1402),#1496,.F.);
#1580=ADVANCED_FACE('',(#1403),#13,.T.);
#1581=ADVANCED_FACE('',(#1404),#1497,.T.);
#1582=ADVANCED_FACE('',(#1405),#1498,.F.);
#1583=ADVANCED_FACE('',(#1406),#1499,.T.);
#1584=ADVANCED_FACE('',(#1407),#1500,.F.);
#1585=ADVANCED_FACE('',(#1408),#14,.T.);
#1586=ADVANCED_FACE('',(#1409),#1501,.T.);
#1587=ADVANCED_FACE('',(#1410),#1502,.F.);
#1588=ADVANCED_FACE('',(#1411),#1503,.T.);
#1589=ADVANCED_FACE('',(#1412),#1504,.F.);
#1590=ADVANCED_FACE('',(#1413),#15,.T.);
#1591=ADVANCED_FACE('',(#1414),#1505,.T.);
#1592=ADVANCED_FACE('',(#1415),#1506,.F.);
#1593=ADVANCED_FACE('',(#1416),#1507,.T.);
#1594=ADVANCED_FACE('',(#1417),#1508,.F.);
#1595=ADVANCED_FACE('',(#1418),#16,.T.);
#1596=ADVANCED_FACE('',(#1419),#1509,.T.);
#1597=ADVANCED_FACE('',(#1420),#1510,.F.);
#1598=ADVANCED_FACE('',(#1421),#1511,.T.);
#1599=ADVANCED_FACE('',(#1422),#1512,.T.);
#1600=ADVANCED_FACE('',(#1423),#17,.T.);
#1601=ADVANCED_FACE('',(#1424),#1513,.F.);
#1602=ADVANCED_FACE('',(#1425),#1514,.T.);
#1603=ADVANCED_FACE('',(#1426),#1515,.T.);
#1604=ADVANCED_FACE('',(#1427),#1516,.T.);
#1605=ADVANCED_FACE('',(#1428),#18,.T.);
#1606=ADVANCED_FACE('',(#1429),#1517,.F.);
#1607=ADVANCED_FACE('',(#1430),#1518,.T.);
#1608=ADVANCED_FACE('',(#1431),#1519,.T.);
#1609=ADVANCED_FACE('',(#1432),#1520,.T.);
#1610=ADVANCED_FACE('',(#1433),#19,.T.);
#1611=ADVANCED_FACE('',(#1434),#1521,.F.);
#1612=ADVANCED_FACE('',(#1435),#1522,.T.);
#1613=ADVANCED_FACE('',(#1436),#1523,.T.);
#1614=ADVANCED_FACE('',(#1437),#1524,.T.);
#1615=ADVANCED_FACE('',(#1438),#20,.T.);
#1616=ADVANCED_FACE('',(#1439),#1525,.F.);
#1617=ADVANCED_FACE('',(#1440),#1526,.T.);
#1618=ADVANCED_FACE('',(#1441),#1527,.T.);
#1619=ADVANCED_FACE('',(#1442),#1528,.T.);
#1620=ADVANCED_FACE('',(#1443),#21,.T.);
#1621=ADVANCED_FACE('',(#1444),#1529,.F.);
#1622=ADVANCED_FACE('',(#1445),#1530,.T.);
#1623=ADVANCED_FACE('',(#1446),#1531,.T.);
#1624=ADVANCED_FACE('',(#1447),#1532,.T.);
#1625=ADVANCED_FACE('',(#1448),#1533,.F.);
#1626=ADVANCED_FACE('',(#1449),#1534,.F.);
#1627=ADVANCED_FACE('',(#1450),#1535,.T.);
#1628=ADVANCED_FACE('',(#1451,#1452),#1536,.T.);
#1629=ADVANCED_FACE('',(#1453),#1537,.T.);
#1630=ADVANCED_FACE('',(#1454),#22,.T.);
#1631=ADVANCED_FACE('',(#1455),#1538,.F.);
#1632=ADVANCED_FACE('',(#1456),#1539,.T.);
#1633=ADVANCED_FACE('',(#1457),#1540,.T.);
#1634=ADVANCED_FACE('',(#1458),#1541,.F.);
#1635=ADVANCED_FACE('',(#1459),#1542,.F.);
#1636=ADVANCED_FACE('',(#1460),#1543,.T.);
#1637=ADVANCED_FACE('',(#1461),#23,.T.);
#1638=ADVANCED_FACE('',(#1462),#1544,.T.);
#1639=ADVANCED_FACE('',(#1463),#1545,.F.);
#1640=ADVANCED_FACE('',(#1464),#24,.T.);
#1641=ADVANCED_FACE('',(#1465),#1546,.T.);
#1642=ADVANCED_FACE('',(#1466),#1547,.T.);
#1643=ADVANCED_FACE('',(#1467),#1548,.T.);
#1644=ADVANCED_FACE('',(#1468),#1549,.F.);
#1645=ADVANCED_FACE('',(#1469),#1550,.F.);
#1646=ADVANCED_FACE('',(#1470),#25,.T.);
#1647=ADVANCED_FACE('',(#1471),#1551,.T.);
#1648=ADVANCED_FACE('',(#1472),#1552,.T.);
#1649=ADVANCED_FACE('',(#1473),#1553,.F.);
#1650=ADVANCED_FACE('',(#1474),#26,.T.);
#1651=ADVANCED_FACE('',(#1475),#1554,.T.);
#1652=ADVANCED_FACE('',(#1476),#1555,.T.);
#1653=ADVANCED_FACE('',(#1477),#1556,.T.);
#1654=ADVANCED_FACE('',(#1478),#1557,.F.);
#1655=ADVANCED_FACE('',(#1479),#1558,.F.);
#1656=ADVANCED_FACE('',(#1480),#27,.T.);
#1657=ADVANCED_FACE('',(#1481),#1559,.T.);
#1658=ADVANCED_FACE('',(#1482),#1560,.T.);
#1659=ADVANCED_FACE('',(#1483),#1561,.T.);
#1660=ADVANCED_FACE('',(#1484),#1562,.F.);
#1661=ADVANCED_FACE('',(#1485),#1563,.F.);
#1662=ADVANCED_FACE('',(#1486),#1564,.T.);
#1663=ADVANCED_FACE('',(#1487),#1565,.T.);
#1664=ADVANCED_FACE('',(#1488,#1489),#28,.F.);
#1665=ADVANCED_FACE('',(#1490),#1566,.T.);
#1666=CLOSED_SHELL('',(#1573,#1574,#1575,#1576,#1577,#1578,#1579,#1580,
#1581,#1582,#1583,#1584,#1585,#1586,#1587,#1588,#1589,#1590,#1591,#1592,
#1593,#1594,#1595,#1596,#1597,#1598,#1599,#1600,#1601,#1602,#1603,#1604,
#1605,#1606,#1607,#1608,#1609,#1610,#1611,#1612,#1613,#1614,#1615,#1616,
#1617,#1618,#1619,#1620,#1621,#1622,#1623,#1624,#1625,#1626,#1627,#1628,
#1629,#1630,#1631,#1632,#1633,#1634,#1635,#1636,#1637,#1638,#1639,#1640,
#1641,#1642,#1643,#1644,#1645,#1646,#1647,#1648,#1649,#1650,#1651,#1652,
#1653,#1654,#1655,#1656,#1657,#1658,#1659,#1660,#1661,#1662,#1663,#1664,
#1665));
#1667=STYLED_ITEM('',(#1668),#1712);
#1668=PRESENTATION_STYLE_ASSIGNMENT((#1675));
#1669=PRESENTATION_STYLE_ASSIGNMENT((#1676));
#1670=PRESENTATION_STYLE_ASSIGNMENT((#1677));
#1671=PRESENTATION_STYLE_ASSIGNMENT((#1678));
#1672=PRESENTATION_STYLE_ASSIGNMENT((#1679));
#1673=PRESENTATION_STYLE_ASSIGNMENT((#1680));
#1674=PRESENTATION_STYLE_ASSIGNMENT((#1681));
#1675=SURFACE_STYLE_USAGE(.BOTH.,#1682);
#1676=SURFACE_STYLE_USAGE(.BOTH.,#1683);
#1677=SURFACE_STYLE_USAGE(.BOTH.,#1684);
#1678=SURFACE_STYLE_USAGE(.BOTH.,#1685);
#1679=SURFACE_STYLE_USAGE(.BOTH.,#1686);
#1680=SURFACE_STYLE_USAGE(.BOTH.,#1687);
#1681=SURFACE_STYLE_USAGE(.BOTH.,#1688);
#1682=SURFACE_SIDE_STYLE('',(#1689));
#1683=SURFACE_SIDE_STYLE('',(#1690));
#1684=SURFACE_SIDE_STYLE('',(#1691));
#1685=SURFACE_SIDE_STYLE('',(#1692));
#1686=SURFACE_SIDE_STYLE('',(#1693));
#1687=SURFACE_SIDE_STYLE('',(#1694));
#1688=SURFACE_SIDE_STYLE('',(#1695));
#1689=SURFACE_STYLE_FILL_AREA(#1696);
#1690=SURFACE_STYLE_FILL_AREA(#1697);
#1691=SURFACE_STYLE_FILL_AREA(#1698);
#1692=SURFACE_STYLE_FILL_AREA(#1699);
#1693=SURFACE_STYLE_FILL_AREA(#1700);
#1694=SURFACE_STYLE_FILL_AREA(#1701);
#1695=SURFACE_STYLE_FILL_AREA(#1702);
#1696=FILL_AREA_STYLE('',(#1703));
#1697=FILL_AREA_STYLE('',(#1704));
#1698=FILL_AREA_STYLE('',(#1705));
#1699=FILL_AREA_STYLE('',(#1706));
#1700=FILL_AREA_STYLE('',(#1707));
#1701=FILL_AREA_STYLE('',(#1708));
#1702=FILL_AREA_STYLE('',(#1709));
#1703=FILL_AREA_STYLE_COLOUR('',#1710);
#1704=FILL_AREA_STYLE_COLOUR('',#1711);
#1705=FILL_AREA_STYLE_COLOUR('',#1711);
#1706=FILL_AREA_STYLE_COLOUR('',#1711);
#1707=FILL_AREA_STYLE_COLOUR('',#1711);
#1708=FILL_AREA_STYLE_COLOUR('',#1711);
#1709=FILL_AREA_STYLE_COLOUR('',#1711);
#1710=COLOUR_RGB('',0.823529411764706,0.819607843137255,0.780392156862745);
#1711=COLOUR_RGB('',0.149019607843137,0.145098039215686,0.145098039215686);
#1712=MANIFOLD_SOLID_BREP('Part 1',#1666);
#1713=SHAPE_DEFINITION_REPRESENTATION(#2771,#1714);
#1714=SHAPE_REPRESENTATION('Part 1',(#1715),#2766);
#1715=AXIS2_PLACEMENT_3D('',#2295,#1843,#1844);
#1716=AXIS2_PLACEMENT_3D('',#2296,#1845,#1846);
#1717=AXIS2_PLACEMENT_3D('',#2312,#1854,#1855);
#1718=AXIS2_PLACEMENT_3D('',#2320,#1859,#1860);
#1719=AXIS2_PLACEMENT_3D('',#2326,#1863,#1864);
#1720=AXIS2_PLACEMENT_3D('',#2338,#1870,#1871);
#1721=AXIS2_PLACEMENT_3D('',#2346,#1875,#1876);
#1722=AXIS2_PLACEMENT_3D('',#2354,#1880,#1881);
#1723=AXIS2_PLACEMENT_3D('',#2362,#1885,#1886);
#1724=AXIS2_PLACEMENT_3D('',#2370,#1890,#1891);
#1725=AXIS2_PLACEMENT_3D('',#2378,#1895,#1896);
#1726=AXIS2_PLACEMENT_3D('',#2390,#1902,#1903);
#1727=AXIS2_PLACEMENT_3D('',#2398,#1907,#1908);
#1728=AXIS2_PLACEMENT_3D('',#2406,#1912,#1913);
#1729=AXIS2_PLACEMENT_3D('',#2414,#1917,#1918);
#1730=AXIS2_PLACEMENT_3D('',#2422,#1922,#1923);
#1731=AXIS2_PLACEMENT_3D('',#2430,#1927,#1928);
#1732=AXIS2_PLACEMENT_3D('',#2438,#1932,#1933);
#1733=AXIS2_PLACEMENT_3D('',#2441,#1935,#1936);
#1734=AXIS2_PLACEMENT_3D('',#2447,#1940,#1941);
#1735=AXIS2_PLACEMENT_3D('',#2448,#1942,#1943);
#1736=AXIS2_PLACEMENT_3D('',#2451,#1945,#1946);
#1737=AXIS2_PLACEMENT_3D('',#2455,#1949,#1950);
#1738=AXIS2_PLACEMENT_3D('',#2458,#1953,#1954);
#1739=AXIS2_PLACEMENT_3D('',#2459,#1955,#1956);
#1740=AXIS2_PLACEMENT_3D('',#2465,#1960,#1961);
#1741=AXIS2_PLACEMENT_3D('',#2466,#1962,#1963);
#1742=AXIS2_PLACEMENT_3D('',#2469,#1965,#1966);
#1743=AXIS2_PLACEMENT_3D('',#2473,#1969,#1970);
#1744=AXIS2_PLACEMENT_3D('',#2476,#1973,#1974);
#1745=AXIS2_PLACEMENT_3D('',#2477,#1975,#1976);
#1746=AXIS2_PLACEMENT_3D('',#2483,#1980,#1981);
#1747=AXIS2_PLACEMENT_3D('',#2484,#1982,#1983);
#1748=AXIS2_PLACEMENT_3D('',#2487,#1985,#1986);
#1749=AXIS2_PLACEMENT_3D('',#2491,#1989,#1990);
#1750=AXIS2_PLACEMENT_3D('',#2494,#1993,#1994);
#1751=AXIS2_PLACEMENT_3D('',#2495,#1995,#1996);
#1752=AXIS2_PLACEMENT_3D('',#2501,#2000,#2001);
#1753=AXIS2_PLACEMENT_3D('',#2502,#2002,#2003);
#1754=AXIS2_PLACEMENT_3D('',#2505,#2005,#2006);
#1755=AXIS2_PLACEMENT_3D('',#2509,#2009,#2010);
#1756=AXIS2_PLACEMENT_3D('',#2512,#2013,#2014);
#1757=AXIS2_PLACEMENT_3D('',#2513,#2015,#2016);
#1758=AXIS2_PLACEMENT_3D('',#2519,#2020,#2021);
#1759=AXIS2_PLACEMENT_3D('',#2520,#2022,#2023);
#1760=AXIS2_PLACEMENT_3D('',#2523,#2025,#2026);
#1761=AXIS2_PLACEMENT_3D('',#2527,#2029,#2030);
#1762=AXIS2_PLACEMENT_3D('',#2530,#2033,#2034);
#1763=AXIS2_PLACEMENT_3D('',#2531,#2035,#2036);
#1764=AXIS2_PLACEMENT_3D('',#2537,#2040,#2041);
#1765=AXIS2_PLACEMENT_3D('',#2540,#2043,#2044);
#1766=AXIS2_PLACEMENT_3D('',#2541,#2045,#2046);
#1767=AXIS2_PLACEMENT_3D('',#2545,#2049,#2050);
#1768=AXIS2_PLACEMENT_3D('',#2548,#2053,#2054);
#1769=AXIS2_PLACEMENT_3D('',#2549,#2055,#2056);
#1770=AXIS2_PLACEMENT_3D('',#2555,#2060,#2061);
#1771=AXIS2_PLACEMENT_3D('',#2558,#2063,#2064);
#1772=AXIS2_PLACEMENT_3D('',#2559,#2065,#2066);
#1773=AXIS2_PLACEMENT_3D('',#2563,#2069,#2070);
#1774=AXIS2_PLACEMENT_3D('',#2566,#2073,#2074);
#1775=AXIS2_PLACEMENT_3D('',#2567,#2075,#2076);
#1776=AXIS2_PLACEMENT_3D('',#2573,#2080,#2081);
#1777=AXIS2_PLACEMENT_3D('',#2576,#2083,#2084);
#1778=AXIS2_PLACEMENT_3D('',#2577,#2085,#2086);
#1779=AXIS2_PLACEMENT_3D('',#2581,#2089,#2090);
#1780=AXIS2_PLACEMENT_3D('',#2584,#2093,#2094);
#1781=AXIS2_PLACEMENT_3D('',#2585,#2095,#2096);
#1782=AXIS2_PLACEMENT_3D('',#2591,#2100,#2101);
#1783=AXIS2_PLACEMENT_3D('',#2594,#2103,#2104);
#1784=AXIS2_PLACEMENT_3D('',#2595,#2105,#2106);
#1785=AXIS2_PLACEMENT_3D('',#2599,#2109,#2110);
#1786=AXIS2_PLACEMENT_3D('',#2602,#2113,#2114);
#1787=AXIS2_PLACEMENT_3D('',#2603,#2115,#2116);
#1788=AXIS2_PLACEMENT_3D('',#2609,#2120,#2121);
#1789=AXIS2_PLACEMENT_3D('',#2612,#2123,#2124);
#1790=AXIS2_PLACEMENT_3D('',#2613,#2125,#2126);
#1791=AXIS2_PLACEMENT_3D('',#2617,#2129,#2130);
#1792=AXIS2_PLACEMENT_3D('',#2620,#2133,#2134);
#1793=AXIS2_PLACEMENT_3D('',#2621,#2135,#2136);
#1794=AXIS2_PLACEMENT_3D('',#2627,#2140,#2141);
#1795=AXIS2_PLACEMENT_3D('',#2632,#2145,#2146);
#1796=AXIS2_PLACEMENT_3D('',#2636,#2149,#2150);
#1797=AXIS2_PLACEMENT_3D('',#2639,#2153,#2154);
#1798=AXIS2_PLACEMENT_3D('',#2640,#2155,#2156);
#1799=AXIS2_PLACEMENT_3D('',#2642,#2157,#2158);
#1800=AXIS2_PLACEMENT_3D('',#2648,#2162,#2163);
#1801=AXIS2_PLACEMENT_3D('',#2651,#2165,#2166);
#1802=AXIS2_PLACEMENT_3D('',#2652,#2167,#2168);
#1803=AXIS2_PLACEMENT_3D('',#2656,#2171,#2172);
#1804=AXIS2_PLACEMENT_3D('',#2658,#2174,#2175);
#1805=AXIS2_PLACEMENT_3D('',#2659,#2176,#2177);
#1806=AXIS2_PLACEMENT_3D('',#2665,#2181,#2182);
#1807=AXIS2_PLACEMENT_3D('',#2669,#2185,#2186);
#1808=AXIS2_PLACEMENT_3D('',#2673,#2189,#2190);
#1809=AXIS2_PLACEMENT_3D('',#2674,#2191,#2192);
#1810=AXIS2_PLACEMENT_3D('',#2675,#2193,#2194);
#1811=AXIS2_PLACEMENT_3D('',#2676,#2195,#2196);
#1812=AXIS2_PLACEMENT_3D('',#2682,#2200,#2201);
#1813=AXIS2_PLACEMENT_3D('',#2685,#2203,#2204);
#1814=AXIS2_PLACEMENT_3D('',#2686,#2205,#2206);
#1815=AXIS2_PLACEMENT_3D('',#2690,#2209,#2210);
#1816=AXIS2_PLACEMENT_3D('',#2692,#2212,#2213);
#1817=AXIS2_PLACEMENT_3D('',#2693,#2214,#2215);
#1818=AXIS2_PLACEMENT_3D('',#2699,#2219,#2220);
#1819=AXIS2_PLACEMENT_3D('',#2703,#2223,#2224);
#1820=AXIS2_PLACEMENT_3D('',#2706,#2226,#2227);
#1821=AXIS2_PLACEMENT_3D('',#2707,#2228,#2229);
#1822=AXIS2_PLACEMENT_3D('',#2709,#2231,#2232);
#1823=AXIS2_PLACEMENT_3D('',#2710,#2233,#2234);
#1824=AXIS2_PLACEMENT_3D('',#2716,#2238,#2239);
#1825=AXIS2_PLACEMENT_3D('',#2719,#2241,#2242);
#1826=AXIS2_PLACEMENT_3D('',#2720,#2243,#2244);
#1827=AXIS2_PLACEMENT_3D('',#2724,#2247,#2248);
#1828=AXIS2_PLACEMENT_3D('',#2726,#2250,#2251);
#1829=AXIS2_PLACEMENT_3D('',#2727,#2252,#2253);
#1830=AXIS2_PLACEMENT_3D('',#2733,#2257,#2258);
#1831=AXIS2_PLACEMENT_3D('',#2737,#2261,#2262);
#1832=AXIS2_PLACEMENT_3D('',#2740,#2264,#2265);
#1833=AXIS2_PLACEMENT_3D('',#2741,#2266,#2267);
#1834=AXIS2_PLACEMENT_3D('',#2743,#2269,#2270);
#1835=AXIS2_PLACEMENT_3D('',#2744,#2271,#2272);
#1836=AXIS2_PLACEMENT_3D('',#2750,#2276,#2277);
#1837=AXIS2_PLACEMENT_3D('',#2754,#2280,#2281);
#1838=AXIS2_PLACEMENT_3D('',#2758,#2284,#2285);
#1839=AXIS2_PLACEMENT_3D('',#2760,#2287,#2288);
#1840=AXIS2_PLACEMENT_3D('',#2761,#2289,#2290);
#1841=AXIS2_PLACEMENT_3D('',#2762,#2291,#2292);
#1842=AXIS2_PLACEMENT_3D('',#2764,#2293,#2294);
#1843=DIRECTION('',(0.,0.,1.));
#1844=DIRECTION('',(1.,0.,0.));
#1845=DIRECTION('',(0.,0.,1.));
#1846=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#1847=DIRECTION('',(-1.,1.50319303454438E-16,0.));
#1848=DIRECTION('',(1.08838589059966E-16,1.,0.));
#1849=DIRECTION('',(-1.,1.50319303454438E-16,0.));
#1850=DIRECTION('',(1.08838589059966E-16,1.,0.));
#1851=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#1852=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1853=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#1854=DIRECTION('',(0.,0.,-1.));
#1855=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1856=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1857=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1858=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1859=DIRECTION('',(0.,0.,-1.));
#1860=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1861=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1862=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1863=DIRECTION('',(0.,0.,1.));
#1864=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1865=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1866=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1867=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1868=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1869=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1870=DIRECTION('',(0.,0.,-1.));
#1871=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1872=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1873=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1874=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1875=DIRECTION('',(0.,0.,-1.));
#1876=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1877=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1878=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1879=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1880=DIRECTION('',(0.,0.,-1.));
#1881=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1882=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1883=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1884=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1885=DIRECTION('',(0.,0.,-1.));
#1886=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1887=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1888=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1889=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1890=DIRECTION('',(0.,0.,-1.));
#1891=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1892=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1893=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1894=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1895=DIRECTION('',(0.,0.,-1.));
#1896=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1897=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1898=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1899=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1900=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1901=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1902=DIRECTION('',(0.,0.,1.));
#1903=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1904=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1905=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1906=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1907=DIRECTION('',(0.,0.,1.));
#1908=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1909=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1910=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1911=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1912=DIRECTION('',(0.,0.,1.));
#1913=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1914=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1915=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1916=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1917=DIRECTION('',(0.,0.,1.));
#1918=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1919=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1920=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1921=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1922=DIRECTION('',(0.,0.,1.));
#1923=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1924=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1925=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1926=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1927=DIRECTION('',(0.,0.,1.));
#1928=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1929=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1930=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1931=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#1932=DIRECTION('',(0.,0.,1.));
#1933=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1934=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#1935=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1936=DIRECTION('',(0.,0.,1.));
#1937=DIRECTION('',(0.,0.,1.));
#1938=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1939=DIRECTION('',(0.,0.,1.));
#1940=DIRECTION('',(0.,0.,1.));
#1941=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#1942=DIRECTION('',(0.,0.,1.));
#1943=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1944=DIRECTION('',(0.,0.,1.));
#1945=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1946=DIRECTION('',(0.,0.,1.));
#1947=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1948=DIRECTION('',(0.,0.,1.));
#1949=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1950=DIRECTION('',(0.,0.,1.));
#1951=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1952=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1953=DIRECTION('',(0.,0.,-1.));
#1954=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#1955=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1956=DIRECTION('',(0.,0.,1.));
#1957=DIRECTION('',(0.,0.,1.));
#1958=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1959=DIRECTION('',(0.,0.,1.));
#1960=DIRECTION('',(0.,0.,1.));
#1961=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#1962=DIRECTION('',(0.,0.,1.));
#1963=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1964=DIRECTION('',(0.,0.,1.));
#1965=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1966=DIRECTION('',(0.,0.,1.));
#1967=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1968=DIRECTION('',(0.,0.,1.));
#1969=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1970=DIRECTION('',(0.,0.,1.));
#1971=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1972=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1973=DIRECTION('',(0.,0.,-1.));
#1974=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#1975=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1976=DIRECTION('',(0.,0.,1.));
#1977=DIRECTION('',(0.,0.,1.));
#1978=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1979=DIRECTION('',(0.,0.,1.));
#1980=DIRECTION('',(0.,0.,1.));
#1981=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#1982=DIRECTION('',(0.,0.,1.));
#1983=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1984=DIRECTION('',(0.,0.,1.));
#1985=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1986=DIRECTION('',(0.,0.,1.));
#1987=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1988=DIRECTION('',(0.,0.,1.));
#1989=DIRECTION('',(1.,6.12323399573677E-17,0.));
#1990=DIRECTION('',(0.,0.,1.));
#1991=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1992=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1993=DIRECTION('',(0.,0.,-1.));
#1994=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#1995=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#1996=DIRECTION('',(0.,0.,1.));
#1997=DIRECTION('',(0.,0.,1.));
#1998=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#1999=DIRECTION('',(0.,0.,1.));
#2000=DIRECTION('',(0.,0.,1.));
#2001=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2002=DIRECTION('',(0.,0.,1.));
#2003=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2004=DIRECTION('',(0.,0.,1.));
#2005=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2006=DIRECTION('',(0.,0.,1.));
#2007=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2008=DIRECTION('',(0.,0.,1.));
#2009=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2010=DIRECTION('',(0.,0.,1.));
#2011=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2012=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2013=DIRECTION('',(0.,0.,-1.));
#2014=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2015=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2016=DIRECTION('',(0.,0.,1.));
#2017=DIRECTION('',(0.,0.,1.));
#2018=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2019=DIRECTION('',(0.,0.,1.));
#2020=DIRECTION('',(0.,0.,1.));
#2021=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2022=DIRECTION('',(0.,0.,1.));
#2023=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2024=DIRECTION('',(0.,0.,1.));
#2025=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2026=DIRECTION('',(0.,0.,1.));
#2027=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2028=DIRECTION('',(0.,0.,1.));
#2029=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2030=DIRECTION('',(0.,0.,1.));
#2031=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2032=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2033=DIRECTION('',(0.,0.,-1.));
#2034=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2035=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2036=DIRECTION('',(0.,0.,-1.));
#2037=DIRECTION('',(0.,0.,1.));
#2038=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2039=DIRECTION('',(0.,0.,1.));
#2040=DIRECTION('',(0.,0.,1.));
#2041=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2042=DIRECTION('',(0.,0.,1.));
#2043=DIRECTION('',(0.,0.,-1.));
#2044=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2045=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2046=DIRECTION('',(0.,0.,-1.));
#2047=DIRECTION('',(0.,0.,1.));
#2048=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2049=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2050=DIRECTION('',(0.,0.,1.));
#2051=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2052=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2053=DIRECTION('',(0.,0.,-1.));
#2054=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2055=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2056=DIRECTION('',(0.,0.,-1.));
#2057=DIRECTION('',(0.,0.,1.));
#2058=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2059=DIRECTION('',(0.,0.,1.));
#2060=DIRECTION('',(0.,0.,1.));
#2061=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2062=DIRECTION('',(0.,0.,1.));
#2063=DIRECTION('',(0.,0.,-1.));
#2064=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2065=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2066=DIRECTION('',(0.,0.,-1.));
#2067=DIRECTION('',(0.,0.,1.));
#2068=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2069=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2070=DIRECTION('',(0.,0.,1.));
#2071=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2072=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2073=DIRECTION('',(0.,0.,-1.));
#2074=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2075=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2076=DIRECTION('',(0.,0.,-1.));
#2077=DIRECTION('',(0.,0.,1.));
#2078=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2079=DIRECTION('',(0.,0.,1.));
#2080=DIRECTION('',(0.,0.,1.));
#2081=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2082=DIRECTION('',(0.,0.,1.));
#2083=DIRECTION('',(0.,0.,-1.));
#2084=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2085=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2086=DIRECTION('',(0.,0.,-1.));
#2087=DIRECTION('',(0.,0.,1.));
#2088=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2089=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2090=DIRECTION('',(0.,0.,1.));
#2091=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2092=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2093=DIRECTION('',(0.,0.,-1.));
#2094=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2095=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2096=DIRECTION('',(0.,0.,-1.));
#2097=DIRECTION('',(0.,0.,1.));
#2098=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2099=DIRECTION('',(0.,0.,1.));
#2100=DIRECTION('',(0.,0.,1.));
#2101=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2102=DIRECTION('',(0.,0.,1.));
#2103=DIRECTION('',(0.,0.,-1.));
#2104=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2105=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2106=DIRECTION('',(0.,0.,-1.));
#2107=DIRECTION('',(0.,0.,1.));
#2108=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2109=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2110=DIRECTION('',(0.,0.,1.));
#2111=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2112=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2113=DIRECTION('',(0.,0.,-1.));
#2114=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2115=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2116=DIRECTION('',(0.,0.,-1.));
#2117=DIRECTION('',(0.,0.,1.));
#2118=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2119=DIRECTION('',(0.,0.,1.));
#2120=DIRECTION('',(0.,0.,1.));
#2121=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2122=DIRECTION('',(0.,0.,1.));
#2123=DIRECTION('',(0.,0.,-1.));
#2124=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2125=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2126=DIRECTION('',(0.,0.,-1.));
#2127=DIRECTION('',(0.,0.,1.));
#2128=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2129=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2130=DIRECTION('',(0.,0.,1.));
#2131=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2132=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2133=DIRECTION('',(0.,0.,-1.));
#2134=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2135=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2136=DIRECTION('',(0.,0.,-1.));
#2137=DIRECTION('',(0.,0.,-1.));
#2138=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2139=DIRECTION('',(0.,0.,-1.));
#2140=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2141=DIRECTION('',(0.,0.,-1.));
#2142=DIRECTION('',(0.,0.,-1.));
#2143=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2144=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2145=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2146=DIRECTION('',(0.,0.,-1.));
#2147=DIRECTION('',(0.,0.,-1.));
#2148=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2149=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2150=DIRECTION('',(0.,0.,-1.));
#2151=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2152=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2153=DIRECTION('',(0.,0.,1.));
#2154=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2155=DIRECTION('',(0.,0.,1.));
#2156=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2157=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2158=DIRECTION('',(0.,0.,-1.));
#2159=DIRECTION('',(0.,0.,1.));
#2160=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2161=DIRECTION('',(0.,0.,1.));
#2162=DIRECTION('',(0.,0.,1.));
#2163=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2164=DIRECTION('',(0.,0.,1.));
#2165=DIRECTION('',(0.,0.,-1.));
#2166=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2167=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2168=DIRECTION('',(0.,0.,-1.));
#2169=DIRECTION('',(0.,0.,1.));
#2170=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2171=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2172=DIRECTION('',(0.,0.,1.));
#2173=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2174=DIRECTION('',(0.,0.,-1.));
#2175=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2176=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2177=DIRECTION('',(0.,0.,1.));
#2178=DIRECTION('',(0.,0.,1.));
#2179=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2180=DIRECTION('',(0.,0.,1.));
#2181=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2182=DIRECTION('',(0.,0.,1.));
#2183=DIRECTION('',(0.,0.,1.));
#2184=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2185=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2186=DIRECTION('',(0.,0.,1.));
#2187=DIRECTION('',(0.,0.,1.));
#2188=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2189=DIRECTION('',(0.,0.,1.));
#2190=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2191=DIRECTION('',(0.,0.,1.));
#2192=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2193=DIRECTION('',(0.,0.,-1.));
#2194=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2195=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2196=DIRECTION('',(0.,0.,1.));
#2197=DIRECTION('',(0.,0.,1.));
#2198=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2199=DIRECTION('',(0.,0.,1.));
#2200=DIRECTION('',(0.,0.,1.));
#2201=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2202=DIRECTION('',(0.,0.,1.));
#2203=DIRECTION('',(0.,0.,-1.));
#2204=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2205=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2206=DIRECTION('',(0.,0.,1.));
#2207=DIRECTION('',(0.,0.,1.));
#2208=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2209=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2210=DIRECTION('',(0.,0.,-1.));
#2211=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2212=DIRECTION('',(0.,0.,-1.));
#2213=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2214=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2215=DIRECTION('',(0.,0.,1.));
#2216=DIRECTION('',(0.,0.,1.));
#2217=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2218=DIRECTION('',(0.,0.,1.));
#2219=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2220=DIRECTION('',(0.,0.,1.));
#2221=DIRECTION('',(0.,0.,1.));
#2222=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2223=DIRECTION('',(0.,0.,1.));
#2224=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2225=DIRECTION('',(0.,0.,1.));
#2226=DIRECTION('',(0.,0.,1.));
#2227=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2228=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2229=DIRECTION('',(0.,0.,1.));
#2230=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2231=DIRECTION('',(0.,0.,-1.));
#2232=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2233=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2234=DIRECTION('',(0.,0.,-1.));
#2235=DIRECTION('',(0.,0.,1.));
#2236=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2237=DIRECTION('',(0.,0.,1.));
#2238=DIRECTION('',(0.,0.,1.));
#2239=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2240=DIRECTION('',(0.,0.,1.));
#2241=DIRECTION('',(0.,0.,-1.));
#2242=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2243=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2244=DIRECTION('',(0.,0.,-1.));
#2245=DIRECTION('',(0.,0.,1.));
#2246=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2247=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2248=DIRECTION('',(0.,0.,1.));
#2249=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2250=DIRECTION('',(0.,0.,-1.));
#2251=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2252=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2253=DIRECTION('',(0.,0.,-1.));
#2254=DIRECTION('',(0.,0.,1.));
#2255=DIRECTION('',(1.,6.12323399573677E-17,0.));
#2256=DIRECTION('',(0.,0.,1.));
#2257=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2258=DIRECTION('',(0.,0.,-1.));
#2259=DIRECTION('',(0.,0.,1.));
#2260=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2261=DIRECTION('',(0.,0.,1.));
#2262=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2263=DIRECTION('',(0.,0.,1.));
#2264=DIRECTION('',(0.,0.,1.));
#2265=DIRECTION('',(-6.12323399573677E-17,1.,0.));
#2266=DIRECTION('',(-1.,-6.12323399573677E-17,0.));
#2267=DIRECTION('',(0.,0.,-1.));
#2268=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2269=DIRECTION('',(0.,0.,-1.));
#2270=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2271=DIRECTION('',(1.,-1.08838589059966E-16,0.));
#2272=DIRECTION('',(1.08838589059966E-16,1.,0.));
#2273=DIRECTION('',(0.,0.,1.));
#2274=DIRECTION('',(1.08838589059966E-16,1.,0.));
#2275=DIRECTION('',(0.,0.,1.));
#2276=DIRECTION('',(1.50319303454438E-16,1.,0.));
#2277=DIRECTION('',(-1.,1.50319303454438E-16,0.));
#2278=DIRECTION('',(0.,0.,1.));
#2279=DIRECTION('',(-1.,1.50319303454438E-16,0.));
#2280=DIRECTION('',(1.,-1.08838589059966E-16,0.));
#2281=DIRECTION('',(1.08838589059966E-16,1.,0.));
#2282=DIRECTION('',(0.,0.,1.));
#2283=DIRECTION('',(1.08838589059966E-16,1.,0.));
#2284=DIRECTION('',(1.50319303454438E-16,1.,0.));
#2285=DIRECTION('',(-1.,1.50319303454438E-16,0.));
#2286=DIRECTION('',(-1.,1.50319303454438E-16,0.));
#2287=DIRECTION('',(0.,0.,-1.));
#2288=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2289=DIRECTION('',(0.,0.,1.));
#2290=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2291=DIRECTION('',(0.,0.,1.));
#2292=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2293=DIRECTION('',(0.,0.,1.));
#2294=DIRECTION('',(6.12323399573677E-17,-1.,0.));
#2295=CARTESIAN_POINT('',(0.,0.,0.));
#2296=CARTESIAN_POINT('',(8.7964469722641E-7,1.14353810639606E-5,8.E-5));
#2297=CARTESIAN_POINT('',(8.79644697226271E-7,-0.00126356461893604,8.E-5));
#2298=CARTESIAN_POINT('',(0.00102587964469723,-0.00126356461893604,8.E-5));
#2299=CARTESIAN_POINT('',(-0.00102412035530277,-0.00126356461893604,8.E-5));
#2300=CARTESIAN_POINT('',(0.00102587964469723,1.14353810639595E-5,8.E-5));
#2301=CARTESIAN_POINT('',(0.00102587964469723,0.00128643538106396,8.E-5));
#2302=CARTESIAN_POINT('',(8.79644697226549E-7,0.00128643538106396,8.E-5));
#2303=CARTESIAN_POINT('',(-0.00102412035530277,0.00128643538106396,8.E-5));
#2304=CARTESIAN_POINT('',(-0.00102412035530277,1.14353810639596E-5,8.E-5));
#2305=CARTESIAN_POINT('',(0.000870879644697226,0.00186743538106396,8.E-5));
#2306=CARTESIAN_POINT('',(0.000870879644697226,0.00200343538106396,8.E-5));
#2307=CARTESIAN_POINT('',(0.000870879644697226,0.00173143538106396,8.E-5));
#2308=CARTESIAN_POINT('',(0.000750879644697226,0.00200343538106396,8.E-5));
#2309=CARTESIAN_POINT('',(0.000630879644697227,0.00200343538106396,8.E-5));
#2310=CARTESIAN_POINT('',(0.000630879644697226,0.00186743538106396,8.E-5));
#2311=CARTESIAN_POINT('',(0.000630879644697226,0.00173143538106396,8.E-5));
#2312=CARTESIAN_POINT('',(0.000750879644697226,0.00173143538106396,8.E-5));
#2313=CARTESIAN_POINT('',(-0.000869120355302773,-0.00184456461893604,8.E-5));
#2314=CARTESIAN_POINT('',(-0.000869120355302773,-0.00198056461893604,8.E-5));
#2315=CARTESIAN_POINT('',(-0.000869120355302773,-0.00170856461893604,8.E-5));
#2316=CARTESIAN_POINT('',(-0.000749120355302774,-0.00198056461893604,8.E-5));
#2317=CARTESIAN_POINT('',(-0.000629120355302774,-0.00198056461893604,8.E-5));
#2318=CARTESIAN_POINT('',(-0.000629120355302774,-0.00184456461893604,8.E-5));
#2319=CARTESIAN_POINT('',(-0.000629120355302774,-0.00170856461893604,8.E-5));
#2320=CARTESIAN_POINT('',(-0.000749120355302774,-0.00170856461893604,8.E-5));
#2321=CARTESIAN_POINT('',(-0.00174912035530277,1.143538106396E-5,8.E-5));
#2322=CARTESIAN_POINT('',(-0.00174862035530278,0.000881435381063959,8.E-5));
#2323=CARTESIAN_POINT('',(-0.00174862035530278,0.00114143538106396,8.E-5));
#2324=CARTESIAN_POINT('',(-0.00160862035530277,0.00114143538106396,8.E-5));
#2325=CARTESIAN_POINT('',(-0.00146912035530277,0.00114143538106396,8.E-5));
#2326=CARTESIAN_POINT('',(-0.00146912035530277,0.00126143538106396,8.E-5));
#2327=CARTESIAN_POINT('',(-0.00146912035530277,0.00138143538106396,8.E-5));
#2328=CARTESIAN_POINT('',(-0.00160862035530277,0.00138143538106396,8.E-5));
#2329=CARTESIAN_POINT('',(-0.00174862035530278,0.00138143538106396,8.E-5));
#2330=CARTESIAN_POINT('',(-0.00174912035530277,1.143538106396E-5,8.E-5));
#2331=CARTESIAN_POINT('',(-0.00174912035530277,0.00201143538106396,8.E-5));
#2332=CARTESIAN_POINT('',(8.79644697226721E-7,0.00201143538106396,8.E-5));
#2333=CARTESIAN_POINT('',(0.00175087964469722,0.00201143538106396,8.E-5));
#2334=CARTESIAN_POINT('',(0.00175087964469722,1.14353810639602E-5,8.E-5));
#2335=CARTESIAN_POINT('',(0.00175037964469723,0.00138143538106396,8.E-5));
#2336=CARTESIAN_POINT('',(0.00161037964469722,0.00138143538106396,8.E-5));
#2337=CARTESIAN_POINT('',(0.00147087964469723,0.00138143538106396,8.E-5));
#2338=CARTESIAN_POINT('',(0.00147087964469723,0.00126143538106396,8.E-5));
#2339=CARTESIAN_POINT('',(0.00147087964469723,0.00114143538106396,8.E-5));
#2340=CARTESIAN_POINT('',(0.00161037964469722,0.00114143538106396,8.E-5));
#2341=CARTESIAN_POINT('',(0.00175037964469723,0.00114143538106396,8.E-5));
#2342=CARTESIAN_POINT('',(0.00175087964469722,1.14353810639602E-5,8.E-5));
#2343=CARTESIAN_POINT('',(0.00175037964469723,0.000881435381063959,8.E-5));
#2344=CARTESIAN_POINT('',(0.00161037964469722,0.00088143538106396,8.E-5));
#2345=CARTESIAN_POINT('',(0.00147087964469723,0.00088143538106396,8.E-5));
#2346=CARTESIAN_POINT('',(0.00147087964469723,0.00076143538106396,8.E-5));
#2347=CARTESIAN_POINT('',(0.00147087964469723,0.00064143538106396,8.E-5));
#2348=CARTESIAN_POINT('',(0.00161037964469722,0.00064143538106396,8.E-5));
#2349=CARTESIAN_POINT('',(0.00175037964469723,0.000641435381063959,8.E-5));
#2350=CARTESIAN_POINT('',(0.00175087964469722,1.14353810639602E-5,8.E-5));
#2351=CARTESIAN_POINT('',(0.00175037964469723,0.000381435381063959,8.E-5));
#2352=CARTESIAN_POINT('',(0.00161037964469722,0.00038143538106396,8.E-5));
#2353=CARTESIAN_POINT('',(0.00147087964469723,0.00038143538106396,8.E-5));
#2354=CARTESIAN_POINT('',(0.00147087964469723,0.00026143538106396,8.E-5));
#2355=CARTESIAN_POINT('',(0.00147087964469723,0.00014143538106396,8.E-5));
#2356=CARTESIAN_POINT('',(0.00161037964469722,0.00014143538106396,8.E-5));
#2357=CARTESIAN_POINT('',(0.00175037964469723,0.000141435381063959,8.E-5));
#2358=CARTESIAN_POINT('',(0.00175087964469722,1.14353810639602E-5,8.E-5));
#2359=CARTESIAN_POINT('',(0.00175037964469723,-0.000118564618936041,8.E-5));
#2360=CARTESIAN_POINT('',(0.00161037964469722,-0.00011856461893604,8.E-5));
#2361=CARTESIAN_POINT('',(0.00147087964469723,-0.00011856461893604,8.E-5));
#2362=CARTESIAN_POINT('',(0.00147087964469723,-0.00023856461893604,8.E-5));
#2363=CARTESIAN_POINT('',(0.00147087964469723,-0.00035856461893604,8.E-5));
#2364=CARTESIAN_POINT('',(0.00161037964469722,-0.00035856461893604,8.E-5));
#2365=CARTESIAN_POINT('',(0.00175037964469723,-0.000358564618936041,8.E-5));
#2366=CARTESIAN_POINT('',(0.00175087964469722,1.14353810639602E-5,8.E-5));
#2367=CARTESIAN_POINT('',(0.00175037964469723,-0.000618564618936041,8.E-5));
#2368=CARTESIAN_POINT('',(0.00161037964469722,-0.00061856461893604,8.E-5));
#2369=CARTESIAN_POINT('',(0.00147087964469723,-0.00061856461893604,8.E-5));
#2370=CARTESIAN_POINT('',(0.00147087964469723,-0.00073856461893604,8.E-5));
#2371=CARTESIAN_POINT('',(0.00147087964469723,-0.00085856461893604,8.E-5));
#2372=CARTESIAN_POINT('',(0.00161037964469722,-0.00085856461893604,8.E-5));
#2373=CARTESIAN_POINT('',(0.00175037964469723,-0.000858564618936041,8.E-5));
#2374=CARTESIAN_POINT('',(0.00175087964469722,1.14353810639602E-5,8.E-5));
#2375=CARTESIAN_POINT('',(0.00175037964469723,-0.00111856461893604,8.E-5));
#2376=CARTESIAN_POINT('',(0.00161037964469722,-0.00111856461893604,8.E-5));
#2377=CARTESIAN_POINT('',(0.00147087964469723,-0.00111856461893604,8.E-5));
#2378=CARTESIAN_POINT('',(0.00147087964469723,-0.00123856461893604,8.E-5));
#2379=CARTESIAN_POINT('',(0.00147087964469723,-0.00135856461893604,8.E-5));
#2380=CARTESIAN_POINT('',(0.00161037964469722,-0.00135856461893604,8.E-5));
#2381=CARTESIAN_POINT('',(0.00175037964469723,-0.00135856461893604,8.E-5));
#2382=CARTESIAN_POINT('',(0.00175087964469722,1.14353810639602E-5,8.E-5));
#2383=CARTESIAN_POINT('',(0.00175087964469722,-0.00198856461893604,8.E-5));
#2384=CARTESIAN_POINT('',(8.79644697226749E-7,-0.00198856461893604,8.E-5));
#2385=CARTESIAN_POINT('',(-0.00174912035530277,-0.00198856461893604,8.E-5));
#2386=CARTESIAN_POINT('',(-0.00174912035530277,1.143538106396E-5,8.E-5));
#2387=CARTESIAN_POINT('',(-0.00174862035530278,-0.00135856461893604,8.E-5));
#2388=CARTESIAN_POINT('',(-0.00160862035530277,-0.00135856461893604,8.E-5));
#2389=CARTESIAN_POINT('',(-0.00146912035530277,-0.00135856461893604,8.E-5));
#2390=CARTESIAN_POINT('',(-0.00146912035530277,-0.00123856461893604,8.E-5));
#2391=CARTESIAN_POINT('',(-0.00146912035530277,-0.00111856461893604,8.E-5));
#2392=CARTESIAN_POINT('',(-0.00160862035530277,-0.00111856461893604,8.E-5));
#2393=CARTESIAN_POINT('',(-0.00174862035530278,-0.00111856461893604,8.E-5));
#2394=CARTESIAN_POINT('',(-0.00174912035530277,1.143538106396E-5,8.E-5));
#2395=CARTESIAN_POINT('',(-0.00174862035530278,-0.000858564618936041,8.E-5));
#2396=CARTESIAN_POINT('',(-0.00160862035530277,-0.00085856461893604,8.E-5));
#2397=CARTESIAN_POINT('',(-0.00146912035530277,-0.00085856461893604,8.E-5));
#2398=CARTESIAN_POINT('',(-0.00146912035530277,-0.000738564618936041,8.E-5));
#2399=CARTESIAN_POINT('',(-0.00146912035530277,-0.00061856461893604,8.E-5));
#2400=CARTESIAN_POINT('',(-0.00160862035530277,-0.00061856461893604,8.E-5));
#2401=CARTESIAN_POINT('',(-0.00174862035530278,-0.000618564618936041,8.E-5));
#2402=CARTESIAN_POINT('',(-0.00174912035530277,1.143538106396E-5,8.E-5));
#2403=CARTESIAN_POINT('',(-0.00174862035530278,-0.000358564618936041,8.E-5));
#2404=CARTESIAN_POINT('',(-0.00160862035530277,-0.000358564618936041,8.E-5));
#2405=CARTESIAN_POINT('',(-0.00146912035530277,-0.000358564618936041,8.E-5));
#2406=CARTESIAN_POINT('',(-0.00146912035530277,-0.00023856461893604,8.E-5));
#2407=CARTESIAN_POINT('',(-0.00146912035530277,-0.00011856461893604,8.E-5));
#2408=CARTESIAN_POINT('',(-0.00160862035530277,-0.00011856461893604,8.E-5));
#2409=CARTESIAN_POINT('',(-0.00174862035530278,-0.00011856461893604,8.E-5));
#2410=CARTESIAN_POINT('',(-0.00174912035530277,1.143538106396E-5,8.E-5));
#2411=CARTESIAN_POINT('',(-0.00174862035530278,0.000141435381063959,8.E-5));
#2412=CARTESIAN_POINT('',(-0.00160862035530277,0.00014143538106396,8.E-5));
#2413=CARTESIAN_POINT('',(-0.00146912035530277,0.00014143538106396,8.E-5));
#2414=CARTESIAN_POINT('',(-0.00146912035530277,0.00026143538106396,8.E-5));
#2415=CARTESIAN_POINT('',(-0.00146912035530277,0.00038143538106396,8.E-5));
#2416=CARTESIAN_POINT('',(-0.00160862035530277,0.00038143538106396,8.E-5));
#2417=CARTESIAN_POINT('',(-0.00174862035530278,0.00038143538106396,8.E-5));
#2418=CARTESIAN_POINT('',(-0.00174912035530277,1.143538106396E-5,8.E-5));
#2419=CARTESIAN_POINT('',(-0.00174862035530278,0.00064143538106396,8.E-5));
#2420=CARTESIAN_POINT('',(-0.00160862035530277,0.00064143538106396,8.E-5));
#2421=CARTESIAN_POINT('',(-0.00146912035530277,0.00064143538106396,8.E-5));
#2422=CARTESIAN_POINT('',(-0.00146912035530277,0.00076143538106396,8.E-5));
#2423=CARTESIAN_POINT('',(-0.00146912035530277,0.00088143538106396,8.E-5));
#2424=CARTESIAN_POINT('',(-0.00160862035530277,0.00088143538106396,8.E-5));
#2425=CARTESIAN_POINT('',(0.000750879644697227,-0.00198056461893604,8.E-5));
#2426=CARTESIAN_POINT('',(0.000870879644697227,-0.00198056461893604,8.E-5));
#2427=CARTESIAN_POINT('',(0.000630879644697227,-0.00198056461893604,8.E-5));
#2428=CARTESIAN_POINT('',(0.000870879644697227,-0.00184456461893604,8.E-5));
#2429=CARTESIAN_POINT('',(0.000870879644697227,-0.00170856461893604,8.E-5));
#2430=CARTESIAN_POINT('',(0.000750879644697227,-0.00170856461893604,8.E-5));
#2431=CARTESIAN_POINT('',(0.000630879644697227,-0.00170856461893604,8.E-5));
#2432=CARTESIAN_POINT('',(0.000630879644697227,-0.00184456461893604,8.E-5));
#2433=CARTESIAN_POINT('',(-0.000749120355302774,0.00200343538106396,8.E-5));
#2434=CARTESIAN_POINT('',(-0.000869120355302774,0.00200343538106396,8.E-5));
#2435=CARTESIAN_POINT('',(-0.000629120355302774,0.00200343538106396,8.E-5));
#2436=CARTESIAN_POINT('',(-0.000869120355302774,0.00186743538106396,8.E-5));
#2437=CARTESIAN_POINT('',(-0.000869120355302774,0.00173143538106396,8.E-5));
#2438=CARTESIAN_POINT('',(-0.000749120355302774,0.00173143538106396,8.E-5));
#2439=CARTESIAN_POINT('',(-0.000629120355302774,0.00173143538106396,8.E-5));
#2440=CARTESIAN_POINT('',(-0.000629120355302774,0.00186743538106396,8.E-5));
#2441=CARTESIAN_POINT('',(-0.00160862035530277,0.00114143538106396,0.));
#2442=CARTESIAN_POINT('',(-0.00174812035530277,0.00114143538106396,0.));
#2443=CARTESIAN_POINT('',(-0.00174812035530277,0.00114143538106396,0.));
#2444=CARTESIAN_POINT('',(-0.00160862035530277,0.00114143538106396,0.));
#2445=CARTESIAN_POINT('',(-0.00146912035530277,0.00114143538106396,0.));
#2446=CARTESIAN_POINT('',(-0.00146912035530277,0.00114143538106396,0.));
#2447=CARTESIAN_POINT('',(-0.00146912035530277,0.00126143538106396,0.));
#2448=CARTESIAN_POINT('',(-0.00146912035530277,0.00126143538106396,0.));
#2449=CARTESIAN_POINT('',(-0.00146912035530277,0.00138143538106396,0.));
#2450=CARTESIAN_POINT('',(-0.00146912035530277,0.00138143538106396,0.));
#2451=CARTESIAN_POINT('',(-0.00160862035530277,0.00138143538106396,0.));
#2452=CARTESIAN_POINT('',(-0.00160862035530277,0.00138143538106396,0.));
#2453=CARTESIAN_POINT('',(-0.00174812035530277,0.00138143538106396,0.));
#2454=CARTESIAN_POINT('',(-0.00174812035530277,0.00138143538106396,0.));
#2455=CARTESIAN_POINT('',(-0.00174812035530277,0.00126143538106396,0.));
#2456=CARTESIAN_POINT('',(-0.00174812035530277,0.00126143538106396,8.E-5));
#2457=CARTESIAN_POINT('',(-0.00174812035530277,0.00126143538106396,0.));
#2458=CARTESIAN_POINT('',(8.79644697226257E-7,0.00251143538106396,0.));
#2459=CARTESIAN_POINT('',(-0.00160862035530277,0.00064143538106396,0.));
#2460=CARTESIAN_POINT('',(-0.00174812035530277,0.000641435381063959,0.));
#2461=CARTESIAN_POINT('',(-0.00174812035530277,0.000641435381063959,0.));
#2462=CARTESIAN_POINT('',(-0.00160862035530277,0.00064143538106396,0.));
#2463=CARTESIAN_POINT('',(-0.00146912035530277,0.00064143538106396,0.));
#2464=CARTESIAN_POINT('',(-0.00146912035530277,0.00064143538106396,0.));
#2465=CARTESIAN_POINT('',(-0.00146912035530277,0.00076143538106396,0.));
#2466=CARTESIAN_POINT('',(-0.00146912035530277,0.00076143538106396,0.));
#2467=CARTESIAN_POINT('',(-0.00146912035530277,0.00088143538106396,0.));
#2468=CARTESIAN_POINT('',(-0.00146912035530277,0.00088143538106396,0.));
#2469=CARTESIAN_POINT('',(-0.00160862035530277,0.00088143538106396,0.));
#2470=CARTESIAN_POINT('',(-0.00160862035530277,0.00088143538106396,0.));
#2471=CARTESIAN_POINT('',(-0.00174812035530277,0.000881435381063959,0.));
#2472=CARTESIAN_POINT('',(-0.00174812035530277,0.000881435381063959,0.));
#2473=CARTESIAN_POINT('',(-0.00174812035530277,0.000761435381063959,0.));
#2474=CARTESIAN_POINT('',(-0.00174812035530277,0.000761435381063959,8.E-5));
#2475=CARTESIAN_POINT('',(-0.00174812035530277,0.000761435381063959,0.));
#2476=CARTESIAN_POINT('',(8.79644697226288E-7,0.00201143538106396,0.));
#2477=CARTESIAN_POINT('',(-0.00160862035530277,0.00014143538106396,0.));
#2478=CARTESIAN_POINT('',(-0.00174812035530277,0.000141435381063959,0.));
#2479=CARTESIAN_POINT('',(-0.00174812035530277,0.000141435381063959,0.));
#2480=CARTESIAN_POINT('',(-0.00160862035530277,0.00014143538106396,0.));
#2481=CARTESIAN_POINT('',(-0.00146912035530277,0.00014143538106396,0.));
#2482=CARTESIAN_POINT('',(-0.00146912035530277,0.00014143538106396,0.));
#2483=CARTESIAN_POINT('',(-0.00146912035530277,0.00026143538106396,0.));
#2484=CARTESIAN_POINT('',(-0.00146912035530277,0.00026143538106396,0.));
#2485=CARTESIAN_POINT('',(-0.00146912035530277,0.00038143538106396,0.));
#2486=CARTESIAN_POINT('',(-0.00146912035530277,0.00038143538106396,0.));
#2487=CARTESIAN_POINT('',(-0.00160862035530277,0.00038143538106396,0.));
#2488=CARTESIAN_POINT('',(-0.00160862035530277,0.00038143538106396,0.));
#2489=CARTESIAN_POINT('',(-0.00174812035530277,0.000381435381063959,0.));
#2490=CARTESIAN_POINT('',(-0.00174812035530277,0.000381435381063959,0.));
#2491=CARTESIAN_POINT('',(-0.00174812035530277,0.000261435381063959,0.));
#2492=CARTESIAN_POINT('',(-0.00174812035530277,0.000261435381063959,8.E-5));
#2493=CARTESIAN_POINT('',(-0.00174812035530277,0.000261435381063959,0.));
#2494=CARTESIAN_POINT('',(8.79644697226318E-7,0.00151143538106396,0.));
#2495=CARTESIAN_POINT('',(-0.00160862035530277,-0.000358564618936041,0.));
#2496=CARTESIAN_POINT('',(-0.00174812035530277,-0.000358564618936041,0.));
#2497=CARTESIAN_POINT('',(-0.00174812035530277,-0.000358564618936041,0.));
#2498=CARTESIAN_POINT('',(-0.00160862035530277,-0.000358564618936041,0.));
#2499=CARTESIAN_POINT('',(-0.00146912035530277,-0.000358564618936041,0.));
#2500=CARTESIAN_POINT('',(-0.00146912035530277,-0.000358564618936041,0.));
#2501=CARTESIAN_POINT('',(-0.00146912035530277,-0.00023856461893604,0.));
#2502=CARTESIAN_POINT('',(-0.00146912035530277,-0.00023856461893604,0.));
#2503=CARTESIAN_POINT('',(-0.00146912035530277,-0.00011856461893604,0.));
#2504=CARTESIAN_POINT('',(-0.00146912035530277,-0.00011856461893604,0.));
#2505=CARTESIAN_POINT('',(-0.00160862035530277,-0.00011856461893604,0.));
#2506=CARTESIAN_POINT('',(-0.00160862035530277,-0.00011856461893604,0.));
#2507=CARTESIAN_POINT('',(-0.00174812035530277,-0.000118564618936041,0.));
#2508=CARTESIAN_POINT('',(-0.00174812035530277,-0.000118564618936041,0.));
#2509=CARTESIAN_POINT('',(-0.00174812035530277,-0.000238564618936041,0.));
#2510=CARTESIAN_POINT('',(-0.00174812035530277,-0.000238564618936041,8.E-5));
#2511=CARTESIAN_POINT('',(-0.00174812035530277,-0.000238564618936041,0.));
#2512=CARTESIAN_POINT('',(8.79644697226349E-7,0.00101143538106396,0.));
#2513=CARTESIAN_POINT('',(-0.00160862035530277,-0.00085856461893604,0.));
#2514=CARTESIAN_POINT('',(-0.00174812035530277,-0.000858564618936041,0.));
#2515=CARTESIAN_POINT('',(-0.00174812035530277,-0.000858564618936041,0.));
#2516=CARTESIAN_POINT('',(-0.00160862035530277,-0.00085856461893604,0.));
#2517=CARTESIAN_POINT('',(-0.00146912035530277,-0.00085856461893604,0.));
#2518=CARTESIAN_POINT('',(-0.00146912035530277,-0.00085856461893604,0.));
#2519=CARTESIAN_POINT('',(-0.00146912035530277,-0.000738564618936041,0.));
#2520=CARTESIAN_POINT('',(-0.00146912035530277,-0.000738564618936041,0.));
#2521=CARTESIAN_POINT('',(-0.00146912035530277,-0.00061856461893604,0.));
#2522=CARTESIAN_POINT('',(-0.00146912035530277,-0.00061856461893604,0.));
#2523=CARTESIAN_POINT('',(-0.00160862035530277,-0.00061856461893604,0.));
#2524=CARTESIAN_POINT('',(-0.00160862035530277,-0.00061856461893604,0.));
#2525=CARTESIAN_POINT('',(-0.00174812035530277,-0.000618564618936041,0.));
#2526=CARTESIAN_POINT('',(-0.00174812035530277,-0.000618564618936041,0.));
#2527=CARTESIAN_POINT('',(-0.00174812035530277,-0.000738564618936041,0.));
#2528=CARTESIAN_POINT('',(-0.00174812035530277,-0.000738564618936041,8.E-5));
#2529=CARTESIAN_POINT('',(-0.00174812035530277,-0.000738564618936041,0.));
#2530=CARTESIAN_POINT('',(8.7964469722638E-7,0.000511435381063961,0.));
#2531=CARTESIAN_POINT('',(0.00161037964469722,0.00114143538106396,0.));
#2532=CARTESIAN_POINT('',(0.00147087964469723,0.00114143538106396,0.));
#2533=CARTESIAN_POINT('',(0.00147087964469723,0.00114143538106396,0.));
#2534=CARTESIAN_POINT('',(0.00161037964469722,0.00114143538106396,0.));
#2535=CARTESIAN_POINT('',(0.00174987964469723,0.00114143538106396,0.));
#2536=CARTESIAN_POINT('',(0.00174987964469723,0.00114143538106396,0.));
#2537=CARTESIAN_POINT('',(0.00147087964469723,0.00126143538106396,0.));
#2538=CARTESIAN_POINT('',(0.00147087964469723,0.00138143538106396,0.));
#2539=CARTESIAN_POINT('',(0.00147087964469723,0.00138143538106396,0.));
#2540=CARTESIAN_POINT('',(0.00147087964469723,0.00126143538106396,0.));
#2541=CARTESIAN_POINT('',(0.00161037964469722,0.00138143538106396,0.));
#2542=CARTESIAN_POINT('',(0.00174987964469723,0.00138143538106396,0.));
#2543=CARTESIAN_POINT('',(0.00174987964469723,0.00138143538106396,0.));
#2544=CARTESIAN_POINT('',(0.00161037964469722,0.00138143538106396,0.));
#2545=CARTESIAN_POINT('',(0.00174987964469723,0.00126143538106396,0.));
#2546=CARTESIAN_POINT('',(0.00174987964469723,0.00126143538106396,8.E-5));
#2547=CARTESIAN_POINT('',(0.00174987964469723,0.00126143538106396,0.));
#2548=CARTESIAN_POINT('',(8.79644697226257E-7,0.00251143538106396,0.));
#2549=CARTESIAN_POINT('',(0.00161037964469722,0.00064143538106396,0.));
#2550=CARTESIAN_POINT('',(0.00147087964469723,0.00064143538106396,0.));
#2551=CARTESIAN_POINT('',(0.00147087964469723,0.00064143538106396,0.));
#2552=CARTESIAN_POINT('',(0.00161037964469722,0.00064143538106396,0.));
#2553=CARTESIAN_POINT('',(0.00174987964469723,0.000641435381063959,0.));
#2554=CARTESIAN_POINT('',(0.00174987964469723,0.000641435381063959,0.));
#2555=CARTESIAN_POINT('',(0.00147087964469723,0.00076143538106396,0.));
#2556=CARTESIAN_POINT('',(0.00147087964469723,0.00088143538106396,0.));
#2557=CARTESIAN_POINT('',(0.00147087964469723,0.00088143538106396,0.));
#2558=CARTESIAN_POINT('',(0.00147087964469723,0.00076143538106396,0.));
#2559=CARTESIAN_POINT('',(0.00161037964469722,0.00088143538106396,0.));
#2560=CARTESIAN_POINT('',(0.00174987964469723,0.000881435381063959,0.));
#2561=CARTESIAN_POINT('',(0.00174987964469723,0.000881435381063959,0.));
#2562=CARTESIAN_POINT('',(0.00161037964469722,0.00088143538106396,0.));
#2563=CARTESIAN_POINT('',(0.00174987964469723,0.000761435381063959,0.));
#2564=CARTESIAN_POINT('',(0.00174987964469723,0.000761435381063959,8.E-5));
#2565=CARTESIAN_POINT('',(0.00174987964469723,0.000761435381063959,0.));
#2566=CARTESIAN_POINT('',(8.79644697226288E-7,0.00201143538106396,0.));
#2567=CARTESIAN_POINT('',(0.00161037964469722,0.00014143538106396,0.));
#2568=CARTESIAN_POINT('',(0.00147087964469723,0.00014143538106396,0.));
#2569=CARTESIAN_POINT('',(0.00147087964469723,0.00014143538106396,0.));
#2570=CARTESIAN_POINT('',(0.00161037964469722,0.00014143538106396,0.));
#2571=CARTESIAN_POINT('',(0.00174987964469723,0.000141435381063959,0.));
#2572=CARTESIAN_POINT('',(0.00174987964469723,0.000141435381063959,0.));
#2573=CARTESIAN_POINT('',(0.00147087964469723,0.00026143538106396,0.));
#2574=CARTESIAN_POINT('',(0.00147087964469723,0.00038143538106396,0.));
#2575=CARTESIAN_POINT('',(0.00147087964469723,0.00038143538106396,0.));
#2576=CARTESIAN_POINT('',(0.00147087964469723,0.00026143538106396,0.));
#2577=CARTESIAN_POINT('',(0.00161037964469722,0.00038143538106396,0.));
#2578=CARTESIAN_POINT('',(0.00174987964469723,0.000381435381063959,0.));
#2579=CARTESIAN_POINT('',(0.00174987964469723,0.000381435381063959,0.));
#2580=CARTESIAN_POINT('',(0.00161037964469722,0.00038143538106396,0.));
#2581=CARTESIAN_POINT('',(0.00174987964469723,0.000261435381063959,0.));
#2582=CARTESIAN_POINT('',(0.00174987964469723,0.000261435381063959,8.E-5));
#2583=CARTESIAN_POINT('',(0.00174987964469723,0.000261435381063959,0.));
#2584=CARTESIAN_POINT('',(8.79644697226318E-7,0.00151143538106396,0.));
#2585=CARTESIAN_POINT('',(0.00161037964469722,-0.00035856461893604,0.));
#2586=CARTESIAN_POINT('',(0.00147087964469723,-0.00035856461893604,0.));
#2587=CARTESIAN_POINT('',(0.00147087964469723,-0.00035856461893604,0.));
#2588=CARTESIAN_POINT('',(0.00161037964469722,-0.00035856461893604,0.));
#2589=CARTESIAN_POINT('',(0.00174987964469723,-0.000358564618936041,0.));
#2590=CARTESIAN_POINT('',(0.00174987964469723,-0.000358564618936041,0.));
#2591=CARTESIAN_POINT('',(0.00147087964469723,-0.00023856461893604,0.));
#2592=CARTESIAN_POINT('',(0.00147087964469723,-0.00011856461893604,0.));
#2593=CARTESIAN_POINT('',(0.00147087964469723,-0.00011856461893604,0.));
#2594=CARTESIAN_POINT('',(0.00147087964469723,-0.00023856461893604,0.));
#2595=CARTESIAN_POINT('',(0.00161037964469722,-0.00011856461893604,0.));
#2596=CARTESIAN_POINT('',(0.00174987964469723,-0.000118564618936041,0.));
#2597=CARTESIAN_POINT('',(0.00174987964469723,-0.000118564618936041,0.));
#2598=CARTESIAN_POINT('',(0.00161037964469722,-0.00011856461893604,0.));
#2599=CARTESIAN_POINT('',(0.00174987964469723,-0.000238564618936041,0.));
#2600=CARTESIAN_POINT('',(0.00174987964469723,-0.000238564618936041,8.E-5));
#2601=CARTESIAN_POINT('',(0.00174987964469723,-0.000238564618936041,0.));
#2602=CARTESIAN_POINT('',(8.79644697226349E-7,0.00101143538106396,0.));
#2603=CARTESIAN_POINT('',(0.00161037964469722,-0.00085856461893604,0.));
#2604=CARTESIAN_POINT('',(0.00147087964469723,-0.00085856461893604,0.));
#2605=CARTESIAN_POINT('',(0.00147087964469723,-0.00085856461893604,0.));
#2606=CARTESIAN_POINT('',(0.00161037964469722,-0.00085856461893604,0.));
#2607=CARTESIAN_POINT('',(0.00174987964469723,-0.000858564618936041,0.));
#2608=CARTESIAN_POINT('',(0.00174987964469723,-0.000858564618936041,0.));
#2609=CARTESIAN_POINT('',(0.00147087964469723,-0.00073856461893604,0.));
#2610=CARTESIAN_POINT('',(0.00147087964469723,-0.00061856461893604,0.));
#2611=CARTESIAN_POINT('',(0.00147087964469723,-0.00061856461893604,0.));
#2612=CARTESIAN_POINT('',(0.00147087964469723,-0.00073856461893604,0.));
#2613=CARTESIAN_POINT('',(0.00161037964469722,-0.00061856461893604,0.));
#2614=CARTESIAN_POINT('',(0.00174987964469723,-0.000618564618936041,0.));
#2615=CARTESIAN_POINT('',(0.00174987964469723,-0.000618564618936041,0.));
#2616=CARTESIAN_POINT('',(0.00161037964469722,-0.00061856461893604,0.));
#2617=CARTESIAN_POINT('',(0.00174987964469723,-0.000738564618936041,0.));
#2618=CARTESIAN_POINT('',(0.00174987964469723,-0.000738564618936041,8.E-5));
#2619=CARTESIAN_POINT('',(0.00174987964469723,-0.000738564618936041,0.));
#2620=CARTESIAN_POINT('',(8.7964469722638E-7,0.000511435381063961,0.));
#2621=CARTESIAN_POINT('',(8.79644697226749E-7,-0.00198856461893604,0.00106));
#2622=CARTESIAN_POINT('',(0.00175087964469722,-0.00198856461893604,0.00106));
#2623=CARTESIAN_POINT('',(0.00175087964469722,-0.00198856461893604,0.00106));
#2624=CARTESIAN_POINT('',(8.79644697226749E-7,-0.00198856461893604,0.00106));
#2625=CARTESIAN_POINT('',(-0.00174912035530277,-0.00198856461893604,0.00106));
#2626=CARTESIAN_POINT('',(-0.00174912035530277,-0.00198856461893604,0.00106));
#2627=CARTESIAN_POINT('',(0.00175087964469722,1.14353810639602E-5,0.00106));
#2628=CARTESIAN_POINT('',(0.00175087964469722,0.00201143538106396,0.00106));
#2629=CARTESIAN_POINT('',(0.00175087964469722,0.00201143538106396,0.00106));
#2630=CARTESIAN_POINT('',(0.00175087964469722,1.14353810639602E-5,0.00106));
#2631=CARTESIAN_POINT('',(0.00174987964469723,-0.00123856461893604,8.E-5));
#2632=CARTESIAN_POINT('',(8.79644697226721E-7,0.00201143538106396,0.00106));
#2633=CARTESIAN_POINT('',(-0.00174912035530277,0.00201143538106396,0.00106));
#2634=CARTESIAN_POINT('',(-0.00174912035530277,0.00201143538106396,0.00106));
#2635=CARTESIAN_POINT('',(8.79644697226721E-7,0.00201143538106396,0.00106));
#2636=CARTESIAN_POINT('',(-0.00174912035530277,1.143538106396E-5,0.00106));
#2637=CARTESIAN_POINT('',(-0.00174812035530277,-0.00123856461893604,8.E-5));
#2638=CARTESIAN_POINT('',(-0.00174912035530277,1.143538106396E-5,0.00106));
#2639=CARTESIAN_POINT('',(8.7964469722641E-7,1.14353810639606E-5,0.00106));
#2640=CARTESIAN_POINT('',(-0.00149912035530277,0.00176143538106396,0.00106));
#2641=CARTESIAN_POINT('',(-0.00149912035530277,0.00163643538106396,0.00106));
#2642=CARTESIAN_POINT('',(0.00161037964469722,-0.00135856461893604,0.));
#2643=CARTESIAN_POINT('',(0.00147087964469723,-0.00135856461893604,0.));
#2644=CARTESIAN_POINT('',(0.00147087964469723,-0.00135856461893604,0.));
#2645=CARTESIAN_POINT('',(0.00161037964469722,-0.00135856461893604,0.));
#2646=CARTESIAN_POINT('',(0.00174987964469723,-0.00135856461893604,0.));
#2647=CARTESIAN_POINT('',(0.00174987964469723,-0.00135856461893604,0.));
#2648=CARTESIAN_POINT('',(0.00147087964469723,-0.00123856461893604,0.));
#2649=CARTESIAN_POINT('',(0.00147087964469723,-0.00111856461893604,0.));
#2650=CARTESIAN_POINT('',(0.00147087964469723,-0.00111856461893604,0.));
#2651=CARTESIAN_POINT('',(0.00147087964469723,-0.00123856461893604,0.));
#2652=CARTESIAN_POINT('',(0.00161037964469722,-0.00111856461893604,0.));
#2653=CARTESIAN_POINT('',(0.00174987964469723,-0.00111856461893604,0.));
#2654=CARTESIAN_POINT('',(0.00174987964469723,-0.00111856461893604,0.));
#2655=CARTESIAN_POINT('',(0.00161037964469722,-0.00111856461893604,0.));
#2656=CARTESIAN_POINT('',(0.00174987964469723,-0.00123856461893604,0.));
#2657=CARTESIAN_POINT('',(0.00174987964469723,-0.00123856461893604,0.));
#2658=CARTESIAN_POINT('',(8.7964469722641E-7,1.14353810639606E-5,0.));
#2659=CARTESIAN_POINT('',(-0.00160862035530277,-0.00135856461893604,0.));
#2660=CARTESIAN_POINT('',(-0.00174812035530277,-0.00135856461893604,0.));
#2661=CARTESIAN_POINT('',(-0.00174812035530277,-0.00135856461893604,0.));
#2662=CARTESIAN_POINT('',(-0.00160862035530277,-0.00135856461893604,0.));
#2663=CARTESIAN_POINT('',(-0.00146912035530277,-0.00135856461893604,0.));
#2664=CARTESIAN_POINT('',(-0.00146912035530277,-0.00135856461893604,0.));
#2665=CARTESIAN_POINT('',(-0.00174812035530277,-0.00123856461893604,0.));
#2666=CARTESIAN_POINT('',(-0.00174812035530277,-0.00111856461893604,0.));
#2667=CARTESIAN_POINT('',(-0.00174812035530277,-0.00111856461893604,0.));
#2668=CARTESIAN_POINT('',(-0.00174812035530277,-0.00123856461893604,0.));
#2669=CARTESIAN_POINT('',(-0.00160862035530277,-0.00111856461893604,0.));
#2670=CARTESIAN_POINT('',(-0.00146912035530277,-0.00111856461893604,0.));
#2671=CARTESIAN_POINT('',(-0.00146912035530277,-0.00111856461893604,0.));
#2672=CARTESIAN_POINT('',(-0.00160862035530277,-0.00111856461893604,0.));
#2673=CARTESIAN_POINT('',(-0.00146912035530277,-0.00123856461893604,0.));
#2674=CARTESIAN_POINT('',(-0.00146912035530277,-0.00123856461893604,0.));
#2675=CARTESIAN_POINT('',(8.7964469722641E-7,1.14353810639606E-5,0.));
#2676=CARTESIAN_POINT('',(-0.000869120355302773,-0.00184456461893604,0.));
#2677=CARTESIAN_POINT('',(-0.000869120355302773,-0.00170856461893604,0.));
#2678=CARTESIAN_POINT('',(-0.000869120355302773,-0.00170856461893604,0.));
#2679=CARTESIAN_POINT('',(-0.000869120355302773,-0.00184456461893604,0.));
#2680=CARTESIAN_POINT('',(-0.000869120355302773,-0.00198056461893604,0.));
#2681=CARTESIAN_POINT('',(-0.000869120355302773,-0.00198056461893604,0.));
#2682=CARTESIAN_POINT('',(-0.000749120355302774,-0.00170856461893604,0.));
#2683=CARTESIAN_POINT('',(-0.000629120355302774,-0.00170856461893604,0.));
#2684=CARTESIAN_POINT('',(-0.000629120355302774,-0.00170856461893604,0.));
#2685=CARTESIAN_POINT('',(-0.000749120355302774,-0.00170856461893604,0.));
#2686=CARTESIAN_POINT('',(-0.000629120355302774,-0.00184456461893604,0.));
#2687=CARTESIAN_POINT('',(-0.000629120355302774,-0.00198056461893604,0.));
#2688=CARTESIAN_POINT('',(-0.000629120355302774,-0.00198056461893604,0.));
#2689=CARTESIAN_POINT('',(-0.000629120355302774,-0.00184456461893604,0.));
#2690=CARTESIAN_POINT('',(-0.000749120355302774,-0.00198056461893604,0.));
#2691=CARTESIAN_POINT('',(-0.000749120355302774,-0.00198056461893604,0.));
#2692=CARTESIAN_POINT('',(4.87964469722607E-6,1.14353810639597E-5,0.));
#2693=CARTESIAN_POINT('',(0.000750879644697227,-0.00198056461893604,0.));
#2694=CARTESIAN_POINT('',(0.000630879644697227,-0.00198056461893604,0.));
#2695=CARTESIAN_POINT('',(0.000630879644697227,-0.00198056461893604,0.));
#2696=CARTESIAN_POINT('',(0.000750879644697227,-0.00198056461893604,0.));
#2697=CARTESIAN_POINT('',(0.000870879644697227,-0.00198056461893604,0.));
#2698=CARTESIAN_POINT('',(0.000870879644697227,-0.00198056461893604,0.));
#2699=CARTESIAN_POINT('',(0.000630879644697227,-0.00184456461893604,0.));
#2700=CARTESIAN_POINT('',(0.000630879644697227,-0.00170856461893604,0.));
#2701=CARTESIAN_POINT('',(0.000630879644697227,-0.00170856461893604,0.));
#2702=CARTESIAN_POINT('',(0.000630879644697227,-0.00184456461893604,0.));
#2703=CARTESIAN_POINT('',(0.000750879644697227,-0.00170856461893604,0.));
#2704=CARTESIAN_POINT('',(0.000870879644697227,-0.00170856461893604,0.));
#2705=CARTESIAN_POINT('',(0.000870879644697227,-0.00170856461893604,0.));
#2706=CARTESIAN_POINT('',(0.000750879644697227,-0.00170856461893604,0.));
#2707=CARTESIAN_POINT('',(0.000870879644697227,-0.00184456461893604,0.));
#2708=CARTESIAN_POINT('',(0.000870879644697227,-0.00184456461893604,0.));
#2709=CARTESIAN_POINT('',(4.87964469722607E-6,1.14353810639597E-5,0.));
#2710=CARTESIAN_POINT('',(0.000870879644697226,0.00186743538106396,0.));
#2711=CARTESIAN_POINT('',(0.000870879644697226,0.00173143538106396,0.));
#2712=CARTESIAN_POINT('',(0.000870879644697226,0.00173143538106396,0.));
#2713=CARTESIAN_POINT('',(0.000870879644697226,0.00186743538106396,0.));
#2714=CARTESIAN_POINT('',(0.000870879644697226,0.00200343538106396,0.));
#2715=CARTESIAN_POINT('',(0.000870879644697226,0.00200343538106396,0.));
#2716=CARTESIAN_POINT('',(0.000750879644697226,0.00173143538106396,0.));
#2717=CARTESIAN_POINT('',(0.000630879644697226,0.00173143538106396,0.));
#2718=CARTESIAN_POINT('',(0.000630879644697226,0.00173143538106396,0.));
#2719=CARTESIAN_POINT('',(0.000750879644697226,0.00173143538106396,0.));
#2720=CARTESIAN_POINT('',(0.000630879644697226,0.00186743538106396,0.));
#2721=CARTESIAN_POINT('',(0.000630879644697227,0.00200343538106396,0.));
#2722=CARTESIAN_POINT('',(0.000630879644697227,0.00200343538106396,0.));
#2723=CARTESIAN_POINT('',(0.000630879644697226,0.00186743538106396,0.));
#2724=CARTESIAN_POINT('',(0.000750879644697226,0.00200343538106396,0.));
#2725=CARTESIAN_POINT('',(0.000750879644697226,0.00200343538106396,0.));
#2726=CARTESIAN_POINT('',(4.87964469722607E-6,1.14353810639597E-5,0.));
#2727=CARTESIAN_POINT('',(-0.000749120355302774,0.00200343538106396,0.));
#2728=CARTESIAN_POINT('',(-0.000629120355302774,0.00200343538106396,0.));
#2729=CARTESIAN_POINT('',(-0.000629120355302774,0.00200343538106396,0.));
#2730=CARTESIAN_POINT('',(-0.000749120355302774,0.00200343538106396,0.));
#2731=CARTESIAN_POINT('',(-0.000869120355302774,0.00200343538106396,0.));
#2732=CARTESIAN_POINT('',(-0.000869120355302774,0.00200343538106396,0.));
#2733=CARTESIAN_POINT('',(-0.000629120355302774,0.00186743538106396,0.));
#2734=CARTESIAN_POINT('',(-0.000629120355302774,0.00173143538106396,0.));
#2735=CARTESIAN_POINT('',(-0.000629120355302774,0.00173143538106396,0.));
#2736=CARTESIAN_POINT('',(-0.000629120355302774,0.00186743538106396,0.));
#2737=CARTESIAN_POINT('',(-0.000749120355302774,0.00173143538106396,0.));
#2738=CARTESIAN_POINT('',(-0.000869120355302774,0.00173143538106396,0.));
#2739=CARTESIAN_POINT('',(-0.000869120355302774,0.00173143538106396,0.));
#2740=CARTESIAN_POINT('',(-0.000749120355302774,0.00173143538106396,0.));
#2741=CARTESIAN_POINT('',(-0.000869120355302774,0.00186743538106396,0.));
#2742=CARTESIAN_POINT('',(-0.000869120355302774,0.00186743538106396,0.));
#2743=CARTESIAN_POINT('',(4.87964469722607E-6,1.14353810639597E-5,0.));
#2744=CARTESIAN_POINT('',(0.00102587964469723,1.14353810639595E-5,0.));
#2745=CARTESIAN_POINT('',(0.00102587964469723,-0.00126356461893604,0.));
#2746=CARTESIAN_POINT('',(0.00102587964469723,-0.00126356461893604,0.));
#2747=CARTESIAN_POINT('',(0.00102587964469723,1.14353810639595E-5,0.));
#2748=CARTESIAN_POINT('',(0.00102587964469723,0.00128643538106396,0.));
#2749=CARTESIAN_POINT('',(0.00102587964469723,0.00128643538106396,0.));
#2750=CARTESIAN_POINT('',(8.79644697226271E-7,-0.00126356461893604,0.));
#2751=CARTESIAN_POINT('',(-0.00102412035530277,-0.00126356461893604,0.));
#2752=CARTESIAN_POINT('',(-0.00102412035530277,-0.00126356461893604,0.));
#2753=CARTESIAN_POINT('',(8.79644697226271E-7,-0.00126356461893604,0.));
#2754=CARTESIAN_POINT('',(-0.00102412035530277,1.14353810639596E-5,0.));
#2755=CARTESIAN_POINT('',(-0.00102412035530277,0.00128643538106396,0.));
#2756=CARTESIAN_POINT('',(-0.00102412035530277,0.00128643538106396,0.));
#2757=CARTESIAN_POINT('',(-0.00102412035530277,1.14353810639596E-5,0.));
#2758=CARTESIAN_POINT('',(8.79644697226549E-7,0.00128643538106396,0.));
#2759=CARTESIAN_POINT('',(8.79644697226549E-7,0.00128643538106396,0.));
#2760=CARTESIAN_POINT('',(4.87964469722607E-6,1.14353810639597E-5,0.));
#2761=CARTESIAN_POINT('',(-0.00149912035530277,0.00176143538106396,0.00105));
#2762=CARTESIAN_POINT('',(-0.00149912035530277,0.00176143538106396,0.00105));
#2763=CARTESIAN_POINT('',(-0.00149912035530277,0.00163643538106396,0.00105));
#2764=CARTESIAN_POINT('',(8.7964469722641E-7,1.14353810639606E-5,0.00105));
#2765=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#1667,
#1567,#1568,#1569,#1570,#1571,#1572),#2766);
#2766=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#2767))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#2770,#2769,#2768))
REPRESENTATION_CONTEXT('Part 1','TOP_LEVEL_ASSEMBLY_PART')
);
#2767=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(5.E-6),#2770,
'DISTANCE_ACCURACY_VALUE','Maximum Tolerance applied to model');
#2768=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#2769=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#2770=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#2771=PRODUCT_DEFINITION_SHAPE('','',#2772);
#2772=PRODUCT_DEFINITION('','',#2774,#2773);
#2773=PRODUCT_DEFINITION_CONTEXT('',#2780,'design');
#2774=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('','',#2776,
 .NOT_KNOWN.);
#2775=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#2776));
#2776=PRODUCT('Part 1','Part 1','Part 1',(#2778));
#2777=PRODUCT_CATEGORY('','');
#2778=PRODUCT_CONTEXT('',#2780,'mechanical');
#2779=APPLICATION_PROTOCOL_DEFINITION('international standard',
'ap242_managed_model_based_3d_engineering',2011,#2780);
#2780=APPLICATION_CONTEXT('managed model based 3d engineering');
ENDSEC;
END-ISO-10303-21;
