(footprint "USB_C_Receptacle_HRO_TYPE-C-31-M-12"
	(version 20240703)
	(generator "pcbnew")
	(generator_version "8.99")
	(layer "F.Cu")
	(descr "USB Type-C receptacle for USB 2.0 and PD, http://www.krhro.com/uploads/soft/180320/1-1P320120243.pdf")
	(tags "usb usb-c 2.0 pd")
	(property "Reference" "REF**"
		(at 0 -5.645 0)
		(layer "F.SilkS")
		(uuid "a144ce76-cc23-44fb-8108-bf18e64a4c3d")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "USB_C_Receptacle_HRO_TYPE-C-31-M-12"
		(at 0 5.1 0)
		(layer "F.Fab")
		(uuid "0ca5cf76-e951-4e58-a402-14dc42a864b3")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Footprint" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "d62fc02d-25b3-4b03-a875-998159095d75")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "2b954944-56ad-4032-bee5-bd7186324aef")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "9d55800b-f601-4afa-9fad-eee332310ae0")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr smd)
	(fp_line
		(start -4.7 -1.9)
		(end -4.7 0.1)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "9513b845-7495-41b4-99da-c8a12a5f4b76")
	)
	(fp_line
		(start 4.7 -1.9)
		(end 4.7 0.1)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "177e7db1-b916-4e65-ace8-532e6e1f85db")
	)
	(fp_line
		(start -5.32 -5.27)
		(end -5.32 4.15)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "d939342c-cab3-429e-ad71-738d34173267")
	)
	(fp_line
		(start -5.32 -5.27)
		(end 5.32 -5.27)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "3215d7dd-650e-4c6a-86e5-bc970c8d9c6c")
	)
	(fp_line
		(start -5.32 4.15)
		(end 5.32 4.15)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "c38cb6a1-740b-49d2-b49e-f8ce3382fe48")
	)
	(fp_line
		(start 5.32 -5.27)
		(end 5.32 4.15)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "3b610068-7f90-495b-a772-9a6b7e6a55c9")
	)
	(fp_line
		(start -4.47 -3.65)
		(end -4.47 3.65)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "47095738-bb75-4e91-802c-f92840c443b3")
	)
	(fp_line
		(start -4.47 -3.65)
		(end 4.47 -3.65)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "6142486d-ba8a-4b38-a6ca-d52b6a020ef2")
	)
	(fp_line
		(start -4.47 3.65)
		(end 4.47 3.65)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "84966fee-864e-4cdc-8345-c62bca33694f")
	)
	(fp_line
		(start 4.47 -3.65)
		(end 4.47 3.65)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "f6b3cf35-21be-4e1f-8c99-3404cfc41d3f")
	)
	(fp_text user "${REFERENCE}"
		(at 0 0 0)
		(layer "F.Fab")
		(uuid "9189a13a-f353-4ddb-8257-370c54398cd0")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "" np_thru_hole circle
		(at -2.89 -2.6)
		(size 0.65 0.65)
		(drill 0.65)
		(layers "*.Cu" "*.Mask")
		(uuid "e13b4b37-788a-41d5-87ca-c66be43ee32d")
	)
	(pad "" np_thru_hole circle
		(at 2.89 -2.6)
		(size 0.65 0.65)
		(drill 0.65)
		(layers "*.Cu" "*.Mask")
		(uuid "4558f1f0-2fa3-46e1-a220-284fa2707bb5")
	)
	(pad "A1" smd rect
		(at -3.25 -4.045)
		(size 0.6 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "245fae56-8b58-4bd8-bbf1-36624dc3fa3e")
	)
	(pad "A4" smd rect
		(at -2.45 -4.045)
		(size 0.6 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "412cd3c1-0d89-4ca6-be0e-1b17d8471f5a")
	)
	(pad "A5" smd rect
		(at -1.25 -4.045)
		(size 0.3 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "93953890-acba-456b-bb6d-b1e79179ffa0")
	)
	(pad "A6" smd rect
		(at -0.25 -4.045)
		(size 0.3 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "6a642da3-3122-4031-a6e9-68498b0f5bb9")
	)
	(pad "A7" smd rect
		(at 0.25 -4.045)
		(size 0.3 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "01b9b129-063c-4bae-b620-9610006254cb")
	)
	(pad "A8" smd rect
		(at 1.25 -4.045)
		(size 0.3 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "46ecdf8a-0917-4257-a388-b77e7451dc23")
	)
	(pad "A9" smd rect
		(at 2.45 -4.045)
		(size 0.6 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "a35f3da7-7460-43f1-99b7-9ed521817778")
	)
	(pad "A12" smd rect
		(at 3.25 -4.045)
		(size 0.6 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "3795b0f1-50f9-473e-9d26-381fc08b3c2d")
	)
	(pad "B1" smd rect
		(at 3.25 -4.045)
		(size 0.6 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "c9e8a0db-2a60-41d9-b783-6c4e64a8fb5d")
	)
	(pad "B4" smd rect
		(at 2.45 -4.045)
		(size 0.6 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "51b193ee-c8c2-4ee6-8d8c-14b79a2c1520")
	)
	(pad "B5" smd rect
		(at 1.75 -4.045)
		(size 0.3 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "45f8f791-f545-4341-9848-fb81a03703a6")
	)
	(pad "B6" smd rect
		(at 0.75 -4.045)
		(size 0.3 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "efc27675-62a0-44de-8210-29c2190dd309")
	)
	(pad "B7" smd rect
		(at -0.75 -4.045)
		(size 0.3 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "96622dcb-c1cc-4b7d-8455-8cd0ce1e5c34")
	)
	(pad "B8" smd rect
		(at -1.75 -4.045)
		(size 0.3 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "09bb6e12-e7e5-4788-b43e-4cc6c50a557a")
	)
	(pad "B9" smd rect
		(at -2.45 -4.045)
		(size 0.6 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "70539c09-2020-4a42-a156-43d1513170d0")
	)
	(pad "B12" smd rect
		(at -3.25 -4.045)
		(size 0.6 1.45)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "eda87c04-3a59-4e2c-a9bd-f59ed31672aa")
	)
	(pad "S1" thru_hole oval
		(at -4.32 -3.13)
		(size 1 2.1)
		(drill oval 0.6 1.7)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "69c3dc88-a37e-49ef-ae4d-497d3191ad5b")
	)
	(pad "S1" thru_hole oval
		(at -4.32 1.05)
		(size 1 1.6)
		(drill oval 0.6 1.2)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "5991ca6f-c5c1-4692-8fe9-cf4b7ca50c4b")
	)
	(pad "S1" thru_hole oval
		(at 4.32 -3.13)
		(size 1 2.1)
		(drill oval 0.6 1.7)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "af5ae63f-ef36-487e-a6b8-831b84890f25")
	)
	(pad "S1" thru_hole oval
		(at 4.32 1.05)
		(size 1 1.6)
		(drill oval 0.6 1.2)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "e783dde5-f762-4528-9b55-9220820b0b1c")
	)
	(model "${KICAD8_3DMODEL_DIR}/Connector_USB.3dshapes/USB_C_Receptacle_HRO_TYPE-C-31-M-12.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
	(model "${KIPRJMOD}/lib/3dmodels/Connector_USB.3dshapes/USB_C_Receptacle_HRO_TYPE-C-31-M-12.STEP"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
