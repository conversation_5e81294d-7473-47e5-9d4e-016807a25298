(kicad_sch
	(version 20241209)
	(generator "eeschema")
	(generator_version "9.0")
	(uuid "e63e39d7-6ac0-4ffd-8aa3-1841a4541b55")
	(paper "A4")
	(lib_symbols
		(symbol "Device:C"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "C"
				(at 0.635 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "C"
				(at 0.635 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0.9652 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Unpolarized capacitor"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "cap capacitor"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "C_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "C_0_1"
				(polyline
					(pts
						(xy -2.032 0.762) (xy 2.032 0.762)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -2.032 -0.762) (xy 2.032 -0.762)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "C_1_1"
				(pin passive line
					(at 0 3.81 270)
					(length 2.794)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -3.81 90)
					(length 2.794)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Device:R"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "R"
				(at 2.032 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "R"
				(at 0 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at -1.778 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Resistor"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "R res resistor"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "R_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "R_0_1"
				(rectangle
					(start -1.016 -2.54)
					(end 1.016 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "R_1_1"
				(pin passive line
					(at 0 3.81 270)
					(length 1.27)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -3.81 90)
					(length 1.27)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:GND"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "GND"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "GND_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "GND_1_1"
				(pin power_in line
					(at 0 0 270)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "power:PWR_FLAG"
			(power)
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
				(hide yes)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#FLG"
				(at 0 1.905 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "PWR_FLAG"
				(at 0 3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Special symbol for telling ERC where power comes from"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "flag power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "PWR_FLAG_0_0"
				(pin power_out line
					(at 0 0 90)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "PWR_FLAG_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 1.27) (xy -1.016 1.905) (xy 0 2.54) (xy 1.016 1.905) (xy 0 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "project:VSOURCE"
			(pin_names
				(offset 1.016)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "V1"
				(at 3.2512 1.1684 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "SINE(0 1.5 1k 0 0 0 0)"
				(at 3.2512 -1.143 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Fieldname" "Value"
				(at 0 0 0)
				(effects
					(font
						(size 1.524 1.524)
					)
					(hide yes)
				)
			)
			(property "Sim.Pins" "1=1 2=2"
				(at -7.62 5.08 0)
				(effects
					(font
						(size 1.524 1.524)
					)
					(hide yes)
				)
			)
			(symbol "VSOURCE_0_1"
				(polyline
					(pts
						(xy 0 1.905) (xy -0.635 0.635) (xy 0.635 0.635) (xy 0 1.905)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(circle
					(center 0 0)
					(radius 2.54)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 -1.905) (xy 0 1.905)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "VSOURCE_1_1"
				(pin input line
					(at 0 5.08 270)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin input line
					(at 0 -5.08 90)
					(length 2.54)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
	)
	(text ".tran 1u 10m\n"
		(exclude_from_sim no)
		(at 129.54 127 0)
		(effects
			(font
				(size 1.524 1.524)
			)
			(justify left bottom)
		)
		(uuid "cd763078-6e8e-42d0-a7f6-5b08fa59c64e")
	)
	(junction
		(at 148.59 101.6)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "6d6a7a48-0605-4a8b-940b-31b71bf98ff6")
	)
	(junction
		(at 130.81 101.6)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "c5758720-4f8e-4b7b-ba94-b4ef3501862a")
	)
	(junction
		(at 130.81 113.03)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "da9d1623-aeb7-4550-b812-f18bd046ab50")
	)
	(wire
		(pts
			(xy 171.45 78.74) (xy 187.96 78.74)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0458ee03-f060-47f9-b4f2-775642ec2902")
	)
	(wire
		(pts
			(xy 157.48 78.74) (xy 130.81 78.74)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "24552a3f-fc08-4544-aa2f-57894893ad1b")
	)
	(wire
		(pts
			(xy 124.46 113.03) (xy 130.81 113.03)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "27c8e26a-d097-466c-b955-c81d733e67c1")
	)
	(wire
		(pts
			(xy 171.45 101.6) (xy 187.96 101.6)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2d7c1bac-3b9b-4a4b-9f6c-************")
	)
	(wire
		(pts
			(xy 148.59 101.6) (xy 157.48 101.6)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "468cf795-46df-4197-993f-b6f14b7eac1a")
	)
	(wire
		(pts
			(xy 130.81 101.6) (xy 140.97 101.6)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5314ff4c-3cb7-4953-baa1-53f048a4a72e")
	)
	(wire
		(pts
			(xy 124.46 114.3) (xy 124.46 113.03)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5c374a3b-3973-4f3e-8add-dec1c7b6490e")
	)
	(wire
		(pts
			(xy 130.81 113.03) (xy 130.81 114.3)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "623c4693-f8b3-402d-8345-81b916125222")
	)
	(wire
		(pts
			(xy 130.81 78.74) (xy 130.81 101.6)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "70e29193-424a-4cf0-b1c8-a4589aaf3765")
	)
	(wire
		(pts
			(xy 130.81 111.76) (xy 130.81 113.03)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e3d5f97d-9dd7-436a-b727-a9d83313fa14")
	)
	(label "out2"
		(at 184.15 78.74 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "002283f4-0704-4937-909b-0f9fd7bf85fa")
	)
	(label "in"
		(at 130.81 78.74 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "549f8e1d-1695-4a5c-8563-3cc0f995f082")
	)
	(label "out1"
		(at 184.15 101.6 0)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "a66d8f67-4349-4f9d-8dde-027db865f987")
	)
	(symbol
		(lib_id "Device:R")
		(at 187.96 82.55 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "0fbfa0e4-d4ac-4eb4-ade0-4c8089a3dd72")
		(property "Reference" "R2"
			(at 189.738 81.9555 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "1K"
			(at 189.738 83.7177 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 186.182 82.55 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 187.96 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 187.96 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "26dcbae3-2848-4450-a6e9-9dcfb1c46164")
		)
		(pin "2"
			(uuid "3afd15d7-e1a2-4640-a3b5-e7f8e38c4db2")
		)
		(instances
			(project ""
				(path "/e63e39d7-6ac0-4ffd-8aa3-1841a4541b55"
					(reference "R2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R")
		(at 144.78 101.6 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "335263d3-7e35-4a9c-83c2-cd71d45f0688")
		(property "Reference" "R1"
			(at 144.78 97.7519 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "100"
			(at 144.78 99.5141 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 144.78 103.378 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 144.78 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 144.78 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "1354903a-b7d2-4e04-b220-6c6c8f058ef7")
		)
		(pin "2"
			(uuid "e0660a46-ff2a-4b28-b311-cf71bc999b82")
		)
		(instances
			(project ""
				(path "/e63e39d7-6ac0-4ffd-8aa3-1841a4541b55"
					(reference "R1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "project:VSOURCE")
		(at 130.81 106.68 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(uuid "5c8fbc47-6c66-4f9c-97ec-82cda511cebe")
		(property "Reference" "V1"
			(at 127.5588 107.8484 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "SINE(0 1.5 1k 0 0 0 0)"
			(at 127.5588 105.537 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 130.81 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Datasheet" ""
			(at 130.81 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Description" ""
			(at 130.81 106.68 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Fieldname" "Value"
			(at 130.81 106.68 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=1 2=2"
			(at 138.43 111.76 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "27183008-7d2c-4e22-b31a-c7ab7593bd3a")
		)
		(pin "2"
			(uuid "8701d230-c990-42af-bf98-0f0bb7fa088c")
		)
		(instances
			(project ""
				(path "/e63e39d7-6ac0-4ffd-8aa3-1841a4541b55"
					(reference "V1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R")
		(at 187.96 105.41 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "6df295b5-7825-454f-8c4b-423b0bc301cf")
		(property "Reference" "R3"
			(at 189.738 104.8155 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "1K"
			(at 189.738 106.5777 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 186.182 105.41 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 187.96 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 187.96 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "10fbe552-04cd-4144-a29a-f8128e66e140")
		)
		(pin "2"
			(uuid "dc5047f6-77e0-42aa-80e1-350f139635dc")
		)
		(instances
			(project ""
				(path "/e63e39d7-6ac0-4ffd-8aa3-1841a4541b55"
					(reference "R3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C")
		(at 148.59 105.41 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "8c7cfb57-e13d-4a3d-beac-498f9f4de146")
		(property "Reference" "C1"
			(at 151.511 104.8155 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "1u"
			(at 151.511 106.5777 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 149.5552 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 148.59 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 148.59 105.41 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "9a72e10b-191a-4fb1-a40f-b2297f01e083")
		)
		(pin "2"
			(uuid "a75142f2-7a9b-4c12-8657-e47f90013fdb")
		)
		(instances
			(project ""
				(path "/e63e39d7-6ac0-4ffd-8aa3-1841a4541b55"
					(reference "C1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 130.81 114.3 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "d0802183-04a8-4aad-b16e-6be404ca6ade")
		(property "Reference" "#PWR01"
			(at 130.81 120.65 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 130.81 118.11 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 130.81 114.3 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 130.81 114.3 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 130.81 114.3 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "19bf5bc5-4a27-4887-bde2-d0b015802be7")
		)
		(instances
			(project ""
				(path "/e63e39d7-6ac0-4ffd-8aa3-1841a4541b55"
					(reference "#PWR01")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 148.59 109.22 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "d2980117-00b9-4eaa-9fba-07086935c25b")
		(property "Reference" "#PWR02"
			(at 148.59 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 148.59 113.03 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 148.59 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 148.59 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 148.59 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "db0ec5a3-8a8a-4b13-a60d-fbb1914e57fb")
		)
		(instances
			(project ""
				(path "/e63e39d7-6ac0-4ffd-8aa3-1841a4541b55"
					(reference "#PWR02")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 187.96 86.36 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "e059c81f-70ae-4403-8e1f-f4a3c3eedd51")
		(property "Reference" "#PWR07"
			(at 187.96 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 187.96 91.44 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 187.96 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 187.96 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 187.96 86.36 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "fe60c974-5a0e-4a6d-8af5-c6274e6fbec4")
		)
		(instances
			(project ""
				(path "/e63e39d7-6ac0-4ffd-8aa3-1841a4541b55"
					(reference "#PWR07")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 187.96 109.22 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "e62d6f48-7372-40c9-90cd-a8a0127461e5")
		(property "Reference" "#PWR04"
			(at 187.96 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 187.96 113.03 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 187.96 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 187.96 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 187.96 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "6ea8f028-0708-48c1-a3f3-5647395c78da")
		)
		(instances
			(project ""
				(path "/e63e39d7-6ac0-4ffd-8aa3-1841a4541b55"
					(reference "#PWR04")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:PWR_FLAG")
		(at 124.46 114.3 180)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "f066344e-960d-418a-bb3b-25320f97b9e8")
		(property "Reference" "#FLG01"
			(at 124.46 116.205 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "PWR_FLAG"
			(at 124.46 119.38 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 124.46 114.3 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 124.46 114.3 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Special symbol for telling ERC where power comes from"
			(at 124.46 114.3 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "933f984d-d88f-4db5-81a7-70205613268d")
		)
		(instances
			(project ""
				(path "/e63e39d7-6ac0-4ffd-8aa3-1841a4541b55"
					(reference "#FLG01")
					(unit 1)
				)
			)
		)
	)
	(sheet
		(at 157.48 95.25)
		(size 13.97 13.97)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(stroke
			(width 0.1524)
			(type solid)
		)
		(fill
			(color 0 0 0 0.0000)
		)
		(uuid "51ab3a6c-36b1-4056-a2d2-39c83ee99c02")
		(property "Sheetname" "subsheet1"
			(at 157.48 94.5384 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left bottom)
			)
		)
		(property "Sheetfile" "subsheet1.kicad_sch"
			(at 157.48 109.8046 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left top)
			)
		)
		(pin "in" input
			(at 157.48 101.6 180)
			(uuid "7d34bd80-1948-4689-9586-e63e4b177b8e")
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(pin "out" output
			(at 171.45 101.6 0)
			(uuid "e983f7d1-388b-4bf0-ab6f-23264e0b4fbe")
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(instances
			(project "mainsheet"
				(path "/e63e39d7-6ac0-4ffd-8aa3-1841a4541b55"
					(page "2")
				)
			)
		)
	)
	(sheet
		(at 157.48 72.39)
		(size 13.97 13.97)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(stroke
			(width 0.1524)
			(type solid)
		)
		(fill
			(color 0 0 0 0.0000)
		)
		(uuid "cd8140cb-ee2c-44d2-bab6-19a75f861228")
		(property "Sheetname" "subsheet2"
			(at 157.48 71.6784 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left bottom)
			)
		)
		(property "Sheetfile" "subsheet2.kicad_sch"
			(at 157.48 86.9446 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left top)
			)
		)
		(pin "in" input
			(at 157.48 78.74 180)
			(uuid "29c3694b-40dc-4b25-afa7-f6f725269a9f")
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(pin "out" output
			(at 171.45 78.74 0)
			(uuid "7a633e69-b897-41f7-b106-0205738f3d20")
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(instances
			(project "mainsheet"
				(path "/e63e39d7-6ac0-4ffd-8aa3-1841a4541b55"
					(page "3")
				)
			)
		)
	)
	(sheet_instances
		(path "/"
			(page "1")
		)
	)
	(embedded_fonts no)
)
