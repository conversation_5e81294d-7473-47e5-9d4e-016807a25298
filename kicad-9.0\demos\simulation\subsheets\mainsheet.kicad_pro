{"board": {"3dviewports": [], "design_settings": {"defaults": {"board_outline_line_width": 0.1, "copper_line_width": 0.2, "copper_text_size_h": 1.5, "copper_text_size_v": 1.5, "copper_text_thickness": 0.3, "other_line_width": 0.15, "silk_line_width": 0.15, "silk_text_size_h": 1.0, "silk_text_size_v": 1.0, "silk_text_thickness": 0.15}, "diff_pair_dimensions": [], "drc_exclusions": [], "rules": {"solder_mask_clearance": 0.0, "solder_mask_min_width": 0.0}, "track_widths": [], "via_dimensions": []}, "ipc2581": {"dist": "", "distpn": "", "internal_id": "", "mfg": "", "mpn": ""}, "layer_pairs": [], "layer_presets": [], "viewports": []}, "boards": [], "cvpcb": {"equivalence_files": []}, "erc": {"erc_exclusions": [], "meta": {"version": 0}, "pin_map": [[0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2], [0, 2, 0, 1, 0, 0, 1, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 2], [0, 1, 0, 0, 0, 0, 1, 1, 2, 1, 1, 2], [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 2], [0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 2], [0, 2, 1, 2, 0, 0, 1, 0, 2, 2, 2, 2], [0, 2, 0, 1, 0, 0, 1, 0, 2, 0, 0, 2], [0, 2, 1, 1, 0, 0, 1, 0, 2, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "rule_severities": {"bus_definition_conflict": "error", "bus_entry_needed": "error", "bus_to_bus_conflict": "error", "bus_to_net_conflict": "error", "conflicting_netclasses": "error", "different_unit_footprint": "error", "different_unit_net": "error", "duplicate_reference": "error", "duplicate_sheet_names": "error", "endpoint_off_grid": "warning", "extra_units": "error", "footprint_filter": "ignore", "footprint_link_issues": "warning", "four_way_junction": "ignore", "global_label_dangling": "warning", "hier_label_mismatch": "error", "label_dangling": "error", "label_multiple_wires": "warning", "lib_symbol_issues": "warning", "lib_symbol_mismatch": "warning", "missing_bidi_pin": "warning", "missing_input_pin": "warning", "missing_power_pin": "error", "missing_unit": "warning", "multiple_net_names": "warning", "net_not_bus_member": "warning", "no_connect_connected": "warning", "no_connect_dangling": "warning", "pin_not_connected": "error", "pin_not_driven": "error", "pin_to_pin": "warning", "power_pin_not_driven": "error", "same_local_global_label": "warning", "similar_label_and_power": "warning", "similar_labels": "warning", "similar_power": "warning", "simulation_model_issue": "error", "single_global_label": "ignore", "unannotated": "error", "unconnected_wire_endpoint": "warning", "unit_value_mismatch": "error", "unresolved_variable": "error", "wire_dangling": "error"}}, "libraries": {"pinned_footprint_libs": [], "pinned_symbol_libs": []}, "meta": {"filename": "mainsheet.kicad_pro", "version": 2}, "net_settings": {"classes": [{"bus_width": 12, "clearance": 0.2, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "<PERSON><PERSON><PERSON>", "pcb_color": "rgba(0, 0, 0, 0.000)", "priority": 2147483647, "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.25, "via_diameter": 0.8, "via_drill": 0.4, "wire_width": 6}], "meta": {"version": 4}, "net_colors": null, "netclass_assignments": null, "netclass_patterns": []}, "pcbnew": {"last_paths": {"gencad": "", "idf": "", "netlist": "", "plot": "", "pos_files": "", "specctra_dsn": "", "step": "", "svg": "", "vrml": ""}, "page_layout_descr_file": ""}, "schematic": {"annotate_start_num": 0, "bom_export_filename": "${PROJECTNAME}.csv", "bom_fmt_presets": [], "bom_fmt_settings": {"field_delimiter": ",", "keep_line_breaks": false, "keep_tabs": false, "name": "CSV", "ref_delimiter": ",", "ref_range_delimiter": "", "string_delimiter": "\""}, "bom_presets": [], "bom_settings": {"exclude_dnp": false, "fields_ordered": [{"group_by": false, "label": "Reference", "name": "Reference", "show": true}, {"group_by": false, "label": "Qty", "name": "${QUANTITY}", "show": true}, {"group_by": true, "label": "Value", "name": "Value", "show": true}, {"group_by": true, "label": "DNP", "name": "${DNP}", "show": true}, {"group_by": true, "label": "Exclude from BOM", "name": "${EXCLUDE_FROM_BOM}", "show": true}, {"group_by": true, "label": "Exclude from Board", "name": "${EXCLUDE_FROM_BOARD}", "show": true}, {"group_by": true, "label": "Footprint", "name": "Footprint", "show": true}, {"group_by": false, "label": "Datasheet", "name": "Datasheet", "show": true}], "filter_string": "", "group_symbols": true, "include_excluded_from_bom": true, "name": "Default Editing", "sort_asc": true, "sort_field": "Référence"}, "connection_grid_size": 50.0, "drawing": {"dashed_lines_dash_length_ratio": 12.0, "dashed_lines_gap_length_ratio": 3.0, "default_bus_thickness": 12.0, "default_junction_size": 40.0, "default_line_thickness": 6.0, "default_text_size": 50.0, "default_wire_thickness": 6.0, "field_names": [], "intersheets_ref_own_page": false, "intersheets_ref_prefix": "", "intersheets_ref_short": false, "intersheets_ref_show": false, "intersheets_ref_suffix": "", "junction_size_choice": 3, "label_size_ratio": 0.3, "operating_point_overlay_i_precision": 3, "operating_point_overlay_i_range": "~A", "operating_point_overlay_v_precision": 3, "operating_point_overlay_v_range": "~V", "overbar_offset_ratio": 1.23, "pin_symbol_size": 25.0, "text_offset_ratio": 0.3}, "legacy_lib_dir": "", "legacy_lib_list": [], "meta": {"version": 1}, "net_format_name": "", "ngspice": {"fix_include_paths": true, "fix_passive_vals": false, "meta": {"version": 0}, "model_mode": 4, "workbook_filename": ""}, "page_layout_descr_file": "", "plot_directory": "", "space_save_all_events": true, "spice_adjust_passive_values": false, "spice_current_sheet_as_root": false, "spice_external_command": "spice \"%I\"", "spice_model_current_sheet_as_root": true, "spice_save_all_currents": false, "spice_save_all_dissipations": false, "spice_save_all_voltages": false, "subpart_first_id": 65, "subpart_id_separator": 0}, "sheets": [["e63e39d7-6ac0-4ffd-8aa3-1841a4541b55", "Root"], ["51ab3a6c-36b1-4056-a2d2-39c83ee99c02", "subsheet1"], ["cd8140cb-ee2c-44d2-bab6-19a75f861228", "subsheet2"]], "text_variables": {}}