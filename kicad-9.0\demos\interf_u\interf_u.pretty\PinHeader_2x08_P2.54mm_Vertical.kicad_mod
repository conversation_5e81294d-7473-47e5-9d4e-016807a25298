(footprint "PinHeader_2x08_P2.54mm_Vertical"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "Through hole straight pin header, 2x08, 2.54mm pitch, double rows")
	(tags "Through hole pin header THT 2x08 2.54mm double row")
	(property "Reference" "REF**"
		(at 0 -5.1 0)
		(layer "F.SilkS")
		(uuid "4688aca9-498f-4e2b-9bb8-c9498371e9d0")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "CONN_8X2"
		(at 0 -3.1 0)
		(layer "F.Fab")
		(hide yes)
		(uuid "03069b17-c945-43c0-85ab-eaa0d9e9a0d9")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "07d23a34-3ddf-4002-8af0-44ea80a7a947")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" "Generic connector, double row, 02x08, odd/even pin numbering scheme (row 1 odd numbers, row 2 even numbers)"
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "b9f771cc-9d54-4cb0-9ab8-4ccad72a781e")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -1.33 -1.33)
		(end 0 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "476223f3-d6c5-4f46-944d-d4bd60362476")
	)
	(fp_line
		(start -1.33 0)
		(end -1.33 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "78cb0748-5433-440f-8112-7612780eea36")
	)
	(fp_line
		(start -1.33 1.27)
		(end -1.33 19.11)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "efd217b1-813f-43e6-a969-53a318dd431e")
	)
	(fp_line
		(start -1.33 1.27)
		(end 1.27 1.27)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "0168c99b-c8a9-457f-9a24-20bfa61b7343")
	)
	(fp_line
		(start -1.33 19.11)
		(end 3.87 19.11)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "9a00bd9e-d16b-47fd-a2b3-4d815a253529")
	)
	(fp_line
		(start 1.27 -1.33)
		(end 3.87 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "c90286ca-df8e-4892-9e30-57e91adef5d5")
	)
	(fp_line
		(start 1.27 1.27)
		(end 1.27 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "06ecdfcf-dbdf-42b4-a6fe-a9c5b015da26")
	)
	(fp_line
		(start 3.87 -1.33)
		(end 3.87 19.11)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "6d7d363e-9caf-490f-ba86-911f8a3d54ac")
	)
	(fp_line
		(start -1.8 -1.8)
		(end -1.8 19.55)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "c728db84-e628-480b-a011-d05f2a85a86c")
	)
	(fp_line
		(start -1.8 19.55)
		(end 4.35 19.55)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "8c7a9c55-b52e-45ef-961a-e5996957f8a5")
	)
	(fp_line
		(start 4.35 -1.8)
		(end -1.8 -1.8)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "14b6d753-f8e2-49b7-aa28-d8739c81557b")
	)
	(fp_line
		(start 4.35 19.55)
		(end 4.35 -1.8)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "2ae6ca6e-b7a0-4d22-837b-e02477690e0c")
	)
	(fp_line
		(start -1.27 0)
		(end 0 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "3311c5fb-199d-4797-bb04-5dbdab469837")
	)
	(fp_line
		(start -1.27 19.05)
		(end -1.27 0)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "84a06401-0939-4b7c-b53d-1ab10394be74")
	)
	(fp_line
		(start 0 -1.27)
		(end 3.81 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "b12b6d51-4834-4879-b115-f15c86edbf10")
	)
	(fp_line
		(start 3.81 -1.27)
		(end 3.81 19.05)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "1790b1cd-a59d-4fa5-b6e3-5d0c8c0c3017")
	)
	(fp_line
		(start 3.81 19.05)
		(end -1.27 19.05)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "83a9c9b8-c419-4e31-9c0b-f2c3a4390841")
	)
	(fp_text user "${REFERENCE}"
		(at 1.27 8.89 90)
		(layer "F.Fab")
		(uuid "a1c5c288-5bff-4838-bc8b-6edde2fcfcd7")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "615c560f-6d48-44c9-906a-4646dfb53188")
	)
	(pad "2" thru_hole oval
		(at 2.54 0)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "fec4cba0-8c11-48de-8d22-97aaaaa4ef7e")
	)
	(pad "3" thru_hole oval
		(at 0 2.54)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0e058ffe-41c5-41c2-93a8-7bbadae9a2dd")
	)
	(pad "4" thru_hole oval
		(at 2.54 2.54)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "935bc8a5-998a-4774-8536-4094771834f2")
	)
	(pad "5" thru_hole oval
		(at 0 5.08)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "fc31b17f-4caf-4da2-b38c-0be8fb57a6e4")
	)
	(pad "6" thru_hole oval
		(at 2.54 5.08)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "27f45ef4-9a51-4948-96eb-0e3c59173c8c")
	)
	(pad "7" thru_hole oval
		(at 0 7.62)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "450f2ac6-62d7-457c-90a0-e88b10604afd")
	)
	(pad "8" thru_hole oval
		(at 2.54 7.62)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c0c81eb8-8c49-49e4-959f-a9854b405ae3")
	)
	(pad "9" thru_hole oval
		(at 0 10.16)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7962b6f0-65a0-47f4-8409-d1c44aea8d78")
	)
	(pad "10" thru_hole oval
		(at 2.54 10.16)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "572d0d02-3bca-4a5a-86e6-90d6ea87c89f")
	)
	(pad "11" thru_hole oval
		(at 0 12.7)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "23d98030-61ac-49cf-8569-a4c2414ec43a")
	)
	(pad "12" thru_hole oval
		(at 2.54 12.7)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "a9ce0dbb-57dc-4699-a55d-c169042216ec")
	)
	(pad "13" thru_hole oval
		(at 0 15.24)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "e6ef4d05-1936-4c0b-9d50-5364af79a22b")
	)
	(pad "14" thru_hole oval
		(at 2.54 15.24)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "2bf26ee3-5493-4d93-81db-0caa3b11c062")
	)
	(pad "15" thru_hole oval
		(at 0 17.78)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "bce068ca-4777-4154-873c-5b7358a6a555")
	)
	(pad "16" thru_hole oval
		(at 2.54 17.78)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f1a67f33-db6d-47de-81d8-0b9947afd2d0")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_2x08_P2.54mm_Vertical.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
