<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<wxFormBuilder_Project>
    <FileVersion major="1" minor="16" />
    <object class="Project" expanded="1">
        <property name="class_decoration"></property>
        <property name="code_generation">C++</property>
        <property name="disconnect_events">1</property>
        <property name="disconnect_mode">source_name</property>
        <property name="disconnect_php_events">0</property>
        <property name="disconnect_python_events">0</property>
        <property name="embedded_files_path">res</property>
        <property name="encoding">UTF-8</property>
        <property name="event_generation">connect</property>
        <property name="file">panel_data_collection_base</property>
        <property name="first_id">1000</property>
        <property name="help_provider">none</property>
        <property name="image_path_wrapper_function_name"></property>
        <property name="indent_with_spaces"></property>
        <property name="internationalize">1</property>
        <property name="name">PanelDataCollection</property>
        <property name="namespace"></property>
        <property name="path">.</property>
        <property name="precompiled_header"></property>
        <property name="relative_path">1</property>
        <property name="skip_lua_events">1</property>
        <property name="skip_php_events">1</property>
        <property name="skip_python_events">1</property>
        <property name="ui_table">UI</property>
        <property name="use_array_enum">0</property>
        <property name="use_enum">1</property>
        <property name="use_microsoft_bom">0</property>
        <object class="Panel" expanded="1">
            <property name="aui_managed">0</property>
            <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
            <property name="bg"></property>
            <property name="context_help"></property>
            <property name="context_menu">1</property>
            <property name="enabled">1</property>
            <property name="event_handler">impl_virtual</property>
            <property name="fg"></property>
            <property name="font"></property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="maximum_size"></property>
            <property name="minimum_size"></property>
            <property name="name">PANEL_DATA_COLLECTION_BASE</property>
            <property name="pos"></property>
            <property name="size">-1,-1</property>
            <property name="subclass">RESETTABLE_PANEL; widgets/resettable_panel.h; Not forward_declare</property>
            <property name="tooltip"></property>
            <property name="two_step_creation">0</property>
            <property name="window_extra_style"></property>
            <property name="window_name"></property>
            <property name="window_style">wxTAB_TRAVERSAL</property>
            <object class="wxBoxSizer" expanded="1">
                <property name="minimum_size"></property>
                <property name="name">bPanelSizer</property>
                <property name="orient">wxHORIZONTAL</property>
                <property name="permission">none</property>
                <object class="sizeritem" expanded="1">
                    <property name="border">5</property>
                    <property name="flag">wxEXPAND</property>
                    <property name="proportion">1</property>
                    <object class="wxBoxSizer" expanded="1">
                        <property name="minimum_size"></property>
                        <property name="name">bSizer8</property>
                        <property name="orient">wxVERTICAL</property>
                        <property name="permission">none</property>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxALL</property>
                            <property name="proportion">0</property>
                            <object class="wxStaticText" expanded="1">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="close_button">1</property>
                                <property name="context_help"></property>
                                <property name="context_menu">1</property>
                                <property name="default_pane">0</property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="label">KiCad can anonymously report crashes and special event data to developers in order to aid identifying critical bugs across the user base more effectively and help profile functionality to guide improvements.&#x0A;&#x0A;To link automatic reports from the same KiCad install, a unique identifier is generated that is completely random, and is only used for the purposes of crash reporting. No personally identifiable information (PII) including IP address is stored or connected to this identifier. You may reset this id at anytime with the button provided.&#x0A;&#x0A;If you choose to voluntarily participate, KiCad will automatically handle sending said reports when crashes or events occur. Your design files such as schematic or PCB are never shared in this process.</property>
                                <property name="markup">0</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size"></property>
                                <property name="moveable">1</property>
                                <property name="name">m_stExplanation</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">1</property>
                                <property name="pos"></property>
                                <property name="resize">Resizable</property>
                                <property name="show">1</property>
                                <property name="size"></property>
                                <property name="style"></property>
                                <property name="subclass">; ; forward_declare</property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style"></property>
                                <property name="wrap">500</property>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxALL</property>
                            <property name="proportion">0</property>
                            <object class="wxCheckBox" expanded="1">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="checked">0</property>
                                <property name="close_button">1</property>
                                <property name="context_help"></property>
                                <property name="context_menu">1</property>
                                <property name="default_pane">0</property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="label">I agree to provide anonymous reports</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size"></property>
                                <property name="moveable">1</property>
                                <property name="name">m_cbOptIn</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">1</property>
                                <property name="pos"></property>
                                <property name="resize">Resizable</property>
                                <property name="show">1</property>
                                <property name="size"></property>
                                <property name="style"></property>
                                <property name="subclass">; ; forward_declare</property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip"></property>
                                <property name="validator_data_type"></property>
                                <property name="validator_style">wxFILTER_NONE</property>
                                <property name="validator_type">wxDefaultValidator</property>
                                <property name="validator_variable"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style"></property>
                            </object>
                        </object>
                        <object class="sizeritem" expanded="1">
                            <property name="border">5</property>
                            <property name="flag">wxEXPAND</property>
                            <property name="proportion">1</property>
                            <object class="wxBoxSizer" expanded="1">
                                <property name="minimum_size"></property>
                                <property name="name">bSizer3</property>
                                <property name="orient">wxHORIZONTAL</property>
                                <property name="permission">none</property>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxALL</property>
                                    <property name="proportion">0</property>
                                    <object class="wxTextCtrl" expanded="1">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="default_pane">0</property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="maxlength"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size">340,-1</property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_sentryUid</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style">wxTE_READONLY</property>
                                        <property name="subclass">; ; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="validator_data_type"></property>
                                        <property name="validator_style">wxFILTER_NONE</property>
                                        <property name="validator_type">wxDefaultValidator</property>
                                        <property name="validator_variable"></property>
                                        <property name="value"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                    </object>
                                </object>
                                <object class="sizeritem" expanded="1">
                                    <property name="border">5</property>
                                    <property name="flag">wxALL</property>
                                    <property name="proportion">0</property>
                                    <object class="wxButton" expanded="1">
                                        <property name="BottomDockable">1</property>
                                        <property name="LeftDockable">1</property>
                                        <property name="RightDockable">1</property>
                                        <property name="TopDockable">1</property>
                                        <property name="aui_layer"></property>
                                        <property name="aui_name"></property>
                                        <property name="aui_position"></property>
                                        <property name="aui_row"></property>
                                        <property name="auth_needed">0</property>
                                        <property name="best_size"></property>
                                        <property name="bg"></property>
                                        <property name="bitmap"></property>
                                        <property name="caption"></property>
                                        <property name="caption_visible">1</property>
                                        <property name="center_pane">0</property>
                                        <property name="close_button">1</property>
                                        <property name="context_help"></property>
                                        <property name="context_menu">1</property>
                                        <property name="current"></property>
                                        <property name="default">0</property>
                                        <property name="default_pane">0</property>
                                        <property name="disabled"></property>
                                        <property name="dock">Dock</property>
                                        <property name="dock_fixed">0</property>
                                        <property name="docking">Left</property>
                                        <property name="enabled">1</property>
                                        <property name="fg"></property>
                                        <property name="floatable">1</property>
                                        <property name="focus"></property>
                                        <property name="font"></property>
                                        <property name="gripper">0</property>
                                        <property name="hidden">0</property>
                                        <property name="id">wxID_ANY</property>
                                        <property name="label">Reset Unique Id</property>
                                        <property name="margins"></property>
                                        <property name="markup">0</property>
                                        <property name="max_size"></property>
                                        <property name="maximize_button">0</property>
                                        <property name="maximum_size"></property>
                                        <property name="min_size"></property>
                                        <property name="minimize_button">0</property>
                                        <property name="minimum_size"></property>
                                        <property name="moveable">1</property>
                                        <property name="name">m_buttonResetId</property>
                                        <property name="pane_border">1</property>
                                        <property name="pane_position"></property>
                                        <property name="pane_size"></property>
                                        <property name="permission">protected</property>
                                        <property name="pin_button">1</property>
                                        <property name="pos"></property>
                                        <property name="position"></property>
                                        <property name="pressed"></property>
                                        <property name="resize">Resizable</property>
                                        <property name="show">1</property>
                                        <property name="size"></property>
                                        <property name="style"></property>
                                        <property name="subclass">; ; forward_declare</property>
                                        <property name="toolbar_pane">0</property>
                                        <property name="tooltip"></property>
                                        <property name="validator_data_type"></property>
                                        <property name="validator_style">wxFILTER_NONE</property>
                                        <property name="validator_type">wxDefaultValidator</property>
                                        <property name="validator_variable"></property>
                                        <property name="window_extra_style"></property>
                                        <property name="window_name"></property>
                                        <property name="window_style"></property>
                                        <event name="OnButtonClick">OnResetIdClick</event>
                                    </object>
                                </object>
                            </object>
                        </object>
                    </object>
                </object>
            </object>
        </object>
    </object>
</wxFormBuilder_Project>
