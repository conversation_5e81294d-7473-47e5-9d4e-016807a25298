(kicad_symbol_lib (version 20211014) (generator kicad_symbol_editor)
  (symbol "<PERSON><PERSON>" (in_bom yes) (on_board yes)
    (property "Reference" "U" (id 0) (at -13.97 27.94 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "Pico" (id 1) (at 0 19.05 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "RPi_Pico:RPi_Pico_SMD_TH" (id 2) (at 0 0 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "Pico_0_0"
      (text "Raspberry Pi Pico" (at 0 21.59 0)
        (effects (font (size 1.27 1.27)))
      )
    )
    (symbol "Pico_0_1"
      (rectangle (start -15.24 26.67) (end 15.24 -26.67)
        (stroke (width 0) (type default) (color 0 0 0 0))
        (fill (type background))
      )
    )
    (symbol "<PERSON>co_1_1"
      (pin bidirectional line (at -17.78 24.13 0) (length 2.54)
        (name "GPIO0" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 1.27 0) (length 2.54)
        (name "GPIO7" (effects (font (size 1.27 1.27))))
        (number "10" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 -1.27 0) (length 2.54)
        (name "GPIO8" (effects (font (size 1.27 1.27))))
        (number "11" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 -3.81 0) (length 2.54)
        (name "GPIO9" (effects (font (size 1.27 1.27))))
        (number "12" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at -17.78 -6.35 0) (length 2.54)
        (name "GND" (effects (font (size 1.27 1.27))))
        (number "13" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 -8.89 0) (length 2.54)
        (name "GPIO10" (effects (font (size 1.27 1.27))))
        (number "14" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 -11.43 0) (length 2.54)
        (name "GPIO11" (effects (font (size 1.27 1.27))))
        (number "15" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 -13.97 0) (length 2.54)
        (name "GPIO12" (effects (font (size 1.27 1.27))))
        (number "16" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 -16.51 0) (length 2.54)
        (name "GPIO13" (effects (font (size 1.27 1.27))))
        (number "17" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at -17.78 -19.05 0) (length 2.54)
        (name "GND" (effects (font (size 1.27 1.27))))
        (number "18" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 -21.59 0) (length 2.54)
        (name "GPIO14" (effects (font (size 1.27 1.27))))
        (number "19" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 21.59 0) (length 2.54)
        (name "GPIO1" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 -24.13 0) (length 2.54)
        (name "GPIO15" (effects (font (size 1.27 1.27))))
        (number "20" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 17.78 -24.13 180) (length 2.54)
        (name "GPIO16" (effects (font (size 1.27 1.27))))
        (number "21" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 17.78 -21.59 180) (length 2.54)
        (name "GPIO17" (effects (font (size 1.27 1.27))))
        (number "22" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at 17.78 -19.05 180) (length 2.54)
        (name "GND" (effects (font (size 1.27 1.27))))
        (number "23" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 17.78 -16.51 180) (length 2.54)
        (name "GPIO18" (effects (font (size 1.27 1.27))))
        (number "24" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 17.78 -13.97 180) (length 2.54)
        (name "GPIO19" (effects (font (size 1.27 1.27))))
        (number "25" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 17.78 -11.43 180) (length 2.54)
        (name "GPIO20" (effects (font (size 1.27 1.27))))
        (number "26" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 17.78 -8.89 180) (length 2.54)
        (name "GPIO21" (effects (font (size 1.27 1.27))))
        (number "27" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at 17.78 -6.35 180) (length 2.54)
        (name "GND" (effects (font (size 1.27 1.27))))
        (number "28" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 17.78 -3.81 180) (length 2.54)
        (name "GPIO22" (effects (font (size 1.27 1.27))))
        (number "29" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at -17.78 19.05 0) (length 2.54)
        (name "GND" (effects (font (size 1.27 1.27))))
        (number "3" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at 17.78 -1.27 180) (length 2.54)
        (name "RUN" (effects (font (size 1.27 1.27))))
        (number "30" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 17.78 1.27 180) (length 2.54)
        (name "GPIO26_ADC0" (effects (font (size 1.27 1.27))))
        (number "31" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 17.78 3.81 180) (length 2.54)
        (name "GPIO27_ADC1" (effects (font (size 1.27 1.27))))
        (number "32" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at 17.78 6.35 180) (length 2.54)
        (name "AGND" (effects (font (size 1.27 1.27))))
        (number "33" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 17.78 8.89 180) (length 2.54)
        (name "GPIO28_ADC2" (effects (font (size 1.27 1.27))))
        (number "34" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at 17.78 11.43 180) (length 2.54)
        (name "ADC_VREF" (effects (font (size 1.27 1.27))))
        (number "35" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at 17.78 13.97 180) (length 2.54)
        (name "3V3" (effects (font (size 1.27 1.27))))
        (number "36" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at 17.78 16.51 180) (length 2.54)
        (name "3V3_EN" (effects (font (size 1.27 1.27))))
        (number "37" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 17.78 19.05 180) (length 2.54)
        (name "GND" (effects (font (size 1.27 1.27))))
        (number "38" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at 17.78 21.59 180) (length 2.54)
        (name "VSYS" (effects (font (size 1.27 1.27))))
        (number "39" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 16.51 0) (length 2.54)
        (name "GPIO2" (effects (font (size 1.27 1.27))))
        (number "4" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at 17.78 24.13 180) (length 2.54)
        (name "VBUS" (effects (font (size 1.27 1.27))))
        (number "40" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -2.54 -29.21 90) (length 2.54)
        (name "SWCLK" (effects (font (size 1.27 1.27))))
        (number "41" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at 0 -29.21 90) (length 2.54)
        (name "GND" (effects (font (size 1.27 1.27))))
        (number "42" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 2.54 -29.21 90) (length 2.54)
        (name "SWDIO" (effects (font (size 1.27 1.27))))
        (number "43" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 13.97 0) (length 2.54)
        (name "GPIO3" (effects (font (size 1.27 1.27))))
        (number "5" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 11.43 0) (length 2.54)
        (name "GPIO4" (effects (font (size 1.27 1.27))))
        (number "6" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 8.89 0) (length 2.54)
        (name "GPIO5" (effects (font (size 1.27 1.27))))
        (number "7" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at -17.78 6.35 0) (length 2.54)
        (name "GND" (effects (font (size 1.27 1.27))))
        (number "8" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -17.78 3.81 0) (length 2.54)
        (name "GPIO6" (effects (font (size 1.27 1.27))))
        (number "9" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "RP2040" (pin_names (offset 1.016)) (in_bom yes) (on_board yes)
    (property "Reference" "U" (id 0) (at -29.21 49.53 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "RP2040" (id 1) (at 24.13 -49.53 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "RP2040_minimal:RP2040-QFN-56" (id 2) (at -19.05 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at -19.05 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "RP2040_0_0"
      (text "Raspberry Pi" (at 0 5.08 0)
        (effects (font (size 2.54 2.54)))
      )
      (text "RP2040" (at 0 0 0)
        (effects (font (size 2.54 2.54)))
      )
    )
    (symbol "RP2040_0_1"
      (rectangle (start 29.21 48.26) (end -29.21 -48.26)
        (stroke (width 0.254) (type default) (color 0 0 0 0))
        (fill (type background))
      )
    )
    (symbol "RP2040_1_1"
      (pin power_in line (at 8.89 50.8 270) (length 2.54)
        (name "IOVDD" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at 6.35 50.8 270) (length 2.54)
        (name "IOVDD" (effects (font (size 1.27 1.27))))
        (number "10" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 12.7 180) (length 2.54)
        (name "GPIO8" (effects (font (size 1.27 1.27))))
        (number "11" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 10.16 180) (length 2.54)
        (name "GPIO9" (effects (font (size 1.27 1.27))))
        (number "12" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 7.62 180) (length 2.54)
        (name "GPIO10" (effects (font (size 1.27 1.27))))
        (number "13" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 5.08 180) (length 2.54)
        (name "GPIO11" (effects (font (size 1.27 1.27))))
        (number "14" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 2.54 180) (length 2.54)
        (name "GPIO12" (effects (font (size 1.27 1.27))))
        (number "15" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 0 180) (length 2.54)
        (name "GPIO13" (effects (font (size 1.27 1.27))))
        (number "16" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -2.54 180) (length 2.54)
        (name "GPIO14" (effects (font (size 1.27 1.27))))
        (number "17" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -5.08 180) (length 2.54)
        (name "GPIO15" (effects (font (size 1.27 1.27))))
        (number "18" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at -12.7 -50.8 90) (length 2.54)
        (name "TESTEN" (effects (font (size 1.27 1.27))))
        (number "19" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 33.02 180) (length 2.54)
        (name "GPIO0" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -31.75 -2.54 0) (length 2.54)
        (name "XIN" (effects (font (size 1.27 1.27))))
        (number "20" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at -31.75 -7.62 0) (length 2.54)
        (name "XOUT" (effects (font (size 1.27 1.27))))
        (number "21" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at 3.81 50.8 270) (length 2.54)
        (name "IOVDD" (effects (font (size 1.27 1.27))))
        (number "22" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at -17.78 50.8 270) (length 2.54)
        (name "DVDD" (effects (font (size 1.27 1.27))))
        (number "23" (effects (font (size 1.27 1.27))))
      )
      (pin output line (at -31.75 -31.75 0) (length 2.54)
        (name "SWCLK" (effects (font (size 1.27 1.27))))
        (number "24" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -31.75 -34.29 0) (length 2.54)
        (name "SWD" (effects (font (size 1.27 1.27))))
        (number "25" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -31.75 -20.32 0) (length 2.54)
        (name "RUN" (effects (font (size 1.27 1.27))))
        (number "26" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -7.62 180) (length 2.54)
        (name "GPIO16" (effects (font (size 1.27 1.27))))
        (number "27" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -10.16 180) (length 2.54)
        (name "GPIO17" (effects (font (size 1.27 1.27))))
        (number "28" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -12.7 180) (length 2.54)
        (name "GPIO18" (effects (font (size 1.27 1.27))))
        (number "29" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 30.48 180) (length 2.54)
        (name "GPIO1" (effects (font (size 1.27 1.27))))
        (number "3" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -15.24 180) (length 2.54)
        (name "GPIO19" (effects (font (size 1.27 1.27))))
        (number "30" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -17.78 180) (length 2.54)
        (name "GPIO20" (effects (font (size 1.27 1.27))))
        (number "31" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -20.32 180) (length 2.54)
        (name "GPIO21" (effects (font (size 1.27 1.27))))
        (number "32" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at 1.27 50.8 270) (length 2.54)
        (name "IOVDD" (effects (font (size 1.27 1.27))))
        (number "33" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -22.86 180) (length 2.54)
        (name "GPIO22" (effects (font (size 1.27 1.27))))
        (number "34" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -25.4 180) (length 2.54)
        (name "GPIO23" (effects (font (size 1.27 1.27))))
        (number "35" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -27.94 180) (length 2.54)
        (name "GPIO24" (effects (font (size 1.27 1.27))))
        (number "36" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -30.48 180) (length 2.54)
        (name "GPIO25" (effects (font (size 1.27 1.27))))
        (number "37" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -35.56 180) (length 2.54)
        (name "GPIO26_ADC0" (effects (font (size 1.27 1.27))))
        (number "38" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -38.1 180) (length 2.54)
        (name "GPIO27_ADC1" (effects (font (size 1.27 1.27))))
        (number "39" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 27.94 180) (length 2.54)
        (name "GPIO2" (effects (font (size 1.27 1.27))))
        (number "4" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -40.64 180) (length 2.54)
        (name "GPIO28_ADC2" (effects (font (size 1.27 1.27))))
        (number "40" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 -43.18 180) (length 2.54)
        (name "GPIO29_ADC3" (effects (font (size 1.27 1.27))))
        (number "41" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at -1.27 50.8 270) (length 2.54)
        (name "IOVDD" (effects (font (size 1.27 1.27))))
        (number "42" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at 16.51 50.8 270) (length 2.54)
        (name "ADC_AVDD" (effects (font (size 1.27 1.27))))
        (number "43" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at -8.89 50.8 270) (length 2.54)
        (name "VREG_IN" (effects (font (size 1.27 1.27))))
        (number "44" (effects (font (size 1.27 1.27))))
      )
      (pin power_out line (at -12.7 50.8 270) (length 2.54)
        (name "VREG_VOUT" (effects (font (size 1.27 1.27))))
        (number "45" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 40.64 180) (length 2.54)
        (name "USB_DM" (effects (font (size 1.27 1.27))))
        (number "46" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 43.18 180) (length 2.54)
        (name "USB_DP" (effects (font (size 1.27 1.27))))
        (number "47" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at 12.7 50.8 270) (length 2.54)
        (name "USB_VDD" (effects (font (size 1.27 1.27))))
        (number "48" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at -3.81 50.8 270) (length 2.54)
        (name "IOVDD" (effects (font (size 1.27 1.27))))
        (number "49" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 25.4 180) (length 2.54)
        (name "GPIO3" (effects (font (size 1.27 1.27))))
        (number "5" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at -20.32 50.8 270) (length 2.54)
        (name "DVDD" (effects (font (size 1.27 1.27))))
        (number "50" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -31.75 20.32 0) (length 2.54)
        (name "QSPI_SD3" (effects (font (size 1.27 1.27))))
        (number "51" (effects (font (size 1.27 1.27))))
      )
      (pin output line (at -31.75 16.51 0) (length 2.54)
        (name "QSPI_SCLK" (effects (font (size 1.27 1.27))))
        (number "52" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -31.75 27.94 0) (length 2.54)
        (name "QSPI_SD0" (effects (font (size 1.27 1.27))))
        (number "53" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -31.75 22.86 0) (length 2.54)
        (name "QSPI_SD2" (effects (font (size 1.27 1.27))))
        (number "54" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -31.75 25.4 0) (length 2.54)
        (name "QSPI_SD1" (effects (font (size 1.27 1.27))))
        (number "55" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at -31.75 31.75 0) (length 2.54)
        (name "QSPI_SS" (effects (font (size 1.27 1.27))))
        (number "56" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at 0 -50.8 90) (length 2.54)
        (name "GND" (effects (font (size 1.27 1.27))))
        (number "57" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 22.86 180) (length 2.54)
        (name "GPIO4" (effects (font (size 1.27 1.27))))
        (number "6" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 20.32 180) (length 2.54)
        (name "GPIO5" (effects (font (size 1.27 1.27))))
        (number "7" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 17.78 180) (length 2.54)
        (name "GPIO6" (effects (font (size 1.27 1.27))))
        (number "8" (effects (font (size 1.27 1.27))))
      )
      (pin bidirectional line (at 31.75 15.24 180) (length 2.54)
        (name "GPIO7" (effects (font (size 1.27 1.27))))
        (number "9" (effects (font (size 1.27 1.27))))
      )
    )
  )
)
