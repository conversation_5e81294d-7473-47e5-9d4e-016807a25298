(footprint "RPi_Pico_SMD_TH" (version 20211014) (generator pcbnew)
  (layer "F.Cu")
  (tedit 6224DF39)
  (descr "Through hole straight pin header, 2x20, 2.54mm pitch, double rows")
  (tags "Through hole pin header THT 2x20 2.54mm double row")
  (attr through_hole)
  (fp_text reference "REF**" (at 0 0) (layer "F.SilkS")
    (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 96315415-cfed-47d2-b3dd-d782358bd0df)
  )
  (fp_text value "RPi_Pico_SMD_TH" (at 0 2.159) (layer "F.Fab")
    (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 46cbe85d-ff47-428e-b187-4ebd50a66e0c)
  )
  (fp_text user "GP11" (at -13.2 11.43 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 000b46d6-b833-4804-8f56-56d539f76d09)
  )
  (fp_text user "AGND" (at 13.054 -6.35 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 08ec951f-e7eb-41cf-9589-697107a98e88)
  )
  (fp_text user "GND" (at 12.8 19.05 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 09bbea88-8bd7-48ec-baae-1b4a9a11a40e)
  )
  (fp_text user "VBUS" (at 13.3 -24.2 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 0f0f7bb5-ade7-4a81-82b4-43be6a8ad05c)
  )
  (fp_text user "GND" (at 12.8 -19.05 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 0fb27e11-fde6-4a25-adbb-e9684771b369)
  )
  (fp_text user "GP13" (at -13.054 16.51 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 113ffcdf-4c54-4e37-81dc-f91efa934ba7)
  )
  (fp_text user "ADC_VREF" (at 14 -12.5 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 162e5bdd-61a8-46a3-8485-826b5d58e1a1)
  )
  (fp_text user "GP4" (at -12.8 -11.43 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 1cacb878-9da4-41fc-aa80-018bc841e19a)
  )
  (fp_text user "GP7" (at -12.7 -1.3 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 1de61170-5337-44c5-ba28-bd477db4bff1)
  )
  (fp_text user "GP15" (at -13.054 24.13 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 2102c637-9f11-48f1-aae6-b4139dc22be2)
  )
  (fp_text user "GP17" (at 13.054 21.59 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 272c2a78-b5f5-4b61-aed3-ec69e0e92729)
  )
  (fp_text user "GP26" (at 13.054 -1.27 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 2b25e886-ded1-450a-ada1-ece4208052e4)
  )
  (fp_text user "SWCLK" (at -5.7 26.2) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 2eea20e6-112c-411a-b615-885ae773135a)
  )
  (fp_text user "3V3_EN" (at 13.7 -17.2 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 2f3fba7a-cf45-4bd8-9035-07e6fa0b4732)
  )
  (fp_text user "3V3" (at 12.9 -13.9 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 319c683d-aed6-4e7d-aee2-ff9871746d52)
  )
  (fp_text user "GP8" (at -12.8 1.27 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 3a1a39fc-8030-4c93-9d9c-d79ba6824099)
  )
  (fp_text user "GP16" (at 13.054 24.13 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 3f2a6679-91d7-4b6c-bf5c-c4d5abb2bc44)
  )
  (fp_text user "GND" (at 12.8 6.35 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 41c18011-40db-4384-9ba4-c0158d0d9d6a)
  )
  (fp_text user "GND" (at -12.8 -6.35 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 4346fe55-f906-453a-b81a-1c013104a598)
  )
  (fp_text user "GP28" (at 13.054 -9.144 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 456c5e47-d71e-4708-b061-1e61634d8648)
  )
  (fp_text user "GP9" (at -12.8 3.81 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 49b5f540-e128-4e08-bb09-f321f8e64056)
  )
  (fp_text user "SWDIO" (at 5.6 26.2) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 49fec31e-3712-4229-8142-b191d90a97d0)
  )
  (fp_text user "GP5" (at -12.8 -8.89 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 4ce9470f-5633-41bf-89ac-74a810939893)
  )
  (fp_text user "GP0" (at -12.8 -24.13 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 51cc007a-3378-4ce3-909c-71e94822f8d1)
  )
  (fp_text user "GP3" (at -12.8 -13.97 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 5576cd03-3bad-40c5-9316-1d286895d52a)
  )
  (fp_text user "GND" (at -12.8 19.05 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 56d2bc5d-fd72-4542-ab0f-053a5fd60efa)
  )
  (fp_text user "GND" (at -12.8 -19.05 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 5e6153e6-2c19-46de-9a8e-b310a2a07861)
  )
  (fp_text user "GP20" (at 13.054 11.43 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 62f15a9a-9893-486e-9ad0-ea43f88fc9e7)
  )
  (fp_text user "GP19" (at 13.054 13.97 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 7273dd21-e834-41d3-b279-d7de727709ca)
  )
  (fp_text user "GP2" (at -12.9 -16.51 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp 96ef76a5-90c3-4767-98ba-2b61887e28d3)
  )
  (fp_text user "GP18" (at 13.054 16.51 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp a3fab380-991d-404b-95d5-1c209b047b6e)
  )
  (fp_text user "GP6" (at -12.8 -3.81 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp aa23bfe3-454b-4a2b-bfe1-101c747eb84e)
  )
  (fp_text user "GP21" (at 13.054 8.9 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp b2b363dd-8e47-4a76-a142-e00e28334875)
  )
  (fp_text user "GP22" (at 13.054 3.81 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp c15b2f75-2e10-4b71-bebb-e2b872171b92)
  )
  (fp_text user "GND" (at -12.8 6.35 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp c512fed3-9770-476b-b048-e781b4f3cd72)
  )
  (fp_text user "GP14" (at -13.1 21.59 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp c7cd39db-931a-4d86-96b8-57e6b39f58f9)
  )
  (fp_text user "VSYS" (at 13.2 -21.59 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp cb1a49ef-0a06-4f40-9008-61d1d1c36198)
  )
  (fp_text user "GP12" (at -13.2 13.97 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp ceb12634-32ca-4cbf-9ff5-5e8b53ab18ad)
  )
  (fp_text user "GP1" (at -12.9 -21.6 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp db6412d3-e6c3-4bdd-abf4-a8f55d56df31)
  )
  (fp_text user "GP10" (at -13.054 8.89 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp dd70858b-2f9a-4b3f-9af5-ead3a9ba57e9)
  )
  (fp_text user "RUN" (at 13 1.27 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp f6a5c856-f2b5-40eb-a958-b666a0d408a0)
  )
  (fp_text user "GP27" (at 13.054 -3.8 45) (layer "F.SilkS")
    (effects (font (size 0.8 0.8) (thickness 0.15)))
    (tstamp ffa442c7-cbef-461f-8613-c211201cec06)
  )
  (fp_text user "Copper Keepouts shown on Dwgs layer" (at 0.1 -30.2) (layer "Cmts.User")
    (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 66ca01b3-51ff-4294-9b77-4492e98f6aec)
  )
  (fp_text user "${REFERENCE}" (at 0 0 180) (layer "F.Fab")
    (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 83184391-76ed-44f0-8cd0-01f89f157bdb)
  )
  (fp_line (start 10.5 4.9) (end 10.5 5.3) (layer "F.SilkS") (width 0.12) (tstamp 099473f1-6598-46ff-a50f-4c520832170d))
  (fp_line (start -10.5 -23.1) (end -10.5 -22.7) (layer "F.SilkS") (width 0.12) (tstamp 0c5dddf1-38df-43d2-b49c-e7b691dab0ab))
  (fp_line (start -10.5 -20.5) (end -10.5 -20.1) (layer "F.SilkS") (width 0.12) (tstamp 0ce1dd44-f307-4f98-9f0d-478fd87daa64))
  (fp_line (start 10.5 10) (end 10.5 10.4) (layer "F.SilkS") (width 0.12) (tstamp 15699041-ed40-45ee-87d8-f5e206a88536))
  (fp_line (start -3.7 25.5) (end -10.5 25.5) (layer "F.SilkS") (width 0.12) (tstamp 1855ca44-ab48-4b76-a210-97fc81d916c4))
  (fp_line (start 10.5 -0.2) (end 10.5 0.2) (layer "F.SilkS") (width 0.12) (tstamp 1876c30c-72b2-4a8d-9f32-bf8b213530b4))
  (fp_line (start 10.5 22.7) (end 10.5 23.1) (layer "F.SilkS") (width 0.12) (tstamp 199124ca-dd64-45cf-a063-97cc545cbea7))
  (fp_line (start 10.5 -23.1) (end 10.5 -22.7) (layer "F.SilkS") (width 0.12) (tstamp 1bd80cf9-f42a-4aee-a408-9dbf4e81e625))
  (fp_line (start -7.493 -22.833) (end -7.493 -25.5) (layer "F.SilkS") (width 0.12) (tstamp 254f7cc6-cee1-44ca-9afe-939b318201aa))
  (fp_line (start 10.5 -5.3) (end 10.5 -4.9) (layer "F.SilkS") (width 0.12) (tstamp 26a22c19-4cc5-4237-9651-0edc4f854154))
  (fp_line (start -10.5 -25.5) (end 10.5 -25.5) (layer "F.SilkS") (width 0.12) (tstamp 3457afc5-3e4f-4220-81d1-b079f653a722))
  (fp_line (start -10.5 20.1) (end -10.5 20.5) (layer "F.SilkS") (width 0.12) (tstamp 3b65c51e-c243-447e-bee9-832d94c1630e))
  (fp_line (start -10.5 -2.7) (end -10.5 -2.3) (layer "F.SilkS") (width 0.12) (tstamp 3bbbbb7d-391c-4fee-ac81-3c47878edc38))
  (fp_line (start -10.5 22.7) (end -10.5 23.1) (layer "F.SilkS") (width 0.12) (tstamp 402c62e6-8d8e-473a-a0cf-2b86e4908cd7))
  (fp_line (start -10.5 -15.4) (end -10.5 -15) (layer "F.SilkS") (width 0.12) (tstamp 4970ec6e-3725-4619-b57d-dc2c2cb86ed0))
  (fp_line (start -10.5 -5.3) (end -10.5 -4.9) (layer "F.SilkS") (width 0.12) (tstamp 4a53fa56-d65b-42a4-a4be-8f49c4c015bb))
  (fp_line (start 10.5 -2.7) (end 10.5 -2.3) (layer "F.SilkS") (width 0.12) (tstamp 4bbde53d-6894-4e18-9480-84a6a26d5f6b))
  (fp_line (start 10.5 25.5) (end 3.7 25.5) (layer "F.SilkS") (width 0.12) (tstamp 54ed3ee1-891b-418e-ab9c-6a18747d7388))
  (fp_line (start 10.5 -15.4) (end 10.5 -15) (layer "F.SilkS") (width 0.12) (tstamp 57f248a7-365e-4c42-b80d-5a7d1f9dfaf3))
  (fp_line (start -10.5 7.4) (end -10.5 7.8) (layer "F.SilkS") (width 0.12) (tstamp 5bab6a37-1fdf-4cf8-b571-44c962ed86e9))
  (fp_line (start -10.5 -22.833) (end -7.493 -22.833) (layer "F.SilkS") (width 0.12) (tstamp 5f48b0f2-82cf-40ce-afac-440f97643c36))
  (fp_line (start -10.5 -7.8) (end -10.5 -7.4) (layer "F.SilkS") (width 0.12) (tstamp 6150c02b-beb5-4af1-951e-3666a285a6ea))
  (fp_line (start -10.5 4.9) (end -10.5 5.3) (layer "F.SilkS") (width 0.12) (tstamp 706c1cb9-5d96-4282-9efc-6147f0125147))
  (fp_line (start -1.5 25.5) (end -1.1 25.5) (layer "F.SilkS") (width 0.12) (tstamp 749d9ed0-2ff2-4b55-abc5-f7231ec3aa28))
  (fp_line (start -10.5 -12.9) (end -10.5 -12.5) (layer "F.SilkS") (width 0.12) (tstamp 755f94aa-38f0-4a64-a7c7-6c71cb18cddf))
  (fp_line (start 10.5 -20.5) (end 10.5 -20.1) (layer "F.SilkS") (width 0.12) (tstamp 80095e91-6317-4cfb-9aea-884c9a1accc5))
  (fp_line (start -10.5 15.1) (end -10.5 15.5) (layer "F.SilkS") (width 0.12) (tstamp 88deea08-baa5-4041-beb7-01c299cf00e6))
  (fp_line (start 1.1 25.5) (end 1.5 25.5) (layer "F.SilkS") (width 0.12) (tstamp 8a8c373f-9bc3-4cf7-8f41-4802da916698))
  (fp_line (start 10.5 -12.9) (end 10.5 -12.5) (layer "F.SilkS") (width 0.12) (tstamp 9112ddd5-10d5-48b8-954f-f1d5adcacbd9))
  (fp_line (start -10.5 10) (end -10.5 10.4) (layer "F.SilkS") (width 0.12) (tstamp 92f063a3-7cce-4a96-8a3a-cf5767f700c6))
  (fp_line (start 10.5 2.3) (end 10.5 2.7) (layer "F.SilkS") (width 0.12) (tstamp 968a6172-7a4e-40ab-a78a-e4d03671e136))
  (fp_line (start -10.5 -10.4) (end -10.5 -10) (layer "F.SilkS") (width 0.12) (tstamp 9c2999b2-1cf1-4204-9d23-243401b77aa3))
  (fp_line (start -10.5 -0.2) (end -10.5 0.2) (layer "F.SilkS") (width 0.12) (tstamp 9ed09117-33cf-45a3-85a7-2606522feaf8))
  (fp_line (start -10.5 17.6) (end -10.5 18) (layer "F.SilkS") (width 0.12) (tstamp a177c3b4-b04c-490e-b3fe-d3d4d7aa24a7))
  (fp_line (start -10.5 12.5) (end -10.5 12.9) (layer "F.SilkS") (width 0.12) (tstamp ad4d05f5-6957-42f8-b65c-c657b9a26485))
  (fp_line (start 10.5 7.4) (end 10.5 7.8) (layer "F.SilkS") (width 0.12) (tstamp af76ce95-feca-41fb-bf31-edaa26d6766a))
  (fp_line (start 10.5 -10.4) (end 10.5 -10) (layer "F.SilkS") (width 0.12) (tstamp c1b11207-7c0a-49b3-a41d-2fe677d5f3b8))
  (fp_line (start 10.5 17.6) (end 10.5 18) (layer "F.SilkS") (width 0.12) (tstamp c346b00c-b5e0-4939-beb4-7f48172ef334))
  (fp_line (start 10.5 -7.8) (end 10.5 -7.4) (layer "F.SilkS") (width 0.12) (tstamp c3d5daf8-d359-42b2-a7c2-0d080ba7e212))
  (fp_line (start -10.5 -25.5) (end -10.5 -25.2) (layer "F.SilkS") (width 0.12) (tstamp ca56e1ad-54bf-4df5-a4f7-99f5d61d0de9))
  (fp_line (start 10.5 20.1) (end 10.5 20.5) (layer "F.SilkS") (width 0.12) (tstamp ca9b74ce-0dee-401c-9544-f599f4cf538d))
  (fp_line (start 10.5 12.5) (end 10.5 12.9) (layer "F.SilkS") (width 0.12) (tstamp d3dd7cdb-b730-487d-804d-99150ba318ef))
  (fp_line (start 10.5 -18) (end 10.5 -17.6) (layer "F.SilkS") (width 0.12) (tstamp e11ae5a5-aa10-4f10-b346-f16e33c7899a))
  (fp_line (start -10.5 2.3) (end -10.5 2.7) (layer "F.SilkS") (width 0.12) (tstamp eb391a95-1c1d-4613-b508-c76b8bc13a73))
  (fp_line (start 10.5 -25.5) (end 10.5 -25.2) (layer "F.SilkS") (width 0.12) (tstamp f23ac723-a36d-491d-9473-7ec0ffed332d))
  (fp_line (start -10.5 -18) (end -10.5 -17.6) (layer "F.SilkS") (width 0.12) (tstamp f8b47531-6c06-4e54-9fc9-cd9d0f3dd69f))
  (fp_line (start 10.5 15.1) (end 10.5 15.5) (layer "F.SilkS") (width 0.12) (tstamp fd60415a-f01a-46c5-9369-ea970e435e5b))
  (fp_poly (pts
      (xy -1.5 -16.5)
      (xy -3.5 -16.5)
      (xy -3.5 -18.5)
      (xy -1.5 -18.5)
    ) (layer "Dwgs.User") (width 0.1) (fill solid) (tstamp 022502e0-e724-4b75-bc35-3c5984dbeb76))
  (fp_poly (pts
      (xy -1.5 -11.5)
      (xy -3.5 -11.5)
      (xy -3.5 -13.5)
      (xy -1.5 -13.5)
    ) (layer "Dwgs.User") (width 0.1) (fill solid) (tstamp 9f969b13-1795-4747-8326-93bdc304ed56))
  (fp_poly (pts
      (xy 3.7 -20.2)
      (xy -3.7 -20.2)
      (xy -3.7 -24.9)
      (xy 3.7 -24.9)
    ) (layer "Dwgs.User") (width 0.1) (fill solid) (tstamp b9d4de74-d246-495d-8b63-12ab2133d6d6))
  (fp_poly (pts
      (xy -1.5 -14)
      (xy -3.5 -14)
      (xy -3.5 -16)
      (xy -1.5 -16)
    ) (layer "Dwgs.User") (width 0.1) (fill solid) (tstamp d655bb0a-cbf9-4908-ad60-7024ff468fbd))
  (fp_line (start 11 -26) (end 11 26) (layer "F.CrtYd") (width 0.12) (tstamp 58390862-1833-41dd-9c4e-98073ea0da33))
  (fp_line (start 11 26) (end -11 26) (layer "F.CrtYd") (width 0.12) (tstamp 5e755161-24a5-4650-a6e3-9836bf074412))
  (fp_line (start -11 -26) (end 11 -26) (layer "F.CrtYd") (width 0.12) (tstamp 9208ea78-8dde-4b3d-91e9-5755ab5efd9a))
  (fp_line (start -11 26) (end -11 -26) (layer "F.CrtYd") (width 0.12) (tstamp e86e4fae-9ca7-4857-a93c-bc6a3048f887))
  (fp_line (start -10.5 -24.2) (end -9.2 -25.5) (layer "F.Fab") (width 0.12) (tstamp 1bf7d0f9-0dcf-4d7c-b58c-318e3dc42bc9))
  (fp_line (start 10.5 -25.5) (end 10.5 25.5) (layer "F.Fab") (width 0.12) (tstamp 247ebffd-2cb6-4379-ba6e-21861fea3913))
  (fp_line (start 10.5 25.5) (end -10.5 25.5) (layer "F.Fab") (width 0.12) (tstamp 94d24676-7ae3-483c-8bd6-88d31adf00b4))
  (fp_line (start -10.5 -25.5) (end 10.5 -25.5) (layer "F.Fab") (width 0.12) (tstamp 966ee9ec-860e-45bb-af89-30bda72b2032))
  (fp_line (start -10.5 25.5) (end -10.5 -25.5) (layer "F.Fab") (width 0.12) (tstamp e45aa7d8-0254-4176-afd9-766820762e19))
  (pad "" np_thru_hole oval (at 2.725 -24) (size 1.8 1.8) (drill 1.8) (layers *.Cu *.Mask) (tstamp 631c7be5-8dc2-4df4-ab73-737bb928e763))
  (pad "" np_thru_hole oval (at -2.725 -24) (size 1.8 1.8) (drill 1.8) (layers *.Cu *.Mask) (tstamp 6d2a06fb-0b1e-452a-ab38-11a5f45e1b32))
  (pad "" np_thru_hole oval (at -2.425 -20.97) (size 1.5 1.5) (drill 1.5) (layers *.Cu *.Mask) (tstamp 929a9b03-e99e-4b88-8e16-759f8c6b59a5))
  (pad "" np_thru_hole oval (at 2.425 -20.97) (size 1.5 1.5) (drill 1.5) (layers *.Cu *.Mask) (tstamp c210293b-1d7a-4e96-92e9-058784106727))
  (pad "1" thru_hole oval (at -8.89 -24.13) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 015f5586-ba76-4a98-9114-f5cd2c67134d))
  (pad "1" smd rect (at -8.89 -24.13) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 761c8e29-382a-475c-a37a-7201cc9cd0f5))
  (pad "2" thru_hole oval (at -8.89 -21.59) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 541721d1-074b-496e-a833-813044b3e8ca))
  (pad "2" smd rect (at -8.89 -21.59) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp e50c80c5-80c4-46a3-8c1e-c9c3a71a0934))
  (pad "3" smd rect (at -8.89 -19.05) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 7233cb6b-d8fd-4fcd-9b4f-8b0ed19b1b12))
  (pad "3" thru_hole rect (at -8.89 -19.05) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp d05faa1f-5f69-41bf-86d3-2cd224432e1b))
  (pad "4" thru_hole oval (at -8.89 -16.51) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 2f424da3-8fae-4941-bc6d-20044787372f))
  (pad "4" smd rect (at -8.89 -16.51) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp df83f395-2d18-47e2-a370-952ca41c2b3a))
  (pad "5" thru_hole oval (at -8.89 -13.97) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 41485de5-6ed3-4c83-b69e-ef83ae18093c))
  (pad "5" smd rect (at -8.89 -13.97) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 653a86ba-a1ae-4175-9d4c-c788087956d0))
  (pad "6" thru_hole oval (at -8.89 -11.43) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 3bca658b-a598-4669-a7cb-3f9b5f47bb5a))
  (pad "6" smd rect (at -8.89 -11.43) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 3ed2c840-383d-4cbd-bc3b-c4ea4c97b333))
  (pad "7" smd rect (at -8.89 -8.89) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 6a0919c2-460c-4229-b872-14e318e1ba8b))
  (pad "7" thru_hole oval (at -8.89 -8.89) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp bef2abc2-bf3e-4a72-ad03-f8da3cd893cb))
  (pad "8" thru_hole rect (at -8.89 -6.35) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp b7aa0362-7c9e-4a42-b191-ab15a38bf3c5))
  (pad "8" smd rect (at -8.89 -6.35) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp d1c19c11-0a13-4237-b6b4-fb2ef1db7c6d))
  (pad "9" smd rect (at -8.89 -3.81) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 29cbb0bc-f66b-4d11-80e7-5bb270e42496))
  (pad "9" thru_hole oval (at -8.89 -3.81) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp dd1edfbb-5fb6-42cd-b740-fd54ab3ef1f1))
  (pad "10" thru_hole oval (at -8.89 -1.27) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 42d3f9d6-2a47-41a8-b942-295fcb83bcd8))
  (pad "10" smd rect (at -8.89 -1.27) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp c401e9c6-1deb-4979-99be-7c801c952098))
  (pad "11" smd rect (at -8.89 1.27) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 355ced6c-c08a-4586-9a09-7a9c624536f6))
  (pad "11" thru_hole oval (at -8.89 1.27) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 7bea05d4-1dec-4cd6-aa53-302dde803254))
  (pad "12" thru_hole oval (at -8.89 3.81) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp a5362821-c161-4c7a-a00c-40e1d7472d56))
  (pad "12" smd rect (at -8.89 3.81) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp c2dd13db-24b6-40f1-b75b-b9ab893d92ea))
  (pad "13" thru_hole rect (at -8.89 6.35) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 1cc5480b-56b7-4379-98e2-ccafc88911a7))
  (pad "13" smd rect (at -8.89 6.35) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp d8200a86-aa75-47a3-ad2a-7f4c9c999a6f))
  (pad "14" smd rect (at -8.89 8.89) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 465137b4-f6f7-4d51-9b40-b161947d5cc1))
  (pad "14" thru_hole oval (at -8.89 8.89) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 9a8ad8bb-d9a9-4b2b-bc88-ea6fd2676d45))
  (pad "15" thru_hole oval (at -8.89 11.43) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 851f3d61-ba3b-4e6e-abd4-cafa4d9b64cb))
  (pad "15" smd rect (at -8.89 11.43) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp d1cd5391-31d2-459f-8adb-4ae3f304a833))
  (pad "16" smd rect (at -8.89 13.97) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 4086cbd7-6ba7-4e63-8da9-17e60627ee17))
  (pad "16" thru_hole oval (at -8.89 13.97) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp ca6e2466-a90a-4dab-be16-b070610e5087))
  (pad "17" smd rect (at -8.89 16.51) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp bb8162f0-99c8-4884-be5b-c0d0c7e81ff6))
  (pad "17" thru_hole oval (at -8.89 16.51) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp d18f2428-546f-4066-8ffb-7653303685db))
  (pad "18" smd rect (at -8.89 19.05) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 91fc5800-6029-46b1-848d-ca0091f97267))
  (pad "18" thru_hole rect (at -8.89 19.05) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp d95c6650-fcd9-4184-97fe-fde43ea5c0cd))
  (pad "19" thru_hole oval (at -8.89 21.59) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 12fa3c3f-3d14-451a-a6a8-884fd1b32fa7))
  (pad "19" smd rect (at -8.89 21.59) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 275b6416-db29-42cc-9307-bf426917c3b4))
  (pad "20" smd rect (at -8.89 24.13) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 3c22d605-7855-4cc6-8ad2-906cadbd02dc))
  (pad "20" thru_hole oval (at -8.89 24.13) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp f4a1ab68-998b-43e3-aa33-40b58210bc99))
  (pad "21" smd rect (at 8.89 24.13) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 24adc223-60f0-4497-98a3-d664c5a13280))
  (pad "21" thru_hole oval (at 8.89 24.13) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp e76ec524-408a-4daa-89f6-0edfdbcfb621))
  (pad "22" smd rect (at 8.89 21.59) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 13ac70df-e9b9-44e5-96e6-20f0b0dc6a3a))
  (pad "22" thru_hole oval (at 8.89 21.59) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 78b44915-d68e-4488-a873-34767153ef98))
  (pad "23" smd rect (at 8.89 19.05) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 278a91dc-d57d-4a5c-a045-34b6bd84131f))
  (pad "23" thru_hole rect (at 8.89 19.05) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 3993c707-5291-41b6-83c0-d1c09cb3833a))
  (pad "24" thru_hole oval (at 8.89 16.51) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 17ff35b3-d658-499b-9a46-ea36063fed4e))
  (pad "24" smd rect (at 8.89 16.51) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 98966de3-2364-43d8-a2e0-b03bb9487b03))
  (pad "25" smd rect (at 8.89 13.97) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 4cc0e615-05a0-4f42-a208-4011ba8ef841))
  (pad "25" thru_hole oval (at 8.89 13.97) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp d13b0eae-4711-4325-a6bb-aa8e3646e86e))
  (pad "26" smd rect (at 8.89 11.43) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 4641c87c-bffa-41fe-ae77-be3a97a6f797))
  (pad "26" thru_hole oval (at 8.89 11.43) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp a917c6d9-225d-4c90-bf25-fe8eff8abd3f))
  (pad "27" thru_hole oval (at 8.89 8.89) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 89a3dae6-dcb5-435b-a383-656b6a19a316))
  (pad "27" smd rect (at 8.89 8.89) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp da546d77-4b03-4562-8fc6-837fd68e7691))
  (pad "28" thru_hole rect (at 8.89 6.35) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp b54cae5b-c17c-4ed7-b249-2e7d5e83609a))
  (pad "28" smd rect (at 8.89 6.35) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp e2fac877-439c-4da0-af2e-5fdc70f85d42))
  (pad "29" thru_hole oval (at 8.89 3.81) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 26bc8641-9bca-4204-9709-deedbe202a36))
  (pad "29" smd rect (at 8.89 3.81) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 2ea8fa6f-efc3-40fe-bcf9-05bfa46ead4f))
  (pad "30" smd rect (at 8.89 1.27) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 9da1ace0-4181-4f12-80f8-16786a9e5c07))
  (pad "30" thru_hole oval (at 8.89 1.27) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp fd5f7d77-0f73-4021-88a8-0641f0fe8d98))
  (pad "31" thru_hole oval (at 8.89 -1.27) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 1755646e-fc08-4e43-a301-d9b3ea704cf6))
  (pad "31" smd rect (at 8.89 -1.27) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 29126f72-63f7-4275-8b12-6b96a71c6f17))
  (pad "32" thru_hole oval (at 8.89 -3.81) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 1317ff66-8ecf-46c9-9612-8d2eae03c537))
  (pad "32" smd rect (at 8.89 -3.81) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp af186015-d283-4209-aade-a247e5de01df))
  (pad "33" smd rect (at 8.89 -6.35) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 8d063f79-9282-4820-bcf4-1ff3c006cf08))
  (pad "33" thru_hole rect (at 8.89 -6.35) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp ef4533db-6ea4-4b68-b436-8e9575be570d))
  (pad "34" smd rect (at 8.89 -8.89) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 0554bea0-89b2-4e25-9ea3-4c73921c94cb))
  (pad "34" thru_hole oval (at 8.89 -8.89) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp f5dba25f-5f9b-4770-84f9-c038fb119360))
  (pad "35" smd rect (at 8.89 -11.43) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 88606262-3ac5-44a1-aacc-18b26cf4d396))
  (pad "35" thru_hole oval (at 8.89 -11.43) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 8aff0f38-92a8-45ec-b106-b185e93ca3fd))
  (pad "36" thru_hole oval (at 8.89 -13.97) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 63caf46e-0228-40de-b819-c6bd29dd1711))
  (pad "36" smd rect (at 8.89 -13.97) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp cd1cff81-9d8a-4511-96d6-4ddb79484001))
  (pad "37" smd rect (at 8.89 -16.51) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 22962957-1efd-404d-83db-5b233b6c15b0))
  (pad "37" thru_hole oval (at 8.89 -16.51) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp a7fc0812-140f-4d96-9cd8-ead8c1c610b1))
  (pad "38" smd rect (at 8.89 -19.05) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 8eb98c56-17e4-4de6-a3e3-06dcfa392040))
  (pad "38" thru_hole rect (at 8.89 -19.05) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 94a10cae-6ef2-4b64-9d98-fb22aa3306cc))
  (pad "39" smd rect (at 8.89 -21.59) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp c66a19ed-90c0-4502-ae75-6a4c4ab9f297))
  (pad "39" thru_hole oval (at 8.89 -21.59) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp f33ec0db-ef0f-4576-8054-2833161a8f30))
  (pad "40" thru_hole oval (at 8.89 -24.13) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 0ba17a9b-d889-426c-b4fe-048bed6b6be8))
  (pad "40" smd rect (at 8.89 -24.13) (size 3.5 1.7) (drill (offset 0.9 0)) (layers "F.Cu" "F.Mask") (tstamp bd085057-7c0e-463a-982b-968a2dc1f0f8))
  (pad "41" smd rect (at -2.54 23.9 90) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp b21299b9-3c4d-43df-b399-7f9b08eb5470))
  (pad "41" thru_hole oval (at -2.54 23.9) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp fc2e9f96-3bed-4896-b995-f56e799f1c77))
  (pad "42" thru_hole rect (at 0 23.9) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 4cfd9a02-97ef-4af4-a6b8-db9be1a8fda5))
  (pad "42" smd rect (at 0 23.9 90) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp 751d823e-1d7b-4501-9658-d06d459b0e16))
  (pad "43" thru_hole oval (at 2.54 23.9) (size 1.7 1.7) (drill 1.02) (layers *.Cu *.Mask) (tstamp 92761c09-a591-4c8e-af4d-e0e2262cb01d))
  (pad "43" smd rect (at 2.54 23.9 90) (size 3.5 1.7) (drill (offset -0.9 0)) (layers "F.Cu" "F.Mask") (tstamp aadc3df5-0e2d-4f3d-b72e-6f184da74c89))
  (model "C:/Users/<USER>/OneDrive/IoT/Tools/KiCad/RP-Pico Libraries/Pico.wrl"
    (offset (xyz 0 0 0))
    (scale (xyz 1 1 1))
    (rotate (xyz 0 0 0))
  )
)
