(footprint "TerminalBlock_Phoenix_MKDS-1,5-2-5.08_1x02_P5.08mm_Horizontal"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "Terminal Block Phoenix MKDS-1,5-2-5.08, 2 pins, pitch 5.08mm, size 10.2x9.8mm^2, drill diamater 1.3mm, pad diameter 2.6mm, see http://www.farnell.com/datasheets/100425.pdf, script-generated using https://github.com/pointhi/kicad-footprint-generator/scripts/TerminalBlock_Phoenix")
	(tags "THT Terminal Block Phoenix MKDS-1,5-2-5.08 pitch 5.08mm size 10.2x9.8mm^2 drill 1.3mm pad 2.6mm")
	(property "Reference" "REF**"
		(at 2.54 -6.26 0)
		(layer "F.SilkS")
		(uuid "3eddac61-4c04-43ff-86c7-0115e6b7e7b9")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "CONN_2"
		(at 2.54 5.66 0)
		(layer "F.Fab")
		(uuid "ad58cede-7f4a-485f-a1b3-ef66bbb7df53")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "c4237725-d10d-428d-a310-2b90b878d71e")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "bbab069a-7096-464e-8a07-0ab57d1856b5")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -2.84 4.16)
		(end -2.84 4.9)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "69cf9754-e750-45fc-a2f3-971cf3ed4bc5")
	)
	(fp_line
		(start -2.84 4.9)
		(end -2.34 4.9)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "48bbb253-c3ef-423c-b831-31e36521f2c3")
	)
	(fp_line
		(start -2.6 -5.261)
		(end -2.6 4.66)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "d6fdad1d-5bf0-4685-9f33-ef2d754a7898")
	)
	(fp_line
		(start -2.6 -5.261)
		(end 7.68 -5.261)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "91b7be44-0f63-4bb6-9e52-dfd53b55ae9a")
	)
	(fp_line
		(start -2.6 -2.301)
		(end 7.68 -2.301)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "8deb919a-f5d1-4ef3-b6d0-b9682b78d4d4")
	)
	(fp_line
		(start -2.6 2.6)
		(end 7.68 2.6)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "d29da081-3475-4bec-b78e-4c43a6bb493b")
	)
	(fp_line
		(start -2.6 4.1)
		(end 7.68 4.1)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "c834f478-bf26-4a92-bd9a-e47130192c0f")
	)
	(fp_line
		(start -2.6 4.66)
		(end 7.68 4.66)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "01258a32-69ca-42a4-a6e0-65d1d059123b")
	)
	(fp_line
		(start 3.853 1.023)
		(end 3.806 1.069)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "11b2c540-8853-4f32-9265-4856813e962d")
	)
	(fp_line
		(start 4.046 1.239)
		(end 4.011 1.274)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "a306b58d-be2f-4086-85a5-9559a332f1b9")
	)
	(fp_line
		(start 6.15 -1.275)
		(end 6.115 -1.239)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "84f0ba76-f643-4185-a4f8-1d66b3027563")
	)
	(fp_line
		(start 6.355 -1.069)
		(end 6.308 -1.023)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "e75a9be1-0a63-46d0-901e-9e88f6672aa1")
	)
	(fp_line
		(start 7.68 -5.261)
		(end 7.68 4.66)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "d1901319-b234-4786-a7a6-3c2403e1dee8")
	)
	(fp_arc
		(start -1.535427 0.683042)
		(mid -1.680501 -0.000524)
		(end -1.535 -0.684)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "8bdd2b7b-6bfb-4827-a2f9-db06260d4d0f")
	)
	(fp_arc
		(start -0.683042 -1.535427)
		(mid 0.000524 -1.680501)
		(end 0.684 -1.535)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "8cb4f15c-3e66-4d07-9cad-1e88c8fc4351")
	)
	(fp_arc
		(start 0.028805 1.680253)
		(mid -0.335551 1.646659)
		(end -0.684 1.535)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "bf985097-c8e8-47fd-abf8-176eb96f4d7e")
	)
	(fp_arc
		(start 0.683318 1.534756)
		(mid 0.349292 1.643288)
		(end 0 1.68)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "6832d8d5-5396-4da6-8e20-819a789b041c")
	)
	(fp_arc
		(start 1.535427 -0.683042)
		(mid 1.680501 0.000524)
		(end 1.535 0.684)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "e9f6aaf5-f796-4075-a213-2146e0c57d13")
	)
	(fp_circle
		(center 5.08 0)
		(end 6.76 0)
		(stroke
			(width 0.12)
			(type solid)
		)
		(fill no)
		(layer "F.SilkS")
		(uuid "2cd3b578-14b8-4efa-a534-1a9e341ce919")
	)
	(fp_line
		(start -3.04 -5.71)
		(end -3.04 5.1)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "22e432b6-c3ed-44e2-a852-42da2e16582d")
	)
	(fp_line
		(start -3.04 5.1)
		(end 8.13 5.1)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "0bbf651c-ef79-4e3d-bc84-3acfb8c4cfa5")
	)
	(fp_line
		(start 8.13 -5.71)
		(end -3.04 -5.71)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "5a39423e-7adb-4088-a715-ff29b462f8a5")
	)
	(fp_line
		(start 8.13 5.1)
		(end 8.13 -5.71)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "13ab1245-17ed-4326-b6c5-90c2ae941663")
	)
	(fp_line
		(start -2.54 -5.2)
		(end 7.62 -5.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "5e65da77-32a8-4311-b513-4e882dfc7de5")
	)
	(fp_line
		(start -2.54 -2.3)
		(end 7.62 -2.3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "eb079439-a515-49b1-95fe-d9c73998c4ed")
	)
	(fp_line
		(start -2.54 2.6)
		(end 7.62 2.6)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "1bf28be6-13cf-4310-bf82-5d5768936eb3")
	)
	(fp_line
		(start -2.54 4.1)
		(end -2.54 -5.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "7699c047-0fc2-4f5a-9e19-c008c4d403a8")
	)
	(fp_line
		(start -2.54 4.1)
		(end 7.62 4.1)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "2e50d628-bdab-430b-8ab4-f2c0ad845e11")
	)
	(fp_line
		(start -2.04 4.6)
		(end -2.54 4.1)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "3deb952c-d1a6-4452-80c3-81503c8d9bbb")
	)
	(fp_line
		(start 0.955 -1.138)
		(end -1.138 0.955)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "d76b6091-a7e0-44dc-828e-70e5b6b0c4b4")
	)
	(fp_line
		(start 1.138 -0.955)
		(end -0.955 1.138)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "f8ad66ea-2090-42cf-83a0-2ff92844339b")
	)
	(fp_line
		(start 6.035 -1.138)
		(end 3.943 0.955)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "5cd18489-44ba-4806-a171-9e81de3b55b1")
	)
	(fp_line
		(start 6.218 -0.955)
		(end 4.126 1.138)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "945b5d23-8815-4252-aa16-8b52e2acd4c3")
	)
	(fp_line
		(start 7.62 -5.2)
		(end 7.62 4.6)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "4de9b75c-eda1-4459-b59e-a39e42f8decd")
	)
	(fp_line
		(start 7.62 4.6)
		(end -2.04 4.6)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "8215538b-81e4-4aa3-b00c-cee9d6d895a8")
	)
	(fp_circle
		(center 0 0)
		(end 1.5 0)
		(stroke
			(width 0.1)
			(type solid)
		)
		(fill no)
		(layer "F.Fab")
		(uuid "6d8e6fbe-86a6-4785-a6a3-f95630e77c6e")
	)
	(fp_circle
		(center 5.08 0)
		(end 6.58 0)
		(stroke
			(width 0.1)
			(type solid)
		)
		(fill no)
		(layer "F.Fab")
		(uuid "28488df3-9bd3-4e33-9fe6-f6427a68449f")
	)
	(fp_text user "${REFERENCE}"
		(at 2.54 3.2 0)
		(layer "F.Fab")
		(uuid "27d4f77c-2c5e-4abf-9350-f4ff2bc5dc70")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 2.6 2.6)
		(drill 1.3)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c85624cd-cb1c-4079-bb16-321b463ee23c")
	)
	(pad "2" thru_hole circle
		(at 5.08 0)
		(size 2.6 2.6)
		(drill 1.3)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "3b5f4816-ab17-4fb3-aa3a-c663f1d29646")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/TerminalBlock_Phoenix.3dshapes/TerminalBlock_Phoenix_MKDS-1,5-2-5.08_1x02_P5.08mm_Horizontal.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
