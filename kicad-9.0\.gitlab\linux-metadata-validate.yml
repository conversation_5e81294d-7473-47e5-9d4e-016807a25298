##########################################################################
# Run a validation of the metadata files for Linux
##########################################################################
validate_linux_metadata:
  stage: test
  needs: []
  interruptible: false
  image: $DEFAULT_FEDORA_IMAGE
  # Due to bug https://github.com/hughsie/appstream-glib/issues/381, this doesn't think our description tag
  # is localized even though it actually is.
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - resources/linux/**/*
        - qa/resources/linux/**/*
        - translation/**/*
  # Only build the metadata files in this CI job
  script:
    - mkdir -p build/linux
    - cd build/linux
    - cmake
      -DCMAKE_BUILD_TYPE=Debug
      -DKICAD_STDLIB_LIGHT_DEBUG=ON
      -DKICAD_SCRIPTING_WXPYTHON=ON
      -DKICAD_BUILD_I18N=ON
      ../../
    - make metadata
    - cd ../../
    - ./qa/resources/linux/verifyMetadataFiles.sh ./ ./build/linux
