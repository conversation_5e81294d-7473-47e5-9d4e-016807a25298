(footprint "GCT_USB4500-03-0-A_REVA" (version 20221018) (generator pcbnew)
  (layer "F.Cu")
  (attr smd)
  (fp_text reference "REF**" (at -4.3 -6.5) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
    (tstamp b7670d4b-3f28-456c-b590-016167e98c00)
  )
  (fp_text value "GCT_USB4500-03-0-A_REVA" (at 0.8 2.7) (layer "F.Fab")
      (effects (font (size 1 0.6) (thickness 0.15)))
    (tstamp 509c4cb8-69da-4e5f-a14d-64dc0106b4f6)
  )
  (fp_text user "PCB Edge" (at 7.6 1.6) (layer "F.Fab")
      (effects (font (size 0.64 0.64) (thickness 0.15)))
    (tstamp 090c76ab-7c15-47a9-ab9b-9c31ddd85e88)
  )
  (fp_text user "PCB CUTOUT" (at -3.6 -2.5 unlocked) (layer "User.4")
      (effects (font (size 1 0.8) (thickness 0.15)) (justify left bottom))
    (tstamp d4249fdd-76a2-4a10-9268-baf54b9ccb27)
  )
  (fp_line (start -4.47 1.6) (end 4.47 1.6)
    (stroke (width 0.2) (type solid)) (layer "F.SilkS") (tstamp 981f0fa9-51b1-4f08-9fe7-9d0fb4b506ec))
  (fp_circle (center -3.27 -6.35) (end -3.17 -6.35)
    (stroke (width 0.2) (type solid)) (fill none) (layer "F.SilkS") (tstamp 1ddf56d3-167f-4841-9545-2ddd162fb53c))
  (fp_line (start -4.62 1.6) (end -4.62 -4.6)
    (stroke (width 0.08) (type solid)) (layer "Edge.Cuts") (tstamp 8faf3a58-707a-46e8-8db5-b757bb7712b9))
  (fp_line (start -3.82 -4.6) (end 3.82 -4.6)
    (stroke (width 0.08) (type solid)) (layer "Edge.Cuts") (tstamp f630c03a-fc91-4c79-9e78-bd29146efb46))
  (fp_line (start 4.62 -4.6) (end 4.62 1.6)
    (stroke (width 0.08) (type solid)) (layer "Edge.Cuts") (tstamp 2f35c06c-9cc2-4bf9-bb07-106bea6e2a06))
  (fp_arc (start -4.619999 -4.6) (mid -4.540342 -4.867309) (end -4.295 -5)
    (stroke (width 0.08) (type solid)) (layer "Edge.Cuts") (tstamp c4e746a4-de48-4ba6-9685-afb011cd44b7))
  (fp_arc (start -4.295 -5) (mid -4.07451 -4.92731) (end -3.97 -4.72)
    (stroke (width 0.08) (type solid)) (layer "Edge.Cuts") (tstamp f860ffdf-8afc-4545-8e6a-04d684da1ff3))
  (fp_arc (start -3.820001 -4.600001) (mid -3.919852 -4.628935) (end -3.969999 -4.72)
    (stroke (width 0.08) (type solid)) (layer "Edge.Cuts") (tstamp 3d298c91-bfe6-4595-8300-354279cca2e6))
  (fp_arc (start 3.969999 -4.720001) (mid 3.919852 -4.628935) (end 3.82 -4.600001)
    (stroke (width 0.08) (type solid)) (layer "Edge.Cuts") (tstamp 2431ae19-51b3-4d81-9a9c-e5c28778b56c))
  (fp_arc (start 3.970002 -4.720001) (mid 4.074512 -4.927309) (end 4.295 -4.999999)
    (stroke (width 0.08) (type solid)) (layer "Edge.Cuts") (tstamp 36e2e138-c1b1-4dc4-8e10-334821b070a7))
  (fp_arc (start 4.295 -5) (mid 4.540343 -4.86731) (end 4.62 -4.6)
    (stroke (width 0.08) (type solid)) (layer "Edge.Cuts") (tstamp 723d1109-c990-4b06-aaf2-d66138db20a7))
  (fp_line (start -6.37 -5.95) (end 6.37 -5.95)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp cbc332dc-4235-4501-a474-b17734d92f20))
  (fp_line (start -6.37 2.35) (end -6.37 -5.95)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b6f4ab00-c20a-4e50-8982-8795dab88384))
  (fp_line (start -6.37 2.35) (end 6.37 2.35)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b210fa55-0cb6-4ccf-a58a-2c0892d9baab))
  (fp_line (start 6.37 2.35) (end 6.37 -5.95)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2a6487ba-6aa6-41df-9ec3-b5fe1c4bbc05))
  (fp_line (start -6 1.6) (end 6 1.6)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 831e0d0b-0b1d-4b09-bfab-24623d3d9b60))
  (fp_line (start -4.47 -4.4) (end 4.47 -4.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 29693cbf-1905-4973-b766-0e3921bb5251))
  (fp_line (start -4.47 2.1) (end -4.47 -4.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e5fa0984-07ac-45c8-b8f6-4175bdc213cd))
  (fp_line (start -4.47 2.1) (end 4.47 2.1)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b4775875-ff96-4122-a265-a4fea343da68))
  (fp_line (start 4.47 2.1) (end 4.47 -4.4)
    (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d2d4cd72-4feb-46b4-8986-2339cfd7512c))
  (fp_circle (center -3.27 -6.35) (end -3.17 -6.35)
    (stroke (width 0.2) (type solid)) (fill none) (layer "F.Fab") (tstamp ae35030f-1700-4c1d-8237-16c43fe1d757))
  (pad "A1_B12" smd rect (at -3.2 -5.45) (size 0.6 1.1) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 097d6961-a2ad-47e5-94eb-742da79f2bc1))
  (pad "A4_B9" smd rect (at -2.4 -5.45) (size 0.6 1.1) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 86a7ce22-7b5d-4a52-96d8-ef4911089995))
  (pad "A5" smd rect (at -1.25 -5.45) (size 0.3 1.1) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp a589ce59-92f9-4ffa-8937-586e7ef7ac1c))
  (pad "A6" smd rect (at -0.25 -5.45) (size 0.3 1.1) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 50988811-830d-4231-9451-e3c18d36e353))
  (pad "A7" smd rect (at 0.25 -5.45) (size 0.3 1.1) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 75aaf704-34b3-4eb9-ae13-c2e63a7ff45e))
  (pad "A8" smd rect (at 1.25 -5.45) (size 0.3 1.1) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp bed9fca2-3136-48cc-9806-a267034a124c))
  (pad "B1_A12" smd rect (at 3.2 -5.45) (size 0.6 1.1) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp acfb428f-4fcf-41fc-ba1b-db781a0bd793))
  (pad "B4_A9" smd rect (at 2.4 -5.45) (size 0.6 1.1) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 76ab6ac7-3b6e-4d84-b04c-ad727e3e295f))
  (pad "B5" smd rect (at 1.75 -5.45) (size 0.3 1.1) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp d6c24e19-6773-4771-9810-243734d807c2))
  (pad "B6" smd rect (at 0.75 -5.45) (size 0.3 1.1) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 7f05fff7-36d8-4552-b3ae-76da853b95e6))
  (pad "B7" smd rect (at -0.75 -5.45) (size 0.3 1.1) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 3e9f4306-1424-41d3-aeab-3c07504f0fbd))
  (pad "B8" smd rect (at -1.75 -5.45) (size 0.3 1.1) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 5de32797-cfd0-4b31-b688-77857c858503))
  (pad "S1" thru_hole oval (at -5.62 0) (size 0.95 2.1) (drill oval 0.6 1.8) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.15) (tstamp e9c1dd9d-34b1-40d6-8bc9-355fcb9e8a9e))
  (pad "S2" thru_hole oval (at 5.62 -4) (size 0.9 1.8) (drill oval 0.6 1.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.14) (tstamp 6d4ea4eb-dd08-4602-a860-a548cda7def5))
  (pad "S3" thru_hole oval (at -5.62 -4) (size 0.9 1.8) (drill oval 0.6 1.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.14) (tstamp 9bf8123f-3e92-4b8f-b778-a5ca9f58dd58))
  (pad "S4" thru_hole oval (at 5.62 0) (size 0.95 2.1) (drill oval 0.6 1.8) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.15) (tstamp f31bc785-3c95-438e-b8f3-a4861867e4d3))
  (model "${KIPRJMOD}/tinytapeout-kicad-libs/3dmodels/USB4500-03-0-A_REVA.step"
    (offset (xyz -0.5 -2 0))
    (scale (xyz 1 1 1))
    (rotate (xyz -90 0 0))
  )
)
