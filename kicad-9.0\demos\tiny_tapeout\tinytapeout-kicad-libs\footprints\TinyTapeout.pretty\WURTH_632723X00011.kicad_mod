(footprint "WURTH_632723X00011" (version 20221018) (generator pcbnew)
  (layer "F.Cu")
  (descr "Usb 3.1 Type c Receptacle Tht/Sm")
  (tags "USB-C Connector")
  (attr through_hole)
  (fp_text reference "REF**" (at -3 -6) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 2307de25-aa80-4632-85ad-31d949fd2734)
  )
  (fp_text value "WURTH_632723X00011" (at 8.63 8.265) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
    (tstamp 1aa57b50-19a2-4683-83a4-380f304ab79b)
  )
  (fp_text user "A1" (at -4.05 -4.7) (layer "Dwgs.User")
      (effects (font (size 0.32 0.32) (thickness 0.08)))
    (tstamp 3d62aca4-9c7e-4bb0-8e64-e9a06c85eca9)
  )
  (fp_text user "B1" (at 3.4 -3.55) (layer "Dwgs.User")
      (effects (font (size 0.32 0.32) (thickness 0.08)))
    (tstamp 8a0a5eb0-9aa0-49ec-8af2-05949741d423)
  )
  (fp_text user "A12" (at 3.55 -4.7) (layer "Dwgs.User")
      (effects (font (size 0.32 0.32) (thickness 0.08)))
    (tstamp 97fa3ab8-e831-4ba7-8e0f-cf348e72c6f2)
  )
  (fp_line (start -4.37 -4.38) (end -4.37 -3.494)
    (stroke (width 0.127) (type solid)) (layer "F.SilkS") (tstamp 9ae1b740-3391-4d9e-8f58-eceb18f6a197))
  (fp_line (start -4.37 -0.8665) (end -4.37 1.2365)
    (stroke (width 0.127) (type solid)) (layer "F.SilkS") (tstamp d4fc77de-9d2a-4dff-bf0e-239960e074d8))
  (fp_line (start -4.37 3.854) (end -4.37 6)
    (stroke (width 0.127) (type solid)) (layer "F.SilkS") (tstamp 0b040948-36ac-47f3-b737-43fc93f419ef))
  (fp_line (start -4.37 6) (end 4.37 6)
    (stroke (width 0.127) (type solid)) (layer "F.SilkS") (tstamp 493944ba-2a05-4061-ac1f-acb63d292ba7))
  (fp_line (start -3.2135 -4.38) (end -4.37 -4.38)
    (stroke (width 0.127) (type solid)) (layer "F.SilkS") (tstamp f082f980-e0a4-4e4a-8d23-558c3d9cf644))
  (fp_line (start 3.2135 -4.38) (end 4.37 -4.38)
    (stroke (width 0.127) (type solid)) (layer "F.SilkS") (tstamp 6bb4266f-6bf0-4ad3-b62b-b310a75876d4))
  (fp_line (start 4.37 -4.38) (end 4.37 -3.494)
    (stroke (width 0.127) (type solid)) (layer "F.SilkS") (tstamp 908fd584-4ed3-4e7f-bc5c-324d6e2793d1))
  (fp_line (start 4.37 -0.8665) (end 4.37 1.2365)
    (stroke (width 0.127) (type solid)) (layer "F.SilkS") (tstamp 2a031c4d-e0a5-4a8c-ae72-cbc623abcb4f))
  (fp_line (start 4.37 6) (end 4.37 3.854)
    (stroke (width 0.127) (type solid)) (layer "F.SilkS") (tstamp 5193ca69-8e3c-4406-a129-d169f1b1bd94))
  (fp_circle (center -3.3 -5.1) (end -3.1 -5.1)
    (stroke (width 0.1) (type solid)) (fill solid) (layer "F.SilkS") (tstamp 8a88d903-36a3-40b4-bc91-f57fdadb71fe))
  (fp_line (start -5.02 -5.43) (end -5.02 7.2)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b45b0a0f-39ff-49eb-bb29-b02df6d054d2))
  (fp_line (start -5.02 7.2) (end 5.02 7.2)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp c4ce677f-d29e-4559-9c43-c2f6689bc306))
  (fp_line (start 5.02 -5.43) (end -5.02 -5.43)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 1c339368-98ea-44b7-9a55-8e8e6b95308d))
  (fp_line (start 5.02 7.2) (end 5.02 -5.43)
    (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 73621b1c-ae10-4809-be22-992807bee143))
  (fp_line (start -4.37 -4.38) (end 4.37 -4.38)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 54385215-4e48-47c1-ab79-39e3a637ab9c))
  (fp_line (start -4.37 6) (end -4.37 -4.38)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp c222a586-f318-4522-96c7-bfe34f391869))
  (fp_line (start -4.37 6) (end 4.37 6)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 63d63a88-f325-4e82-ae10-5abc19c5a7b1))
  (fp_line (start -4.37 6.95) (end -4.37 6)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp f786a291-c993-4af5-9a22-cca43fb93fe9))
  (fp_line (start 4.37 -4.38) (end 4.37 6)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 79d44454-7db1-4071-8b83-bb0794a786b8))
  (fp_line (start 4.37 6) (end 4.37 6.95)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp ac4ca388-0cc8-4cd6-901e-6137e4459e36))
  (fp_line (start 4.37 6.95) (end -4.37 6.95)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 4493feaf-09b2-48c0-9ae9-b0b2a0dff981))
  (fp_circle (center -3.2 -5.1) (end -3.15 -5.1)
    (stroke (width 0.1) (type solid)) (fill none) (layer "F.Fab") (tstamp 5b817bda-8f20-4a7f-9d3c-afbd15a4454e))
  (pad "A1" smd rect (at -2.75 -4.58 90) (size 1.2 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp b820c4a6-f591-4178-bc57-3a01b6f24355))
  (pad "A2" smd rect (at -2.25 -4.58 90) (size 1.2 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp aea5ff00-0f62-4373-8e21-bf1117633565))
  (pad "A3" smd rect (at -1.75 -4.58 90) (size 1.2 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 30d973ea-9a8a-4e2b-b603-c6c7c48f5067))
  (pad "A4" smd rect (at -1.25 -4.58 90) (size 1.2 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 48e6904d-bfe4-4e2b-a11f-2f514832f1e1))
  (pad "A5" smd rect (at -0.75 -4.58 90) (size 1.2 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp d468548c-1076-4184-b145-2a2453fe7701))
  (pad "A6" smd rect (at -0.25 -4.58 90) (size 1.2 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 80d384b1-99e6-4abd-b656-5db05bc805f4))
  (pad "A7" smd rect (at 0.25 -4.58 90) (size 1.2 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp ff58070c-5b83-4bc5-957b-76aa96cda3c5))
  (pad "A8" smd rect (at 0.75 -4.58 90) (size 1.2 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp c313c19a-b122-467f-9ed7-167667a5be4b))
  (pad "A9" smd rect (at 1.25 -4.58 90) (size 1.2 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 45055702-52f8-4a9c-9511-4add789e323f))
  (pad "A10" smd rect (at 1.75 -4.58 90) (size 1.2 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 8492773c-a8d5-4a59-8d45-c924e4affb13))
  (pad "A11" smd rect (at 2.25 -4.58 90) (size 1.2 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 431a8f5a-4236-432c-9ddd-9df33e5104ec))
  (pad "A12" smd rect (at 2.75 -4.58 90) (size 1.2 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp 4a22c01a-e2ad-4821-93dd-1133b4b2421d))
  (pad "B1" thru_hole circle (at 2.8 -3.33) (size 0.65 0.65) (drill 0.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp 306a2fc3-0e17-4ddd-ab1f-7d79efe9d8ea))
  (pad "B2" thru_hole circle (at 2.4 -2.63) (size 0.65 0.65) (drill 0.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp f298a9ed-ca78-4408-9253-8e910164eff3))
  (pad "B3" thru_hole circle (at 1.6 -2.63) (size 0.65 0.65) (drill 0.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp d539ae2b-ec02-420e-b639-b33f9772fff4))
  (pad "B4" thru_hole circle (at 1.2 -3.33) (size 0.65 0.65) (drill 0.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp 1ce85e02-d4b0-4859-a90c-29086b20ca2f))
  (pad "B5" thru_hole circle (at 0.8 -2.63) (size 0.65 0.65) (drill 0.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp 5b084355-7ee1-4b77-8c6f-638cbefb9de4))
  (pad "B6" thru_hole circle (at 0.4 -3.33) (size 0.65 0.65) (drill 0.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp e449c33c-f2b7-45a3-8c1a-7c41c6293201))
  (pad "B7" thru_hole circle (at -0.4 -3.33) (size 0.65 0.65) (drill 0.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp 3095f710-9734-4be7-939f-4b80d36639ee))
  (pad "B8" thru_hole circle (at -0.8 -2.63) (size 0.65 0.65) (drill 0.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp 158d17a9-d0fd-4047-aea4-0135982f10b2))
  (pad "B9" thru_hole circle (at -1.2 -3.33) (size 0.65 0.65) (drill 0.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp ccd1b43f-56c9-4539-b473-efbeeea5dc53))
  (pad "B10" thru_hole circle (at -1.6 -2.63) (size 0.65 0.65) (drill 0.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp b5c3dc20-7cdc-45f3-a3a5-846da8942552))
  (pad "B11" thru_hole circle (at -2.4 -2.63) (size 0.65 0.65) (drill 0.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp 5ce5bcac-83fb-4243-a659-af9b02cad7db))
  (pad "B12" thru_hole circle (at -2.8 -3.33) (size 0.65 0.65) (drill 0.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp fa74d6d4-e8a5-415e-a3f2-aec55d2e7a40))
  (pad "GND" thru_hole circle (at 2 -3.33) (size 0.65 0.65) (drill 0.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp af8cad14-bcf5-4f54-b771-8f223eb3c3ec))
  (pad "GND1" thru_hole circle (at -2 -3.33) (size 0.65 0.65) (drill 0.4) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp 7fe3924a-7f0a-4138-a5b6-a24c5cf1b35d))
  (pad "S1" thru_hole oval (at -4.27 -2.18) (size 1 2) (drill oval 0.6 1.2) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp 5660b56a-431e-46e5-a88b-7f28a1ba0ce6))
  (pad "S2" thru_hole oval (at 4.27 -2.18) (size 1 2) (drill oval 0.6 1.2) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp 1e0328c3-ce3d-4f2b-8d1e-3cdc1e31cc2d))
  (pad "S3" thru_hole oval (at 4.27 2.55) (size 1 2) (drill oval 0.6 1.2) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp e8c9dc26-7667-4ba7-bcda-88c103f81b73))
  (pad "S4" thru_hole oval (at -4.27 2.55) (size 1 2) (drill oval 0.6 1.2) (layers "*.Cu" "*.Mask")
    (solder_mask_margin 0.102) (tstamp 7080c940-9877-463b-82f1-f63df990b7b7))
  (pad "S5" smd rect (at 0 0 90) (size 1 0.2) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp d257f22a-193e-447f-8a32-7fbf9e9a791e))
  (pad "S6" smd rect (at 0 3.1 90) (size 1 0.2) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.102) (tstamp e56324b5-ace6-42e1-9a3d-e26c7590fb41))
  (model "${KIPRJMOD}/tinytapeout-kicad-libs/3dmodels/632723300011.step"
    (offset (xyz 0 -2.1 1.5))
    (scale (xyz 1 1 1))
    (rotate (xyz 90 0 180))
  )
)
