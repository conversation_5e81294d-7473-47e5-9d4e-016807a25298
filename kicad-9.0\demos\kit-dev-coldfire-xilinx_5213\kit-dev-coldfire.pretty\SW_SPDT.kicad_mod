(footprint "SW_SPDT"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "Switch inverseur")
	(tags "SWITCH DEV")
	(property "Reference" "REF**"
		(at 9.017 2.413 180)
		(layer "F.SilkS")
		(uuid "f89512bb-b1fb-470b-87a5-7b2b11fa7530")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "SWITCH_INV"
		(at 8.89 -2.286 180)
		(layer "F.SilkS")
		(uuid "613294c2-ceaa-432e-b192-f9a104a9c931")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "7a37c010-7ee8-4609-8821-72582c95a81a")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "3f796942-8fdb-463b-9e0e-9e02b47f1968")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start 2.54 -6.35)
		(end 2.54 6.35)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "86dbf71b-8eb4-4faa-83dc-0b835c3194dd")
	)
	(fp_line
		(start 2.54 6.35)
		(end 15.24 6.35)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "fe2668c1-8f5b-4adb-a39e-e845258b885b")
	)
	(fp_line
		(start 15.24 -6.35)
		(end 2.54 -6.35)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "b70bf016-f051-463f-84c7-85a3720193f5")
	)
	(fp_line
		(start 15.24 -3.175)
		(end 22.86 -3.175)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "01117594-e21d-4e5d-9211-583bd34a1ed3")
	)
	(fp_line
		(start 15.24 6.35)
		(end 15.24 -6.35)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "60049b74-b80b-4f5f-9ba6-4be22b6fff5b")
	)
	(fp_line
		(start 22.86 -3.175)
		(end 22.86 3.175)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "d160c1a3-d2ce-43a6-bdb1-49f4e76ca805")
	)
	(fp_line
		(start 22.86 -1.27)
		(end 33.02 -3.175)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "6454423b-cbe0-41ef-b8a2-4a918189b44e")
	)
	(fp_line
		(start 22.86 3.175)
		(end 15.24 3.175)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "56e273f6-d330-45ab-84f4-5095d3d110c1")
	)
	(fp_line
		(start 33.02 -3.175)
		(end 33.655 -3.175)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "92a41416-1bbb-4db3-9f10-06690932b2cb")
	)
	(fp_line
		(start 33.655 -3.175)
		(end 34.29 -2.54)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "46514378-6f3b-4b40-8628-b1f55657f736")
	)
	(fp_line
		(start 33.655 -0.635)
		(end 22.86 1.27)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "c30a2578-82bf-453b-9c9f-af72fe1073ee")
	)
	(fp_line
		(start 34.29 -2.54)
		(end 34.29 -1.905)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "0efb09a2-5ebe-4c38-951b-52f60b99eb1b")
	)
	(fp_line
		(start 34.29 -1.905)
		(end 34.29 -1.27)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "ba6c7fbb-1d98-449d-9505-860876272f6f")
	)
	(fp_line
		(start 34.29 -1.27)
		(end 33.655 -0.635)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "8d097981-c91a-475f-bcf5-d0dae13c08b4")
	)
	(fp_line
		(start -2.5 -6.75)
		(end 35 -6.75)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "1bf4770e-0533-45d9-a45f-048ce01607dc")
	)
	(fp_line
		(start -2.5 6.75)
		(end -2.5 -6.75)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "85602767-911a-4c91-b24c-cdf4cfb4a51c")
	)
	(fp_line
		(start 35 -6.75)
		(end 35 6.75)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "c211b238-10aa-4926-9566-287e8e9daae2")
	)
	(fp_line
		(start 35 6.75)
		(end -2.5 6.75)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "1047b0a6-**************-47df700ba456")
	)
	(pad "1" thru_hole rect
		(at 0 -5.08)
		(size 3.81 2.54)
		(drill 1.6)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "50e3e900-47fe-4e07-98f3-6495e1ae67ab")
	)
	(pad "2" thru_hole rect
		(at 0 0)
		(size 3.81 2.54)
		(drill 1.6)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "bebd02e8-2932-49cb-98c4-8980a8634756")
	)
	(pad "3" thru_hole rect
		(at 0 5.08)
		(size 3.81 2.54)
		(drill 1.6)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c3949fc2-917a-4c78-ae6a-adf0b392d325")
	)
	(embedded_fonts no)
	(model "${KIPRJMOD}/prj.3dshapes/Device.switch_toggle_horizontal_right_angle_PC_terminal.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
