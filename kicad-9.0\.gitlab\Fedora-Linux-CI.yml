##########################################################################
# Build KiCad on Fedora and save the results
##########################################################################
.fedora_build_linux_base:
  stage: build
# Don't tag until we have separate CI for MRs
#  tags:
#    - kicad-fedora
  image: $DEFAULT_FEDORA_IMAGE
  extends: .only_code
  interruptible: false
  cache:
    key: "cache-fedora-linux"
    paths:
      - ccache/
  before_script:
    # CCache Config
    - mkdir -p ccache
    - export CCACHE_BASEDIR=${PWD}
    - export CCACHE_DIR=${PWD}/ccache
  script:
    - mkdir -p build/linux
    - cd build/linux
    - cmake
      -G Ninja
      -DCMAKE_BUILD_TYPE=QABUILD
      -DKICAD_STDLIB_LIGHT_DEBUG=ON
      -DKICAD_BUILD_I18N=ON
      -DKICAD_TEST_XML_OUTPUT=ON
      -DKICAD_BUILD_PNS_DEBUG_TOOL=ON
      -DKICAD_USE_CMAKE_FINDPROTOBUF=ON
      ../../
    - ninja 2>&1 | tee compilation_log.txt
    - cd ../../
  artifacts:
    # Only save the artifacts that are needed for running the tests in the next stage
    # and the compilation log. The entire build directory is too large to save as an
    # artifact.
    expire_in: 2 hrs
    when: always
    paths:
      - build/linux/3d-viewer/
      - build/linux/api/libkiapi.so*
      - build/linux/common/libkicommon.so*
      - build/linux/common/gal/libkigal.so*
      - build/linux/eeschema/_eeschema.kiface
      - build/linux/cvpcb/_cvpcb.kiface
      - build/linux/kicad/kicad-cli
      - build/linux/pcbnew/pcbnew.py
      - build/linux/pcbnew/_pcbnew.kiface
      - build/linux/pcbnew/_pcbnew.so
      - build/linux/qa/
      - build/linux/schemas/
      - build/linux/compilation_log.txt
    exclude:
      - build/linux/**/*.o
      - build/linux/**/*.a

# This split is to ensure we can always use our runner for our jobs
fedora_build_linux_kicad:
  extends: .fedora_build_linux_base
  tags:
    - kicad-fedora
  only:
    variables:
      - $CI_PROJECT_ROOT_NAMESPACE == "kicad"

fedora_build_linux_public:
  extends: .fedora_build_linux_base
  tags:
    - saas-linux-medium-amd64
  except:
    variables:
      - $CI_PROJECT_ROOT_NAMESPACE == "kicad"


# Upload the compilation log in an easily downloadable form
.fedora_report_build_warn:
  stage: report
  extends: .only_code
  when: always
  script:
    - echo "Uploading compilation log"
    - cp build/linux/compilation_log.txt compilation_log.txt
  artifacts:
    expire_in: 1 year
    expose_as: 'Build log'
    name: "build_log.txt"
    paths:
      - compilation_log.txt

fedora_report_build_warn_kicad:
  extends: .fedora_report_build_warn
  tags:
    - kicad-fedora
  needs:
    - job: fedora_build_linux_kicad
      artifacts: true
  only:
    variables:
      - $CI_PROJECT_ROOT_NAMESPACE == "kicad"

fedora_report_build_warn_public:
  extends: .fedora_report_build_warn
  needs:
    - job: fedora_build_linux_public
      artifacts: true
  except:
    variables:
      - $CI_PROJECT_ROOT_NAMESPACE == "kicad"


# Report on the metrics of the code
.fedora_report_metrics:
  stage: report
  extends: .only_code
  when: always
  script:
    - cat build/linux/compilation_log.txt | { grep "warning:" || test $? = 1; } | awk 'END{print "number_of_fedora_warnings "NR}' > metrics.txt
    - cat metrics.txt
  artifacts:
    reports:
      metrics: metrics.txt

fedora_report_metrics_kicad:
  extends: .fedora_report_metrics
  tags:
    - kicad-fedora
  needs:
    - job: fedora_build_linux_kicad
      artifacts: true
  only:
    variables:
      - $CI_PROJECT_ROOT_NAMESPACE == "kicad"

fedora_report_metrics_public:
  extends: .fedora_report_metrics
  needs:
    - job: fedora_build_linux_public
      artifacts: true
  except:
    variables:
      - $CI_PROJECT_ROOT_NAMESPACE == "kicad"

##########################################################################
# Run the code unit tests.
##########################################################################
.fedora_qa:
  image: $DEFAULT_FEDORA_IMAGE
  extends:
    - .unit_test
    - .only_code
  before_script:
    - if [ "$TEST" == "cli" ] || [ "$TEST" == "python" ]; then python3 -m pip install -r qa/tests/requirements.txt; fi
    - if [ "$TEST" == "cli" ]; then sudo dnf -y install gerbv; fi
  parallel:
    matrix:
      # The name of the test without the qa_ prefix
      - TEST: [python, cli, common, gerbview, pcbnew, pns_regressions, eeschema, kimath, sexpr, kicad2step, spice, api]

fedora_qa_kicad:
  extends: .fedora_qa
  tags:
    - kicad-fedora
  needs:
    - job: fedora_build_linux_kicad
      artifacts: true
  only:
    variables:
      - $CI_PROJECT_ROOT_NAMESPACE == "kicad"

fedora_qa_public:
  extends: .fedora_qa
  needs:
    - job: fedora_build_linux_public
      artifacts: true
  except:
    variables:
      - $CI_PROJECT_ROOT_NAMESPACE == "kicad"
