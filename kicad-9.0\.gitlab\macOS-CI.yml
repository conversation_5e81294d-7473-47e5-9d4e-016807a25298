##########################################################################
# Build KiCad on Windows and save the results
##########################################################################
macos_build:
  stage: build
  tags:
    - kicad-macos
  interruptible: false
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_PROJECT_ROOT_NAMESPACE == "kicad"
  script:
    - mkdir -p build/macos
    - cd build/macos
    - cmake
      -G Ninja
      -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/kicad-mac-builder/toolchain/kicad-mac-builder.cmake
      ../../
    - cmake --build . 2>&1 | tee compilation_log.txt
    - cd ../../
  artifacts:
    # Only save the artifacts that are needed for running the tests in the next stage
    # and the compilation log. The entire build directory is too large to save as an
    # artifact.
    expire_in: 2 hrs
    when: always
    paths:
      - build/macos/compilation_log.txt
