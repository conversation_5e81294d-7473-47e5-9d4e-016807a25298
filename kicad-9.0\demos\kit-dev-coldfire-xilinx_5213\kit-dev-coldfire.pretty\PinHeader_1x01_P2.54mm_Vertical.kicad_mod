(footprint "PinHeader_1x01_P2.54mm_Vertical"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "Through hole straight pin header, 1x01, 2.54mm pitch, single row")
	(tags "Through hole pin header THT 1x01 2.54mm single row")
	(property "Reference" "REF**"
		(at -1.918 -0.377 90)
		(layer "F.SilkS")
		(uuid "941e2603-6732-4854-938d-e11ab23bc39c")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "CONN_1"
		(at 0 2.33 0)
		(layer "F.Fab")
		(uuid "32be9b37-27d1-49cf-8340-1024954a0786")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "1769f25f-ece2-461f-8f42-7d6d028178a3")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "58668a60-467b-4751-b570-a7065eff2f23")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -1.33 -1.33)
		(end 0 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "47a3aaa4-dc65-4f7c-8028-4a2bfbfa7494")
	)
	(fp_line
		(start -1.33 0)
		(end -1.33 -1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "cfa60ee3-9002-4b1b-91d6-da57cecc7f84")
	)
	(fp_line
		(start -1.33 1.27)
		(end -1.33 1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "97227afd-986d-46b9-9d7e-c5cc51b130f6")
	)
	(fp_line
		(start -1.33 1.27)
		(end 1.33 1.27)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "b56e8cbb-a055-405d-97bf-69dc4300cdcf")
	)
	(fp_line
		(start -1.33 1.33)
		(end 1.33 1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "efeda525-5333-40ce-9eb8-e466ad0c1ef7")
	)
	(fp_line
		(start 1.33 1.27)
		(end 1.33 1.33)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "d473793c-80a2-4944-8a11-3b244cee876c")
	)
	(fp_line
		(start -1.8 -1.8)
		(end -1.8 1.8)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "93ee3c3f-6bc5-44ba-b2dc-602a84fcfafc")
	)
	(fp_line
		(start -1.8 1.8)
		(end 1.8 1.8)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "130733d6-80f5-4b36-8793-5cca6cb47018")
	)
	(fp_line
		(start 1.8 -1.8)
		(end -1.8 -1.8)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "0484d29a-2b36-46e4-a815-2a54547d0dba")
	)
	(fp_line
		(start 1.8 1.8)
		(end 1.8 -1.8)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "cd9c803d-034a-4d56-a575-4e568e004ac8")
	)
	(fp_line
		(start -1.27 -0.635)
		(end -0.635 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "108356e7-fe9d-469c-944a-37f4d3adf381")
	)
	(fp_line
		(start -1.27 1.27)
		(end -1.27 -0.635)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "9bcd963f-ea78-4a38-bf4e-da707088dfaa")
	)
	(fp_line
		(start -0.635 -1.27)
		(end 1.27 -1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "c6570547-32d3-479b-a1bd-22f6bc1fbc72")
	)
	(fp_line
		(start 1.27 -1.27)
		(end 1.27 1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "699ee44a-879b-4bfd-9421-31a010c793af")
	)
	(fp_line
		(start 1.27 1.27)
		(end -1.27 1.27)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "9007b759-7edf-4712-ba3a-45fef0866b94")
	)
	(fp_text user "${REFERENCE}"
		(at 0 0 90)
		(layer "F.Fab")
		(uuid "20bd97ee-e2c2-450b-ac4d-5ef58f9e898d")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 1.7 1.7)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "ecf959d0-76b2-444b-b347-fe6ebdb14b0b")
	)
	(embedded_fonts no)
	(model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x01_P2.54mm_Vertical.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
