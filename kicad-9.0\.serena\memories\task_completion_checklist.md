# KiCad Task Completion Checklist

## When Making S-Expression Keyword Changes

### 1. Validate Keyword Format
- Ensure keywords follow naming rules (lowercase, underscores only)
- Check for duplicates within the same .keywords file
- Verify no conflicts with existing keywords in other files

### 2. Build and Test
```cmd
# Regenerate lexer classes
cmake --build build --target clean  
cmake -B build -S .
cmake --build build

# Check for compilation errors
# Focus on generated *_lexer.h and *_keywords.cpp files
```

### 3. Update Parser Code
- Add handling for new keywords in relevant parser classes
- Update switch/case statements to handle new token types
- Ensure proper error handling for new syntax

### 4. Test File Format Changes
- Create test files using new keywords
- Verify round-trip (save/load) functionality works
- Test edge cases and error conditions

### 5. Documentation
- Update any relevant documentation if keywords affect user-visible features
- Add comments in parser code explaining new keyword semantics

## General Development Tasks

### Before Committing
- Run build to ensure no compilation errors
- Check that all modified files follow coding conventions
- Verify line endings are consistent (LF for cross-platform compatibility)

### Code Quality Checks
- Use clang-format if available for consistent formatting
- Check for memory leaks in parser modifications
- Ensure proper exception handling in parsing code

### Testing Considerations  
- Test with various file sizes and complexity
- Verify error messages are helpful and accurate
- Check performance impact of keyword table changes

## When Adding New File Formats
1. Create new .keywords file following naming conventions
2. Add make_lexer() call in appropriate CMakeLists.txt
3. Implement parser class inheriting from generated lexer
4. Add file format to appropriate I/O managers
5. Create test cases for new format