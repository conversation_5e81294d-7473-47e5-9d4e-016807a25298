(footprint "Crystal_4-SMD_2.5x2mm"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(property "Reference" "REF**"
		(at 2.135711 -0.830583 90)
		(layer "F.SilkS")
		(uuid "1223da39-57cc-4c92-8ff5-b3cc7c446e17")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "12MHz 50ppm 16-20pF"
		(at 0 0.75 0)
		(layer "F.Fab")
		(hide yes)
		(uuid "f3354eb3-261a-4521-ac67-0a88b30f7395")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.15)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "B.Fab")
		(hide yes)
		(uuid "f84d4a2b-b378-46ed-8d6c-4ae7574f3156")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
			(justify mirror)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "B.Fab")
		(hide yes)
		(uuid "f75a710a-7e67-4131-8a0a-5c8e9ea4b148")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
			(justify mirror)
		)
	)
	(property "MPN" "CM4012M00020T8188026"
		(at 0 0 45)
		(layer "F.Fab")
		(hide yes)
		(uuid "45d5ee66-c633-47ec-8d06-bdcb4f8459f5")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(solder_mask_margin 0.001)
	(solder_paste_margin -0.025)
	(attr smd)
	(fp_poly
		(pts
			(xy -1.175 1.475) (xy -1.425 1.475) (xy -1.3 1.275)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(fill yes)
		(layer "F.SilkS")
		(uuid "51d9799f-80ba-43e7-9df6-9ab70bf43ec2")
	)
	(fp_poly
		(pts
			(xy 1.5 0.1) (xy 1.35 0.1) (xy 1.35 -0.1) (xy 1.5 -0.1)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(fill yes)
		(layer "F.SilkS")
		(uuid "f3d0cf92-8c18-446a-a788-643a4d502e59")
	)
	(fp_poly
		(pts
			(xy 0.2 1.25) (xy -0.2 1.25) (xy -0.2 1.1) (xy 0.2 1.1)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(fill yes)
		(layer "F.SilkS")
		(uuid "95f0ba5f-5caa-471b-8592-3ddf6f99e1d6")
	)
	(fp_poly
		(pts
			(xy 0.2 -1.1) (xy -0.2 -1.1) (xy -0.2 -1.25) (xy 0.2 -1.25)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(fill yes)
		(layer "F.SilkS")
		(uuid "b417a1a2-0eaf-4e51-a2f2-4885e4c44962")
	)
	(fp_poly
		(pts
			(xy -1.35 0.1) (xy -1.5 0.1) (xy -1.5 -0.1) (xy -1.35 -0.1)
		)
		(stroke
			(width 0)
			(type solid)
		)
		(fill yes)
		(layer "F.SilkS")
		(uuid "d3548c21-a9c8-4d5b-8e1c-37f651798f8c")
	)
	(fp_line
		(start 1.5 1.25)
		(end 1.5 -1.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "df6508f6-7c13-4b43-8598-35d6c9eee27b")
	)
	(fp_line
		(start 1.5 -1.25)
		(end -1.5 -1.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "4f67c9fc-3d04-4f7d-bfd3-4e6109f59da5")
	)
	(fp_line
		(start -1.5 1.25)
		(end 1.5 1.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "71ea956f-8b63-411a-bf4d-4a4645564cf6")
	)
	(fp_line
		(start -1.5 -1.25)
		(end -1.5 1.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(uuid "1a480a9a-4b00-4212-987b-49acb01da967")
	)
	(fp_line
		(start 1.25 1)
		(end 1.25 -1)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "1ac0a3d3-52ae-4b4f-a392-6e4469b66206")
	)
	(fp_line
		(start 1.25 -1)
		(end -1.25 -1)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "63bea4ed-1c0d-4e7e-9fa4-4c57e6517572")
	)
	(fp_line
		(start -1.25 1)
		(end 1.25 1)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "48755f10-0c20-44b8-8004-002efb01be24")
	)
	(fp_line
		(start -1.25 -1)
		(end -1.25 1)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "d8ad8ced-752a-4637-8ab3-fc3f38159ab1")
	)
	(fp_line
		(start -1.25 -1)
		(end -1.25 -1)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "add47800-9aa2-465c-b366-d2520f923c19")
	)
	(pad "" smd roundrect
		(at -0.85 -0.7 180)
		(size 1 1)
		(layers "F.Paste")
		(roundrect_rratio 0.33)
		(uuid "76b78415-4bb8-4c26-8b09-c50114890136")
	)
	(pad "" smd roundrect
		(at -0.85 0.7 180)
		(size 1 1)
		(layers "F.Paste")
		(roundrect_rratio 0.33)
		(uuid "a74e9e35-782f-4de7-8f4c-3bb69d55412e")
	)
	(pad "" smd roundrect
		(at 0.85 -0.7 180)
		(size 1 1)
		(layers "F.Paste")
		(roundrect_rratio 0.33)
		(uuid "90bc91a0-d89e-47f1-a858-3cf8981c92b9")
	)
	(pad "" smd roundrect
		(at 0.85 0.7 180)
		(size 1 1)
		(layers "F.Paste")
		(roundrect_rratio 0.33)
		(uuid "20d6bfa2-21f3-4da6-94c7-d4d24784a315")
	)
	(pad "1" smd roundrect
		(at -0.85 0.7 180)
		(size 1.2 1.1)
		(layers "F.Cu" "F.Mask")
		(roundrect_rratio 0.1)
		(uuid "78571f75-ad83-4395-b57e-872d23c39947")
	)
	(pad "2" smd roundrect
		(at 0.85 0.7 180)
		(size 1.2 1.1)
		(layers "F.Cu" "F.Mask")
		(roundrect_rratio 0.1)
		(uuid "06f86e7d-d819-4ecd-bfad-83997f925436")
	)
	(pad "3" smd roundrect
		(at 0.85 -0.7 180)
		(size 1.2 1.1)
		(layers "F.Cu" "F.Mask")
		(roundrect_rratio 0.1)
		(uuid "a6c80730-9093-413b-9e82-684d2b0a9663")
	)
	(pad "4" smd roundrect
		(at -0.85 -0.7 180)
		(size 1.2 1.1)
		(layers "F.Cu" "F.Mask")
		(roundrect_rratio 0.1)
		(uuid "9aa3f3b8-502f-41e7-a472-3794cd7601fd")
	)
	(embedded_fonts no)
	(model "${KIPRJMOD}/3dmodels/Crystal_SMD_4P_2520.step"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz -90 0 0)
		)
	)
)
