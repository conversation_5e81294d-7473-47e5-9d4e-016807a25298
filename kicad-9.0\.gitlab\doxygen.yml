build_doxygen_docker:
  image: docker:stable
  services:
    - docker:dind
  stage: build
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule" && $SCHEDULED_JOB_NAME == "doxygen"
  tags:
    - gitlab-org-docker
  before_script:
    - docker info
    - docker login -u gitlab-ci-token -p "$CI_JOB_TOKEN" "$CI_REGISTRY"
  script:
    - docker build -t "${CI_REGISTRY_IMAGE}/doxygen:$CI_COMMIT_BRANCH" -f ./doxygen/doxygen.Dockerfile .
    - docker push "${CI_REGISTRY_IMAGE}/doxygen:$CI_COMMIT_BRANCH"
