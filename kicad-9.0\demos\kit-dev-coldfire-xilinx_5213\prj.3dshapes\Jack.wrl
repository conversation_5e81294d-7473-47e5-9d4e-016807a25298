#VRML V2.0 utf8
#Exported from Wings 3D 0.98.36
DEF cube1_sep13 Transform {
  children [
    Shape {
      appearance Appearance {
        material DEF couleur_220_223_223 Material {
          diffuseColor 0.860000 0.873332 0.873332
          emissiveColor 0.00000e+0 0.00000e+0 0.00000e+0
          specularColor 1.00000 1.00000 1.00000
          ambientIntensity 1.00000
          transparency 0.00000e+0
          shininess 1.00000
        }
      }
      geometry IndexedFaceSet {
        normalPerVertex TRUE
        coord Coordinate { point [
          0.627981 -2.62178 -1.08830,
          1.87798 -2.62178 -1.08830,
          1.83041 -2.62178 -1.32748,
          1.69492 -2.62178 -1.53025,
          1.49216 -2.62178 -1.66573,
          1.25298 -2.62178 -1.71330,
          1.01380 -2.62178 -1.66573,
          0.811039 -2.62178 -1.53025,
          0.675556 -2.62178 -1.32748,
          0.627981 -2.37178 -1.08830,
          1.87798 -2.37178 -1.08830,
          1.83041 -2.37178 -1.32748,
          1.69492 -2.37178 -1.53025,
          1.49216 -2.37178 -1.66573,
          1.25298 -2.37178 -1.71330,
          1.01380 -2.37178 -1.66573,
          0.811039 -2.37178 -1.53025,
          0.675556 -2.37178 -1.32748 ] }
        coordIndex [
          0, 1, 10, 9, -1,
          0, 8, 7, 6, 5, 4, 3, 2, 1, -1,
          0, 9, 17, 8, -1,
          1, 2, 11, 10, -1,
          2, 3, 12, 11, -1,
          3, 4, 13, 12, -1,
          4, 5, 14, 13, -1,
          5, 6, 15, 14, -1,
          6, 7, 16, 15, -1,
          7, 8, 17, 16, -1,
          9, 10, 11, 12, 13, 14, 15, 16, 17, -1 ]
        normal Normal { vector [
          -0.607112 -0.619006 0.498244,
          0.607112 -0.619006 0.498244,
          0.823093 -0.454182 -0.340936,
          0.629968 -0.454182 -0.629968,
          0.340936 -0.454182 -0.823093,
          -2.26909e-16 -0.454182 -0.890909,
          -0.340936 -0.454182 -0.823093,
          -0.629968 -0.454182 -0.629968,
          -0.823093 -0.454182 -0.340936,
          -0.607112 0.619006 0.498244,
          0.607112 0.619006 0.498244,
          0.823093 0.454182 -0.340936,
          0.629968 0.454182 -0.629968,
          0.340936 0.454182 -0.823093,
          -2.26909e-16 0.454182 -0.890909,
          -0.340936 0.454182 -0.823093,
          -0.629968 0.454182 -0.629968,
          -0.823093 0.454182 -0.340936 ] }
        normalIndex [
          0, 1, 10, 9, -1,
          0, 8, 7, 6, 5, 4, 3, 2, 1, -1,
          0, 9, 17, 8, -1,
          1, 2, 11, 10, -1,
          2, 3, 12, 11, -1,
          3, 4, 13, 12, -1,
          4, 5, 14, 13, -1,
          5, 6, 15, 14, -1,
          6, 7, 16, 15, -1,
          7, 8, 17, 16, -1,
          9, 10, 11, 12, 13, 14, 15, 16, 17, -1 ]
      }
    }
  ]
}

DEF cube1_sep12 Transform {
  children [
    Shape {
      appearance Appearance {
        material USE couleur_220_223_223
      }
      geometry IndexedFaceSet {
        normalPerVertex TRUE
        coord Coordinate { point [
          1.87798 -2.37178 1.91170,
          0.627981 -2.37178 1.91170,
          0.627981 -2.12178 1.91170,
          1.87798 -2.12178 1.91170,
          1.87798 -2.37178 1.66170,
          0.627981 -2.37178 1.66170,
          0.627981 -2.12178 1.66170,
          1.87798 -2.12178 1.66170 ] }
        coordIndex [
          0, 1, 5, 4, -1,
          0, 3, 2, 1, -1,
          0, 4, 7, 3, -1,
          1, 2, 6, 5, -1,
          2, 3, 7, 6, -1,
          4, 5, 6, 7, -1 ]
        normal Normal { vector [
          0.577350 -0.577350 0.577350,
          -0.577350 -0.577350 0.577350,
          -0.577350 0.577350 0.577350,
          0.577350 0.577350 0.577350,
          0.577350 -0.577350 -0.577350,
          -0.577350 -0.577350 -0.577350,
          -0.577350 0.577350 -0.577350,
          0.577350 0.577350 -0.577350 ] }
        normalIndex [
          0, 1, 5, 4, -1,
          0, 3, 2, 1, -1,
          0, 4, 7, 3, -1,
          1, 2, 6, 5, -1,
          2, 3, 7, 6, -1,
          4, 5, 6, 7, -1 ]
      }
    }
  ]
}

DEF cube1_sep17 Transform {
  children [
    Shape {
      appearance Appearance {
        material DEF couleur_025_025_025 Material {
          diffuseColor 0.294040 0.239153 0.239153
          emissiveColor 1.00000e-3 1.00000e-3 1.00000e-3
          specularColor 1.00000 1.00000 1.00000
          ambientIntensity 1.00000
          transparency 0.00000e+0
          shininess 1.00000
        }
      }
      geometry IndexedFaceSet {
        normalPerVertex TRUE
        coord Coordinate { point [
          3.14074 -2.24678 3.23670,
          -2.20926 -2.24678 3.23670,
          -2.20926 2.25322 3.23670,
          3.14074 2.25322 3.23670,
          3.14074 -2.24678 3.66967e-2,
          -2.20926 -2.24678 3.66967e-2,
          -2.20926 2.25322 3.66967e-2,
          3.14074 2.25322 3.66967e-2 ] }
        coordIndex [
          0, 1, 5, 4, -1,
          0, 3, 2, 1, -1,
          0, 4, 7, 3, -1,
          1, 2, 6, 5, -1,
          2, 3, 7, 6, -1,
          4, 5, 6, 7, -1 ]
        normal Normal { vector [
          0.577350 -0.577350 0.577350,
          -0.577350 -0.577350 0.577350,
          -0.577350 0.577350 0.577350,
          0.577350 0.577350 0.577350,
          0.577350 -0.577350 -0.577350,
          -0.577350 -0.577350 -0.577350,
          -0.577350 0.577350 -0.577350,
          0.577350 0.577350 -0.577350 ] }
        normalIndex [
          0, 1, 5, 4, -1,
          0, 3, 2, 1, -1,
          0, 4, 7, 3, -1,
          1, 2, 6, 5, -1,
          2, 3, 7, 6, -1,
          4, 5, 6, 7, -1 ]
      }
    }
  ]
}

DEF cube1_sep16 Transform {
  children [
    Shape {
      appearance Appearance {
        material USE couleur_025_025_025
      }
      geometry IndexedFaceSet {
        normalPerVertex TRUE
        coord Coordinate { point [
          -1.95926 -2.24678 5.53670,
          -3.70926 -2.24678 5.53670,
          -3.70926 2.25322 5.53670,
          -1.95926 2.25322 5.53670,
          -1.95926 -2.24678 3.66967e-2,
          -3.70926 -2.24678 3.66967e-2,
          -3.70926 2.25322 3.66967e-2,
          -1.95926 2.25322 3.66967e-2,
          -2.55926 -1.64678 4.83670,
          -3.70926 -1.64678 4.83670,
          -3.70926 1.65322 4.83670,
          -2.55926 1.65322 4.83670,
          -2.55926 -1.64678 1.63670,
          -3.70926 -1.64678 1.63670,
          -3.70926 1.65322 1.63670,
          -2.55926 1.65322 1.63670 ] }
        coordIndex [
          0, 1, 5, 4, -1,
          0, 3, 2, 1, -1,
          0, 4, 7, 3, -1,
          1, 2, 10, 9, -1,
          1, 9, 13, 5, -1,
          2, 3, 7, 6, -1,
          2, 6, 14, 10, -1,
          4, 5, 6, 7, -1,
          5, 13, 14, 6, -1,
          8, 9, 10, 11, -1,
          8, 11, 15, 12, -1,
          8, 12, 13, 9, -1,
          10, 14, 15, 11, -1,
          12, 15, 14, 13, -1 ]
        normal Normal { vector [
          0.577350 -0.577350 0.577350,
          -0.816497 -0.408248 0.408248,
          -0.816497 0.408248 0.408248,
          0.577350 0.577350 0.577350,
          0.577350 -0.577350 -0.577350,
          -0.816497 -0.408248 -0.408248,
          -0.816497 0.408248 -0.408248,
          0.577350 0.577350 -0.577350,
          -0.577350 0.577350 -0.577350,
          -0.816497 0.408248 -0.408248,
          -0.816497 -0.408248 -0.408248,
          -0.577350 -0.577350 -0.577350,
          -0.577350 0.577350 0.577350,
          -0.816497 0.408248 0.408248,
          -0.816497 -0.408248 0.408248,
          -0.577350 -0.577350 0.577350 ] }
        normalIndex [
          0, 1, 5, 4, -1,
          0, 3, 2, 1, -1,
          0, 4, 7, 3, -1,
          1, 2, 10, 9, -1,
          1, 9, 13, 5, -1,
          2, 3, 7, 6, -1,
          2, 6, 14, 10, -1,
          4, 5, 6, 7, -1,
          5, 13, 14, 6, -1,
          8, 9, 10, 11, -1,
          8, 11, 15, 12, -1,
          8, 12, 13, 9, -1,
          10, 14, 15, 11, -1,
          12, 15, 14, 13, -1 ]
      }
    }
  ]
}

DEF cube1_sep15 Transform {
  children [
    Shape {
      appearance Appearance {
        material USE couleur_025_025_025
      }
      geometry IndexedFaceSet {
        normalPerVertex TRUE
        coord Coordinate { point [
          -2.95926 1.95322 3.23670,
          -2.95926 1.80478 3.98293,
          -2.95926 1.38207 4.61555,
          -2.95926 0.749448 5.03826,
          -2.95926 3.21527e-3 5.18670,
          -2.95926 -0.743017 5.03826,
          -2.95926 -1.37564 4.61555,
          -2.95926 -1.79835 3.98293,
          -2.95926 -1.94678 3.23670,
          -2.95926 -1.79835 2.49046,
          -2.95926 -1.37564 1.85784,
          -2.95926 -0.743017 1.43513,
          -2.95926 3.21527e-3 1.28670,
          -2.95926 0.749448 1.43513,
          -2.95926 1.38207 1.85784,
          -2.95926 1.80478 2.49046,
          3.09074 1.95322 3.23670,
          3.09074 1.80478 3.98293,
          3.09074 1.38207 4.61555,
          3.09074 0.749448 5.03826,
          3.09074 3.21527e-3 5.18670,
          3.09074 -0.743017 5.03826,
          3.09074 -1.37564 4.61555,
          3.09074 -1.79835 3.98293,
          3.09074 -1.94678 3.23670,
          3.09074 -1.79835 2.49046,
          3.09074 -1.37564 1.85784,
          3.09074 -0.743017 1.43513,
          3.09074 3.21527e-3 1.28670,
          3.09074 0.749448 1.43513,
          3.09074 1.38207 1.85784,
          3.09074 1.80478 2.49046,
          -2.95926 1.57087 3.23670,
          -2.95926 1.45154 3.83661,
          -2.95926 1.11171 4.34519,
          -2.95926 0.603130 4.68502,
          -2.95926 3.21527e-3 4.80435,
          -2.95926 -0.596700 4.68502,
          -2.95926 -1.10528 4.34519,
          -2.95926 -1.44511 3.83661,
          -2.95926 -1.56444 3.23670,
          -2.95926 -1.44511 2.63678,
          -2.95926 -1.10528 2.12820,
          -2.95926 -0.596700 1.78837,
          -2.95926 3.21527e-3 1.66904,
          -2.95926 0.603130 1.78837,
          -2.95926 1.11171 2.12820,
          -2.95926 1.45154 2.63678,
          -2.33426 1.57087 3.23670,
          -2.33426 1.45154 3.83661,
          -2.33426 1.11171 4.34519,
          -2.33426 0.603130 4.68502,
          -2.33426 3.21527e-3 4.80435,
          -2.33426 -0.596700 4.68502,
          -2.33426 -1.10528 4.34519,
          -2.33426 -1.44511 3.83661,
          -2.33426 -1.56444 3.23670,
          -2.33426 -1.44511 2.63678,
          -2.33426 -1.10528 2.12820,
          -2.33426 -0.596700 1.78837,
          -2.33426 3.21527e-3 1.66904,
          -2.33426 0.603130 1.78837,
          -2.33426 1.11171 2.12820,
          -2.33426 1.45154 2.63678 ] }
        coordIndex [
          0, 1, 17, 16, -1,
          0, 15, 47, 32, -1,
          0, 16, 31, 15, -1,
          0, 32, 33, 1, -1,
          1, 2, 18, 17, -1,
          1, 33, 34, 2, -1,
          2, 3, 19, 18, -1,
          2, 34, 35, 3, -1,
          3, 4, 20, 19, -1,
          3, 35, 36, 4, -1,
          4, 5, 21, 20, -1,
          4, 36, 37, 5, -1,
          5, 6, 22, 21, -1,
          5, 37, 38, 6, -1,
          6, 7, 23, 22, -1,
          6, 38, 39, 7, -1,
          7, 8, 24, 23, -1,
          7, 39, 40, 8, -1,
          8, 9, 25, 24, -1,
          8, 40, 41, 9, -1,
          9, 10, 26, 25, -1,
          9, 41, 42, 10, -1,
          10, 11, 27, 26, -1,
          10, 42, 43, 11, -1,
          11, 12, 28, 27, -1,
          11, 43, 44, 12, -1,
          12, 13, 29, 28, -1,
          12, 44, 45, 13, -1,
          13, 14, 30, 29, -1,
          13, 45, 46, 14, -1,
          14, 15, 31, 30, -1,
          14, 46, 47, 15, -1,
          16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, -1,
          32, 47, 63, 48, -1,
          32, 48, 49, 33, -1,
          33, 49, 50, 34, -1,
          34, 50, 51, 35, -1,
          35, 51, 52, 36, -1,
          36, 52, 53, 37, -1,
          37, 53, 54, 38, -1,
          38, 54, 55, 39, -1,
          39, 55, 56, 40, -1,
          40, 56, 57, 41, -1,
          41, 57, 58, 42, -1,
          42, 58, 59, 43, -1,
          43, 59, 60, 44, -1,
          44, 60, 61, 45, -1,
          45, 61, 62, 46, -1,
          46, 62, 63, 47, -1,
          48, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, -1 ]
        normal Normal { vector [
          -0.713933 0.700215 1.98156e-17,
          -0.713933 0.646914 0.267960,
          -0.713933 0.495126 0.495126,
          -0.713933 0.267960 0.646914,
          -0.713933 0.00000e+0 0.700215,
          -0.713933 -0.267960 0.646914,
          -0.713933 -0.495126 0.495126,
          -0.713933 -0.646914 0.267960,
          -0.713933 -0.700215 2.37787e-16,
          -0.713933 -0.646914 -0.267960,
          -0.713933 -0.495126 -0.495126,
          -0.713933 -0.267960 -0.646914,
          -0.713933 2.97234e-17 -0.700215,
          -0.713933 0.267960 -0.646914,
          -0.713933 0.495126 -0.495126,
          -0.713933 0.646914 -0.267960,
          0.454182 0.890909 2.52121e-17,
          0.454182 0.823093 0.340936,
          0.454182 0.629968 0.629968,
          0.454182 0.340936 0.823093,
          0.454182 1.26061e-17 0.890909,
          0.454182 -0.340936 0.823093,
          0.454182 -0.629968 0.629968,
          0.454182 -0.823093 0.340936,
          0.454182 -0.890909 3.02546e-16,
          0.454182 -0.823093 -0.340936,
          0.454182 -0.629968 -0.629968,
          0.454182 -0.340936 -0.823093,
          0.454182 5.04243e-17 -0.890909,
          0.454182 0.340936 -0.823093,
          0.454182 0.629968 -0.629968,
          0.454182 0.823093 -0.340936,
          -0.713933 -0.700215 1.28801e-16,
          -0.713933 -0.646914 -0.267960,
          -0.713933 -0.495126 -0.495126,
          -0.713933 -0.267960 -0.646914,
          -0.713933 -1.78340e-16 -0.700215,
          -0.713933 0.267960 -0.646914,
          -0.713933 0.495126 -0.495126,
          -0.713933 0.646914 -0.267960,
          -0.713933 0.700215 -1.18894e-16,
          -0.713933 0.646914 0.267960,
          -0.713933 0.495126 0.495126,
          -0.713933 0.267960 0.646914,
          -0.713933 2.08064e-16 0.700215,
          -0.713933 -0.267960 0.646914,
          -0.713933 -0.495126 0.495126,
          -0.713933 -0.646914 0.267960,
          -0.454182 -0.890909 2.14303e-16,
          -0.454182 -0.823093 -0.340936,
          -0.454182 -0.629968 -0.629968,
          -0.454182 -0.340936 -0.823093,
          -0.454182 -1.89091e-16 -0.890909,
          -0.454182 0.340936 -0.823093,
          -0.454182 0.629968 -0.629968,
          -0.454182 0.823093 -0.340936,
          -0.454182 0.890909 -1.00849e-16,
          -0.454182 0.823093 0.340936,
          -0.454182 0.629968 0.629968,
          -0.454182 0.340936 0.823093,
          -0.454182 3.02546e-16 0.890909,
          -0.454182 -0.340936 0.823093,
          -0.454182 -0.629968 0.629968,
          -0.454182 -0.823093 0.340936 ] }
        normalIndex [
          0, 1, 17, 16, -1,
          0, 15, 47, 32, -1,
          0, 16, 31, 15, -1,
          0, 32, 33, 1, -1,
          1, 2, 18, 17, -1,
          1, 33, 34, 2, -1,
          2, 3, 19, 18, -1,
          2, 34, 35, 3, -1,
          3, 4, 20, 19, -1,
          3, 35, 36, 4, -1,
          4, 5, 21, 20, -1,
          4, 36, 37, 5, -1,
          5, 6, 22, 21, -1,
          5, 37, 38, 6, -1,
          6, 7, 23, 22, -1,
          6, 38, 39, 7, -1,
          7, 8, 24, 23, -1,
          7, 39, 40, 8, -1,
          8, 9, 25, 24, -1,
          8, 40, 41, 9, -1,
          9, 10, 26, 25, -1,
          9, 41, 42, 10, -1,
          10, 11, 27, 26, -1,
          10, 42, 43, 11, -1,
          11, 12, 28, 27, -1,
          11, 43, 44, 12, -1,
          12, 13, 29, 28, -1,
          12, 44, 45, 13, -1,
          13, 14, 30, 29, -1,
          13, 45, 46, 14, -1,
          14, 15, 31, 30, -1,
          14, 46, 47, 15, -1,
          16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, -1,
          32, 47, 63, 48, -1,
          32, 48, 49, 33, -1,
          33, 49, 50, 34, -1,
          34, 50, 51, 35, -1,
          35, 51, 52, 36, -1,
          36, 52, 53, 37, -1,
          37, 53, 54, 38, -1,
          38, 54, 55, 39, -1,
          39, 55, 56, 40, -1,
          40, 56, 57, 41, -1,
          41, 57, 58, 42, -1,
          42, 58, 59, 43, -1,
          43, 59, 60, 44, -1,
          44, 60, 61, 45, -1,
          45, 61, 62, 46, -1,
          46, 62, 63, 47, -1,
          48, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, -1 ]
      }
    }
  ]
}

DEF cube1_sep14 Transform {
  children [
    Shape {
      appearance Appearance {
        material USE couleur_025_025_025
      }
      geometry IndexedFaceSet {
        normalPerVertex TRUE
        coord Coordinate { point [
          -3.70926 2.24572 3.23670,
          -3.70926 2.07502 4.09486,
          -3.70926 1.58890 5.02238,
          -3.70926 0.861383 5.30850,
          -3.70926 3.21527e-3 5.47920,
          -3.70926 -0.854952 5.30850,
          -3.70926 -1.58247 5.02238,
          -3.70926 -2.06858 4.09486,
          -3.70926 -2.23928 3.23670,
          -3.70926 -2.06858 2.37853,
          -3.70926 -1.58247 1.45101,
          -3.70926 -0.854952 1.16490,
          -3.70926 3.21527e-3 0.994197,
          -3.70926 0.861383 1.16490,
          -3.70926 1.58890 1.45101,
          -3.70926 2.07502 2.37853,
          -2.70926 1.95322 3.23670,
          -2.70926 1.80478 3.98293,
          -2.70926 1.38207 4.61555,
          -2.70926 0.749448 5.03826,
          -2.70926 3.21527e-3 5.18670,
          -2.70926 -0.743017 5.03826,
          -2.70926 -1.37564 4.61555,
          -2.70926 -1.79835 3.98293,
          -2.70926 -1.94678 3.23670,
          -2.70926 -1.79835 2.49046,
          -2.70926 -1.37564 1.85784,
          -2.70926 -0.743017 1.43513,
          -2.70926 3.21527e-3 1.28670,
          -2.70926 0.749448 1.43513,
          -2.70926 1.38207 1.85784,
          -2.70926 1.80478 2.49046,
          -3.70926 1.57087 3.23670,
          -3.70926 1.45154 3.83661,
          -3.70926 1.11171 4.34519,
          -3.70926 0.603130 4.68502,
          -3.70926 3.21527e-3 4.80435,
          -3.70926 -0.596700 4.68502,
          -3.70926 -1.10528 4.34519,
          -3.70926 -1.44511 3.83661,
          -3.70926 -1.56444 3.23670,
          -3.70926 -1.44511 2.63678,
          -3.70926 -1.10528 2.12820,
          -3.70926 -0.596700 1.78837,
          -3.70926 3.21527e-3 1.66904,
          -3.70926 0.603130 1.78837,
          -3.70926 1.11171 2.12820,
          -3.70926 1.45154 2.63678,
          -2.58426 1.57087 3.23670,
          -2.58426 1.45154 3.83661,
          -2.58426 1.11171 4.34519,
          -2.58426 0.603130 4.68502,
          -2.58426 3.21527e-3 4.80435,
          -2.58426 -0.596700 4.68502,
          -2.58426 -1.10528 4.34519,
          -2.58426 -1.44511 3.83661,
          -2.58426 -1.56444 3.23670,
          -2.58426 -1.44511 2.63678,
          -2.58426 -1.10528 2.12820,
          -2.58426 -0.596700 1.78837,
          -2.58426 3.21527e-3 1.66904,
          -2.58426 0.603130 1.78837,
          -2.58426 1.11171 2.12820,
          -2.58426 1.45154 2.63678 ] }
        coordIndex [
          0, 1, 17, 16, -1,
          0, 15, 47, 32, -1,
          0, 16, 31, 15, -1,
          0, 32, 33, 1, -1,
          1, 2, 18, 17, -1,
          1, 33, 34, 2, -1,
          2, 3, 19, 18, -1,
          2, 34, 35, 3, -1,
          3, 4, 20, 19, -1,
          3, 35, 36, 4, -1,
          4, 5, 21, 20, -1,
          4, 36, 37, 5, -1,
          5, 6, 22, 21, -1,
          5, 37, 38, 6, -1,
          6, 7, 23, 22, -1,
          6, 38, 39, 7, -1,
          7, 8, 24, 23, -1,
          7, 39, 40, 8, -1,
          8, 9, 25, 24, -1,
          8, 40, 41, 9, -1,
          9, 10, 26, 25, -1,
          9, 41, 42, 10, -1,
          10, 11, 27, 26, -1,
          10, 42, 43, 11, -1,
          11, 12, 28, 27, -1,
          11, 43, 44, 12, -1,
          12, 13, 29, 28, -1,
          12, 44, 45, 13, -1,
          13, 14, 30, 29, -1,
          13, 45, 46, 14, -1,
          14, 15, 31, 30, -1,
          14, 46, 47, 15, -1,
          16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, -1,
          32, 47, 63, 48, -1,
          32, 48, 49, 33, -1,
          33, 49, 50, 34, -1,
          34, 50, 51, 35, -1,
          35, 51, 52, 36, -1,
          36, 52, 53, 37, -1,
          37, 53, 54, 38, -1,
          38, 54, 55, 39, -1,
          39, 55, 56, 40, -1,
          40, 56, 57, 41, -1,
          41, 57, 58, 42, -1,
          42, 58, 59, 43, -1,
          43, 59, 60, 44, -1,
          44, 60, 61, 45, -1,
          45, 61, 62, 46, -1,
          46, 62, 63, 47, -1,
          48, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, -1 ]
        normal Normal { vector [
          -0.609206 0.793012 4.66939e-17,
          -0.598099 0.749843 0.282866,
          -0.592292 0.557159 0.582035,
          -0.590320 0.266540 0.761892,
          -0.609206 -4.66939e-17 0.793012,
          -0.590320 -0.266540 0.761892,
          -0.592292 -0.557159 0.582035,
          -0.598099 -0.749843 0.282866,
          -0.609206 -0.793012 3.73551e-16,
          -0.598099 -0.749843 -0.282866,
          -0.592292 -0.557159 -0.582035,
          -0.590320 -0.266540 -0.761892,
          -0.609206 -9.33877e-17 -0.793012,
          -0.590320 0.266540 -0.761892,
          -0.592292 0.557159 -0.582035,
          -0.598099 0.749843 -0.282866,
          0.635399 0.772184 4.54675e-17,
          0.646330 0.713947 0.269325,
          0.677898 0.508362 0.531058,
          0.654393 0.249694 0.713739,
          0.635399 -3.41006e-17 0.772184,
          0.654393 -0.249694 0.713739,
          0.677898 -0.508362 0.531058,
          0.646330 -0.713947 0.269325,
          0.635399 -0.772184 3.63740e-16,
          0.646330 -0.713947 -0.269325,
          0.677898 -0.508362 -0.531058,
          0.654393 -0.249694 -0.713739,
          0.635399 -7.95681e-17 -0.772184,
          0.654393 0.249694 -0.713739,
          0.677898 0.508362 -0.531058,
          0.646330 0.713947 -0.269325,
          -0.713933 -0.700215 2.57603e-16,
          -0.713933 -0.646914 -0.267960,
          -0.713933 -0.495126 -0.495126,
          -0.713933 -0.267960 -0.646914,
          -0.713933 -3.46773e-16 -0.700215,
          -0.713933 0.267960 -0.646914,
          -0.713933 0.495126 -0.495126,
          -0.713933 0.646914 -0.267960,
          -0.713933 0.700215 -1.08986e-16,
          -0.713933 0.646914 0.267960,
          -0.713933 0.495126 0.495126,
          -0.713933 0.267960 0.646914,
          -0.713933 1.48617e-16 0.700215,
          -0.713933 -0.267960 0.646914,
          -0.713933 -0.495126 0.495126,
          -0.713933 -0.646914 0.267960,
          -0.454182 -0.890909 3.78182e-16,
          -0.454182 -0.823093 -0.340936,
          -0.454182 -0.629968 -0.629968,
          -0.454182 -0.340936 -0.823093,
          -0.454182 -5.29455e-16 -0.890909,
          -0.454182 0.340936 -0.823093,
          -0.454182 0.629968 -0.629968,
          -0.454182 0.823093 -0.340936,
          -0.454182 0.890909 -8.82425e-17,
          -0.454182 0.823093 0.340936,
          -0.454182 0.629968 0.629968,
          -0.454182 0.340936 0.823093,
          -0.454182 1.00849e-16 0.890909,
          -0.454182 -0.340936 0.823093,
          -0.454182 -0.629968 0.629968,
          -0.454182 -0.823093 0.340936 ] }
        normalIndex [
          0, 1, 17, 16, -1,
          0, 15, 47, 32, -1,
          0, 16, 31, 15, -1,
          0, 32, 33, 1, -1,
          1, 2, 18, 17, -1,
          1, 33, 34, 2, -1,
          2, 3, 19, 18, -1,
          2, 34, 35, 3, -1,
          3, 4, 20, 19, -1,
          3, 35, 36, 4, -1,
          4, 5, 21, 20, -1,
          4, 36, 37, 5, -1,
          5, 6, 22, 21, -1,
          5, 37, 38, 6, -1,
          6, 7, 23, 22, -1,
          6, 38, 39, 7, -1,
          7, 8, 24, 23, -1,
          7, 39, 40, 8, -1,
          8, 9, 25, 24, -1,
          8, 40, 41, 9, -1,
          9, 10, 26, 25, -1,
          9, 41, 42, 10, -1,
          10, 11, 27, 26, -1,
          10, 42, 43, 11, -1,
          11, 12, 28, 27, -1,
          11, 43, 44, 12, -1,
          12, 13, 29, 28, -1,
          12, 44, 45, 13, -1,
          13, 14, 30, 29, -1,
          13, 45, 46, 14, -1,
          14, 15, 31, 30, -1,
          14, 46, 47, 15, -1,
          16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, -1,
          32, 47, 63, 48, -1,
          32, 48, 49, 33, -1,
          33, 49, 50, 34, -1,
          34, 50, 51, 35, -1,
          35, 51, 52, 36, -1,
          36, 52, 53, 37, -1,
          37, 53, 54, 38, -1,
          38, 54, 55, 39, -1,
          39, 55, 56, 40, -1,
          40, 56, 57, 41, -1,
          41, 57, 58, 42, -1,
          42, 58, 59, 43, -1,
          43, 59, 60, 44, -1,
          44, 60, 61, 45, -1,
          45, 61, 62, 46, -1,
          46, 62, 63, 47, -1,
          48, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, -1 ]
      }
    }
  ]
}

DEF cube1_sep11 Transform {
  children [
    Shape {
      appearance Appearance {
        material USE couleur_220_223_223
      }
      geometry IndexedFaceSet {
        normalPerVertex TRUE
        coord Coordinate { point [
          1.87798 -2.37178 1.91170,
          0.627981 -2.37178 1.91170,
          1.87798 -2.37178 -1.08830,
          1.87798 -2.62178 -1.08830,
          0.627981 -2.62178 -1.08830,
          0.627981 -2.37178 -1.08830,
          1.87798 -2.50178 1.91170,
          1.87798 -2.62178 1.79170,
          0.627981 -2.50178 1.91170,
          0.627981 -2.62178 1.79170 ] }
        coordIndex [
          0, 1, 8, 6, -1,
          0, 2, 5, 1, -1,
          0, 6, 7, 3, 2, -1,
          1, 5, 4, 9, 8, -1,
          2, 3, 4, 5, -1,
          3, 7, 9, 4, -1,
          6, 8, 9, 7, -1 ]
        normal Normal { vector [
          0.577350 0.577350 0.577350,
          -0.577350 0.577350 0.577350,
          0.577350 0.577350 -0.577350,
          0.577350 -0.577350 -0.577350,
          -0.577350 -0.577350 -0.577350,
          -0.577350 0.577350 -0.577350,
          0.475963 -0.336557 0.812520,
          0.475963 -0.812520 0.336557,
          -0.475963 -0.336557 0.812520,
          -0.475963 -0.812520 0.336557 ] }
        normalIndex [
          0, 1, 8, 6, -1,
          0, 2, 5, 1, -1,
          0, 6, 7, 3, 2, -1,
          1, 5, 4, 9, 8, -1,
          2, 3, 4, 5, -1,
          3, 7, 9, 4, -1,
          6, 8, 9, 7, -1 ]
      }
    }
  ]
}

DEF cube1_sep10 Transform {
  children [
    Shape {
      appearance Appearance {
        material USE couleur_220_223_223
      }
      geometry IndexedFaceSet {
        normalPerVertex TRUE
        coord Coordinate { point [
          4.07353e-2 -0.621785 0.261697,
          -5.92647e-2 -0.621785 0.261697,
          -5.92647e-2 0.628215 0.261697,
          4.07353e-2 0.628215 0.261697,
          4.07353e-2 -0.621785 -1.08830,
          -5.92647e-2 -0.621785 -1.08830,
          -5.92647e-2 0.628215 -1.08830,
          4.07353e-2 0.628215 -1.08830 ] }
        coordIndex [
          0, 1, 5, 4, -1,
          0, 3, 2, 1, -1,
          0, 4, 7, 3, -1,
          1, 2, 6, 5, -1,
          2, 3, 7, 6, -1,
          4, 5, 6, 7, -1 ]
        normal Normal { vector [
          0.577350 -0.577350 0.577350,
          -0.577350 -0.577350 0.577350,
          -0.577350 0.577350 0.577350,
          0.577350 0.577350 0.577350,
          0.577350 -0.577350 -0.577350,
          -0.577350 -0.577350 -0.577350,
          -0.577350 0.577350 -0.577350,
          0.577350 0.577350 -0.577350 ] }
        normalIndex [
          0, 1, 5, 4, -1,
          0, 3, 2, 1, -1,
          0, 4, 7, 3, -1,
          1, 2, 6, 5, -1,
          2, 3, 7, 6, -1,
          4, 5, 6, 7, -1 ]
      }
    }
  ]
}

DEF cube1_sep9 Transform {
  children [
    Shape {
      appearance Appearance {
        material USE couleur_220_223_223
      }
      geometry IndexedFaceSet {
        normalPerVertex TRUE
        coord Coordinate { point [
          -5.92647e-2 0.628215 -1.08830,
          -5.92647e-2 -0.621785 -1.08830,
          -5.92647e-2 -0.574209 -1.32748,
          -5.92647e-2 -0.438726 -1.53025,
          -5.92647e-2 -0.235962 -1.66573,
          -5.92647e-2 3.21527e-3 -1.71330,
          -5.92647e-2 0.242392 -1.66573,
          -5.92647e-2 0.445157 -1.53025,
          -5.92647e-2 0.580640 -1.32748,
          4.07353e-2 0.628215 -1.08830,
          4.07353e-2 -0.621785 -1.08830,
          4.07353e-2 -0.574209 -1.32748,
          4.07353e-2 -0.438726 -1.53025,
          4.07353e-2 -0.235962 -1.66573,
          4.07353e-2 3.21527e-3 -1.71330,
          4.07353e-2 0.242392 -1.66573,
          4.07353e-2 0.445157 -1.53025,
          4.07353e-2 0.580640 -1.32748 ] }
        coordIndex [
          0, 1, 10, 9, -1,
          0, 8, 7, 6, 5, 4, 3, 2, 1, -1,
          0, 9, 17, 8, -1,
          1, 2, 11, 10, -1,
          2, 3, 12, 11, -1,
          3, 4, 13, 12, -1,
          4, 5, 14, 13, -1,
          5, 6, 15, 14, -1,
          6, 7, 16, 15, -1,
          7, 8, 17, 16, -1,
          9, 10, 11, 12, 13, 14, 15, 16, 17, -1 ]
        normal Normal { vector [
          -0.619006 0.607112 0.498244,
          -0.619006 -0.607112 0.498244,
          -0.454182 -0.823093 -0.340936,
          -0.454182 -0.629968 -0.629968,
          -0.454182 -0.340936 -0.823093,
          -0.454182 0.00000e+0 -0.890909,
          -0.454182 0.340936 -0.823093,
          -0.454182 0.629968 -0.629968,
          -0.454182 0.823093 -0.340936,
          0.619006 0.607112 0.498244,
          0.619006 -0.607112 0.498244,
          0.454182 -0.823093 -0.340936,
          0.454182 -0.629968 -0.629968,
          0.454182 -0.340936 -0.823093,
          0.454182 0.00000e+0 -0.890909,
          0.454182 0.340936 -0.823093,
          0.454182 0.629968 -0.629968,
          0.454182 0.823093 -0.340936 ] }
        normalIndex [
          0, 1, 10, 9, -1,
          0, 8, 7, 6, 5, 4, 3, 2, 1, -1,
          0, 9, 17, 8, -1,
          1, 2, 11, 10, -1,
          2, 3, 12, 11, -1,
          3, 4, 13, 12, -1,
          4, 5, 14, 13, -1,
          5, 6, 15, 14, -1,
          6, 7, 16, 15, -1,
          7, 8, 17, 16, -1,
          9, 10, 11, 12, 13, 14, 15, 16, 17, -1 ]
      }
    }
  ]
}

DEF cube1_sep8 Transform {
  children [
    Shape {
      appearance Appearance {
        material USE couleur_220_223_223
      }
      geometry IndexedFaceSet {
        normalPerVertex TRUE
        coord Coordinate { point [
          2.96574 1.05622 3.23670,
          2.96574 0.976060 3.63966,
          2.96574 0.747799 3.98128,
          2.96574 0.406181 4.20954,
          2.96574 3.21527e-3 4.28970,
          2.96574 -0.399750 4.20954,
          2.96574 -0.741368 3.98128,
          2.96574 -0.969630 3.63966,
          2.96574 -1.04978 3.23670,
          2.96574 -0.969630 2.83373,
          2.96574 -0.741368 2.49211,
          2.96574 -0.399750 2.26385,
          2.96574 3.21527e-3 2.18370,
          2.96574 0.406181 2.26385,
          2.96574 0.747799 2.49211,
          2.96574 0.976060 2.83373,
          3.21574 1.05622 3.23670,
          3.21574 0.976060 3.63966,
          3.21574 0.747799 3.98128,
          3.21574 0.406181 4.20954,
          3.21574 3.21527e-3 4.28970,
          3.21574 -0.399750 4.20954,
          3.21574 -0.741368 3.98128,
          3.21574 -0.969630 3.63966,
          3.21574 -1.04978 3.23670,
          3.21574 -0.969630 2.83373,
          3.21574 -0.741368 2.49211,
          3.21574 -0.399750 2.26385,
          3.21574 3.21527e-3 2.18370,
          3.21574 0.406181 2.26385,
          3.21574 0.747799 2.49211,
          3.21574 0.976060 2.83373,
          3.29371 0.721819 3.23670,
          3.29371 0.667118 3.51169,
          3.29371 0.511345 3.74483,
          3.29371 0.278213 3.90060,
          3.29371 3.21527e-3 3.95530,
          3.29371 -0.271782 3.90060,
          3.29371 -0.504914 3.74483,
          3.29371 -0.660688 3.51169,
          3.29371 -0.715388 3.23670,
          3.29371 -0.660688 2.96170,
          3.29371 -0.504914 2.72857,
          3.29371 -0.271782 2.57279,
          3.29371 3.21527e-3 2.51809,
          3.29371 0.278213 2.57279,
          3.29371 0.511345 2.72857,
          3.29371 0.667118 2.96170,
          3.21574 0.721819 3.23670,
          3.21574 0.667118 3.51169,
          3.21574 0.511345 3.74483,
          3.21574 0.278213 3.90060,
          3.21574 3.21527e-3 3.95530,
          3.21574 -0.271782 3.90060,
          3.21574 -0.504914 3.74483,
          3.21574 -0.660688 3.51169,
          3.21574 -0.715388 3.23670,
          3.21574 -0.660688 2.96170,
          3.21574 -0.504914 2.72857,
          3.21574 -0.271782 2.57279,
          3.21574 3.21527e-3 2.51809,
          3.21574 0.278213 2.57279,
          3.21574 0.511345 2.72857,
          3.21574 0.667118 2.96170,
          3.20527 0.811989 3.23670,
          3.20527 0.750425 3.54620,
          3.20527 0.575105 3.80859,
          3.20527 0.312720 3.98391,
          3.20527 3.21527e-3 4.04547,
          3.20527 -0.306289 3.98391,
          3.20527 -0.568674 3.80859,
          3.20527 -0.743994 3.54620,
          3.20527 -0.805559 3.23670,
          3.20527 -0.743994 2.92719,
          3.20527 -0.568674 2.66481,
          3.20527 -0.306289 2.48949,
          3.20527 3.21527e-3 2.42792,
          3.20527 0.312720 2.48949,
          3.20527 0.575105 2.66481,
          3.20527 0.750425 2.92719,
          2.97620 0.811989 3.23670,
          2.97620 0.750425 3.54620,
          2.97620 0.575105 3.80859,
          2.97620 0.312720 3.98391,
          2.97620 3.21527e-3 4.04547,
          2.97620 -0.306289 3.98391,
          2.97620 -0.568674 3.80859,
          2.97620 -0.743994 3.54620,
          2.97620 -0.805559 3.23670,
          2.97620 -0.743994 2.92719,
          2.97620 -0.568674 2.66481,
          2.97620 -0.306289 2.48949,
          2.97620 3.21527e-3 2.42792,
          2.97620 0.312720 2.48949,
          2.97620 0.575105 2.66481,
          2.97620 0.750425 2.92719,
          3.29371 0.477593 3.23670,
          3.29371 0.441483 3.41823,
          3.29371 0.338651 3.57213,
          3.29371 0.184752 3.67496,
          3.29371 3.21527e-3 3.71107,
          3.29371 -0.178321 3.67496,
          3.29371 -0.332220 3.57213,
          3.29371 -0.435052 3.41823,
          3.29371 -0.471162 3.23670,
          3.29371 -0.435052 3.05516,
          3.29371 -0.332220 2.90126,
          3.29371 -0.178321 2.79843,
          3.29371 3.21527e-3 2.76232,
          3.29371 0.184752 2.79843,
          3.29371 0.338651 2.90126,
          3.29371 0.441483 3.05516,
          2.97620 0.477593 3.23670,
          2.97620 0.441483 3.41823,
          2.97620 0.338651 3.57213,
          2.97620 0.184752 3.67496,
          2.97620 3.21527e-3 3.71107,
          2.97620 -0.178321 3.67496,
          2.97620 -0.332220 3.57213,
          2.97620 -0.435052 3.41823,
          2.97620 -0.471162 3.23670,
          2.97620 -0.435052 3.05516,
          2.97620 -0.332220 2.90126,
          2.97620 -0.178321 2.79843,
          2.97620 3.21527e-3 2.76232,
          2.97620 0.184752 2.79843,
          2.97620 0.338651 2.90126,
          2.97620 0.441483 3.05516 ] }
        coordIndex [
          0, 1, 17, 16, -1,
          0, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, -1,
          0, 16, 31, 15, -1,
          1, 2, 18, 17, -1,
          2, 3, 19, 18, -1,
          3, 4, 20, 19, -1,
          4, 5, 21, 20, -1,
          5, 6, 22, 21, -1,
          6, 7, 23, 22, -1,
          7, 8, 24, 23, -1,
          8, 9, 25, 24, -1,
          9, 10, 26, 25, -1,
          10, 11, 27, 26, -1,
          11, 12, 28, 27, -1,
          12, 13, 29, 28, -1,
          13, 14, 30, 29, -1,
          14, 15, 31, 30, -1,
          16, 17, 49, 48, -1,
          16, 48, 63, 31, -1,
          17, 18, 50, 49, -1,
          18, 19, 51, 50, -1,
          19, 20, 52, 51, -1,
          20, 21, 53, 52, -1,
          21, 22, 54, 53, -1,
          22, 23, 55, 54, -1,
          23, 24, 56, 55, -1,
          24, 25, 57, 56, -1,
          25, 26, 58, 57, -1,
          26, 27, 59, 58, -1,
          27, 28, 60, 59, -1,
          28, 29, 61, 60, -1,
          29, 30, 62, 61, -1,
          30, 31, 63, 62, -1,
          32, 33, 97, 96, -1,
          32, 47, 63, 48, -1,
          32, 48, 49, 33, -1,
          32, 96, 111, 47, -1,
          33, 34, 98, 97, -1,
          33, 49, 50, 34, -1,
          34, 35, 99, 98, -1,
          34, 50, 51, 35, -1,
          35, 36, 100, 99, -1,
          35, 51, 52, 36, -1,
          36, 37, 101, 100, -1,
          36, 52, 53, 37, -1,
          37, 38, 102, 101, -1,
          37, 53, 54, 38, -1,
          38, 39, 103, 102, -1,
          38, 54, 55, 39, -1,
          39, 40, 104, 103, -1,
          39, 55, 56, 40, -1,
          40, 41, 105, 104, -1,
          40, 56, 57, 41, -1,
          41, 42, 106, 105, -1,
          41, 57, 58, 42, -1,
          42, 43, 107, 106, -1,
          42, 58, 59, 43, -1,
          43, 44, 108, 107, -1,
          43, 59, 60, 44, -1,
          44, 45, 109, 108, -1,
          44, 60, 61, 45, -1,
          45, 46, 110, 109, -1,
          45, 61, 62, 46, -1,
          46, 47, 111, 110, -1,
          46, 62, 63, 47, -1,
          64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, -1,
          64, 79, 95, 80, -1,
          64, 80, 81, 65, -1,
          65, 81, 82, 66, -1,
          66, 82, 83, 67, -1,
          67, 83, 84, 68, -1,
          68, 84, 85, 69, -1,
          69, 85, 86, 70, -1,
          70, 86, 87, 71, -1,
          71, 87, 88, 72, -1,
          72, 88, 89, 73, -1,
          73, 89, 90, 74, -1,
          74, 90, 91, 75, -1,
          75, 91, 92, 76, -1,
          76, 92, 93, 77, -1,
          77, 93, 94, 78, -1,
          78, 94, 95, 79, -1,
          80, 95, 127, 112, -1,
          80, 112, 113, 81, -1,
          81, 113, 114, 82, -1,
          82, 114, 115, 83, -1,
          83, 115, 116, 84, -1,
          84, 116, 117, 85, -1,
          85, 117, 118, 86, -1,
          86, 118, 119, 87, -1,
          87, 119, 120, 88, -1,
          88, 120, 121, 89, -1,
          89, 121, 122, 90, -1,
          90, 122, 123, 91, -1,
          91, 123, 124, 92, -1,
          92, 124, 125, 93, -1,
          93, 125, 126, 94, -1,
          94, 126, 127, 95, -1,
          96, 97, 113, 112, -1,
          96, 112, 127, 111, -1,
          97, 98, 114, 113, -1,
          98, 99, 115, 114, -1,
          99, 100, 116, 115, -1,
          100, 101, 117, 116, -1,
          101, 102, 118, 117, -1,
          102, 103, 119, 118, -1,
          103, 104, 120, 119, -1,
          104, 105, 121, 120, -1,
          105, 106, 122, 121, -1,
          106, 107, 123, 122, -1,
          107, 108, 124, 123, -1,
          108, 109, 125, 124, -1,
          109, 110, 126, 125, -1,
          110, 111, 127, 126, -1 ]
        normal Normal { vector [
          -0.454182 0.890909 0.00000e+0,
          -0.454182 0.823093 0.340936,
          -0.454182 0.629968 0.629968,
          -0.454182 0.340936 0.823093,
          -0.454182 -2.52121e-17 0.890909,
          -0.454182 -0.340936 0.823093,
          -0.454182 -0.629968 0.629968,
          -0.454182 -0.823093 0.340936,
          -0.454182 -0.890909 -1.38667e-16,
          -0.454182 -0.823093 -0.340936,
          -0.454182 -0.629968 -0.629968,
          -0.454182 -0.340936 -0.823093,
          -0.454182 -9.70667e-16 -0.890909,
          -0.454182 0.340936 -0.823093,
          -0.454182 0.629968 -0.629968,
          -0.454182 0.823093 -0.340936,
          0.713933 0.700215 0.00000e+0,
          0.713933 0.646914 0.267960,
          0.713933 0.495126 0.495126,
          0.713933 0.267960 0.646914,
          0.713933 1.77777e-16 0.700215,
          0.713933 -0.267960 0.646914,
          0.713933 -0.495126 0.495126,
          0.713933 -0.646914 0.267960,
          0.713933 -0.700215 -1.08986e-16,
          0.713933 -0.646914 -0.267960,
          0.713933 -0.495126 -0.495126,
          0.713933 -0.267960 -0.646914,
          0.713933 -7.43085e-16 -0.700215,
          0.713933 0.267960 -0.646914,
          0.713933 0.495126 -0.495126,
          0.713933 0.646914 -0.267960,
          0.713933 0.700215 -3.95076e-16,
          0.713933 0.646914 0.267960,
          0.713933 0.495126 0.495126,
          0.713933 0.267960 0.646914,
          0.713933 6.97452e-16 0.700215,
          0.713933 -0.267960 0.646914,
          0.713933 -0.495126 0.495126,
          0.713933 -0.646914 0.267960,
          0.713933 -0.700215 9.28861e-16,
          0.713933 -0.646914 -0.267960,
          0.713933 -0.495126 -0.495126,
          0.713933 -0.267960 -0.646914,
          0.713933 -1.85667e-15 -0.700215,
          0.713933 0.267960 -0.646914,
          0.713933 0.495126 -0.495126,
          0.713933 0.646914 -0.267960,
          0.713933 0.700215 -3.96312e-17,
          0.713933 0.646914 0.267960,
          0.713933 0.495126 0.495126,
          0.713933 0.267960 0.646914,
          0.713933 3.36302e-16 0.700215,
          0.713933 -0.267960 0.646914,
          0.713933 -0.495126 0.495126,
          0.713933 -0.646914 0.267960,
          0.713933 -0.700215 2.17972e-16,
          0.713933 -0.646914 -0.267960,
          0.713933 -0.495126 -0.495126,
          0.713933 -0.267960 -0.646914,
          0.713933 -1.31774e-15 -0.700215,
          0.713933 0.267960 -0.646914,
          0.713933 0.495126 -0.495126,
          0.713933 0.646914 -0.267960,
          0.454182 0.890909 1.51273e-16,
          0.454182 0.823093 0.340936,
          0.454182 0.629968 0.629968,
          0.454182 0.340936 0.823093,
          0.454182 -1.00849e-16 0.890909,
          0.454182 -0.340936 0.823093,
          0.454182 -0.629968 0.629968,
          0.454182 -0.823093 0.340936,
          0.454182 -0.890909 -3.02546e-16,
          0.454182 -0.823093 -0.340936,
          0.454182 -0.629968 -0.629968,
          0.454182 -0.340936 -0.823093,
          0.454182 -2.52121e-15 -0.890909,
          0.454182 0.340936 -0.823093,
          0.454182 0.629968 -0.629968,
          0.454182 0.823093 -0.340936,
          -0.713933 0.700215 1.18894e-16,
          -0.713933 0.646914 0.267960,
          -0.713933 0.495126 0.495126,
          -0.713933 0.267960 0.646914,
          -0.713933 -6.21095e-16 0.700215,
          -0.713933 -0.267960 0.646914,
          -0.713933 -0.495126 0.495126,
          -0.713933 -0.646914 0.267960,
          -0.713933 -0.700215 7.27637e-16,
          -0.713933 -0.646914 -0.267960,
          -0.713933 -0.495126 -0.495126,
          -0.713933 -0.267960 -0.646914,
          -0.713933 -1.90230e-15 -0.700215,
          -0.713933 0.267960 -0.646914,
          -0.713933 0.495126 -0.495126,
          -0.713933 0.646914 -0.267960,
          0.713933 -0.700215 -3.35629e-16,
          0.713933 -0.646914 -0.267960,
          0.713933 -0.495126 -0.495126,
          0.713933 -0.267960 -0.646914,
          0.713933 1.04423e-15 -0.700215,
          0.713933 0.267960 -0.646914,
          0.713933 0.495126 -0.495126,
          0.713933 0.646914 -0.267960,
          0.713933 0.700215 1.66204e-15,
          0.713933 0.646914 0.267960,
          0.713933 0.495126 0.495126,
          0.713933 0.267960 0.646914,
          0.713933 3.72591e-16 0.700215,
          0.713933 -0.267960 0.646914,
          0.713933 -0.495126 0.495126,
          0.713933 -0.646914 0.267960,
          -0.713933 -0.700215 1.98156e-17,
          -0.713933 -0.646914 -0.267960,
          -0.713933 -0.495126 -0.495126,
          -0.713933 -0.267960 -0.646914,
          -0.713933 -1.15797e-16 -0.700215,
          -0.713933 0.267960 -0.646914,
          -0.713933 0.495126 -0.495126,
          -0.713933 0.646914 -0.267960,
          -0.713933 0.700215 1.91657e-15,
          -0.713933 0.646914 0.267960,
          -0.713933 0.495126 0.495126,
          -0.713933 0.267960 0.646914,
          -0.713933 9.11518e-16 0.700215,
          -0.713933 -0.267960 0.646914,
          -0.713933 -0.495126 0.495126,
          -0.713933 -0.646914 0.267960 ] }
        normalIndex [
          0, 1, 17, 16, -1,
          0, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, -1,
          0, 16, 31, 15, -1,
          1, 2, 18, 17, -1,
          2, 3, 19, 18, -1,
          3, 4, 20, 19, -1,
          4, 5, 21, 20, -1,
          5, 6, 22, 21, -1,
          6, 7, 23, 22, -1,
          7, 8, 24, 23, -1,
          8, 9, 25, 24, -1,
          9, 10, 26, 25, -1,
          10, 11, 27, 26, -1,
          11, 12, 28, 27, -1,
          12, 13, 29, 28, -1,
          13, 14, 30, 29, -1,
          14, 15, 31, 30, -1,
          16, 17, 49, 48, -1,
          16, 48, 63, 31, -1,
          17, 18, 50, 49, -1,
          18, 19, 51, 50, -1,
          19, 20, 52, 51, -1,
          20, 21, 53, 52, -1,
          21, 22, 54, 53, -1,
          22, 23, 55, 54, -1,
          23, 24, 56, 55, -1,
          24, 25, 57, 56, -1,
          25, 26, 58, 57, -1,
          26, 27, 59, 58, -1,
          27, 28, 60, 59, -1,
          28, 29, 61, 60, -1,
          29, 30, 62, 61, -1,
          30, 31, 63, 62, -1,
          32, 33, 97, 96, -1,
          32, 47, 63, 48, -1,
          32, 48, 49, 33, -1,
          32, 96, 111, 47, -1,
          33, 34, 98, 97, -1,
          33, 49, 50, 34, -1,
          34, 35, 99, 98, -1,
          34, 50, 51, 35, -1,
          35, 36, 100, 99, -1,
          35, 51, 52, 36, -1,
          36, 37, 101, 100, -1,
          36, 52, 53, 37, -1,
          37, 38, 102, 101, -1,
          37, 53, 54, 38, -1,
          38, 39, 103, 102, -1,
          38, 54, 55, 39, -1,
          39, 40, 104, 103, -1,
          39, 55, 56, 40, -1,
          40, 41, 105, 104, -1,
          40, 56, 57, 41, -1,
          41, 42, 106, 105, -1,
          41, 57, 58, 42, -1,
          42, 43, 107, 106, -1,
          42, 58, 59, 43, -1,
          43, 44, 108, 107, -1,
          43, 59, 60, 44, -1,
          44, 45, 109, 108, -1,
          44, 60, 61, 45, -1,
          45, 46, 110, 109, -1,
          45, 61, 62, 46, -1,
          46, 47, 111, 110, -1,
          46, 62, 63, 47, -1,
          64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, -1,
          64, 79, 95, 80, -1,
          64, 80, 81, 65, -1,
          65, 81, 82, 66, -1,
          66, 82, 83, 67, -1,
          67, 83, 84, 68, -1,
          68, 84, 85, 69, -1,
          69, 85, 86, 70, -1,
          70, 86, 87, 71, -1,
          71, 87, 88, 72, -1,
          72, 88, 89, 73, -1,
          73, 89, 90, 74, -1,
          74, 90, 91, 75, -1,
          75, 91, 92, 76, -1,
          76, 92, 93, 77, -1,
          77, 93, 94, 78, -1,
          78, 94, 95, 79, -1,
          80, 95, 127, 112, -1,
          80, 112, 113, 81, -1,
          81, 113, 114, 82, -1,
          82, 114, 115, 83, -1,
          83, 115, 116, 84, -1,
          84, 116, 117, 85, -1,
          85, 117, 118, 86, -1,
          86, 118, 119, 87, -1,
          87, 119, 120, 88, -1,
          88, 120, 121, 89, -1,
          89, 121, 122, 90, -1,
          90, 122, 123, 91, -1,
          91, 123, 124, 92, -1,
          92, 124, 125, 93, -1,
          93, 125, 126, 94, -1,
          94, 126, 127, 95, -1,
          96, 97, 113, 112, -1,
          96, 112, 127, 111, -1,
          97, 98, 114, 113, -1,
          98, 99, 115, 114, -1,
          99, 100, 116, 115, -1,
          100, 101, 117, 116, -1,
          101, 102, 118, 117, -1,
          102, 103, 119, 118, -1,
          103, 104, 120, 119, -1,
          104, 105, 121, 120, -1,
          105, 106, 122, 121, -1,
          106, 107, 123, 122, -1,
          107, 108, 124, 123, -1,
          108, 109, 125, 124, -1,
          109, 110, 126, 125, -1,
          110, 111, 127, 126, -1 ]
      }
    }
  ]
}

DEF cube1_sep7 Transform {
  children [
    Shape {
      appearance Appearance {
        material USE couleur_220_223_223
      }
      geometry IndexedFaceSet {
        normalPerVertex TRUE
        coord Coordinate { point [
          2.96574 0.703215 -1.01330,
          2.96574 -0.696785 -1.01330,
          2.96574 -0.643500 -1.28118,
          2.96574 -0.491759 -1.50828,
          2.96574 -0.264663 -1.66002,
          2.96574 3.21527e-3 -1.71330,
          2.96574 0.271094 -1.66002,
          2.96574 0.498190 -1.50828,
          2.96574 0.649931 -1.28118,
          3.21574 0.703215 -1.01330,
          3.21574 -0.696785 -1.01330,
          3.21574 -0.643500 -1.28118,
          3.21574 -0.491759 -1.50828,
          3.21574 -0.264663 -1.66002,
          3.21574 3.21527e-3 -1.71330,
          3.21574 0.271094 -1.66002,
          3.21574 0.498190 -1.50828,
          3.21574 0.649931 -1.28118 ] }
        coordIndex [
          0, 1, 10, 9, -1,
          0, 8, 7, 6, 5, 4, 3, 2, 1, -1,
          0, 9, 17, 8, -1,
          1, 2, 11, 10, -1,
          2, 3, 12, 11, -1,
          3, 4, 13, 12, -1,
          4, 5, 14, 13, -1,
          5, 6, 15, 14, -1,
          6, 7, 16, 15, -1,
          7, 8, 17, 16, -1,
          9, 10, 11, 12, 13, 14, 15, 16, 17, -1 ]
        normal Normal { vector [
          -0.619006 0.607112 0.498244,
          -0.619006 -0.607112 0.498244,
          -0.454182 -0.823093 -0.340936,
          -0.454182 -0.629968 -0.629968,
          -0.454182 -0.340936 -0.823093,
          -0.454182 0.00000e+0 -0.890909,
          -0.454182 0.340936 -0.823093,
          -0.454182 0.629968 -0.629968,
          -0.454182 0.823093 -0.340936,
          0.619006 0.607112 0.498244,
          0.619006 -0.607112 0.498244,
          0.454182 -0.823093 -0.340936,
          0.454182 -0.629968 -0.629968,
          0.454182 -0.340936 -0.823093,
          0.454182 -1.34454e-16 -0.890909,
          0.454182 0.340936 -0.823093,
          0.454182 0.629968 -0.629968,
          0.454182 0.823093 -0.340936 ] }
        normalIndex [
          0, 1, 10, 9, -1,
          0, 8, 7, 6, 5, 4, 3, 2, 1, -1,
          0, 9, 17, 8, -1,
          1, 2, 11, 10, -1,
          2, 3, 12, 11, -1,
          3, 4, 13, 12, -1,
          4, 5, 14, 13, -1,
          5, 6, 15, 14, -1,
          6, 7, 16, 15, -1,
          7, 8, 17, 16, -1,
          9, 10, 11, 12, 13, 14, 15, 16, 17, -1 ]
      }
    }
  ]
}

DEF cube1_sep6 Transform {
  children [
    Shape {
      appearance Appearance {
        material USE couleur_220_223_223
      }
      geometry IndexedFaceSet {
        normalPerVertex TRUE
        coord Coordinate { point [
          3.21574 -0.696785 2.53670,
          2.96574 -0.696785 2.53670,
          2.96574 0.703215 2.53670,
          3.21574 0.703215 2.53670,
          3.21574 -0.696785 -1.01330,
          2.96574 -0.696785 -1.01330,
          2.96574 0.703215 -1.01330,
          3.21574 0.703215 -1.01330 ] }
        coordIndex [
          0, 1, 5, 4, -1,
          0, 3, 2, 1, -1,
          0, 4, 7, 3, -1,
          1, 2, 6, 5, -1,
          2, 3, 7, 6, -1,
          4, 5, 6, 7, -1 ]
        normal Normal { vector [
          0.577350 -0.577350 0.577350,
          -0.577350 -0.577350 0.577350,
          -0.577350 0.577350 0.577350,
          0.577350 0.577350 0.577350,
          0.577350 -0.577350 -0.577350,
          -0.577350 -0.577350 -0.577350,
          -0.577350 0.577350 -0.577350,
          0.577350 0.577350 -0.577350 ] }
        normalIndex [
          0, 1, 5, 4, -1,
          0, 3, 2, 1, -1,
          0, 4, 7, 3, -1,
          1, 2, 6, 5, -1,
          2, 3, 7, 6, -1,
          4, 5, 6, 7, -1 ]
      }
    }
  ]
}

DEF cube1 Transform {
  children [
    Shape {
      appearance Appearance {
        material DEF couleur_215_215_215 Material {
          diffuseColor 0.840000 0.840000 0.840000
          emissiveColor 0.00000e+0 0.00000e+0 0.00000e+0
          specularColor 1.00000 1.00000 1.00000
          ambientIntensity 1.00000
          transparency 0.00000e+0
          shininess 1.00000
        }
      }
      geometry IndexedFaceSet {
        normalPerVertex TRUE
        coord Coordinate { point [
          -2.40926 0.503215 3.23670,
          -2.40926 0.465155 3.42804,
          -2.40926 0.356769 3.59025,
          -2.40926 0.194557 3.69864,
          -2.40926 3.21527e-3 3.73670,
          -2.40926 -0.188126 3.69864,
          -2.40926 -0.350338 3.59025,
          -2.40926 -0.458724 3.42804,
          -2.40926 -0.496785 3.23670,
          -2.40926 -0.458724 3.04535,
          -2.40926 -0.350338 2.88314,
          -2.40926 -0.188126 2.77476,
          -2.40926 3.21527e-3 2.73670,
          -2.40926 0.194557 2.77476,
          -2.40926 0.356769 2.88314,
          -2.40926 0.465155 3.04535,
          -3.24492 0.503215 3.23670,
          -3.24492 0.465155 3.42804,
          -3.24492 0.356769 3.59025,
          -3.24492 0.194557 3.69864,
          -3.24492 3.21527e-3 3.73670,
          -3.24492 -0.188126 3.69864,
          -3.24492 -0.350338 3.59025,
          -3.24492 -0.458724 3.42804,
          -3.24492 -0.496785 3.23670,
          -3.24492 -0.458724 3.04535,
          -3.24492 -0.350338 2.88314,
          -3.24492 -0.188126 2.77476,
          -3.24492 3.21527e-3 2.73670,
          -3.24492 0.194557 2.77476,
          -3.24492 0.356769 2.88314,
          -3.24492 0.465155 3.04535,
          -3.40926 0.310349 3.36392,
          -3.40926 0.335654 3.23670,
          -3.40926 0.310349 3.10948,
          -3.40926 0.238285 3.00163,
          -3.40926 0.130434 2.92956,
          -3.40926 3.21527e-3 2.90426,
          -3.40926 -0.124004 2.92956,
          -3.40926 -0.231854 3.00163,
          -3.40926 -0.303918 3.10948,
          -3.40926 -0.329223 3.23670,
          -3.40926 -0.303918 3.36392,
          -3.40926 -0.231854 3.47177,
          -3.40926 -0.124004 3.54383,
          -3.40926 3.21527e-3 3.56914,
          -3.40926 0.130434 3.54383,
          -3.40926 0.238285 3.47177 ] }
        coordIndex [
          0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, -1,
          0, 15, 31, 16, -1,
          0, 16, 17, 1, -1,
          1, 17, 18, 2, -1,
          2, 18, 19, 3, -1,
          3, 19, 20, 4, -1,
          4, 20, 21, 5, -1,
          5, 21, 22, 6, -1,
          6, 22, 23, 7, -1,
          7, 23, 24, 8, -1,
          8, 24, 25, 9, -1,
          9, 25, 26, 10, -1,
          10, 26, 27, 11, -1,
          11, 27, 28, 12, -1,
          12, 28, 29, 13, -1,
          13, 29, 30, 14, -1,
          14, 30, 31, 15, -1,
          16, 31, 34, 33, -1,
          16, 33, 32, 17, -1,
          17, 32, 47, 18, -1,
          18, 47, 46, 19, -1,
          19, 46, 45, 20, -1,
          20, 45, 44, 21, -1,
          21, 44, 43, 22, -1,
          22, 43, 42, 23, -1,
          23, 42, 41, 24, -1,
          24, 41, 40, 25, -1,
          25, 40, 39, 26, -1,
          26, 39, 38, 27, -1,
          27, 38, 37, 28, -1,
          28, 37, 36, 29, -1,
          29, 36, 35, 30, -1,
          30, 35, 34, 31, -1,
          32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, -1 ]
        normal Normal { vector [
          0.454182 0.890909 -4.91637e-16,
          0.454182 0.823093 0.340936,
          0.454182 0.629968 0.629968,
          0.454182 0.340936 0.823093,
          0.454182 -2.14303e-16 0.890909,
          0.454182 -0.340936 0.823093,
          0.454182 -0.629968 0.629968,
          0.454182 -0.823093 0.340936,
          0.454182 -0.890909 1.26061e-17,
          0.454182 -0.823093 -0.340936,
          0.454182 -0.629968 -0.629968,
          0.454182 -0.340936 -0.823093,
          0.454182 -2.50861e-15 -0.890909,
          0.454182 0.340936 -0.823093,
          0.454182 0.629968 -0.629968,
          0.454182 0.823093 -0.340936,
          -0.389055 0.921214 -8.47558e-16,
          -0.389055 0.851091 0.352534,
          -0.389055 0.651397 0.651397,
          -0.389055 0.352534 0.851091,
          -0.389055 -9.92635e-17 0.921214,
          -0.389055 -0.352534 0.851091,
          -0.389055 -0.651397 0.651397,
          -0.389055 -0.851091 0.352534,
          -0.389055 -0.921214 2.44341e-16,
          -0.389055 -0.851091 -0.352534,
          -0.389055 -0.651397 -0.651397,
          -0.389055 -0.352534 -0.851091,
          -0.389055 -2.35178e-15 -0.921214,
          -0.389055 0.352534 -0.851091,
          -0.389055 0.651397 -0.651397,
          -0.389055 0.851091 -0.352534,
          -0.867082 0.460245 0.190640,
          -0.867082 0.498165 -7.17740e-16,
          -0.867082 0.460245 -0.190640,
          -0.867082 0.352256 -0.352256,
          -0.867082 0.190640 -0.460245,
          -0.867082 -1.24608e-15 -0.498165,
          -0.867082 -0.190640 -0.460245,
          -0.867082 -0.352256 -0.352256,
          -0.867082 -0.460245 -0.190640,
          -0.867082 -0.498165 3.09027e-16,
          -0.867082 -0.460245 0.190640,
          -0.867082 -0.352256 0.352256,
          -0.867082 -0.190640 0.460245,
          -0.867082 -1.19623e-16 0.498165,
          -0.867082 0.190640 0.460245,
          -0.867082 0.352256 0.352256 ] }
        normalIndex [
          0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, -1,
          0, 15, 31, 16, -1,
          0, 16, 17, 1, -1,
          1, 17, 18, 2, -1,
          2, 18, 19, 3, -1,
          3, 19, 20, 4, -1,
          4, 20, 21, 5, -1,
          5, 21, 22, 6, -1,
          6, 22, 23, 7, -1,
          7, 23, 24, 8, -1,
          8, 24, 25, 9, -1,
          9, 25, 26, 10, -1,
          10, 26, 27, 11, -1,
          11, 27, 28, 12, -1,
          12, 28, 29, 13, -1,
          13, 29, 30, 14, -1,
          14, 30, 31, 15, -1,
          16, 31, 34, 33, -1,
          16, 33, 32, 17, -1,
          17, 32, 47, 18, -1,
          18, 47, 46, 19, -1,
          19, 46, 45, 20, -1,
          20, 45, 44, 21, -1,
          21, 44, 43, 22, -1,
          22, 43, 42, 23, -1,
          23, 42, 41, 24, -1,
          24, 41, 40, 25, -1,
          25, 40, 39, 26, -1,
          26, 39, 38, 27, -1,
          27, 38, 37, 28, -1,
          28, 37, 36, 29, -1,
          29, 36, 35, 30, -1,
          30, 35, 34, 31, -1,
          32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, -1 ]
      }
    }
  ]
}

