# Copyright 2015 The Crashpad Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("../build/crashpad_buildconfig.gni")

crashpad_static_library("client") {
  sources = [
    "crashpad_client.h",
    "prune_crash_reports.cc",
    "prune_crash_reports.h",
    "simulate_crash.h",
  ]

  if (crashpad_is_mac) {
    sources += [
      "crashpad_client_mac.cc",
      "simulate_crash_mac.cc",
      "simulate_crash_mac.h",
    ]
  }

  if (crashpad_is_ios) {
    sources += [
      "crashpad_client_ios.cc",
      "ios_handler/exception_processor.h",
      "ios_handler/exception_processor.mm",
      "ios_handler/in_process_handler.cc",
      "ios_handler/in_process_handler.h",
      "ios_handler/in_process_intermediate_dump_handler.cc",
      "ios_handler/in_process_intermediate_dump_handler.h",
      "ios_handler/prune_intermediate_dumps_and_crash_reports_thread.cc",
      "ios_handler/prune_intermediate_dumps_and_crash_reports_thread.h",
      "simulate_crash_ios.h",
      "upload_behavior_ios.h",
    ]
  }

  if (crashpad_is_linux || crashpad_is_android) {
    sources += [
      "crashpad_client_linux.cc",
      "simulate_crash_linux.h",
    ]
  }

  if (crashpad_is_linux || crashpad_is_android || crashpad_is_fuchsia) {
    sources += [
      "client_argv_handling.cc",
      "client_argv_handling.h",
    ]
  }

  if (crashpad_is_win) {
    sources += [
      "crashpad_client_win.cc",
      "simulate_crash_win.h",
    ]
  }

  if (crashpad_is_fuchsia) {
    sources += [ "crashpad_client_fuchsia.cc" ]
  }

  public_configs = [ "..:crashpad_config" ]

  public_deps = [
    ":common",
    "$mini_chromium_source_parent:base",
    "../util",
  ]

  deps = [
    ":common",
    "$mini_chromium_source_parent:chromeos_buildflags",
  ]

  if (crashpad_is_win) {
    libs = [ "rpcrt4.lib" ]
    cflags = [ "/wd4201" ]  # nonstandard extension used : nameless struct/union
  }

  if (crashpad_is_apple) {
    deps += [ "../build:apple_enable_arc" ]
  }

  if (crashpad_is_ios) {
    deps += [
      "../handler:common",
      "../minidump",
      "../snapshot",
    ]
  }

  if (crashpad_is_linux || crashpad_is_android) {
    deps += [ "../third_party/lss" ]
  }

  if (crashpad_is_fuchsia) {
    deps += [ "../third_party/fuchsia" ]
    if (crashpad_is_in_fuchsia) {
      deps += [ "//sdk/lib/fdio" ]
    }
  }
}

static_library("common") {
  sources = [
    "annotation.cc",
    "annotation.h",
    "annotation_list.cc",
    "annotation_list.h",
    "crash_report_database.cc",
    "crash_report_database.h",
    "crashpad_info.cc",
    "crashpad_info.h",
    "length_delimited_ring_buffer.h",
    "ring_buffer_annotation.h",
    "settings.cc",
    "settings.h",
    "simple_address_range_bag.h",
    "simple_string_dictionary.h",
  ]

  if (crashpad_is_apple) {
    sources += [ "crash_report_database_mac.mm" ]
  }
  if (crashpad_is_win) {
    sources += [ "crash_report_database_win.cc" ]
  }
  if (crashpad_is_linux || crashpad_is_android || crashpad_is_fuchsia) {
    sources += [
      "crash_report_database_generic.cc",
      "crashpad_info_note.S",
    ]
  }

  public_configs = [ "..:crashpad_config" ]
  public_deps = [
    "$mini_chromium_source_parent:base",
    "../util",
  ]
  deps = [ "../util" ]
  configs += [ "../build:flock_always_supported_defines" ]

  if (crashpad_is_apple) {
    deps += [ "../build:apple_enable_arc" ]
  }
}

crashpad_executable("ring_buffer_annotation_load_test") {
  testonly = true
  sources = [ "ring_buffer_annotation_load_test_main.cc" ]
  deps = [
    ":client",
    "$mini_chromium_source_parent:base",
    "../tools:tool_support",
  ]
}

source_set("client_test") {
  testonly = true

  sources = [
    "annotation_list_test.cc",
    "annotation_test.cc",
    "crash_report_database_test.cc",
    "length_delimited_ring_buffer_test.cc",
    "prune_crash_reports_test.cc",
    "ring_buffer_annotation_test.cc",
    "settings_test.cc",
    "simple_address_range_bag_test.cc",
    "simple_string_dictionary_test.cc",
  ]

  if (crashpad_is_mac) {
    sources += [ "simulate_crash_mac_test.cc" ]
  }

  if (crashpad_is_win) {
    sources += [ "crashpad_client_win_test.cc" ]
  }

  if (crashpad_is_ios) {
    sources += [
      "crashpad_client_ios_test.mm",
      "ios_handler/exception_processor_test.mm",
      "ios_handler/in_process_handler_test.cc",
      "ios_handler/in_process_intermediate_dump_handler_test.cc",
    ]
  }

  if (crashpad_is_linux || crashpad_is_android) {
    sources += [ "crashpad_client_linux_test.cc" ]
  }

  deps = [
    ":client",
    "$mini_chromium_source_parent:base",
    "../compat",
    "../snapshot",
    "../test",
    "../third_party/googletest:googlemock",
    "../third_party/googletest:googletest",
    "../util",
  ]

  if (!crashpad_is_ios && !crashpad_is_fuchsia) {
    data_deps = [ "../handler:crashpad_handler" ]
  }

  if (crashpad_is_apple) {
    deps += [ "../build:apple_enable_arc" ]
  }

  if (crashpad_is_win) {
    data_deps += [
      "../handler:crashpad_handler_console",
      "../handler/win/wer:crashpad_wer_handler",
    ]
  }
}

if (crashpad_is_linux || crashpad_is_android) {
  source_set("pthread_create") {
    sources = [ "pthread_create_linux.cc" ]

    deps = [ ":client" ]
  }
}
