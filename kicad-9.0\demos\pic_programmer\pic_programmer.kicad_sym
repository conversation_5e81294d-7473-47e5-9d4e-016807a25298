(kicad_symbol_lib
	(version 20241209)
	(generator "kicad_symbol_editor")
	(generator_version "9.0")
	(symbol "24C16"
		(pin_names
			(offset 0.762)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 3.81 8.89 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "24C16"
			(at 5.08 -8.89 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "24C16_0_0"
			(pin power_in line
				(at 0 12.7 270)
				(length 5.08)
				(name "VCC"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at 0 -12.7 90)
				(length 5.08)
				(name "GND"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(symbol "24C16_1_1"
			(rectangle
				(start -10.16 -7.62)
				(end 10.16 7.62)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin input line
				(at -17.78 5.08 0)
				(length 7.62)
				(name "A0"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 2.54 0)
				(length 7.62)
				(name "A1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -17.78 0 0)
				(length 7.62)
				(name "A2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at 17.78 2.54 180)
				(length 7.62)
				(name "WP"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at 17.78 -2.54 180)
				(length 7.62)
				(name "SCL"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin bidirectional line
				(at 17.78 -5.08 180)
				(length 7.62)
				(name "SDA"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "74LS125"
		(pin_names
			(offset 0.254)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 5.08 3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "74LS125"
			(at 7.62 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 2.54 1.27 0)
			(effects
				(font
					(size 0.254 0.254)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "DIP?14*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "74LS125_0_0"
			(pin power_in line
				(at -1.27 5.08 270)
				(length 2.54)
				(name "VCC"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
			)
			(pin power_in line
				(at -1.27 -5.08 90)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
		)
		(symbol "74LS125_1_0"
			(polyline
				(pts
					(xy -3.81 3.81) (xy -3.81 -3.81) (xy 3.81 0) (xy -3.81 3.81)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin input line
				(at -7.62 0 0)
				(length 3.81)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input inverted
				(at 2.54 -5.08 90)
				(length 4.445)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin tri_state line
				(at 7.62 0 180)
				(length 3.81)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "74LS125_2_0"
			(polyline
				(pts
					(xy -3.81 3.81) (xy -3.81 -3.81) (xy 3.81 0) (xy -3.81 3.81)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin input line
				(at -7.62 0 0)
				(length 3.81)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input inverted
				(at 2.54 -5.08 90)
				(length 4.445)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin tri_state line
				(at 7.62 0 180)
				(length 3.81)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "74LS125_3_0"
			(polyline
				(pts
					(xy -3.81 3.81) (xy -3.81 -3.81) (xy 3.81 0) (xy -3.81 3.81)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin input line
				(at -7.62 0 0)
				(length 3.81)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input inverted
				(at 2.54 -5.08 90)
				(length 4.445)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin tri_state line
				(at 7.62 0 180)
				(length 3.81)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "74LS125_4_0"
			(polyline
				(pts
					(xy -3.81 3.81) (xy -3.81 -3.81) (xy 3.81 0) (xy -3.81 3.81)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type background)
				)
			)
			(pin input line
				(at -7.62 0 0)
				(length 3.81)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input inverted
				(at 2.54 -5.08 90)
				(length 4.445)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin tri_state line
				(at 7.62 0 180)
				(length 3.81)
				(name "~"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "7805"
		(pin_names
			(offset 0.762)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 3.81 -4.9784 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "7805"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" ""
			(at 1.27 -7.5184 0)
			(effects
				(font
					(size 0.381 0.381)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "7805_0_1"
			(rectangle
				(start -5.08 -3.81)
				(end 5.08 3.81)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "7805_1_1"
			(pin input line
				(at -10.16 1.27 0)
				(length 5.08)
				(name "VI"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin input line
				(at 0 -6.35 90)
				(length 2.54)
				(name "GND"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
			)
			(pin power_out line
				(at 10.16 1.27 180)
				(length 5.08)
				(name "VO"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "BC237"
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "Q"
			(at 0 -3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "BC237"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "Package_TO_SOT_THT:TO-92"
			(at -2.54 -5.08 0)
			(effects
				(font
					(size 0.381 0.381)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "BC237_0_1"
			(polyline
				(pts
					(xy 0 1.905) (xy 0 -1.905) (xy 0 -1.905)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 2.54 2.54)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 0)
				(radius 2.8194)
				(stroke
					(width 0.3048)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 1.27 -1.27) (xy 0 0) (xy 0 0)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 2.286 -2.286) (xy 2.54 -2.54) (xy 2.54 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 2.286 -2.286) (xy 1.778 -0.762) (xy 0.762 -1.778) (xy 2.286 -2.286) (xy 2.286 -2.286)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "BC237_1_1"
			(pin input line
				(at -5.08 0 0)
				(length 5.08)
				(name "B"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 5.08 270)
				(length 2.54)
				(name "C"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 -5.08 90)
				(length 2.54)
				(name "E"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "BC307"
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "Q"
			(at -1.27 -3.81 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify right)
			)
		)
		(property "Value" "BC307"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.524 1.524)
				)
				(justify right)
			)
		)
		(property "Footprint" "Package_TO_SOT_THT:TO-92"
			(at -2.54 -5.08 0)
			(effects
				(font
					(size 0.381 0.381)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "BC307_0_1"
			(polyline
				(pts
					(xy 0 1.905) (xy 0 -1.905) (xy 0 -1.905)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 2.54 2.54)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0.635 -0.635) (xy 0 0) (xy 0 0)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0.635 -0.635) (xy 1.27 -1.905) (xy 1.905 -1.27) (xy 0.635 -0.635) (xy 0.635 -0.635)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
			(circle
				(center 1.27 0)
				(radius 2.8194)
				(stroke
					(width 0.3048)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 2.54 -2.54) (xy 1.651 -1.651) (xy 1.651 -1.651)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "BC307_1_1"
			(pin input line
				(at -5.08 0 0)
				(length 5.08)
				(name "B"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 5.08 270)
				(length 2.54)
				(name "C"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 2.54 -5.08 90)
				(length 2.54)
				(name "E"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "C"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.254)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "C"
			(at 0.635 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "C"
			(at 0.635 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0.9652 -3.81 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "C? C_????_* C_???? SMD*_c Capacitor*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "C_0_1"
			(polyline
				(pts
					(xy -2.032 0.762) (xy 2.032 0.762)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.032 -0.762) (xy 2.032 -0.762)
				)
				(stroke
					(width 0.508)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "C_1_1"
			(pin passive line
				(at 0 3.81 270)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 0 -3.81 90)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CONN_2"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "P"
			(at -1.27 0 90)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Value" "CONN_2"
			(at 1.27 0 90)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "CONN_2_0_1"
			(rectangle
				(start -2.54 3.81)
				(end 2.54 -3.81)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "CONN_2_1_1"
			(pin passive inverted
				(at -8.89 2.54 0)
				(length 6.35)
				(name "P1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive inverted
				(at -8.89 -2.54 0)
				(length 6.35)
				(name "PM"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "CP"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.254)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "C"
			(at 0.635 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "CP"
			(at 0.635 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 0.9652 -3.81 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "CP* Elko* TantalC* C*elec c_elec* SMD*_Pol"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "CP_0_1"
			(rectangle
				(start -2.286 1.016)
				(end 2.286 0.508)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.778 2.286)
				(end -0.762 2.286)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start -1.27 1.778)
				(end -1.27 2.794)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(rectangle
				(start 2.286 -0.508)
				(end -2.286 -1.016)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "CP_1_1"
			(pin passive line
				(at 0 3.81 270)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 0 -3.81 90)
				(length 2.794)
				(name "~"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "D"
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "D"
			(at 0 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "D"
			(at 0 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "Diode_* D-Pak_TO252AA *SingleDiode *_Diode_* *SingleDiode*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "D_0_1"
			(polyline
				(pts
					(xy -1.27 1.27) (xy -1.27 -1.27)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 0) (xy 1.27 1.27) (xy 1.27 -1.27) (xy -1.27 0)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "D_1_1"
			(pin passive line
				(at -3.81 0 0)
				(length 2.54)
				(name "K"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
			)
			(pin passive line
				(at 3.81 0 180)
				(length 2.54)
				(name "A"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "DB9"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at 0 13.97 0)
			(effects
				(font
					(size 1.778 1.778)
				)
			)
		)
		(property "Value" "DB9"
			(at 0 -13.97 0)
			(effects
				(font
					(size 1.778 1.778)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "DB9*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "DB9_0_1"
			(polyline
				(pts
					(xy -3.81 10.16) (xy -2.54 10.16)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 7.62) (xy 0.508 7.62)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 5.08) (xy -2.54 5.08)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 2.54) (xy 0.508 2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 0) (xy -2.54 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -2.54) (xy 0.508 -2.54)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -5.08) (xy -2.54 -5.08)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -7.62) (xy 0.508 -7.62)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -10.16) (xy -2.54 -10.16)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -11.6586) (xy -3.556 -11.938)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.81 -11.684) (xy -3.81 11.684)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.556 11.938) (xy -3.81 11.684)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.556 11.938) (xy -2.54 12.446)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -3.556 -11.938) (xy -2.794 -12.446)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.794 -12.446) (xy -1.27 -12.446)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -2.54 12.446) (xy -1.778 12.446)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 10.16)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 5.08)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 0)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 -5.08)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center -1.778 -10.16)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 7.62)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 2.54)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 -2.54)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 1.27 -7.62)
				(radius 0.762)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.2766 9.906) (xy -1.778 12.446)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.2766 9.906) (xy 3.81 9.398)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.556 -10.3886) (xy -1.27 -12.446)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.81 9.398) (xy 3.81 -9.906)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 3.81 -9.906) (xy 3.556 -10.3886)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "DB9_1_1"
			(pin passive line
				(at -11.43 10.16 0)
				(length 7.62)
				(name "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 7.62 0)
				(length 7.62)
				(name "P9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 5.08 0)
				(length 7.62)
				(name "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 2.54 0)
				(length 7.62)
				(name "P8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 0 0)
				(length 7.62)
				(name "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -2.54 0)
				(length 7.62)
				(name "P7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -5.08 0)
				(length 7.62)
				(name "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -7.62 0)
				(length 7.62)
				(name "P6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -11.43 -10.16 0)
				(length 7.62)
				(name "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "D_Schottky"
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "D"
			(at 0 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "D_Schottky"
			(at 0 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at -2.54 0 0)
			(effects
				(font
					(size 0.254 0.254)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "D-Pak_TO252AA Diode_* *SingleDiode *SingleDiode* *_Diode_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "D_Schottky_0_1"
			(polyline
				(pts
					(xy -1.905 0.635) (xy -1.905 1.27) (xy -1.27 1.27) (xy -1.27 -1.27) (xy -0.635 -1.27) (xy -0.635 -0.635)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 0) (xy 1.27 1.27) (xy 1.27 -1.27) (xy -1.27 0)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "D_Schottky_1_1"
			(pin passive line
				(at -3.81 0 0)
				(length 2.54)
				(name "K"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin passive line
				(at 3.81 0 180)
				(length 2.54)
				(name "A"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "GND"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 0 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 0 -1.778 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "GND_0_1"
			(polyline
				(pts
					(xy -1.27 0) (xy 0 -1.27) (xy 1.27 0) (xy -1.27 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "GND_1_1"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "GND"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.762 0.762)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "INDUCTOR"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "L"
			(at -1.27 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "INDUCTOR"
			(at 2.54 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "INDUCTOR_0_1"
			(arc
				(start 0.0254 4.9784)
				(mid 1.1942 3.7719)
				(end 0.0254 2.5654)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start 0.0254 2.5908)
				(mid 1.2704 1.3081)
				(end 0.0254 0.0254)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start 0.0254 0.0508)
				(mid 1.2704 -1.2319)
				(end 0.0254 -2.5146)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start 0.0254 -2.54)
				(mid 1.245 -3.7973)
				(end 0.0254 -5.0546)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "INDUCTOR_1_1"
			(pin passive line
				(at 0 7.62 270)
				(length 2.54)
				(name "1"
					(effects
						(font
							(size 1.778 1.778)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.778 1.778)
						)
					)
				)
			)
			(pin passive line
				(at 0 -7.62 90)
				(length 2.54)
				(name "2"
					(effects
						(font
							(size 1.778 1.778)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.778 1.778)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "JUMPER"
		(pin_names
			(offset 0.762)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "JP"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "JUMPER"
			(at 0 -2.032 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "JUMPER_0_1"
			(circle
				(center -2.54 0)
				(radius 0.889)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(arc
				(start -2.4892 1.27)
				(mid 0.0127 2.5097)
				(end 2.5146 1.27)
				(stroke
					(width 0.3048)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(circle
				(center 2.54 0)
				(radius 0.889)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(pin passive line
				(at -7.62 0 0)
				(length 4.191)
				(name "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 7.62 0 180)
				(length 4.191)
				(name "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "LED"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "D"
			(at 0 2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "LED"
			(at 0 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "LED-3MM LED-5MM LED-10MM LED-0603 LED-0805 LED-1206 LEDV"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "LED_0_1"
			(polyline
				(pts
					(xy -2.032 -0.635) (xy -3.175 -1.651) (xy -3.048 -1.016)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.651 -1.016) (xy -2.794 -2.032) (xy -2.667 -1.397)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 1.27) (xy -1.27 -1.27)
				)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy -1.27 0) (xy 1.27 1.27) (xy 1.27 -1.27) (xy -1.27 0)
				)
				(stroke
					(width 0.1524)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "LED_1_1"
			(pin passive line
				(at -5.08 0 0)
				(length 3.81)
				(name "K"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 5.08 0 180)
				(length 3.81)
				(name "A"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "LT1373"
		(pin_names
			(offset 0.762)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 15.24 12.7 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "LT1373"
			(at -12.7 12.7 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "LT1373_0_1"
			(rectangle
				(start -17.78 -10.16)
				(end 17.78 10.16)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "LT1373_1_1"
			(pin passive line
				(at -25.4 6.35 0)
				(length 7.62)
				(name "FB-"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -25.4 -6.35 0)
				(length 7.62)
				(name "S/S"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -7.62 -17.78 90)
				(length 7.62)
				(name "GND"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at -3.81 -17.78 90)
				(length 7.62)
				(name "GND_S"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin power_in line
				(at 0 17.78 270)
				(length 7.62)
				(name "Vin"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at 6.35 -17.78 90)
				(length 7.62)
				(name "Vc"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at 25.4 6.35 180)
				(length 7.62)
				(name "Vsw"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin input line
				(at 25.4 -6.35 180)
				(length 7.62)
				(name "FB+"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "MOUNTING_HOLE"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0.762)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "P"
			(at 2.032 0 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(justify left)
			)
		)
		(property "Value" "MOUNTING_HOLE"
			(at 0 1.397 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "*Hole*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "MOUNTING_HOLE_0_1"
			(circle
				(center 0 0)
				(radius 0.7874)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "PIC12C508A"
		(pin_names
			(offset 1.016)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U"
			(at 0 17.78 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "PIC12C508A"
			(at 0 -16.51 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "PIC12C508A_0_1"
			(rectangle
				(start 10.16 -15.24)
				(end -11.43 16.51)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "PIC12C508A_1_1"
			(pin power_in line
				(at -19.05 12.7 0)
				(length 7.62)
				(name "VDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -19.05 5.08 0)
				(length 7.62)
				(name "GP5/OSC1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -19.05 -5.08 0)
				(length 7.62)
				(name "GP4/OSC2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -19.05 -12.7 0)
				(length 7.62)
				(name "GP3/MCLR"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 17.78 12.7 180)
				(length 7.62)
				(name "VSS"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 17.78 5.08 180)
				(length 7.62)
				(name "GP0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 17.78 -5.08 180)
				(length 7.62)
				(name "GP1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 17.78 -12.7 180)
				(length 7.62)
				(name "GP2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "PIC16F54"
		(pin_names
			(offset 1.016)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "U?"
			(at 0 -19.05 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Value" "PIC16F54"
			(at 0 20.32 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "PIC16F54_0_1"
			(rectangle
				(start -12.7 17.78)
				(end 11.43 -17.78)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "PIC16F54_1_1"
			(pin bidirectional line
				(at -20.32 15.24 0)
				(length 7.62)
				(name "RA2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 11.43 0)
				(length 7.62)
				(name "RA3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at -20.32 7.62 0)
				(length 7.62)
				(name "T0ckl"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 3.81 0)
				(length 7.62)
				(name "MCLR"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -20.32 0 0)
				(length 7.62)
				(name "VSS"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 -3.81 0)
				(length 7.62)
				(name "RB0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 -7.62 0)
				(length 7.62)
				(name "RB1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 -11.43 0)
				(length 7.62)
				(name "RB2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 -15.24 0)
				(length 7.62)
				(name "RB3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 15.24 180)
				(length 7.62)
				(name "RA1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 11.43 180)
				(length 7.62)
				(name "RA0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 19.05 7.62 180)
				(length 7.62)
				(name "OSC1/CLKI"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 19.05 3.81 180)
				(length 7.62)
				(name "OSC2/CLKO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 19.05 0 180)
				(length 7.62)
				(name "VDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 -3.81 180)
				(length 7.62)
				(name "ICSPD/RB7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 -7.62 180)
				(length 7.62)
				(name "ICSPC/RB6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 -11.43 180)
				(length 7.62)
				(name "RB5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 -15.24 180)
				(length 7.62)
				(name "RB4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(symbol "PIC16F54_1_2"
			(pin bidirectional line
				(at -20.32 15.24 0)
				(length 7.62)
				(name "RA2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 11.43 0)
				(length 7.62)
				(name "RA3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at -20.32 7.62 0)
				(length 7.62)
				(name "T0ckl"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at -20.32 3.81 0)
				(length 7.62)
				(name "MCLR"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at -20.32 0 0)
				(length 7.62)
				(name "VSS"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 -3.81 0)
				(length 7.62)
				(name "RB0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 -7.62 0)
				(length 7.62)
				(name "RB1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 -11.43 0)
				(length 7.62)
				(name "RB2"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at -20.32 -15.24 0)
				(length 7.62)
				(name "RB3"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 15.24 180)
				(length 7.62)
				(name "RA1"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "18"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 11.43 180)
				(length 7.62)
				(name "RA0"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "17"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin input line
				(at 19.05 7.62 180)
				(length 7.62)
				(name "OSC1/CLKI"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin output line
				(at 19.05 3.81 180)
				(length 7.62)
				(name "OSC2/CLKO"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin power_in line
				(at 19.05 0 180)
				(length 7.62)
				(name "VDD"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 -3.81 180)
				(length 7.62)
				(name "ICSPD/RB7"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 -7.62 180)
				(length 7.62)
				(name "ICSPC/RB6"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 -11.43 180)
				(length 7.62)
				(name "RB5"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
			(pin bidirectional line
				(at 19.05 -15.24 180)
				(length 7.62)
				(name "RB4"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.27 1.27)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "POT"
		(pin_names
			(offset 1.016)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "RV"
			(at 0 -2.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "POT"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "POT_0_1"
			(rectangle
				(start -3.81 1.27)
				(end 3.81 -1.27)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 1.27) (xy -0.508 1.778) (xy 0.508 1.778)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type outline)
				)
			)
		)
		(symbol "POT_1_1"
			(pin passive line
				(at -6.35 0 0)
				(length 2.54)
				(name "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 0 3.81 270)
				(length 2.032)
				(name "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
			(pin passive line
				(at 6.35 0 180)
				(length 2.54)
				(name "3"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "PWR_FLAG"
		(power)
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
			(hide yes)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#FLG"
			(at 0 2.413 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "PWR_FLAG"
			(at 0 4.572 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "PWR_FLAG_0_0"
			(pin power_out line
				(at 0 0 90)
				(length 0)
				(name "pwr"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
		)
		(symbol "PWR_FLAG_0_1"
			(polyline
				(pts
					(xy 0 0) (xy 0 1.27) (xy -1.905 2.54) (xy 0 3.81) (xy 1.905 2.54) (xy 0 1.27)
				)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "R"
		(pin_numbers
			(hide yes)
		)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "R"
			(at 2.032 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "R"
			(at 0 0 90)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at -1.778 0 90)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "ki_fp_filters" "R_* Resistor_*"
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "R_0_1"
			(rectangle
				(start -1.016 -2.54)
				(end 1.016 2.54)
				(stroke
					(width 0.2032)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "R_1_1"
			(pin passive line
				(at 0 3.81 270)
				(length 1.27)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 0 -3.81 90)
				(length 1.27)
				(name "~"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "SUPP28"
		(pin_names
			(offset 1.016)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "J"
			(at 0 2.54 0)
			(effects
				(font
					(size 1.778 1.778)
				)
			)
		)
		(property "Value" "SUPP28"
			(at 0 -2.54 0)
			(effects
				(font
					(size 1.778 1.778)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "SUPP28_0_1"
			(rectangle
				(start -7.62 -19.05)
				(end 7.62 19.05)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "SUPP28_1_1"
			(pin passive line
				(at -15.24 16.51 0)
				(length 7.62)
				(name "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 13.97 0)
				(length 7.62)
				(name "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 11.43 0)
				(length 7.62)
				(name "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 8.89 0)
				(length 7.62)
				(name "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 6.35 0)
				(length 7.62)
				(name "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 3.81 0)
				(length 7.62)
				(name "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 1.27 0)
				(length 7.62)
				(name "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -1.27 0)
				(length 7.62)
				(name "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -3.81 0)
				(length 7.62)
				(name "9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -6.35 0)
				(length 7.62)
				(name "10"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -8.89 0)
				(length 7.62)
				(name "11"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -11.43 0)
				(length 7.62)
				(name "12"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -13.97 0)
				(length 7.62)
				(name "13"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -16.51 0)
				(length 7.62)
				(name "14"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 16.51 180)
				(length 7.62)
				(name "28"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "28"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 13.97 180)
				(length 7.62)
				(name "27"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "27"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 11.43 180)
				(length 7.62)
				(name "26"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "26"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 8.89 180)
				(length 7.62)
				(name "25"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "25"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 6.35 180)
				(length 7.62)
				(name "24"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "24"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 3.81 180)
				(length 7.62)
				(name "23"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "23"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 1.27 180)
				(length 7.62)
				(name "22"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "22"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -1.27 180)
				(length 7.62)
				(name "21"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "21"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -3.81 180)
				(length 7.62)
				(name "20"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "20"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -6.35 180)
				(length 7.62)
				(name "19"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "19"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -8.89 180)
				(length 7.62)
				(name "18"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "18"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -11.43 180)
				(length 7.62)
				(name "17"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "17"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -13.97 180)
				(length 7.62)
				(name "16"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -16.51 180)
				(length 7.62)
				(name "15"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "SUPP40"
		(pin_names
			(offset 1.016)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "P"
			(at 0 27.94 0)
			(effects
				(font
					(size 1.778 1.778)
				)
			)
		)
		(property "Value" "SUPP40"
			(at 0 -27.94 0)
			(effects
				(font
					(size 1.778 1.778)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "SUPP40_0_1"
			(rectangle
				(start -7.62 -26.67)
				(end 7.62 26.67)
				(stroke
					(width 0.254)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(symbol "SUPP40_1_1"
			(pin passive line
				(at -15.24 24.13 0)
				(length 7.62)
				(name "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 21.59 0)
				(length 7.62)
				(name "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "2"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 19.05 0)
				(length 7.62)
				(name "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "3"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 16.51 0)
				(length 7.62)
				(name "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "4"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 13.97 0)
				(length 7.62)
				(name "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "5"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 11.43 0)
				(length 7.62)
				(name "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "6"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 8.89 0)
				(length 7.62)
				(name "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "7"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 6.35 0)
				(length 7.62)
				(name "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "8"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 3.81 0)
				(length 7.62)
				(name "9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "9"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 1.27 0)
				(length 7.62)
				(name "10"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "10"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -1.27 0)
				(length 7.62)
				(name "11"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "11"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -3.81 0)
				(length 7.62)
				(name "12"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "12"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -6.35 0)
				(length 7.62)
				(name "13"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "13"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -8.89 0)
				(length 7.62)
				(name "14"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "14"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -11.43 0)
				(length 7.62)
				(name "15"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "15"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -13.97 0)
				(length 7.62)
				(name "16"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "16"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -16.51 0)
				(length 7.62)
				(name "17"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "17"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -19.05 0)
				(length 7.62)
				(name "18"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "18"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -21.59 0)
				(length 7.62)
				(name "19"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "19"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at -15.24 -24.13 0)
				(length 7.62)
				(name "20"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "20"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 24.13 180)
				(length 7.62)
				(name "40"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "40"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 21.59 180)
				(length 7.62)
				(name "39"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "39"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 19.05 180)
				(length 7.62)
				(name "38"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "38"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 16.51 180)
				(length 7.62)
				(name "37"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "37"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 13.97 180)
				(length 7.62)
				(name "36"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "36"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 11.43 180)
				(length 7.62)
				(name "35"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "35"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 8.89 180)
				(length 7.62)
				(name "34"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "34"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 6.35 180)
				(length 7.62)
				(name "33"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "33"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 3.81 180)
				(length 7.62)
				(name "32"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "32"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 1.27 180)
				(length 7.62)
				(name "31"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "31"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -1.27 180)
				(length 7.62)
				(name "30"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "30"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -3.81 180)
				(length 7.62)
				(name "29"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "29"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -6.35 180)
				(length 7.62)
				(name "28"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "28"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -8.89 180)
				(length 7.62)
				(name "27"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "27"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -11.43 180)
				(length 7.62)
				(name "26"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "26"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -13.97 180)
				(length 7.62)
				(name "25"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "25"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -16.51 180)
				(length 7.62)
				(name "24"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "24"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -19.05 180)
				(length 7.62)
				(name "23"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "23"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -21.59 180)
				(length 7.62)
				(name "22"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "22"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
			(pin passive line
				(at 15.24 -24.13 180)
				(length 7.62)
				(name "21"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
				(number "21"
					(effects
						(font
							(size 1.524 1.524)
						)
					)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "VCC"
		(power)
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 2.54 0)
			(effects
				(font
					(size 0.762 0.762)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 0 2.54 0)
			(effects
				(font
					(size 0.762 0.762)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "VCC_0_0"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "VCC"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 0.508 0.508)
						)
					)
				)
			)
		)
		(symbol "VCC_0_1"
			(circle
				(center 0 1.27)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 0) (xy 0 0.762) (xy 0 0.762)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
	(symbol "VPP"
		(pin_names
			(offset 0)
		)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(property "Reference" "#PWR"
			(at 0 5.08 0)
			(effects
				(font
					(size 1.016 1.016)
				)
				(hide yes)
			)
		)
		(property "Value" "VPP"
			(at 0 3.81 0)
			(effects
				(font
					(size 1.016 1.016)
				)
			)
		)
		(property "Footprint" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.524 1.524)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(symbol "VPP_0_0"
			(pin power_in line
				(at 0 0 90)
				(length 0)
				(hide yes)
				(name "VPP"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
				(number "1"
					(effects
						(font
							(size 1.016 1.016)
						)
					)
				)
			)
		)
		(symbol "VPP_0_1"
			(circle
				(center 0 2.032)
				(radius 0.508)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
			(polyline
				(pts
					(xy 0 1.524) (xy 0 0)
				)
				(stroke
					(width 0)
					(type default)
				)
				(fill
					(type none)
				)
			)
		)
		(embedded_fonts no)
	)
)
