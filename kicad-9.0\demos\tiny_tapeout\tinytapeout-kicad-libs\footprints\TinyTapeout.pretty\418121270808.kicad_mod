(footprint "************"
	(version 20240108)
	(generator "pcbnew")
	(generator_version "8.0")
	(layer "F.Cu")
	(descr "Wurth Small Compact SMT with Raised Actuator 2.54 mm,8poles")
	(tags "DIP switch")
	(property "Reference" "REF**"
		(at 13.512 -3.0764 0)
		(layer "F.SilkS")
		(uuid "ad1a1fe2-5d8b-4d6e-8e82-ee17b04caa38")
		(effects
			(font
				(size 0.64 0.64)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "************"
		(at 16.2868 -1.7764 0)
		(layer "F.Fab")
		(uuid "28ce5093-e04d-443d-9cea-dd1e889c5124")
		(effects
			(font
				(size 0.64 0.64)
				(thickness 0.15)
			)
		)
	)
	(property "Footprint" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "b25cc11e-99cb-4d1b-a6e6-ec03638798ac")
		(effects
			(font
				(size 1.27 1.27)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "49537fc5-ddd3-414c-9c7b-10105bac27ca")
		(effects
			(font
				(size 1.27 1.27)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "e727fc4d-09a6-4f2e-9d70-a51783f20d92")
		(effects
			(font
				(size 1.27 1.27)
			)
		)
	)
	(attr smd)
	(fp_line
		(start -10.73 -3.2)
		(end -10.73 3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "b255bf29-3b11-486f-8545-e14869415295")
	)
	(fp_line
		(start -10.73 3.2)
		(end -9.79 3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "b73a1708-6573-41f6-b48b-aec90ee5e85c")
	)
	(fp_line
		(start -9.79 -3.2)
		(end -10.73 -3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "3de25840-a7ee-4bfc-8c1d-156be7fc4c7e")
	)
	(fp_line
		(start -8.02 3.2)
		(end -7.22 3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "c6e44d87-f12c-4f3c-9bbe-e0b4bc768afe")
	)
	(fp_line
		(start -7.22 -3.2)
		(end -8.02 -3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "ed0b9b1e-58a5-4dc9-8052-e403c26b4ca0")
	)
	(fp_line
		(start -5.48 3.2)
		(end -4.68 3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "43ba718d-0176-415b-8fd8-01b2b282cc13")
	)
	(fp_line
		(start -4.68 -3.2)
		(end -5.48 -3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "2652f2b0-c5c9-4538-b675-363b60cce2f7")
	)
	(fp_line
		(start -2.94 3.2)
		(end -2.14 3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "9e108f9c-8450-4b8e-b560-ae21288a1548")
	)
	(fp_line
		(start -2.14 -3.2)
		(end -2.94 -3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "0a9d4991-86e6-4274-a547-86b86b4b065e")
	)
	(fp_line
		(start -0.4 3.2)
		(end 0.4 3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f2741d29-67f6-4baa-b305-dc793674a16f")
	)
	(fp_line
		(start 0.4 -3.2)
		(end -0.4 -3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "5b68b83a-d917-4f56-a924-506c353b5d49")
	)
	(fp_line
		(start 2.14 3.2)
		(end 2.94 3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "f7966e70-41de-4f5f-a500-c3ed7d8eef52")
	)
	(fp_line
		(start 2.94 -3.2)
		(end 2.14 -3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "59d7d23e-3cb6-4f09-bf99-8fe31779ec5b")
	)
	(fp_line
		(start 4.68 3.2)
		(end 5.48 3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "40dcef3d-1bb7-4fc9-99b4-7c2f6c7fc675")
	)
	(fp_line
		(start 5.48 -3.2)
		(end 4.68 -3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "c897e70e-9649-42a9-91ce-a8a0dd96144a")
	)
	(fp_line
		(start 7.22 3.2)
		(end 8.02 3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "a70960e2-e99c-4d1e-91d0-d339b2b6f1a9")
	)
	(fp_line
		(start 8.02 -3.2)
		(end 7.22 -3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "8639bff6-a6b0-4b21-bdc4-16b40d3a04a9")
	)
	(fp_line
		(start 9.79 -3.2)
		(end 10.73 -3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "0e8d3516-0cab-4f4a-9b4d-d54c06344523")
	)
	(fp_line
		(start 10.73 -3.2)
		(end 10.73 3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "920681e7-7a5e-47b7-b9a8-3adaef6c7324")
	)
	(fp_line
		(start 10.73 3.2)
		(end 9.79 3.2)
		(stroke
			(width 0.2)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "3de29a23-d00b-4145-b60b-9b19e4651f84")
	)
	(fp_circle
		(center -9.39 2.7)
		(end -9.29 2.7)
		(stroke
			(width 0.2)
			(type solid)
		)
		(fill none)
		(layer "F.SilkS")
		(uuid "ecb0e48d-e0a4-4124-a4f5-e475c3a21f78")
	)
	(fp_line
		(start -11.25 -3.4)
		(end -9.67 -3.4)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "541b234e-4c08-4a0e-b124-95fdb27cce5f")
	)
	(fp_line
		(start -11.25 3.4)
		(end -11.25 -3.4)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "d2793766-5437-4694-9b20-07a10e8a8ac2")
	)
	(fp_line
		(start -9.67 -5.7)
		(end 9.67 -5.7)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "ab859cb2-214b-4b29-994d-3c80a2a0e9ed")
	)
	(fp_line
		(start -9.67 -3.4)
		(end -9.67 -5.7)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "ed279970-5ee6-4e51-adb0-3d126f14f21d")
	)
	(fp_line
		(start -9.67 3.4)
		(end -11.25 3.4)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "5ec39d3a-f249-4123-a120-79631527967c")
	)
	(fp_line
		(start -9.67 5.7)
		(end -9.67 3.4)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "c20f8fc3-097a-46cd-ba5c-ff14d216484a")
	)
	(fp_line
		(start 9.67 -5.7)
		(end 9.67 -3.4)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "6dcb274e-1074-401f-8b93-385ec9ae97c0")
	)
	(fp_line
		(start 9.67 -3.4)
		(end 11.25 -3.4)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "03ac4267-ee60-4b5d-a403-610bebc8ac05")
	)
	(fp_line
		(start 9.67 3.4)
		(end 9.67 5.7)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "ae375911-c019-4ccd-b68b-cbaaff8f06b7")
	)
	(fp_line
		(start 9.67 5.7)
		(end -9.67 5.7)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "70f17a48-bd87-4fd9-8efc-3e2c3f0cfc8e")
	)
	(fp_line
		(start 11.25 -3.4)
		(end 11.25 3.4)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "74445b2e-e1bf-487c-985a-db11989cfcc7")
	)
	(fp_line
		(start 11.25 3.4)
		(end 9.67 3.4)
		(stroke
			(width 0.12)
			(type default)
		)
		(layer "F.CrtYd")
		(uuid "4e0e1325-2f85-458a-a807-3e9b09d902c9")
	)
	(fp_line
		(start -10.63 -3.1)
		(end 10.63 -3.1)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "f5c9bf7f-ca60-4630-ad75-0942be0d3f37")
	)
	(fp_line
		(start -10.63 3.1)
		(end -10.63 -3.1)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "34d7a22a-8bae-4be8-96d1-b29b97e1f652")
	)
	(fp_line
		(start 10.63 -3.1)
		(end 10.63 3.1)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "2897905f-1792-4020-8131-dd03fe3c0eb2")
	)
	(fp_line
		(start 10.63 3.1)
		(end -10.63 3.1)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(uuid "287937fc-2a7b-4897-ae37-a7adc188436a")
	)
	(fp_text user "${REFERENCE}"
		(at -5.2 0.4 0)
		(unlocked yes)
		(layer "F.Fab")
		(uuid "56939241-aac5-4b4b-a74d-367cab4f6ef3")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
			(justify left bottom)
		)
	)
	(pad "1" smd rect
		(at -8.89 4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "df6de85e-3fe9-4b4a-a9d1-ef7749610f2e")
	)
	(pad "2" smd rect
		(at -6.35 4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "99c581a4-04d2-4d56-969a-fd3ef12276e5")
	)
	(pad "3" smd rect
		(at -3.81 4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "9278a186-fb30-4e70-85f2-0eca9ef3e089")
	)
	(pad "4" smd rect
		(at -1.27 4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "0ba72233-99ad-4ec7-9b62-8969479c6c48")
	)
	(pad "5" smd rect
		(at 1.27 4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "4b638d4a-a5aa-4fd4-b0f5-96867fd04ca6")
	)
	(pad "6" smd rect
		(at 3.81 4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "08f5a3e9-0afe-422f-bee4-e01852c92216")
	)
	(pad "7" smd rect
		(at 6.35 4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "e4381e3e-e3a7-4cf9-92eb-e873ca8576c5")
	)
	(pad "8" smd rect
		(at 8.89 4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "c21c8b17-2c6e-4613-b8a3-d7a24d0e987e")
	)
	(pad "9" smd rect
		(at 8.89 -4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "9ac9df8f-bec8-4e2a-887d-d2fb860eaf5e")
	)
	(pad "10" smd rect
		(at 6.35 -4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "e17592b5-7c54-4d2c-bc96-7d0f0df4ab78")
	)
	(pad "11" smd rect
		(at 3.81 -4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "083be566-3e7f-4ff2-8579-7094dd3b2285")
	)
	(pad "12" smd rect
		(at 1.27 -4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "0b1be1f9-ca43-49c3-9765-add57749a190")
	)
	(pad "13" smd rect
		(at -1.27 -4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "0da32744-d591-4949-b3e0-cf16754c8b7b")
	)
	(pad "14" smd rect
		(at -3.81 -4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "42aef306-8ed2-4350-a73d-6765ca88ba7a")
	)
	(pad "15" smd rect
		(at -6.35 -4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "aefae8ed-6d9c-42c6-9e89-b35d53076507")
	)
	(pad "16" smd rect
		(at -8.89 -4.4)
		(size 1.1 2.2)
		(layers "F.Cu" "F.Paste" "F.Mask")
		(uuid "7dc970dd-4853-4548-a73e-2b49c96a68f0")
	)
	(model "${KIPRJMOD}/tinytapeout-kicad-libs/3dmodels/************.stp"
		(offset
			(xyz 0 0 3.1)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)