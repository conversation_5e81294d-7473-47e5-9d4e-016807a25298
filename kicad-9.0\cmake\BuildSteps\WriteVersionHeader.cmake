#
#  This program source code file is part of KICAD, a free EDA CAD application.
#
#  Copyright (C) 2015 <PERSON> <<EMAIL>>
#  Copyright (C) 2015-2024 KiCad Developers, see AUTHORS.txt for contributors.
#
#  This program is free software; you can redistribute it and/or
#  modify it under the terms of the GNU General Public License
#  as published by the Free Software Foundation; either version 2
#  of the License, or (at your option) any later version.
#
#  This program is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#  GNU General Public License for more details.
#
#  You should have received a copy of the GNU General Public License
#  along with this program; if not, you may find one here:
#  http://www.gnu.org/licenses/old-licenses/gpl-2.0.html
#  or you may search the http://www.gnu.org website for the version 2 license,
#  or you may write to the Free Software Foundation, Inc.,
#  51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA
#

# Create the KiCad version strings
include( ${KICAD_CMAKE_MODULE_PATH}/KiCadVersion.cmake )
include( ${KICAD_CMAKE_MODULE_PATH}/KiCadFullVersion.cmake )

# Extract the major and minor build version as a string
string( REGEX MATCH
        "([0-9]+)\\.([0-9]+)\\.([0-9]+).*"
        KICAD_MAJOR_MINOR_PATCH_VERSION
        "${KICAD_SEMANTIC_VERSION}"
    )

if( CMAKE_MATCH_COUNT EQUAL 3 )
    # Match slot 0 is the full string, so we want slots 1 & 2
    set( KICAD_MAJOR_MINOR_VERSION "${CMAKE_MATCH_1}.${CMAKE_MATCH_2}" )

    set( KICAD_MAJOR_MINOR_PATCH_VERSION "${CMAKE_MATCH_1}.${CMAKE_MATCH_2}.${CMAKE_MATCH_3}" )
    set( KICAD_MAJOR_MINOR_PATCH_TUPLE "{ ${CMAKE_MATCH_1}, ${CMAKE_MATCH_2}, ${CMAKE_MATCH_3} }" )

    set( KICAD_MAJOR_VERSION "${CMAKE_MATCH_1}" )
    set( KICAD_MINOR_VERSION "${CMAKE_MATCH_2}" )
    set( KICAD_PATCH_VERSION "${CMAKE_MATCH_3}" )

    if( KICAD_MINOR_VERSION STREQUAL "99" )
        set( KICAD_IS_NIGHTLY "1" )
    else()
        set( KICAD_IS_NIGHTLY "0" )
    endif()

    set( KICAD_WIN32_RC_FILEVER_STR "${CMAKE_MATCH_1}.${CMAKE_MATCH_2}.${CMAKE_MATCH_3}.${KICAD_GIT_REV}\\0" )
    set( KICAD_WIN32_RC_FILEVER     "${CMAKE_MATCH_1}, ${CMAKE_MATCH_2}, ${CMAKE_MATCH_3}, ${KICAD_GIT_REV}" )
    set( KICAD_WIN32_RC_PRODVER_STR "${CMAKE_MATCH_1}.${CMAKE_MATCH_2}.${CMAKE_MATCH_3}\\0" )
    set( KICAD_WIN32_RC_PRODVER     "${CMAKE_MATCH_1}, ${CMAKE_MATCH_2}, ${CMAKE_MATCH_3}, ${KICAD_GIT_REV}" )
else()
    message( FATAL_ERROR "Unable to extract major, minor and patch version string" )
endif()

set( _wvh_new_version_text
"/* Do not modify this file, it was automatically generated by CMake. */

/*
 * Define the KiCad build version strings.
 */
#ifndef __KICAD_VERSION_H__
#define __KICAD_VERSION_H__

#ifndef INCLUDE_KICAD_VERSION
#error Do not include kicad_build_version.h directly.  Include build_version.h instead.
#endif

#define KICAD_COMMIT_HASH               \"${KICAD_COMMIT_HASH}\"
#define KICAD_VERSION                   \"${KICAD_VERSION}\"
#define KICAD_VERSION_FULL              \"${KICAD_VERSION_FULL}\"
#define KICAD_SEMANTIC_VERSION          \"${KICAD_SEMANTIC_VERSION}\"
#define KICAD_MAJOR_VERSION             \"${KICAD_MAJOR_VERSION}\"
#define KICAD_MINOR_VERSION             \"${KICAD_MINOR_VERSION}\"
#define KICAD_PATCH_VERSION             \"${KICAD_PATCH_VERSION}\"
#define KICAD_IS_NIGHTLY                ${KICAD_IS_NIGHTLY}
#define KICAD_MAJOR_MINOR_VERSION       \"${KICAD_MAJOR_MINOR_VERSION}\"
#define KICAD_MAJOR_MINOR_PATCH_VERSION \"${KICAD_MAJOR_MINOR_PATCH_VERSION}\"
#define KICAD_MAJOR_MINOR_PATCH_TUPLE   ${KICAD_MAJOR_MINOR_PATCH_TUPLE}
#define KICAD_WIN32_RC_PRODVER          ${KICAD_WIN32_RC_PRODVER}
#define KICAD_WIN32_RC_PRODVER_STR      \"${KICAD_WIN32_RC_PRODVER_STR}\"
#define KICAD_WIN32_RC_FILEVER          ${KICAD_WIN32_RC_FILEVER}
#define KICAD_WIN32_RC_FILEVER_STR      \"${KICAD_WIN32_RC_FILEVER_STR}\"

#endif  /* __KICAD_VERSION_H__ */
" )

set( _wvh_write_version_file ON )

# Only write the header if it has changed, to avoid rebuilds
if( EXISTS ${OUTPUT_FILE} )
    file( READ ${OUTPUT_FILE} _wvh_old_version_text )
    if( _wvh_old_version_text STREQUAL _wvh_new_version_text )
        message( STATUS "Not updating ${OUTPUT_FILE}" )
        set( _wvh_write_version_file OFF )
    endif()
endif()

if( _wvh_write_version_file )
    message( STATUS "Writing ${OUTPUT_FILE} file with version: ${KICAD_VERSION_FULL}" )

    file( WRITE ${OUTPUT_FILE} ${_wvh_new_version_text} )

    # Write a simple document with only the full version
    file( WRITE ${TEXT_OUTPUT_FILE} "${KICAD_VERSION_FULL}" )
endif()

# There should always be a valid version.h file.  Otherwise, the build will fail.
if( NOT EXISTS ${OUTPUT_FILE} )
    message( FATAL_ERROR "Configuration failed to write file ${OUTPUT_FILE}." )
endif()
