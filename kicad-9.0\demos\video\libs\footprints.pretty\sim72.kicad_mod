(footprint "sim72"
	(version 20241229)
	(generator "pcbnew")
	(generator_version "9.0")
	(layer "F.Cu")
	(descr "Support 72 pins pour barettes SIMM 32 bits")
	(tags "SIMM")
	(property "Reference" "REF**"
		(at -18.542 -3.175 0)
		(layer "F.SilkS")
		(uuid "9620b842-3ef9-4da7-a7a9-481c84a769a9")
		(effects
			(font
				(size 1.5 1.5)
				(thickness 0.3048)
			)
		)
	)
	(property "Value" "SIM4X32"
		(at 14.224 -3.048 0)
		(layer "F.SilkS")
		(uuid "f25b95f1-b383-4215-95aa-94892ec14af7")
		(effects
			(font
				(size 1.5 1.5)
				(thickness 0.3048)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "a42002cb-8aa7-48df-a15e-ccea82388595")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0)
		(unlocked yes)
		(layer "F.Fab")
		(hide yes)
		(uuid "759bc5eb-f858-4349-b323-518ea9dfdb42")
		(effects
			(font
				(size 1.27 1.27)
				(thickness 0.15)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -57.785 -4.445)
		(end -57.785 3.175)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "40986c9b-598a-4c34-a656-8a08c51b292d")
	)
	(fp_line
		(start -57.785 3.175)
		(end 57.785 3.175)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "fbf3f4a1-e1dc-4828-a629-64f2170a85ed")
	)
	(fp_line
		(start 46.99 3.175)
		(end 47.625 2.159)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "cecb046e-dd3c-4bcd-91ef-b19111e98a99")
	)
	(fp_line
		(start 47.625 2.159)
		(end 48.26 3.175)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "82c3f726-e91f-4174-99bf-9196082583f0")
	)
	(fp_line
		(start 57.785 -4.445)
		(end -57.785 -4.445)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "6db576ee-7bf1-44b6-a39d-c8a24e44e090")
	)
	(fp_line
		(start 57.785 3.175)
		(end 57.785 -4.445)
		(stroke
			(width 0.3048)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "73e9ff5e-a14f-4a4e-ad1e-8fec87d821b8")
	)
	(fp_rect
		(start -57.9 -4.7)
		(end 58 3.4)
		(stroke
			(width 0.12)
			(type default)
		)
		(fill no)
		(layer "F.CrtYd")
		(uuid "45853c9c-bbd9-4a9a-bcba-0f1a5be75df2")
	)
	(pad "" thru_hole circle
		(at -55.88 0)
		(size 3 3)
		(drill 2.5)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6e0feb08-40bb-4e76-bfc6-acb4a7c08269")
	)
	(pad "" thru_hole circle
		(at 0 0)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "692af3a2-0d75-4cca-9e10-56865be8692b")
	)
	(pad "" thru_hole circle
		(at 55.88 0)
		(size 3 3)
		(drill 2.5)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "cd0d5beb-071e-4290-8b50-88ebab0e3b60")
	)
	(pad "1" thru_hole rect
		(at 47.625 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "1242c470-64b9-4326-bc23-0cb42f5d8956")
	)
	(pad "2" thru_hole circle
		(at 46.355 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "daed15e4-3339-47f0-8363-00edb81afd1d")
	)
	(pad "3" thru_hole circle
		(at 45.085 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "e7220cc7-d2b8-4a23-87fc-d6b97a1f1526")
	)
	(pad "4" thru_hole circle
		(at 43.815 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "bb9c7142-075d-4e6a-b1c3-777ac453cd28")
	)
	(pad "5" thru_hole circle
		(at 42.545 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "18ea159c-a720-4026-9aac-a3eb52410bf8")
	)
	(pad "6" thru_hole circle
		(at 41.275 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "fcb622d1-196e-470c-89c8-c52942bae265")
	)
	(pad "7" thru_hole circle
		(at 40.005 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "9e886d4d-86ad-4fa9-bab1-90e604969344")
	)
	(pad "8" thru_hole circle
		(at 38.735 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6f1c5061-e600-46d2-8be1-d3abe6d43390")
	)
	(pad "9" thru_hole circle
		(at 37.465 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "ca575eba-02ac-462e-b234-cb7353ba6f60")
	)
	(pad "10" thru_hole circle
		(at 36.195 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "fb7d2483-196c-453c-869c-99626f8f4bcd")
	)
	(pad "11" thru_hole circle
		(at 34.925 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "40891511-d67b-4f5b-8fbc-56fd38f1d4d0")
	)
	(pad "12" thru_hole circle
		(at 33.655 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f0d03c2d-8705-445a-813c-0e39e50e78c0")
	)
	(pad "13" thru_hole circle
		(at 32.385 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "b17905f0-80cc-4d32-a11f-4ddb5bae924c")
	)
	(pad "14" thru_hole circle
		(at 31.115 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0e1aae9a-0aa6-42a7-9443-339ab8038b5d")
	)
	(pad "15" thru_hole circle
		(at 29.845 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "04a5e2aa-f7c7-45dd-8298-7afef98159ac")
	)
	(pad "16" thru_hole circle
		(at 28.575 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "53edd92a-8c7e-4381-ab33-a22072587cec")
	)
	(pad "17" thru_hole circle
		(at 27.305 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c4b3f908-63dd-4a8d-b67c-cfa2e39b07a6")
	)
	(pad "18" thru_hole circle
		(at 26.035 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6847ee5b-4411-4d69-9b2b-b5a266c4bd34")
	)
	(pad "19" thru_hole circle
		(at 24.765 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "2539c069-863d-424b-be02-239d9daee61b")
	)
	(pad "20" thru_hole circle
		(at 23.495 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "446f0331-a0a5-436b-91f5-14bc1d2db796")
	)
	(pad "21" thru_hole circle
		(at 22.225 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6a89e758-621e-4402-b822-52d2b75230ad")
	)
	(pad "22" thru_hole circle
		(at 20.955 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "ab8a5bef-9d89-4e6e-b756-b4c65cd9942f")
	)
	(pad "23" thru_hole circle
		(at 19.685 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "01c1313d-eda7-4951-8c28-cf1badbcd378")
	)
	(pad "24" thru_hole circle
		(at 18.415 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "24a31c2c-449c-4cc4-a8b3-5cf888d94813")
	)
	(pad "25" thru_hole circle
		(at 17.145 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "e965ac8d-eb38-4659-a7fc-d4225d174ed2")
	)
	(pad "26" thru_hole circle
		(at 15.875 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "39aba99c-95b6-462e-af88-31bbcde91172")
	)
	(pad "27" thru_hole circle
		(at 14.605 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6e3b4eea-54b0-4cb9-b0f7-b66ec27a0be9")
	)
	(pad "28" thru_hole circle
		(at 13.335 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "e0fc47e6-499f-4556-9253-4b7723eaac41")
	)
	(pad "29" thru_hole circle
		(at 12.065 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "ea1f33f5-6b74-4244-80e6-f353ad804c5f")
	)
	(pad "30" thru_hole circle
		(at 10.795 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "667cdb2c-d9f4-4d2e-9173-358af2c58e04")
	)
	(pad "31" thru_hole circle
		(at 9.525 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "3af1983e-f1a7-4dbf-ae18-5543d390e312")
	)
	(pad "32" thru_hole circle
		(at 8.255 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6d4e3c97-9b23-4b27-ada9-e16a3deedb4c")
	)
	(pad "33" thru_hole circle
		(at 6.985 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "8042a7fc-bd83-4ec7-8471-beda6c889f89")
	)
	(pad "34" thru_hole circle
		(at 5.715 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "a7d05b6c-8e3e-485a-b275-b68a9e1e3b67")
	)
	(pad "35" thru_hole circle
		(at 4.445 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "df45b270-136f-4d9c-9148-293832f79812")
	)
	(pad "36" thru_hole circle
		(at 3.175 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "08b05225-ebaa-43d8-adf5-4176ed8df5a9")
	)
	(pad "37" thru_hole circle
		(at -3.175 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "059d708a-2d98-4fe7-8665-c994fe123006")
	)
	(pad "38" thru_hole circle
		(at -4.445 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "860229f9-a22e-4387-bb66-dfd74b995def")
	)
	(pad "39" thru_hole circle
		(at -5.715 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "a1d38970-ab38-41d9-97d3-6edbae12bb1c")
	)
	(pad "40" thru_hole circle
		(at -6.985 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "779f80ab-426f-4e7f-92b1-28855e3a95dc")
	)
	(pad "41" thru_hole circle
		(at -8.255 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7d5fee4f-c8c5-4093-8d8d-76530c674b9a")
	)
	(pad "42" thru_hole circle
		(at -9.525 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "9193b1ed-ca59-4c69-a399-2ab5001c3f8e")
	)
	(pad "43" thru_hole circle
		(at -10.795 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "802722e3-95d5-4329-ac23-9170186ac495")
	)
	(pad "44" thru_hole circle
		(at -12.065 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6b3429d3-f36d-4360-a132-709c14ccb1cb")
	)
	(pad "45" thru_hole circle
		(at -13.335 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "d2288d53-7217-4cfe-ac4e-7381ce956d7b")
	)
	(pad "46" thru_hole circle
		(at -14.605 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "167bd668-5398-4a3a-aea3-39bf30cacd60")
	)
	(pad "47" thru_hole circle
		(at -15.875 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "944b9cab-b56f-4f6b-8a26-b17066604e0b")
	)
	(pad "48" thru_hole circle
		(at -17.145 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "dbcc85bd-1c43-4599-9474-b6396a57edc2")
	)
	(pad "49" thru_hole circle
		(at -18.415 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "958caddf-05b8-4825-8a23-6e853ed62edd")
	)
	(pad "50" thru_hole circle
		(at -19.685 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "89f1a5cd-e799-44e9-9e68-653209afbbc0")
	)
	(pad "51" thru_hole circle
		(at -20.955 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "03c77a20-98b8-487f-bf07-2cb275aaa7c3")
	)
	(pad "52" thru_hole circle
		(at -22.225 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0e5f5012-76d8-4a25-9fc2-0112273f2b14")
	)
	(pad "53" thru_hole circle
		(at -23.495 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "f727f05f-14cb-4bfe-8ff8-b8ceab3527dd")
	)
	(pad "54" thru_hole circle
		(at -24.765 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "a3109561-cea1-4208-9a40-c51794563fd5")
	)
	(pad "55" thru_hole circle
		(at -26.035 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "603f395b-6892-49e8-a747-c3c4f9281be6")
	)
	(pad "56" thru_hole circle
		(at -27.305 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7db6eabb-48f4-44da-8da1-2942c8f8aade")
	)
	(pad "57" thru_hole circle
		(at -28.575 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "a9d72a26-1672-449d-a779-2824ccfaaa77")
	)
	(pad "58" thru_hole circle
		(at -29.845 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "cbe51993-6dbd-4980-a767-a7d8e284b131")
	)
	(pad "59" thru_hole circle
		(at -31.115 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "3c861ae8-f51f-4fd9-9732-193ce84571d2")
	)
	(pad "60" thru_hole circle
		(at -32.385 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "a7b39811-fcd3-40c3-ac0d-2d3e077562bb")
	)
	(pad "61" thru_hole circle
		(at -33.655 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "82338dfa-669e-4b12-b19b-8469cf632604")
	)
	(pad "62" thru_hole circle
		(at -34.925 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "672b9e46-861d-4058-ab24-2160900482e2")
	)
	(pad "63" thru_hole circle
		(at -36.195 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "0655755b-47ca-4811-8326-bc6b69fb9755")
	)
	(pad "64" thru_hole circle
		(at -37.465 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "9cdf0824-8058-4ec0-a50a-9ca77096f858")
	)
	(pad "65" thru_hole circle
		(at -38.735 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6cfeea51-c121-4152-9de3-a894300e37c8")
	)
	(pad "66" thru_hole circle
		(at -40.005 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "3b853d19-99e7-44e2-b35c-fe2a81b7a5a2")
	)
	(pad "67" thru_hole circle
		(at -41.275 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "1bc91f5f-5cd8-40b3-a4bd-1d3dcdf59a76")
	)
	(pad "68" thru_hole circle
		(at -42.545 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "1f99bee6-84c7-4689-8e4a-046337341a25")
	)
	(pad "69" thru_hole circle
		(at -43.815 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "7395e940-59a0-4452-977f-8ceaf5d20b39")
	)
	(pad "70" thru_hole circle
		(at -45.085 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "6e042ecd-64b3-4f67-9a70-5b3559b70747")
	)
	(pad "71" thru_hole circle
		(at -46.355 1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "a8a70bec-a9d9-4529-be76-9d66104b8378")
	)
	(pad "72" thru_hole circle
		(at -47.625 -1.27)
		(size 1.143 1.143)
		(drill 0.635)
		(layers "*.Cu" "*.Mask")
		(remove_unused_layers no)
		(uuid "c24bd94d-834c-4fc7-aa06-b2bbc11e06dc")
	)
	(embedded_fonts no)
)
