# KiCad S-Expression Keyword Management System

## Overview
KiCad uses a sophisticated auto-generated lexical analysis system for parsing S-expression files. The system converts plain text keyword files into optimized C++ lexer classes.

## Architecture

### 1. Keyword Definition Files (.keywords)
Located throughout the codebase, these plain text files define valid keywords:
- `common/pcb.keywords` - PCB file format keywords
- `eeschema/schematic.keywords` - Schematic file format keywords  
- `common/netlist.keywords` - Netlist format keywords
- `common/drc_rules.keywords` - DRC rules keywords
- And many others...

**Format Rules:**
- One keyword per line
- Only lowercase letters, numbers, and underscores allowed
- Must start with lowercase letter
- Comments start with #
- No duplicates allowed

### 2. Code Generation System
**Script**: `cmake/BuildSteps/TokenList2DsnLexer.cmake`
**CMake Functions**: `make_lexer()` and `make_lexer_export()` in `cmake/Functions.cmake`

**Generated Files:**
- `*_lexer.h` - Lexer class header inheriting from DSNLEXER
- `*_keywords.cpp` - Keyword table implementation

### 3. Base Lexer Infrastructure
**Core Class**: `DSNLEXER` in `include/dsnlexer.h`
- Provides S-expression tokenizing
- Hash table lookup for keywords
- Error reporting with line numbers
- Support for strings, numbers, symbols, brackets

### 4. Generated Lexer Classes
Each .keywords file generates a class like `PCB_LEXER`, `SCHEMATIC_LEXER`, etc.

**Features:**
- Inherits from DSNLEXER
- Contains static keyword tables
- Hash map for O(1) keyword lookup
- Token enumeration (T_keyword_name)
- Multiple constructor options (file, string, LINE_READER)

## Usage Pattern

1. **Define Keywords**: Add keywords to appropriate .keywords file
2. **Build Process**: CMake automatically generates lexer classes
3. **Parser Implementation**: Inherit from generated lexer class
4. **Token Processing**: Use switch/case on token enums

## Key Benefits
- **Performance**: Hash-based keyword lookup
- **Type Safety**: Strongly typed token enums  
- **Maintainability**: Keywords defined in simple text files
- **Consistency**: Unified parsing across all file formats
- **Error Handling**: Precise error reporting with context