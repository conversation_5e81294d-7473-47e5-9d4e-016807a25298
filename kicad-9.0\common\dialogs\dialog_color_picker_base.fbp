<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<wxFormBuilder_Project>
  <FileVersion major="1" minor="17"/>
  <object class="Project" expanded="true">
    <property name="class_decoration"></property>
    <property name="code_generation">C++</property>
    <property name="disconnect_events">1</property>
    <property name="disconnect_mode">source_name</property>
    <property name="disconnect_php_events">0</property>
    <property name="disconnect_python_events">0</property>
    <property name="embedded_files_path">res</property>
    <property name="encoding">UTF-8</property>
    <property name="event_generation">connect</property>
    <property name="file">dialog_color_picker_base</property>
    <property name="first_id">1000</property>
    <property name="help_provider">none</property>
    <property name="image_path_wrapper_function_name"></property>
    <property name="indent_with_spaces"></property>
    <property name="internationalize">1</property>
    <property name="name">DIALOG_COLOR_PICKER_BASE</property>
    <property name="namespace"></property>
    <property name="path">.</property>
    <property name="precompiled_header"></property>
    <property name="relative_path">1</property>
    <property name="skip_lua_events">1</property>
    <property name="skip_php_events">1</property>
    <property name="skip_python_events">1</property>
    <property name="ui_table">UI</property>
    <property name="use_array_enum">0</property>
    <property name="use_enum">0</property>
    <property name="use_microsoft_bom">0</property>
    <object class="Dialog" expanded="true">
      <property name="aui_managed">0</property>
      <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
      <property name="bg"></property>
      <property name="center">wxBOTH</property>
      <property name="context_help"></property>
      <property name="context_menu">1</property>
      <property name="drag_accept_files">0</property>
      <property name="enabled">1</property>
      <property name="event_handler">impl_virtual</property>
      <property name="extra_style"></property>
      <property name="fg"></property>
      <property name="font"></property>
      <property name="hidden">0</property>
      <property name="id">wxID_ANY</property>
      <property name="maximum_size"></property>
      <property name="minimum_size"></property>
      <property name="name">DIALOG_COLOR_PICKER_BASE</property>
      <property name="pos"></property>
      <property name="size">-1,-1</property>
      <property name="style">wxDEFAULT_DIALOG_STYLE|wxRESIZE_BORDER</property>
      <property name="subclass">DIALOG_SHIM; dialog_shim.h</property>
      <property name="title">Color Picker</property>
      <property name="tooltip"></property>
      <property name="two_step_creation">0</property>
      <property name="window_extra_style"></property>
      <property name="window_name"></property>
      <property name="window_style"></property>
      <object class="wxBoxSizer" expanded="true">
        <property name="minimum_size"></property>
        <property name="name">bSizerMain</property>
        <property name="orient">wxVERTICAL</property>
        <property name="permission">none</property>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxEXPAND</property>
          <property name="proportion">1</property>
          <object class="wxBoxSizer" expanded="true">
            <property name="minimum_size"></property>
            <property name="name">bSizerUpperMain</property>
            <property name="orient">wxHORIZONTAL</property>
            <property name="permission">none</property>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND | wxALL</property>
              <property name="proportion">1</property>
              <object class="wxNotebook" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmapsize"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_notebook</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass"></property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <object class="notebookpage" expanded="true">
                  <property name="bitmap"></property>
                  <property name="label">Color Picker</property>
                  <property name="select">1</property>
                  <object class="wxPanel" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer"></property>
                    <property name="aui_name"></property>
                    <property name="aui_position"></property>
                    <property name="aui_row"></property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_panelFreeColors</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="subclass"></property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style">wxTAB_TRAVERSAL</property>
                    <object class="wxBoxSizer" expanded="true">
                      <property name="minimum_size"></property>
                      <property name="name">bSizerUpperFreeColors</property>
                      <property name="orient">wxVERTICAL</property>
                      <property name="permission">none</property>
                      <object class="sizeritem" expanded="true">
                        <property name="border">5</property>
                        <property name="flag">wxEXPAND</property>
                        <property name="proportion">1</property>
                        <object class="wxBoxSizer" expanded="true">
                          <property name="minimum_size"></property>
                          <property name="name">bSizerPanels</property>
                          <property name="orient">wxHORIZONTAL</property>
                          <property name="permission">none</property>
                          <object class="sizeritem" expanded="true">
                            <property name="border">5</property>
                            <property name="flag">wxBOTTOM|wxEXPAND|wxLEFT|wxRIGHT</property>
                            <property name="proportion">1</property>
                            <object class="wxStaticBoxSizer" expanded="true">
                              <property name="id">wxID_ANY</property>
                              <property name="label">RGB</property>
                              <property name="minimum_size">-1,-1</property>
                              <property name="name">sbSizerViewRGB</property>
                              <property name="orient">wxVERTICAL</property>
                              <property name="parent">1</property>
                              <property name="permission">none</property>
                              <object class="sizeritem" expanded="false">
                                <property name="border">5</property>
                                <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxALL|wxEXPAND|wxSHAPED</property>
                                <property name="proportion">0</property>
                                <object class="wxStaticBitmap" expanded="false">
                                  <property name="BottomDockable">1</property>
                                  <property name="LeftDockable">1</property>
                                  <property name="RightDockable">1</property>
                                  <property name="TopDockable">1</property>
                                  <property name="aui_layer"></property>
                                  <property name="aui_name"></property>
                                  <property name="aui_position"></property>
                                  <property name="aui_row"></property>
                                  <property name="best_size"></property>
                                  <property name="bg"></property>
                                  <property name="bitmap"></property>
                                  <property name="caption"></property>
                                  <property name="caption_visible">1</property>
                                  <property name="center_pane">0</property>
                                  <property name="close_button">1</property>
                                  <property name="context_help"></property>
                                  <property name="context_menu">1</property>
                                  <property name="default_pane">0</property>
                                  <property name="dock">Dock</property>
                                  <property name="dock_fixed">0</property>
                                  <property name="docking">Left</property>
                                  <property name="drag_accept_files">0</property>
                                  <property name="enabled">1</property>
                                  <property name="fg"></property>
                                  <property name="floatable">1</property>
                                  <property name="font"></property>
                                  <property name="gripper">0</property>
                                  <property name="hidden">0</property>
                                  <property name="id">wxID_ANY</property>
                                  <property name="max_size"></property>
                                  <property name="maximize_button">0</property>
                                  <property name="maximum_size">-1,-1</property>
                                  <property name="min_size"></property>
                                  <property name="minimize_button">0</property>
                                  <property name="minimum_size">264,264</property>
                                  <property name="moveable">1</property>
                                  <property name="name">m_RgbBitmap</property>
                                  <property name="pane_border">1</property>
                                  <property name="pane_position"></property>
                                  <property name="pane_size"></property>
                                  <property name="permission">protected</property>
                                  <property name="pin_button">1</property>
                                  <property name="pos"></property>
                                  <property name="resize">Resizable</property>
                                  <property name="show">1</property>
                                  <property name="size">264,264</property>
                                  <property name="subclass"></property>
                                  <property name="toolbar_pane">0</property>
                                  <property name="tooltip"></property>
                                  <property name="window_extra_style"></property>
                                  <property name="window_name"></property>
                                  <property name="window_style"></property>
                                  <event name="OnLeftDown">onRGBMouseClick</event>
                                  <event name="OnMotion">onRGBMouseDrag</event>
                                </object>
                              </object>
                              <object class="sizeritem" expanded="true">
                                <property name="border">5</property>
                                <property name="flag">wxEXPAND</property>
                                <property name="proportion">1</property>
                                <object class="spacer" expanded="true">
                                  <property name="height">0</property>
                                  <property name="permission">protected</property>
                                  <property name="width">0</property>
                                </object>
                              </object>
                              <object class="sizeritem" expanded="true">
                                <property name="border">5</property>
                                <property name="flag">wxEXPAND</property>
                                <property name="proportion">0</property>
                                <object class="wxFlexGridSizer" expanded="true">
                                  <property name="cols">3</property>
                                  <property name="flexible_direction">wxBOTH</property>
                                  <property name="growablecols">0,1,2</property>
                                  <property name="growablerows"></property>
                                  <property name="hgap">0</property>
                                  <property name="minimum_size"></property>
                                  <property name="name">fgSizerRGB</property>
                                  <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                                  <property name="permission">none</property>
                                  <property name="rows">0</property>
                                  <property name="vgap">0</property>
                                  <object class="sizeritem" expanded="false">
                                    <property name="border">5</property>
                                    <property name="flag">wxTOP|wxRIGHT|wxLEFT</property>
                                    <property name="proportion">0</property>
                                    <object class="wxStaticText" expanded="false">
                                      <property name="BottomDockable">1</property>
                                      <property name="LeftDockable">1</property>
                                      <property name="RightDockable">1</property>
                                      <property name="TopDockable">1</property>
                                      <property name="aui_layer"></property>
                                      <property name="aui_name"></property>
                                      <property name="aui_position"></property>
                                      <property name="aui_row"></property>
                                      <property name="best_size"></property>
                                      <property name="bg"></property>
                                      <property name="caption"></property>
                                      <property name="caption_visible">1</property>
                                      <property name="center_pane">0</property>
                                      <property name="close_button">1</property>
                                      <property name="context_help"></property>
                                      <property name="context_menu">1</property>
                                      <property name="default_pane">0</property>
                                      <property name="dock">Dock</property>
                                      <property name="dock_fixed">0</property>
                                      <property name="docking">Left</property>
                                      <property name="drag_accept_files">0</property>
                                      <property name="enabled">1</property>
                                      <property name="fg"></property>
                                      <property name="floatable">1</property>
                                      <property name="font"></property>
                                      <property name="gripper">0</property>
                                      <property name="hidden">0</property>
                                      <property name="id">wxID_ANY</property>
                                      <property name="label">Red:</property>
                                      <property name="markup">0</property>
                                      <property name="max_size"></property>
                                      <property name="maximize_button">0</property>
                                      <property name="maximum_size"></property>
                                      <property name="min_size"></property>
                                      <property name="minimize_button">0</property>
                                      <property name="minimum_size"></property>
                                      <property name="moveable">1</property>
                                      <property name="name">m_staticTextR</property>
                                      <property name="pane_border">1</property>
                                      <property name="pane_position"></property>
                                      <property name="pane_size"></property>
                                      <property name="permission">protected</property>
                                      <property name="pin_button">1</property>
                                      <property name="pos"></property>
                                      <property name="resize">Resizable</property>
                                      <property name="show">1</property>
                                      <property name="size"></property>
                                      <property name="style"></property>
                                      <property name="subclass"></property>
                                      <property name="toolbar_pane">0</property>
                                      <property name="tooltip"></property>
                                      <property name="window_extra_style"></property>
                                      <property name="window_name"></property>
                                      <property name="window_style"></property>
                                      <property name="wrap">-1</property>
                                    </object>
                                  </object>
                                  <object class="sizeritem" expanded="false">
                                    <property name="border">5</property>
                                    <property name="flag">wxTOP|wxRIGHT|wxLEFT</property>
                                    <property name="proportion">0</property>
                                    <object class="wxStaticText" expanded="false">
                                      <property name="BottomDockable">1</property>
                                      <property name="LeftDockable">1</property>
                                      <property name="RightDockable">1</property>
                                      <property name="TopDockable">1</property>
                                      <property name="aui_layer"></property>
                                      <property name="aui_name"></property>
                                      <property name="aui_position"></property>
                                      <property name="aui_row"></property>
                                      <property name="best_size"></property>
                                      <property name="bg"></property>
                                      <property name="caption"></property>
                                      <property name="caption_visible">1</property>
                                      <property name="center_pane">0</property>
                                      <property name="close_button">1</property>
                                      <property name="context_help"></property>
                                      <property name="context_menu">1</property>
                                      <property name="default_pane">0</property>
                                      <property name="dock">Dock</property>
                                      <property name="dock_fixed">0</property>
                                      <property name="docking">Left</property>
                                      <property name="drag_accept_files">0</property>
                                      <property name="enabled">1</property>
                                      <property name="fg"></property>
                                      <property name="floatable">1</property>
                                      <property name="font"></property>
                                      <property name="gripper">0</property>
                                      <property name="hidden">0</property>
                                      <property name="id">wxID_ANY</property>
                                      <property name="label">Green:</property>
                                      <property name="markup">0</property>
                                      <property name="max_size"></property>
                                      <property name="maximize_button">0</property>
                                      <property name="maximum_size"></property>
                                      <property name="min_size"></property>
                                      <property name="minimize_button">0</property>
                                      <property name="minimum_size"></property>
                                      <property name="moveable">1</property>
                                      <property name="name">m_staticTextG</property>
                                      <property name="pane_border">1</property>
                                      <property name="pane_position"></property>
                                      <property name="pane_size"></property>
                                      <property name="permission">protected</property>
                                      <property name="pin_button">1</property>
                                      <property name="pos"></property>
                                      <property name="resize">Resizable</property>
                                      <property name="show">1</property>
                                      <property name="size"></property>
                                      <property name="style"></property>
                                      <property name="subclass"></property>
                                      <property name="toolbar_pane">0</property>
                                      <property name="tooltip"></property>
                                      <property name="window_extra_style"></property>
                                      <property name="window_name"></property>
                                      <property name="window_style"></property>
                                      <property name="wrap">-1</property>
                                    </object>
                                  </object>
                                  <object class="sizeritem" expanded="false">
                                    <property name="border">5</property>
                                    <property name="flag">wxTOP|wxRIGHT|wxLEFT</property>
                                    <property name="proportion">0</property>
                                    <object class="wxStaticText" expanded="false">
                                      <property name="BottomDockable">1</property>
                                      <property name="LeftDockable">1</property>
                                      <property name="RightDockable">1</property>
                                      <property name="TopDockable">1</property>
                                      <property name="aui_layer"></property>
                                      <property name="aui_name"></property>
                                      <property name="aui_position"></property>
                                      <property name="aui_row"></property>
                                      <property name="best_size"></property>
                                      <property name="bg"></property>
                                      <property name="caption"></property>
                                      <property name="caption_visible">1</property>
                                      <property name="center_pane">0</property>
                                      <property name="close_button">1</property>
                                      <property name="context_help"></property>
                                      <property name="context_menu">1</property>
                                      <property name="default_pane">0</property>
                                      <property name="dock">Dock</property>
                                      <property name="dock_fixed">0</property>
                                      <property name="docking">Left</property>
                                      <property name="drag_accept_files">0</property>
                                      <property name="enabled">1</property>
                                      <property name="fg"></property>
                                      <property name="floatable">1</property>
                                      <property name="font"></property>
                                      <property name="gripper">0</property>
                                      <property name="hidden">0</property>
                                      <property name="id">wxID_ANY</property>
                                      <property name="label">Blue:</property>
                                      <property name="markup">0</property>
                                      <property name="max_size"></property>
                                      <property name="maximize_button">0</property>
                                      <property name="maximum_size"></property>
                                      <property name="min_size"></property>
                                      <property name="minimize_button">0</property>
                                      <property name="minimum_size"></property>
                                      <property name="moveable">1</property>
                                      <property name="name">m_staticTextB</property>
                                      <property name="pane_border">1</property>
                                      <property name="pane_position"></property>
                                      <property name="pane_size"></property>
                                      <property name="permission">protected</property>
                                      <property name="pin_button">1</property>
                                      <property name="pos"></property>
                                      <property name="resize">Resizable</property>
                                      <property name="show">1</property>
                                      <property name="size"></property>
                                      <property name="style"></property>
                                      <property name="subclass"></property>
                                      <property name="toolbar_pane">0</property>
                                      <property name="tooltip"></property>
                                      <property name="window_extra_style"></property>
                                      <property name="window_name"></property>
                                      <property name="window_style"></property>
                                      <property name="wrap">-1</property>
                                    </object>
                                  </object>
                                  <object class="sizeritem" expanded="false">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND|wxBOTTOM|wxRIGHT|wxLEFT</property>
                                    <property name="proportion">0</property>
                                    <object class="wxSpinCtrl" expanded="false">
                                      <property name="BottomDockable">1</property>
                                      <property name="LeftDockable">1</property>
                                      <property name="RightDockable">1</property>
                                      <property name="TopDockable">1</property>
                                      <property name="aui_layer"></property>
                                      <property name="aui_name"></property>
                                      <property name="aui_position"></property>
                                      <property name="aui_row"></property>
                                      <property name="best_size"></property>
                                      <property name="bg"></property>
                                      <property name="caption"></property>
                                      <property name="caption_visible">1</property>
                                      <property name="center_pane">0</property>
                                      <property name="close_button">1</property>
                                      <property name="context_help"></property>
                                      <property name="context_menu">1</property>
                                      <property name="default_pane">0</property>
                                      <property name="dock">Dock</property>
                                      <property name="dock_fixed">0</property>
                                      <property name="docking">Left</property>
                                      <property name="drag_accept_files">0</property>
                                      <property name="enabled">1</property>
                                      <property name="fg"></property>
                                      <property name="floatable">1</property>
                                      <property name="font"></property>
                                      <property name="gripper">0</property>
                                      <property name="hidden">0</property>
                                      <property name="id">wxID_ANY</property>
                                      <property name="initial">128</property>
                                      <property name="max">255</property>
                                      <property name="max_size"></property>
                                      <property name="maximize_button">0</property>
                                      <property name="maximum_size"></property>
                                      <property name="min">0</property>
                                      <property name="min_size"></property>
                                      <property name="minimize_button">0</property>
                                      <property name="minimum_size">-1,-1</property>
                                      <property name="moveable">1</property>
                                      <property name="name">m_spinCtrlRed</property>
                                      <property name="pane_border">1</property>
                                      <property name="pane_position"></property>
                                      <property name="pane_size"></property>
                                      <property name="permission">protected</property>
                                      <property name="pin_button">1</property>
                                      <property name="pos"></property>
                                      <property name="resize">Resizable</property>
                                      <property name="show">1</property>
                                      <property name="size"></property>
                                      <property name="style">wxSP_ARROW_KEYS</property>
                                      <property name="subclass"></property>
                                      <property name="toolbar_pane">0</property>
                                      <property name="tooltip"></property>
                                      <property name="value"></property>
                                      <property name="window_extra_style"></property>
                                      <property name="window_name"></property>
                                      <property name="window_style"></property>
                                      <event name="OnSpinCtrl">OnChangeEditRed</event>
                                    </object>
                                  </object>
                                  <object class="sizeritem" expanded="false">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND|wxBOTTOM|wxRIGHT|wxLEFT</property>
                                    <property name="proportion">0</property>
                                    <object class="wxSpinCtrl" expanded="false">
                                      <property name="BottomDockable">1</property>
                                      <property name="LeftDockable">1</property>
                                      <property name="RightDockable">1</property>
                                      <property name="TopDockable">1</property>
                                      <property name="aui_layer"></property>
                                      <property name="aui_name"></property>
                                      <property name="aui_position"></property>
                                      <property name="aui_row"></property>
                                      <property name="best_size"></property>
                                      <property name="bg"></property>
                                      <property name="caption"></property>
                                      <property name="caption_visible">1</property>
                                      <property name="center_pane">0</property>
                                      <property name="close_button">1</property>
                                      <property name="context_help"></property>
                                      <property name="context_menu">1</property>
                                      <property name="default_pane">0</property>
                                      <property name="dock">Dock</property>
                                      <property name="dock_fixed">0</property>
                                      <property name="docking">Left</property>
                                      <property name="drag_accept_files">0</property>
                                      <property name="enabled">1</property>
                                      <property name="fg"></property>
                                      <property name="floatable">1</property>
                                      <property name="font"></property>
                                      <property name="gripper">0</property>
                                      <property name="hidden">0</property>
                                      <property name="id">wxID_ANY</property>
                                      <property name="initial">128</property>
                                      <property name="max">255</property>
                                      <property name="max_size"></property>
                                      <property name="maximize_button">0</property>
                                      <property name="maximum_size"></property>
                                      <property name="min">0</property>
                                      <property name="min_size"></property>
                                      <property name="minimize_button">0</property>
                                      <property name="minimum_size">-1,-1</property>
                                      <property name="moveable">1</property>
                                      <property name="name">m_spinCtrlGreen</property>
                                      <property name="pane_border">1</property>
                                      <property name="pane_position"></property>
                                      <property name="pane_size"></property>
                                      <property name="permission">protected</property>
                                      <property name="pin_button">1</property>
                                      <property name="pos"></property>
                                      <property name="resize">Resizable</property>
                                      <property name="show">1</property>
                                      <property name="size"></property>
                                      <property name="style">wxSP_ARROW_KEYS</property>
                                      <property name="subclass"></property>
                                      <property name="toolbar_pane">0</property>
                                      <property name="tooltip"></property>
                                      <property name="value"></property>
                                      <property name="window_extra_style"></property>
                                      <property name="window_name"></property>
                                      <property name="window_style"></property>
                                      <event name="OnSpinCtrl">OnChangeEditGreen</event>
                                    </object>
                                  </object>
                                  <object class="sizeritem" expanded="false">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND|wxBOTTOM|wxRIGHT|wxLEFT</property>
                                    <property name="proportion">0</property>
                                    <object class="wxSpinCtrl" expanded="false">
                                      <property name="BottomDockable">1</property>
                                      <property name="LeftDockable">1</property>
                                      <property name="RightDockable">1</property>
                                      <property name="TopDockable">1</property>
                                      <property name="aui_layer"></property>
                                      <property name="aui_name"></property>
                                      <property name="aui_position"></property>
                                      <property name="aui_row"></property>
                                      <property name="best_size"></property>
                                      <property name="bg"></property>
                                      <property name="caption"></property>
                                      <property name="caption_visible">1</property>
                                      <property name="center_pane">0</property>
                                      <property name="close_button">1</property>
                                      <property name="context_help"></property>
                                      <property name="context_menu">1</property>
                                      <property name="default_pane">0</property>
                                      <property name="dock">Dock</property>
                                      <property name="dock_fixed">0</property>
                                      <property name="docking">Left</property>
                                      <property name="drag_accept_files">0</property>
                                      <property name="enabled">1</property>
                                      <property name="fg"></property>
                                      <property name="floatable">1</property>
                                      <property name="font"></property>
                                      <property name="gripper">0</property>
                                      <property name="hidden">0</property>
                                      <property name="id">wxID_ANY</property>
                                      <property name="initial">128</property>
                                      <property name="max">255</property>
                                      <property name="max_size"></property>
                                      <property name="maximize_button">0</property>
                                      <property name="maximum_size"></property>
                                      <property name="min">0</property>
                                      <property name="min_size"></property>
                                      <property name="minimize_button">0</property>
                                      <property name="minimum_size">-1,-1</property>
                                      <property name="moveable">1</property>
                                      <property name="name">m_spinCtrlBlue</property>
                                      <property name="pane_border">1</property>
                                      <property name="pane_position"></property>
                                      <property name="pane_size"></property>
                                      <property name="permission">protected</property>
                                      <property name="pin_button">1</property>
                                      <property name="pos"></property>
                                      <property name="resize">Resizable</property>
                                      <property name="show">1</property>
                                      <property name="size"></property>
                                      <property name="style">wxSP_ARROW_KEYS</property>
                                      <property name="subclass"></property>
                                      <property name="toolbar_pane">0</property>
                                      <property name="tooltip"></property>
                                      <property name="value"></property>
                                      <property name="window_extra_style"></property>
                                      <property name="window_name"></property>
                                      <property name="window_style"></property>
                                      <event name="OnSpinCtrl">OnChangeEditBlue</event>
                                    </object>
                                  </object>
                                </object>
                              </object>
                            </object>
                          </object>
                          <object class="sizeritem" expanded="true">
                            <property name="border">5</property>
                            <property name="flag">wxEXPAND|wxBOTTOM|wxRIGHT|wxLEFT</property>
                            <property name="proportion">1</property>
                            <object class="wxStaticBoxSizer" expanded="true">
                              <property name="id">wxID_ANY</property>
                              <property name="label">HSV</property>
                              <property name="minimum_size"></property>
                              <property name="name">sbSizerViewHSV</property>
                              <property name="orient">wxVERTICAL</property>
                              <property name="parent">1</property>
                              <property name="permission">none</property>
                              <object class="sizeritem" expanded="false">
                                <property name="border">5</property>
                                <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxALL|wxEXPAND|wxSHAPED</property>
                                <property name="proportion">0</property>
                                <object class="wxStaticBitmap" expanded="false">
                                  <property name="BottomDockable">1</property>
                                  <property name="LeftDockable">1</property>
                                  <property name="RightDockable">1</property>
                                  <property name="TopDockable">1</property>
                                  <property name="aui_layer"></property>
                                  <property name="aui_name"></property>
                                  <property name="aui_position"></property>
                                  <property name="aui_row"></property>
                                  <property name="best_size"></property>
                                  <property name="bg"></property>
                                  <property name="bitmap"></property>
                                  <property name="caption"></property>
                                  <property name="caption_visible">1</property>
                                  <property name="center_pane">0</property>
                                  <property name="close_button">1</property>
                                  <property name="context_help"></property>
                                  <property name="context_menu">1</property>
                                  <property name="default_pane">0</property>
                                  <property name="dock">Dock</property>
                                  <property name="dock_fixed">0</property>
                                  <property name="docking">Left</property>
                                  <property name="drag_accept_files">0</property>
                                  <property name="enabled">1</property>
                                  <property name="fg"></property>
                                  <property name="floatable">1</property>
                                  <property name="font"></property>
                                  <property name="gripper">0</property>
                                  <property name="hidden">0</property>
                                  <property name="id">wxID_ANY</property>
                                  <property name="max_size"></property>
                                  <property name="maximize_button">0</property>
                                  <property name="maximum_size">-1,-1</property>
                                  <property name="min_size"></property>
                                  <property name="minimize_button">0</property>
                                  <property name="minimum_size">264,264</property>
                                  <property name="moveable">1</property>
                                  <property name="name">m_HsvBitmap</property>
                                  <property name="pane_border">1</property>
                                  <property name="pane_position"></property>
                                  <property name="pane_size"></property>
                                  <property name="permission">protected</property>
                                  <property name="pin_button">1</property>
                                  <property name="pos"></property>
                                  <property name="resize">Resizable</property>
                                  <property name="show">1</property>
                                  <property name="size">264,264</property>
                                  <property name="subclass"></property>
                                  <property name="toolbar_pane">0</property>
                                  <property name="tooltip"></property>
                                  <property name="window_extra_style"></property>
                                  <property name="window_name"></property>
                                  <property name="window_style"></property>
                                  <event name="OnLeftDown">onHSVMouseClick</event>
                                  <event name="OnMotion">onHSVMouseDrag</event>
                                  <event name="OnSize">onSize</event>
                                </object>
                              </object>
                              <object class="sizeritem" expanded="true">
                                <property name="border">5</property>
                                <property name="flag">wxEXPAND</property>
                                <property name="proportion">1</property>
                                <object class="spacer" expanded="true">
                                  <property name="height">0</property>
                                  <property name="permission">protected</property>
                                  <property name="width">0</property>
                                </object>
                              </object>
                              <object class="sizeritem" expanded="false">
                                <property name="border">5</property>
                                <property name="flag">wxEXPAND</property>
                                <property name="proportion">0</property>
                                <object class="wxFlexGridSizer" expanded="false">
                                  <property name="cols">2</property>
                                  <property name="flexible_direction">wxBOTH</property>
                                  <property name="growablecols">0,1</property>
                                  <property name="growablerows"></property>
                                  <property name="hgap">0</property>
                                  <property name="minimum_size"></property>
                                  <property name="name">fgSizerHSV</property>
                                  <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                                  <property name="permission">none</property>
                                  <property name="rows">0</property>
                                  <property name="vgap">0</property>
                                  <object class="sizeritem" expanded="false">
                                    <property name="border">5</property>
                                    <property name="flag">wxTOP|wxRIGHT|wxLEFT</property>
                                    <property name="proportion">0</property>
                                    <object class="wxStaticText" expanded="false">
                                      <property name="BottomDockable">1</property>
                                      <property name="LeftDockable">1</property>
                                      <property name="RightDockable">1</property>
                                      <property name="TopDockable">1</property>
                                      <property name="aui_layer"></property>
                                      <property name="aui_name"></property>
                                      <property name="aui_position"></property>
                                      <property name="aui_row"></property>
                                      <property name="best_size"></property>
                                      <property name="bg"></property>
                                      <property name="caption"></property>
                                      <property name="caption_visible">1</property>
                                      <property name="center_pane">0</property>
                                      <property name="close_button">1</property>
                                      <property name="context_help"></property>
                                      <property name="context_menu">1</property>
                                      <property name="default_pane">0</property>
                                      <property name="dock">Dock</property>
                                      <property name="dock_fixed">0</property>
                                      <property name="docking">Left</property>
                                      <property name="drag_accept_files">0</property>
                                      <property name="enabled">1</property>
                                      <property name="fg"></property>
                                      <property name="floatable">1</property>
                                      <property name="font"></property>
                                      <property name="gripper">0</property>
                                      <property name="hidden">0</property>
                                      <property name="id">wxID_ANY</property>
                                      <property name="label">Hue:</property>
                                      <property name="markup">0</property>
                                      <property name="max_size"></property>
                                      <property name="maximize_button">0</property>
                                      <property name="maximum_size"></property>
                                      <property name="min_size"></property>
                                      <property name="minimize_button">0</property>
                                      <property name="minimum_size"></property>
                                      <property name="moveable">1</property>
                                      <property name="name">m_staticTextHue</property>
                                      <property name="pane_border">1</property>
                                      <property name="pane_position"></property>
                                      <property name="pane_size"></property>
                                      <property name="permission">protected</property>
                                      <property name="pin_button">1</property>
                                      <property name="pos"></property>
                                      <property name="resize">Resizable</property>
                                      <property name="show">1</property>
                                      <property name="size"></property>
                                      <property name="style"></property>
                                      <property name="subclass"></property>
                                      <property name="toolbar_pane">0</property>
                                      <property name="tooltip"></property>
                                      <property name="window_extra_style"></property>
                                      <property name="window_name"></property>
                                      <property name="window_style"></property>
                                      <property name="wrap">-1</property>
                                    </object>
                                  </object>
                                  <object class="sizeritem" expanded="false">
                                    <property name="border">5</property>
                                    <property name="flag">wxTOP|wxRIGHT|wxLEFT</property>
                                    <property name="proportion">0</property>
                                    <object class="wxStaticText" expanded="false">
                                      <property name="BottomDockable">1</property>
                                      <property name="LeftDockable">1</property>
                                      <property name="RightDockable">1</property>
                                      <property name="TopDockable">1</property>
                                      <property name="aui_layer"></property>
                                      <property name="aui_name"></property>
                                      <property name="aui_position"></property>
                                      <property name="aui_row"></property>
                                      <property name="best_size"></property>
                                      <property name="bg"></property>
                                      <property name="caption"></property>
                                      <property name="caption_visible">1</property>
                                      <property name="center_pane">0</property>
                                      <property name="close_button">1</property>
                                      <property name="context_help"></property>
                                      <property name="context_menu">1</property>
                                      <property name="default_pane">0</property>
                                      <property name="dock">Dock</property>
                                      <property name="dock_fixed">0</property>
                                      <property name="docking">Left</property>
                                      <property name="drag_accept_files">0</property>
                                      <property name="enabled">1</property>
                                      <property name="fg"></property>
                                      <property name="floatable">1</property>
                                      <property name="font"></property>
                                      <property name="gripper">0</property>
                                      <property name="hidden">0</property>
                                      <property name="id">wxID_ANY</property>
                                      <property name="label">Saturation:</property>
                                      <property name="markup">0</property>
                                      <property name="max_size"></property>
                                      <property name="maximize_button">0</property>
                                      <property name="maximum_size"></property>
                                      <property name="min_size"></property>
                                      <property name="minimize_button">0</property>
                                      <property name="minimum_size"></property>
                                      <property name="moveable">1</property>
                                      <property name="name">m_staticTextSat</property>
                                      <property name="pane_border">1</property>
                                      <property name="pane_position"></property>
                                      <property name="pane_size"></property>
                                      <property name="permission">protected</property>
                                      <property name="pin_button">1</property>
                                      <property name="pos"></property>
                                      <property name="resize">Resizable</property>
                                      <property name="show">1</property>
                                      <property name="size"></property>
                                      <property name="style"></property>
                                      <property name="subclass"></property>
                                      <property name="toolbar_pane">0</property>
                                      <property name="tooltip"></property>
                                      <property name="window_extra_style"></property>
                                      <property name="window_name"></property>
                                      <property name="window_style"></property>
                                      <property name="wrap">-1</property>
                                    </object>
                                  </object>
                                  <object class="sizeritem" expanded="false">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND|wxBOTTOM|wxRIGHT|wxLEFT</property>
                                    <property name="proportion">0</property>
                                    <object class="wxSpinCtrl" expanded="false">
                                      <property name="BottomDockable">1</property>
                                      <property name="LeftDockable">1</property>
                                      <property name="RightDockable">1</property>
                                      <property name="TopDockable">1</property>
                                      <property name="aui_layer"></property>
                                      <property name="aui_name"></property>
                                      <property name="aui_position"></property>
                                      <property name="aui_row"></property>
                                      <property name="best_size"></property>
                                      <property name="bg"></property>
                                      <property name="caption"></property>
                                      <property name="caption_visible">1</property>
                                      <property name="center_pane">0</property>
                                      <property name="close_button">1</property>
                                      <property name="context_help"></property>
                                      <property name="context_menu">1</property>
                                      <property name="default_pane">0</property>
                                      <property name="dock">Dock</property>
                                      <property name="dock_fixed">0</property>
                                      <property name="docking">Left</property>
                                      <property name="drag_accept_files">0</property>
                                      <property name="enabled">1</property>
                                      <property name="fg"></property>
                                      <property name="floatable">1</property>
                                      <property name="font"></property>
                                      <property name="gripper">0</property>
                                      <property name="hidden">0</property>
                                      <property name="id">wxID_ANY</property>
                                      <property name="initial">0</property>
                                      <property name="max">359</property>
                                      <property name="max_size"></property>
                                      <property name="maximize_button">0</property>
                                      <property name="maximum_size"></property>
                                      <property name="min">0</property>
                                      <property name="min_size"></property>
                                      <property name="minimize_button">0</property>
                                      <property name="minimum_size">-1,-1</property>
                                      <property name="moveable">1</property>
                                      <property name="name">m_spinCtrlHue</property>
                                      <property name="pane_border">1</property>
                                      <property name="pane_position"></property>
                                      <property name="pane_size"></property>
                                      <property name="permission">protected</property>
                                      <property name="pin_button">1</property>
                                      <property name="pos"></property>
                                      <property name="resize">Resizable</property>
                                      <property name="show">1</property>
                                      <property name="size"></property>
                                      <property name="style">wxSP_ARROW_KEYS|wxSP_WRAP</property>
                                      <property name="subclass"></property>
                                      <property name="toolbar_pane">0</property>
                                      <property name="tooltip"></property>
                                      <property name="value"></property>
                                      <property name="window_extra_style"></property>
                                      <property name="window_name"></property>
                                      <property name="window_style"></property>
                                      <event name="OnSpinCtrl">OnChangeEditHue</event>
                                    </object>
                                  </object>
                                  <object class="sizeritem" expanded="false">
                                    <property name="border">5</property>
                                    <property name="flag">wxEXPAND|wxBOTTOM|wxRIGHT|wxLEFT</property>
                                    <property name="proportion">0</property>
                                    <object class="wxSpinCtrl" expanded="false">
                                      <property name="BottomDockable">1</property>
                                      <property name="LeftDockable">1</property>
                                      <property name="RightDockable">1</property>
                                      <property name="TopDockable">1</property>
                                      <property name="aui_layer"></property>
                                      <property name="aui_name"></property>
                                      <property name="aui_position"></property>
                                      <property name="aui_row"></property>
                                      <property name="best_size"></property>
                                      <property name="bg"></property>
                                      <property name="caption"></property>
                                      <property name="caption_visible">1</property>
                                      <property name="center_pane">0</property>
                                      <property name="close_button">1</property>
                                      <property name="context_help"></property>
                                      <property name="context_menu">1</property>
                                      <property name="default_pane">0</property>
                                      <property name="dock">Dock</property>
                                      <property name="dock_fixed">0</property>
                                      <property name="docking">Left</property>
                                      <property name="drag_accept_files">0</property>
                                      <property name="enabled">1</property>
                                      <property name="fg"></property>
                                      <property name="floatable">1</property>
                                      <property name="font"></property>
                                      <property name="gripper">0</property>
                                      <property name="hidden">0</property>
                                      <property name="id">wxID_ANY</property>
                                      <property name="initial">128</property>
                                      <property name="max">255</property>
                                      <property name="max_size"></property>
                                      <property name="maximize_button">0</property>
                                      <property name="maximum_size"></property>
                                      <property name="min">0</property>
                                      <property name="min_size"></property>
                                      <property name="minimize_button">0</property>
                                      <property name="minimum_size">-1,-1</property>
                                      <property name="moveable">1</property>
                                      <property name="name">m_spinCtrlSaturation</property>
                                      <property name="pane_border">1</property>
                                      <property name="pane_position"></property>
                                      <property name="pane_size"></property>
                                      <property name="permission">protected</property>
                                      <property name="pin_button">1</property>
                                      <property name="pos"></property>
                                      <property name="resize">Resizable</property>
                                      <property name="show">1</property>
                                      <property name="size"></property>
                                      <property name="style">wxSP_ARROW_KEYS</property>
                                      <property name="subclass"></property>
                                      <property name="toolbar_pane">0</property>
                                      <property name="tooltip"></property>
                                      <property name="value"></property>
                                      <property name="window_extra_style"></property>
                                      <property name="window_name"></property>
                                      <property name="window_style"></property>
                                      <event name="OnSpinCtrl">OnChangeEditSat</event>
                                    </object>
                                  </object>
                                </object>
                              </object>
                            </object>
                          </object>
                          <object class="sizeritem" expanded="false">
                            <property name="border">5</property>
                            <property name="flag">wxALL|wxEXPAND</property>
                            <property name="proportion">0</property>
                            <object class="wxBoxSizer" expanded="false">
                              <property name="minimum_size"></property>
                              <property name="name">bSizerBright</property>
                              <property name="orient">wxVERTICAL</property>
                              <property name="permission">none</property>
                              <object class="sizeritem" expanded="false">
                                <property name="border">5</property>
                                <property name="flag">wxALL|wxALIGN_CENTER_HORIZONTAL</property>
                                <property name="proportion">0</property>
                                <object class="wxStaticText" expanded="false">
                                  <property name="BottomDockable">1</property>
                                  <property name="LeftDockable">1</property>
                                  <property name="RightDockable">1</property>
                                  <property name="TopDockable">1</property>
                                  <property name="aui_layer"></property>
                                  <property name="aui_name"></property>
                                  <property name="aui_position"></property>
                                  <property name="aui_row"></property>
                                  <property name="best_size"></property>
                                  <property name="bg"></property>
                                  <property name="caption"></property>
                                  <property name="caption_visible">1</property>
                                  <property name="center_pane">0</property>
                                  <property name="close_button">1</property>
                                  <property name="context_help"></property>
                                  <property name="context_menu">1</property>
                                  <property name="default_pane">0</property>
                                  <property name="dock">Dock</property>
                                  <property name="dock_fixed">0</property>
                                  <property name="docking">Left</property>
                                  <property name="drag_accept_files">0</property>
                                  <property name="enabled">1</property>
                                  <property name="fg"></property>
                                  <property name="floatable">1</property>
                                  <property name="font"></property>
                                  <property name="gripper">0</property>
                                  <property name="hidden">0</property>
                                  <property name="id">wxID_ANY</property>
                                  <property name="label">Value:</property>
                                  <property name="markup">0</property>
                                  <property name="max_size"></property>
                                  <property name="maximize_button">0</property>
                                  <property name="maximum_size"></property>
                                  <property name="min_size"></property>
                                  <property name="minimize_button">0</property>
                                  <property name="minimum_size"></property>
                                  <property name="moveable">1</property>
                                  <property name="name">m_staticTextBright</property>
                                  <property name="pane_border">1</property>
                                  <property name="pane_position"></property>
                                  <property name="pane_size"></property>
                                  <property name="permission">protected</property>
                                  <property name="pin_button">1</property>
                                  <property name="pos"></property>
                                  <property name="resize">Resizable</property>
                                  <property name="show">1</property>
                                  <property name="size"></property>
                                  <property name="style"></property>
                                  <property name="subclass"></property>
                                  <property name="toolbar_pane">0</property>
                                  <property name="tooltip"></property>
                                  <property name="window_extra_style"></property>
                                  <property name="window_name"></property>
                                  <property name="window_style"></property>
                                  <property name="wrap">-1</property>
                                </object>
                              </object>
                              <object class="sizeritem" expanded="false">
                                <property name="border">5</property>
                                <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxRIGHT|wxTOP</property>
                                <property name="proportion">1</property>
                                <object class="wxSlider" expanded="false">
                                  <property name="BottomDockable">1</property>
                                  <property name="LeftDockable">1</property>
                                  <property name="RightDockable">1</property>
                                  <property name="TopDockable">1</property>
                                  <property name="aui_layer"></property>
                                  <property name="aui_name"></property>
                                  <property name="aui_position"></property>
                                  <property name="aui_row"></property>
                                  <property name="best_size"></property>
                                  <property name="bg"></property>
                                  <property name="caption"></property>
                                  <property name="caption_visible">1</property>
                                  <property name="center_pane">0</property>
                                  <property name="close_button">1</property>
                                  <property name="context_help"></property>
                                  <property name="context_menu">1</property>
                                  <property name="default_pane">0</property>
                                  <property name="dock">Dock</property>
                                  <property name="dock_fixed">0</property>
                                  <property name="docking">Left</property>
                                  <property name="drag_accept_files">0</property>
                                  <property name="enabled">1</property>
                                  <property name="fg"></property>
                                  <property name="floatable">1</property>
                                  <property name="font"></property>
                                  <property name="gripper">0</property>
                                  <property name="hidden">0</property>
                                  <property name="id">wxID_ANY</property>
                                  <property name="maxValue">255</property>
                                  <property name="max_size"></property>
                                  <property name="maximize_button">0</property>
                                  <property name="maximum_size"></property>
                                  <property name="minValue">0</property>
                                  <property name="min_size"></property>
                                  <property name="minimize_button">0</property>
                                  <property name="minimum_size"></property>
                                  <property name="moveable">1</property>
                                  <property name="name">m_sliderBrightness</property>
                                  <property name="pane_border">1</property>
                                  <property name="pane_position"></property>
                                  <property name="pane_size"></property>
                                  <property name="permission">protected</property>
                                  <property name="pin_button">1</property>
                                  <property name="pos"></property>
                                  <property name="resize">Resizable</property>
                                  <property name="show">1</property>
                                  <property name="size"></property>
                                  <property name="style">wxSL_INVERSE|wxSL_LABELS|wxSL_LEFT|wxSL_VERTICAL</property>
                                  <property name="subclass"></property>
                                  <property name="toolbar_pane">0</property>
                                  <property name="tooltip"></property>
                                  <property name="validator_data_type"></property>
                                  <property name="validator_style">wxFILTER_NONE</property>
                                  <property name="validator_type">wxDefaultValidator</property>
                                  <property name="validator_variable"></property>
                                  <property name="value">255</property>
                                  <property name="window_extra_style"></property>
                                  <property name="window_name"></property>
                                  <property name="window_style"></property>
                                  <event name="OnScroll">OnChangeBrightness</event>
                                </object>
                              </object>
                            </object>
                          </object>
                        </object>
                      </object>
                    </object>
                  </object>
                </object>
                <object class="notebookpage" expanded="false">
                  <property name="bitmap"></property>
                  <property name="label">Defined Colors</property>
                  <property name="select">0</property>
                  <object class="wxPanel" expanded="false">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer"></property>
                    <property name="aui_name"></property>
                    <property name="aui_position"></property>
                    <property name="aui_row"></property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_panelDefinedColors</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="subclass"></property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style">wxTAB_TRAVERSAL</property>
                    <object class="wxBoxSizer" expanded="false">
                      <property name="minimum_size"></property>
                      <property name="name">m_SizerDefinedColors</property>
                      <property name="orient">wxVERTICAL</property>
                      <property name="permission">protected</property>
                      <object class="sizeritem" expanded="false">
                        <property name="border">10</property>
                        <property name="flag">wxALL|wxEXPAND</property>
                        <property name="proportion">1</property>
                        <object class="wxFlexGridSizer" expanded="false">
                          <property name="cols">10</property>
                          <property name="flexible_direction">wxBOTH</property>
                          <property name="growablecols">1,3,5,7,9</property>
                          <property name="growablerows"></property>
                          <property name="hgap">5</property>
                          <property name="minimum_size"></property>
                          <property name="name">m_fgridColor</property>
                          <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
                          <property name="permission">protected</property>
                          <property name="rows">0</property>
                          <property name="vgap">25</property>
                        </object>
                      </object>
                    </object>
                  </object>
                </object>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND|wxRIGHT|wxLEFT</property>
              <property name="proportion">0</property>
              <object class="wxBoxSizer" expanded="true">
                <property name="minimum_size"></property>
                <property name="name">m_SizerTransparency</property>
                <property name="orient">wxVERTICAL</property>
                <property name="permission">protected</property>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxTOP</property>
                  <property name="proportion">0</property>
                  <object class="spacer" expanded="true">
                    <property name="height">20</property>
                    <property name="permission">protected</property>
                    <property name="width">0</property>
                  </object>
                </object>
                <object class="sizeritem" expanded="true">
                  <property name="border">5</property>
                  <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxTOP|wxLEFT</property>
                  <property name="proportion">0</property>
                  <object class="wxStaticText" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer"></property>
                    <property name="aui_name"></property>
                    <property name="aui_position"></property>
                    <property name="aui_row"></property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="label">Opacity:</property>
                    <property name="markup">0</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_opacityLabel</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style"></property>
                    <property name="subclass"></property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <property name="wrap">-1</property>
                  </object>
                </object>
                <object class="sizeritem" expanded="true">
                  <property name="border">10</property>
                  <property name="flag">wxTOP|wxBOTTOM|wxRIGHT|wxALIGN_CENTER_HORIZONTAL</property>
                  <property name="proportion">1</property>
                  <object class="wxSlider" expanded="true">
                    <property name="BottomDockable">1</property>
                    <property name="LeftDockable">1</property>
                    <property name="RightDockable">1</property>
                    <property name="TopDockable">1</property>
                    <property name="aui_layer"></property>
                    <property name="aui_name"></property>
                    <property name="aui_position"></property>
                    <property name="aui_row"></property>
                    <property name="best_size"></property>
                    <property name="bg"></property>
                    <property name="caption"></property>
                    <property name="caption_visible">1</property>
                    <property name="center_pane">0</property>
                    <property name="close_button">1</property>
                    <property name="context_help"></property>
                    <property name="context_menu">1</property>
                    <property name="default_pane">0</property>
                    <property name="dock">Dock</property>
                    <property name="dock_fixed">0</property>
                    <property name="docking">Left</property>
                    <property name="drag_accept_files">0</property>
                    <property name="enabled">1</property>
                    <property name="fg"></property>
                    <property name="floatable">1</property>
                    <property name="font"></property>
                    <property name="gripper">0</property>
                    <property name="hidden">0</property>
                    <property name="id">wxID_ANY</property>
                    <property name="maxValue">100</property>
                    <property name="max_size"></property>
                    <property name="maximize_button">0</property>
                    <property name="maximum_size"></property>
                    <property name="minValue">0</property>
                    <property name="min_size"></property>
                    <property name="minimize_button">0</property>
                    <property name="minimum_size"></property>
                    <property name="moveable">1</property>
                    <property name="name">m_sliderTransparency</property>
                    <property name="pane_border">1</property>
                    <property name="pane_position"></property>
                    <property name="pane_size"></property>
                    <property name="permission">protected</property>
                    <property name="pin_button">1</property>
                    <property name="pos"></property>
                    <property name="resize">Resizable</property>
                    <property name="show">1</property>
                    <property name="size"></property>
                    <property name="style">wxSL_INVERSE|wxSL_LABELS|wxSL_LEFT|wxSL_VERTICAL</property>
                    <property name="subclass"></property>
                    <property name="toolbar_pane">0</property>
                    <property name="tooltip"></property>
                    <property name="validator_data_type"></property>
                    <property name="validator_style">wxFILTER_NONE</property>
                    <property name="validator_type">wxDefaultValidator</property>
                    <property name="validator_variable"></property>
                    <property name="value">80</property>
                    <property name="window_extra_style"></property>
                    <property name="window_name"></property>
                    <property name="window_style"></property>
                    <event name="OnScroll">OnChangeAlpha</event>
                  </object>
                </object>
              </object>
            </object>
          </object>
        </object>
        <object class="sizeritem" expanded="true">
          <property name="border">10</property>
          <property name="flag">wxEXPAND|wxLEFT</property>
          <property name="proportion">0</property>
          <object class="wxBoxSizer" expanded="true">
            <property name="minimum_size"></property>
            <property name="name">bButtonsSizer</property>
            <property name="orient">wxHORIZONTAL</property>
            <property name="permission">none</property>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL|wxLEFT|wxRIGHT</property>
              <property name="proportion">0</property>
              <object class="wxStaticText" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Preview (old/new):</property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_staticTextOldColor</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass"></property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <property name="wrap">-1</property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxALIGN_CENTER_VERTICAL|wxSHAPED</property>
              <property name="proportion">0</property>
              <object class="wxStaticBitmap" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size">24,24</property>
                <property name="moveable">1</property>
                <property name="name">m_OldColorRect</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">-1,-1</property>
                <property name="subclass"></property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxALIGN_CENTER_VERTICAL|wxSHAPED</property>
              <property name="proportion">0</property>
              <object class="wxStaticBitmap" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size">24,24</property>
                <property name="moveable">1</property>
                <property name="name">m_NewColorRect</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">-1,-1</property>
                <property name="subclass"></property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxALIGN_CENTER_VERTICAL|wxLEFT</property>
              <property name="proportion">0</property>
              <object class="wxTextCtrl" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="maxlength"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size">176,-1</property>
                <property name="moveable">1</property>
                <property name="name">m_colorValue</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="value"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnText">OnColorValueText</event>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">0</property>
              <object class="spacer" expanded="true">
                <property name="height">0</property>
                <property name="permission">protected</property>
                <property name="width">20</property>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxALL|wxALIGN_CENTER_VERTICAL</property>
              <property name="proportion">0</property>
              <object class="wxButton" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Reset to Default</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_resetToDefault</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">OnResetButton</event>
              </object>
            </object>
            <object class="sizeritem" expanded="true">
              <property name="border">5</property>
              <property name="flag">wxALL</property>
              <property name="proportion">1</property>
              <object class="wxStdDialogButtonSizer" expanded="true">
                <property name="Apply">0</property>
                <property name="Cancel">1</property>
                <property name="ContextHelp">0</property>
                <property name="Help">0</property>
                <property name="No">0</property>
                <property name="OK">1</property>
                <property name="Save">0</property>
                <property name="Yes">0</property>
                <property name="minimum_size"></property>
                <property name="name">m_sdbSizer</property>
                <property name="permission">protected</property>
              </object>
            </object>
          </object>
        </object>
      </object>
    </object>
  </object>
</wxFormBuilder_Project>
