(kicad_symbol_lib (version 20211014) (generator kicad_symbol_editor)
  (symbol "7seg-single" (pin_names (offset 0.635)) (in_bom yes) (on_board yes)
    (property "Reference" "U" (id 0) (at -2.54 13.97 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "7seg-single" (id 1) (at -3.81 -13.97 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0 -15.24 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at -12.7 12.065 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "ki_keywords" "display LED 7-segment" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "ki_description" "One digit 7 segment blue LED, common cathode" (id 5) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "ki_fp_filters" "KCSC02?136*" (id 6) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "7seg-single_0_0"
      (text "A" (at 0.254 5.588 0)
        (effects (font (size 0.508 0.508)))
      )
      (text "B" (at 2.54 4.826 0)
        (effects (font (size 0.508 0.508)))
      )
      (text "C" (at 2.286 1.778 0)
        (effects (font (size 0.508 0.508)))
      )
      (text "D" (at -0.254 1.016 0)
        (effects (font (size 0.508 0.508)))
      )
      (text "DP" (at 3.556 0.254 0)
        (effects (font (size 0.508 0.508)))
      )
      (text "E" (at -2.54 1.778 0)
        (effects (font (size 0.508 0.508)))
      )
      (text "F" (at -2.286 4.826 0)
        (effects (font (size 0.508 0.508)))
      )
      (text "G" (at 0 4.064 0)
        (effects (font (size 0.508 0.508)))
      )
    )
    (symbol "7seg-single_0_1"
      (rectangle (start -5.08 12.7) (end 5.08 -12.7)
        (stroke (width 0.254) (type default) (color 0 0 0 0))
        (fill (type background))
      )
      (polyline
        (pts
          (xy -1.524 2.794)
          (xy -1.778 0.762)
        )
        (stroke (width 0.508) (type default) (color 0 0 0 0))
        (fill (type none))
      )
      (polyline
        (pts
          (xy -1.27 0.254)
          (xy 0.762 0.254)
        )
        (stroke (width 0.508) (type default) (color 0 0 0 0))
        (fill (type none))
      )
      (polyline
        (pts
          (xy -1.27 5.842)
          (xy -1.524 3.81)
        )
        (stroke (width 0.508) (type default) (color 0 0 0 0))
        (fill (type none))
      )
      (polyline
        (pts
          (xy -1.016 3.302)
          (xy 1.016 3.302)
        )
        (stroke (width 0.508) (type default) (color 0 0 0 0))
        (fill (type none))
      )
      (polyline
        (pts
          (xy -0.762 6.35)
          (xy 1.27 6.35)
        )
        (stroke (width 0.508) (type default) (color 0 0 0 0))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 1.524 2.794)
          (xy 1.27 0.762)
        )
        (stroke (width 0.508) (type default) (color 0 0 0 0))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 1.778 5.842)
          (xy 1.524 3.81)
        )
        (stroke (width 0.508) (type default) (color 0 0 0 0))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 2.54 0.254)
          (xy 2.54 0.254)
        )
        (stroke (width 0.508) (type default) (color 0 0 0 0))
        (fill (type none))
      )
    )
    (symbol "7seg-single_1_1"
      (pin input line (at -7.62 -2.54 0) (length 2.54)
        (name "E" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -7.62 -7.62 0) (length 2.54)
        (name "G" (effects (font (size 1.27 1.27))))
        (number "10" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -7.62 0 0) (length 2.54)
        (name "D" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at 7.62 -7.62 180) (length 2.54)
        (name "CC" (effects (font (size 1.27 1.27))))
        (number "3" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -7.62 2.54 0) (length 2.54)
        (name "C" (effects (font (size 1.27 1.27))))
        (number "4" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -7.62 -10.16 0) (length 2.54)
        (name "DP" (effects (font (size 1.27 1.27))))
        (number "5" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -7.62 5.08 0) (length 2.54)
        (name "B" (effects (font (size 1.27 1.27))))
        (number "6" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -7.62 7.62 0) (length 2.54)
        (name "A" (effects (font (size 1.27 1.27))))
        (number "7" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at 7.62 -10.16 180) (length 2.54)
        (name "CC" (effects (font (size 1.27 1.27))))
        (number "8" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -7.62 -5.08 0) (length 2.54)
        (name "F" (effects (font (size 1.27 1.27))))
        (number "9" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "MCP1319" (in_bom yes) (on_board yes)
    (property "Reference" "U" (id 0) (at -2.54 6.35 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "MCP1319" (id 1) (at 1.27 -6.35 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Package_TO_SOT_SMD:SOT-23-5" (id 2) (at 0 -8.89 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "MCP1319_0_1"
      (rectangle (start -3.81 5.08) (end 6.35 -5.08)
        (stroke (width 0) (type default) (color 0 0 0 0))
        (fill (type background))
      )
      (rectangle (start 2.54 3.81) (end 2.54 3.81)
        (stroke (width 0) (type default) (color 0 0 0 0))
        (fill (type none))
      )
    )
    (symbol "MCP1319_1_1"
      (pin input line (at -6.35 2.54 0) (length 2.54)
        (name "~{RST}" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -6.35 0 0) (length 2.54)
        (name "GND" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -6.35 -2.54 0) (length 2.54)
        (name "RST" (effects (font (size 1.27 1.27))))
        (number "3" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at 8.89 -2.54 180) (length 2.54)
        (name "~{MR}" (effects (font (size 1.27 1.27))))
        (number "4" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at 8.89 2.54 180) (length 2.54)
        (name "VDD" (effects (font (size 1.27 1.27))))
        (number "5" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "MIC5318" (in_bom yes) (on_board yes)
    (property "Reference" "U" (id 0) (at -3.81 5.08 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "MIC5318" (id 1) (at -1.27 -5.08 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Package_TO_SOT_SMD:SOT-23-5" (id 2) (at -2.54 6.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at -2.54 6.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "MIC5318_0_1"
      (rectangle (start -5.08 3.81) (end 5.08 -3.81)
        (stroke (width 0) (type default) (color 0 0 0 0))
        (fill (type background))
      )
    )
    (symbol "MIC5318_1_1"
      (pin input line (at -7.62 2.54 0) (length 2.54)
        (name "VIN" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -7.62 0 0) (length 2.54)
        (name "GND" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -7.62 -2.54 0) (length 2.54)
        (name "EN" (effects (font (size 1.27 1.27))))
        (number "3" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at 7.62 -2.54 180) (length 2.54)
        (name "ADJ" (effects (font (size 1.27 1.27))))
        (number "4" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at 7.62 2.54 180) (length 2.54)
        (name "VOUT" (effects (font (size 1.27 1.27))))
        (number "5" (effects (font (size 1.27 1.27))))
      )
    )
  )
)
